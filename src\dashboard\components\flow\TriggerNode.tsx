import React, { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import {
  Box,
  Text,
  VStack,
  HStack,
} from '@chakra-ui/react';
import { FiPlay } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';

interface TriggerNodeData {
  label: string;
}

const TriggerNode = memo(({ data, selected }: NodeProps<TriggerNodeData>) => {
  const { currentScheme } = useTheme();

  return (
    <Box
      bg={currentScheme.colors.surface}
      border={`2px solid ${selected ? currentScheme.colors.primary : currentScheme.colors.border}`}
      borderRadius="full"
      p={2}
      minW="80px"
      minH="80px"
      boxShadow="lg"
      position="relative"
      display="flex"
      alignItems="center"
      justifyContent="center"
      _hover={{
        boxShadow: 'xl',
        transform: 'scale(1.05)',
        borderColor: currentScheme.colors.primary,
      }}
      transition="all 0.2s"
    >
      <VStack spacing={1} align="center">
        <Box
          bg={currentScheme.colors.primary}
          color="white"
          borderRadius="full"
          p={1}
          fontSize="sm"
          boxShadow="sm"
        >
          <FiPlay />
        </Box>
        <Text 
          fontSize="xs" 
          fontWeight="bold" 
          color={currentScheme.colors.text}
          textAlign="center"
        >
          {data.label}
        </Text>
      </VStack>
      
      <Handle
        type="source"
        position={Position.Bottom}
        style={{
          background: currentScheme.colors.background,
          border: `2px solid ${currentScheme.colors.primary}`,
          width: '16px',
          height: '16px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          bottom: '-8px',
          left: '50%',
          transform: 'translateX(-50%)',
        }}
      />
    </Box>
  );
});

TriggerNode.displayName = 'TriggerNode';

export default TriggerNode; 