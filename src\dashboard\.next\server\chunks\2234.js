"use strict";exports.id=2234,exports.ids=[2234],exports.modules={2234:(e,s,l)=>{l.a(e,async(e,o)=>{try{l.r(s),l.d(s,{default:()=>i});var a=l(8732),n=l(9733),r=l(2015),d=l(8079),t=e([n]);n=(t.then?(await t)():t)[0];let c={welcome:{enabled:!1,channelId:"",message:"Welcome {user} to {guild}! You are the {memberCount}th member.",autoRoles:[],embedColor:"#00FF00"},goodbye:{enabled:!1,channelId:"",message:"Goodbye {user}! We will miss you.",embedColor:"#FF0000"}};function i({isOpen:e,onClose:s,channels:l=[],roles:o=[]}){let t=(0,n.useToast)(),[i,h]=(0,r.useState)(c),[m,x]=(0,r.useState)(!0),[b,g]=(0,r.useState)(!1),u=l.filter(e=>0===e.type),j=o.filter(e=>"@everyone"!==e.name),y=async()=>{g(!0);try{if(!(await fetch("/api/automation/welcome",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(i)})).ok)throw Error("Failed to save settings");t({title:"Settings Saved",status:"success",duration:3e3}),s()}catch(e){t({title:"Error saving settings",description:e.message,status:"error",duration:5e3})}finally{g(!1)}},C=(e,s)=>{h(l=>({...l,welcome:{...l.welcome,[e]:s}}))},p=(e,s)=>{h(l=>({...l,goodbye:{...l.goodbye,[e]:s}}))};return(0,a.jsxs)(n.Modal,{isOpen:e,onClose:s,size:"3xl",scrollBehavior:"inside",children:[(0,a.jsx)(n.ModalOverlay,{}),(0,a.jsxs)(n.ModalContent,{children:[(0,a.jsx)(n.ModalHeader,{children:"Welcome & Goodbye System"}),(0,a.jsx)(n.ModalCloseButton,{}),(0,a.jsx)(n.ModalBody,{children:m?(0,a.jsxs)(n.VStack,{justify:"center",h:"400px",children:[(0,a.jsx)(n.Spinner,{size:"xl"}),(0,a.jsx)(n.Text,{children:"Loading Settings..."})]}):(0,a.jsxs)(n.Tabs,{isFitted:!0,variant:"enclosed",children:[(0,a.jsxs)(n.TabList,{children:[(0,a.jsxs)(n.Tab,{children:[(0,a.jsx)(n.Icon,{as:d.mEP,mr:2})," Welcome Messages"]}),(0,a.jsxs)(n.Tab,{children:[(0,a.jsx)(n.Icon,{as:d.QeK,mr:2})," Goodbye Messages"]})]}),(0,a.jsxs)(n.TabPanels,{children:[(0,a.jsx)(n.TabPanel,{children:(0,a.jsxs)(n.VStack,{spacing:6,align:"stretch",children:[(0,a.jsxs)(n.FormControl,{display:"flex",alignItems:"center",children:[(0,a.jsx)(n.FormLabel,{htmlFor:"welcome-enabled",mb:"0",children:"Enable Welcome Messages"}),(0,a.jsx)(n.Switch,{id:"welcome-enabled",isChecked:i.welcome.enabled,onChange:e=>C("enabled",e.target.checked)})]}),(0,a.jsxs)(n.FormControl,{isDisabled:!i.welcome.enabled,children:[(0,a.jsx)(n.FormLabel,{children:"Welcome Channel"}),(0,a.jsx)(n.Select,{placeholder:"Select a channel",value:i.welcome.channelId||"",onChange:e=>C("channelId",e.target.value),children:u.map(e=>(0,a.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))})]}),(0,a.jsxs)(n.FormControl,{isDisabled:!i.welcome.enabled,children:[(0,a.jsx)(n.FormLabel,{children:"Welcome Message"}),(0,a.jsx)(n.Textarea,{value:i.welcome.message,onChange:e=>C("message",e.target.value),placeholder:"Enter your welcome message...",rows:5}),(0,a.jsxs)(n.Text,{fontSize:"xs",color:"gray.500",mt:1,children:["Placeholders: ","{user}",", ","{username}",", ","{userTag}",", ","{guild}",", ","{memberCount}"]})]}),(0,a.jsxs)(n.FormControl,{isDisabled:!i.welcome.enabled,children:[(0,a.jsx)(n.FormLabel,{children:"Auto-Assign Roles"}),(0,a.jsx)(n.Box,{p:3,borderWidth:1,borderRadius:"md",maxH:"200px",overflowY:"auto",children:(0,a.jsx)(n.CheckboxGroup,{value:i.welcome.autoRoles,onChange:e=>C("autoRoles",e),children:(0,a.jsx)(n.SimpleGrid,{columns:{base:1,md:2},spacing:2,children:j.map(e=>(0,a.jsx)(n.Checkbox,{value:e.id,children:e.name},e.id))})})})]})]})}),(0,a.jsx)(n.TabPanel,{children:(0,a.jsxs)(n.VStack,{spacing:6,align:"stretch",children:[(0,a.jsxs)(n.FormControl,{display:"flex",alignItems:"center",children:[(0,a.jsx)(n.FormLabel,{htmlFor:"goodbye-enabled",mb:"0",children:"Enable Goodbye Messages"}),(0,a.jsx)(n.Switch,{id:"goodbye-enabled",isChecked:i.goodbye.enabled,onChange:e=>p("enabled",e.target.checked)})]}),(0,a.jsxs)(n.FormControl,{isDisabled:!i.goodbye.enabled,children:[(0,a.jsx)(n.FormLabel,{children:"Goodbye Channel"}),(0,a.jsx)(n.Select,{placeholder:"Select a channel",value:i.goodbye.channelId||"",onChange:e=>p("channelId",e.target.value),children:u.map(e=>(0,a.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))})]}),(0,a.jsxs)(n.FormControl,{isDisabled:!i.goodbye.enabled,children:[(0,a.jsx)(n.FormLabel,{children:"Goodbye Message"}),(0,a.jsx)(n.Textarea,{value:i.goodbye.message,onChange:e=>p("message",e.target.value),placeholder:"Enter your goodbye message...",rows:5}),(0,a.jsxs)(n.Text,{fontSize:"xs",color:"gray.500",mt:1,children:["Placeholders: ","{user}",", ","{username}",", ","{userTag}",", ","{guild}",", ","{memberCount}"]})]})]})})]})]})}),(0,a.jsxs)(n.ModalFooter,{children:[(0,a.jsx)(n.Button,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,a.jsx)(n.Button,{colorScheme:"blue",onClick:y,isLoading:b,isDisabled:m,children:"Save Settings"})]})]})]})}o()}catch(e){o(e)}})}};