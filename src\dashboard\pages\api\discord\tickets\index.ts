// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../../core/config';

/*******************
 * MongoDB helper  *
 *******************/
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase() {
  if (cachedClient) return cachedClient;
  cachedClient = await MongoClient.connect(mongoUrl);
  return cachedClient;
}

/*******************
 *  Route handler  *
 *******************/
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const {
    token: botToken,
    guildId,
    ticketCategoryId,
    ticketLogChannelId,
  } = dashboardConfig.bot as any;

  if (!botToken || !guildId) {
    return res.status(500).json({ error: 'Bot configuration missing (token/guildId)' });
  }

  let client: MongoClient;
  try {
    client = await connectToDatabase();
  } catch (err) {
    console.error('Mongo connection error:', err);
    return res.status(500).json({ error: 'Database connection failed' });
  }

  const db = client.db(dbName);
  const collection = db.collection('tickets');

  switch (req.method) {
    /*****************
     * LIST TICKETS *
     *****************/
    case 'GET': {
      try {
        // Admins can see all tickets, normal users only their own
        const isAdmin = (session.user as any).isAdmin;
        const filter = isAdmin ? {} : { creatorId: (session.user as any).id };
        const tickets = await collection.find(filter).sort({ createdAt: -1 }).toArray();

        // Attach a convenient Discord link using config guildId
        const enhanced = tickets.map((t: any) => ({
          ...t,
          discordLink: `https://discord.com/channels/${guildId}/${t.channelId}`,
        }));
        return res.status(200).json(enhanced);
      } catch (error) {
        console.error('Fetch tickets error:', error);
        return res.status(500).json({ error: 'Failed to fetch tickets' });
      }
    }

    /*******************
     * CREATE TICKET  *
     *******************/
    case 'POST': {
      const { reason = '', category = 'support' } = req.body ?? {};

      // Build channel name (max 100 chars, lowercase, no spaces)
      const discriminator = Math.floor(Math.random() * 9000 + 1000);
      const slug = category === '18plus' ? '18plus' : category;
      const channelName = `ticket-${slug}-${discriminator}`.slice(0, 100);

      // Permission overwrites – allow creator & support roles to view
      const overwrites: any[] = [
        {
          id: guildId, // @everyone – deny
          type: 0,
          deny: '1024', // VIEW_CHANNEL
        },
        {
          id: (session.user as any).id, // creator – allow view & send
          type: 1,
          allow: '68608', // VIEW_CHANNEL + SEND_MESSAGES + READ_MESSAGE_HISTORY
        },
      ];

      // Optional support role(s) from config
      if (dashboardConfig.dashboard?.adminRoleIds?.length) {
        dashboardConfig.dashboard.adminRoleIds.forEach((roleId: string) => {
          overwrites.push({
            id: roleId,
            type: 0,
            allow: '68608',
          });
        });
      }

      try {
        /*********************
         * Create channel on Discord
         *********************/
        const channelRes = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {
          method: 'POST',
          headers: {
            Authorization: `Bot ${botToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: channelName,
            type: 0, // GUILD_TEXT
            parent_id: ticketCategoryId ?? undefined,
            permission_overwrites: overwrites,
            topic: `Ticket created by ${(session.user as any).name} | Reason: ${reason}`.slice(0, 1024),
          }),
        });

        if (!channelRes.ok) {
          let discordError;
          try {
            discordError = await channelRes.json();
          } catch {
            discordError = await channelRes.text();
          }
          console.error('Discord channel creation failed:', discordError);
          return res.status(channelRes.status).json({ error: 'Failed to create Discord channel', details: discordError });
        }

        const channelData = await channelRes.json();

        /*********************
         * Store ticket in DB
         *********************/
        const ticketDoc = {
          creatorId: (session.user as any).id,
          creatorTag: (session.user as any).name,
          channelId: channelData.id,
          status: 'open',
          reason,
          category,
          createdAt: new Date(),
        };

        const insertRes = await collection.insertOne(ticketDoc);

        /********************
         * Send ticket embed with buttons
         ********************/
        try {
          const ticketIdStr = insertRes.insertedId.toString();
          await fetch(`https://discord.com/api/v10/channels/${channelData.id}/messages`, {
            method: 'POST',
            headers: {
              Authorization: `Bot ${botToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              embeds: [
                {
                  title: '🎫 Support Ticket',
                  description: `Hello <@${ticketDoc.creatorId}>! A staff member will be with you shortly.\n\n**Category:** ${category}\n**Reason:** ${reason || 'No reason provided'}`,
                  color: 0x2b6cb0,
                  timestamp: new Date().toISOString(),
                  footer: { text: `Ticket ID: ${ticketIdStr}` },
                },
              ],
              components: [
                {
                  type: 1,
                  components: [
                    {
                      type: 2,
                      style: 1,
                      label: 'Claim',
                      custom_id: `ticket_claim_${ticketIdStr}`,
                    },
                    {
                      type: 2,
                      style: 4,
                      label: 'Close',
                      custom_id: `ticket_close_${ticketIdStr}`,
                    },
                  ],
                },
              ],
            }),
          });
        } catch (err) {
          console.error('Failed to send ticket embed:', err);
        }

        // Log in ticketLogChannel if configured
        if (ticketLogChannelId) {
          try {
            await fetch(`https://discord.com/api/v10/channels/${ticketLogChannelId}/messages`, {
              method: 'POST',
              headers: {
                Authorization: `Bot ${botToken}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                embeds: [{
                  title: '🎫 New Ticket Created',
                  color: 0x3b82f6,
                  fields: [
                    { name: 'Ticket ID', value: insertRes.insertedId.toString(), inline: true },
                    { name: 'Category', value: category, inline: true },
                    { name: 'User', value: `<@${ticketDoc.creatorId}>`, inline: false },
                    { name: 'Reason', value: reason || 'None', inline: false },
                  ],
                  timestamp: new Date().toISOString(),
                }]
              }),
            });
          } catch (err) {
            console.error('Failed to post ticket log:', err);
          }
        }

        return res.status(201).json({ ...ticketDoc, _id: insertRes.insertedId, discordLink: `https://discord.com/channels/${guildId}/${ticketDoc.channelId}` });
      } catch (error) {
        console.error('Ticket creation error:', error);
        return res.status(500).json({ error: 'Failed to create ticket' });
      }
    }

    default:
      res.setHeader('Allow', ['GET', 'POST']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
} 