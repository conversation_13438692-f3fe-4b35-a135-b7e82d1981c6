// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // For sensitive operations like editing/deleting roles we require admin.
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    const { roleId } = req.query;
    if (!roleId || typeof roleId !== 'string') {
      return res.status(400).json({ error: 'Role ID is required' });
    }

    if (req.method === 'PATCH') {
      try {
        console.log('Updating role:', roleId, 'with data:', req.body);

        // Convert hex color to decimal for Discord API
        let { color, ...roleData } = req.body;
        if (color) {
          // Remove # if present and convert to decimal
          color = parseInt(color.replace('#', ''), 16);
          roleData.color = color;
        }

        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles/${roleId}`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bot ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(roleData),
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        const updatedRole = await response.json();
        return res.status(200).json(updatedRole);
      } catch (error) {
        console.error('Error updating role:', error);
        return res.status(500).json({ error: 'Failed to update role' });
      }
    }

    if (req.method === 'DELETE') {
      try {
        console.log('Deleting role:', roleId);
        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles/${roleId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        return res.status(200).json({ message: 'Role deleted successfully' });
      } catch (error) {
        console.error('Error deleting role:', error);
        return res.status(500).json({ error: 'Failed to delete role' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in role handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 