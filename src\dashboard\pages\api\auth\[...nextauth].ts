// @ts-nocheck

import NextAuth from 'next-auth';
import Discord<PERSON>rovider from 'next-auth/providers/discord';
import type { NextAuthOptions } from 'next-auth';
import { dashboardConfig } from '../../../core/config';

export const authOptions: NextAuthOptions = {
  providers: [
    DiscordProvider({
      clientId: dashboardConfig.bot.clientId,
      clientSecret: dashboardConfig.bot.clientSecret,
      authorization: {
        params: {
          scope: 'identify email guilds'
        }
      }
    })
  ],
  callbacks: {
    async jwt({ token, account, profile }) {
      if (account && profile) {
        token.accessToken = account.access_token || null;
        token.id = profile.id || null;
      }
      return token;
    },
    async session({ session, token }) {
      if (session?.user) {
        // Ensure we have valid values for serialization
        const userId = token.id || null;
        const accessToken = token.accessToken || null;

        // Attach Discord user ID to session
        (session.user as any).id = userId;
        (session.user as any).accessToken = accessToken;

        // Default to false for admin status
        let isAdmin = false;

        if (userId) {
          console.log('Checking admin status for user:', userId);
          
          // Check explicit admin IDs
          const adminIds: string[] = dashboardConfig.dashboard.admins || [];
          console.log('Configured admin IDs:', adminIds);
          
          if (adminIds.includes(userId)) {
            console.log('User is in admin list');
            isAdmin = true;
          } else {
            console.log('User not in admin list, checking roles...');
            
            // Check roles if configured
            const roleIds: string[] = dashboardConfig.dashboard.adminRoleIds || [];
            console.log('Configured admin role IDs:', roleIds);
            
            if (roleIds.length && dashboardConfig.bot.token && dashboardConfig.bot.guildId) {
              try {
                console.log('Fetching member roles from Discord API...');
                console.log('Bot token (first 20 chars):', dashboardConfig.bot.token.substring(0, 20) + '...');
                console.log('Guild ID:', dashboardConfig.bot.guildId);
                console.log('User ID:', userId);
                console.log('Admin role IDs to check:', roleIds);
                
                const res = await fetch(
                  `https://discord.com/api/v10/guilds/${dashboardConfig.bot.guildId}/members/${userId}`,
                  {
                    headers: { Authorization: `Bot ${dashboardConfig.bot.token}` },
                  }
                );
                
                console.log('Discord API response status:', res.status);
                
                if (res.ok) {
                  const member = await res.json();
                  console.log('Member data:', JSON.stringify(member, null, 2));
                  console.log('Member roles:', member.roles);
                  isAdmin = roleIds.some((rid) => member.roles?.includes(rid)) || false;
                  console.log('Has admin role:', isAdmin);
                } else {
                  const errorText = await res.text();
                  console.error('Failed to fetch member - Status:', res.status);
                  console.error('Error response:', errorText);
                }
              } catch (error) {
                console.error('Failed to fetch guild member:', error);
              }
            } else {
              console.log('No role IDs configured or missing bot token/guild ID');
            }
          }
        } else {
          console.log('No user ID available');
        }

        // Set admin status
        (session.user as any).isAdmin = isAdmin;
        console.log('Final admin status:', isAdmin);

        // Ensure all session values are serializable
        session.user = {
          ...session.user,
          id: (session.user as any).id || null,
          accessToken: (session.user as any).accessToken || null,
          isAdmin: (session.user as any).isAdmin || false,
          name: session.user.name || null,
          email: session.user.email || null,
          image: session.user.image || null,
        };
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      // Dynamically derive equivalent localhost URL (same protocol & port)
      const parsed = new URL(baseUrl);
      const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;

      if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {
        return url;
      }

      return baseUrl;
    }
  },
  secret: dashboardConfig.dashboard.session.secret || dashboardConfig.bot.clientSecret,
  pages: {
    signIn: '/signin',
  },
  // Always show auth errors, but only debug logs in development
  debug: process.env.NODE_ENV === 'development',
  logger: {
    error: (code, metadata) => {
      console.error('[NextAuth Error]', code, metadata);
    },
    warn: (code) => {
      if (process.env.NODE_ENV !== 'production') {
        console.warn('[NextAuth Warn]', code);
      }
    },
    debug: (code, metadata) => {
      if (process.env.NODE_ENV !== 'production') {
        console.debug('[NextAuth Debug]', code, metadata);
      }
    }
  },
};

export default NextAuth(authOptions); 