import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { getDb } from '../../../../apiHelpers/db';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check if user is admin
  const isAdmin = (session.user as any)?.isAdmin;
  if (!isAdmin) {
    return res.status(403).json({ error: 'Admin access required' });
  }

  const { id } = req.query;
  if (typeof id !== 'string') {
    return res.status(400).json({ error: 'Invalid application ID' });
  }

  const db = await getDb();
  const collection = db.collection('custom_applications');

  if (req.method === 'GET') {
    try {
      const application = await collection.findOne({ id });
      if (!application) {
        return res.status(404).json({ error: 'Application not found' });
      }
      return res.status(200).json({ application });
    } catch (error) {
      console.error('Error fetching application:', error);
      return res.status(500).json({ error: 'Failed to fetch application' });
    }
  }

  if (req.method === 'DELETE') {
    try {
      const result = await collection.deleteOne({ id });
      
      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Application not found' });
      }
      
      return res.status(200).json({ success: true, message: 'Application deleted successfully' });
    } catch (error) {
      console.error('Error deleting application:', error);
      return res.status(500).json({ error: 'Failed to delete application' });
    }
  }

  if (req.method === 'PUT') {
    try {
      const applicationData = req.body;
      
      const updatedApp = {
        ...applicationData,
        id,
        updatedAt: new Date(),
      };
      
      const result = await collection.updateOne(
        { id },
        { $set: updatedApp }
      );
      
      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Application not found' });
      }
      
      return res.status(200).json({ application: updatedApp });
    } catch (error) {
      console.error('Error updating application:', error);
      return res.status(500).json({ error: 'Failed to update application' });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
} 