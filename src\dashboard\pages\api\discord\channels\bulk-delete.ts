// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';

const BATCH_SIZE = 5; // Process 5 channels at a time
const MAX_RETRIES = 2; // Number of retries per channel
const RETRY_DELAY = 1000; // 1 second delay between retries

async function deleteChannel(token: string, channelId: string, retryCount = 0): Promise<boolean> {
  try {
    const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bot ${token}`,
      },
    });

    if (response.ok) {
      return true;
    }

    // If we get a rate limit response, wait and retry
    if (response.status === 429) {
      const data = await response.json();
      const retryAfter = (data.retry_after || 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, retryAfter));
      if (retryCount < MAX_RETRIES) {
        return deleteChannel(token, channelId, retryCount + 1);
      }
    }

    console.error(`Failed to delete channel ${channelId}:`, await response.text());
    return false;
  } catch (error) {
    console.error(`Error deleting channel ${channelId}:`, error);
    if (retryCount < MAX_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      return deleteChannel(token, channelId, retryCount + 1);
    }
    return false;
  }
}

async function processBatch(token: string, channelIds: string[]): Promise<{ succeeded: string[], failed: string[] }> {
  const results = await Promise.all(
    channelIds.map(async channelId => {
      const success = await deleteChannel(token, channelId);
      return { channelId, success };
    })
  );

  return results.reduce(
    (acc, { channelId, success }) => {
      if (success) {
        acc.succeeded.push(channelId);
      } else {
        acc.failed.push(channelId);
      }
      return acc;
    },
    { succeeded: [], failed: [] }
  );
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // For sensitive operations like deleting channels we require admin.
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    if (req.method === 'POST') {
      try {
        const { channelIds } = req.body;

        if (!Array.isArray(channelIds) || channelIds.length === 0) {
          return res.status(400).json({ error: 'Channel IDs array is required' });
        }

        console.log(`Starting bulk delete of ${channelIds.length} channels`);

        // Fetch channel information to log voice channel deletions
        const channelsResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!channelsResponse.ok) {
          throw new Error('Failed to fetch channels for validation');
        }

        const allChannels = await channelsResponse.json();
        const voiceChannels = channelIds.filter(id => {
          const channel = allChannels.find((c: any) => c.id === id);
          return channel && channel.type === 2; // 2 is GUILD_VOICE
        });

        if (voiceChannels.length > 0) {
          console.warn(`Warning: Bulk deletion includes ${voiceChannels.length} voice channels:`, voiceChannels);
        }

        const results = {
          succeeded: [] as string[],
          failed: [] as string[],
        };

        // Process channels in batches
        for (let i = 0; i < channelIds.length; i += BATCH_SIZE) {
          const batch = channelIds.slice(i, i + BATCH_SIZE);
          console.log(`Processing batch ${i / BATCH_SIZE + 1}:`, batch);

          const batchResults = await processBatch(token, batch);
          results.succeeded.push(...batchResults.succeeded);
          results.failed.push(...batchResults.failed);

          // Add a small delay between batches to avoid rate limits
          if (i + BATCH_SIZE < channelIds.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        console.log('Bulk delete completed:', results);

        return res.status(200).json({
          message: `Successfully deleted ${results.succeeded.length} channels${
            results.failed.length > 0 ? `, failed to delete ${results.failed.length} channels` : ''
          }`,
          succeeded: results.succeeded,
          failed: results.failed,
        });
      } catch (error) {
        console.error('Error in bulk channel deletion:', error);
        return res.status(500).json({ error: 'Failed to delete channels' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in bulk channel deletion handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 