"use strict";(()=>{var e={};e.id=9309,e.ids=[9309],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},3433:(e,t,o)=>{e.exports=o(5600)},4722:e=>{e.exports=require("next-auth/react")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},7264:(e,t,o)=>{o.r(t),o.d(t,{config:()=>l,default:()=>u,routeModule:()=>c});var r={};o.r(r),o.d(r,{default:()=>d});var i=o(3433),a=o(264),n=o(584),s=o(4722);async function d(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let o=await (0,s.getSession)({req:e});if(!o?.user||"933023999770918932"!==o.user.id)return t.status(403).json({error:"Unauthorized"});let r=`${e.headers["x-forwarded-proto"]||"http"}://${e.headers.host}`,[i,a,n,d]=await Promise.all([fetch(`${r}/api/discord/roles`,{headers:{Cookie:e.headers.cookie||""}}),fetch(`${r}/api/discord/channels`,{headers:{Cookie:e.headers.cookie||""}}),fetch(`${r}/api/discord/guild`,{headers:{Cookie:e.headers.cookie||""}}),fetch(`${r}/api/discord/users`,{headers:{Cookie:e.headers.cookie||""}}).catch(()=>null)]);if(!i.ok||!a.ok||!n.ok)throw Error("Failed to fetch data from Discord API endpoints");let[u,l,c]=await Promise.all([i.json(),a.json(),n.json()]),p=[];if(d&&d.ok)try{p=await d.json()}catch(e){}let m=l.filter(e=>"GUILD_TEXT"===e.type||"GUILD_VOICE"===e.type||"GUILD_CATEGORY"===e.type).map(e=>({id:e.id,name:e.name,type:"GUILD_TEXT"===e.type?"text":"GUILD_VOICE"===e.type?"voice":"category",position:e.position||0})).sort((e,t)=>e.position-t.position),f=u.filter(e=>!e.managed&&"@everyone"!==e.name).map(e=>({id:e.id,name:e.name,color:e.color,position:e.position,permissions:e.permissions||[]})).sort((e,t)=>t.position-e.position),h=Array.isArray(p)?p.filter(e=>!e.bot).map(e=>({id:e.id,username:e.username,displayName:e.displayName||e.username,avatar:e.avatar,roles:e.roles||[],joinedAt:e.joinedAt})).slice(0,20):[];t.status(200).json({guild:{id:c.id,name:c.name,icon:c.icon,memberCount:c.memberCount,ownerId:c.ownerId,createdAt:c.createdAt},channels:m,roles:f,members:h})}catch(e){t.status(500).json({error:"Failed to fetch guild data",details:e instanceof Error?e.message:"Unknown error"})}}let u=(0,n.M)(r,"default"),l=(0,n.M)(r,"config"),c=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/admin/experimental/addon-builder/guild-data",pathname:"/api/admin/experimental/addon-builder/guild-data",bundlePath:"",filename:""},userland:r})}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=7264);module.exports=o})();