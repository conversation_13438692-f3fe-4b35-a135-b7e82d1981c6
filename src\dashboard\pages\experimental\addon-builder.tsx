import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { Box, VStack, Heading, Alert, AlertIcon, AlertDescription, Spinner, Center } from '@chakra-ui/react';
import Layout from '../../components/Layout';
import AddonBuilder from '../../components/AddonBuilder';

export default function AddonBuilderPage() {

  return (
    <Layout>
      <VStack spacing={8} p={8} align="stretch" maxW="100%" overflow="hidden">
        <Box textAlign="center">
          <Heading size="xl" mb={2}>
            ⚗️ Addon Builder
          </Heading>
          <Alert status="info" mb={4}>
            <AlertIcon />
            <AlertDescription>
              <strong>Experimental Feature:</strong> This is a beta feature for creating custom Discord bot addons. 
              Use with caution and report any issues you encounter.
            </AlertDescription>
          </Alert>
        </Box>

        <AddonBuilder />
      </VStack>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  
  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fexperimental%2Faddon-builder',
        permanent: false,
      },
    };
  }

  return { props: {} };
}; 