// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../../core/config';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    const { channelId } = req.query;
    if (!channelId || typeof channelId !== 'string') {
      return res.status(400).json({ error: 'Channel ID is required' });
    }

    if (req.method === 'POST') {
      try {
        const { content, embeds, components, flags } = req.body;

        // Ensure at least content, embeds, or components is provided
        if (!content && (!embeds || embeds.length === 0) && (!components || components.length === 0)) {
          return res.status(400).json({ error: 'Message must contain content, embeds, or components' });
        }

        // Convert hex colors to decimal in embeds and clean empty properties
        if (embeds?.length > 0) {
          embeds.forEach(embed => {
            if (embed.color && typeof embed.color === 'string' && embed.color.startsWith('#')) {
              embed.color = parseInt(embed.color.replace('#', ''), 16);
            }
            // Remove empty strings / null / undefined values
            Object.keys(embed).forEach((key) => {
              const k: any = key;
              if (embed[k] === '' || embed[k] === null || embed[k] === undefined) {
                delete embed[k];
              }
            });
            // Remove empty fields array
            if (Array.isArray(embed.fields) && embed.fields.length === 0) {
              delete embed.fields;
            }
          });
        }

        const messageData: any = {};
        if (content) messageData.content = content;
        if (embeds?.length > 0) messageData.embeds = embeds;
        if (components?.length > 0) messageData.components = components;
        if (typeof flags === 'number') messageData.flags = flags;

        const response = await fetch(`https://discord.com/api/v10/channels/${channelId}/messages`, {
          method: 'POST',
          headers: {
            Authorization: `Bot ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(messageData),
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        const message = await response.json();
        return res.status(200).json(message);
      } catch (error) {
        console.error('Error sending message:', error);
        return res.status(500).json({ error: 'Failed to send message' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in message handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 