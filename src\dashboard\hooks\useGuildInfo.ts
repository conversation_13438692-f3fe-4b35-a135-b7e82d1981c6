import useSWR from 'swr';
import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface GuildInfo {
  id: string;
  name: string;
  icon?: string | null;
  botName?: string;
}

const fetcher = async (url: string) => {
  const res = await fetch(url);
  if (!res.ok) {
    if (res.status === 401) {
      // Return default data for unauthorized state
      return {
        name: '404 Bot',
        botName: '404 Bot'
      };
    }
    throw new Error('Failed to fetch guild info');
  }
  return res.json();
};

export default function useGuildInfo() {
  const { data: session, status } = useSession();
  
  // Only fetch if we're authenticated
  const shouldFetch = status === 'authenticated';
  const { data, error } = useSWR<GuildInfo>(
    shouldFetch ? '/api/discord/guild' : null,
    fetcher,
    {
      revalidateOnFocus: false,
      revalidateOnReconnect: false
    }
  );

  // Local preference state (guild vs bot)
  const [pref, setPref] = useState<'guild' | 'bot'>(() => {
    if (typeof window === 'undefined') return 'guild';
    return (localStorage.getItem('dashboardDisplayNamePref') as 'guild' | 'bot') || 'guild';
  });

  // Function to update preference and broadcast change
  const updatePreference = useCallback((newPref: 'guild' | 'bot') => {
    setPref(newPref);
    if (typeof window !== 'undefined') {
      localStorage.setItem('dashboardDisplayNamePref', newPref);
      window.dispatchEvent(new CustomEvent('displayNamePrefChanged', { detail: newPref }));
    }
  }, []);

  // Listen for preference changes in this tab (custom event) or other tabs (storage event)
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const handleCustom = (e: any) => {
      if (e?.detail) setPref(e.detail);
    };
    const handleStorage = (e: StorageEvent) => {
      if (e.key === 'dashboardDisplayNamePref') {
        setPref((e.newValue as 'guild' | 'bot') || 'guild');
      }
    };

    window.addEventListener('displayNamePrefChanged', handleCustom);
    window.addEventListener('storage', handleStorage);
    return () => {
      window.removeEventListener('displayNamePrefChanged', handleCustom);
      window.removeEventListener('storage', handleStorage);
    };
  }, []);

  // Default display name when not authenticated
  const defaultName = '404 Bot Dashboard';

  // Determine displayName
  let displayName = defaultName;
  if (data) {
    if (pref === 'bot' && data.botName) {
      displayName = data.botName;
    } else {
      displayName = data.name || defaultName;
    }
  }

  return {
    guild: data,
    displayName,
    pref,
    updatePreference,
    isLoading: shouldFetch && !error && !data,
    isError: !!error,
  } as const;
} 