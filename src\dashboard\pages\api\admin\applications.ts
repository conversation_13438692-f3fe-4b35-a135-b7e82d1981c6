import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { getDb } from '../../../apiHelpers/db';
import { ObjectId } from 'mongodb'; // Import ObjectId

// TODO: Implement DEVELOPER_ID management
const DEVELOPER_ID = 'your_developer_id_here'; // Replace with actual developer ID or load from config

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session?.user?.id) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Check if user is admin
    const isAdmin = (session.user as any)?.isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const db = await getDb();
    const submissionsCollection = db.collection('application_submissions');

    if (req.method === 'GET') {
      const submissions = await submissionsCollection.find({}).toArray();

      // Calculate stats based on new data structure
      const stats = {
        total: submissions.length,
        pending: submissions.filter(sub => sub.status === 'pending').length,
        approved: submissions.filter(sub => sub.status === 'approved').length,
        rejected: submissions.filter(sub => sub.status === 'rejected').length,
        recentIncrease: 0 // TODO: Calculate based on date comparison
      };

      return res.status(200).json({ applications: submissions, stats });
    }

    if (req.method === 'PATCH') {
      const { applicationSubmissionId, action } = req.body;

      if (!applicationSubmissionId || !action) {
        return res.status(400).json({ error: 'Missing applicationSubmissionId or action' });
      }

      const updateData = {
        status: action === 'approve' ? 'approved' : 'rejected',
        reviewedAt: new Date(),
        reviewedBy: session.user.id
      };

      await submissionsCollection.updateOne(
        { _id: new ObjectId(applicationSubmissionId) }, // Use ObjectId for _id
        { $set: updateData }
      );

      return res.status(200).json({ success: true });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in applications API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
