{"version": 1, "files": ["../../../webpack-api-runtime.js", "../../../../../../../package.json", "../../../../../../../config.yml", "../../../../../../addons/example/config.yml", "../../../../../../addons/example/commands/ping.ts", "../../../../../../addons/moderation/commands/kick.ts", "../../../../../../addons/example/index.ts", "../../../../../../addons/moderation/commands/purge.ts", "../../../../../../addons/moderation/commands/lockdown.ts", "../../../../../../addons/moderation/commands/slowmode.ts", "../../../../../../addons/moderation/commands/timeout.ts", "../../../../../../addons/moderation/commands/untimeout.ts", "../../../../../../addons/moderation/commands/ban.ts", "../../../../../../addons/moderation/commands/warnings.ts", "../../../../../../addons/moderation/commands/warn.ts", "../../../../../../addons/moderation/config.yml", "../../../../../../addons/server-hub/index.ts", "../../../../../../addons/tickets/config.yml", "../../../../../../addons/server-hub/config.yml", "../../../../../../addons/tickets/index WORKING.ts", "../../../../../../addons/uwu-hug/commands/hug.ts", "../../../../../../addons/tickets/index.ts", "../../../../../../addons/moderation/index.ts", "../../../../../../addons/uwu-hug/config.yml", "../../../../../../addons/uwu-hug/flow.json", "../../../../../../addons/uwu-hug/index.ts", "../../../../../../addons/voice-mistress/events/buttonHandler.ts", "../../../../../../addons/voice-mistress/index.ts", "../../../../../../addons/voice-mistress/utils/channelUtils.ts", "../../../../../../addons/welcome-goodbye/config.yml", "../../../../../../addons/welcome-goodbye/index.ts", "../../../../../../addons/voice-mistress/config.yml", "../../../../../../addons/welcome-goodbye/types.ts", "../../../../../../addons/welcome-goodbye/utils/numberUtils.ts", "../../../../../../../dist/addons/example/config.yml", "../../../../../../../dist/addons/example/commands/ping.js", "../../../../../../../dist/addons/moderation/commands/kick.js", "../../../../../../../dist/addons/example/index.js", "../../../../../../../dist/addons/moderation/commands/purge.js", "../../../../../../../dist/addons/moderation/commands/lockdown.js", "../../../../../../../dist/addons/moderation/commands/ban.js", "../../../../../../../dist/addons/moderation/commands/timeout.js", "../../../../../../../dist/addons/moderation/commands/slowmode.js", "../../../../../../../dist/addons/moderation/commands/untimeout.js", "../../../../../../../dist/addons/moderation/commands/warnings.js", "../../../../../../../dist/addons/moderation/config.yml", "../../../../../../../dist/addons/moderation/index.js", "../../../../../../../dist/addons/server-hub/config.yml", "../../../../../../../dist/addons/server-hub/index.js", "../../../../../../../dist/addons/tickets/config.yml", "../../../../../../../dist/addons/moderation/commands/warn.js", "../../../../../../../dist/addons/tickets/index WORKING.js", "../../../../../../../dist/addons/tickets/index.js", "../../../../../../../dist/addons/uwu-hug/commands/hug.js", "../../../../../../../dist/addons/uwu-hug/index.js", "../../../../../../../dist/addons/voice-mistress/config.yml", "../../../../../../../dist/addons/voice-mistress/events/buttonHandler.js", "../../../../../../../dist/addons/voice-mistress/index.js", "../../../../../../../dist/addons/uwu-hug/config.yml", "../../../../../../../dist/addons/voice-mistress/utils/channelUtils.js", "../../../../../../../dist/addons/welcome-goodbye/config.yml", "../../../../../../../dist/addons/welcome-goodbye/index.js", "../../../../../../../dist/addons/welcome-goodbye/utils/numberUtils.js", "../../../../../../../dist/addons/welcome-goodbye/types.js"]}