"use strict";(()=>{var e={};e.id=6178,e.ids=[6178],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},7910:e=>{e.exports=require("stream")},7967:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{default:()=>d});var a=t(8732),s=t(9733),n=t(2015),i=t(1011),l=t(8079),c=e([s,i]);[s,i]=c.then?(await c)():c;let h={moderation:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"}},example:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"}},tickets:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"}},"voice-mistress":{color:"pink",gradient:{from:"rgba(237, 137, 179, 0.4)",to:"rgba(237, 137, 179, 0.1)"}},"welcome-goodbye":{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"}},Unknown:{color:"gray",gradient:{from:"rgba(113, 128, 150, 0.4)",to:"rgba(113, 128, 150, 0.1)"}}},m=({command:e,onDelete:r,onToggle:t})=>{let o=h[e.addon]||h.Unknown,[i,c]=(0,n.useState)(!1),d=async()=>{c(!0),await t(e,!e.enabled),c(!1)};return(0,a.jsx)(s.Card,{bg:`linear-gradient(135deg, ${o.gradient.from}, ${o.gradient.to})`,backdropFilter:"blur(10px)",borderWidth:2,borderColor:`${o.color}.400`,rounded:"xl",overflow:"hidden",transition:"all 0.2s",opacity:e.enabled?1:.7,_hover:{transform:"translateY(-2px)",boxShadow:`0 4px 20px ${o.gradient.from}`},children:(0,a.jsx)(s.CardBody,{children:(0,a.jsxs)(s.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(s.HStack,{justify:"space-between",children:[(0,a.jsxs)(s.HStack,{children:[(0,a.jsx)(s.Icon,{as:l.JSe,color:`${o.color}.400`,boxSize:5}),(0,a.jsxs)(s.Heading,{size:"md",color:"white",children:["/",e.name]})]}),(0,a.jsxs)(s.VStack,{spacing:1,align:"end",children:[(0,a.jsx)(s.Badge,{colorScheme:o.color,size:"sm",children:e.category}),(0,a.jsx)(s.Badge,{variant:"outline",colorScheme:"gray",size:"sm",children:e.scope})]})]}),(0,a.jsx)(s.Text,{color:"gray.300",fontSize:"sm",noOfLines:2,children:e.description}),(0,a.jsxs)(s.Text,{color:"gray.400",fontSize:"xs",children:["ID: ",e.id]}),(0,a.jsxs)(s.HStack,{justify:"space-between",children:[(0,a.jsx)(s.Switch,{isChecked:e.enabled,onChange:d,isDisabled:i,colorScheme:o.color}),(0,a.jsx)(s.Button,{size:"sm",leftIcon:(0,a.jsx)(s.Icon,{as:l.IXo}),variant:"ghost",colorScheme:o.color,onClick:()=>r(e),children:"Remove"})]})]})})})};function d(){let[e,r]=(0,n.useState)([]),[t,o]=(0,n.useState)([]),[c,d]=(0,n.useState)(""),[p,u]=(0,n.useState)("all"),[g,x]=(0,n.useState)(!0),[b,j]=(0,n.useState)(!1),[S,f]=(0,n.useState)(null),{isOpen:y,onOpen:w,onClose:v}=(0,s.useDisclosure)(),C=(0,n.useRef)(null),k=(0,s.useToast)(),A=Array.from(new Set(e.map(e=>e.category))).sort(),P=async()=>{try{x(!0);let e=await fetch("/api/admin/commands");if(e.ok){let t=await e.json();r(t)}else throw Error("Failed to fetch commands")}catch(e){k({title:"Error",description:e.message||"Failed to fetch commands",status:"error",duration:5e3})}finally{x(!1)}},D=async()=>{j(!0),await P(),j(!1)},I=async e=>{f(e),w()},_=async(t,o)=>{try{if(!(await fetch("/api/admin/commands",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({commandId:t.id,enabled:o})})).ok)throw Error("Failed to update command state");r(e.map(e=>e.id===t.id?{...e,enabled:o}:e)),k({title:"Success",description:`Command /${t.name} has been ${o?"enabled":"disabled"}`,status:"success",duration:3e3})}catch(e){k({title:"Error",description:e.message||"Failed to update command state",status:"error",duration:5e3})}},M=async()=>{if(S)try{if(!(await fetch(`/api/admin/commands?commandId=${S.id}&scope=${S.scope}`,{method:"DELETE"})).ok)throw Error("Failed to delete command");r(e.filter(e=>e.id!==S.id)),k({title:"Success",description:`Command /${S.name} has been removed`,status:"success",duration:3e3})}catch(e){k({title:"Error",description:e.message||"Failed to delete command",status:"error",duration:5e3})}finally{v(),f(null)}};return(0,a.jsx)(i.A,{children:(0,a.jsxs)(s.Container,{maxW:"7xl",py:6,children:[(0,a.jsxs)(s.VStack,{spacing:6,align:"stretch",children:[(0,a.jsxs)(s.Flex,{direction:{base:"column",lg:"row"},justify:"space-between",align:{base:"start",lg:"center"},gap:4,children:[(0,a.jsxs)(s.Box,{children:[(0,a.jsx)(s.Heading,{size:"lg",mb:2,bgGradient:"linear(to-r, pink.500, purple.500)",bgClip:"text",children:"Bot Commands"}),(0,a.jsxs)(s.Text,{color:"gray.400",children:["Manage your Discord bot's slash commands (",t.length," of ",e.length,")"]})]}),(0,a.jsx)(s.Button,{leftIcon:(0,a.jsx)(s.Icon,{as:l.jTZ}),colorScheme:"purple",variant:"outline",onClick:D,isLoading:b,children:"Refresh"})]}),(0,a.jsxs)(s.Flex,{direction:{base:"column",md:"row"},gap:4,children:[(0,a.jsxs)(s.InputGroup,{flex:1,children:[(0,a.jsx)(s.InputLeftElement,{pointerEvents:"none",children:(0,a.jsx)(s.Icon,{as:l.CKj,color:"gray.400"})}),(0,a.jsx)(s.Input,{placeholder:"Search commands...",value:c,onChange:e=>d(e.target.value),bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",_hover:{borderColor:"whiteAlpha.300"},_focus:{borderColor:"purple.400",boxShadow:"0 0 0 1px var(--chakra-colors-purple-400)"}})]}),(0,a.jsxs)(s.Select,{value:p,onChange:e=>u(e.target.value),w:{base:"full",md:"200px"},bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",_hover:{borderColor:"whiteAlpha.300"},_focus:{borderColor:"purple.400",boxShadow:"0 0 0 1px var(--chakra-colors-purple-400)"},children:[(0,a.jsx)("option",{value:"all",children:"All Categories"}),A.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsx)(s.Wrap,{spacing:3,children:A.map(r=>{let t=e.filter(e=>e.category===r).length,o=h[r.toLowerCase()]||h.Unknown;return(0,a.jsx)(s.WrapItem,{children:(0,a.jsxs)(s.Badge,{colorScheme:o.color,variant:p===r?"solid":"outline",cursor:"pointer",onClick:()=>u(p===r?"all":r),px:3,py:1,rounded:"full",children:[r," (",t,")"]})},r)})}),g?(0,a.jsx)(s.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:[1,2,3].map(e=>(0,a.jsx)(s.Skeleton,{height:"200px",rounded:"xl"},e))}):(0,a.jsx)(s.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:t.map(e=>(0,a.jsx)(m,{command:e,onDelete:I,onToggle:_},e.id))}),!g&&0===t.length&&(0,a.jsxs)(s.Box,{textAlign:"center",py:12,children:[(0,a.jsx)(s.Icon,{as:l.K7R,boxSize:12,color:"gray.400",mb:4}),(0,a.jsx)(s.Heading,{size:"md",color:"gray.400",mb:2,children:"No commands found"}),(0,a.jsx)(s.Text,{color:"gray.500",children:"Try adjusting your search or category filter"})]})]}),(0,a.jsx)(s.AlertDialog,{isOpen:y,leastDestructiveRef:C,onClose:v,children:(0,a.jsx)(s.AlertDialogOverlay,{children:(0,a.jsxs)(s.AlertDialogContent,{bg:"gray.800",borderColor:"whiteAlpha.200",borderWidth:1,children:[(0,a.jsx)(s.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",children:"Delete Command"}),(0,a.jsxs)(s.AlertDialogBody,{children:["Are you sure you want to remove the command /",S?.name,"? This action cannot be undone."]}),(0,a.jsxs)(s.AlertDialogFooter,{children:[(0,a.jsx)(s.Button,{ref:C,onClick:v,children:"Cancel"}),(0,a.jsx)(s.Button,{colorScheme:"red",onClick:M,ml:3,children:"Delete"})]})]})})})]})})}o()}catch(e){o(e)}})},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9690:(e,r,t)=>{t.a(e,async(e,o)=>{try{t.r(r),t.d(r,{config:()=>g,default:()=>h,getServerSideProps:()=>u,getStaticPaths:()=>p,getStaticProps:()=>m,reportWebVitals:()=>x,routeModule:()=>w,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var a=t(1292),s=t(8834),n=t(786),i=t(3567),l=t(8077),c=t(7967),d=e([l,c]);[l,c]=d.then?(await d)():d;let h=(0,n.M)(c,"default"),m=(0,n.M)(c,"getStaticProps"),p=(0,n.M)(c,"getStaticPaths"),u=(0,n.M)(c,"getServerSideProps"),g=(0,n.M)(c,"config"),x=(0,n.M)(c,"reportWebVitals"),b=(0,n.M)(c,"unstable_getStaticProps"),j=(0,n.M)(c,"unstable_getStaticPaths"),S=(0,n.M)(c,"unstable_getStaticParams"),f=(0,n.M)(c,"unstable_getServerProps"),y=(0,n.M)(c,"unstable_getServerSideProps"),w=new a.PagesRouteModule({definition:{kind:s.A.PAGES,page:"/admin/commands",pathname:"/admin/commands",bundlePath:"",filename:""},components:{App:l.default,Document:i.default},userland:c});o()}catch(e){o(e)}})},9733:e=>{e.exports=import("@chakra-ui/react")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[8270,4874,752,6281,5333],()=>t(9690));module.exports=o})();