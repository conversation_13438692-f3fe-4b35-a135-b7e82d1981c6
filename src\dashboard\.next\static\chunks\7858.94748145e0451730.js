(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7858],{7858:(e,t,i)=>{e=i.nmd(e),ace.define("ace/snippets",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/event_emitter","ace/lib/lang","ace/range","ace/range_list","ace/keyboard/hash_handler","ace/tokenizer","ace/clipboard","ace/editor"],function(e,t,i){"use strict";var n=e("./lib/dom"),o=e("./lib/oop"),r=e("./lib/event_emitter").EventEmitter,s=e("./lib/lang"),a=e("./range").Range,p=e("./range_list").RangeList,c=e("./keyboard/hash_handler").<PERSON>h<PERSON><PERSON><PERSON>,l=e("./tokenizer").Tokenizer,h=e("./clipboard"),u={CURRENT_WORD:function(e){return e.session.getTextRange(e.session.getWordRange())},SELECTION:function(e,t,i){var n=e.session.getTextRange();return i?n.replace(/\n\r?([ \t]*\S)/g,"\n"+i+"$1"):n},CURRENT_LINE:function(e){return e.session.getLine(e.getCursorPosition().row)},PREV_LINE:function(e){return e.session.getLine(e.getCursorPosition().row-1)},LINE_INDEX:function(e){return e.getCursorPosition().row},LINE_NUMBER:function(e){return e.getCursorPosition().row+1},SOFT_TABS:function(e){return e.session.getUseSoftTabs()?"YES":"NO"},TAB_SIZE:function(e){return e.session.getTabSize()},CLIPBOARD:function(e){return h.getText&&h.getText()},FILENAME:function(e){return/[^/\\]*$/.exec(this.FILEPATH(e))[0]},FILENAME_BASE:function(e){return/[^/\\]*$/.exec(this.FILEPATH(e))[0].replace(/\.[^.]*$/,"")},DIRECTORY:function(e){return this.FILEPATH(e).replace(/[^/\\]*$/,"")},FILEPATH:function(e){return"/not implemented.txt"},WORKSPACE_NAME:function(){return"Unknown"},FULLNAME:function(){return"Unknown"},BLOCK_COMMENT_START:function(e){var t=e.session.$mode||{};return t.blockComment&&t.blockComment.start||""},BLOCK_COMMENT_END:function(e){var t=e.session.$mode||{};return t.blockComment&&t.blockComment.end||""},LINE_COMMENT:function(e){return(e.session.$mode||{}).lineCommentStart||""},CURRENT_YEAR:d.bind(null,{year:"numeric"}),CURRENT_YEAR_SHORT:d.bind(null,{year:"2-digit"}),CURRENT_MONTH:d.bind(null,{month:"numeric"}),CURRENT_MONTH_NAME:d.bind(null,{month:"long"}),CURRENT_MONTH_NAME_SHORT:d.bind(null,{month:"short"}),CURRENT_DATE:d.bind(null,{day:"2-digit"}),CURRENT_DAY_NAME:d.bind(null,{weekday:"long"}),CURRENT_DAY_NAME_SHORT:d.bind(null,{weekday:"short"}),CURRENT_HOUR:d.bind(null,{hour:"2-digit",hour12:!1}),CURRENT_MINUTE:d.bind(null,{minute:"2-digit"}),CURRENT_SECOND:d.bind(null,{second:"2-digit"})};function d(e){var t=new Date().toLocaleString("en-us",e);return 1==t.length?"0"+t:t}u.SELECTED_TEXT=u.SELECTION;var f=function(){function e(){this.snippetMap={},this.snippetNameMap={},this.variables=u}return e.prototype.getTokenizer=function(){return e.$tokenizer||this.createTokenizer()},e.prototype.createTokenizer=function(){function t(e){return(e=e.substr(1),/^\d+$/.test(e))?[{tabstopId:parseInt(e,10)}]:[{text:e}]}function i(e){return"(?:[^\\\\"+e+"]|\\\\.)"}var n={regex:"/("+i("/")+"+)/",onMatch:function(e,t,i){var n=i[0];return n.fmtString=!0,n.guard=e.slice(1,-1),n.flag="",""},next:"formatString"};return e.$tokenizer=new l({start:[{regex:/\\./,onMatch:function(e,t,i){var n=e[1];return"}"==n&&i.length?e=n:-1!="`$\\".indexOf(n)&&(e=n),[e]}},{regex:/}/,onMatch:function(e,t,i){return[i.length?i.shift():e]}},{regex:/\$(?:\d+|\w+)/,onMatch:t},{regex:/\$\{[\dA-Z_a-z]+/,onMatch:function(e,i,n){var o=t(e.substr(1));return n.unshift(o[0]),o},next:"snippetVar"},{regex:/\n/,token:"newline",merge:!1}],snippetVar:[{regex:"\\|"+i("\\|")+"*\\|",onMatch:function(e,t,i){var n=e.slice(1,-1).replace(/\\[,|\\]|,/g,function(e){return 2==e.length?e[1]:"\0"}).split("\0").map(function(e){return{value:e}});return i[0].choices=n,[n[0]]},next:"start"},n,{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"start"}],formatString:[{regex:/:/,onMatch:function(e,t,i){return i.length&&i[0].expectElse?(i[0].expectElse=!1,i[0].ifEnd={elseEnd:i[0]},[i[0].ifEnd]):":"}},{regex:/\\./,onMatch:function(e,t,i){var n=e[1];return"}"==n&&i.length||-1!="`$\\".indexOf(n)?e=n:"n"==n?e="\n":"t"==n?e="	":-1!="ulULE".indexOf(n)&&(e={changeCase:n,local:n>"a"}),[e]}},{regex:"/\\w*}",onMatch:function(e,t,i){var n=i.shift();return n&&(n.flag=e.slice(1,-1)),this.next=n&&n.tabstopId?"start":"",[n||e]},next:"start"},{regex:/\$(?:\d+|\w+)/,onMatch:function(e,t,i){return[{text:e.slice(1)}]}},{regex:/\${\w+/,onMatch:function(e,t,i){var n={text:e.slice(2)};return i.unshift(n),[n]},next:"formatStringVar"},{regex:/\n/,token:"newline",merge:!1},{regex:/}/,onMatch:function(e,t,i){var n=i.shift();return this.next=n&&n.tabstopId?"start":"",[n||e]},next:"start"}],formatStringVar:[{regex:/:\/\w+}/,onMatch:function(e,t,i){return i[0].formatFunction=e.slice(2,-1),[i.shift()]},next:"formatString"},n,{regex:/:[\?\-+]?/,onMatch:function(e,t,i){"+"==e[1]&&(i[0].ifEnd=i[0]),"?"==e[1]&&(i[0].expectElse=!0)},next:"formatString"},{regex:"([^:}\\\\]|\\\\.)*:?",token:"",next:"formatString"}]}),e.$tokenizer},e.prototype.tokenizeTmSnippet=function(e,t){return this.getTokenizer().getLineTokens(e,t).tokens.map(function(e){return e.value||e})},e.prototype.getVariableValue=function(e,t,i){if(/^\d+$/.test(t))return(this.variables.__||{})[t]||"";if(/^[A-Z]\d+$/.test(t))return(this.variables[t[0]+"__"]||{})[t.substr(1)]||"";if(t=t.replace(/^TM_/,""),!this.variables.hasOwnProperty(t))return"";var n=this.variables[t];return"function"==typeof n&&(n=this.variables[t](e,t,i)),null==n?"":n},e.prototype.tmStrFormat=function(e,t,i){if(!t.fmt)return e;var n=t.flag||"",o=t.guard;o=new RegExp(o,n.replace(/[^gim]/g,""));var r="string"==typeof t.fmt?this.tokenizeTmSnippet(t.fmt,"formatString"):t.fmt,s=this;return e.replace(o,function(){var e=s.variables.__;s.variables.__=[].slice.call(arguments);for(var t=s.resolveVariables(r,i),n="E",o=0;o<t.length;o++){var a=t[o];if("object"==typeof a)if(t[o]="",a.changeCase&&a.local){var p=t[o+1];p&&"string"==typeof p&&("u"==a.changeCase?t[o]=p[0].toUpperCase():t[o]=p[0].toLowerCase(),t[o+1]=p.substr(1))}else a.changeCase&&(n=a.changeCase);else"U"==n?t[o]=a.toUpperCase():"L"==n&&(t[o]=a.toLowerCase())}return s.variables.__=e,t.join("")})},e.prototype.tmFormatFunction=function(e,t,i){return"upcase"==t.formatFunction?e.toUpperCase():"downcase"==t.formatFunction?e.toLowerCase():e},e.prototype.resolveVariables=function(e,t){for(var i=[],n="",o=!0,r=0;r<e.length;r++){var s=e[r];if("string"==typeof s){i.push(s),"\n"==s?(o=!0,n=""):o&&(n=/^\t*/.exec(s)[0],o=/\S/.test(s));continue}if(s){if(o=!1,s.fmtString){var a=e.indexOf(s,r+1);-1==a&&(a=e.length),s.fmt=e.slice(r+1,a),r=a}if(s.text){var p=this.getVariableValue(t,s.text,n)+"";s.fmtString&&(p=this.tmStrFormat(p,s,t)),s.formatFunction&&(p=this.tmFormatFunction(p,s,t)),p&&!s.ifEnd?(i.push(p),c(s)):!p&&s.ifEnd&&c(s.ifEnd)}else s.elseEnd?c(s.elseEnd):null!=s.tabstopId?i.push(s):null!=s.changeCase&&i.push(s)}}function c(t){var i=e.indexOf(t,r+1);-1!=i&&(r=i)}return i},e.prototype.getDisplayTextForSnippet=function(e,t){return g.call(this,e,t).text},e.prototype.insertSnippetForSelection=function(e,t,i){void 0===i&&(i={});var n=g.call(this,e,t,i),o=e.getSelectionRange(),r=e.session.replace(o,n.text),s=new m(e),a=e.inVirtualSelectionMode&&e.selection.index;s.addTabstops(n.tabstops,o.start,r,a)},e.prototype.insertSnippet=function(e,t,i){void 0===i&&(i={});var n=this;if(e.inVirtualSelectionMode)return n.insertSnippetForSelection(e,t,i);e.forEachSelection(function(){n.insertSnippetForSelection(e,t,i)},null,{keepOrder:!0}),e.tabstopManager&&e.tabstopManager.tabNext()},e.prototype.$getScope=function(e){var t=e.session.$mode.$id||"";if("html"===(t=t.split("/").pop())||"php"===t){"php"!==t||e.session.$mode.inlinePhp||(t="html");var i=e.getCursorPosition(),n=e.session.getState(i.row);"object"==typeof n&&(n=n[0]),n.substring&&("js-"==n.substring(0,3)?t="javascript":"css-"==n.substring(0,4)?t="css":"php-"==n.substring(0,4)&&(t="php"))}return t},e.prototype.getActiveScopes=function(e){var t=this.$getScope(e),i=[t],n=this.snippetMap;return n[t]&&n[t].includeScopes&&i.push.apply(i,n[t].includeScopes),i.push("_"),i},e.prototype.expandWithTab=function(e,t){var i=this,n=e.forEachSelection(function(){return i.expandSnippetForSelection(e,t)},null,{keepOrder:!0});return n&&e.tabstopManager&&e.tabstopManager.tabNext(),n},e.prototype.expandSnippetForSelection=function(e,t){var i,n=e.getCursorPosition(),o=e.session.getLine(n.row),r=o.substring(0,n.column),s=o.substr(n.column),a=this.snippetMap;return this.getActiveScopes(e).some(function(e){var t=a[e];return t&&(i=this.findMatchingSnippet(t,r,s)),!!i},this),!!i&&(!!t&&!!t.dryRun||(e.session.doc.removeInLine(n.row,n.column-i.replaceBefore.length,n.column+i.replaceAfter.length),this.variables.M__=i.matchBefore,this.variables.T__=i.matchAfter,this.insertSnippetForSelection(e,i.content),this.variables.M__=this.variables.T__=null,!0))},e.prototype.findMatchingSnippet=function(e,t,i){for(var n=e.length;n--;){var o=e[n];if((!o.startRe||o.startRe.test(t))&&(!o.endRe||o.endRe.test(i))&&(o.startRe||o.endRe))return o.matchBefore=o.startRe?o.startRe.exec(t):[""],o.matchAfter=o.endRe?o.endRe.exec(i):[""],o.replaceBefore=o.triggerRe?o.triggerRe.exec(t)[0]:"",o.replaceAfter=o.endTriggerRe?o.endTriggerRe.exec(i)[0]:"",o}},e.prototype.register=function(e,t){var i=this.snippetMap,n=this.snippetNameMap,o=this;function r(e){return e&&!/^\^?\(.*\)\$?$|^\\b$/.test(e)&&(e="(?:"+e+")"),e||""}function a(e,t,i){return e=r(e),t=r(t),i?(e=t+e)&&"$"!=e[e.length-1]&&(e+="$"):(e+=t)&&"^"!=e[0]&&(e="^"+e),new RegExp(e)}function p(e){e.scope||(e.scope=t||"_"),i[t=e.scope]||(i[t]=[],n[t]={});var r=n[t];if(e.name){var p=r[e.name];p&&o.unregister(p),r[e.name]=e}i[t].push(e),e.prefix&&(e.tabTrigger=e.prefix),!e.content&&e.body&&(e.content=Array.isArray(e.body)?e.body.join("\n"):e.body),e.tabTrigger&&!e.trigger&&(!e.guard&&/^\w/.test(e.tabTrigger)&&(e.guard="\\b"),e.trigger=s.escapeRegExp(e.tabTrigger)),(e.trigger||e.guard||e.endTrigger||e.endGuard)&&(e.startRe=a(e.trigger,e.guard,!0),e.triggerRe=new RegExp(e.trigger),e.endRe=a(e.endTrigger,e.endGuard,!0),e.endTriggerRe=new RegExp(e.endTrigger))}e||(e=[]),Array.isArray(e)?e.forEach(p):Object.keys(e).forEach(function(t){p(e[t])}),this._signal("registerSnippets",{scope:t})},e.prototype.unregister=function(e,t){var i=this.snippetMap,n=this.snippetNameMap;function o(e){var o=n[e.scope||t];if(o&&o[e.name]){delete o[e.name];var r=i[e.scope||t],s=r&&r.indexOf(e);s>=0&&r.splice(s,1)}}e.content?o(e):Array.isArray(e)&&e.forEach(o)},e.prototype.parseSnippetFile=function(e){e=e.replace(/\r/g,"");for(var t,i=[],n={},o=/^#.*|^({[\s\S]*})\s*$|^(\S+) (.*)$|^((?:\n*\t.*)+)/gm;t=o.exec(e);){if(t[1])try{n=JSON.parse(t[1]),i.push(n)}catch(e){}if(t[4])n.content=t[4].replace(/^\t/gm,""),i.push(n),n={};else{var r=t[2],s=t[3];if("regex"==r){var a=/\/((?:[^\/\\]|\\.)*)|$/g;n.guard=a.exec(s)[1],n.trigger=a.exec(s)[1],n.endTrigger=a.exec(s)[1],n.endGuard=a.exec(s)[1]}else"snippet"==r?(n.tabTrigger=s.match(/^\S*/)[0],n.name||(n.name=s)):r&&(n[r]=s)}}return i},e.prototype.getSnippetByName=function(e,t){var i,n=this.snippetNameMap;return this.getActiveScopes(t).some(function(t){var o=n[t];return o&&(i=o[e]),!!i},this),i},e}();o.implement(f.prototype,r);var g=function(e,t,i){void 0===i&&(i={});var n=e.getCursorPosition(),o=e.session.getLine(n.row),r=e.session.getTabString(),s=o.match(/^\s*/)[0];n.column<s.length&&(s=s.slice(0,n.column)),t=t.replace(/\r/g,"");var a=this.tokenizeTmSnippet(t);a=(a=this.resolveVariables(a,e)).map(function(e){return"\n"!=e||i.excludeExtraIndent?"string"==typeof e?e.replace(/\t/g,r):e:e+s});var p=[];a.forEach(function(e,t){if("object"==typeof e){var i=e.tabstopId,n=p[i];if(n||((n=p[i]=[]).index=i,n.value="",n.parents={}),-1===n.indexOf(e)){e.choices&&!n.choices&&(n.choices=e.choices),n.push(e);var o=a.indexOf(e,t+1);if(-1!==o){var r=a.slice(t+1,o);r.some(function(e){return"object"==typeof e})&&!n.value?n.value=r:r.length&&(!n.value||"string"!=typeof n.value)&&(n.value=r.join(""))}}}}),p.forEach(function(e){e.length=0});for(var c={},l=0;l<a.length;l++){var h=a[l];if("object"==typeof h){var u=h.tabstopId,d=p[u],f=a.indexOf(h,l+1);if(c[u]){c[u]===h&&(delete c[u],Object.keys(c).forEach(function(e){d.parents[e]=!0}));continue}c[u]=h;var g=d.value;"string"!=typeof g?g=function(e){for(var t=[],i=0;i<e.length;i++){var n=e[i];if("object"==typeof n){if(c[n.tabstopId])continue;n=t[e.lastIndexOf(n,i-1)]||{tabstopId:n.tabstopId}}t[i]=n}return t}(g):h.fmt&&(g=this.tmStrFormat(g,h,e)),a.splice.apply(a,[l+1,Math.max(0,f-l)].concat(g,h)),-1===d.indexOf(h)&&d.push(h)}}var m=0,v=0,b="";return a.forEach(function(e){if("string"==typeof e){var t=e.split("\n");t.length>1?(v=t[t.length-1].length,m+=t.length-1):v+=e.length,b+=e}else e&&(e.start?e.end={row:m,column:v}:e.start={row:m,column:v})}),{text:b,tabstops:p,tokens:a}},m=function(){function e(e){if(this.index=0,this.ranges=[],this.tabstops=[],e.tabstopManager)return e.tabstopManager;e.tabstopManager=this,this.$onChange=this.onChange.bind(this),this.$onChangeSelection=s.delayedCall(this.onChangeSelection.bind(this)).schedule,this.$onChangeSession=this.onChangeSession.bind(this),this.$onAfterExec=this.onAfterExec.bind(this),this.attach(e)}return e.prototype.attach=function(e){this.$openTabstops=null,this.selectedTabstop=null,this.editor=e,this.session=e.session,this.editor.on("change",this.$onChange),this.editor.on("changeSelection",this.$onChangeSelection),this.editor.on("changeSession",this.$onChangeSession),this.editor.commands.on("afterExec",this.$onAfterExec),this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler)},e.prototype.detach=function(){this.tabstops.forEach(this.removeTabstopMarkers,this),this.ranges.length=0,this.tabstops.length=0,this.selectedTabstop=null,this.editor.off("change",this.$onChange),this.editor.off("changeSelection",this.$onChangeSelection),this.editor.off("changeSession",this.$onChangeSession),this.editor.commands.off("afterExec",this.$onAfterExec),this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.tabstopManager=null,this.session=null,this.editor=null},e.prototype.onChange=function(e){for(var t="r"==e.action[0],i=this.selectedTabstop||{},n=i.parents||{},o=this.tabstops.slice(),r=0;r<o.length;r++){var s=o[r],a=s==i||n[s.index];if(s.rangeList.$bias=+!a,"remove"==e.action&&s!==i){var p=s.parents&&s.parents[i.index],c=s.rangeList.pointIndex(e.start,p);c=c<0?-c-1:c+1;var l=s.rangeList.pointIndex(e.end,p);l=l<0?-l-1:l-1;for(var h=s.rangeList.ranges.slice(c,l),u=0;u<h.length;u++)this.removeRange(h[u])}s.rangeList.$onChange(e)}var d=this.session;this.$inChange||!t||1!=d.getLength()||d.getValue()||this.detach()},e.prototype.updateLinkedFields=function(){var e=this.selectedTabstop;if(e&&e.hasLinkedRanges&&e.firstNonLinked){this.$inChange=!0;for(var i=this.session,n=i.getTextRange(e.firstNonLinked),o=0;o<e.length;o++){var r=e[o];if(r.linked){var s=r.original,a=t.snippetManager.tmStrFormat(n,s,this.editor);i.replace(r,a)}}this.$inChange=!1}},e.prototype.onAfterExec=function(e){e.command&&!e.command.readOnly&&this.updateLinkedFields()},e.prototype.onChangeSelection=function(){if(this.editor){for(var e=this.editor.selection.lead,t=this.editor.selection.anchor,i=this.editor.selection.isEmpty(),n=0;n<this.ranges.length;n++)if(!this.ranges[n].linked){var o=this.ranges[n].contains(e.row,e.column),r=i||this.ranges[n].contains(t.row,t.column);if(o&&r)return}this.detach()}},e.prototype.onChangeSession=function(){this.detach()},e.prototype.tabNext=function(e){var t=this.tabstops.length,i=this.index+(e||1);(i=Math.min(Math.max(i,1),t))==t&&(i=0),this.selectTabstop(i),this.updateTabstopMarkers(),0===i&&this.detach()},e.prototype.selectTabstop=function(e){this.$openTabstops=null;var t=this.tabstops[this.index];if(t&&this.addTabstopMarkers(t),this.index=e,(t=this.tabstops[this.index])&&t.length){this.selectedTabstop=t;var i=t.firstNonLinked||t;if(t.choices&&(i.cursor=i.start),this.editor.inVirtualSelectionMode)this.editor.selection.fromOrientedRange(i);else{var n=this.editor.multiSelect;n.toSingleRange(i);for(var o=0;o<t.length;o++)t.hasLinkedRanges&&t[o].linked||n.addRange(t[o].clone(),!0)}this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler),this.selectedTabstop&&this.selectedTabstop.choices&&this.editor.execCommand("startAutocomplete",{matches:this.selectedTabstop.choices})}},e.prototype.addTabstops=function(e,t,i){var n=this.useLink||!this.editor.getOption("enableMultiselect");if(this.$openTabstops||(this.$openTabstops=[]),!e[0]){var o=a.fromPoints(i,i);b(o.start,t),b(o.end,t),e[0]=[o],e[0].index=0}var r=[this.index+1,0],s=this.ranges,c=this.snippetId=(this.snippetId||0)+1;e.forEach(function(e,i){var o=this.$openTabstops[i]||e;o.snippetId=c;for(var l=0;l<e.length;l++){var h=e[l],u=a.fromPoints(h.start,h.end||h.start);v(u.start,t),v(u.end,t),u.original=h,u.tabstop=o,s.push(u),o!=e?o.unshift(u):o[l]=u,h.fmtString||o.firstNonLinked&&n?(u.linked=!0,o.hasLinkedRanges=!0):o.firstNonLinked||(o.firstNonLinked=u)}o.firstNonLinked||(o.hasLinkedRanges=!1),o===e&&(r.push(o),this.$openTabstops[i]=o),this.addTabstopMarkers(o),o.rangeList=o.rangeList||new p,o.rangeList.$bias=0,o.rangeList.addList(o)},this),r.length>2&&(this.tabstops.length&&r.push(r.splice(2,1)[0]),this.tabstops.splice.apply(this.tabstops,r))},e.prototype.addTabstopMarkers=function(e){var t=this.session;e.forEach(function(e){e.markerId||(e.markerId=t.addMarker(e,"ace_snippet-marker","text"))})},e.prototype.removeTabstopMarkers=function(e){var t=this.session;e.forEach(function(e){t.removeMarker(e.markerId),e.markerId=null})},e.prototype.updateTabstopMarkers=function(){if(this.selectedTabstop){var e=this.selectedTabstop.snippetId;0===this.selectedTabstop.index&&e--,this.tabstops.forEach(function(t){t.snippetId===e?this.addTabstopMarkers(t):this.removeTabstopMarkers(t)},this)}},e.prototype.removeRange=function(e){var t=e.tabstop.indexOf(e);-1!=t&&e.tabstop.splice(t,1),-1!=(t=this.ranges.indexOf(e))&&this.ranges.splice(t,1),-1!=(t=e.tabstop.rangeList.ranges.indexOf(e))&&e.tabstop.splice(t,1),this.session.removeMarker(e.markerId),!e.tabstop.length&&(-1!=(t=this.tabstops.indexOf(e.tabstop))&&this.tabstops.splice(t,1),this.tabstops.length||this.detach())},e}();m.prototype.keyboardHandler=new c,m.prototype.keyboardHandler.bindKeys({Tab:function(e){t.snippetManager&&t.snippetManager.expandWithTab(e)||(e.tabstopManager.tabNext(1),e.renderer.scrollCursorIntoView())},"Shift-Tab":function(e){e.tabstopManager.tabNext(-1),e.renderer.scrollCursorIntoView()},Esc:function(e){e.tabstopManager.detach()}});var v=function(e,t){0==e.row&&(e.column+=t.column),e.row+=t.row},b=function(e,t){e.row==t.row&&(e.column-=t.column),e.row-=t.row};n.importCssString("\n.ace_snippet-marker {\n    -moz-box-sizing: border-box;\n    box-sizing: border-box;\n    background: rgba(194, 193, 208, 0.09);\n    border: 1px dotted rgba(211, 208, 235, 0.62);\n    position: absolute;\n}","snippets.css",!1),t.snippetManager=new f,(function(){this.insertSnippet=function(e,i){return t.snippetManager.insertSnippet(this,e,i)},this.expandSnippet=function(e){return t.snippetManager.expandWithTab(this,e)}}).call(e("./editor").Editor.prototype)}),ace.define("ace/autocomplete/popup",["require","exports","module","ace/virtual_renderer","ace/editor","ace/range","ace/lib/event","ace/lib/lang","ace/lib/dom","ace/config","ace/lib/useragent"],function(e,t,i){"use strict";var n=e("../virtual_renderer").VirtualRenderer,o=e("../editor").Editor,r=e("../range").Range,s=e("../lib/event"),a=e("../lib/lang"),p=e("../lib/dom"),c=e("../config").nls,l=e("./../lib/useragent"),h=function(e){return"suggest-aria-id:".concat(e)},u=l.isSafari?"menu":"listbox",d=l.isSafari?"menuitem":"option",f=l.isSafari?"aria-current":"aria-selected",g=function(e){var t=new n(e);t.$maxLines=4;var i=new o(t);return i.setHighlightActiveLine(!1),i.setShowPrintMargin(!1),i.renderer.setShowGutter(!1),i.renderer.setHighlightGutterLine(!1),i.$mouseHandler.$focusTimeout=0,i.$highlightTagPending=!0,i};p.importCssString('\n.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\n    background-color: #CAD6FA;\n    z-index: 1;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\n    background-color: #3a674e;\n}\n.ace_editor.ace_autocomplete .ace_line-hover {\n    border: 1px solid #abbffe;\n    margin-top: -1px;\n    background: rgba(233,233,253,0.4);\n    position: absolute;\n    z-index: 2;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {\n    border: 1px solid rgba(109, 150, 13, 0.8);\n    background: rgba(58, 103, 78, 0.62);\n}\n.ace_completion-meta {\n    opacity: 0.5;\n    margin-left: 0.9em;\n}\n.ace_completion-message {\n    margin-left: 0.9em;\n    color: blue;\n}\n.ace_editor.ace_autocomplete .ace_completion-highlight{\n    color: #2d69c7;\n}\n.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{\n    color: #93ca12;\n}\n.ace_editor.ace_autocomplete {\n    width: 300px;\n    z-index: 200000;\n    border: 1px lightgray solid;\n    position: fixed;\n    box-shadow: 2px 3px 5px rgba(0,0,0,.2);\n    line-height: 1.4;\n    background: #fefefe;\n    color: #111;\n}\n.ace_dark.ace_editor.ace_autocomplete {\n    border: 1px #484747 solid;\n    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);\n    line-height: 1.4;\n    background: #25282c;\n    color: #c1c1c1;\n}\n.ace_autocomplete .ace_text-layer  {\n    width: calc(100% - 8px);\n}\n.ace_autocomplete .ace_line {\n    display: flex;\n    align-items: center;\n}\n.ace_autocomplete .ace_line > * {\n    min-width: 0;\n    flex: 0 0 auto;\n}\n.ace_autocomplete .ace_line .ace_ {\n    flex: 0 1 auto;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n.ace_autocomplete .ace_completion-spacer {\n    flex: 1;\n}\n.ace_autocomplete.ace_loading:after  {\n    content: "";\n    position: absolute;\n    top: 0px;\n    height: 2px;\n    width: 8%;\n    background: blue;\n    z-index: 100;\n    animation: ace_progress 3s infinite linear;\n    animation-delay: 300ms;\n    transform: translateX(-100%) scaleX(1);\n}\n@keyframes ace_progress {\n    0% { transform: translateX(-100%) scaleX(1) }\n    50% { transform: translateX(625%) scaleX(2) } \n    100% { transform: translateX(1500%) scaleX(3) } \n}\n@media (prefers-reduced-motion) {\n    .ace_autocomplete.ace_loading:after {\n        transform: translateX(625%) scaleX(2);\n        animation: none;\n     }\n}\n',"autocompletion.css",!1),t.AcePopup=function(e){var t,i=p.createElement("div"),n=g(i);e&&e.appendChild(i),i.style.display="none",n.renderer.content.style.cursor="default",n.renderer.setStyle("ace_autocomplete"),n.renderer.$textLayer.element.setAttribute("role",u),n.renderer.$textLayer.element.setAttribute("aria-roledescription",c("autocomplete.popup.aria-roledescription","Autocomplete suggestions")),n.renderer.$textLayer.element.setAttribute("aria-label",c("autocomplete.popup.aria-label","Autocomplete suggestions")),n.renderer.textarea.setAttribute("aria-hidden","true"),n.setOption("displayIndentGuides",!1),n.setOption("dragDelay",150);var o=function(){};n.focus=o,n.$isFocused=!0,n.renderer.$cursorLayer.restartTimer=o,n.renderer.$cursorLayer.element.style.opacity="0",n.renderer.$maxLines=8,n.renderer.$keepTextAreaAtCursor=!1,n.setHighlightActiveLine(!1),n.session.highlight(""),n.session.$searchHighlight.clazz="ace_highlight-marker",n.on("mousedown",function(e){var t=e.getDocumentPosition();n.selection.moveToPosition(t),m.start.row=m.end.row=t.row,e.stop()});var l=new r(-1,0,-1,1/0),m=new r(-1,0,-1,1/0);m.id=n.session.addMarker(m,"ace_active-line","fullLine"),n.setSelectOnHover=function(e){e?l.id&&(n.session.removeMarker(l.id),l.id=null):l.id=n.session.addMarker(l,"ace_line-hover","fullLine")},n.setSelectOnHover(!1),n.on("mousemove",function(e){if(!t){t=e;return}if(t.x!=e.x||t.y!=e.y){(t=e).scrollTop=n.renderer.scrollTop,n.isMouseOver=!0;var i=t.getDocumentPosition().row;l.start.row!=i&&(l.id||n.setRow(i),b(i))}}),n.renderer.on("beforeRender",function(){if(t&&-1!=l.start.row){t.$pos=null;var e=t.getDocumentPosition().row;l.id||n.setRow(e),b(e,!0)}}),n.renderer.on("afterRender",function(){for(var e=n.renderer.$textLayer,t=e.config.firstRow,i=e.config.lastRow;t<=i;t++){var o=e.element.childNodes[t-e.config.firstRow];o.setAttribute("role",d),o.setAttribute("aria-roledescription",c("autocomplete.popup.item.aria-roledescription","item")),o.setAttribute("aria-setsize",n.data.length),o.setAttribute("aria-describedby","doc-tooltip"),o.setAttribute("aria-posinset",t+1);var r=n.getData(t);if(r){var s="".concat(r.caption||r.value).concat(r.meta?", ".concat(r.meta):"");o.setAttribute("aria-label",s)}o.querySelectorAll(".ace_completion-highlight").forEach(function(e){e.setAttribute("role","mark")})}}),n.renderer.on("afterRender",function(){var e=n.getRow(),t=n.renderer.$textLayer,i=t.element.childNodes[e-t.config.firstRow],o=document.activeElement;if(i!==n.selectedNode&&n.selectedNode&&(p.removeCssClass(n.selectedNode,"ace_selected"),n.selectedNode.removeAttribute(f),n.selectedNode.removeAttribute("id")),o.removeAttribute("aria-activedescendant"),n.selectedNode=i,i){var r=h(e);p.addCssClass(i,"ace_selected"),i.id=r,t.element.setAttribute("aria-activedescendant",r),o.setAttribute("aria-activedescendant",r),i.setAttribute(f,"true")}});var v=function(){b(-1)},b=function(e,t){e!==l.start.row&&(l.start.row=l.end.row=e,t||n.session._emit("changeBackMarker"),n._emit("changeHoverMarker"))};n.getHoveredRow=function(){return l.start.row},s.addListener(n.container,"mouseout",function(){n.isMouseOver=!1,v()}),n.on("hide",v),n.on("changeSelection",v),n.session.doc.getLength=function(){return n.data.length},n.session.doc.getLine=function(e){var t=n.data[e];return"string"==typeof t?t:t&&t.value||""};var x=n.session.bgTokenizer;return x.$tokenizeRow=function(e){var t=n.data[e],i=[];if(!t)return i;"string"==typeof t&&(t={value:t});var o=t.caption||t.value||t.name;function r(e,n){e&&i.push({type:(t.className||"")+(n||""),value:e})}for(var s=o.toLowerCase(),a=(n.filterText||"").toLowerCase(),p=0,c=0,l=0;l<=a.length;l++)if(l!=c&&(t.matchMask&1<<l||l==a.length)){var h=a.slice(c,l);c=l;var u=s.indexOf(h,p);if(-1==u)continue;r(o.slice(p,u),""),p=u+h.length,r(o.slice(u,p),"completion-highlight")}return r(o.slice(p,o.length),""),i.push({type:"completion-spacer",value:" "}),t.meta&&i.push({type:"completion-meta",value:t.meta}),t.message&&i.push({type:"completion-message",value:t.message}),i},x.$updateOnChange=o,x.start=o,n.session.$computeWidth=function(){return this.screenWidth=0},n.isOpen=!1,n.isTopdown=!1,n.autoSelect=!0,n.filterText="",n.isMouseOver=!1,n.data=[],n.setData=function(e,t){n.filterText=t||"",n.setValue(a.stringRepeat("\n",e.length),-1),n.data=e||[],n.setRow(0)},n.getData=function(e){return n.data[e]},n.getRow=function(){return m.start.row},n.setRow=function(e){e=Math.max(this.autoSelect?0:-1,Math.min(this.data.length-1,e)),m.start.row!=e&&(n.selection.clearSelection(),m.start.row=m.end.row=e||0,n.session._emit("changeBackMarker"),n.moveCursorTo(e||0,0),n.isOpen&&n._signal("select"))},n.on("changeSelection",function(){n.isOpen&&n.setRow(n.selection.lead.row),n.renderer.scrollCursorIntoView()}),n.hide=function(){this.container.style.display="none",n.anchorPos=null,n.anchor=null,n.isOpen&&(n.isOpen=!1,this._signal("hide"))},n.tryShow=function(e,i,o,r){if(!r&&n.isOpen&&n.anchorPos&&n.anchor&&n.anchorPos.top===e.top&&n.anchorPos.left===e.left&&n.anchor===o)return!0;var s=this.container,a=this.renderer.scrollBar.width||10,p=window.innerHeight-a,c=window.innerWidth-a,l=this.renderer,h=l.$maxLines*i*1.4,u={top:0,bottom:0},d=p-e.top-3*this.$borderSize-i,f=e.top-3*this.$borderSize;o||(o=f<=d||d>=h?"bottom":"top"),"top"===o?(u.bottom=e.top-this.$borderSize,u.top=u.bottom-h):"bottom"===o&&(u.top=e.top+i+this.$borderSize,u.bottom=u.top+h);var g=u.top>=0&&u.bottom<=p;if(!r&&!g)return!1;g?l.$maxPixelHeight=null:"top"===o?l.$maxPixelHeight=f:l.$maxPixelHeight=d,"top"===o?(s.style.top="",s.style.bottom=p+a-u.bottom+"px",n.isTopdown=!1):(s.style.top=u.top+"px",s.style.bottom="",n.isTopdown=!0),s.style.display="";var m=e.left;return m+s.offsetWidth>c&&(m=c-s.offsetWidth),s.style.left=m+"px",s.style.right="",n.isOpen||(n.isOpen=!0,this._signal("show"),t=null),n.anchorPos=e,n.anchor=o,!0},n.show=function(e,t,i){this.tryShow(e,t,i?"bottom":void 0,!0)},n.goTo=function(e){var t=this.getRow(),i=this.session.getLength()-1;switch(e){case"up":t=t<=0?i:t-1;break;case"down":t=t>=i?-1:t+1;break;case"start":t=0;break;case"end":t=i}this.setRow(t)},n.getTextLeftOffset=function(){return this.$borderSize+this.renderer.$padding+this.$imageSize},n.$imageSize=0,n.$borderSize=1,n},t.$singleLineEditor=g,t.getAriaId=h}),ace.define("ace/autocomplete/inline_screenreader",["require","exports","module"],function(e,t,i){"use strict";t.AceInlineScreenReader=function(){function e(e){this.editor=e,this.screenReaderDiv=document.createElement("div"),this.screenReaderDiv.classList.add("ace_screenreader-only"),this.editor.container.appendChild(this.screenReaderDiv)}return e.prototype.setScreenReaderContent=function(e){for(!this.popup&&this.editor.completer&&this.editor.completer.popup&&(this.popup=this.editor.completer.popup,this.popup.renderer.on("afterRender",(function(){var e=this.popup.getRow(),t=this.popup.renderer.$textLayer,i=t.element.childNodes[e-t.config.firstRow];if(i){for(var n="doc-tooltip ",o=0;o<this._lines.length;o++)n+="ace-inline-screenreader-line-".concat(o," ");i.setAttribute("aria-describedby",n)}}).bind(this)));this.screenReaderDiv.firstChild;)this.screenReaderDiv.removeChild(this.screenReaderDiv.firstChild);this._lines=e.split(/\r\n|\r|\n/);var t=this.createCodeBlock();this.screenReaderDiv.appendChild(t)},e.prototype.destroy=function(){this.screenReaderDiv.remove()},e.prototype.createCodeBlock=function(){var e=document.createElement("pre");e.setAttribute("id","ace-inline-screenreader");for(var t=0;t<this._lines.length;t++){var i=document.createElement("code");i.setAttribute("id","ace-inline-screenreader-line-".concat(t));var n=document.createTextNode(this._lines[t]);i.appendChild(n),e.appendChild(i)}return e},e}()}),ace.define("ace/autocomplete/inline",["require","exports","module","ace/snippets","ace/autocomplete/inline_screenreader"],function(e,t,i){"use strict";var n=e("../snippets").snippetManager,o=e("./inline_screenreader").AceInlineScreenReader;t.AceInline=function(){function e(){this.editor=null}return e.prototype.show=function(e,t,i){if(i=i||"",e&&this.editor&&this.editor!==e&&(this.hide(),this.editor=null,this.inlineScreenReader=null),!e||!t)return!1;this.inlineScreenReader||(this.inlineScreenReader=new o(e));var r=t.snippet?n.getDisplayTextForSnippet(e,t.snippet):t.value;return!t.hideInlinePreview&&!!r&&!!r.startsWith(i)&&(this.editor=e,this.inlineScreenReader.setScreenReaderContent(r),""===(r=r.slice(i.length))?e.removeGhostText():e.setGhostText(r),!0)},e.prototype.isOpen=function(){return!!this.editor&&!!this.editor.renderer.$ghostText},e.prototype.hide=function(){return!!this.editor&&(this.editor.removeGhostText(),!0)},e.prototype.destroy=function(){this.hide(),this.editor=null,this.inlineScreenReader&&(this.inlineScreenReader.destroy(),this.inlineScreenReader=null)},e}()}),ace.define("ace/autocomplete/util",["require","exports","module"],function(e,t,i){"use strict";t.parForEach=function(e,t,i){var n=0,o=e.length;0===o&&i();for(var r=0;r<o;r++)t(e[r],function(e,t){++n===o&&i(e,t)})};var n=/[a-zA-Z_0-9\$\-\u00A2-\u2000\u2070-\uFFFF]/;t.retrievePrecedingIdentifier=function(e,t,i){i=i||n;for(var o=[],r=t-1;r>=0;r--)if(i.test(e[r]))o.push(e[r]);else break;return o.reverse().join("")},t.retrieveFollowingIdentifier=function(e,t,i){i=i||n;for(var o=[],r=t;r<e.length;r++)if(i.test(e[r]))o.push(e[r]);else break;return o},t.getCompletionPrefix=function(e){var t,i=e.getCursorPosition(),n=e.session.getLine(i.row);return e.completers.forEach((function(e){e.identifierRegexps&&e.identifierRegexps.forEach((function(e){!t&&e&&(t=this.retrievePrecedingIdentifier(n,i.column,e))}).bind(this))}).bind(this)),t||this.retrievePrecedingIdentifier(n,i.column)},t.triggerAutocomplete=function(e,t){var t=null==t?e.session.getPrecedingCharacter():t;return e.completers.some(function(e){if(e.triggerCharacters&&Array.isArray(e.triggerCharacters))return e.triggerCharacters.includes(t)})}}),ace.define("ace/autocomplete",["require","exports","module","ace/keyboard/hash_handler","ace/autocomplete/popup","ace/autocomplete/inline","ace/autocomplete/popup","ace/autocomplete/util","ace/lib/lang","ace/lib/dom","ace/snippets","ace/config","ace/lib/event","ace/lib/scroll"],function(e,t,i){"use strict";var n=e("./keyboard/hash_handler").HashHandler,o=e("./autocomplete/popup").AcePopup,r=e("./autocomplete/inline").AceInline,s=e("./autocomplete/popup").getAriaId,a=e("./autocomplete/util"),p=e("./lib/lang"),c=e("./lib/dom"),l=e("./snippets").snippetManager,h=e("./config"),u=e("./lib/event"),d=e("./lib/scroll").preventParentScroll,f=function(e,t){t.completer&&t.completer.destroy()},g=function(){function e(){this.autoInsert=!1,this.autoSelect=!0,this.autoShown=!1,this.exactMatch=!1,this.inlineEnabled=!1,this.keyboardHandler=new n,this.keyboardHandler.bindKeys(this.commands),this.parentNode=null,this.setSelectOnHover=!1,this.hasSeen=new Set,this.showLoadingState=!1,this.stickySelectionDelay=500,this.blurListener=this.blurListener.bind(this),this.changeListener=this.changeListener.bind(this),this.mousedownListener=this.mousedownListener.bind(this),this.mousewheelListener=this.mousewheelListener.bind(this),this.onLayoutChange=this.onLayoutChange.bind(this),this.changeTimer=p.delayedCall((function(){this.updateCompletions(!0)}).bind(this)),this.tooltipTimer=p.delayedCall(this.updateDocTooltip.bind(this),50),this.popupTimer=p.delayedCall(this.$updatePopupPosition.bind(this),50),this.stickySelectionTimer=p.delayedCall((function(){this.stickySelection=!0}).bind(this),this.stickySelectionDelay),this.$firstOpenTimer=p.delayedCall((function(){var t=this.completionProvider&&this.completionProvider.initialPosition;this.autoShown||this.popup&&this.popup.isOpen||!t||0===this.editor.completers.length||(this.completions=new v(e.completionsForLoading),this.openPopup(this.editor,t.prefix,!1),this.popup.renderer.setStyle("ace_loading",!0))}).bind(this),this.stickySelectionDelay)}return Object.defineProperty(e,"completionsForLoading",{get:function(){return[{caption:h.nls("autocomplete.loading","Loading..."),value:""}]},enumerable:!1,configurable:!0}),e.prototype.$init=function(){return this.popup=new o(this.parentNode||document.body||document.documentElement),this.popup.on("click",(function(e){this.insertMatch(),e.stop()}).bind(this)),this.popup.focus=this.editor.focus.bind(this.editor),this.popup.on("show",this.$onPopupShow.bind(this)),this.popup.on("hide",this.$onHidePopup.bind(this)),this.popup.on("select",this.$onPopupChange.bind(this)),u.addListener(this.popup.container,"mouseout",this.mouseOutListener.bind(this)),this.popup.on("changeHoverMarker",this.tooltipTimer.bind(null,null)),this.popup.renderer.on("afterRender",this.$onPopupRender.bind(this)),this.popup},e.prototype.$initInline=function(){if(this.inlineEnabled&&!this.inlineRenderer)return this.inlineRenderer=new r,this.inlineRenderer},e.prototype.getPopup=function(){return this.popup||this.$init()},e.prototype.$onHidePopup=function(){this.inlineRenderer&&this.inlineRenderer.hide(),this.hideDocTooltip(),this.stickySelectionTimer.cancel(),this.popupTimer.cancel(),this.stickySelection=!1},e.prototype.$seen=function(e){!this.hasSeen.has(e)&&e&&e.completer&&e.completer.onSeen&&"function"==typeof e.completer.onSeen&&(e.completer.onSeen(this.editor,e),this.hasSeen.add(e))},e.prototype.$onPopupChange=function(e){if(this.inlineRenderer&&this.inlineEnabled){var t=e?null:this.popup.getData(this.popup.getRow());if(this.$updateGhostText(t),this.popup.isMouseOver&&this.setSelectOnHover)return void this.tooltipTimer.call(null,null);this.popupTimer.schedule(),this.tooltipTimer.schedule()}else this.popupTimer.call(null,null),this.tooltipTimer.call(null,null)},e.prototype.$updateGhostText=function(e){var t=this.base.row,i=this.base.column,n=this.editor.getCursorPosition().column,o=this.editor.session.getLine(t).slice(i,n);this.inlineRenderer.show(this.editor,e,o)?this.$seen(e):this.inlineRenderer.hide()},e.prototype.$onPopupRender=function(){var e=this.inlineRenderer&&this.inlineEnabled;if(this.completions&&this.completions.filtered&&this.completions.filtered.length>0)for(var t=this.popup.getFirstVisibleRow();t<=this.popup.getLastVisibleRow();t++){var i=this.popup.getData(t);i&&(!e||i.hideInlinePreview)&&this.$seen(i)}},e.prototype.$onPopupShow=function(e){this.$onPopupChange(e),this.stickySelection=!1,this.stickySelectionDelay>=0&&this.stickySelectionTimer.schedule(this.stickySelectionDelay)},e.prototype.observeLayoutChanges=function(){if(!this.$elements&&this.editor){window.addEventListener("resize",this.onLayoutChange,{passive:!0}),window.addEventListener("wheel",this.mousewheelListener);for(var e=this.editor.container.parentNode,t=[];e;)t.push(e),e.addEventListener("scroll",this.onLayoutChange,{passive:!0}),e=e.parentNode;this.$elements=t}},e.prototype.unObserveLayoutChanges=function(){var e=this;window.removeEventListener("resize",this.onLayoutChange,{passive:!0}),window.removeEventListener("wheel",this.mousewheelListener),this.$elements&&this.$elements.forEach(function(t){t.removeEventListener("scroll",e.onLayoutChange,{passive:!0})}),this.$elements=null},e.prototype.onLayoutChange=function(){if(!this.popup.isOpen)return this.unObserveLayoutChanges();this.$updatePopupPosition(),this.updateDocTooltip()},e.prototype.$updatePopupPosition=function(){var e=this.editor,t=e.renderer,i=t.layerConfig.lineHeight,n=t.$cursorLayer.getPixelPosition(this.base,!0);n.left-=this.popup.getTextLeftOffset();var o=e.container.getBoundingClientRect();n.top+=o.top-t.layerConfig.offset,n.left+=o.left-e.renderer.scrollLeft,n.left+=t.gutterWidth;var r={top:n.top,left:n.left};t.$ghostText&&t.$ghostTextWidget&&this.base.row===t.$ghostText.position.row&&(r.top+=t.$ghostTextWidget.el.offsetHeight);var s=e.container.getBoundingClientRect().bottom-i,a=s<r.top?{top:s,left:r.left}:r;!this.popup.tryShow(a,i,"bottom")&&(this.popup.tryShow(n,i,"top")||this.popup.show(n,i))},e.prototype.openPopup=function(e,t,i){this.$firstOpenTimer.cancel(),this.popup||this.$init(),this.inlineEnabled&&!this.inlineRenderer&&this.$initInline(),this.popup.autoSelect=this.autoSelect,this.popup.setSelectOnHover(this.setSelectOnHover);var n,o=this.popup.getRow(),r=this.popup.data[o];this.popup.setData(this.completions.filtered,this.completions.filterText),this.editor.textInput.setAriaOptions&&this.editor.textInput.setAriaOptions({activeDescendant:s(this.popup.getRow()),inline:this.inlineEnabled}),e.keyBinding.addKeyboardHandler(this.keyboardHandler),this.stickySelection&&(n=this.popup.data.indexOf(r)),n&&-1!==n||(n=0),this.popup.setRow(this.autoSelect?n:-1),n===o&&r!==this.completions.filtered[n]&&this.$onPopupChange();var a=this.inlineRenderer&&this.inlineEnabled;if(n===o&&a){var p=this.popup.getData(this.popup.getRow());this.$updateGhostText(p)}!i&&(this.popup.setTheme(e.getTheme()),this.popup.setFontSize(e.getFontSize()),this.$updatePopupPosition(),this.tooltipNode&&this.updateDocTooltip()),this.changeTimer.cancel(),this.observeLayoutChanges()},e.prototype.detach=function(){this.editor&&(this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler),this.editor.off("changeSelection",this.changeListener),this.editor.off("blur",this.blurListener),this.editor.off("mousedown",this.mousedownListener),this.editor.off("mousewheel",this.mousewheelListener)),this.$firstOpenTimer.cancel(),this.changeTimer.cancel(),this.hideDocTooltip(),this.completionProvider&&this.completionProvider.detach(),this.popup&&this.popup.isOpen&&this.popup.hide(),this.popup&&this.popup.renderer&&this.popup.renderer.off("afterRender",this.$onPopupRender),this.base&&this.base.detach(),this.activated=!1,this.completionProvider=this.completions=this.base=null,this.unObserveLayoutChanges()},e.prototype.changeListener=function(e){var t=this.editor.selection.lead;(t.row!=this.base.row||t.column<this.base.column)&&this.detach(),this.activated?this.changeTimer.schedule():this.detach()},e.prototype.blurListener=function(e){var t=document.activeElement,i=this.editor.textInput.getElement(),n=e.relatedTarget&&this.tooltipNode&&this.tooltipNode.contains(e.relatedTarget),o=this.popup&&this.popup.container;t==i||t.parentNode==o||n||t==this.tooltipNode||e.relatedTarget==i||this.detach()},e.prototype.mousedownListener=function(e){this.detach()},e.prototype.mousewheelListener=function(e){this.popup&&!this.popup.isMouseOver&&this.detach()},e.prototype.mouseOutListener=function(e){this.popup.isOpen&&this.$updatePopupPosition()},e.prototype.goTo=function(e){this.popup.goTo(e)},e.prototype.insertMatch=function(e,t){if(e||(e=this.popup.getData(this.popup.getRow())),!e)return!1;if(""===e.value)return this.detach();var i=this.completions,n=this.getCompletionProvider().insertMatch(this.editor,e,i.filterText,t);return this.completions==i&&this.detach(),n},e.prototype.showPopup=function(e,t){this.editor&&this.detach(),this.activated=!0,this.editor=e,e.completer!=this&&(e.completer&&e.completer.detach(),e.completer=this),e.on("changeSelection",this.changeListener),e.on("blur",this.blurListener),e.on("mousedown",this.mousedownListener),e.on("mousewheel",this.mousewheelListener),this.updateCompletions(!1,t)},e.prototype.getCompletionProvider=function(e){return this.completionProvider||(this.completionProvider=new m(e)),this.completionProvider},e.prototype.gatherCompletions=function(e,t){return this.getCompletionProvider().gatherCompletions(e,t)},e.prototype.updateCompletions=function(t,i){if(t&&this.base&&this.completions){var n=this.editor.getCursorPosition(),o=this.editor.session.getTextRange({start:this.base,end:n});return o==this.completions.filterText?void 0:(this.completions.setFilter(o),this.completions.filtered.length&&(1!=this.completions.filtered.length||this.completions.filtered[0].value!=o||this.completions.filtered[0].snippet))?void this.openPopup(this.editor,o,t):this.detach()}if(i&&i.matches){var n=this.editor.getSelectionRange().start;return this.base=this.editor.session.doc.createAnchor(n.row,n.column),this.base.$insertRight=!0,this.completions=new v(i.matches),this.getCompletionProvider().completions=this.completions,this.openPopup(this.editor,"",t)}var r=this.editor.getSession(),n=this.editor.getCursorPosition(),o=a.getCompletionPrefix(this.editor);this.base=r.doc.createAnchor(n.row,n.column-o.length),this.base.$insertRight=!0;var s={exactMatch:this.exactMatch,ignoreCaption:this.ignoreCaption};this.getCompletionProvider({prefix:o,pos:n}).provideCompletions(this.editor,s,(function(i,n,o){var r=n.filtered,s=a.getCompletionPrefix(this.editor);if(this.$firstOpenTimer.cancel(),o){if(!r.length){var p=!this.autoShown&&this.emptyMessage;if("function"==typeof p&&(p=this.emptyMessage(s)),p){var c=[{caption:p,value:""}];this.completions=new v(c),this.openPopup(this.editor,s,t),this.popup.renderer.setStyle("ace_loading",!1),this.popup.renderer.setStyle("ace_empty-message",!0);return}return this.detach()}if(1==r.length&&r[0].value==s&&!r[0].snippet)return this.detach();if(this.autoInsert&&!this.autoShown&&1==r.length)return this.insertMatch(r[0])}this.completions=!o&&this.showLoadingState?new v(e.completionsForLoading.concat(r),n.filterText):n,this.openPopup(this.editor,s,t),this.popup.renderer.setStyle("ace_empty-message",!1),this.popup.renderer.setStyle("ace_loading",!o)}).bind(this)),!this.showLoadingState||this.autoShown||this.popup&&this.popup.isOpen||this.$firstOpenTimer.delay(this.stickySelectionDelay/2)},e.prototype.cancelContextMenu=function(){this.editor.$mouseHandler.cancelContextMenu()},e.prototype.updateDocTooltip=function(){var e=this.popup,t=this.completions&&this.completions.filtered,i=t&&(t[e.getHoveredRow()]||t[e.getRow()]),n=null;if(!i||!this.editor||!this.popup.isOpen)return this.hideDocTooltip();for(var o=this.editor.completers.length,r=0;r<o;r++){var s=this.editor.completers[r];if(s.getDocTooltip&&i.completerId===s.id){n=s.getDocTooltip(i);break}}if(n||"string"==typeof i||(n=i),"string"==typeof n&&(n={docText:n}),!n||!(n.docHTML||n.docText))return this.hideDocTooltip();this.showDocTooltip(n)},e.prototype.showDocTooltip=function(e){this.tooltipNode||(this.tooltipNode=c.createElement("div"),this.tooltipNode.style.margin="0",this.tooltipNode.style.pointerEvents="auto",this.tooltipNode.style.overscrollBehavior="contain",this.tooltipNode.tabIndex=-1,this.tooltipNode.onblur=this.blurListener.bind(this),this.tooltipNode.onclick=this.onTooltipClick.bind(this),this.tooltipNode.id="doc-tooltip",this.tooltipNode.setAttribute("role","tooltip"),this.tooltipNode.addEventListener("wheel",d));var t=this.editor.renderer.theme;this.tooltipNode.className="ace_tooltip ace_doc-tooltip "+(t.isDark?"ace_dark ":"")+(t.cssClass||"");var i=this.tooltipNode;e.docHTML?i.innerHTML=e.docHTML:e.docText&&(i.textContent=e.docText),i.parentNode||this.popup.container.appendChild(this.tooltipNode);var n=this.popup,o=n.container.getBoundingClientRect(),r=n.renderer.scrollBar.width||10,s=o.left,a=[Math.min((window.innerWidth-o.right-r)/400,1),Math.min(s/400,1),Math.min((n.isTopdown?o.top:window.innerHeight-r-o.bottom)/300*.9)],p=Math.max.apply(Math,a),l=i.style;l.display="block",p==a[0]?(l.left=o.right+1+"px",l.right="",l.maxWidth=400*p+"px",l.top=o.top+"px",l.bottom="",l.maxHeight=Math.min(window.innerHeight-r-o.top,300)+"px"):p==a[1]?(l.right=window.innerWidth-o.left+"px",l.left="",l.maxWidth=400*p+"px",l.top=o.top+"px",l.bottom="",l.maxHeight=Math.min(window.innerHeight-r-o.top,300)+"px"):p==a[2]&&(l.left=window.innerWidth-o.left+"px",l.maxWidth=Math.min(400,window.innerWidth)+"px",n.isTopdown?(l.top=o.bottom+"px",l.left=o.left+"px",l.right="",l.bottom="",l.maxHeight=Math.min(window.innerHeight-r-o.bottom,300)+"px"):(l.top=n.container.offsetTop-i.offsetHeight+"px",l.left=o.left+"px",l.right="",l.bottom="",l.maxHeight=Math.min(n.container.offsetTop,300)+"px"))},e.prototype.hideDocTooltip=function(){if(this.tooltipTimer.cancel(),this.tooltipNode){var e=this.tooltipNode;this.editor.isFocused()||document.activeElement!=e||this.editor.focus(),this.tooltipNode=null,e.parentNode&&e.parentNode.removeChild(e)}},e.prototype.onTooltipClick=function(e){for(var t=e.target;t&&t!=this.tooltipNode;){if("A"==t.nodeName&&t.href){t.rel="noreferrer",t.target="_blank";break}t=t.parentNode}},e.prototype.destroy=function(){if(this.detach(),this.popup){this.popup.destroy();var e=this.popup.container;e&&e.parentNode&&e.parentNode.removeChild(e)}this.editor&&this.editor.completer==this&&(this.editor.off("destroy",f),this.editor.completer=null),this.inlineRenderer=this.popup=this.editor=null},e.for=function(t){return t.completer instanceof e||(t.completer&&(t.completer.destroy(),t.completer=null),h.get("sharedPopups")?(e.$sharedInstance||(e.$sharedInstance=new e),t.completer=e.$sharedInstance):(t.completer=new e,t.once("destroy",f))),t.completer},e}();g.prototype.commands={Up:function(e){e.completer.goTo("up")},Down:function(e){e.completer.goTo("down")},"Ctrl-Up|Ctrl-Home":function(e){e.completer.goTo("start")},"Ctrl-Down|Ctrl-End":function(e){e.completer.goTo("end")},Esc:function(e){e.completer.detach()},Return:function(e){return e.completer.insertMatch()},"Shift-Return":function(e){e.completer.insertMatch(null,{deleteSuffix:!0})},Tab:function(e){var t=e.completer.insertMatch();if(t||e.tabstopManager)return t;e.completer.goTo("down")},Backspace:function(e){e.execCommand("backspace"),!a.getCompletionPrefix(e)&&e.completer&&e.completer.detach()},PageUp:function(e){e.completer.popup.gotoPageUp()},PageDown:function(e){e.completer.popup.gotoPageDown()}},g.startCommand={name:"startAutocomplete",exec:function(e,t){var i=g.for(e);i.autoInsert=!1,i.autoSelect=!0,i.autoShown=!1,i.showPopup(e,t),i.cancelContextMenu()},bindKey:"Ctrl-Space|Ctrl-Shift-Space|Alt-Space"};var m=function(){function e(e){this.initialPosition=e,this.active=!0}return e.prototype.insertByIndex=function(e,t,i){return!!this.completions&&!!this.completions.filtered&&this.insertMatch(e,this.completions.filtered[t],i)},e.prototype.insertMatch=function(e,t,i){if(!t)return!1;if(e.startOperation({command:{name:"insertMatch"}}),t.completer&&t.completer.insertMatch)t.completer.insertMatch(e,t);else{if(!this.completions)return!1;var n=this.completions.filterText.length,o=0;if(t.range&&t.range.start.row===t.range.end.row&&(n-=this.initialPosition.prefix.length,n+=this.initialPosition.pos.column-t.range.start.column,o+=t.range.end.column-this.initialPosition.pos.column),n||o){r=e.selection.getAllRanges?e.selection.getAllRanges():[e.getSelectionRange()];for(var r,s,a=0;s=r[a];a++)s.start.column-=n,s.end.column+=o,e.session.remove(s)}t.snippet?l.insertSnippet(e,t.snippet):this.$insertString(e,t),t.completer&&t.completer.onInsert&&"function"==typeof t.completer.onInsert&&t.completer.onInsert(e,t),t.command&&"startAutocomplete"===t.command&&e.execCommand(t.command)}return e.endOperation(),!0},e.prototype.$insertString=function(e,t){var i=t.value||t;e.execCommand("insertstring",i)},e.prototype.gatherCompletions=function(e,t){var i=e.getSession(),n=e.getCursorPosition(),o=a.getCompletionPrefix(e),r=[];this.completers=e.completers;var s=e.completers.length;return e.completers.forEach(function(p,c){p.getCompletions(e,i,n,o,function(i,n){p.hideInlinePreview&&(n=n.map(function(e){return Object.assign(e,{hideInlinePreview:p.hideInlinePreview})})),!i&&n&&(r=r.concat(n)),t(null,{prefix:a.getCompletionPrefix(e),matches:r,finished:0==--s})})}),!0},e.prototype.provideCompletions=function(e,t,i){var n=(function(e){var n=e.prefix,o=e.matches;this.completions=new v(o),t.exactMatch&&(this.completions.exactMatch=!0),t.ignoreCaption&&(this.completions.ignoreCaption=!0),this.completions.setFilter(n),(e.finished||this.completions.filtered.length)&&i(null,this.completions,e.finished)}).bind(this),o=!0,r=null;if(this.gatherCompletions(e,(function(e,t){if(this.active&&(e&&(i(e,[],!0),this.detach()),0===t.prefix.indexOf(t.prefix))){if(o){r=t;return}n(t)}}).bind(this)),o=!1,r){var s=r;r=null,n(s)}},e.prototype.detach=function(){this.active=!1,this.completers&&this.completers.forEach(function(e){"function"==typeof e.cancel&&e.cancel()})},e}(),v=function(){function e(e,t){this.all=e,this.filtered=e,this.filterText=t||"",this.exactMatch=!1,this.ignoreCaption=!1}return e.prototype.setFilter=function(e){if(e.length>this.filterText&&0===e.lastIndexOf(this.filterText,0))var t=this.filtered;else var t=this.all;this.filterText=e;var i=null;t=(t=(t=this.filterCompletions(t,this.filterText)).sort(function(e,t){return t.exactMatch-e.exactMatch||t.$score-e.$score||(e.caption||e.value).localeCompare(t.caption||t.value)})).filter(function(e){var t=e.snippet||e.caption||e.value;return t!==i&&(i=t,!0)}),this.filtered=t},e.prototype.filterCompletions=function(e,t){var i=[],n=t.toUpperCase(),o=t.toLowerCase();e:for(var r,s=0;r=e[s];s++){if(r.skipFilter){r.$score=r.score,i.push(r);continue}var a,p,c=!this.ignoreCaption&&r.caption||r.value||r.snippet;if(c){var l=-1,h=0,u=0;if(this.exactMatch){if(t!==c.substr(0,t.length))continue}else{var d=c.toLowerCase().indexOf(o);if(d>-1)u=d;else for(var f=0;f<t.length;f++){var g=c.indexOf(o[f],l+1),m=c.indexOf(n[f],l+1);if((a=g>=0&&(m<0||g<m)?g:m)<0)continue e;(p=a-l-1)>0&&(-1===l&&(u+=10),u+=p,h|=1<<f),l=a}}r.matchMask=h,r.exactMatch=+!u,r.$score=(r.score||0)-u,i.push(r)}}return i},e}();t.Autocomplete=g,t.CompletionProvider=m,t.FilteredList=v}),ace.define("ace/marker_group",["require","exports","module"],function(e,t,i){"use strict";var n=function(){function e(e,t){t&&(this.markerType=t.markerType),this.markers=[],this.session=e,e.addDynamicMarker(this)}return e.prototype.getMarkerAtPosition=function(e){return this.markers.find(function(t){return t.range.contains(e.row,e.column)})},e.prototype.markersComparator=function(e,t){return e.range.start.row-t.range.start.row},e.prototype.setMarkers=function(e){this.markers=e.sort(this.markersComparator).slice(0,this.MAX_MARKERS),this.session._signal("changeBackMarker")},e.prototype.update=function(e,t,i,n){if(this.markers&&this.markers.length)for(var o,r=n.firstRow,s=n.lastRow,a=0,p=0,c=0;c<this.markers.length;c++){var l=this.markers[c];if(!(l.range.end.row<r)&&!(l.range.start.row>s)){if(l.range.start.row===p?a++:(p=l.range.start.row,a=0),!(a>200)){var h=l.range.clipRows(r,s);if(h.start.row!==h.end.row||h.start.column!==h.end.column){var u=h.toScreenRange(i);if(u.isEmpty()){(o=i.getNextFoldLine(h.end.row,o))&&o.end.row>h.end.row&&(r=o.end.row);continue}"fullLine"===this.markerType?t.drawFullLineMarker(e,u,l.className,n):u.isMultiLine()?"line"===this.markerType?t.drawMultiLineMarker(e,u,l.className,n):t.drawTextMarker(e,u,l.className,n):t.drawSingleLineMarker(e,u,l.className+" ace_br15",n)}}}}},e}();n.prototype.MAX_MARKERS=1e4,t.MarkerGroup=n}),ace.define("ace/autocomplete/text_completer",["require","exports","module","ace/range"],function(e,t,i){var n=e("../range").Range,o=/[^a-zA-Z_0-9\$\-\u00C0-\u1FFF\u2C00-\uD7FF\w]+/;t.getCompletions=function(e,t,i,r,s){var a,p,c,l,h=(a=t.getTextRange(n.fromPoints({row:0,column:0},i)).split(o).length-1,p=t.getValue().split(o),c=Object.create(null),l=p[a],p.forEach(function(e,t){if(e&&e!==l){var i=Math.abs(a-t),n=p.length-i;c[e]?c[e]=Math.max(n,c[e]):c[e]=n}}),c);s(null,Object.keys(h).map(function(e){return{caption:e,value:e,score:h[e],meta:"local"}}))}}),ace.define("ace/ext/language_tools",["require","exports","module","ace/snippets","ace/autocomplete","ace/config","ace/lib/lang","ace/autocomplete/util","ace/marker_group","ace/autocomplete/text_completer","ace/editor","ace/config"],function(e,t,i){"use strict";var n,o=e("../snippets").snippetManager,r=e("../autocomplete").Autocomplete,s=e("../config"),a=e("../lib/lang"),p=e("../autocomplete/util"),c=e("../marker_group").MarkerGroup,l=e("../autocomplete/text_completer"),h={getCompletions:function(e,t,i,n,o){if(t.$mode.completer)return t.$mode.completer.getCompletions(e,t,i,n,o);var r=e.session.getState(i.row),s=t.$mode.getCompletions(r,t,i,n);o(null,s=s.map(function(e){return e.completerId=h.id,e}))},id:"keywordCompleter"},u=function(e){var t={};return e.replace(/\${(\d+)(:(.*?))?}/g,function(e,i,n,o){return t[i]=o||""}).replace(/\$(\d+?)/g,function(e,i){return t[i]})},d={getCompletions:function(e,t,i,n,r){var s=[],a=t.getTokenAt(i.row,i.column);a&&a.type.match(/(tag-name|tag-open|tag-whitespace|attribute-name|attribute-value)\.xml$/)?s.push("html-tag"):s=o.getActiveScopes(e);var p=o.snippetMap,c=[];s.forEach(function(e){for(var t=p[e]||[],i=t.length;i--;){var n=t[i],o=n.name||n.tabTrigger;o&&c.push({caption:o,snippet:n.content,meta:n.tabTrigger&&!n.name?n.tabTrigger+"⇥ ":"snippet",completerId:d.id})}},this),r(null,c)},getDocTooltip:function(e){e.snippet&&!e.docHTML&&(e.docHTML=["<b>",a.escapeHTML(e.caption),"</b>","<hr></hr>",a.escapeHTML(u(e.snippet))].join(""))},id:"snippetCompleter"},f=[d,l,h];t.setCompleters=function(e){f.length=0,e&&f.push.apply(f,e)},t.addCompleter=function(e){f.push(e)},t.textCompleter=l,t.keyWordCompleter=h,t.snippetCompleter=d;var g={name:"expandSnippet",exec:function(e){return o.expandWithTab(e)},bindKey:"Tab"},m=function(e,t){v(t.session.$mode)},v=function(e){"string"==typeof e&&(e=s.$modes[e]),e&&(o.files||(o.files={}),b(e.$id,e.snippetFileId),e.modes&&e.modes.forEach(v))},b=function(e,t){t&&e&&!o.files[e]&&(o.files[e]={},s.loadModule(t,function(t){t&&(o.files[e]=t,!t.snippets&&t.snippetText&&(t.snippets=o.parseSnippetFile(t.snippetText)),o.register(t.snippets||[],t.scope),t.includeScopes&&(o.snippetMap[t.scope].includeScopes=t.includeScopes,t.includeScopes.forEach(function(e){v("ace/mode/"+e)})))}))},x=function(e){var t=e.editor,i=t.completer&&t.completer.activated;if("backspace"===e.command.name)i&&!p.getCompletionPrefix(t)&&t.completer.detach();else if("insertstring"===e.command.name&&!i){n=e;var o=e.editor.$liveAutocompletionDelay;o?y.delay(o):w(e)}},y=a.delayedCall(function(){w(n)},0),w=function(e){var t=e.editor,i=p.getCompletionPrefix(t),n=e.args,o=p.triggerAutocomplete(t,n);if(i&&i.length>=t.$liveAutocompletionThreshold||o){var s=r.for(t);s.autoShown=!0,s.showPopup(t)}},T=e("../editor").Editor;e("../config").defineOptions(T.prototype,"editor",{enableBasicAutocompletion:{set:function(e){e?(r.for(this),this.completers||(this.completers=Array.isArray(e)?e:f),this.commands.addCommand(r.startCommand)):this.commands.removeCommand(r.startCommand)},value:!1},enableLiveAutocompletion:{set:function(e){e?(this.completers||(this.completers=Array.isArray(e)?e:f),this.commands.on("afterExec",x)):this.commands.off("afterExec",x)},value:!1},liveAutocompletionDelay:{initialValue:0},liveAutocompletionThreshold:{initialValue:0},enableSnippets:{set:function(e){e?(this.commands.addCommand(g),this.on("changeMode",m),m(null,this)):(this.commands.removeCommand(g),this.off("changeMode",m))},value:!1}}),t.MarkerGroup=c}),ace.require(["ace/ext/language_tools"],function(t){e&&(e.exports=t)})}}]);