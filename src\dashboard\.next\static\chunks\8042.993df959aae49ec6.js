(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8042],{15646:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var s=n(36663),a=(0,s.getAceInstance)(),h=n(97357),l=n(23195),c=n(77117),f=n(94285),u=n(21531),p=n(23594);e.default=function(t){function e(e){var n=t.call(this,e)||this;return s.editorEvents.forEach(function(t){n[t]=n[t].bind(n)}),n.debounce=s.debounce,n}return i(e,t),e.prototype.isInShadow=function(t){for(var e=t&&t.parentNode;e;){if("[object ShadowRoot]"===e.toString())return!0;e=e.parentNode}return!1},e.prototype.componentDidMount=function(){var t=this,e=this.props,n=e.className,r=e.onBeforeLoad,i=e.mode,o=e.focus,h=e.theme,c=e.fontSize,f=e.value,u=e.defaultValue,d=e.cursorStart,g=e.showGutter,b=e.wrapEnabled,v=e.showPrintMargin,y=e.scrollMargin,_=void 0===y?[0,0,0,0]:y,m=e.keyboardHandler,w=e.onLoad,S=e.commands,O=e.annotations,M=e.markers,E=e.splits;this.editor=a.edit(this.refEditor),this.isInShadow(this.refEditor)&&this.editor.renderer.attachToShadowRoot(),this.editor.setTheme("ace/theme/".concat(h)),r&&r(a);var x=Object.keys(this.props.editorProps),C=new l.Split(this.editor.container,"ace/theme/".concat(h),E);this.editor.env.split=C,this.splitEditor=C.getEditor(0),this.split=C,this.editor.setShowPrintMargin(!1),this.editor.renderer.setShowGutter(!1);var j=this.splitEditor.$options;this.props.debounceChangePeriod&&(this.onChange=this.debounce(this.onChange,this.props.debounceChangePeriod)),C.forEach(function(e,n){for(var r=0;r<x.length;r++)e[x[r]]=t.props.editorProps[x[r]];var o=p(u,n),l=p(f,n,"");e.session.setUndoManager(new a.UndoManager),e.setTheme("ace/theme/".concat(h)),e.renderer.setScrollMargin(_[0],_[1],_[2],_[3]),e.getSession().setMode("ace/mode/".concat(i)),e.setFontSize(c),e.renderer.setShowGutter(g),e.getSession().setUseWrapMode(b),e.setShowPrintMargin(v),e.on("focus",t.onFocus),e.on("blur",t.onBlur),e.on("input",t.onInput),e.on("copy",t.onCopy),e.on("paste",t.onPaste),e.on("change",t.onChange),e.getSession().selection.on("changeSelection",t.onSelectionChange),e.getSession().selection.on("changeCursor",t.onCursorChange),e.session.on("changeScrollTop",t.onScroll),e.setValue(void 0===o?l:o,d);var y=p(O,n,[]),w=p(M,n,[]);e.getSession().setAnnotations(y),w&&w.length>0&&t.handleMarkers(w,e);for(var r=0;r<s.editorOptions.length;r++){var E=s.editorOptions[r];j.hasOwnProperty(E)?e.setOption(E,t.props[E]):t.props[E]&&console.warn("ReaceAce: editor option ".concat(E," was activated but not found. Did you need to import a related tool or did you possibly mispell the option?"))}t.handleOptions(t.props,e),Array.isArray(S)&&S.forEach(function(t){"string"==typeof t.exec?e.commands.bindKey(t.bindKey,t.exec):e.commands.addCommand(t)}),m&&e.setKeyboardHandler("ace/keyboard/"+m)}),n&&(this.refEditor.className+=" "+n),o&&this.splitEditor.focus();var P=this.editor.env.split;P.setOrientation("below"===this.props.orientation?P.BELOW:P.BESIDE),P.resize(!0),w&&w(P)},e.prototype.componentDidUpdate=function(t){var e=this,n=this.props,r=this.editor.env.split;if(n.splits!==t.splits&&r.setSplits(n.splits),n.orientation!==t.orientation&&r.setOrientation("below"===n.orientation?r.BELOW:r.BESIDE),r.forEach(function(r,i){n.mode!==t.mode&&r.getSession().setMode("ace/mode/"+n.mode),n.keyboardHandler!==t.keyboardHandler&&(n.keyboardHandler?r.setKeyboardHandler("ace/keyboard/"+n.keyboardHandler):r.setKeyboardHandler(null)),n.fontSize!==t.fontSize&&r.setFontSize(n.fontSize),n.wrapEnabled!==t.wrapEnabled&&r.getSession().setUseWrapMode(n.wrapEnabled),n.showPrintMargin!==t.showPrintMargin&&r.setShowPrintMargin(n.showPrintMargin),n.showGutter!==t.showGutter&&r.renderer.setShowGutter(n.showGutter);for(var o=0;o<s.editorOptions.length;o++){var a=s.editorOptions[o];n[a]!==t[a]&&r.setOption(a,n[a])}u(n.setOptions,t.setOptions)||e.handleOptions(n,r);var h=p(n.value,i,"");if(r.getValue()!==h){e.silent=!0;var l=r.session.selection.toJSON();r.setValue(h,n.cursorStart),r.session.selection.fromJSON(l),e.silent=!1}var c=p(n.annotations,i,[]);u(c,p(t.annotations,i,[]))||r.getSession().setAnnotations(c);var f=p(n.markers,i,[]);!u(f,p(t.markers,i,[]))&&Array.isArray(f)&&e.handleMarkers(f,r)}),n.className!==t.className){var i=this.refEditor.className.trim().split(" ");t.className.trim().split(" ").forEach(function(t){var e=i.indexOf(t);i.splice(e,1)}),this.refEditor.className=" "+n.className+" "+i.join(" ")}n.theme!==t.theme&&r.setTheme("ace/theme/"+n.theme),n.focus&&!t.focus&&this.splitEditor.focus(),(n.height!==this.props.height||n.width!==this.props.width)&&this.editor.resize()},e.prototype.componentWillUnmount=function(){this.editor.destroy(),this.editor=null},e.prototype.onChange=function(t){if(this.props.onChange&&!this.silent){var e=[];this.editor.env.split.forEach(function(t){e.push(t.getValue())}),this.props.onChange(e,t)}},e.prototype.onSelectionChange=function(t){if(this.props.onSelectionChange){var e=[];this.editor.env.split.forEach(function(t){e.push(t.getSelection())}),this.props.onSelectionChange(e,t)}},e.prototype.onCursorChange=function(t){if(this.props.onCursorChange){var e=[];this.editor.env.split.forEach(function(t){e.push(t.getSelection())}),this.props.onCursorChange(e,t)}},e.prototype.onFocus=function(t){this.props.onFocus&&this.props.onFocus(t)},e.prototype.onInput=function(t){this.props.onInput&&this.props.onInput(t)},e.prototype.onBlur=function(t){this.props.onBlur&&this.props.onBlur(t)},e.prototype.onCopy=function(t){this.props.onCopy&&this.props.onCopy(t)},e.prototype.onPaste=function(t){this.props.onPaste&&this.props.onPaste(t)},e.prototype.onScroll=function(){this.props.onScroll&&this.props.onScroll(this.editor)},e.prototype.handleOptions=function(t,e){for(var n=Object.keys(t.setOptions),r=0;r<n.length;r++)e.setOption(n[r],t.setOptions[n[r]])},e.prototype.handleMarkers=function(t,e){var n=e.getSession().getMarkers(!0);for(var r in n)n.hasOwnProperty(r)&&e.getSession().removeMarker(n[r].id);for(var r in n=e.getSession().getMarkers(!1))n.hasOwnProperty(r)&&e.getSession().removeMarker(n[r].id);t.forEach(function(t){var n=t.startRow,r=t.startCol,i=t.endRow,o=t.endCol,s=t.className,a=t.type,l=t.inFront,c=new h.Range(n,r,i,o);e.getSession().addMarker(c,s,a,void 0!==l&&l)})},e.prototype.updateRef=function(t){this.refEditor=t},e.prototype.render=function(){var t=this.props,e=t.name,n=t.width,r=t.height,i=t.style,s=o({width:n,height:r},i);return f.createElement("div",{ref:this.updateRef,id:e,style:s})},e.propTypes={className:c.string,debounceChangePeriod:c.number,defaultValue:c.arrayOf(c.string),focus:c.bool,fontSize:c.oneOfType([c.number,c.string]),height:c.string,mode:c.string,name:c.string,onBlur:c.func,onChange:c.func,onCopy:c.func,onFocus:c.func,onInput:c.func,onLoad:c.func,onPaste:c.func,onScroll:c.func,orientation:c.string,showGutter:c.bool,splits:c.number,theme:c.string,value:c.arrayOf(c.string),width:c.string,onSelectionChange:c.func,onCursorChange:c.func,onBeforeLoad:c.func,minLines:c.number,maxLines:c.number,readOnly:c.bool,highlightActiveLine:c.bool,tabSize:c.number,showPrintMargin:c.bool,cursorStart:c.number,editorProps:c.object,setOptions:c.object,style:c.object,scrollMargin:c.array,annotations:c.array,markers:c.array,keyboardHandler:c.string,wrapEnabled:c.bool,enableBasicAutocompletion:c.oneOfType([c.bool,c.array]),enableLiveAutocompletion:c.oneOfType([c.bool,c.array]),commands:c.array},e.defaultProps={name:"ace-editor",focus:!1,orientation:"beside",splits:2,mode:"",theme:"",height:"500px",width:"500px",value:[],fontSize:12,showGutter:!0,onChange:null,onPaste:null,onLoad:null,onScroll:null,minLines:null,maxLines:null,readOnly:!1,highlightActiveLine:!0,showPrintMargin:!0,tabSize:4,cursorStart:1,editorProps:{},style:{},scrollMargin:[0,0,0,0],setOptions:{},wrapEnabled:!1,enableBasicAutocompletion:!1,enableLiveAutocompletion:!1},e}(f.Component)},21531:(t,e,n)=>{t=n.nmd(t);var r,i,o="__lodash_hash_undefined__",s="[object Arguments]",a="[object Array]",h="[object Boolean]",l="[object Date]",c="[object Error]",f="[object Function]",u="[object Map]",p="[object Number]",d="[object Object]",g="[object Promise]",b="[object RegExp]",v="[object Set]",y="[object String]",_="[object WeakMap]",m="[object ArrayBuffer]",w="[object DataView]",S=/^\[object .+?Constructor\]$/,O=/^(?:0|[1-9]\d*)$/,M={};M["[object Float32Array]"]=M["[object Float64Array]"]=M["[object Int8Array]"]=M["[object Int16Array]"]=M["[object Int32Array]"]=M["[object Uint8Array]"]=M["[object Uint8ClampedArray]"]=M["[object Uint16Array]"]=M["[object Uint32Array]"]=!0,M[s]=M[a]=M[m]=M[h]=M[w]=M[l]=M[c]=M[f]=M[u]=M[p]=M[d]=M[b]=M[v]=M[y]=M[_]=!1;var E="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,x="object"==typeof self&&self&&self.Object===Object&&self,C=E||x||Function("return this")(),j=e&&!e.nodeType&&e,P=j&&t&&!t.nodeType&&t,k=P&&P.exports===j,A=k&&E.process,D=function(){try{return A&&A.binding&&A.binding("util")}catch(t){}}(),L=D&&D.isTypedArray;function $(t){var e=-1,n=Array(t.size);return t.forEach(function(t,r){n[++e]=[r,t]}),n}function z(t){var e=-1,n=Array(t.size);return t.forEach(function(t){n[++e]=t}),n}var T=Array.prototype,I=Function.prototype,F=Object.prototype,N=C["__core-js_shared__"],R=I.toString,B=F.hasOwnProperty,U=function(){var t=/[^.]+$/.exec(N&&N.keys&&N.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),H=F.toString,G=RegExp("^"+R.call(B).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),V=k?C.Buffer:void 0,W=C.Symbol,K=C.Uint8Array,q=F.propertyIsEnumerable,J=T.splice,Q=W?W.toStringTag:void 0,X=Object.getOwnPropertySymbols,Y=V?V.isBuffer:void 0,Z=(r=Object.keys,i=Object,function(t){return r(i(t))}),tt=tM(C,"DataView"),te=tM(C,"Map"),tn=tM(C,"Promise"),tr=tM(C,"Set"),ti=tM(C,"WeakMap"),to=tM(Object,"create"),ts=tC(tt),ta=tC(te),th=tC(tn),tl=tC(tr),tc=tC(ti),tf=W?W.prototype:void 0,tu=tf?tf.valueOf:void 0;function tp(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function td(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function tg(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function tb(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new tg;++e<n;)this.add(t[e])}function tv(t){var e=this.__data__=new td(t);this.size=e.size}function ty(t,e){for(var n=t.length;n--;)if(tj(t[n][0],e))return n;return -1}function t_(t){var e;return null==t?void 0===t?"[object Undefined]":"[object Null]":Q&&Q in Object(t)?function(t){var e=B.call(t,Q),n=t[Q];try{t[Q]=void 0;var r=!0}catch(t){}var i=H.call(t);return r&&(e?t[Q]=n:delete t[Q]),i}(t):(e=t,H.call(e))}function tm(t){return tz(t)&&t_(t)==s}function tw(t,e,n,r,i,o){var s=1&n,a=t.length,h=e.length;if(a!=h&&!(s&&h>a))return!1;var l=o.get(t);if(l&&o.get(e))return l==e;var c=-1,f=!0,u=2&n?new tb:void 0;for(o.set(t,e),o.set(e,t);++c<a;){var p=t[c],d=e[c];if(r)var g=s?r(d,p,c,e,t,o):r(p,d,c,t,e,o);if(void 0!==g){if(g)continue;f=!1;break}if(u){if(!function(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}(e,function(t,e){if(!u.has(e)&&(p===t||i(p,t,n,r,o)))return u.push(e)})){f=!1;break}}else if(!(p===d||i(p,d,n,r,o))){f=!1;break}}return o.delete(t),o.delete(e),f}function tS(t){var e;return e=tI(t),tk(t)?e:function(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}(e,tE(t))}function tO(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function tM(t,e){var n,r=null==t?void 0:t[e];return!(!t$(r)||(n=r,U&&U in n))&&(tD(r)?G:S).test(tC(r))?r:void 0}tp.prototype.clear=function(){this.__data__=to?to(null):{},this.size=0},tp.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e},tp.prototype.get=function(t){var e=this.__data__;if(to){var n=e[t];return n===o?void 0:n}return B.call(e,t)?e[t]:void 0},tp.prototype.has=function(t){var e=this.__data__;return to?void 0!==e[t]:B.call(e,t)},tp.prototype.set=function(t,e){var n=this.__data__;return this.size+=+!this.has(t),n[t]=to&&void 0===e?o:e,this},td.prototype.clear=function(){this.__data__=[],this.size=0},td.prototype.delete=function(t){var e=this.__data__,n=ty(e,t);return!(n<0)&&(n==e.length-1?e.pop():J.call(e,n,1),--this.size,!0)},td.prototype.get=function(t){var e=this.__data__,n=ty(e,t);return n<0?void 0:e[n][1]},td.prototype.has=function(t){return ty(this.__data__,t)>-1},td.prototype.set=function(t,e){var n=this.__data__,r=ty(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},tg.prototype.clear=function(){this.size=0,this.__data__={hash:new tp,map:new(te||td),string:new tp}},tg.prototype.delete=function(t){var e=tO(this,t).delete(t);return this.size-=!!e,e},tg.prototype.get=function(t){return tO(this,t).get(t)},tg.prototype.has=function(t){return tO(this,t).has(t)},tg.prototype.set=function(t,e){var n=tO(this,t),r=n.size;return n.set(t,e),this.size+=+(n.size!=r),this},tb.prototype.add=tb.prototype.push=function(t){return this.__data__.set(t,o),this},tb.prototype.has=function(t){return this.__data__.has(t)},tv.prototype.clear=function(){this.__data__=new td,this.size=0},tv.prototype.delete=function(t){var e=this.__data__,n=e.delete(t);return this.size=e.size,n},tv.prototype.get=function(t){return this.__data__.get(t)},tv.prototype.has=function(t){return this.__data__.has(t)},tv.prototype.set=function(t,e){var n=this.__data__;if(n instanceof td){var r=n.__data__;if(!te||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new tg(r)}return n.set(t,e),this.size=n.size,this};var tE=X?function(t){return null==t?[]:function(t,e){for(var n=-1,r=null==t?0:t.length,i=0,o=[];++n<r;){var s=t[n];e(s,n,t)&&(o[i++]=s)}return o}(X(t=Object(t)),function(e){return q.call(t,e)})}:function(){return[]},tx=t_;function tC(t){if(null!=t){try{return R.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function tj(t,e){return t===e||t!=t&&e!=e}(tt&&tx(new tt(new ArrayBuffer(1)))!=w||te&&tx(new te)!=u||tn&&tx(tn.resolve())!=g||tr&&tx(new tr)!=v||ti&&tx(new ti)!=_)&&(tx=function(t){var e=t_(t),n=e==d?t.constructor:void 0,r=n?tC(n):"";if(r)switch(r){case ts:return w;case ta:return u;case th:return g;case tl:return v;case tc:return _}return e});var tP=tm(function(){return arguments}())?tm:function(t){return tz(t)&&B.call(t,"callee")&&!q.call(t,"callee")},tk=Array.isArray,tA=Y||function(){return!1};function tD(t){if(!t$(t))return!1;var e=t_(t);return e==f||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}function tL(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}function t$(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function tz(t){return null!=t&&"object"==typeof t}var tT=L?function(t){return L(t)}:function(t){return tz(t)&&tL(t.length)&&!!M[t_(t)]};function tI(t){return null!=t&&tL(t.length)&&!tD(t)?function(t,e){var n,r,i=tk(t),o=!i&&tP(t),s=!i&&!o&&tA(t),a=!i&&!o&&!s&&tT(t),h=i||o||s||a,l=h?function(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}(t.length,String):[],c=l.length;for(var f in t){B.call(t,f)&&!(h&&("length"==f||s&&("offset"==f||"parent"==f)||a&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||(n=f,(r=null==(r=c)?0x1fffffffffffff:r)&&("number"==typeof n||O.test(n))&&n>-1&&n%1==0&&n<r)))&&l.push(f)}return l}(t):function(t){if(n=(e=t)&&e.constructor,e!==("function"==typeof n&&n.prototype||F))return Z(t);var e,n,r=[];for(var i in Object(t))B.call(t,i)&&"constructor"!=i&&r.push(i);return r}(t)}t.exports=function(t,e){return function t(e,n,r,i,o){return e===n||(null!=e&&null!=n&&(tz(e)||tz(n))?function(t,e,n,r,i,o){var f=tk(t),g=tk(e),_=f?a:tx(t),S=g?a:tx(e);_=_==s?d:_,S=S==s?d:S;var O=_==d,M=S==d,E=_==S;if(E&&tA(t)){if(!tA(e))return!1;f=!0,O=!1}if(E&&!O)return o||(o=new tv),f||tT(t)?tw(t,e,n,r,i,o):function(t,e,n,r,i,o,s){switch(n){case w:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case m:if(t.byteLength!=e.byteLength||!o(new K(t),new K(e)))break;return!0;case h:case l:case p:return tj(+t,+e);case c:return t.name==e.name&&t.message==e.message;case b:case y:return t==e+"";case u:var a=$;case v:var f=1&r;if(a||(a=z),t.size!=e.size&&!f)break;var d=s.get(t);if(d)return d==e;r|=2,s.set(t,e);var g=tw(a(t),a(e),r,i,o,s);return s.delete(t),g;case"[object Symbol]":if(tu)return tu.call(t)==tu.call(e)}return!1}(t,e,_,n,r,i,o);if(!(1&n)){var x=O&&B.call(t,"__wrapped__"),C=M&&B.call(e,"__wrapped__");if(x||C){var j=x?t.value():t,P=C?e.value():e;return o||(o=new tv),i(j,P,n,r,o)}}return!!E&&(o||(o=new tv),function(t,e,n,r,i,o){var s=1&n,a=tS(t),h=a.length;if(h!=tS(e).length&&!s)return!1;for(var l=h;l--;){var c=a[l];if(!(s?c in e:B.call(e,c)))return!1}var f=o.get(t);if(f&&o.get(e))return f==e;var u=!0;o.set(t,e),o.set(e,t);for(var p=s;++l<h;){var d=t[c=a[l]],g=e[c];if(r)var b=s?r(g,d,c,e,t,o):r(d,g,c,t,e,o);if(!(void 0===b?d===g||i(d,g,n,r,o):b)){u=!1;break}p||(p="constructor"==c)}if(u&&!p){var v=t.constructor,y=e.constructor;v!=y&&"constructor"in t&&"constructor"in e&&!("function"==typeof v&&v instanceof v&&"function"==typeof y&&y instanceof y)&&(u=!1)}return o.delete(t),o.delete(e),u}(t,e,n,r,i,o))}(e,n,r,i,t,o):e!=e&&n!=n)}(t,e)}},23195:(t,e,n)=>{t=n.nmd(t),ace.define("ace/split",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/lib/event_emitter","ace/editor","ace/virtual_renderer","ace/edit_session"],function(t,e,n){"use strict";var r,i=t("./lib/oop");t("./lib/lang");var o=t("./lib/event_emitter").EventEmitter,s=t("./editor").Editor,a=t("./virtual_renderer").VirtualRenderer,h=t("./edit_session").EditSession;(function(){i.implement(this,o),this.$createEditor=function(){var t=document.createElement("div");t.className=this.$editorCSS,t.style.cssText="position: absolute; top:0px; bottom:0px",this.$container.appendChild(t);var e=new s(new a(t,this.$theme));return e.on("focus",(function(){this._emit("focus",e)}).bind(this)),this.$editors.push(e),e.setFontSize(this.$fontSize),e},this.setSplits=function(t){var e;if(t<1)throw"The number of splits have to be > 0!";if(t!=this.$splits){if(t>this.$splits){for(;this.$splits<this.$editors.length&&this.$splits<t;)e=this.$editors[this.$splits],this.$container.appendChild(e.container),e.setFontSize(this.$fontSize),this.$splits++;for(;this.$splits<t;)this.$createEditor(),this.$splits++}else for(;this.$splits>t;)e=this.$editors[this.$splits-1],this.$container.removeChild(e.container),this.$splits--;this.resize()}},this.getSplits=function(){return this.$splits},this.getEditor=function(t){return this.$editors[t]},this.getCurrentEditor=function(){return this.$cEditor},this.focus=function(){this.$cEditor.focus()},this.blur=function(){this.$cEditor.blur()},this.setTheme=function(t){this.$editors.forEach(function(e){e.setTheme(t)})},this.setKeyboardHandler=function(t){this.$editors.forEach(function(e){e.setKeyboardHandler(t)})},this.forEach=function(t,e){this.$editors.forEach(t,e)},this.$fontSize="",this.setFontSize=function(t){this.$fontSize=t,this.forEach(function(e){e.setFontSize(t)})},this.$cloneSession=function(t){var e=new h(t.getDocument(),t.getMode()),n=t.getUndoManager();return e.setUndoManager(n),e.setTabSize(t.getTabSize()),e.setUseSoftTabs(t.getUseSoftTabs()),e.setOverwrite(t.getOverwrite()),e.setBreakpoints(t.getBreakpoints()),e.setUseWrapMode(t.getUseWrapMode()),e.setUseWorker(t.getUseWorker()),e.setWrapLimitRange(t.$wrapLimitRange.min,t.$wrapLimitRange.max),e.$foldData=t.$cloneFoldData(),e},this.setSession=function(t,e){var n=null==e?this.$cEditor:this.$editors[e];return this.$editors.some(function(e){return e.session===t})&&(t=this.$cloneSession(t)),n.setSession(t),t},this.getOrientation=function(){return this.$orientation},this.setOrientation=function(t){this.$orientation!=t&&(this.$orientation=t,this.resize())},this.resize=function(){var t,e=this.$container.clientWidth,n=this.$container.clientHeight;if(this.$orientation==this.BESIDE)for(var r=e/this.$splits,i=0;i<this.$splits;i++)(t=this.$editors[i]).container.style.width=r+"px",t.container.style.top="0px",t.container.style.left=i*r+"px",t.container.style.height=n+"px",t.resize();else for(var o=n/this.$splits,i=0;i<this.$splits;i++)(t=this.$editors[i]).container.style.width=e+"px",t.container.style.top=i*o+"px",t.container.style.left="0px",t.container.style.height=o+"px",t.resize()}}).call((r=function(t,e,n){this.BELOW=1,this.BESIDE=0,this.$container=t,this.$theme=e,this.$splits=0,this.$editorCSS="",this.$editors=[],this.$orientation=this.BESIDE,this.setSplits(n||1),this.$cEditor=this.$editors[0],this.on("focus",(function(t){this.$cEditor=t}).bind(this))}).prototype),e.Split=r}),ace.define("ace/ext/split",["require","exports","module","ace/ext/split","ace/split"],function(t,e,n){"use strict";n.exports=t("../split")}),ace.require(["ace/ext/split"],function(e){t&&(t.exports=e)})},23594:(t,e,n)=>{var r="__lodash_hash_undefined__",i=1/0,o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,s=/^\w*$/,a=/^\./,h=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,l=/\\(\\)?/g,c=/^\[object .+?Constructor\]$/,f="object"==typeof n.g&&n.g&&n.g.Object===Object&&n.g,u="object"==typeof self&&self&&self.Object===Object&&self,p=f||u||Function("return this")(),d=Array.prototype,g=Function.prototype,b=Object.prototype,v=p["__core-js_shared__"],y=function(){var t=/[^.]+$/.exec(v&&v.keys&&v.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}(),_=g.toString,m=b.hasOwnProperty,w=b.toString,S=RegExp("^"+_.call(m).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),O=p.Symbol,M=d.splice,E=$(p,"Map"),x=$(Object,"create"),C=O?O.prototype:void 0,j=C?C.toString:void 0;function P(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function k(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function A(t){var e=-1,n=t?t.length:0;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function D(t,e){for(var n,r,i=t.length;i--;){if(n=t[i][0],n===(r=e)||n!=n&&r!=r)return i}return -1}function L(t,e){var n,r,i=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?i["string"==typeof e?"string":"hash"]:i.map}function $(t,e){var n,r,i,o=null==t?void 0:t[e];return!(!F(o)||(n=o,y&&y in n))&&("[object Function]"==(i=F(r=o)?w.call(r):"")||"[object GeneratorFunction]"==i||function(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}(o)?S:c).test(function(t){if(null!=t){try{return _.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(o))?o:void 0}P.prototype.clear=function(){this.__data__=x?x(null):{}},P.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},P.prototype.get=function(t){var e=this.__data__;if(x){var n=e[t];return n===r?void 0:n}return m.call(e,t)?e[t]:void 0},P.prototype.has=function(t){var e=this.__data__;return x?void 0!==e[t]:m.call(e,t)},P.prototype.set=function(t,e){return this.__data__[t]=x&&void 0===e?r:e,this},k.prototype.clear=function(){this.__data__=[]},k.prototype.delete=function(t){var e=this.__data__,n=D(e,t);return!(n<0)&&(n==e.length-1?e.pop():M.call(e,n,1),!0)},k.prototype.get=function(t){var e=this.__data__,n=D(e,t);return n<0?void 0:e[n][1]},k.prototype.has=function(t){return D(this.__data__,t)>-1},k.prototype.set=function(t,e){var n=this.__data__,r=D(n,t);return r<0?n.push([t,e]):n[r][1]=e,this},A.prototype.clear=function(){this.__data__={hash:new P,map:new(E||k),string:new P}},A.prototype.delete=function(t){return L(this,t).delete(t)},A.prototype.get=function(t){return L(this,t).get(t)},A.prototype.has=function(t){return L(this,t).has(t)},A.prototype.set=function(t,e){return L(this,t).set(t,e),this};var z=T(function(t){t=null==(e=t)?"":function(t){if("string"==typeof t)return t;if(N(t))return j?j.call(t):"";var e=t+"";return"0"==e&&1/t==-i?"-0":e}(e);var e,n=[];return a.test(t)&&n.push(""),t.replace(h,function(t,e,r,i){n.push(r?i.replace(l,"$1"):e||t)}),n});function T(t,e){if("function"!=typeof t||e&&"function"!=typeof e)throw TypeError("Expected a function");var n=function(){var r=arguments,i=e?e.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var s=t.apply(this,r);return n.cache=o.set(i,s),s};return n.cache=new(T.Cache||A),n}T.Cache=A;var I=Array.isArray;function F(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function N(t){return"symbol"==typeof t||!!t&&"object"==typeof t&&"[object Symbol]"==w.call(t)}t.exports=function(t,e,n){var r=null==t?void 0:function(t,e){var n;e=!function(t,e){if(I(t))return!1;var n=typeof t;return!!("number"==n||"symbol"==n||"boolean"==n||null==t||N(t))||s.test(t)||!o.test(t)||null!=e&&t in Object(e)}(e,t)?I(n=e)?n:z(n):[e];for(var r=0,a=e.length;null!=t&&r<a;)t=t[function(t){if("string"==typeof t||N(t))return t;var e=t+"";return"0"==e&&1/t==-i?"-0":e}(e[r++])];return r&&r==a?t:void 0}(t,e);return void 0===r?n:r}},29027:(t,e,n)=>{"use strict";var r=n(34544);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,n,i,o,s){if(s!==r){var a=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function e(){return t}t.isRequired=t;var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return n.PropTypes=n,n}},34544:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},36663:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getAceInstance=e.debounce=e.editorEvents=e.editorOptions=void 0,e.editorOptions=["minLines","maxLines","readOnly","highlightActiveLine","tabSize","enableBasicAutocompletion","enableLiveAutocompletion","enableSnippets"],e.editorEvents=["onChange","onFocus","onInput","onBlur","onCopy","onPaste","onSelectionChange","onCursorChange","onScroll","handleOptions","updateRef"],e.getAceInstance=function(){var t;return"undefined"==typeof window?(n.g.window={},t=n(97357),delete n.g.window):window.ace?(t=window.ace).acequire=window.ace.require||window.ace.acequire:t=n(97357),t},e.debounce=function(t,e){var n=null;return function(){var r=this,i=arguments;clearTimeout(n),n=setTimeout(function(){t.apply(r,i)},e)}}},42400:t=>{var e=function(){this.Diff_Timeout=1,this.Diff_EditCost=4,this.Match_Threshold=.5,this.Match_Distance=1e3,this.Patch_DeleteThreshold=.5,this.Patch_Margin=4,this.Match_MaxBits=32};e.Diff=function(t,e){return[t,e]},e.prototype.diff_main=function(t,n,r,i){void 0===i&&(i=this.Diff_Timeout<=0?Number.MAX_VALUE:(new Date).getTime()+1e3*this.Diff_Timeout);var o=i;if(null==t||null==n)throw Error("Null input. (diff_main)");if(t==n)return t?[new e.Diff(0,t)]:[];void 0===r&&(r=!0);var s=r,a=this.diff_commonPrefix(t,n),h=t.substring(0,a);t=t.substring(a),n=n.substring(a),a=this.diff_commonSuffix(t,n);var l=t.substring(t.length-a);t=t.substring(0,t.length-a),n=n.substring(0,n.length-a);var c=this.diff_compute_(t,n,s,o);return h&&c.unshift(new e.Diff(0,h)),l&&c.push(new e.Diff(0,l)),this.diff_cleanupMerge(c),c},e.prototype.diff_compute_=function(t,n,r,i){if(!t)return[new e.Diff(1,n)];if(!n)return[new e.Diff(-1,t)];var o,s=t.length>n.length?t:n,a=t.length>n.length?n:t,h=s.indexOf(a);if(-1!=h)return o=[new e.Diff(1,s.substring(0,h)),new e.Diff(0,a),new e.Diff(1,s.substring(h+a.length))],t.length>n.length&&(o[0][0]=o[2][0]=-1),o;if(1==a.length)return[new e.Diff(-1,t),new e.Diff(1,n)];var l=this.diff_halfMatch_(t,n);if(l){var c=l[0],f=l[1],u=l[2],p=l[3],d=l[4],g=this.diff_main(c,u,r,i),b=this.diff_main(f,p,r,i);return g.concat([new e.Diff(0,d)],b)}return r&&t.length>100&&n.length>100?this.diff_lineMode_(t,n,i):this.diff_bisect_(t,n,i)},e.prototype.diff_lineMode_=function(t,n,r){var i=this.diff_linesToChars_(t,n);t=i.chars1,n=i.chars2;var o=i.lineArray,s=this.diff_main(t,n,!1,r);this.diff_charsToLines_(s,o),this.diff_cleanupSemantic(s),s.push(new e.Diff(0,""));for(var a=0,h=0,l=0,c="",f="";a<s.length;){switch(s[a][0]){case 1:l++,f+=s[a][1];break;case -1:h++,c+=s[a][1];break;case 0:if(h>=1&&l>=1){s.splice(a-h-l,h+l),a=a-h-l;for(var u=this.diff_main(c,f,!1,r),p=u.length-1;p>=0;p--)s.splice(a,0,u[p]);a+=u.length}l=0,h=0,c="",f=""}a++}return s.pop(),s},e.prototype.diff_bisect_=function(t,n,r){for(var i=t.length,o=n.length,s=Math.ceil((i+o)/2),a=2*s,h=Array(a),l=Array(a),c=0;c<a;c++)h[c]=-1,l[c]=-1;h[s+1]=0,l[s+1]=0;for(var f=i-o,u=f%2!=0,p=0,d=0,g=0,b=0,v=0;v<s&&!(new Date().getTime()>r);v++){for(var y=-v+p;y<=v-d;y+=2){for(var _,m=s+y,w=(_=y==-v||y!=v&&h[m-1]<h[m+1]?h[m+1]:h[m-1]+1)-y;_<i&&w<o&&t.charAt(_)==n.charAt(w);)_++,w++;if(h[m]=_,_>i)d+=2;else if(w>o)p+=2;else if(u){var S=s+f-y;if(S>=0&&S<a&&-1!=l[S]){var O=i-l[S];if(_>=O)return this.diff_bisectSplit_(t,n,_,w,r)}}}for(var M=-v+g;M<=v-b;M+=2){for(var O,S=s+M,E=(O=M==-v||M!=v&&l[S-1]<l[S+1]?l[S+1]:l[S-1]+1)-M;O<i&&E<o&&t.charAt(i-O-1)==n.charAt(o-E-1);)O++,E++;if(l[S]=O,O>i)b+=2;else if(E>o)g+=2;else if(!u){var m=s+f-M;if(m>=0&&m<a&&-1!=h[m]){var _=h[m],w=s+_-m;if(_>=(O=i-O))return this.diff_bisectSplit_(t,n,_,w,r)}}}}return[new e.Diff(-1,t),new e.Diff(1,n)]},e.prototype.diff_bisectSplit_=function(t,e,n,r,i){var o=t.substring(0,n),s=e.substring(0,r),a=t.substring(n),h=e.substring(r),l=this.diff_main(o,s,!1,i),c=this.diff_main(a,h,!1,i);return l.concat(c)},e.prototype.diff_linesToChars_=function(t,e){var n=[],r={};function i(t){for(var e="",i=0,s=-1,a=n.length;s<t.length-1;){-1==(s=t.indexOf("\n",i))&&(s=t.length-1);var h=t.substring(i,s+1);(r.hasOwnProperty?r.hasOwnProperty(h):void 0!==r[h])?e+=String.fromCharCode(r[h]):(a==o&&(h=t.substring(i),s=t.length),e+=String.fromCharCode(a),r[h]=a,n[a++]=h),i=s+1}return e}n[0]="";var o=4e4,s=i(t);return o=65535,{chars1:s,chars2:i(e),lineArray:n}},e.prototype.diff_charsToLines_=function(t,e){for(var n=0;n<t.length;n++){for(var r=t[n][1],i=[],o=0;o<r.length;o++)i[o]=e[r.charCodeAt(o)];t[n][1]=i.join("")}},e.prototype.diff_commonPrefix=function(t,e){if(!t||!e||t.charAt(0)!=e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,o=0;n<i;)t.substring(o,i)==e.substring(o,i)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i},e.prototype.diff_commonSuffix=function(t,e){if(!t||!e||t.charAt(t.length-1)!=e.charAt(e.length-1))return 0;for(var n=0,r=Math.min(t.length,e.length),i=r,o=0;n<i;)t.substring(t.length-i,t.length-o)==e.substring(e.length-i,e.length-o)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i},e.prototype.diff_commonOverlap_=function(t,e){var n=t.length,r=e.length;if(0==n||0==r)return 0;n>r?t=t.substring(n-r):n<r&&(e=e.substring(0,n));var i=Math.min(n,r);if(t==e)return i;for(var o=0,s=1;;){var a=t.substring(i-s),h=e.indexOf(a);if(-1==h)return o;s+=h,(0==h||t.substring(i-s)==e.substring(0,s))&&(o=s,s++)}},e.prototype.diff_halfMatch_=function(t,e){if(this.Diff_Timeout<=0)return null;var n,r,i,o,s,a=t.length>e.length?t:e,h=t.length>e.length?e:t;if(a.length<4||2*h.length<a.length)return null;var l=this;function c(t,e,n){for(var r,i,o,s,a=t.substring(n,n+Math.floor(t.length/4)),h=-1,c="";-1!=(h=e.indexOf(a,h+1));){var f=l.diff_commonPrefix(t.substring(n),e.substring(h)),u=l.diff_commonSuffix(t.substring(0,n),e.substring(0,h));c.length<u+f&&(c=e.substring(h-u,h)+e.substring(h,h+f),r=t.substring(0,n-u),i=t.substring(n+f),o=e.substring(0,h-u),s=e.substring(h+f))}return 2*c.length>=t.length?[r,i,o,s,c]:null}var f=c(a,h,Math.ceil(a.length/4)),u=c(a,h,Math.ceil(a.length/2));return f||u?(n=u?f&&f[4].length>u[4].length?f:u:f,t.length>e.length?(r=n[0],i=n[1],o=n[2],s=n[3]):(o=n[0],s=n[1],r=n[2],i=n[3]),[r,i,o,s,n[4]]):null},e.prototype.diff_cleanupSemantic=function(t){for(var n=!1,r=[],i=0,o=null,s=0,a=0,h=0,l=0,c=0;s<t.length;)0==t[s][0]?(r[i++]=s,a=l,h=c,l=0,c=0,o=t[s][1]):(1==t[s][0]?l+=t[s][1].length:c+=t[s][1].length,o&&o.length<=Math.max(a,h)&&o.length<=Math.max(l,c)&&(t.splice(r[i-1],0,new e.Diff(-1,o)),t[r[i-1]+1][0]=1,i--,s=--i>0?r[i-1]:-1,a=0,h=0,l=0,c=0,o=null,n=!0)),s++;for(n&&this.diff_cleanupMerge(t),this.diff_cleanupSemanticLossless(t),s=1;s<t.length;){if(-1==t[s-1][0]&&1==t[s][0]){var f=t[s-1][1],u=t[s][1],p=this.diff_commonOverlap_(f,u),d=this.diff_commonOverlap_(u,f);p>=d?(p>=f.length/2||p>=u.length/2)&&(t.splice(s,0,new e.Diff(0,u.substring(0,p))),t[s-1][1]=f.substring(0,f.length-p),t[s+1][1]=u.substring(p),s++):(d>=f.length/2||d>=u.length/2)&&(t.splice(s,0,new e.Diff(0,f.substring(0,d))),t[s-1][0]=1,t[s-1][1]=u.substring(0,u.length-d),t[s+1][0]=-1,t[s+1][1]=f.substring(d),s++),s++}s++}},e.prototype.diff_cleanupSemanticLossless=function(t){function n(t,n){if(!t||!n)return 6;var r=t.charAt(t.length-1),i=n.charAt(0),o=r.match(e.nonAlphaNumericRegex_),s=i.match(e.nonAlphaNumericRegex_),a=o&&r.match(e.whitespaceRegex_),h=s&&i.match(e.whitespaceRegex_),l=a&&r.match(e.linebreakRegex_),c=h&&i.match(e.linebreakRegex_),f=l&&t.match(e.blanklineEndRegex_),u=c&&n.match(e.blanklineStartRegex_);if(f||u)return 5;if(l||c)return 4;if(o&&!a&&h)return 3;if(a||h)return 2;if(o||s)return 1;return 0}for(var r=1;r<t.length-1;){if(0==t[r-1][0]&&0==t[r+1][0]){var i=t[r-1][1],o=t[r][1],s=t[r+1][1],a=this.diff_commonSuffix(i,o);if(a){var h=o.substring(o.length-a);i=i.substring(0,i.length-a),o=h+o.substring(0,o.length-a),s=h+s}for(var l=i,c=o,f=s,u=n(i,o)+n(o,s);o.charAt(0)===s.charAt(0);){i+=o.charAt(0),o=o.substring(1)+s.charAt(0),s=s.substring(1);var p=n(i,o)+n(o,s);p>=u&&(u=p,l=i,c=o,f=s)}t[r-1][1]!=l&&(l?t[r-1][1]=l:(t.splice(r-1,1),r--),t[r][1]=c,f?t[r+1][1]=f:(t.splice(r+1,1),r--))}r++}},e.nonAlphaNumericRegex_=/[^a-zA-Z0-9]/,e.whitespaceRegex_=/\s/,e.linebreakRegex_=/[\r\n]/,e.blanklineEndRegex_=/\n\r?\n$/,e.blanklineStartRegex_=/^\r?\n\r?\n/,e.prototype.diff_cleanupEfficiency=function(t){for(var n=!1,r=[],i=0,o=null,s=0,a=!1,h=!1,l=!1,c=!1;s<t.length;)0==t[s][0]?(t[s][1].length<this.Diff_EditCost&&(l||c)?(r[i++]=s,a=l,h=c,o=t[s][1]):(i=0,o=null),l=c=!1):(-1==t[s][0]?c=!0:l=!0,o&&(a&&h&&l&&c||o.length<this.Diff_EditCost/2&&a+h+l+c==3)&&(t.splice(r[i-1],0,new e.Diff(-1,o)),t[r[i-1]+1][0]=1,i--,o=null,a&&h?(l=c=!0,i=0):(s=--i>0?r[i-1]:-1,l=c=!1),n=!0)),s++;n&&this.diff_cleanupMerge(t)},e.prototype.diff_cleanupMerge=function(t){t.push(new e.Diff(0,""));for(var n,r=0,i=0,o=0,s="",a="";r<t.length;)switch(t[r][0]){case 1:o++,a+=t[r][1],r++;break;case -1:i++,s+=t[r][1],r++;break;case 0:i+o>1?(0!==i&&0!==o&&(0!==(n=this.diff_commonPrefix(a,s))&&(r-i-o>0&&0==t[r-i-o-1][0]?t[r-i-o-1][1]+=a.substring(0,n):(t.splice(0,0,new e.Diff(0,a.substring(0,n))),r++),a=a.substring(n),s=s.substring(n)),0!==(n=this.diff_commonSuffix(a,s))&&(t[r][1]=a.substring(a.length-n)+t[r][1],a=a.substring(0,a.length-n),s=s.substring(0,s.length-n))),r-=i+o,t.splice(r,i+o),s.length&&(t.splice(r,0,new e.Diff(-1,s)),r++),a.length&&(t.splice(r,0,new e.Diff(1,a)),r++),r++):0!==r&&0==t[r-1][0]?(t[r-1][1]+=t[r][1],t.splice(r,1)):r++,o=0,i=0,s="",a=""}""===t[t.length-1][1]&&t.pop();var h=!1;for(r=1;r<t.length-1;)0==t[r-1][0]&&0==t[r+1][0]&&(t[r][1].substring(t[r][1].length-t[r-1][1].length)==t[r-1][1]?(t[r][1]=t[r-1][1]+t[r][1].substring(0,t[r][1].length-t[r-1][1].length),t[r+1][1]=t[r-1][1]+t[r+1][1],t.splice(r-1,1),h=!0):t[r][1].substring(0,t[r+1][1].length)==t[r+1][1]&&(t[r-1][1]+=t[r+1][1],t[r][1]=t[r][1].substring(t[r+1][1].length)+t[r+1][1],t.splice(r+1,1),h=!0)),r++;h&&this.diff_cleanupMerge(t)},e.prototype.diff_xIndex=function(t,e){var n,r=0,i=0,o=0,s=0;for(n=0;n<t.length&&(1!==t[n][0]&&(r+=t[n][1].length),-1!==t[n][0]&&(i+=t[n][1].length),!(r>e));n++)o=r,s=i;return t.length!=n&&-1===t[n][0]?s:s+(e-o)},e.prototype.diff_prettyHtml=function(t){for(var e=[],n=/&/g,r=/</g,i=/>/g,o=/\n/g,s=0;s<t.length;s++){var a=t[s][0],h=t[s][1].replace(n,"&amp;").replace(r,"&lt;").replace(i,"&gt;").replace(o,"&para;<br>");switch(a){case 1:e[s]='<ins style="background:#e6ffe6;">'+h+"</ins>";break;case -1:e[s]='<del style="background:#ffe6e6;">'+h+"</del>";break;case 0:e[s]="<span>"+h+"</span>"}}return e.join("")},e.prototype.diff_text1=function(t){for(var e=[],n=0;n<t.length;n++)1!==t[n][0]&&(e[n]=t[n][1]);return e.join("")},e.prototype.diff_text2=function(t){for(var e=[],n=0;n<t.length;n++)-1!==t[n][0]&&(e[n]=t[n][1]);return e.join("")},e.prototype.diff_levenshtein=function(t){for(var e=0,n=0,r=0,i=0;i<t.length;i++){var o=t[i][0],s=t[i][1];switch(o){case 1:n+=s.length;break;case -1:r+=s.length;break;case 0:e+=Math.max(n,r),n=0,r=0}}return e+Math.max(n,r)},e.prototype.diff_toDelta=function(t){for(var e=[],n=0;n<t.length;n++)switch(t[n][0]){case 1:e[n]="+"+encodeURI(t[n][1]);break;case -1:e[n]="-"+t[n][1].length;break;case 0:e[n]="="+t[n][1].length}return e.join("	").replace(/%20/g," ")},e.prototype.diff_fromDelta=function(t,n){for(var r=[],i=0,o=0,s=n.split(/\t/g),a=0;a<s.length;a++){var h=s[a].substring(1);switch(s[a].charAt(0)){case"+":try{r[i++]=new e.Diff(1,decodeURI(h))}catch(t){throw Error("Illegal escape in diff_fromDelta: "+h)}break;case"-":case"=":var l=parseInt(h,10);if(isNaN(l)||l<0)throw Error("Invalid number in diff_fromDelta: "+h);var c=t.substring(o,o+=l);"="==s[a].charAt(0)?r[i++]=new e.Diff(0,c):r[i++]=new e.Diff(-1,c);break;default:if(s[a])throw Error("Invalid diff operation in diff_fromDelta: "+s[a])}}if(o!=t.length)throw Error("Delta length ("+o+") does not equal source text length ("+t.length+").");return r},e.prototype.match_main=function(t,e,n){if(null==t||null==e||null==n)throw Error("Null input. (match_main)");return(n=Math.max(0,Math.min(n,t.length)),t==e)?0:t.length?t.substring(n,n+e.length)==e?n:this.match_bitap_(t,e,n):-1},e.prototype.match_bitap_=function(t,e,n){if(e.length>this.Match_MaxBits)throw Error("Pattern too long for this browser.");var r,i,o,s=this.match_alphabet_(e),a=this;function h(t,r){var i=t/e.length,o=Math.abs(n-r);return a.Match_Distance?i+o/a.Match_Distance:o?1:i}var l=this.Match_Threshold,c=t.indexOf(e,n);-1!=c&&(l=Math.min(h(0,c),l),-1!=(c=t.lastIndexOf(e,n+e.length))&&(l=Math.min(h(0,c),l)));var f=1<<e.length-1;c=-1;for(var u=e.length+t.length,p=0;p<e.length;p++){for(r=0,i=u;r<i;)h(p,n+i)<=l?r=i:u=i,i=Math.floor((u-r)/2+r);u=i;var d=Math.max(1,n-i+1),g=Math.min(n+i,t.length)+e.length,b=Array(g+2);b[g+1]=(1<<p)-1;for(var v=g;v>=d;v--){var y=s[t.charAt(v-1)];if(0===p?b[v]=(b[v+1]<<1|1)&y:b[v]=(b[v+1]<<1|1)&y|((o[v+1]|o[v])<<1|1)|o[v+1],b[v]&f){var _=h(p,v-1);if(_<=l)if(l=_,(c=v-1)>n)d=Math.max(1,2*n-c);else break}}if(h(p+1,n)>l)break;o=b}return c},e.prototype.match_alphabet_=function(t){for(var e={},n=0;n<t.length;n++)e[t.charAt(n)]=0;for(var n=0;n<t.length;n++)e[t.charAt(n)]|=1<<t.length-n-1;return e},e.prototype.patch_addContext_=function(t,n){if(0!=n.length){if(null===t.start2)throw Error("patch not initialized");for(var r=n.substring(t.start2,t.start2+t.length1),i=0;n.indexOf(r)!=n.lastIndexOf(r)&&r.length<this.Match_MaxBits-this.Patch_Margin-this.Patch_Margin;)i+=this.Patch_Margin,r=n.substring(t.start2-i,t.start2+t.length1+i);i+=this.Patch_Margin;var o=n.substring(t.start2-i,t.start2);o&&t.diffs.unshift(new e.Diff(0,o));var s=n.substring(t.start2+t.length1,t.start2+t.length1+i);s&&t.diffs.push(new e.Diff(0,s)),t.start1-=o.length,t.start2-=o.length,t.length1+=o.length+s.length,t.length2+=o.length+s.length}},e.prototype.patch_make=function(t,n,r){if("string"==typeof t&&"string"==typeof n&&void 0===r)i=t,(o=this.diff_main(i,n,!0)).length>2&&(this.diff_cleanupSemantic(o),this.diff_cleanupEfficiency(o));else if(t&&"object"==typeof t&&void 0===n&&void 0===r)o=t,i=this.diff_text1(o);else if("string"==typeof t&&n&&"object"==typeof n&&void 0===r)i=t,o=n;else if("string"==typeof t&&"string"==typeof n&&r&&"object"==typeof r)i=t,o=r;else throw Error("Unknown call format to patch_make.");if(0===o.length)return[];for(var i,o,s=[],a=new e.patch_obj,h=0,l=0,c=0,f=i,u=i,p=0;p<o.length;p++){var d=o[p][0],g=o[p][1];switch(!h&&0!==d&&(a.start1=l,a.start2=c),d){case 1:a.diffs[h++]=o[p],a.length2+=g.length,u=u.substring(0,c)+g+u.substring(c);break;case -1:a.length1+=g.length,a.diffs[h++]=o[p],u=u.substring(0,c)+u.substring(c+g.length);break;case 0:g.length<=2*this.Patch_Margin&&h&&o.length!=p+1?(a.diffs[h++]=o[p],a.length1+=g.length,a.length2+=g.length):g.length>=2*this.Patch_Margin&&h&&(this.patch_addContext_(a,f),s.push(a),a=new e.patch_obj,h=0,f=u,l=c)}1!==d&&(l+=g.length),-1!==d&&(c+=g.length)}return h&&(this.patch_addContext_(a,f),s.push(a)),s},e.prototype.patch_deepCopy=function(t){for(var n=[],r=0;r<t.length;r++){var i=t[r],o=new e.patch_obj;o.diffs=[];for(var s=0;s<i.diffs.length;s++)o.diffs[s]=new e.Diff(i.diffs[s][0],i.diffs[s][1]);o.start1=i.start1,o.start2=i.start2,o.length1=i.length1,o.length2=i.length2,n[r]=o}return n},e.prototype.patch_apply=function(t,e){if(0==t.length)return[e,[]];t=this.patch_deepCopy(t);var n=this.patch_addPadding(t);e=n+e+n,this.patch_splitMax(t);for(var r=0,i=[],o=0;o<t.length;o++){var s=t[o].start2+r,a=this.diff_text1(t[o].diffs),h=-1;if(a.length>this.Match_MaxBits?-1!=(c=this.match_main(e,a.substring(0,this.Match_MaxBits),s))&&(-1==(h=this.match_main(e,a.substring(a.length-this.Match_MaxBits),s+a.length-this.Match_MaxBits))||c>=h)&&(c=-1):c=this.match_main(e,a,s),-1==c)i[o]=!1,r-=t[o].length2-t[o].length1;else if(i[o]=!0,r=c-s,f=-1==h?e.substring(c,c+a.length):e.substring(c,h+this.Match_MaxBits),a==f)e=e.substring(0,c)+this.diff_text2(t[o].diffs)+e.substring(c+a.length);else{var l=this.diff_main(a,f,!1);if(a.length>this.Match_MaxBits&&this.diff_levenshtein(l)/a.length>this.Patch_DeleteThreshold)i[o]=!1;else{this.diff_cleanupSemanticLossless(l);for(var c,f,u,p=0,d=0;d<t[o].diffs.length;d++){var g=t[o].diffs[d];0!==g[0]&&(u=this.diff_xIndex(l,p)),1===g[0]?e=e.substring(0,c+u)+g[1]+e.substring(c+u):-1===g[0]&&(e=e.substring(0,c+u)+e.substring(c+this.diff_xIndex(l,p+g[1].length))),-1!==g[0]&&(p+=g[1].length)}}}}return[e=e.substring(n.length,e.length-n.length),i]},e.prototype.patch_addPadding=function(t){for(var n=this.Patch_Margin,r="",i=1;i<=n;i++)r+=String.fromCharCode(i);for(var i=0;i<t.length;i++)t[i].start1+=n,t[i].start2+=n;var o=t[0],s=o.diffs;if(0==s.length||0!=s[0][0])s.unshift(new e.Diff(0,r)),o.start1-=n,o.start2-=n,o.length1+=n,o.length2+=n;else if(n>s[0][1].length){var a=n-s[0][1].length;s[0][1]=r.substring(s[0][1].length)+s[0][1],o.start1-=a,o.start2-=a,o.length1+=a,o.length2+=a}if(0==(s=(o=t[t.length-1]).diffs).length||0!=s[s.length-1][0])s.push(new e.Diff(0,r)),o.length1+=n,o.length2+=n;else if(n>s[s.length-1][1].length){var a=n-s[s.length-1][1].length;s[s.length-1][1]+=r.substring(0,a),o.length1+=a,o.length2+=a}return r},e.prototype.patch_splitMax=function(t){for(var n=this.Match_MaxBits,r=0;r<t.length;r++)if(!(t[r].length1<=n)){var i=t[r];t.splice(r--,1);for(var o=i.start1,s=i.start2,a="";0!==i.diffs.length;){var h=new e.patch_obj,l=!0;for(h.start1=o-a.length,h.start2=s-a.length,""!==a&&(h.length1=h.length2=a.length,h.diffs.push(new e.Diff(0,a)));0!==i.diffs.length&&h.length1<n-this.Patch_Margin;){var c=i.diffs[0][0],f=i.diffs[0][1];1===c?(h.length2+=f.length,s+=f.length,h.diffs.push(i.diffs.shift()),l=!1):-1===c&&1==h.diffs.length&&0==h.diffs[0][0]&&f.length>2*n?(h.length1+=f.length,o+=f.length,l=!1,h.diffs.push(new e.Diff(c,f)),i.diffs.shift()):(f=f.substring(0,n-h.length1-this.Patch_Margin),h.length1+=f.length,o+=f.length,0===c?(h.length2+=f.length,s+=f.length):l=!1,h.diffs.push(new e.Diff(c,f)),f==i.diffs[0][1]?i.diffs.shift():i.diffs[0][1]=i.diffs[0][1].substring(f.length))}a=(a=this.diff_text2(h.diffs)).substring(a.length-this.Patch_Margin);var u=this.diff_text1(i.diffs).substring(0,this.Patch_Margin);""!==u&&(h.length1+=u.length,h.length2+=u.length,0!==h.diffs.length&&0===h.diffs[h.diffs.length-1][0]?h.diffs[h.diffs.length-1][1]+=u:h.diffs.push(new e.Diff(0,u))),l||t.splice(++r,0,h)}}},e.prototype.patch_toText=function(t){for(var e=[],n=0;n<t.length;n++)e[n]=t[n];return e.join("")},e.prototype.patch_fromText=function(t){var n=[];if(!t)return n;for(var r=t.split("\n"),i=0,o=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;i<r.length;){var s=r[i].match(o);if(!s)throw Error("Invalid patch string: "+r[i]);var a=new e.patch_obj;for(n.push(a),a.start1=parseInt(s[1],10),""===s[2]?(a.start1--,a.length1=1):"0"==s[2]?a.length1=0:(a.start1--,a.length1=parseInt(s[2],10)),a.start2=parseInt(s[3],10),""===s[4]?(a.start2--,a.length2=1):"0"==s[4]?a.length2=0:(a.start2--,a.length2=parseInt(s[4],10)),i++;i<r.length;){var h=r[i].charAt(0);try{var l=decodeURI(r[i].substring(1))}catch(t){throw Error("Illegal escape in patch_fromText: "+l)}if("-"==h)a.diffs.push(new e.Diff(-1,l));else if("+"==h)a.diffs.push(new e.Diff(1,l));else if(" "==h)a.diffs.push(new e.Diff(0,l));else if("@"==h)break;else if(""===h);else throw Error('Invalid patch mode "'+h+'" in: '+l);i++}}return n},e.patch_obj=function(){this.diffs=[],this.start1=null,this.start2=null,this.length1=0,this.length2=0},e.patch_obj.prototype.toString=function(){t=0===this.length1?this.start1+",0":1==this.length1?this.start1+1:this.start1+1+","+this.length1;for(var t,e,n=0===this.length2?this.start2+",0":1==this.length2?this.start2+1:this.start2+1+","+this.length2,r=["@@ -"+t+" +"+n+" @@\n"],i=0;i<this.diffs.length;i++){switch(this.diffs[i][0]){case 1:e="+";break;case -1:e="-";break;case 0:e=" "}r[i+1]=e+encodeURI(this.diffs[i][1])+"\n"}return r.join("").replace(/%20/g," ")},t.exports=e,t.exports.diff_match_patch=e,t.exports.DIFF_DELETE=-1,t.exports.DIFF_INSERT=1,t.exports.DIFF_EQUAL=0},48189:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var o=n(77117),s=n(94285),a=n(15646),h=n(42400);e.default=function(t){function e(e){var n=t.call(this,e)||this;return n.state={value:n.props.value},n.onChange=n.onChange.bind(n),n.diff=n.diff.bind(n),n}return i(e,t),e.prototype.componentDidUpdate=function(){var t=this.props.value;t!==this.state.value&&this.setState({value:t})},e.prototype.onChange=function(t){this.setState({value:t}),this.props.onChange&&this.props.onChange(t)},e.prototype.diff=function(){var t=new h,e=this.state.value[0],n=this.state.value[1];if(0===e.length&&0===n.length)return[];var r=t.diff_main(e,n);t.diff_cleanupSemantic(r);var i=this.generateDiffedLines(r);return this.setCodeMarkers(i)},e.prototype.generateDiffedLines=function(t){var e={DIFF_EQUAL:0,DIFF_DELETE:-1,DIFF_INSERT:1},n={left:[],right:[]},r={left:1,right:1};return t.forEach(function(t){var i=t[0],o=t[1],s=o.split("\n").length-1;if(0!==o.length){var a=o[0],h=o[o.length-1],l=0;switch(i){case e.DIFF_EQUAL:r.left+=s,r.right+=s;break;case e.DIFF_DELETE:"\n"===a&&(r.left++,s--),0===(l=s)&&n.right.push({startLine:r.right,endLine:r.right}),"\n"===h&&(l-=1),n.left.push({startLine:r.left,endLine:r.left+l}),r.left+=s;break;case e.DIFF_INSERT:"\n"===a&&(r.right++,s--),0===(l=s)&&n.left.push({startLine:r.left,endLine:r.left}),"\n"===h&&(l-=1),n.right.push({startLine:r.right,endLine:r.right+l}),r.right+=s;break;default:throw Error("Diff type was not defined.")}}}),n},e.prototype.setCodeMarkers=function(t){void 0===t&&(t={left:[],right:[]});for(var e=[],n={left:[],right:[]},r=0;r<t.left.length;r++){var i={startRow:t.left[r].startLine-1,endRow:t.left[r].endLine,type:"text",className:"codeMarker"};n.left.push(i)}for(var r=0;r<t.right.length;r++){var i={startRow:t.right[r].startLine-1,endRow:t.right[r].endLine,type:"text",className:"codeMarker"};n.right.push(i)}return e[0]=n.left,e[1]=n.right,e},e.prototype.render=function(){var t=this.diff();return s.createElement(a.default,{name:this.props.name,className:this.props.className,focus:this.props.focus,orientation:this.props.orientation,splits:this.props.splits,mode:this.props.mode,theme:this.props.theme,height:this.props.height,width:this.props.width,fontSize:this.props.fontSize,showGutter:this.props.showGutter,onChange:this.onChange,onPaste:this.props.onPaste,onLoad:this.props.onLoad,onScroll:this.props.onScroll,minLines:this.props.minLines,maxLines:this.props.maxLines,readOnly:this.props.readOnly,highlightActiveLine:this.props.highlightActiveLine,showPrintMargin:this.props.showPrintMargin,tabSize:this.props.tabSize,cursorStart:this.props.cursorStart,editorProps:this.props.editorProps,style:this.props.style,scrollMargin:this.props.scrollMargin,setOptions:this.props.setOptions,wrapEnabled:this.props.wrapEnabled,enableBasicAutocompletion:this.props.enableBasicAutocompletion,enableLiveAutocompletion:this.props.enableLiveAutocompletion,value:this.state.value,markers:t})},e.propTypes={cursorStart:o.number,editorProps:o.object,enableBasicAutocompletion:o.bool,enableLiveAutocompletion:o.bool,focus:o.bool,fontSize:o.number,height:o.string,highlightActiveLine:o.bool,maxLines:o.number,minLines:o.number,mode:o.string,name:o.string,className:o.string,onLoad:o.func,onPaste:o.func,onScroll:o.func,onChange:o.func,orientation:o.string,readOnly:o.bool,scrollMargin:o.array,setOptions:o.object,showGutter:o.bool,showPrintMargin:o.bool,splits:o.number,style:o.object,tabSize:o.number,theme:o.string,value:o.array,width:o.string,wrapEnabled:o.bool},e.defaultProps={cursorStart:1,editorProps:{},enableBasicAutocompletion:!1,enableLiveAutocompletion:!1,focus:!1,fontSize:12,height:"500px",highlightActiveLine:!0,maxLines:null,minLines:null,mode:"",name:"ace-editor",onLoad:null,onScroll:null,onPaste:null,onChange:null,orientation:"beside",readOnly:!1,scrollMargin:[0,0,0,0],setOptions:{},showGutter:!0,showPrintMargin:!0,splits:2,style:{},tabSize:4,theme:"github",value:["",""],width:"500px",wrapEnabled:!0},e}(s.Component)},68042:(t,e,n)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.diff=e.split=void 0;var r=n(84341);e.diff=n(48189).default,e.split=n(15646).default,e.default=r.default},77117:(t,e,n)=>{t.exports=n(29027)()},84341:function(t,e,n){"use strict";var r,i=this&&this.__extends||(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),o=this&&this.__assign||function(){return(o=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var i in e=arguments[n])Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t}).apply(this,arguments)};Object.defineProperty(e,"__esModule",{value:!0});var s=n(97357),a=n(77117),h=n(94285),l=n(21531),c=n(36663),f=(0,c.getAceInstance)();e.default=function(t){function e(e){var n=t.call(this,e)||this;return c.editorEvents.forEach(function(t){n[t]=n[t].bind(n)}),n.debounce=c.debounce,n}return i(e,t),e.prototype.isInShadow=function(t){for(var e=t&&t.parentNode;e;){if("[object ShadowRoot]"===e.toString())return!0;e=e.parentNode}return!1},e.prototype.componentDidMount=function(){var t=this,e=this.props,n=e.className,r=e.onBeforeLoad,i=e.onValidate,o=e.mode,s=e.focus,a=e.theme,h=e.fontSize,l=e.lineHeight,u=e.value,p=e.defaultValue,d=e.showGutter,g=e.wrapEnabled,b=e.showPrintMargin,v=e.scrollMargin,y=void 0===v?[0,0,0,0]:v,_=e.keyboardHandler,m=e.onLoad,w=e.commands,S=e.annotations,O=e.markers,M=e.placeholder;this.editor=f.edit(this.refEditor),r&&r(f);for(var E=Object.keys(this.props.editorProps),x=0;x<E.length;x++)this.editor[E[x]]=this.props.editorProps[E[x]];this.props.debounceChangePeriod&&(this.onChange=this.debounce(this.onChange,this.props.debounceChangePeriod)),this.editor.renderer.setScrollMargin(y[0],y[1],y[2],y[3]),this.isInShadow(this.refEditor)&&this.editor.renderer.attachToShadowRoot(),this.editor.getSession().setMode("string"==typeof o?"ace/mode/".concat(o):o),a&&""!==a&&this.editor.setTheme("ace/theme/".concat(a)),this.editor.setFontSize("number"==typeof h?"".concat(h,"px"):h),l&&(this.editor.container.style.lineHeight="number"==typeof l?"".concat(l,"px"):"".concat(l),this.editor.renderer.updateFontSize()),this.editor.getSession().setValue(p||u||""),this.props.navigateToFileEnd&&this.editor.navigateFileEnd(),this.editor.renderer.setShowGutter(d),this.editor.getSession().setUseWrapMode(g),this.editor.setShowPrintMargin(b),this.editor.on("focus",this.onFocus),this.editor.on("blur",this.onBlur),this.editor.on("copy",this.onCopy),this.editor.on("paste",this.onPaste),this.editor.on("change",this.onChange),this.editor.on("input",this.onInput),M&&this.updatePlaceholder(),this.editor.getSession().selection.on("changeSelection",this.onSelectionChange),this.editor.getSession().selection.on("changeCursor",this.onCursorChange),i&&this.editor.getSession().on("changeAnnotation",function(){var e=t.editor.getSession().getAnnotations();t.props.onValidate(e)}),this.editor.session.on("changeScrollTop",this.onScroll),this.editor.getSession().setAnnotations(S||[]),O&&O.length>0&&this.handleMarkers(O);var C=this.editor.$options;c.editorOptions.forEach(function(e){C.hasOwnProperty(e)?t.editor.setOption(e,t.props[e]):t.props[e]&&console.warn("ReactAce: editor option ".concat(e," was activated but not found. Did you need to import a related tool or did you possibly mispell the option?"))}),this.handleOptions(this.props),Array.isArray(w)&&w.forEach(function(e){"string"==typeof e.exec?t.editor.commands.bindKey(e.bindKey,e.exec):t.editor.commands.addCommand(e)}),_&&this.editor.setKeyboardHandler("ace/keyboard/"+_),n&&(this.refEditor.className+=" "+n),m&&m(this.editor),this.editor.resize(),s&&this.editor.focus()},e.prototype.componentDidUpdate=function(t){for(var e=this.props,n=0;n<c.editorOptions.length;n++){var r=c.editorOptions[n];e[r]!==t[r]&&this.editor.setOption(r,e[r])}if(e.className!==t.className){var i=this.refEditor.className.trim().split(" ");t.className.trim().split(" ").forEach(function(t){var e=i.indexOf(t);i.splice(e,1)}),this.refEditor.className=" "+e.className+" "+i.join(" ")}var o=this.editor&&null!=e.value&&this.editor.getValue()!==e.value;if(o){this.silent=!0;var s=this.editor.session.selection.toJSON();this.editor.setValue(e.value,e.cursorStart),this.editor.session.selection.fromJSON(s),this.silent=!1}e.placeholder!==t.placeholder&&this.updatePlaceholder(),e.mode!==t.mode&&this.editor.getSession().setMode("string"==typeof e.mode?"ace/mode/".concat(e.mode):e.mode),e.theme!==t.theme&&this.editor.setTheme("ace/theme/"+e.theme),e.keyboardHandler!==t.keyboardHandler&&(e.keyboardHandler?this.editor.setKeyboardHandler("ace/keyboard/"+e.keyboardHandler):this.editor.setKeyboardHandler(null)),e.fontSize!==t.fontSize&&this.editor.setFontSize("number"==typeof e.fontSize?"".concat(e.fontSize,"px"):e.fontSize),e.lineHeight!==t.lineHeight&&(this.editor.container.style.lineHeight="number"==typeof e.lineHeight?"".concat(e.lineHeight,"px"):e.lineHeight,this.editor.renderer.updateFontSize()),e.wrapEnabled!==t.wrapEnabled&&this.editor.getSession().setUseWrapMode(e.wrapEnabled),e.showPrintMargin!==t.showPrintMargin&&this.editor.setShowPrintMargin(e.showPrintMargin),e.showGutter!==t.showGutter&&this.editor.renderer.setShowGutter(e.showGutter),l(e.setOptions,t.setOptions)||this.handleOptions(e),(o||!l(e.annotations,t.annotations))&&this.editor.getSession().setAnnotations(e.annotations||[]),!l(e.markers,t.markers)&&Array.isArray(e.markers)&&this.handleMarkers(e.markers),l(e.scrollMargin,t.scrollMargin)||this.handleScrollMargins(e.scrollMargin),(t.height!==this.props.height||t.width!==this.props.width)&&this.editor.resize(),this.props.focus&&!t.focus&&this.editor.focus()},e.prototype.handleScrollMargins=function(t){void 0===t&&(t=[0,0,0,0]),this.editor.renderer.setScrollMargin(t[0],t[1],t[2],t[3])},e.prototype.componentWillUnmount=function(){this.editor&&(this.editor.destroy(),this.editor=null)},e.prototype.onChange=function(t){if(this.props.onChange&&!this.silent){var e=this.editor.getValue();this.props.onChange(e,t)}},e.prototype.onSelectionChange=function(t){if(this.props.onSelectionChange){var e=this.editor.getSelection();this.props.onSelectionChange(e,t)}},e.prototype.onCursorChange=function(t){if(this.props.onCursorChange){var e=this.editor.getSelection();this.props.onCursorChange(e,t)}},e.prototype.onInput=function(t){this.props.onInput&&this.props.onInput(t),this.props.placeholder&&this.updatePlaceholder()},e.prototype.onFocus=function(t){this.props.onFocus&&this.props.onFocus(t,this.editor)},e.prototype.onBlur=function(t){this.props.onBlur&&this.props.onBlur(t,this.editor)},e.prototype.onCopy=function(t){var e=t.text;this.props.onCopy&&this.props.onCopy(e)},e.prototype.onPaste=function(t){var e=t.text;this.props.onPaste&&this.props.onPaste(e)},e.prototype.onScroll=function(){this.props.onScroll&&this.props.onScroll(this.editor)},e.prototype.handleOptions=function(t){for(var e=Object.keys(t.setOptions),n=0;n<e.length;n++)this.editor.setOption(e[n],t.setOptions[e[n]])},e.prototype.handleMarkers=function(t){var e=this,n=this.editor.getSession().getMarkers(!0);for(var r in n)n.hasOwnProperty(r)&&this.editor.getSession().removeMarker(n[r].id);for(var r in n=this.editor.getSession().getMarkers(!1))n.hasOwnProperty(r)&&"ace_active-line"!==n[r].clazz&&"ace_selected-word"!==n[r].clazz&&this.editor.getSession().removeMarker(n[r].id);t.forEach(function(t){var n=t.startRow,r=t.startCol,i=t.endRow,o=t.endCol,a=t.className,h=t.type,l=t.inFront,c=new s.Range(n,r,i,o);e.editor.getSession().addMarker(c,a,h,void 0!==l&&l)})},e.prototype.updatePlaceholder=function(){var t=this.editor,e=this.props.placeholder,n=!t.session.getValue().length,r=t.renderer.placeholderNode;!n&&r?(t.renderer.scroller.removeChild(t.renderer.placeholderNode),t.renderer.placeholderNode=null):n&&!r?((r=t.renderer.placeholderNode=document.createElement("div")).textContent=e||"",r.className="ace_comment ace_placeholder",r.style.padding="0 9px",r.style.position="absolute",r.style.zIndex="3",t.renderer.scroller.appendChild(r)):n&&r&&(r.textContent=e)},e.prototype.updateRef=function(t){this.refEditor=t},e.prototype.render=function(){var t=this.props,e=t.name,n=t.width,r=t.height,i=t.style,s=o({width:n,height:r},i);return h.createElement("div",{ref:this.updateRef,id:e,style:s})},e.propTypes={mode:a.oneOfType([a.string,a.object]),focus:a.bool,theme:a.string,name:a.string,className:a.string,height:a.string,width:a.string,fontSize:a.oneOfType([a.number,a.string]),lineHeight:a.oneOfType([a.number,a.string]),showGutter:a.bool,onChange:a.func,onCopy:a.func,onPaste:a.func,onFocus:a.func,onInput:a.func,onBlur:a.func,onScroll:a.func,value:a.string,defaultValue:a.string,onLoad:a.func,onSelectionChange:a.func,onCursorChange:a.func,onBeforeLoad:a.func,onValidate:a.func,minLines:a.number,maxLines:a.number,readOnly:a.bool,highlightActiveLine:a.bool,tabSize:a.number,showPrintMargin:a.bool,cursorStart:a.number,debounceChangePeriod:a.number,editorProps:a.object,setOptions:a.object,style:a.object,scrollMargin:a.array,annotations:a.array,markers:a.array,keyboardHandler:a.string,wrapEnabled:a.bool,enableSnippets:a.bool,enableBasicAutocompletion:a.oneOfType([a.bool,a.array]),enableLiveAutocompletion:a.oneOfType([a.bool,a.array]),navigateToFileEnd:a.bool,commands:a.array,placeholder:a.string},e.defaultProps={name:"ace-editor",focus:!1,mode:"",theme:"",height:"500px",width:"500px",fontSize:12,enableSnippets:!1,showGutter:!0,onChange:null,onPaste:null,onLoad:null,onScroll:null,minLines:null,maxLines:null,readOnly:!1,highlightActiveLine:!0,showPrintMargin:!0,tabSize:4,cursorStart:1,editorProps:{},style:{},scrollMargin:[0,0,0,0],setOptions:{},wrapEnabled:!1,enableBasicAutocompletion:!1,enableLiveAutocompletion:!1,placeholder:null,navigateToFileEnd:!0},e}(h.Component)}}]);