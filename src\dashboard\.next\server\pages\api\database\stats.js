"use strict";(()=>{var e={};e.id=4012,e.ids=[4012],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},6282:(e,t,o)=>{o.r(t),o.d(t,{config:()=>p,default:()=>g,routeModule:()=>h});var a={};o.r(a),o.d(a,{default:()=>f});var i=o(3433),n=o(264),r=o(584),s=o(5806),d=o(8525),l=o(2518);let{url:c,name:u}=o(8580).dashboardConfig.database,b=null;async function m(){if(b)return b;let e=await l.MongoClient.connect(c);return b=e,e}async function f(e,t){if(!await (0,s.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let e=(await m()).db(u),o=await e.listCollections().toArray(),a=0;for(let t of o){let o=await e.collection(t.name).countDocuments();a+=o}let i=await e.stats();return t.status(200).json({collectionsCount:o.length,documentsCount:a,databaseSize:i.dataSize,storageSize:i.storageSize,avgObjSize:i.avgObjSize,indexes:i.indexes,indexSize:i.indexSize})}catch(e){return t.status(500).json({error:"Internal server error"})}}let g=(0,r.M)(a,"default"),p=(0,r.M)(a,"config"),h=new i.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/database/stats",pathname:"/api/database/stats",bundlePath:"",filename:""},userland:a})},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var a=o(5542),i=o.n(a);let n=require("next-auth/providers/discord");var r=o.n(n),s=o(8580);let d={providers:[r()({clientId:s.dashboardConfig.bot.clientId,clientSecret:s.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let i=!1;if(o)if((s.dashboardConfig.dashboard.admins||[]).includes(o))i=!0;else{let e=s.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&s.dashboardConfig.bot.token&&s.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${s.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${s.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();i=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:s.dashboardConfig.dashboard.session.secret||s.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),i=o(2115),n=o.n(i),r=o(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");s=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=6282);module.exports=o})();