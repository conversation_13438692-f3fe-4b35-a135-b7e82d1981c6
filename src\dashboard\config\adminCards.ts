import { FiSettings, FiUsers, FiPackage, FiServer, FiShield, FiDatabase, FiActivity, FiMessageSquare, FiGlobe, FiBox, FiCommand, FiZap, FiCalendar } from 'react-icons/fi';

export interface AdminCardConfig {
  id: string;
  title: string;
  description: string;
  icon: any;
  href: string;
  color: string;
  gradient: {
    from: string;
    to: string;
  };
  badge?: {
    text: string;
    color: string;
  };
  stats?: {
    value: number;
    label: string;
    change?: {
      value: number;
      isIncrease: boolean;
    };
  }[];
  requiresPermission?: string[];
  developerOnly?: boolean;
}

export const ADMIN_CARD_CONFIGS: AdminCardConfig[] = [
  {
    id: 'guild-settings',
    title: 'Guild Settings',
    description: 'Configure features and settings for your guild.',
    icon: FiSettings,
    href: '/admin/guilds',
    color: 'blue',
    gradient: {
      from: 'rgba(66, 153, 225, 0.4)',
      to: 'rgba(66, 153, 225, 0.1)'
    }
  },
  {
    id: 'applications',
    title: 'Applications',
    description: 'Review and manage moderator applications.',
    icon: FiUsers,
    href: '/admin/applications',
    color: 'purple',
    gradient: {
      from: 'rgba(159, 122, 234, 0.4)',
      to: 'rgba(159, 122, 234, 0.1)'
    },
    stats: [
      {
        value: 0, // Will be updated dynamically
        label: 'Total',
        change: {
          value: 0,
          isIncrease: true
        }
      },
      {
        value: 0, // Will be updated dynamically
        label: 'Pending'
      }
    ]
  },
  {
    id: 'addons',
    title: 'Addons',
    description: 'Enable or configure bot addons and extensions.',
    icon: FiBox,
    href: '/admin/addons',
    color: 'green',
    gradient: {
      from: 'rgba(72, 187, 120, 0.4)',
      to: 'rgba(72, 187, 120, 0.1)'
    }
  },
  {
    id: 'event-scheduler',
    title: 'Event Scheduler',
    description: 'Schedule timed messages and server events.',
    icon: FiCalendar,
    href: '/admin/scheduler',
    color: 'teal',
    gradient: {
      from: 'rgba(49, 151, 149, 0.4)',
      to: 'rgba(49, 151, 149, 0.1)'
    },
    badge: {
      text: 'New',
      color: 'teal'
    }
  },
  {
    id: 'users',
    title: 'Users',
    description: 'Manage server members, roles, and permissions.',
    icon: FiUsers,
    href: '/admin/users',
    color: 'yellow',
    gradient: {
      from: 'rgba(236, 201, 75, 0.4)',
      to: 'rgba(236, 201, 75, 0.1)'
    }
  },
  {
    id: 'database',
    title: 'Database',
    description: 'Manage database collections and records',
    icon: FiDatabase,
    href: '/admin/database',
    color: 'cyan',
    gradient: {
      from: 'rgba(49, 151, 149, 0.4)',
      to: 'rgba(49, 151, 149, 0.1)'
    },
    requiresPermission: ['ADMIN']
  },
  {
    id: 'activity',
    title: 'Activity Logs',
    description: 'View server activity and audit logs.',
    icon: FiActivity,
    href: '/admin/activity-logs',
    color: 'orange',
    gradient: {
      from: 'rgba(237, 137, 54, 0.4)',
      to: 'rgba(237, 137, 54, 0.1)'
    }
  },
  {
    id: 'commands',
    title: 'Commands',
    description: 'Configure and manage bot commands.',
    icon: FiCommand,
    href: '/admin/commands',
    color: 'pink',
    gradient: {
      from: 'rgba(237, 100, 166, 0.4)',
      to: 'rgba(237, 100, 166, 0.1)'
    }
  },
  {
    id: 'embed-builder',
    title: 'Message Builder',
    description: 'Create rich embeds with interactive components and buttons.',
    icon: FiMessageSquare,
    href: '/admin/embed-builder',
    color: 'pink',
    gradient: {
      from: 'rgba(236, 72, 153, 0.4)',
      to: 'rgba(236, 72, 153, 0.1)'
    },
    badge: {
      text: 'New',
      color: 'pink'
    }
  }
];
