/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-5fc50a"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/mode-yaml.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/mode-yaml.js ***!
  \******************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* module decorator */ module = __webpack_require__.nmd(module);\nace.define(\"ace/mode/yaml_highlight_rules\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text_highlight_rules\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../lib/oop\");\nvar TextHighlightRules = require(\"./text_highlight_rules\").TextHighlightRules;\nvar YamlHighlightRules = function () {\n    this.$rules = {\n        \"start\": [\n            {\n                token: \"comment\",\n                regex: \"#.*$\"\n            }, {\n                token: \"list.markup\",\n                regex: /^(?:-{3}|\\.{3})\\s*(?=#|$)/\n            }, {\n                token: \"list.markup\",\n                regex: /^\\s*[\\-?](?:$|\\s)/\n            }, {\n                token: \"constant\",\n                regex: \"!![\\\\w//]+\"\n            }, {\n                token: \"constant.language\",\n                regex: \"[&\\\\*][a-zA-Z0-9-_]+\"\n            }, {\n                token: [\"meta.tag\", \"keyword\"],\n                regex: /^(\\s*\\w[^\\s:]*?)(:(?=\\s|$))/\n            }, {\n                token: [\"meta.tag\", \"keyword\"],\n                regex: /(\\w[^\\s:]*?)(\\s*:(?=\\s|$))/\n            }, {\n                token: \"keyword.operator\",\n                regex: \"<<\\\\w*:\\\\w*\"\n            }, {\n                token: \"keyword.operator\",\n                regex: \"-\\\\s*(?=[{])\"\n            }, {\n                token: \"string\", // single line\n                regex: '[\"](?:(?:\\\\\\\\.)|(?:[^\"\\\\\\\\]))*?[\"]'\n            }, {\n                token: \"string\", // multi line string start\n                regex: /[|>][-+\\d]*(?:$|\\s+(?:$|#))/,\n                onMatch: function (val, state, stack, line) {\n                    line = line.replace(/ #.*/, \"\");\n                    var indent = /^ *((:\\s*)?-(\\s*[^|>])?)?/.exec(line)[0]\n                        .replace(/\\S\\s*$/, \"\").length;\n                    var indentationIndicator = parseInt(/\\d+[\\s+-]*$/.exec(line));\n                    if (indentationIndicator) {\n                        indent += indentationIndicator - 1;\n                        this.next = \"mlString\";\n                    }\n                    else {\n                        this.next = \"mlStringPre\";\n                    }\n                    if (!stack.length) {\n                        stack.push(this.next);\n                        stack.push(indent);\n                    }\n                    else {\n                        stack[0] = this.next;\n                        stack[1] = indent;\n                    }\n                    return this.token;\n                },\n                next: \"mlString\"\n            }, {\n                token: \"string\", // single quoted string\n                regex: \"['](?:(?:\\\\\\\\.)|(?:[^'\\\\\\\\]))*?[']\"\n            }, {\n                token: \"constant.numeric\", // float\n                regex: /(\\b|[+\\-\\.])[\\d_]+(?:(?:\\.[\\d_]*)?(?:[eE][+\\-]?[\\d_]+)?)(?=[^\\d-\\w]|$)$/\n            }, {\n                token: \"constant.numeric\", // other number\n                regex: /[+\\-]?\\.inf\\b|NaN\\b|0x[\\dA-Fa-f_]+|0b[10_]+/\n            }, {\n                token: \"constant.language.boolean\",\n                regex: \"\\\\b(?:true|false|TRUE|FALSE|True|False|yes|no)\\\\b\"\n            }, {\n                token: \"paren.lparen\",\n                regex: \"[[({]\"\n            }, {\n                token: \"paren.rparen\",\n                regex: \"[\\\\])}]\"\n            }, {\n                token: \"text\",\n                regex: /[^\\s,:\\[\\]\\{\\}]+/\n            }\n        ],\n        \"mlStringPre\": [\n            {\n                token: \"indent\",\n                regex: /^ *$/\n            }, {\n                token: \"indent\",\n                regex: /^ */,\n                onMatch: function (val, state, stack) {\n                    var curIndent = stack[1];\n                    if (curIndent >= val.length) {\n                        this.next = \"start\";\n                        stack.shift();\n                        stack.shift();\n                    }\n                    else {\n                        stack[1] = val.length - 1;\n                        this.next = stack[0] = \"mlString\";\n                    }\n                    return this.token;\n                },\n                next: \"mlString\"\n            }, {\n                defaultToken: \"string\"\n            }\n        ],\n        \"mlString\": [\n            {\n                token: \"indent\",\n                regex: /^ *$/\n            }, {\n                token: \"indent\",\n                regex: /^ */,\n                onMatch: function (val, state, stack) {\n                    var curIndent = stack[1];\n                    if (curIndent >= val.length) {\n                        this.next = \"start\";\n                        stack.splice(0);\n                    }\n                    else {\n                        this.next = \"mlString\";\n                    }\n                    return this.token;\n                },\n                next: \"mlString\"\n            }, {\n                token: \"string\",\n                regex: '.+'\n            }\n        ]\n    };\n    this.normalizeRules();\n};\noop.inherits(YamlHighlightRules, TextHighlightRules);\nexports.YamlHighlightRules = YamlHighlightRules;\n\n});\n\nace.define(\"ace/mode/matching_brace_outdent\",[\"require\",\"exports\",\"module\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar Range = require(\"../range\").Range;\nvar MatchingBraceOutdent = function () { };\n(function () {\n    this.checkOutdent = function (line, input) {\n        if (!/^\\s+$/.test(line))\n            return false;\n        return /^\\s*\\}/.test(input);\n    };\n    this.autoOutdent = function (doc, row) {\n        var line = doc.getLine(row);\n        var match = line.match(/^(\\s*\\})/);\n        if (!match)\n            return 0;\n        var column = match[1].length;\n        var openBracePos = doc.findMatchingBracket({ row: row, column: column });\n        if (!openBracePos || openBracePos.row == row)\n            return 0;\n        var indent = this.$getIndent(doc.getLine(openBracePos.row));\n        doc.replace(new Range(row, 0, row, column - 1), indent);\n    };\n    this.$getIndent = function (line) {\n        return line.match(/^\\s*/)[0];\n    };\n}).call(MatchingBraceOutdent.prototype);\nexports.MatchingBraceOutdent = MatchingBraceOutdent;\n\n});\n\nace.define(\"ace/mode/folding/coffee\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/folding/fold_mode\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../../lib/oop\");\nvar BaseFoldMode = require(\"./fold_mode\").FoldMode;\nvar Range = require(\"../../range\").Range;\nvar FoldMode = exports.FoldMode = function () { };\noop.inherits(FoldMode, BaseFoldMode);\n(function () {\n    this.commentBlock = function (session, row) {\n        var re = /\\S/;\n        var line = session.getLine(row);\n        var startLevel = line.search(re);\n        if (startLevel == -1 || line[startLevel] != \"#\")\n            return;\n        var startColumn = line.length;\n        var maxRow = session.getLength();\n        var startRow = row;\n        var endRow = row;\n        while (++row < maxRow) {\n            line = session.getLine(row);\n            var level = line.search(re);\n            if (level == -1)\n                continue;\n            if (line[level] != \"#\")\n                break;\n            endRow = row;\n        }\n        if (endRow > startRow) {\n            var endColumn = session.getLine(endRow).length;\n            return new Range(startRow, startColumn, endRow, endColumn);\n        }\n    };\n    this.getFoldWidgetRange = function (session, foldStyle, row) {\n        var range = this.indentationBlock(session, row);\n        if (range)\n            return range;\n        range = this.commentBlock(session, row);\n        if (range)\n            return range;\n    };\n    this.getFoldWidget = function (session, foldStyle, row) {\n        var line = session.getLine(row);\n        var indent = line.search(/\\S/);\n        var next = session.getLine(row + 1);\n        var prev = session.getLine(row - 1);\n        var prevIndent = prev.search(/\\S/);\n        var nextIndent = next.search(/\\S/);\n        if (indent == -1) {\n            session.foldWidgets[row - 1] = prevIndent != -1 && prevIndent < nextIndent ? \"start\" : \"\";\n            return \"\";\n        }\n        if (prevIndent == -1) {\n            if (indent == nextIndent && line[indent] == \"#\" && next[indent] == \"#\") {\n                session.foldWidgets[row - 1] = \"\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"start\";\n            }\n        }\n        else if (prevIndent == indent && line[indent] == \"#\" && prev[indent] == \"#\") {\n            if (session.getLine(row - 2).search(/\\S/) == -1) {\n                session.foldWidgets[row - 1] = \"start\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"\";\n            }\n        }\n        if (prevIndent != -1 && prevIndent < indent)\n            session.foldWidgets[row - 1] = \"start\";\n        else\n            session.foldWidgets[row - 1] = \"\";\n        if (indent < nextIndent)\n            return \"start\";\n        else\n            return \"\";\n    };\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/folding/yaml\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/folding/coffee\",\"ace/range\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../../lib/oop\");\nvar CoffeeFoldMode = require(\"./coffee\").FoldMode;\nvar Range = require(\"../../range\").Range;\nvar FoldMode = exports.FoldMode = function () { };\noop.inherits(FoldMode, CoffeeFoldMode);\n(function () {\n    this.getFoldWidgetRange = function (session, foldStyle, row) {\n        var re = /\\S/;\n        var line = session.getLine(row);\n        var startLevel = line.search(re);\n        var isCommentFold = line[startLevel] === \"#\";\n        var isDashFold = line[startLevel] === \"-\";\n        if (startLevel == -1)\n            return;\n        var startColumn = line.length;\n        var maxRow = session.getLength();\n        var startRow = row;\n        var endRow = row;\n        if (isCommentFold) {\n            var range = this.commentBlock(session, row);\n            if (range)\n                return range;\n        }\n        else if (isDashFold) {\n            var range = this.indentationBlock(session, row);\n            if (range)\n                return range;\n        }\n        else {\n            while (++row < maxRow) {\n                var line = session.getLine(row);\n                var level = line.search(re);\n                if (level == -1)\n                    continue;\n                if (level <= startLevel && line[startLevel] !== '-') {\n                    var token = session.getTokenAt(row, 0);\n                    if (!token || token.type !== \"string\")\n                        break;\n                }\n                endRow = row;\n            }\n        }\n        if (endRow > startRow) {\n            var endColumn = session.getLine(endRow).length;\n            return new Range(startRow, startColumn, endRow, endColumn);\n        }\n    };\n    this.getFoldWidget = function (session, foldStyle, row) {\n        var line = session.getLine(row);\n        var indent = line.search(/\\S/);\n        var next = session.getLine(row + 1);\n        var prev = session.getLine(row - 1);\n        var prevIndent = prev.search(/\\S/);\n        var nextIndent = next.search(/\\S/);\n        var lineStartsWithDash = line[indent] === '-';\n        if (indent == -1) {\n            session.foldWidgets[row - 1] = prevIndent != -1 && prevIndent < nextIndent ? \"start\" : \"\";\n            return \"\";\n        }\n        if (prevIndent == -1) {\n            if (indent == nextIndent && line[indent] == \"#\" && next[indent] == \"#\") {\n                session.foldWidgets[row - 1] = \"\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"start\";\n            }\n        }\n        else if (prevIndent == indent && line[indent] == \"#\" && prev[indent] == \"#\") {\n            if (session.getLine(row - 2).search(/\\S/) == -1) {\n                session.foldWidgets[row - 1] = \"start\";\n                session.foldWidgets[row + 1] = \"\";\n                return \"\";\n            }\n        }\n        if (prevIndent != -1 && prevIndent < indent) {\n            session.foldWidgets[row - 1] = \"start\";\n        }\n        else if (prevIndent != -1 && (prevIndent == indent && lineStartsWithDash)) {\n            session.foldWidgets[row - 1] = \"start\";\n        }\n        else {\n            session.foldWidgets[row - 1] = \"\";\n        }\n        if (indent < nextIndent)\n            return \"start\";\n        else\n            return \"\";\n    };\n}).call(FoldMode.prototype);\n\n});\n\nace.define(\"ace/mode/yaml\",[\"require\",\"exports\",\"module\",\"ace/lib/oop\",\"ace/mode/text\",\"ace/mode/yaml_highlight_rules\",\"ace/mode/matching_brace_outdent\",\"ace/mode/folding/yaml\",\"ace/worker/worker_client\"], function(require, exports, module){\"use strict\";\nvar oop = require(\"../lib/oop\");\nvar TextMode = require(\"./text\").Mode;\nvar YamlHighlightRules = require(\"./yaml_highlight_rules\").YamlHighlightRules;\nvar MatchingBraceOutdent = require(\"./matching_brace_outdent\").MatchingBraceOutdent;\nvar FoldMode = require(\"./folding/yaml\").FoldMode;\nvar WorkerClient = require(\"../worker/worker_client\").WorkerClient;\nvar Mode = function () {\n    this.HighlightRules = YamlHighlightRules;\n    this.$outdent = new MatchingBraceOutdent();\n    this.foldingRules = new FoldMode();\n    this.$behaviour = this.$defaultBehaviour;\n};\noop.inherits(Mode, TextMode);\n(function () {\n    this.lineCommentStart = [\"#\"];\n    this.getNextLineIndent = function (state, line, tab) {\n        var indent = this.$getIndent(line);\n        if (state == \"start\") {\n            var match = line.match(/^.*[\\{\\(\\[]\\s*$/);\n            if (match) {\n                indent += tab;\n            }\n        }\n        return indent;\n    };\n    this.checkOutdent = function (state, line, input) {\n        return this.$outdent.checkOutdent(line, input);\n    };\n    this.autoOutdent = function (state, doc, row) {\n        this.$outdent.autoOutdent(doc, row);\n    };\n    this.createWorker = function (session) {\n        var worker = new WorkerClient([\"ace\"], \"ace/mode/yaml_worker\", \"YamlWorker\");\n        worker.attachToDocument(session.getDocument());\n        worker.on(\"annotate\", function (results) {\n            session.setAnnotations(results.data);\n        });\n        worker.on(\"terminate\", function () {\n            session.clearAnnotations();\n        });\n        return worker;\n    };\n    this.$id = \"ace/mode/yaml\";\n}).call(Mode.prototype);\nexports.Mode = Mode;\n\n});                (function() {\n                    ace.require([\"ace/mode/yaml\"], function(m) {\n                        if ( true && module) {\n                            module.exports = m;\n                        }\n                    });\n                })();\n            //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/mode-yaml.js\n"));

/***/ })

}]);