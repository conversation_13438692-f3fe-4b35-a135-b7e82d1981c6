import {
  Box,
  IconButton,
  <PERSON>over,
  <PERSON>over<PERSON>rigger,
  PopoverContent,
  PopoverHeader,
  PopoverBody,
  Text,
  Badge,
  Tooltip,
  VStack,
} from '@chakra-ui/react';
import { FiBell } from 'react-icons/fi';
import { useState } from 'react';
import { useSession } from 'next-auth/react';

// Lightweight notification center - minimal functionality to reduce bundle size
export default function NotificationCenter() {
  const { data: session } = useSession();
  const [notifications] = useState<any[]>([]); // Empty for now to reduce complexity
  
  // Don't render if no session
  if (!session?.user) {
    return null;
  }

  const unreadCount = 0; // Simplified for now

  return (
    <Popover placement="bottom-end">
      <PopoverTrigger>
        <Box position="relative">
          <Tooltip label="Notifications" placement="bottom">
            <IconButton
              aria-label="Notifications"
              icon={<FiBell />}
              variant="ghost"
              size="sm"
              color="gray.300"
              _hover={{
                bg: "whiteAlpha.200",
                color: "white",
                transform: "scale(1.05)",
              }}
              transition="all 0.2s"
            />
          </Tooltip>
          {unreadCount > 0 && (
            <Badge
              position="absolute"
              top="-1"
              right="-1"
              colorScheme="red"
              borderRadius="full"
              fontSize="xs"
              minW="18px"
              h="18px"
              display="flex"
              alignItems="center"
              justifyContent="center"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Box>
      </PopoverTrigger>
      <PopoverContent
        bg="gray.800"
        borderColor="whiteAlpha.200"
        boxShadow="2xl"
        maxW="400px"
        _focus={{ boxShadow: "2xl" }}
      >
        <PopoverHeader
          borderBottomColor="whiteAlpha.200"
          fontWeight="semibold"
          fontSize="lg"
          color="white"
        >
          Notifications
        </PopoverHeader>
        <PopoverBody maxH="400px" overflowY="auto">
          <VStack spacing={0} align="stretch">
            {!notifications || notifications.length === 0 ? (
              <Box py={8} textAlign="center">
                <Text color="gray.400" fontSize="sm">
                  No notifications yet
                </Text>
              </Box>
            ) : (
              (notifications || []).map((notification) => (
                <Box key={notification.id} p={3} borderBottom="1px" borderColor="whiteAlpha.100">
                  <Text fontSize="sm" color="white" fontWeight="medium">
                    {notification.title}
                  </Text>
                  <Text fontSize="xs" color="gray.400" mt={1}>
                    {notification.message}
                  </Text>
                </Box>
              ))
            )}
          </VStack>
        </PopoverBody>
      </PopoverContent>
    </Popover>
  );
} 