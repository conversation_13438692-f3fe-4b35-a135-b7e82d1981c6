"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3035],{25680:(e,t,r)=>{r.d(t,{r:()=>c});var n=r(94513),a=r(58714),l=r(2923),u=r(33225);let i=(0,l.R)(function(e,t){let{templateAreas:r,gap:a,rowGap:l,columnGap:i,column:s,row:o,autoFlow:c,autoRows:d,templateRows:m,autoColumns:p,templateColumns:b,...f}=e;return(0,n.jsx)(u.B.div,{ref:t,__css:{display:"grid",gridTemplateAreas:r,gridGap:a,gridRowGap:l,gridColumnGap:i,gridAutoColumns:p,gridColumn:s,gridRow:o,gridAutoFlow:c,gridAutoRows:d,gridTemplateRows:m,gridTemplateColumns:b},...f})});i.displayName="Grid";var s=r(83745),o=r(79364);let c=(0,l.R)(function(e,t){var r,l,u;let{columns:c,spacingX:d,spacingY:m,spacing:p,minChildWidth:b,...f}=e,v=(0,s.D)(),N=b?(r=b,l=v,(0,a.bk)(r,e=>{let t=(0,o.gf)("sizes",e,"number"==typeof e?`${e}px`:e)(l);return null===e?null:`repeat(auto-fit, minmax(${t}, 1fr))`})):(u=c,(0,a.bk)(u,e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`));return(0,n.jsx)(i,{ref:t,gap:p,columnGap:d,rowGap:m,templateColumns:N,...f})});c.displayName="SimpleGrid"},61481:(e,t,r)=>{r.d(t,{Sh:()=>B,Q0:()=>H,Q7:()=>T,OO:()=>F,lw:()=>D});var n=r(94513),a=r(75387),l=r(29035),u=r(22697),i=r(94285),s=r(49217);let o=e=>(0,n.jsx)(s.I,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})}),c=e=>(0,n.jsx)(s.I,{viewBox:"0 0 24 24",...e,children:(0,n.jsx)("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})});var d=r(65507);function m(e,t){let r=function(e){let t=parseFloat(e);return"number"!=typeof t||Number.isNaN(t)?0:t}(e),n=10**(t??10);return r=Math.round(r*n)/n,t?r.toFixed(t):r.toString()}function p(e){if(!Number.isFinite(e))return 0;let t=1,r=0;for(;Math.round(e*t)/t!==e;)t*=10,r+=1;return r}function b(e){return parseFloat(e.toString().replace(/[^\w.-]+/g,""))}function f(e,t){return Math.max(p(t),p(e))}function v(e,t,r){let n=b(e);if(Number.isNaN(n))return;let a=f(n,t);return m(n,r??a)}var N=r(50227),x=r(80222),g=r(40747),C=r(78961),y=r(50614),h=r(81405);function w(e,t,r,n){(0,i.useEffect)(()=>{if(!e.current||!n)return;let a=e.current.ownerDocument.defaultView??window,l=Array.isArray(t)?t:[t],u=new a.MutationObserver(e=>{for(let t of e)"attributes"===t.type&&t.attributeName&&l.includes(t.attributeName)&&r(t)});return u.observe(e.current,{attributes:!0,attributeFilter:l}),()=>u.disconnect()})}let k=/^[Ee0-9+\-.]$/;function _(e){return k.test(e)}var A=r(44637),I=r(2923),S=r(33225),R=r(56915);let[M,E]=(0,l.q)({name:"NumberInputStylesContext",errorMessage:"useNumberInputStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<NumberInput />\" "}),[j,P]=(0,l.q)({name:"NumberInputContext",errorMessage:"useNumberInputContext: `context` is undefined. Seems you forgot to wrap number-input's components within <NumberInput />"}),T=(0,I.R)(function(e,t){let r=(0,R.o)("NumberInput",e),l=(0,a.M)(e),{htmlProps:s,...o}=function(e={}){let{focusInputOnChange:t=!0,clampValueOnBlur:r=!0,keepWithinRange:n=!0,min:a=Number.MIN_SAFE_INTEGER,max:l=Number.MAX_SAFE_INTEGER,step:u=1,isReadOnly:s,isDisabled:o,isRequired:c,isInvalid:p,pattern:k="[0-9]*(.[0-9]+)?",inputMode:A="decimal",allowMouseWheel:I,id:S,onChange:R,precision:M,name:E,"aria-describedby":j,"aria-label":P,"aria-labelledby":T,onFocus:D,onBlur:F,onInvalid:q,getAriaValueText:B,isValidCharacter:H,format:G,parse:O,...K}=e,L=(0,d.c)(D),U=(0,d.c)(F),z=(0,d.c)(q),V=(0,d.c)(H??_),$=(0,d.c)(B),Q=function(e={}){let{onChange:t,precision:r,defaultValue:n,value:a,step:l=1,min:u=Number.MIN_SAFE_INTEGER,max:s=Number.MAX_SAFE_INTEGER,keepWithinRange:o=!0}=e,c=(0,d.c)(t),[p,N]=(0,i.useState)(()=>null==n?"":v(n,l,r)??""),x=void 0!==a,g=x?a:p,C=f(b(g),l),y=r??C,h=(0,i.useCallback)(e=>{e!==g&&(x||N(e.toString()),c?.(e.toString(),b(e)))},[c,x,g]),w=(0,i.useCallback)(e=>{var t;let r=e;return o&&(r=null==(t=r)?t:(s<u&&console.warn("clamp: max cannot be less than min"),Math.min(Math.max(t,u),s))),m(r,y)},[y,o,s,u]),k=(0,i.useCallback)((e=l)=>{let t;h(w(""===g?b(e):b(g)+e))},[w,l,h,g]),_=(0,i.useCallback)((e=l)=>{let t;h(w(""===g?b(-e):b(g)-e))},[w,l,h,g]),A=(0,i.useCallback)(()=>{let e;h(null==n?"":v(n,l,r)??u)},[n,r,l,h,u]),I=(0,i.useCallback)(e=>{h(v(e,l,y)??u)},[y,l,h,u]),S=b(g),R=S>s||S<u;return{isOutOfRange:R,isAtMax:S===s,isAtMin:S===u,precision:y,value:g,valueAsNumber:S,update:h,reset:A,increment:k,decrement:_,clamp:w,cast:I,setValue:N}}(e),{update:X,increment:Y,decrement:J}=Q,[W,Z]=(0,i.useState)(!1),ee=!(s||o),et=(0,i.useRef)(null),er=(0,i.useRef)(null),en=(0,i.useRef)(null),ea=(0,i.useRef)(null),el=(0,i.useCallback)(e=>e.split("").filter(V).join(""),[V]),eu=(0,i.useCallback)(e=>O?.(e)??e,[O]),ei=(0,i.useCallback)(e=>(G?.(e)??e).toString(),[G]);(0,N.w)(()=>{Q.valueAsNumber>l?z?.("rangeOverflow",ei(Q.value),Q.valueAsNumber):Q.valueAsNumber<a&&z?.("rangeOverflow",ei(Q.value),Q.valueAsNumber)},[Q.valueAsNumber,Q.value,ei,z]),(0,x.U)(()=>{if(et.current&&et.current.value!=Q.value){let e=eu(et.current.value);Q.setValue(el(e))}},[eu,el]);let es=(0,i.useCallback)((e=u)=>{ee&&Y(e)},[Y,ee,u]),eo=(0,i.useCallback)((e=u)=>{ee&&J(e)},[J,ee,u]),ec=function(e,t){let[r,n]=(0,i.useState)(!1),[a,l]=(0,i.useState)(null),[u,s]=(0,i.useState)(!0),o=(0,i.useRef)(null),c=()=>clearTimeout(o.current);!function(e,t){let r=(0,d.c)(e);(0,i.useEffect)(()=>{let e=null;return null!==t&&(e=window.setInterval(()=>r(),t)),()=>{e&&window.clearInterval(e)}},[t,r])}(()=>{"increment"===a&&e(),"decrement"===a&&t()},r?50:null);let m=(0,i.useCallback)(()=>{u&&e(),o.current=setTimeout(()=>{s(!1),n(!0),l("increment")},300)},[e,u]),p=(0,i.useCallback)(()=>{u&&t(),o.current=setTimeout(()=>{s(!1),n(!0),l("decrement")},300)},[t,u]),b=(0,i.useCallback)(()=>{s(!0),n(!1),c()},[]);return(0,i.useEffect)(()=>()=>c(),[]),{up:m,down:p,stop:b,isSpinning:r}}(es,eo);w(en,"disabled",ec.stop,ec.isSpinning),w(ea,"disabled",ec.stop,ec.isSpinning);let ed=(0,i.useCallback)(e=>{e.nativeEvent.isComposing||(X(el(eu(e.currentTarget.value))),er.current={start:e.currentTarget.selectionStart,end:e.currentTarget.selectionEnd})},[X,el,eu]),em=(0,i.useCallback)(e=>{L?.(e),er.current&&(e.currentTarget.selectionStart=er.current.start??e.currentTarget.value?.length,e.currentTarget.selectionEnd=er.current.end??e.currentTarget.selectionStart)},[L]),ep=(0,i.useCallback)(e=>{if(e.nativeEvent.isComposing)return;!function(e,t){if(null==e.key)return!0;let r=e.ctrlKey||e.altKey||e.metaKey;return 1!==e.key.length||!!r||t(e.key)}(e,V)&&e.preventDefault();let t=eb(e)*u,r={ArrowUp:()=>es(t),ArrowDown:()=>eo(t),Home:()=>X(a),End:()=>X(l)}[e.key];r&&(e.preventDefault(),r(e))},[V,u,es,eo,X,a,l]),eb=e=>{let t=1;return(e.metaKey||e.ctrlKey)&&(t=.1),e.shiftKey&&(t=10),t},ef=(0,i.useMemo)(()=>{let e=$?.(Q.value);return null!=e?e:Q.value.toString()||void 0},[Q.value,$]),ev=(0,i.useCallback)(()=>{let e=Q.value;""!==Q.value&&(/^[eE]/.test(Q.value.toString())?Q.setValue(""):(Q.valueAsNumber<a&&(e=a),Q.valueAsNumber>l&&(e=l),Q.cast(e)))},[Q,l,a]),eN=(0,i.useCallback)(()=>{Z(!1),r&&ev()},[r,Z,ev]),ex=(0,i.useCallback)(()=>{t&&requestAnimationFrame(()=>{et.current?.focus()})},[t]),eg=(0,i.useCallback)(e=>{e.preventDefault(),ec.up(),ex()},[ex,ec]),eC=(0,i.useCallback)(e=>{e.preventDefault(),ec.down(),ex()},[ex,ec]);(0,g.M)(()=>et.current,"wheel",e=>{let t=(et.current?.ownerDocument??document).activeElement===et.current;if(!I||!t)return;e.preventDefault();let r=eb(e)*u,n=Math.sign(e.deltaY);-1===n?es(r):1===n&&eo(r)},{passive:!1});let ey=(0,i.useCallback)((e={},t=null)=>{let r=o||n&&Q.isAtMax;return{...e,ref:(0,C.Px)(t,en),role:"button",tabIndex:-1,onPointerDown:(0,y.H)(e.onPointerDown,e=>{0!==e.button||r||eg(e)}),onPointerLeave:(0,y.H)(e.onPointerLeave,ec.stop),onPointerUp:(0,y.H)(e.onPointerUp,ec.stop),disabled:r,"aria-disabled":(0,h.r)(r)}},[Q.isAtMax,n,eg,ec.stop,o]),eh=(0,i.useCallback)((e={},t=null)=>{let r=o||n&&Q.isAtMin;return{...e,ref:(0,C.Px)(t,ea),role:"button",tabIndex:-1,onPointerDown:(0,y.H)(e.onPointerDown,e=>{0!==e.button||r||eC(e)}),onPointerLeave:(0,y.H)(e.onPointerLeave,ec.stop),onPointerUp:(0,y.H)(e.onPointerUp,ec.stop),disabled:r,"aria-disabled":(0,h.r)(r)}},[Q.isAtMin,n,eC,ec.stop,o]),ew=(0,i.useCallback)((e={},t=null)=>({name:E,inputMode:A,type:"text",pattern:k,"aria-labelledby":T,"aria-label":P,"aria-describedby":j,id:S,disabled:o,role:"spinbutton",...e,readOnly:e.readOnly??s,"aria-readonly":e.readOnly??s,"aria-required":e.required??c,required:e.required??c,ref:(0,C.Px)(et,t),value:ei(Q.value),"aria-valuemin":a,"aria-valuemax":l,"aria-valuenow":Number.isNaN(Q.valueAsNumber)?void 0:Q.valueAsNumber,"aria-invalid":(0,h.r)(p??Q.isOutOfRange),"aria-valuetext":ef,autoComplete:"off",autoCorrect:"off",onChange:(0,y.H)(e.onChange,ed),onKeyDown:(0,y.H)(e.onKeyDown,ep),onFocus:(0,y.H)(e.onFocus,em,()=>Z(!0)),onBlur:(0,y.H)(e.onBlur,U,eN)}),[E,A,k,T,P,ei,j,S,o,c,s,p,Q.value,Q.valueAsNumber,Q.isOutOfRange,a,l,ef,ed,ep,em,U,eN]);return{value:ei(Q.value),valueAsNumber:Q.valueAsNumber,isFocused:W,isDisabled:o,isReadOnly:s,getIncrementButtonProps:ey,getDecrementButtonProps:eh,getInputProps:ew,htmlProps:K}}((0,A.v)(l)),c=(0,i.useMemo)(()=>o,[o]);return(0,n.jsx)(j,{value:c,children:(0,n.jsx)(M,{value:r,children:(0,n.jsx)(S.B.div,{...s,ref:t,className:(0,u.cx)("chakra-numberinput",e.className),__css:{position:"relative",zIndex:0,...r.root}})})})});T.displayName="NumberInput";let D=(0,I.R)(function(e,t){let r=E();return(0,n.jsx)(S.B.div,{"aria-hidden":!0,ref:t,...e,__css:{display:"flex",flexDirection:"column",position:"absolute",top:"0",insetEnd:"0px",margin:"1px",height:"calc(100% - 2px)",zIndex:1,...r.stepperGroup}})});D.displayName="NumberInputStepper";let F=(0,I.R)(function(e,t){let{getInputProps:r}=P(),a=r(e,t),l=E();return(0,n.jsx)(S.B.input,{...a,className:(0,u.cx)("chakra-numberinput__field",e.className),__css:{width:"100%",...l.field}})});F.displayName="NumberInputField";let q=(0,S.B)("div",{baseStyle:{display:"flex",justifyContent:"center",alignItems:"center",flex:1,transitionProperty:"common",transitionDuration:"normal",userSelect:"none",cursor:"pointer",lineHeight:"normal"}}),B=(0,I.R)(function(e,t){let r=E(),{getDecrementButtonProps:a}=P(),l=a(e,t);return(0,n.jsx)(q,{...l,__css:r.stepper,children:e.children??(0,n.jsx)(o,{})})});B.displayName="NumberDecrementStepper";let H=(0,I.R)(function(e,t){let{getIncrementButtonProps:r}=P(),a=r(e,t),l=E();return(0,n.jsx)(q,{...a,__css:l.stepper,children:e.children??(0,n.jsx)(c,{})})});H.displayName="NumberIncrementStepper"},63730:(e,t,r)=>{r.d(t,{l:()=>c});var n=r(94513),a=r(75387),l=r(22697),u=r(40443),i=r(2923),s=r(56915),o=r(33225);let c=(0,i.R)(function(e,t){let r=(0,s.V)("FormLabel",e),i=(0,a.M)(e),{className:c,children:m,requiredIndicator:p=(0,n.jsx)(d,{}),optionalIndicator:b=null,...f}=i,v=(0,u.Uc)(),N=v?.getLabelProps(f,t)??{ref:t,...f};return(0,n.jsxs)(o.B.label,{...N,className:(0,l.cx)("chakra-form__label",i.className),__css:{display:"block",textAlign:"start",...r},children:[m,v?.isRequired?p:b]})});c.displayName="FormLabel";let d=(0,i.R)(function(e,t){let r=(0,u.Uc)(),a=(0,u.TP)();if(!r?.isRequired)return null;let i=(0,l.cx)("chakra-form__required-indicator",e.className);return(0,n.jsx)(o.B.span,{...r?.getRequiredIndicatorProps(e,t),__css:a.requiredIndicator,className:i})});d.displayName="RequiredIndicator"},64057:(e,t,r)=>{r.d(t,{p:()=>c});var n=r(94513),a=r(75387),l=r(22697),u=r(44637),i=r(2923),s=r(56915),o=r(33225);let c=(0,i.R)(function(e,t){let{htmlSize:r,...i}=e,c=(0,s.o)("Input",i),d=(0,a.M)(i),m=(0,u.t)(d),p=(0,l.cx)("chakra-input",e.className);return(0,n.jsx)(o.B.input,{size:r,...m,__css:c.field,ref:t,className:p})});c.displayName="Input",c.id="Input"}}]);