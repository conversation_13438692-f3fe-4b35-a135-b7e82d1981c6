import { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON>er, ChatInputCommandInteraction, PermissionFlagsBits, ChannelType, TextChannel, Collection } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('purge')
  .setDescription('Bulk delete messages in a channel')
  .addIntegerOption(option =>
    option.setName('amount')
      .setDescription('Number of messages to delete (2-100)')
      .setRequired(true)
      .setMinValue(2)
      .setMaxValue(100))
  .addChannelOption(option =>
    option.setName('channel')
      .setDescription('Channel to purge (defaults to current)')
      .addChannelTypes(ChannelType.GuildText)
      .setRequired(false))
  .addUserOption(option =>
    option.setName('user')
      .setDescription('Member to filter by (you can also paste a user ID)')
      .setRequired(false))
  .addBooleanOption(option =>
    option.setName('include_bots')
      .setDescription('Include bot messages')
      .setRequired(false))
  .addBooleanOption(option =>
    option.setName('images_only')
      .setDescription('Only delete messages with images/attachments')
      .setRequired(false))
  .addBooleanOption(option =>
    option.setName('all_channels')
      .setDescription('Purge messages across **all** text channels')
      .setRequired(false))
  .setDefaultMemberPermissions(PermissionFlagsBits.ManageMessages);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const amount = interaction.options.getInteger('amount', true);
  const channelOption = interaction.options.getChannel('channel');
  const channel = (channelOption ?? interaction.channel) as TextChannel | null;

  const targetUser = interaction.options.getUser('user');
  const includeBots = interaction.options.getBoolean('include_bots') ?? false;
  const imagesOnly = interaction.options.getBoolean('images_only') ?? false;
  const allChannels = interaction.options.getBoolean('all_channels') ?? false;

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ManageMessages)) {
    await interaction.reply({ content: '❌ You do not have permission to manage messages.', ephemeral: true });
    return;
  }

  if (!allChannels && (!channel || channel.type !== ChannelType.GuildText)) {
    await interaction.reply({ content: '❌ Invalid channel specified.', ephemeral: true });
    return;
  }

  // Confirmation buttons
  const { ActionRowBuilder, ButtonBuilder, ButtonStyle, ComponentType, Collection } = await import('discord.js');

  const confirmBtn = new ButtonBuilder()
    .setCustomId('purge_confirm')
    .setLabel('Confirm Purge')
    .setStyle(ButtonStyle.Danger);

  const cancelBtn = new ButtonBuilder()
    .setCustomId('purge_cancel')
    .setLabel('Cancel')
    .setStyle(ButtonStyle.Secondary);

  const row = new ActionRowBuilder().addComponents(confirmBtn, cancelBtn);

  let previewMsg = `⚠️ Purge **${amount}** messages in ${channel} ?`;
  const filters: string[] = [];
  if (targetUser) filters.push(`user ${targetUser.tag}`);
  if (!includeBots) filters.push('excluding bot messages');
  if (imagesOnly) filters.push('only images');
  if (filters.length) previewMsg += `\n• Filters: ${filters.join(', ')}`;
  if (allChannels) previewMsg += `\n• Scope: **ALL channels**`;

  await interaction.reply({
    content: previewMsg,
    components: [row as any],
    ephemeral: true,
  });

  try {
    const buttonInteraction = await interaction.fetchReply().then((msg: any) =>
      msg.awaitMessageComponent({
        componentType: ComponentType.Button,
        filter: (i: any) => i.user.id === interaction.user.id,
        time: 15000,
      })
    );

    if (buttonInteraction.customId === 'purge_cancel') {
      await buttonInteraction.update({ content: '❌ Purge cancelled.', components: [] });
      return;
    }

    // Setup progress embed
    const { EmbedBuilder } = await import('discord.js');
    const embed = new EmbedBuilder()
      .setTitle('Purge in progress')
      .setDescription('Starting...');

    // Defer the button update to acknowledge interaction and remove buttons
    await buttonInteraction.deferUpdate();
    await interaction.editReply({ embeds: [embed], components: [] });

    // Helper to update progress embed
    const updateProgress = async (desc: string) => {
      embed.setDescription(desc);
      await interaction.editReply({ embeds: [embed] });
    };

    const textChannels: Collection<string, TextChannel> = allChannels
      ? (interaction.guild?.channels.cache.filter((c): c is TextChannel => c.type === ChannelType.GuildText) as Collection<string, TextChannel> ?? new Collection())
      : new Collection<string, TextChannel>().set(channel!.id, channel as TextChannel);

    let totalDeleted = 0;

    for (const [, ch] of textChannels) {
      let messages = await ch.messages.fetch({ limit: 100 });

      if (!includeBots) messages = messages.filter((m: any) => !m.author.bot);
      if (targetUser) messages = messages.filter((m: any) => m.author.id === targetUser.id);
      if (imagesOnly) messages = messages.filter((m: any) => m.attachments.size > 0 || (m.embeds as any[]).some((e: any) => e.image));

      const toDelete = new Collection<string, any>();
      for (const [, msg] of messages) {
        if (toDelete.size >= amount) break;
        toDelete.set(msg.id, msg);
      }

      if (toDelete.size > 0) {
        await ch.bulkDelete(toDelete, true).catch(() => {});
        totalDeleted += toDelete.size;
      }

      await updateProgress(`Processed ${ch} • Deleted so far: **${totalDeleted}**`);
    }

    await updateProgress(`✅ Completed purge. Total deleted: **${totalDeleted}** messages across ${allChannels ? textChannels.size : 1} channel(s).`);
  } catch {
    await interaction.editReply({ content: '⌛ Purge timed out or no response.', components: [] });
  }
}

export const cooldown = 5000; 