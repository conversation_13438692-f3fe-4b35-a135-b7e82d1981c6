// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import { MongoClient, ObjectId } from 'mongodb';
import { dashboardConfig } from '../../../../../core/config';

const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;
async function connect() {
  if (!cachedClient) cachedClient = await MongoClient.connect(mongoUrl);
  return cachedClient;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) return res.status(401).json({ error: 'Unauthorized' });

  const { id } = req.query;
  if (!id || Array.isArray(id)) return res.status(400).json({ error: 'Invalid ticket id' });

  const { token: botToken } = dashboardConfig.bot;

  let client: MongoClient;
  try { client = await connect(); } catch { return res.status(500).json({ error: 'DB connect failed' }); }
  const tickets = client.db(dbName).collection('tickets');
  const ticket = await tickets.findOne({ _id: new ObjectId(id) });
  if (!ticket) return res.status(404).json({ error: 'Ticket not found' });

  const isAdmin = (session.user as any).isAdmin;
  const isCreator = ticket.creatorId === (session.user as any).id;
  if (!isAdmin && !isCreator) return res.status(403).json({ error: 'Forbidden' });

  // If transcript already saved in DB serve it
  if (ticket.transcriptHtml) {
    // Check if it's a properly formatted HTML transcript
    if (ticket.transcriptHtml.includes('<style') || ticket.transcriptHtml.includes('<!DOCTYPE html>')) {
      res.setHeader('Content-Type', 'text/html');
      return res.status(200).send(ticket.transcriptHtml);
    }
  }

  // Otherwise fetch messages live (fallback for tickets without saved transcripts)
  let allMessages: any[] = [];
  let before: string | undefined = undefined;
  try {
    while (true) {
      const url = `https://discord.com/api/v10/channels/${ticket.channelId}/messages?limit=100${before ? `&before=${before}` : ''}`;
      const resp = await fetch(url, { headers: { Authorization: `Bot ${botToken}` } });
      if (!resp.ok) {
        if (resp.status === 404) {
          throw new Error('Channel not found - ticket channel may have been deleted');
        } else if (resp.status === 403) {
          throw new Error('Access denied - bot may not have permission to read channel history');
        }
        throw new Error(`Discord API error: ${resp.status} ${resp.statusText}`);
      }
      const batch = await resp.json();
      allMessages = allMessages.concat(batch);
      if (batch.length < 100) break;
      before = batch[batch.length - 1].id;
    }
  } catch (err: any) {
    console.error('Discord message fetch error:', err);
    return res.status(500).json({ 
      error: 'Failed to fetch messages', 
      details: err.message || 'Unknown error occurred while fetching Discord messages',
      suggestion: 'The ticket channel may have been deleted. Transcripts should be saved when tickets are closed.'
    });
  }

  // oldest first
  allMessages.reverse();

  // Get unique participants
  const participants = [...new Set(allMessages.map(m => m.author.id))].map(id => {
    const msg = allMessages.find(m => m.author.id === id);
    return {
      id,
      username: msg?.author.username || 'Unknown User',
      avatar: msg?.author.avatar 
        ? `https://cdn.discordapp.com/avatars/${id}/${msg.author.avatar}.png?size=32`
        : `https://cdn.discordapp.com/embed/avatars/${parseInt(msg.author.discriminator || '0') % 5}.png`,
      role: id === ticket.creatorId ? 'Creator' : 'Staff'
    };
  });

  // Generate the participants HTML
  const participantsHtml = participants.map(p => `
    <div class="participant">
      <img src="${p.avatar}" alt="${p.username}'s avatar"/>
      <div>
        <div class="participant-name">${p.username}</div>
        <div class="participant-role">${p.role}</div>
      </div>
    </div>
  `).join('');

  // Convert user mentions to usernames
  async function resolveUserMention(userId: string): Promise<string> {
    try {
      const resp = await fetch(`https://discord.com/api/v10/users/${userId}`, {
        headers: { Authorization: `Bot ${botToken}` }
      });
      if (!resp.ok) return `<@${userId}>`;
      const user = await resp.json();
      return `@${user.username}`;
    } catch {
      return `<@${userId}>`;
    }
  }

  async function attachmentToImg(att: any) {
    // Non-image attachments – link out directly
    if (!att.content_type?.startsWith('image/')) {
      return `<a href="${att.url}" target="_blank">${att.filename || att.url}</a>`;
    }

    // Always embed GIFs or images larger than 1 MB via the remote URL to preserve animation/avoid bloat
    if (att.content_type === 'image/gif' || att.size > 1024 * 1024) {
      return `<img class="attach" src="${att.url}" />`;
    }

    // Inline small static images as data URIs for portability
    try {
      const buf = await (await fetch(att.url)).arrayBuffer();
      const base64 = Buffer.from(buf).toString('base64');
      return `<img class="attach" src="data:${att.content_type};base64,${base64}" />`;
    } catch {
      return `<img class="attach" src="${att.url}" />`;
    }
  }

  const rowsPromises = allMessages.map(async (m) => {
    const t = new Date(m.timestamp).toLocaleString();
    const avatar = m.author.avatar
      ? `https://cdn.discordapp.com/avatars/${m.author.id}/${m.author.avatar}.png?size=32`
      : `https://cdn.discordapp.com/embed/avatars/${parseInt(m.author.discriminator || '0') % 5}.png`;
    
    let content = (m.content || '').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\n/g, '<br/>');

    // Convert user mentions
    const mentionRegex = /<@!?(\d+)>/g;
    const mentions = content.match(mentionRegex);
    if (mentions) {
      for (const mention of mentions) {
        const userId = mention.match(/\d+/)?.[0];
        if (userId) {
          const username = await resolveUserMention(userId);
          content = content.replace(mention, username);
        }
      }
    }

    // Handle initial ticket message with embed
    if (m === allMessages[0] && m.embeds?.length > 0) {
      const embed = m.embeds[0];
      content = `
<div class="ticket-embed">
  <div class="ticket-embed-header">
    <span class="ticket-embed-icon">📝</span>
    <div class="ticket-embed-title">${embed.title || 'Support Ticket'}</div>
  </div>
  ${embed.fields?.map((f: any) => `
    <div class="ticket-embed-field">
      <div class="ticket-embed-label">${f.name}</div>
      <div class="ticket-embed-value">${f.value}</div>
    </div>
  `).join('') || ''}
  ${embed.description ? `<div class="ticket-embed-description">${embed.description}</div>` : ''}
</div>`;
    }

    const emojiRegex = /<a?:([\w]+):(\d+)>/g;
    content = await replaceAsync(content, emojiRegex, async (_match, _name, emId) => {
      const ext = _match.startsWith('<a:') ? 'gif' : 'png';
      const url = `https://cdn.discordapp.com/emojis/${emId}.${ext}`;
      // Don't convert GIFs to data URI to preserve animation
      if (ext === 'gif') {
        return `<img class="emoji" src="${url}" />`;
      }
      const dataUri = await fetchToDataUri(url, `image/${ext}`);
      return `<img class="emoji" src="${dataUri}" />`;
    });

    // --- Embed Tenor links ---
    const tenorRegex = /(https?:\/\/tenor\.com\/view\/[^\s]+)/g;
    content = await replaceAsync(content, tenorRegex, async (link: string) => {
      const idMatch = link.match(/-(\d+)(?:\?.*)?$/);
      if (!idMatch) return '';

      const tenorId = idMatch[1];
      try {
        const apiResp = await fetch(`https://g.tenor.com/v1/gifs?ids=${tenorId}&key=LIVDSRZULELA`);
        const apiJson: any = await apiResp.json();
        if (!apiJson?.results?.length) throw 0;

        const media = apiJson.results[0].media_formats || apiJson.results[0].media?.[0];
        const preferredOrder = ['mediumgif','gif','tinygif','nanogif'];
        let chosenUrl: string | undefined;
        for (const key of preferredOrder) {
          if (media?.[key]?.url) { chosenUrl = media[key].url; break; }
        }
        if (!chosenUrl) throw 0;

        return chosenUrl;
      } catch {
        return '';
      }
    });

    // Convert bare image/gif links to embedded images
    const urlRegex = /(https?:\/\/\S+\.(?:png|jpe?g|gif|webp))/gi;
    content = await replaceAsync(content, urlRegex, async (link: string) => {
      return link;
    });

    // Attachments
    let attachHtml = '';
    if (m.attachments?.length) {
      const imgs = await Promise.all(m.attachments.map(attachmentToImg));
      attachHtml = imgs.join('');
    }

    return `<div class="msg">
      <img class="avatar" src="${avatar}"/>
      <div class="bubble">
        <div class="meta">
          <span class="name">${m.author.username}</span>
          <span class="time">${t}</span>
        </div>
        <div class="content">${content}${attachHtml}</div>
      </div>
    </div>`;
  });

  const rows = (await Promise.all(rowsPromises)).join('\n');

  const badgeColor = category => ({support:'#3b82f6', '18plus':'#ef4444', other:'#a855f7'}[category]||'#6b7280');

  const transcriptHtml = `<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${id} Transcript</title>
<style>
/* Reset and base styles */
*{box-sizing:border-box;margin:0;padding:0}
:root {
  --bg-primary: #0f172a;
  --bg-secondary: rgba(255,255,255,0.08);
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  --accent-blue: #3b82f6;
  --accent-purple: #a855f7;
  --accent-red: #ef4444;
}

/* Main layout */
body{
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  display: grid;
  grid-template-columns: 280px 1fr;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(29,78,216,.15), transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(60,46,123,.15), transparent 50%);
}

/* Sidebar */
.sidebar {
  background: var(--bg-secondary);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255,255,255,0.1);
  padding: 24px;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 32px;
}

.sidebar-section h3 {
  color: var(--text-secondary);
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Main content */
.main {
  padding: 40px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* Header */
.header {
  margin-bottom: 32px;
  text-align: center;
}

h1 {
  font-size: 32px;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16px;
}

/* Support details */
.support-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.detail-item {
  text-align: center;
  padding: 12px;
  background: rgba(255,255,255,0.02);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: rgba(255,255,255,0.04);
  transform: translateY(-2px);
}

.detail-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-weight: 500;
}

.detail-value {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 600;
}

/* Messages */
.messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.msg {
  display: flex;
  gap: 16px;
  animation: fadeIn 0.3s ease;
  padding: 4px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.msg:hover {
  background: rgba(255,255,255,0.03);
}

.avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.msg:hover .avatar {
  transform: scale(1.05);
}

.bubble {
  background: rgba(255,255,255,0.04);
  padding: 16px;
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.06);
  max-width: calc(100% - 58px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.msg:hover .bubble {
  border-color: rgba(255,255,255,0.1);
  background: rgba(255,255,255,0.05);
}

.meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name {
  font-size: 15px;
  font-weight: 500;
  color: #60a5fa;
}

.time {
  font-size: 12px;
  color: var(--text-secondary);
}

.content {
  font-size: 15px;
  line-height: 1.5;
  color: var(--text-primary);
  word-break: break-word;
}

/* Attachments */
.attach {
  display: block;
  margin-top: 12px;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: transform 0.2s ease;
}

.attach:hover {
  transform: scale(1.02);
}

.emoji {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  margin: 0 2px;
  transition: transform 0.15s ease;
  cursor: pointer;
}

.emoji:hover {
  transform: scale(1.2);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Category badge */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 99px;
  font-size: 13px;
  font-weight: 500;
  background: var(--badge-bg);
  color: white;
  margin-bottom: 24px;
}

/* Ticket embed */
.ticket-embed {
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px;
  padding: 24px;
  margin-top: 12px;
  transition: all 0.2s ease;
}

.ticket-embed:hover {
  background: rgba(255,255,255,0.04);
  border-color: rgba(255,255,255,0.15);
  transform: translateY(-2px);
}

.ticket-embed-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.ticket-embed-icon {
  font-size: 24px;
}

.ticket-embed-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--accent-blue);
  background: linear-gradient(135deg, #60a5fa 0%, #818cf8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.ticket-embed-field {
  margin-bottom: 12px;
  padding-left: 28px;
}

.ticket-embed-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.ticket-embed-value {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
}

.ticket-embed-description {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255,255,255,0.1);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  padding-left: 28px;
}

/* Participants */
.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}
</style></head><body>
  <div class="sidebar">
    <div class="sidebar-section">
      <h3>Participants</h3>
      ${participantsHtml}
    </div>
    <div class="sidebar-section">
      <h3>Support Info</h3>
      <div class="detail-item">
        <div class="detail-label">Status</div>
        <div class="detail-value">Closed</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Category</div>
        <div class="detail-value">${ticket.category || 'Support'}</div>
      </div>
    </div>
  </div>

  <div class="main">
    <div class="header">
      <h1>Ticket Transcript</h1>
      <span class="badge" style="--badge-bg:${badgeColor(ticket.category || 'other')}">${ticket.category || 'Support'}</span>
    </div>

    <div class="support-details">
      <div class="detail-item">
        <div class="detail-label">Ticket ID</div>
        <div class="detail-value">${id}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Created</div>
        <div class="detail-value">${new Date(ticket.createdAt).toLocaleString()}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Closed</div>
        <div class="detail-value">${ticket.closedAt ? new Date(ticket.closedAt).toLocaleString() : 'N/A'}</div>
      </div>
    </div>

    <div class="messages">
      ${rows}
    </div>
  </div>
</body></html>`;

  // update DB with styled transcript for future requests
  try { await tickets.updateOne({ _id: new ObjectId(id) }, { $set: { transcriptHtml: transcriptHtml } }); } catch {}

  res.setHeader('Content-Type', 'text/html');
  res.status(200).send(transcriptHtml);
}

// helpers
function replaceAsync(str: string, regex: RegExp, asyncFn: (...args:any[])=>Promise<string>): Promise<string> {
  const promises: Promise<string>[] = [];
  str.replace(regex, (...args) => {
    const promise = asyncFn(...args);
    promises.push(promise);
    return args[0];
  });
  return Promise.all(promises).then(results => {
    return str.replace(regex, () => results.shift() as string);
  });
}

async function fetchToDataUri(url: string, mimeGuess: string): Promise<string> {
  try {
    const res = await fetch(url);
    const buf = await res.arrayBuffer();
    if (buf.byteLength > 1024*1024) return url; // too big
    const base64 = Buffer.from(buf).toString('base64');
    return `data:${mimeGuess};base64,${base64}`;
  } catch { return url; }
} 