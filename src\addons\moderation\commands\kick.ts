import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('kick')
  .setDescription('Kick a member from the server')
  .addUserOption(option =>
    option.setName('user')
      .setDescription('The user to kick')
      .setRequired(true))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for the kick')
      .setRequired(false))
  .addBooleanOption(option =>
    option.setName('dm')
      .setDescription('DM the user with the kick reason')
      .setRequired(false))
  .setDefaultMemberPermissions(PermissionFlagsBits.KickMembers);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const targetUser = interaction.options.getUser('user', true);
  const reason = interaction.options.getString('reason') ?? 'No reason provided';
  const dmUser = interaction.options.getBoolean('dm') ?? false;

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.KickMembers)) {
    await interaction.reply({ content: '❌ You do not have permission to kick members.', ephemeral: true });
    return;
  }

  const member = await interaction.guild?.members.fetch(targetUser.id).catch(() => null);
  if (!member) {
    await interaction.reply({ content: '❌ Could not find that member in this guild.', ephemeral: true });
    return;
  }

  if (!member.kickable) {
    await interaction.reply({ content: '❌ I cannot kick this user. They may have higher permissions or roles than me.', ephemeral: true });
    return;
  }

  // Confirmation buttons
  const { ActionRowBuilder, ButtonBuilder, ButtonStyle, ComponentType } = await import('discord.js');
  const confirmBtn = new ButtonBuilder()
    .setCustomId('kick_confirm')
    .setLabel('Confirm Kick')
    .setStyle(ButtonStyle.Danger);
  const cancelBtn = new ButtonBuilder()
    .setCustomId('kick_cancel')
    .setLabel('Cancel')
    .setStyle(ButtonStyle.Secondary);
  const row = new ActionRowBuilder().addComponents(confirmBtn, cancelBtn);

  await interaction.reply({
    content: `⚠️ Are you sure you want to kick **${targetUser.tag}**?`,
    components: [row as any],
    ephemeral: true,
  });

  try {
    const buttonInteraction = await interaction.fetchReply().then((msg: any) =>
      msg.awaitMessageComponent({
        componentType: ComponentType.Button,
        filter: (i: any) => i.user.id === interaction.user.id,
        time: 15000,
      })
    );

    if (buttonInteraction.customId === 'kick_cancel') {
      await buttonInteraction.update({ content: '❌ Kick cancelled.', components: [] });
      return;
    }

    // Proceed with kick
    if (dmUser) {
      await targetUser.send(`You have been kicked from **${interaction.guild?.name}** | Reason: ${reason}`).catch(() => {});
    }

    await member.kick(reason);

    await buttonInteraction.update({
      content: `👢 Kicked **${targetUser.tag}** | Reason: ${reason}`,
      components: [],
    });
  } catch {
    await interaction.editReply({ content: '⌛ Kick timed out or no response.', components: [] });
  }
}

export const cooldown = 5000; 