"use strict";exports.id=5333,exports.ids=[5333],exports.modules={1011:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>d});var a=o(8732),n=o(9733),s=o(3427),i=o(6287),l=o(3001),c=e([n,s,i,l]);[n,s,i,l]=c.then?(await c)():c;let d=({children:e})=>{let{currentScheme:r}=(0,l.DP)();return(0,a.jsx)(n.Box,{minH:"100vh",bg:r.colors.background,position:"relative",overflow:"hidden",_before:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,bgImage:`
          radial-gradient(circle at 15% 50%, ${r.colors.primary}15 0%, transparent 25%),
          radial-gradient(circle at 85% 30%, ${r.colors.accent}15 0%, transparent 25%)
        `,zIndex:0},_after:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,backdropFilter:"blur(100px)",zIndex:0},children:(0,a.jsxs)(n.Box,{position:"relative",zIndex:1,display:"flex",flexDirection:"column",minH:"100vh",children:[(0,a.jsx)(n.Box,{position:"fixed",top:0,left:0,right:0,zIndex:30,children:(0,a.jsx)(s.A,{})}),(0,a.jsxs)(n.Box,{display:"flex",flex:"1",position:"relative",pt:"4rem",children:[(0,a.jsx)(n.Box,{position:"fixed",top:"4rem",bottom:0,left:0,w:"64",zIndex:20,children:(0,a.jsx)(i.A,{})}),(0,a.jsx)(n.Box,{flex:"1",ml:"64",p:{base:4,md:8},maxW:"100%",transition:"all 0.3s",position:"relative",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)",pointerEvents:"none",zIndex:-1},children:(0,a.jsx)(n.Container,{maxW:"container.xl",children:e})})]})]})})};t()}catch(e){t(e)}})},2087:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>d});var a=o(8732),n=o(9733),s=o(8079),i=o(2015),l=o(4722),c=e([n]);function d(){let{data:e}=(0,l.useSession)(),[r]=(0,i.useState)([]);return e?.user?(0,a.jsxs)(n.Popover,{placement:"bottom-end",children:[(0,a.jsx)(n.PopoverTrigger,{children:(0,a.jsxs)(n.Box,{position:"relative",children:[(0,a.jsx)(n.Tooltip,{label:"Notifications",placement:"bottom",children:(0,a.jsx)(n.IconButton,{"aria-label":"Notifications",icon:(0,a.jsx)(s.zd,{}),variant:"ghost",size:"sm",color:"gray.300",_hover:{bg:"whiteAlpha.200",color:"white",transform:"scale(1.05)"},transition:"all 0.2s"})}),!1]})}),(0,a.jsxs)(n.PopoverContent,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"2xl",maxW:"400px",_focus:{boxShadow:"2xl"},children:[(0,a.jsx)(n.PopoverHeader,{borderBottomColor:"whiteAlpha.200",fontWeight:"semibold",fontSize:"lg",color:"white",children:"Notifications"}),(0,a.jsx)(n.PopoverBody,{maxH:"400px",overflowY:"auto",children:(0,a.jsx)(n.VStack,{spacing:0,align:"stretch",children:r&&0!==r.length?(r||[]).map(e=>(0,a.jsxs)(n.Box,{p:3,borderBottom:"1px",borderColor:"whiteAlpha.100",children:[(0,a.jsx)(n.Text,{fontSize:"sm",color:"white",fontWeight:"medium",children:e.title}),(0,a.jsx)(n.Text,{fontSize:"xs",color:"gray.400",mt:1,children:e.message})]},e.id)):(0,a.jsx)(n.Box,{py:8,textAlign:"center",children:(0,a.jsx)(n.Text,{color:"gray.400",fontSize:"sm",children:"No notifications yet"})})})})]})]}):null}n=(c.then?(await c)():c)[0],t()}catch(e){t(e)}})},3001:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{DP:()=>p,NP:()=>h,nk:()=>c});var a=o(8732),n=o(2015),s=o(9733),i=o(6390),l=e([s,i]);[s,i]=l.then?(await l)():l;let c=[{id:"default",name:"Default Purple",description:"Classic purple and blue gradient theme",colors:{primary:"#8b5cf6",primaryLight:"#a78bfa",primaryDark:"#7c3aed",secondary:"#5865F2",accent:"#06b6d4",background:"#1a202c",surface:"rgba(255,255,255,0.03)",text:"#f7fafc",textSecondary:"#a0aec0",border:"rgba(255,255,255,0.2)",success:"#68d391",warning:"#fbb6ce",error:"#fc8181",info:"#63b3ed"}},{id:"ocean",name:"Ocean Blue",description:"Deep blue ocean-inspired theme",colors:{primary:"#0ea5e9",primaryLight:"#38bdf8",primaryDark:"#0284c7",secondary:"#06b6d4",accent:"#8b5cf6",background:"#0f172a",surface:"rgba(59, 130, 246, 0.05)",text:"#f1f5f9",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}},{id:"forest",name:"Forest Green",description:"Nature-inspired green theme",colors:{primary:"#059669",primaryLight:"#10b981",primaryDark:"#047857",secondary:"#065f46",accent:"#8b5cf6",background:"#0f1419",surface:"rgba(16, 185, 129, 0.05)",text:"#f0fdf4",textSecondary:"#86efac",border:"rgba(16, 185, 129, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"sunset",name:"Sunset Orange",description:"Warm sunset-inspired theme",colors:{primary:"#ea580c",primaryLight:"#fb923c",primaryDark:"#c2410c",secondary:"#dc2626",accent:"#8b5cf6",background:"#1c1917",surface:"rgba(251, 146, 60, 0.05)",text:"#fef7ed",textSecondary:"#fdba74",border:"rgba(251, 146, 60, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"rose",name:"Rose Pink",description:"Elegant rose and pink theme",colors:{primary:"#e11d48",primaryLight:"#f43f5e",primaryDark:"#be123c",secondary:"#ec4899",accent:"#8b5cf6",background:"#1f1720",surface:"rgba(244, 63, 94, 0.05)",text:"#fdf2f8",textSecondary:"#fda4af",border:"rgba(244, 63, 94, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"midnight",name:"Midnight Blue",description:"Dark midnight blue theme",colors:{primary:"#1e40af",primaryLight:"#3b82f6",primaryDark:"#1e3a8a",secondary:"#4338ca",accent:"#06b6d4",background:"#0c0a1f",surface:"rgba(59, 130, 246, 0.05)",text:"#f8fafc",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}}],d=(0,n.createContext)(void 0),p=()=>{let e=(0,n.useContext)(d);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},h=({children:e})=>{let[r,o]=(0,n.useState)(c[0]),[t,l]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("dashboard-color-scheme"),r=localStorage.getItem("dashboard-custom-schemes");if(r)try{let e=JSON.parse(r);l(e)}catch(e){}if(e){let r=c.find(r=>r.id===e);if(r)o(r);else{let r=localStorage.getItem("dashboard-custom-schemes");if(r)try{let t=JSON.parse(r).find(r=>r.id===e);t&&o(t)}catch(e){}}}},[]),(0,n.useEffect)(()=>{localStorage.setItem("dashboard-color-scheme",r.id)},[r]),(0,n.useEffect)(()=>{localStorage.setItem("dashboard-custom-schemes",JSON.stringify(t))},[t]);let p=[...c,...t],h=(0,s.extendTheme)({...i.A,colors:{...i.A.colors,brand:{50:r.colors.primaryLight+"20",100:r.colors.primaryLight+"40",200:r.colors.primaryLight+"60",300:r.colors.primaryLight+"80",400:r.colors.primaryLight,500:r.colors.primary,600:r.colors.primaryDark,700:r.colors.primaryDark+"CC",800:r.colors.primaryDark+"AA",900:r.colors.primaryDark+"88"},custom:{primary:r.colors.primary,primaryLight:r.colors.primaryLight,primaryDark:r.colors.primaryDark,secondary:r.colors.secondary,accent:r.colors.accent,background:r.colors.background,surface:r.colors.surface,text:r.colors.text,textSecondary:r.colors.textSecondary,border:r.colors.border,success:r.colors.success,warning:r.colors.warning,error:r.colors.error,info:r.colors.info}},styles:{global:{body:{bg:r.colors.background,color:r.colors.text}}}});return(0,a.jsx)(d.Provider,{value:{currentScheme:r,setColorScheme:e=>{let r=c.find(r=>r.id===e);if(r)return void o(r);let a=t.find(r=>r.id===e);a&&o(a)},colorSchemes:p,customSchemes:t,addCustomScheme:e=>{l(r=>[...r.filter(r=>r.id!==e.id),e]),o(e)},deleteCustomScheme:e=>{l(r=>r.filter(r=>r.id!==e)),r.id===e&&o(c[0])},resetToDefault:()=>{o(c[0])}},children:(0,a.jsx)(s.ChakraProvider,{theme:h,children:e})})};t()}catch(e){t(e)}})},3427:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>p});var a=o(8732),n=o(9733),s=o(4722),i=o(8079),l=o(4715),c=o(2087),d=e([n,l,c]);function p(){let{data:e}=(0,s.useSession)(),{displayName:r}=(0,l.A)(),o=r?`${r} Dashboard`:"Bot Dashboard";return(0,a.jsx)(n.Box,{px:6,py:2,bg:"rgba(255,255,255,0.05)",backdropFilter:"blur(20px)",borderBottom:"1px solid",borderColor:"whiteAlpha.200",position:"sticky",top:0,zIndex:1e3,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))",zIndex:-1},children:(0,a.jsxs)(n.Flex,{h:16,alignItems:"center",justifyContent:"space-between",children:[(0,a.jsx)(n.Box,{flex:"1",children:(0,a.jsx)(n.Heading,{as:"h1",fontSize:"xl",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",_hover:{bgGradient:"linear(to-r, blue.300, purple.300)",transform:"scale(1.02)"},transition:"all 0.2s",children:o})}),(0,a.jsx)(n.Box,{flex:"1",display:"flex",justifyContent:"flex-end",children:(0,a.jsx)(n.Flex,{alignItems:"center",gap:4,children:e?.user?(0,a.jsxs)(n.HStack,{spacing:4,children:[(0,a.jsx)(c.A,{}),(0,a.jsxs)(n.Menu,{children:[(0,a.jsx)(n.MenuButton,{as:n.Button,variant:"ghost",size:"sm",px:2,py:1,borderRadius:"full",_hover:{bg:"whiteAlpha.200"},children:(0,a.jsxs)(n.HStack,{spacing:2,children:[(0,a.jsx)(n.Avatar,{size:"sm",name:e.user.name??void 0,src:e.user.image??void 0,borderWidth:2,borderColor:"blue.400",_hover:{borderColor:"purple.400",transform:"scale(1.05)"},transition:"all 0.2s"}),(0,a.jsx)(n.Text,{color:"gray.300",display:{base:"none",md:"block"},children:e.user.name})]})}),(0,a.jsx)(n.MenuList,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"lg",_hover:{borderColor:"blue.400"},children:(0,a.jsx)(n.MenuItem,{icon:(0,a.jsx)(i.QeK,{}),onClick:()=>(0,s.signOut)(),_hover:{bg:"whiteAlpha.200",color:"red.400"},children:"Sign out"})})]})]}):(0,a.jsx)(n.Button,{onClick:()=>(0,s.signIn)("discord",{callbackUrl:"/overview"}),bgGradient:"linear(to-r, blue.500, purple.500)",color:"white",_hover:{bgGradient:"linear(to-r, blue.400, purple.400)",transform:"translateY(-1px)"},_active:{bgGradient:"linear(to-r, blue.600, purple.600)",transform:"translateY(1px)"},transition:"all 0.2s",children:"Login with Discord"})})})]})})}[n,l,c]=d.then?(await d)():d,t()}catch(e){t(e)}})},3567:(e,r,o)=>{o.r(r),o.d(r,{default:()=>n});var t=o(8732),a=o(8270);function n(){return(0,t.jsxs)(a.Html,{lang:"en",children:[(0,t.jsx)(a.Head,{}),(0,t.jsxs)("body",{children:[(0,t.jsx)(a.Main,{}),(0,t.jsx)(a.NextScript,{})]})]})}},4715:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>l});var a=o(4078),n=o(2015),s=o(4722),i=e([a]);a=(i.then?(await i)():i)[0];let c=async e=>{let r=await fetch(e);if(!r.ok){if(401===r.status)return{name:"404 Bot",botName:"404 Bot"};throw Error("Failed to fetch guild info")}return r.json()};function l(){let{data:e,status:r}=(0,s.useSession)(),o="authenticated"===r,{data:t,error:i}=(0,a.default)(o?"/api/discord/guild":null,c,{revalidateOnFocus:!1,revalidateOnReconnect:!1}),[l,d]=(0,n.useState)(()=>"guild"),p=(0,n.useCallback)(e=>{d(e)},[]),h="404 Bot Dashboard",x=h;return t&&(x="bot"===l&&t.botName?t.botName:t.name||h),{guild:t,displayName:x,pref:l,updatePreference:p,isLoading:o&&!i&&!t,isError:!!i}}t()}catch(e){t(e)}})},6287:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>m});var a=o(8732),n=o(9733),s=o(8079),i=o(6281),l=o.n(i),c=o(4722),d=o(8358),p=o(2015),h=o(4715),x=o(3001),f=e([n,h,x]);[n,h,x]=f.then?(await f)():f;let g="1.0.0";function m(){let{data:e}=(0,c.useSession)(),r=(0,d.useRouter)(),o=e?.user?.isAdmin;e?.user?.id;let[t,i]=(0,p.useState)(!1),[f,m]=(0,p.useState)(!1),[b,u]=(0,p.useState)(!1),[y,j]=(0,p.useState)([]),{displayName:v}=(0,h.A)(),{currentScheme:S}=(0,x.DP)(),k=[{name:"Overview",icon:s.V5Y,href:"/overview"},{name:"Applications",icon:s.est,href:"/applications"},{name:"Tickets",icon:s.lrG,href:"/tickets"},{name:"Game Servers",icon:s.ufi,href:"/gameservers"}],w=[{name:"Server Management",href:"/admin/guilds",icon:s.VSk},{name:"Addons",href:"/admin/addons",icon:s.X3y},{name:"Errors",href:"/admin/errors",icon:s.y3G}],C=e=>"/overview"===e?r.pathname===e:r.pathname.startsWith(e);return(0,a.jsxs)(n.Box,{as:"nav",h:"100%",bg:S.colors.surface,backdropFilter:"blur(20px)",borderRight:"1px solid",borderColor:S.colors.border,py:8,display:"flex",flexDirection:"column",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:`linear-gradient(180deg, ${S.colors.primary}15 0%, ${S.colors.accent}15 100%)`,zIndex:-1},children:[(0,a.jsxs)(n.VStack,{spacing:2,align:"stretch",flex:"1",children:[k.map(e=>{let r=C(e.href);return"Applications"===e.name?(0,a.jsxs)(n.VStack,{spacing:0,align:"stretch",children:[(0,a.jsxs)(n.Box,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:f||C(e.href)?S.colors.text:S.colors.textSecondary,bg:C(e.href)?`${S.colors.primary}30`:"transparent",_hover:{bg:C(e.href)?`${S.colors.primary}40`:S.colors.surface,color:S.colors.text,transform:"translateX(4px)"},_active:{bg:`${S.colors.primary}50`},borderRight:C(e.href)?"2px solid":"none",borderColor:C(e.href)?S.colors.primary:"transparent",transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>m(!f),children:[(0,a.jsx)(n.Icon,{as:e.icon,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(n.Text,{display:{base:"none",lg:"block"},bgGradient:C(e.href)?`linear(to-r, ${S.colors.primaryLight}, ${S.colors.accent})`:"none",bgClip:C(e.href)?"text":"none",transition:"all 0.2s",flex:"1",children:e.name}),(0,a.jsx)(n.Icon,{as:s.fK4,w:4,h:4,ml:2,transition:"all 0.2s",transform:f?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,a.jsx)(n.Collapse,{in:f,animateOpacity:!0,children:(0,a.jsx)(n.VStack,{spacing:1,align:"stretch",pl:4,py:2,children:y.length>0?y.map(e=>(0,a.jsx)(n.Link,{as:l(),href:`/applications/${e.id}`,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:C(`/applications/${e.id}`)?S.colors.text:S.colors.textSecondary,bg:C(`/applications/${e.id}`)?`${S.colors.primary}20`:"transparent",_hover:{bg:S.colors.surface,color:S.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:(0,a.jsx)(n.Text,{display:{base:"none",lg:"block"},children:e.title})},e.id)):(0,a.jsx)(n.Text,{px:4,py:2,fontSize:"xs",color:S.colors.textSecondary,display:{base:"none",lg:"block"},children:"No open applications"})})})]},e.name):(0,a.jsx)(n.Tooltip,{label:e.name,placement:"right",hasArrow:!0,gutter:20,openDelay:500,display:{base:"block","2xl":"none"},children:(0,a.jsxs)(n.Link,{as:l(),href:e.href,display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:r?S.colors.text:S.colors.textSecondary,bg:r?`${S.colors.primary}30`:"transparent",_hover:{bg:r?`${S.colors.primary}40`:S.colors.surface,color:S.colors.text,transform:"translateX(4px)"},_active:{bg:`${S.colors.primary}50`},borderRight:r?"2px solid":"none",borderColor:r?S.colors.primary:"transparent",transition:"all 0.2s",role:"group",children:[(0,a.jsx)(n.Icon,{as:e.icon,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(n.Text,{display:{base:"none",lg:"block"},bgGradient:r?`linear(to-r, ${S.colors.primaryLight}, ${S.colors.accent})`:"none",bgClip:r?"text":"none",transition:"all 0.2s",children:e.name}),"Applications"===e.name&&!b&&(0,a.jsx)(n.Badge,{ml:2,colorScheme:"red",variant:"solid",fontSize:"xs",display:{base:"none",lg:"block"},children:"Closed"}),"Applications"===e.name&&y.length>0&&(0,a.jsxs)(n.Badge,{ml:2,colorScheme:"green",variant:"solid",fontSize:"xs",display:{base:"none",lg:"block"},children:[y.length," Open"]})]})},e.name)}),o&&(0,a.jsxs)(n.VStack,{spacing:0,align:"stretch",children:[(0,a.jsxs)(n.Box,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:S.colors.textSecondary,bg:"transparent",_hover:{bg:S.colors.surface,color:S.colors.text,transform:"translateX(4px)"},transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>i(!t),children:[(0,a.jsx)(n.Icon,{as:s.LIi,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(n.Text,{display:{base:"none",lg:"block"},transition:"all 0.2s",flex:"1",children:"Admin"}),(0,a.jsx)(n.Icon,{as:s.fK4,w:4,h:4,ml:2,transition:"all 0.2s",transform:t?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,a.jsx)(n.Collapse,{in:t,animateOpacity:!0,children:(0,a.jsx)(n.VStack,{spacing:1,align:"stretch",pl:4,py:2,children:w.map(e=>(0,a.jsxs)(n.Link,{as:l(),href:e.href,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:C(e.href)?S.colors.text:S.colors.textSecondary,bg:C(e.href)?`${S.colors.primary}20`:"transparent",_hover:{bg:S.colors.surface,color:S.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:[(0,a.jsx)(n.Icon,{as:e.icon,w:4,h:4,mr:2,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,a.jsx)(n.Text,{display:{base:"none",lg:"block"},children:e.name})]},e.href))})})]})]}),(0,a.jsxs)(n.Box,{px:4,pt:4,mt:"auto",children:[(0,a.jsx)(n.Divider,{borderColor:S.colors.border,mb:4}),(0,a.jsx)(n.Text,{fontSize:"xs",color:S.colors.textSecondary,textAlign:"center",bgGradient:`linear(to-r, ${S.colors.primaryLight}, ${S.colors.accent})`,bgClip:"text",opacity:.7,_hover:{opacity:1,transform:"scale(1.05)"},transition:"all 0.2s",children:v?`${v} v${g}`:`Bot v${g}`})]})]})}t()}catch(e){t(e)}})},6390:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{A:()=>s});var a=o(9733),n=e([a]);a=(n.then?(await n)():n)[0];let s=(0,a.extendTheme)({config:{initialColorMode:"dark",useSystemColorMode:!1},fonts:{heading:"'Inter', sans-serif",body:"'Inter', sans-serif"},colors:{brand:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},discord:{50:"#e8e9fd",100:"#d1d3fc",200:"#b9bcfa",300:"#a2a5f9",400:"#8b8ef7",500:"#5865F2",600:"#4752c4",700:"#363f97",800:"#242c69",900:"#12193c"}},styles:{global:{body:{bg:"gray.900",color:"gray.100"}}},components:{Button:{defaultProps:{colorScheme:"brand"},variants:{solid:e=>({bg:`${e.colorScheme}.500`,color:"white",_hover:{bg:`${e.colorScheme}.600`,transform:"translateY(-2px)",boxShadow:"lg"},_active:{bg:`${e.colorScheme}.700`,transform:"translateY(0)"},transition:"all 0.2s ease"})}},Link:{baseStyle:{_hover:{textDecoration:"none"}}},Box:{baseStyle:{transition:"all 0.2s ease"}}}});t()}catch(e){t(e)}})},8077:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.r(r),o.d(r,{default:()=>p});var a=o(8732),n=o(4722),s=o(4959),i=o.n(s),l=o(3001),c=e([l]);function d({Component:e,pageProps:r}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(i(),{children:[(0,a.jsx)("title",{children:"404 Bot Dashboard"}),(0,a.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,a.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,a.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,a.jsx)("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,a.jsx)(e,{...r})]})}function p({Component:e,pageProps:{session:r,...o}}){return(0,a.jsx)(n.SessionProvider,{session:r,children:(0,a.jsx)(l.NP,{children:(0,a.jsx)(d,{Component:e,pageProps:o})})})}l=(c.then?(await c)():c)[0],t()}catch(e){t(e)}})}};