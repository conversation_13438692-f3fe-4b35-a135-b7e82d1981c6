import { DatabaseManager } from './DatabaseManager.js';
import { ConfigManager } from './ConfigManager.js';
import type { BotConfig } from '../types/index.js';

const defaultSettings = {
  welcome: {
    enabled: false,
    channelId: null,
    message: 'Welcome {user} to {guild}! You are the {memberCount}th member.',
    autoRoles: [],
    embedColor: '#00FF00',
  },
  goodbye: {
    enabled: false,
    channelId: null,
    message: 'Goodbye {user}! We will miss you.',
    embedColor: '#FF0000',
  },
};

export class WelcomeGoodbyeManager {
  private dbManager: DatabaseManager;
  private config: BotConfig;

  constructor() {
    this.config = ConfigManager.getConfig();
    this.dbManager = new DatabaseManager(this.config);
  }

  async getSettings(guildId: string) {
    await this.dbManager.connect();
    const guildConfig = await this.dbManager.findOne('guild_configs', { guildId });
    return {
      welcome: { ...defaultSettings.welcome, ...guildConfig?.welcome },
      goodbye: { ...defaultSettings.goodbye, ...guildConfig?.goodbye },
    };
  }

  async saveSettings(guildId: string, welcome: any, goodbye: any) {
    await this.dbManager.connect();
    const updateData = {
      guildId,
      welcome: {
        enabled: welcome.enabled,
        channelId: welcome.channelId || null,
        message: welcome.message || defaultSettings.welcome.message,
        autoRoles: welcome.autoRoles || [],
        embedColor: welcome.embedColor || defaultSettings.welcome.embedColor,
      },
      goodbye: {
        enabled: goodbye.enabled,
        channelId: goodbye.channelId || null,
        message: goodbye.message || defaultSettings.goodbye.message,
        embedColor: goodbye.embedColor || defaultSettings.goodbye.embedColor,
      },
    };

    await this.dbManager.updateOne(
      'guild_configs',
      { guildId },
      { $set: updateData },
      { upsert: true }
    );
  }
} 