"use strict";(()=>{var e={};e.id=7194,e.ids=[7194],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},799:(e,t,o)=>{o.r(t),o.d(t,{config:()=>g,default:()=>b,routeModule:()=>y});var s={};o.r(s),o.d(s,{default:()=>h});var n=o(3433),i=o(264),r=o(584),a=o(5806),d=o(8525),c=o(9021),l=o.n(c),u=o(3873),f=o.n(u),m=o(2115),p=o.n(m);async function h(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});let o=await (0,a.getServerSession)(e,t,d.authOptions);if(!o)return t.status(401).json({error:"Unauthorized"});if(!o.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});try{let e=function(){let e=process.cwd().includes("dashboard")?f().resolve(process.cwd(),"..",".."):process.cwd(),t=[f().join(e,"config.yml"),f().join(e,"404-bot","config.yml")].find(e=>l().existsSync(e));if(!t)throw Error("config.yml not found");return t}(),o=l().readFileSync(e,"utf8"),s=p().parse(o),n=function(){let e=process.cwd().includes("dashboard")?f().resolve(process.cwd(),"..",".."):process.cwd(),t=[f().join(e,"src","addons"),f().join(e,"404-bot","src","addons"),f().join(e,"dist","addons"),f().join(e,"404-bot","dist","addons")],o=[];t.forEach((e,t)=>{l().existsSync(e)&&o.push(e)});let s=f().join(e,"src","addons"),n=f().join(e,"dist","addons");if(l().existsSync(s)&&!o.includes(s)&&o.push(s),l().existsSync(n)&&!o.includes(n)&&o.push(n),0===o.length)throw Error("No addons directories found");return Array.from(new Set(o))}(),i=new Map;n.forEach(e=>{l().readdirSync(e,{withFileTypes:!0}).filter(e=>e.isDirectory()).map(e=>e.name).forEach(t=>{let o=f().join(e,t);i.has(t)||i.set(t,{name:t,fullPath:o})})});let r=Array.from(i.values());r.forEach(({name:e,fullPath:t})=>{l().readdirSync(t)});let a=[],d=[];return r.forEach(({name:e,fullPath:t})=>{let o={name:e,version:"1.0.0",description:"No description available",author:"Unknown"},n=!1;try{let e=f().join(t,"config.yml");if(l().existsSync(e)){let t=l().readFileSync(e,"utf8"),s=p().parse(t),i=s?.addon||s;i&&(o={...o,name:i.name??o.name,version:i.version??o.version,description:i.description??o.description,author:i.author??o.author},n="Addon Builder"===i.author||"Generated addon from visual builder"===i.description||i.description?.includes("Generated addon from visual builder"))}}catch(e){}if(!n){let e=f().join(t,"flow.json");l().existsSync(e)&&(n=!0)}try{let e=f().join(t,"index.ts");if(l().existsSync(e)){let t=l().readFileSync(e,"utf8");if(!n){let e=t.includes("Generated addon from visual builder"),o=t.includes("author: 'Addon Builder'");n=e&&o}let s=t.match(/info:\s*{([\s\S]*?)}/m);if(s){let e=s[1],t=e.match(/version:\s*['"]([^'\"]*)['"]/),n=e.match(/description:\s*['"]([^'\"]*)['"]/),i=e.match(/author:\s*['"]([^'\"]*)['"]/);t&&(o.version=t[1]),n&&(!o.description||"No description available"===o.description)&&(o.description=n[1]),i&&(!o.author||"Unknown"===o.author)&&(o.author=i[1])}}}catch(e){}let i=s.addons?.enabled&&(!s.addons.disabled||!s.addons.disabled.includes(e)),r=l().existsSync(f().join(t,"config.yml")),c={...o,enabled:i,hasConfig:r,isCustomAddon:n,config:s.addons?.configs?.[e]||{},status:"active"};n?d.push(c):a.push(c)}),t.status(200).json({builtInAddons:a,customAddons:d,addons:[...a,...d]})}catch(e){return t.status(500).json({error:"Internal server error",details:e.message})}}let b=(0,r.M)(s,"default"),g=(0,r.M)(s,"config"),y=new n.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/admin/addons",pathname:"/api/admin/addons",bundlePath:"",filename:""},userland:s})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>c});var s=o(5542),n=o.n(s);let i=require("next-auth/providers/discord");var r=o.n(i),a=o(8580);let d={providers:[r()({clientId:a.dashboardConfig.bot.clientId,clientSecret:a.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,s=t.accessToken||null;e.user.id=o,e.user.accessToken=s;let n=!1;if(o)if((a.dashboardConfig.dashboard.admins||[]).includes(o))n=!0;else{let e=a.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&a.dashboardConfig.bot.token&&a.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${a.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${a.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();n=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=n,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),s=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:a.dashboardConfig.dashboard.session.secret||a.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},c=n()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>c});var s=o(9021),n=o(2115),i=o.n(n),r=o(3873);let a={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");a=i().parse(t)}catch(e){process.exit(1)}let d={bot:{token:a.bot.token,clientId:a.bot.clientId,clientSecret:a.bot.clientSecret,guildId:a.bot.guildId,ticketCategoryId:a.bot.ticketCategoryId||null,ticketLogChannelId:a.bot.ticketLogChannelId||null,prefix:a.bot.prefix},dashboard:{admins:a.dashboard?.admins||[],adminRoleIds:a.dashboard?.adminRoleIds||[],session:{secret:a.dashboard?.session?.secret||a.bot.clientSecret}},database:{url:a.database.url,name:a.database.name,options:{maxPoolSize:a.database.options?.maxPoolSize||10,minPoolSize:a.database.options?.minPoolSize||1,maxIdleTimeMS:a.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:a.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:a.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:a.database.options?.connectTimeoutMS||1e4,retryWrites:a.database.options?.retryWrites!==!1,retryReads:a.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let c=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=799);module.exports=o})();