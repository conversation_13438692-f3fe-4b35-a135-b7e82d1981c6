(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6178],{25149:(e,o,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/commands",function(){return r(38968)}])},38968:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>O});var a=r(94513),n=r(68443),t=r(59818),s=r(79156),l=r(78902),i=r(49217),c=r(31678),d=r(71601),h=r(41611),m=r(55631),g=r(62690),u=r(95845),p=r(22907),x=r(59001),b=r(19521),j=r(51961),f=r(6159),w=r(26282),y=r(64057),C=r(25964),S=r(98703),E=r(25680),v=r(7746),k=r(5095),_=r(7680),z=r(47847),A=r(85104),T=r(28245),D=r(94285),I=r(60341),L=r(97146);let F={moderation:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"}},example:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"}},tickets:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"}},"voice-mistress":{color:"pink",gradient:{from:"rgba(237, 137, 179, 0.4)",to:"rgba(237, 137, 179, 0.1)"}},"welcome-goodbye":{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"}},Unknown:{color:"gray",gradient:{from:"rgba(113, 128, 150, 0.4)",to:"rgba(113, 128, 150, 0.1)"}}},N=e=>{let{command:o,onDelete:r,onToggle:u}=e,p=F[o.addon]||F.Unknown,[x,b]=(0,D.useState)(!1),j=async()=>{b(!0),await u(o,!o.enabled),b(!1)};return(0,a.jsx)(n.Z,{bg:"linear-gradient(135deg, ".concat(p.gradient.from,", ").concat(p.gradient.to,")"),backdropFilter:"blur(10px)",borderWidth:2,borderColor:"".concat(p.color,".400"),rounded:"xl",overflow:"hidden",transition:"all 0.2s",opacity:o.enabled?1:.7,_hover:{transform:"translateY(-2px)",boxShadow:"0 4px 20px ".concat(p.gradient.from)},children:(0,a.jsx)(t.b,{children:(0,a.jsxs)(s.T,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.z,{justify:"space-between",children:[(0,a.jsxs)(l.z,{children:[(0,a.jsx)(i.I,{as:L.JSe,color:"".concat(p.color,".400"),boxSize:5}),(0,a.jsxs)(c.D,{size:"md",color:"white",children:["/",o.name]})]}),(0,a.jsxs)(s.T,{spacing:1,align:"end",children:[(0,a.jsx)(d.E,{colorScheme:p.color,size:"sm",children:o.category}),(0,a.jsx)(d.E,{variant:"outline",colorScheme:"gray",size:"sm",children:o.scope})]})]}),(0,a.jsx)(h.E,{color:"gray.300",fontSize:"sm",noOfLines:2,children:o.description}),(0,a.jsxs)(h.E,{color:"gray.400",fontSize:"xs",children:["ID: ",o.id]}),(0,a.jsxs)(l.z,{justify:"space-between",children:[(0,a.jsx)(m.d,{isChecked:o.enabled,onChange:j,isDisabled:x,colorScheme:p.color}),(0,a.jsx)(g.$,{size:"sm",leftIcon:(0,a.jsx)(i.I,{as:L.IXo}),variant:"ghost",colorScheme:p.color,onClick:()=>r(o),children:"Remove"})]})]})})})};function O(){let[e,o]=(0,D.useState)([]),[r,n]=(0,D.useState)([]),[t,l]=(0,D.useState)(""),[m,O]=(0,D.useState)("all"),[R,W]=(0,D.useState)(!0),[U,$]=(0,D.useState)(!1),[P,X]=(0,D.useState)(null),{isOpen:B,onOpen:J,onClose:K}=(0,u.j)(),M=(0,D.useRef)(null),Z=(0,p.d)(),G=Array.from(new Set(e.map(e=>e.category))).sort();(0,D.useEffect)(()=>{let o=e;t.trim()&&(o=o.filter(e=>e.name.toLowerCase().includes(t.toLowerCase())||e.description.toLowerCase().includes(t.toLowerCase())||e.category.toLowerCase().includes(t.toLowerCase()))),"all"!==m&&(o=o.filter(e=>e.category===m)),n(o)},[e,t,m]);let Q=async()=>{try{W(!0);let e=await fetch("/api/admin/commands");if(e.ok){let r=await e.json();o(r)}else throw Error("Failed to fetch commands")}catch(e){Z({title:"Error",description:e.message||"Failed to fetch commands",status:"error",duration:5e3})}finally{W(!1)}};(0,D.useEffect)(()=>{Q()},[]);let Y=async()=>{$(!0),await Q(),$(!1)},q=async e=>{X(e),J()},H=async(r,a)=>{try{if(!(await fetch("/api/admin/commands",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({commandId:r.id,enabled:a})})).ok)throw Error("Failed to update command state");o(e.map(e=>e.id===r.id?{...e,enabled:a}:e)),Z({title:"Success",description:"Command /".concat(r.name," has been ").concat(a?"enabled":"disabled"),status:"success",duration:3e3})}catch(e){Z({title:"Error",description:e.message||"Failed to update command state",status:"error",duration:5e3})}},V=async()=>{if(P)try{if(!(await fetch("/api/admin/commands?commandId=".concat(P.id,"&scope=").concat(P.scope),{method:"DELETE"})).ok)throw Error("Failed to delete command");o(e.filter(e=>e.id!==P.id)),Z({title:"Success",description:"Command /".concat(P.name," has been removed"),status:"success",duration:3e3})}catch(e){Z({title:"Error",description:e.message||"Failed to delete command",status:"error",duration:5e3})}finally{K(),X(null)}};return(0,a.jsx)(I.A,{children:(0,a.jsxs)(x.m,{maxW:"7xl",py:6,children:[(0,a.jsxs)(s.T,{spacing:6,align:"stretch",children:[(0,a.jsxs)(b.s,{direction:{base:"column",lg:"row"},justify:"space-between",align:{base:"start",lg:"center"},gap:4,children:[(0,a.jsxs)(j.a,{children:[(0,a.jsx)(c.D,{size:"lg",mb:2,bgGradient:"linear(to-r, pink.500, purple.500)",bgClip:"text",children:"Bot Commands"}),(0,a.jsxs)(h.E,{color:"gray.400",children:["Manage your Discord bot's slash commands (",r.length," of ",e.length,")"]})]}),(0,a.jsx)(g.$,{leftIcon:(0,a.jsx)(i.I,{as:L.jTZ}),colorScheme:"purple",variant:"outline",onClick:Y,isLoading:U,children:"Refresh"})]}),(0,a.jsxs)(b.s,{direction:{base:"column",md:"row"},gap:4,children:[(0,a.jsxs)(f.M,{flex:1,children:[(0,a.jsx)(w.W,{pointerEvents:"none",children:(0,a.jsx)(i.I,{as:L.CKj,color:"gray.400"})}),(0,a.jsx)(y.p,{placeholder:"Search commands...",value:t,onChange:e=>l(e.target.value),bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",_hover:{borderColor:"whiteAlpha.300"},_focus:{borderColor:"purple.400",boxShadow:"0 0 0 1px var(--chakra-colors-purple-400)"}})]}),(0,a.jsxs)(C.l,{value:m,onChange:e=>O(e.target.value),w:{base:"full",md:"200px"},bg:"whiteAlpha.50",border:"1px solid",borderColor:"whiteAlpha.200",_hover:{borderColor:"whiteAlpha.300"},_focus:{borderColor:"purple.400",boxShadow:"0 0 0 1px var(--chakra-colors-purple-400)"},children:[(0,a.jsx)("option",{value:"all",children:"All Categories"}),G.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsx)(S.B,{spacing:3,children:G.map(o=>{let r=e.filter(e=>e.category===o).length,n=F[o.toLowerCase()]||F.Unknown;return(0,a.jsx)(S.Q,{children:(0,a.jsxs)(d.E,{colorScheme:n.color,variant:m===o?"solid":"outline",cursor:"pointer",onClick:()=>O(m===o?"all":o),px:3,py:1,rounded:"full",children:[o," (",r,")"]})},o)})}),R?(0,a.jsx)(E.r,{columns:{base:1,md:2,lg:3},spacing:6,children:[1,2,3].map(e=>(0,a.jsx)(v.E,{height:"200px",rounded:"xl"},e))}):(0,a.jsx)(E.r,{columns:{base:1,md:2,lg:3},spacing:6,children:r.map(e=>(0,a.jsx)(N,{command:e,onDelete:q,onToggle:H},e.id))}),!R&&0===r.length&&(0,a.jsxs)(j.a,{textAlign:"center",py:12,children:[(0,a.jsx)(i.I,{as:L.K7R,boxSize:12,color:"gray.400",mb:4}),(0,a.jsx)(c.D,{size:"md",color:"gray.400",mb:2,children:"No commands found"}),(0,a.jsx)(h.E,{color:"gray.500",children:"Try adjusting your search or category filter"})]})]}),(0,a.jsx)(k.Lt,{isOpen:B,leastDestructiveRef:M,onClose:K,children:(0,a.jsx)(_.m,{children:(0,a.jsxs)(k.EO,{bg:"gray.800",borderColor:"whiteAlpha.200",borderWidth:1,children:[(0,a.jsx)(z.r,{fontSize:"lg",fontWeight:"bold",children:"Delete Command"}),(0,a.jsxs)(A.c,{children:["Are you sure you want to remove the command /",null==P?void 0:P.name,"? This action cannot be undone."]}),(0,a.jsxs)(T.j,{children:[(0,a.jsx)(g.$,{ref:M,onClick:K,children:"Cancel"}),(0,a.jsx)(g.$,{colorScheme:"red",onClick:V,ml:3,children:"Delete"})]})]})})})]})})}}},e=>{var o=o=>e(e.s=o);e.O(0,[4108,9998,4976,217,2965,3177,8920,341,636,6593,8792],()=>o(25149)),_N_E=e.O()}]);