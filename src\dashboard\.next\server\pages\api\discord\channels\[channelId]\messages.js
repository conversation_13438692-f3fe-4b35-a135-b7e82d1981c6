"use strict";(()=>{var e={};e.id=4604,e.ids=[4604],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var s=o(5542),r=o.n(s);let n=require("next-auth/providers/discord");var a=o.n(n),i=o(8580);let d={providers:[a()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,s=t.accessToken||null;e.user.id=o,e.user.accessToken=s;let r=!1;if(o)if((i.dashboardConfig.dashboard.admins||[]).includes(o))r=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();r=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),s=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=r()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var s=o(9021),r=o(2115),n=o.n(r),a=o(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");i=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},8920:(e,t,o)=>{o.r(t),o.d(t,{config:()=>f,default:()=>u,routeModule:()=>m});var s={};o.r(s),o.d(s,{default:()=>c});var r=o(3433),n=o(264),a=o(584),i=o(5806),d=o(8525),l=o(8580);async function c(e,t){try{let o=await (0,i.getServerSession)(e,t,d.authOptions);if(!o?.user)return t.status(401).json({error:"Unauthorized"});let{guildId:s,token:r}=l.dashboardConfig.bot;if(!r||!s)return t.status(500).json({error:"Bot configuration missing"});let{channelId:n}=e.query;if(!n||"string"!=typeof n)return t.status(400).json({error:"Channel ID is required"});if("POST"===e.method)try{let{content:o,embeds:s,components:a,flags:i}=e.body;if(!o&&(!s||0===s.length)&&(!a||0===a.length))return t.status(400).json({error:"Message must contain content, embeds, or components"});s?.length>0&&s.forEach(e=>{e.color&&"string"==typeof e.color&&e.color.startsWith("#")&&(e.color=parseInt(e.color.replace("#",""),16)),Object.keys(e).forEach(t=>{(""===e[t]||null===e[t]||void 0===e[t])&&delete e[t]}),Array.isArray(e.fields)&&0===e.fields.length&&delete e.fields});let d={};o&&(d.content=o),s?.length>0&&(d.embeds=s),a?.length>0&&(d.components=a),"number"==typeof i&&(d.flags=i);let l=await fetch(`https://discord.com/api/v10/channels/${n}/messages`,{method:"POST",headers:{Authorization:`Bot ${r}`,"Content-Type":"application/json"},body:JSON.stringify(d)});if(!l.ok){let e;try{e=await l.json()}catch{e=await l.text()}return t.status(l.status).json(e)}let c=await l.json();return t.status(200).json(c)}catch(e){return t.status(500).json({error:"Failed to send message"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let u=(0,a.M)(s,"default"),f=(0,a.M)(s,"config"),m=new r.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/discord/channels/[channelId]/messages",pathname:"/api/discord/channels/[channelId]/messages",bundlePath:"",filename:""},userland:s})},9021:e=>{e.exports=require("fs")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=8920);module.exports=o})();