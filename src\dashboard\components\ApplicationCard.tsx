import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Icon,
  Button,
  Tooltip,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  List,
  ListItem,
  ListIcon,
} from '@chakra-ui/react';
import { FiClock, FiUsers, FiCheckCircle, FiAlertCircle, FiCalendar } from 'react-icons/fi';
import * as Icons from 'react-icons/fi';
import { ApplicationType } from '../types/applications';

interface ApplicationCardProps {
  application: ApplicationType;
  onApply: (appType: ApplicationType) => void;
  hasApplied?: boolean;
}

export const ApplicationCard: React.FC<ApplicationCardProps> = ({
  application,
  onApply,
  hasApplied = false,
}) => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const IconComponent = Icons[application.icon as keyof typeof Icons];

  return (
    <>
      <Box
        bg={application.gradient ? `linear-gradient(135deg, ${application.gradient.from}, ${application.gradient.to})` : 'gray.800'}
        borderRadius="xl"
        border="1px solid"
        borderColor={application.isOpen ? `${application.color}.400` : 'gray.600'}
        p={6}
        position="relative"
        transition="all 0.3s"
        opacity={application.isOpen ? 1 : 0.7}
        _hover={{
          transform: application.isOpen ? 'translateY(-2px)' : 'none',
          boxShadow: application.isOpen ? '0 4px 12px rgba(0,0,0,0.2)' : 'none',
        }}
      >
        <VStack align="stretch" spacing={4}>
          <HStack justify="space-between">
            <HStack>
              <Icon
                as={IconComponent}
                boxSize={6}
                color={`${application.color}.300`}
              />
              <Text fontSize="xl" fontWeight="bold" color="white">
                {application.title}
              </Text>
            </HStack>
            <Badge
              colorScheme={application.isOpen ? 'green' : 'gray'}
              variant="solid"
              px={3}
              py={1}
              borderRadius="full"
            >
              {application.isOpen ? 'Open' : 'Closed'}
            </Badge>
          </HStack>

          <Text color="gray.300" noOfLines={2}>
            {application.description}
          </Text>

          <HStack spacing={4} color="gray.400" fontSize="sm">
            <Tooltip label="Average response time">
              <HStack>
                <Icon as={FiClock} />
                <Text>{application.metadata.averageResponseTime}</Text>
              </HStack>
            </Tooltip>
            <Tooltip label="Total applications">
              <HStack>
                <Icon as={FiUsers} />
                <Text>{application.metadata.totalApplications}</Text>
              </HStack>
            </Tooltip>
            <Tooltip label="Acceptance rate">
              <HStack>
                <Icon as={FiCheckCircle} />
                <Text>{application.metadata.acceptanceRate}%</Text>
              </HStack>
            </Tooltip>
          </HStack>

          <HStack justify="space-between" align="center">
            <Button
              variant="ghost"
              size="sm"
              onClick={onOpen}
              color={`${application.color}.300`}
              _hover={{
                bg: `${application.color}.900`,
                color: `${application.color}.200`,
              }}
            >
              View Details
            </Button>
            <Button
              colorScheme={application.color}
              isDisabled={!application.isOpen || hasApplied}
              onClick={() => onApply(application)}
            >
              {hasApplied ? 'Applied' : 'Apply Now'}
            </Button>
          </HStack>
        </VStack>
      </Box>

      <Modal isOpen={isOpen} onClose={onClose} size="lg">
        <ModalOverlay backdropFilter="blur(10px)" />
        <ModalContent bg="gray.800" border="1px" borderColor="whiteAlpha.200">
          <ModalHeader color="white">
            <HStack>
              <Icon as={IconComponent} color={`${application.color}.300`} />
              <Text>{application.title} Details</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack align="stretch" spacing={6}>
              <Box>
                <Text fontWeight="bold" color="gray.300" mb={2}>
                  Requirements
                </Text>
                <List spacing={2}>
                  {application.requirements?.minAge && (
                    <ListItem color="gray.400">
                      <ListIcon as={FiAlertCircle} color={`${application.color}.300`} />
                      Must be {application.requirements.minAge}+ years old
                    </ListItem>
                  )}
                  {application.requirements?.minAccountAge && (
                    <ListItem color="gray.400">
                      <ListIcon as={FiCalendar} color={`${application.color}.300`} />
                      Account must be at least {application.requirements.minAccountAge} days old
                    </ListItem>
                  )}
                  {application.requirements?.timezone && (
                    <ListItem color="gray.400">
                      <ListIcon as={FiClock} color={`${application.color}.300`} />
                      Must provide timezone information
                    </ListItem>
                  )}
                  {application.requirements?.availability && (
                    <ListItem color="gray.400">
                      <ListIcon as={FiUsers} color={`${application.color}.300`} />
                      Must specify availability hours
                    </ListItem>
                  )}
                </List>
              </Box>

              <Box>
                <Text fontWeight="bold" color="gray.300" mb={2}>
                  Application Process
                </Text>
                <List spacing={2} color="gray.400">
                  <ListItem>
                    <ListIcon as={FiCheckCircle} color={`${application.color}.300`} />
                    Fill out the application form
                  </ListItem>
                  {application.requiresApproval && (
                    <ListItem>
                      <ListIcon as={FiClock} color={`${application.color}.300`} />
                      Wait for review ({application.metadata.averageResponseTime})
                    </ListItem>
                  )}
                </List>
              </Box>

              {!application.isOpen && application.metadata.nextReviewDate && (
                <Box bg={`${application.color}.900`} p={4} borderRadius="md">
                  <Text color="white">
                    Next application review period starts: {new Date(application.metadata.nextReviewDate).toLocaleDateString()}
                  </Text>
                </Box>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Close
            </Button>
            <Button
              colorScheme={application.color}
              isDisabled={!application.isOpen || hasApplied}
              onClick={() => {
                onClose();
                onApply(application);
              }}
            >
              {hasApplied ? 'Already Applied' : 'Apply Now'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
}; 