// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { dashboardConfig } from '../../../core/config';

// Discord channel type mapping
const CHANNEL_TYPES = {
  GUILD_TEXT: 0,
  GUILD_VOICE: 2,
  GUILD_CATEGORY: 4,
  GUILD_ANNOUNCEMENT: 5,
  GUILD_ANNOUNCEMENT_THREAD: 10,
  GUILD_PUBLIC_THREAD: 11,
  GUILD_PRIVATE_THREAD: 12,
  GUILD_STAGE_VOICE: 13,
  GUILD_FORUM: 15,
};

// Reverse mapping for type conversion
const CHANNEL_TYPE_NAMES = {
  0: 'GUILD_TEXT',
  2: 'GUILD_VOICE',
  4: 'GUILD_CATEGORY',
  5: 'GUILD_ANNOUNCEMENT',
  10: 'GUILD_ANNOUNCEMENT_THREAD',
  11: 'GUILD_PUBLIC_THREAD',
  12: 'GUILD_PRIVATE_THREAD',
  13: 'GUILD_STAGE_VOICE',
  15: 'GUILD_FORUM',
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // For sensitive operations like creating channels we still require admin.
    const isAdmin = (session.user as any).isAdmin;

    const { guildId, token } = dashboardConfig.bot;
    
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    if (req.method === 'GET') {
      try {
        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch channels');
        }

        const channels = await response.json();

        // Format channels based on the request source
        const formattedChannels = channels.map(channel => {
          // Base channel format
          const formattedChannel = {
            id: channel.id,
            name: channel.name,
            type: CHANNEL_TYPE_NAMES[channel.type] || 'unknown',
            position: channel.position,
          };

          // Add parent_id if it exists
          if (channel.parent_id) {
            formattedChannel.parent_id = channel.parent_id;
          }

          // Add raw type for the channel management page
          formattedChannel.raw_type = channel.type;

          // Add additional properties based on channel type
          if (channel.type === CHANNEL_TYPES.GUILD_TEXT) { // Text channel
            formattedChannel.topic = channel.topic;
            formattedChannel.nsfw = channel.nsfw;
            formattedChannel.rate_limit_per_user = channel.rate_limit_per_user;
          } else if (channel.type === CHANNEL_TYPES.GUILD_VOICE) { // Voice channel
            formattedChannel.bitrate = channel.bitrate;
            formattedChannel.user_limit = channel.user_limit;
          }

          return formattedChannel;
        });

        return res.status(200).json(formattedChannels);
      } catch (error) {
        console.error('Error fetching channels:', error);
        return res.status(500).json({ error: 'Failed to fetch channels' });
      }
    }

    if (req.method === 'POST') {
      // Creating channels requires admin permissions
      if (!isAdmin) {
        return res.status(403).json({ error: 'Forbidden - Admin access required' });
      }

      try {
        const {
          name,
          type,
          topic,
          nsfw,
          bitrate,
          userLimit,
          parent,
          position,
          rateLimitPerUser,
        } = req.body;

        console.log('Received channel creation request:', req.body);

        // Validate required fields
        if (!name || (typeof type !== 'number' && typeof type !== 'string')) {
          console.log('Validation failed:', { name, type });
          return res.status(400).json({ error: 'Name and type are required' });
        }

        // Convert string type to numeric if needed
        let numericType = typeof type === 'number' ? type : CHANNEL_TYPES[type];
        console.log('Channel type conversion:', { original: type, converted: numericType });
        if (typeof numericType !== 'number') {
          return res.status(400).json({ error: 'Invalid channel type' });
        }

        // Prepare channel data based on type
        const channelData: any = {
          name,
          type: numericType,
          position: position || 0,
        };

        // Add type-specific properties
        if (numericType === CHANNEL_TYPES.GUILD_TEXT) {
          if (topic) channelData.topic = topic;
          channelData.nsfw = Boolean(nsfw);
          if (typeof rateLimitPerUser === 'number' && !isNaN(rateLimitPerUser)) {
            channelData.rate_limit_per_user = rateLimitPerUser;
          }
        } else if (numericType === CHANNEL_TYPES.GUILD_VOICE) {
          if (typeof bitrate === 'number' && !isNaN(bitrate)) {
            channelData.bitrate = bitrate;
          }
          if (typeof userLimit === 'number' && !isNaN(userLimit)) {
            channelData.user_limit = userLimit;
          }
        }

        // Add parent category if specified
        if (parent && numericType !== CHANNEL_TYPES.GUILD_CATEGORY) {
          channelData.parent_id = parent;
        }

        console.log('Creating channel with data:', channelData);
        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {
          method: 'POST',
          headers: {
            Authorization: `Bot ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(channelData),
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        const newChannel = await response.json();
        return res.status(201).json(newChannel);
      } catch (error) {
        console.error('Error creating channel:', error);
        return res.status(500).json({ error: 'Failed to create channel' });
      }
    }

    if (req.method === 'PATCH') {
      // Editing channels requires admin permissions
      if (!isAdmin) {
        return res.status(403).json({ error: 'Forbidden - Admin access required' });
      }

      try {
        const channelId = req.query.channelId as string;
        if (!channelId) {
          return res.status(400).json({ error: 'Channel ID is required' });
        }

        const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bot ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(req.body),
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        const updatedChannel = await response.json();
        return res.status(200).json(updatedChannel);
      } catch (error) {
        console.error('Error updating channel:', error);
        return res.status(500).json({ error: 'Failed to update channel' });
      }
    }

    if (req.method === 'DELETE') {
      // Deleting channels requires admin permissions
      if (!isAdmin) {
        return res.status(403).json({ error: 'Forbidden - Admin access required' });
      }

      try {
        const channelId = req.query.channelId as string;
        if (!channelId) {
          return res.status(400).json({ error: 'Channel ID is required' });
        }

        const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        return res.status(200).json({ message: 'Channel deleted successfully' });
      } catch (error) {
        console.error('Error deleting channel:', error);
        return res.status(500).json({ error: 'Failed to delete channel' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in channel handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 