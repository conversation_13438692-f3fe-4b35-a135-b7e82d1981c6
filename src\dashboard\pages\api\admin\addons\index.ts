import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';
import YAML from 'yaml';

// Helper function to locate config.yml
function locateConfig(): string {
  // Get the correct project root (dashboard runs from src/dashboard)
  const projectRoot = process.cwd().includes('dashboard') 
    ? path.resolve(process.cwd(), '..', '..')
    : process.cwd();
    
  const attempts = [
    path.join(projectRoot, 'config.yml'),
    path.join(projectRoot, '404-bot', 'config.yml'),
  ];
  
  let found = attempts.find(p => fs.existsSync(p));
  if (!found) throw new Error('config.yml not found');
  return found;
}

// Helper function to locate addons directories
function locateAddonsDirs(): string[] {
  // Get the correct project root (dashboard runs from src/dashboard)
  const projectRoot = process.cwd().includes('dashboard') 
    ? path.resolve(process.cwd(), '..', '..')
    : process.cwd();
  
  console.log('Process cwd:', process.cwd());
  console.log('Project root:', projectRoot);
  
  const attempts = [
    // Source directories (TypeScript) - check these first
    path.join(projectRoot, 'src', 'addons'),
    path.join(projectRoot, '404-bot', 'src', 'addons'),
    // Compiled directories (JavaScript)
    path.join(projectRoot, 'dist', 'addons'),
    path.join(projectRoot, '404-bot', 'dist', 'addons'),
  ];
  
  console.log('=== LOCATE ADDONS DIRS DEBUG ===');
  console.log('Checking directories in order:');
  const foundDirs: string[] = [];
  attempts.forEach((attempt, i) => {
    const exists = fs.existsSync(attempt);
    console.log(`${i + 1}. ${attempt} - ${exists ? 'EXISTS' : 'NOT FOUND'}`);
    if (exists) {
      foundDirs.push(attempt);
    }
  });
  
  // Also check fallback paths using project root
  const fallbackSrc = path.join(projectRoot, 'src', 'addons');
  const fallbackDist = path.join(projectRoot, 'dist', 'addons');
  console.log('Trying fallback paths:');
  console.log(`- ${fallbackSrc} - ${fs.existsSync(fallbackSrc) ? 'EXISTS' : 'NOT FOUND'}`);
  console.log(`- ${fallbackDist} - ${fs.existsSync(fallbackDist) ? 'EXISTS' : 'NOT FOUND'}`);
  
  if (fs.existsSync(fallbackSrc) && !foundDirs.includes(fallbackSrc)) foundDirs.push(fallbackSrc);
  if (fs.existsSync(fallbackDist) && !foundDirs.includes(fallbackDist)) foundDirs.push(fallbackDist);
  
  if (foundDirs.length === 0) throw new Error('No addons directories found');
  
  // Remove duplicates
  const uniqueDirs = Array.from(new Set(foundDirs));
  console.log('Found addons directories:', uniqueDirs);
  return uniqueDirs;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check admin permission
  const isAdmin = (session.user as any).isAdmin;
  if (!isAdmin) {
    return res.status(403).json({ error: 'Forbidden - Admin access required' });
  }

  try {
    // Read config file
    const configPath = locateConfig();
    console.log('Found config at:', configPath);
    const configFile = fs.readFileSync(configPath, 'utf8');
    const config = YAML.parse(configFile);

    // Get addons directories
    const addonsDirs = locateAddonsDirs();
    console.log('=== ADDON SCANNING DEBUG ===');
    console.log('Found addons directories at:', addonsDirs);
    
    const allAddons = new Map<string, { name: string, fullPath: string }>();
    
    // Collect all addons from all directories
    addonsDirs.forEach(dir => {
      console.log(`Processing addon directory: ${dir}`);
      
      const dirContents = fs.readdirSync(dir, { withFileTypes: true });
      const dirAddons = dirContents
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name);
      console.log(`Addons found in ${dir}:`, dirAddons);
      
      dirAddons.forEach(name => {
        const fullPath = path.join(dir, name);
        // If we already have this addon, skip it (first found wins)
        if (!allAddons.has(name)) {
          allAddons.set(name, { name, fullPath });
        }
      });
    });

    const addonEntries = Array.from(allAddons.values());
    console.log('All unique addon entries:', addonEntries.map(a => `${a.name} (${a.fullPath})`));
    
    // Also list what's actually in each directory
    addonEntries.forEach(({ name, fullPath }) => {
      const files = fs.readdirSync(fullPath);
      console.log(`Contents of ${name}:`, files);
    });

    // Separate built-in and custom addons
    const builtInAddons: any[] = [];
    const customAddons: any[] = [];

    // Build info array for each addon directory
    addonEntries.forEach(({ name, fullPath }) => {
      console.log(`Processing addon: ${name}`);
      
      // Default metadata
      let info = {
        name,
        version: '1.0.0',
        description: 'No description available',
        author: 'Unknown'
      };

      // Check if this is a custom addon (created by addon builder)
      let isCustomAddon = false;

      // 1) Check config.yml metadata if present
      try {
        const cfgPath = path.join(fullPath, 'config.yml');
        if (fs.existsSync(cfgPath)) {
          const cfgRaw = fs.readFileSync(cfgPath, 'utf8');
          const cfg = YAML.parse(cfgRaw);
          console.log(`Config for ${name}:`, cfg);
          
          // Handle both nested (old) and flat (new) config structures
          const configData = cfg?.addon || cfg; // Use nested if available, otherwise flat
          
          if (configData) {
            info = {
              ...info,
              name: configData.name ?? info.name,
              version: configData.version ?? info.version,
              description: configData.description ?? info.description,
              author: configData.author ?? info.author
            };
            
            // Check if it's a custom addon (check both structures)
            isCustomAddon = configData.author === 'Addon Builder' || 
                            configData.description === 'Generated addon from visual builder' ||
                            configData.description?.includes('Generated addon from visual builder');
            console.log(`${name} - Config-based detection: Author: "${configData.author}", Description: "${configData.description}", isCustomAddon: ${isCustomAddon}`);
          }
        } else {
          console.log(`No config.yml found for ${name}`);
        }
      } catch (err) {
        console.warn(`Error reading addon ${name} config.yml:`, err);
      }

      // Additional check: Look for flow.json file (indicates visual builder creation)
      if (!isCustomAddon) {
        const flowJsonPath = path.join(fullPath, 'flow.json');
        if (fs.existsSync(flowJsonPath)) {
          isCustomAddon = true;
          console.log(`${name} - Found flow.json file, marking as custom addon`);
        }
      }

      // 2) Fallback to parsing index.ts if still missing description or author
      try {
        const indexPath = path.join(fullPath, 'index.ts');
        if (fs.existsSync(indexPath)) {
          const indexContent = fs.readFileSync(indexPath, 'utf8');
          
          // Check if it's a custom addon by looking at the file content
          if (!isCustomAddon) {
            const hasGeneratedMarker = indexContent.includes('Generated addon from visual builder');
            const hasBuilderAuthor = indexContent.includes('author: \'Addon Builder\'');
            isCustomAddon = hasGeneratedMarker && hasBuilderAuthor;
            console.log(`${name} - Checking index.ts: hasGeneratedMarker=${hasGeneratedMarker}, hasBuilderAuthor=${hasBuilderAuthor}, isCustomAddon=${isCustomAddon}`);
          }
          
          // Try to extract info from the addon structure
          const infoMatch = indexContent.match(/info:\s*{([\s\S]*?)}/m);
          if (infoMatch) {
            const objBlock = infoMatch[1];
            // simple regex extractions
            const versionMatch = objBlock.match(/version:\s*['"]([^'\"]*)['"]/);
            const descMatch = objBlock.match(/description:\s*['"]([^'\"]*)['"]/);
            const authorMatch = objBlock.match(/author:\s*['"]([^'\"]*)['"]/);

            if (versionMatch) info.version = versionMatch[1];
            if (descMatch && (!info.description || info.description === 'No description available')) info.description = descMatch[1];
            if (authorMatch && (!info.author || info.author === 'Unknown')) info.author = authorMatch[1];
          }
        }
      } catch (error) {
        console.warn(`Error reading addon ${name} index.ts info:`, error);
      }

      // Check if addon is enabled
      const isEnabled = config.addons?.enabled && 
        (!config.addons.disabled || !config.addons.disabled.includes(name));

      // Check if addon has config
      const hasConfig = fs.existsSync(path.join(fullPath, 'config.yml'));

      const addonData = {
        ...info,
        enabled: isEnabled,
        hasConfig,
        isCustomAddon,
        config: config.addons?.configs?.[name] || {},
        status: 'active' as const
      };

      console.log(`Final categorization for ${name}: isCustomAddon=${isCustomAddon}`);

      // Categorize the addon
      if (isCustomAddon) {
        customAddons.push(addonData);
        console.log(`Added ${name} to customAddons`);
      } else {
        builtInAddons.push(addonData);
        console.log(`Added ${name} to builtInAddons`);
      }
    });

    console.log(`Final results: builtInAddons=${builtInAddons.length}, customAddons=${customAddons.length}`);
    console.log('Built-in addons:', builtInAddons.map(a => a.name));
    console.log('Custom addons:', customAddons.map(a => a.name));

    return res.status(200).json({
      builtInAddons,
      customAddons,
      // Legacy support - return all addons for backward compatibility
      addons: [...builtInAddons, ...customAddons]
    });
  } catch (error: any) {
    console.error('Error listing addons:', error);
    return res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 