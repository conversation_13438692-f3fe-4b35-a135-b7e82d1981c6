import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ 
        error: 'Unauthorized',
        hasSession: false 
      });
    }

    const user = session.user as any;
    
    return res.status(200).json({
      hasSession: true,
      userId: user.id,
      isAdmin: user.isAdmin,
      name: user.name,
      email: user.email,
      debug: {
        sessionKeys: Object.keys(session),
        userKeys: Object.keys(user),
        fullUser: user
      }
    });
  } catch (error: any) {
    console.error('Permission check error:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    });
  }
} 