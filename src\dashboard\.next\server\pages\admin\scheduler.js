"use strict";(()=>{var e={};e.id=2063,e.ids=[2063],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2029:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.r(r),o.d(r,{config:()=>x,default:()=>m,getServerSideProps:()=>u,getStaticPaths:()=>b,getStaticProps:()=>h,reportWebVitals:()=>p,routeModule:()=>v,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>I,unstable_getStaticParams:()=>T,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>g});var l=o(1292),s=o(8834),n=o(786),i=o(3567),a=o(8077),c=o(6094),d=e([a,c]);[a,c]=d.then?(await d)():d;let m=(0,n.M)(c,"default"),h=(0,n.M)(c,"getStaticProps"),b=(0,n.M)(c,"getStaticPaths"),u=(0,n.M)(c,"getServerSideProps"),x=(0,n.M)(c,"config"),p=(0,n.M)(c,"reportWebVitals"),g=(0,n.M)(c,"unstable_getStaticProps"),j=(0,n.M)(c,"unstable_getStaticPaths"),T=(0,n.M)(c,"unstable_getStaticParams"),S=(0,n.M)(c,"unstable_getServerProps"),I=(0,n.M)(c,"unstable_getServerSideProps"),v=new l.PagesRouteModule({definition:{kind:s.A.PAGES,page:"/admin/scheduler",pathname:"/admin/scheduler",bundlePath:"",filename:""},components:{App:a.default,Document:i.default},userland:c});t()}catch(e){t(e)}})},2115:e=>{e.exports=require("yaml")},2326:e=>{e.exports=require("react-dom")},3762:(e,r,o)=>{o.d(r,{N:()=>b});var t=o(5542),l=o.n(t);let s=require("next-auth/providers/discord");var n=o.n(s),i=o(9021),a=o(2115),c=o.n(a),d=o(3873);let m={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d.resolve(process.cwd(),e)).find(e=>i.existsSync(e));if(!e){let r=d.resolve(__dirname,"../../../config.yml");i.existsSync(r)&&(e=r)}if(!e)throw Error("config.yml not found");let r=i.readFileSync(e,"utf8");m=c().parse(r)}catch(e){process.exit(1)}let h={bot:{token:m.bot.token,clientId:m.bot.clientId,clientSecret:m.bot.clientSecret,guildId:m.bot.guildId,ticketCategoryId:m.bot.ticketCategoryId||null,ticketLogChannelId:m.bot.ticketLogChannelId||null,prefix:m.bot.prefix},dashboard:{admins:m.dashboard?.admins||[],adminRoleIds:m.dashboard?.adminRoleIds||[],session:{secret:m.dashboard?.session?.secret||m.bot.clientSecret}},database:{url:m.database.url,name:m.database.name,options:{maxPoolSize:m.database.options?.maxPoolSize||10,minPoolSize:m.database.options?.minPoolSize||1,maxIdleTimeMS:m.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:m.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:m.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:m.database.options?.connectTimeoutMS||1e4,retryWrites:m.database.options?.retryWrites!==!1,retryReads:m.database.options?.retryReads!==!1}}};h.bot.token||process.exit(1),h.bot.clientId&&h.bot.clientSecret||process.exit(1),h.bot.guildId||process.exit(1),h.database.url&&h.database.name||process.exit(1);let b={providers:[n()({clientId:h.bot.clientId,clientSecret:h.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:r,profile:o})=>(r&&o&&(e.accessToken=r.access_token||null,e.id=o.id||null),e),async session({session:e,token:r}){if(e?.user){let o=r.id||null,t=r.accessToken||null;e.user.id=o,e.user.accessToken=t;let l=!1;if(o)if((h.dashboard.admins||[]).includes(o))l=!0;else{let e=h.dashboard.adminRoleIds||[];if(e.length&&h.bot.token&&h.bot.guildId)try{let r=await fetch(`https://discord.com/api/v10/guilds/${h.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${h.bot.token}`}});if(r.ok){let o=await r.json();l=e.some(e=>o.roles?.includes(e))||!1}else await r.text()}catch(e){}}e.user.isAdmin=l,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:r}){let o=new URL(r),t=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(r)||e.startsWith(t)?e:r}},secret:h.dashboard.session.secret||h.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,r)=>{},warn:e=>{},debug:(e,r)=>{}}};l()(b)},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},5542:e=>{e.exports=require("next-auth")},5806:e=>{e.exports=require("next-auth/next")},6094:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.r(r),o.d(r,{default:()=>u,getServerSideProps:()=>x});var l=o(8732),s=o(9733),n=o(2015),i=o(5806),a=o(3762),c=o(8079),d=o(1011),m=o(6884),h=o(3001),b=e([s,d,m,h]);[s,d,m,h]=b.then?(await b)():b;let u=()=>{let e=(0,s.useToast)(),{isOpen:r,onOpen:o,onClose:t}=(0,s.useDisclosure)(),[i,a]=(0,n.useState)([]),[b,u]=(0,n.useState)([]),[x,p]=(0,n.useState)(!0),[g,j]=(0,n.useState)(null),{currentScheme:T}=(0,h.DP)(),{handleSubmit:S,control:I,register:v,reset:E,formState:{errors:C,isSubmitting:y}}=(0,m.useForm)({defaultValues:{eventType:"TIMED_MESSAGE",isEnabled:!0,locationType:"VOICE",intervalValue:24,intervalUnit:"hours"}}),f=(0,m.useWatch)({control:I,name:"eventType"}),F=(0,m.useWatch)({control:I,name:"locationType"}),k=(0,m.useWatch)({control:I,name:"embedEnabled"}),U=async()=>{try{let e=await fetch("/api/scheduler");if(!e.ok)throw Error("Failed to fetch events");let r=await e.json();a(r)}catch(r){e({title:"Error fetching events",description:r.message,status:"error",duration:5e3,isClosable:!0})}finally{p(!1)}},w=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let r=await e.json();u(r)}catch(r){e({title:"Error fetching channels",description:r.message,status:"error",duration:5e3,isClosable:!0})}};(0,n.useEffect)(()=>{U(),w()},[]);let{textChannels:M,voiceChannels:A,categories:L}=(0,n.useMemo)(()=>{let e=b.filter(e=>4===e.raw_type).sort((e,r)=>e.position-r.position),r=b.filter(e=>[0,5,10,11,12].includes(e.raw_type)),o=b.filter(e=>[2,13].includes(e.raw_type));return{textChannels:r,voiceChannels:o,categories:e}},[b]),D=(e=null)=>{if(j(e),e){let r={...e,startTime:e.startTime?new Date(e.startTime).toISOString().slice(0,16):"",endTime:e.endTime?new Date(e.endTime).toISOString().slice(0,16):""};if("TIMED_MESSAGE"===e.eventType&&"string"==typeof e.message)try{let o=JSON.parse(e.message);if(o&&"object"==typeof o&&!Array.isArray(o)&&o.embeds){let e=o.embeds[0];r.message=o.content||"",r.embedEnabled=!0,r.embedTitle=e.title||"",r.embedDescription=e.description||"",r.embedColor=e.color?"#"+e.color.toString(16).padStart(6,"0"):"",r.embedAuthorName=e.author?.name||"",r.embedAuthorUrl=e.author?.url||"",r.embedAuthorIconUrl=e.author?.icon_url||"",r.embedImageUrl=e.image?.url||"",r.embedThumbnailUrl=e.thumbnail?.url||"",r.embedFooterText=e.footer?.text||"",r.embedFooterIconUrl=e.footer?.icon_url||"",r.embedTimestamp=!!e.timestamp}else r.embedEnabled=!1}catch(e){r.embedEnabled=!1}E(r)}else E({eventType:"TIMED_MESSAGE",name:"",isEnabled:!0,locationType:"VOICE",message:"",description:"",channelId:"",eventChannelId:"",startTime:"",endTime:"",location:"",intervalValue:24,intervalUnit:"hours",embedEnabled:!1,embedTitle:"",embedDescription:"",embedColor:"",embedAuthorName:"",embedAuthorUrl:"",embedAuthorIconUrl:"",embedImageUrl:"",embedThumbnailUrl:"",embedFooterText:"",embedFooterIconUrl:"",embedTimestamp:!1});o()},_=async r=>{let o=g?`/api/scheduler?eventId=${g._id}`:"/api/scheduler",l=g?"PUT":"POST",{eventType:s,name:n,isEnabled:i}=r,a={eventType:s,name:n,isEnabled:i};if("TIMED_MESSAGE"===s){if(a={...a,channelId:r.channelId,message:r.message,intervalValue:r.intervalValue,intervalUnit:r.intervalUnit},r.embedEnabled){let e={};r.embedTitle&&(e.title=r.embedTitle),r.embedDescription&&(e.description=r.embedDescription),r.embedColor&&(e.color=parseInt(r.embedColor.replace("#",""),16)),(r.embedAuthorName||r.embedAuthorUrl||r.embedAuthorIconUrl)&&(e.author={},r.embedAuthorName&&(e.author.name=r.embedAuthorName),r.embedAuthorUrl&&(e.author.url=r.embedAuthorUrl),r.embedAuthorIconUrl&&(e.author.icon_url=r.embedAuthorIconUrl)),r.embedImageUrl&&(e.image={url:r.embedImageUrl}),r.embedThumbnailUrl&&(e.thumbnail={url:r.embedThumbnailUrl}),(r.embedFooterText||r.embedFooterIconUrl)&&(e.footer={},r.embedFooterText&&(e.footer.text=r.embedFooterText),r.embedFooterIconUrl&&(e.footer.icon_url=r.embedFooterIconUrl)),r.embedTimestamp&&(e.timestamp=new Date().toISOString()),a.message=JSON.stringify({content:r.message||void 0,embeds:[e]})}}else"GUILD_EVENT"===s&&(a={...a,description:r.description,startTime:r.startTime?new Date(r.startTime).toISOString():void 0,endTime:r.endTime?new Date(r.endTime).toISOString():void 0,locationType:r.locationType},"VOICE"===r.locationType?a.eventChannelId=r.eventChannelId:a.location=r.location);try{let r=await fetch(o,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok){let e="Failed to save event";try{e=(await r.json()).error||e}catch{}throw Error(e)}e({title:`Event ${g?"updated":"created"}`,status:"success",duration:3e3,isClosable:!0}),U(),t()}catch(r){e({title:"Error saving event",description:r.message,status:"error",duration:5e3,isClosable:!0})}},V=async r=>{if(window.confirm("Are you sure you want to delete this event?"))try{let o=await fetch(`/api/scheduler?eventId=${r}`,{method:"DELETE"});if(!o.ok)throw Error((await o.json()).error||"Failed to delete");e({title:"Event deleted",status:"success",duration:3e3,isClosable:!0}),U()}catch(r){e({title:"Error deleting event",description:r.message,status:"error",duration:5e3,isClosable:!0})}},G=e=>{let r=b.find(r=>r.id===e);return r?`#${r.name}`:"Unknown Channel"},N=e=>{let r=L.map(r=>({...r,children:e.filter(e=>e.parent_id===r.id).sort((e,r)=>e.position-r.position)})).filter(e=>e.children.length>0),o=e.filter(e=>!e.parent_id).sort((e,r)=>e.position-r.position);return(0,l.jsxs)(l.Fragment,{children:[r.map(e=>(0,l.jsx)("optgroup",{label:e.name,children:e.children.map(e=>(0,l.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))},e.id)),o.length>0&&(0,l.jsx)("optgroup",{label:"No Category",children:o.map(e=>(0,l.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))})]})};return(0,l.jsxs)(d.A,{children:[(0,l.jsx)(s.Container,{maxW:"container.xl",py:8,children:(0,l.jsxs)(s.VStack,{spacing:8,align:"stretch",children:[(0,l.jsxs)(s.HStack,{justify:"space-between",children:[(0,l.jsxs)(s.VStack,{align:"start",children:[(0,l.jsxs)(s.Heading,{size:"lg",children:[(0,l.jsx)(s.Icon,{as:c.wIk,mr:3}),"Event Scheduler"]}),(0,l.jsx)(s.Text,{color:"gray.500",children:"Schedule timed messages and guild events for your server."})]}),(0,l.jsx)(s.Button,{leftIcon:(0,l.jsx)(c.GGD,{}),colorScheme:"blue",onClick:()=>D(),children:"Create Event"})]}),(0,l.jsxs)(s.Card,{children:[(0,l.jsx)(s.CardHeader,{children:(0,l.jsx)(s.Heading,{size:"md",children:"Scheduled Events"})}),(0,l.jsx)(s.CardBody,{children:x?(0,l.jsx)(s.Spinner,{}):0===i.length?(0,l.jsx)(s.Text,{children:"No scheduled events found. Create one to get started!"}):(0,l.jsx)(s.Box,{overflowX:"auto",children:(0,l.jsxs)(s.Table,{variant:"simple",children:[(0,l.jsx)(s.Thead,{children:(0,l.jsxs)(s.Tr,{children:[(0,l.jsx)(s.Th,{children:"Status"}),(0,l.jsx)(s.Th,{children:"Type"}),(0,l.jsx)(s.Th,{children:"Name"}),(0,l.jsx)(s.Th,{children:"Target / Location"}),(0,l.jsx)(s.Th,{children:"Schedule"}),(0,l.jsx)(s.Th,{children:"Actions"})]})}),(0,l.jsx)(s.Tbody,{children:i.map(e=>(0,l.jsxs)(s.Tr,{children:[(0,l.jsx)(s.Td,{children:(0,l.jsx)(s.Badge,{colorScheme:e.isEnabled?"green":"red",children:e.isEnabled?"Enabled":"Disabled"})}),(0,l.jsx)(s.Td,{children:(0,l.jsxs)(s.HStack,{children:[(0,l.jsx)(s.Icon,{as:"GUILD_EVENT"===e.eventType?c.usP:c.mEP}),(0,l.jsx)(s.Text,{children:"GUILD_EVENT"===e.eventType?"Guild Event":"Message"})]})}),(0,l.jsx)(s.Td,{children:e.name}),(0,l.jsx)(s.Td,{children:"TIMED_MESSAGE"===e.eventType?G(e.channelId):"VOICE"===e.locationType?G(e.eventChannelId):e.location}),(0,l.jsx)(s.Td,{children:"TIMED_MESSAGE"===e.eventType?(0,l.jsx)(s.Text,{fontFamily:"mono",fontSize:"sm",children:e.cron||`${e.intervalValue} ${e.intervalUnit}`}):(0,l.jsx)(s.Text,{fontSize:"sm",children:new Date(e.startTime).toLocaleString()})}),(0,l.jsx)(s.Td,{children:(0,l.jsxs)(s.HStack,{spacing:2,children:[(0,l.jsx)(s.Tooltip,{label:"Edit Event",children:(0,l.jsx)(s.IconButton,{"aria-label":"Edit",icon:(0,l.jsx)(c.SG1,{}),size:"sm",onClick:()=>D(e)})}),(0,l.jsx)(s.Tooltip,{label:"Delete Event",children:(0,l.jsx)(s.IconButton,{"aria-label":"Delete",icon:(0,l.jsx)(c.IXo,{}),size:"sm",colorScheme:"red",onClick:()=>V(e._id)})})]})})]},e._id))})]})})})]})]})}),(0,l.jsxs)(s.Modal,{isOpen:r,onClose:t,size:"6xl",closeOnOverlayClick:!1,children:[" ",(0,l.jsx)(s.ModalOverlay,{bg:"blackAlpha.600"}),(0,l.jsxs)(s.ModalContent,{bg:T.colors.background,border:"2px solid",borderColor:"blue.400",maxW:"1200px",as:"form",onSubmit:S(_),children:[(0,l.jsxs)(s.ModalHeader,{color:T.colors.text,children:[g?"Edit":"Create"," Scheduled Event"]}),(0,l.jsx)(s.ModalCloseButton,{}),(0,l.jsx)(s.ModalBody,{pb:6,children:(0,l.jsxs)(s.VStack,{spacing:6,align:"stretch",children:[(0,l.jsxs)(s.FormControl,{children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Event Type"}),(0,l.jsx)(m.Controller,{name:"eventType",control:I,render:({field:e})=>(0,l.jsx)(s.RadioGroup,{...e,children:(0,l.jsxs)(s.Stack,{direction:"row",spacing:5,children:[(0,l.jsx)(s.Radio,{value:"TIMED_MESSAGE",children:"Timed Message"}),(0,l.jsx)(s.Radio,{value:"GUILD_EVENT",children:"Guild Event"})]})})})]}),(0,l.jsxs)(s.Tabs,{variant:"enclosed",colorScheme:"blue",children:[(0,l.jsx)(s.TabList,{children:"TIMED_MESSAGE"===f?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(s.Tab,{children:"Timed Message Settings"}),(0,l.jsx)(s.Tab,{children:"Message Builder"})]}):(0,l.jsx)(s.Tab,{children:"Guild Event Settings"})}),(0,l.jsxs)(s.TabPanels,{children:["TIMED_MESSAGE"===f&&(0,l.jsx)(s.TabPanel,{children:(0,l.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.name,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Event Name"}),(0,l.jsx)(s.Input,{...v("name",{required:"Event name is required"}),placeholder:"e.g., Daily Announcement",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{display:"flex",alignItems:"center",children:[(0,l.jsx)(s.FormLabel,{htmlFor:"isEnabled",mb:"0",color:T.colors.text,children:"Enable Event"}),(0,l.jsx)(s.Switch,{id:"isEnabled",...v("isEnabled")})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.channelId,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Channel"}),(0,l.jsx)(s.Select,{...v("channelId",{required:"Channel is required"}),placeholder:"Select a text channel",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border,children:N(M)})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.intervalValue,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Interval"}),(0,l.jsxs)(s.HStack,{children:[(0,l.jsx)(m.Controller,{name:"intervalValue",control:I,rules:{required:"Interval is required",min:1},render:({field:e})=>(0,l.jsxs)(s.NumberInput,{min:1,...e,onChange:r=>e.onChange(Number(r)),children:[(0,l.jsx)(s.NumberInputField,{placeholder:"e.g., 5",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border}),(0,l.jsxs)(s.NumberInputStepper,{children:[(0,l.jsx)(s.NumberIncrementStepper,{}),(0,l.jsx)(s.NumberDecrementStepper,{})]})]})}),(0,l.jsxs)(s.Select,{...v("intervalUnit",{required:"Unit is required"}),w:"40%",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border,children:[(0,l.jsx)("option",{value:"seconds",children:"Seconds"}),(0,l.jsx)("option",{value:"minutes",children:"Minutes"}),(0,l.jsx)("option",{value:"hours",children:"Hours"}),(0,l.jsx)("option",{value:"days",children:"Days"}),(0,l.jsx)("option",{value:"weeks",children:"Weeks"}),(0,l.jsx)("option",{value:"months",children:"Months"})]})]}),(0,l.jsx)(s.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Example: Every 5 minutes, every 2 hours, every 1 day, etc."})]})]})}),"TIMED_MESSAGE"===f&&(0,l.jsx)(s.TabPanel,{children:(0,l.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.message,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Message Content"}),(0,l.jsx)(s.Textarea,{...v("message"),placeholder:"Enter your message content. Optional if using an embed. Supports Discord markdown.",rows:3,bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border}),(0,l.jsx)(s.Text,{fontSize:"xs",color:T.colors.textSecondary,mt:1,children:"This will be sent as plain text or alongside your embed."})]}),(0,l.jsxs)(s.FormControl,{display:"flex",alignItems:"center",children:[(0,l.jsx)(s.FormLabel,{htmlFor:"embedEnabled",mb:"0",color:T.colors.text,children:"Enable Embed"}),(0,l.jsx)(s.Switch,{id:"embedEnabled",...v("embedEnabled")})]}),k&&(0,l.jsxs)(s.VStack,{spacing:4,w:"full",pl:4,borderLeft:"2px solid",borderColor:"blue.200",children:[(0,l.jsx)(s.Text,{fontSize:"lg",fontWeight:"bold",color:T.colors.text,children:"Embed Details"}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedTitle,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Title"}),(0,l.jsx)(s.Input,{...v("embedTitle"),placeholder:"e.g., Important Announcement",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedDescription,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Description"}),(0,l.jsx)(s.Textarea,{...v("embedDescription"),placeholder:"Main content of the embed.",rows:3,bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedColor,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Color (Hex Code)"}),(0,l.jsx)(s.Input,{type:"color",...v("embedColor"),placeholder:"e.g., #00FF00",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border}),(0,l.jsx)(s.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Enter a hex color code (e.g., #RRGGBB)."})]}),(0,l.jsx)(s.Text,{fontSize:"md",fontWeight:"bold",color:T.colors.text,mt:4,mb:2,children:"Author Fields"}),(0,l.jsxs)(s.SimpleGrid,{columns:2,spacing:4,children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedAuthorName,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Author Name"}),(0,l.jsx)(s.Input,{...v("embedAuthorName"),placeholder:"e.g., 404 Bot",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedAuthorUrl,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Author URL"}),(0,l.jsx)(s.Input,{...v("embedAuthorUrl"),placeholder:"https://example.com",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedAuthorIconUrl,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Author Icon URL"}),(0,l.jsx)(s.Input,{...v("embedAuthorIconUrl"),placeholder:"https://example.com/icon.png",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]})]}),(0,l.jsx)(s.Text,{fontSize:"md",fontWeight:"bold",color:T.colors.text,mt:4,mb:2,children:"Image/Thumbnail Fields"}),(0,l.jsxs)(s.SimpleGrid,{columns:2,spacing:4,children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedImageUrl,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Image URL"}),(0,l.jsx)(s.Input,{...v("embedImageUrl"),placeholder:"https://example.com/image.png",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border}),(0,l.jsx)(s.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Large image displayed above the footer."})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedThumbnailUrl,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Thumbnail URL"}),(0,l.jsx)(s.Input,{...v("embedThumbnailUrl"),placeholder:"https://example.com/thumbnail.png",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border}),(0,l.jsx)(s.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Small image usually in the top right."})]})]}),(0,l.jsx)(s.Text,{fontSize:"md",fontWeight:"bold",color:T.colors.text,mt:4,mb:2,children:"Footer Fields"}),(0,l.jsxs)(s.SimpleGrid,{columns:2,spacing:4,children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedFooterText,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Footer Text"}),(0,l.jsx)(s.Input,{...v("embedFooterText"),placeholder:"e.g., Powered by 404 Bot",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{isInvalid:!!C.embedFooterIconUrl,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Footer Icon URL"}),(0,l.jsx)(s.Input,{...v("embedFooterIconUrl"),placeholder:"https://example.com/footer-icon.png",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]})]}),(0,l.jsxs)(s.FormControl,{display:"flex",alignItems:"center",children:[(0,l.jsx)(s.FormLabel,{htmlFor:"embedTimestamp",mb:"0",color:T.colors.text,children:"Show Timestamp"}),(0,l.jsx)(s.Switch,{id:"embedTimestamp",...v("embedTimestamp")}),(0,l.jsx)(s.Text,{fontSize:"xs",color:"gray.500",ml:2,children:"Adds current time to the embed footer."})]})]})]})}),"GUILD_EVENT"===f&&(0,l.jsx)(s.TabPanel,{children:(0,l.jsxs)(s.VStack,{spacing:4,align:"stretch",children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.description,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Description"}),(0,l.jsx)(s.Textarea,{...v("description",{required:"GUILD_EVENT"===f&&"Description is required"}),placeholder:"Describe the event.",rows:3,bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.SimpleGrid,{columns:2,spacing:4,w:"full",children:[(0,l.jsxs)(s.FormControl,{isInvalid:!!C.startTime,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Start Time"}),(0,l.jsx)(s.Input,{type:"datetime-local",...v("startTime",{required:"GUILD_EVENT"===f&&"Start time is required"}),bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]}),(0,l.jsxs)(s.FormControl,{children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"End Time (Optional)"}),(0,l.jsx)(s.Input,{type:"datetime-local",...v("endTime"),bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]})]}),(0,l.jsxs)(s.FormControl,{children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Location Type"}),(0,l.jsx)(m.Controller,{name:"locationType",control:I,render:({field:e})=>(0,l.jsx)(s.RadioGroup,{...e,children:(0,l.jsxs)(s.Stack,{direction:"row",spacing:5,children:[(0,l.jsx)(s.Radio,{value:"VOICE",children:"Voice Channel"}),(0,l.jsx)(s.Radio,{value:"EXTERNAL",children:"External URL"})]})})})]}),"VOICE"===F?(0,l.jsxs)(s.FormControl,{isInvalid:!!C.eventChannelId,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"Voice Channel"}),(0,l.jsx)(s.Select,{...v("eventChannelId",{required:"GUILD_EVENT"===f&&"VOICE"===F&&"Voice channel is required"}),placeholder:"Select a voice channel",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border,children:N(A)})]}):(0,l.jsxs)(s.FormControl,{isInvalid:!!C.location,children:[(0,l.jsx)(s.FormLabel,{color:T.colors.text,children:"External URL"}),(0,l.jsx)(s.Input,{...v("location",{required:"GUILD_EVENT"===f&&"EXTERNAL"===F&&"URL is required"}),placeholder:"https://your.event.link",bg:T.colors.background,color:T.colors.text,borderColor:T.colors.border})]})]})})]})]})]})}),(0,l.jsxs)(s.ModalFooter,{children:[(0,l.jsx)(s.Button,{variant:"ghost",mr:3,onClick:t,children:"Cancel"}),(0,l.jsx)(s.Button,{colorScheme:"blue",type:"submit",isLoading:y,children:g?"Save Changes":"Create Event"})]})]})]})]})},x=async e=>{let r=await (0,i.getServerSession)(e.req,e.res,a.N);return r&&r.user.isAdmin?{props:{session:r}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fadmin%2Fscheduler",permanent:!1}}};t()}catch(e){t(e)}})},6884:e=>{e.exports=import("react-hook-form")},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var r=require("../../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8270,4874,752,6281,5333],()=>o(2029));module.exports=t})();