# 404 Bot - Discord Bot with Addon System & MongoDB

A robust Discord bot built with TypeScript featuring a modular addon system, comprehensive logging, MongoDB integration, and YAML configuration.

## Features

- 🔧 **Modular Addon System** - Easily extend functionality with custom addons
- 🗃️ **MongoDB Integration** - Full MongoDB support with connection pooling and error handling
- 📝 **Comprehensive Logging** - Detailed logging for bot and addons with file rotation
- ⚙️ **YAML Configuration** - Human-readable configuration with validation
- 🔄 **Hot Reloading** - Addons can be reloaded without restarting the bot
- 🛡️ **Error Handling** - Robust error handling and graceful shutdowns
- ⏱️ **Command Cooldowns** - Built-in cooldown system for commands
- 🎯 **TypeScript** - Full type safety and modern JavaScript features

## Quick Start

1. **Setup MongoDB**
   - **Local**: Install MongoDB and run on port 27017
   - **Cloud**: Create a MongoDB Atlas cluster (free tier available)
   - **Docker**: `docker run -d -p 27017:27017 --name mongodb mongo:latest`

2. **Clone and install dependencies**
```bash
cd 404-bot
pnpm install
```

3. **Configure the bot**
```bash
cp config.example.yml config.yml
# Edit config.yml with your bot token and MongoDB connection
```

4. **Build and run**
```bash
pnpm build
pnpm start

# Or for development with hot reload
pnpm dev
```

## Configuration

The bot uses YAML configuration files. Copy `config.example.yml` to `config.yml` and update:

```yaml
bot:
  token: "YOUR_BOT_TOKEN_HERE"
  clientId: "YOUR_CLIENT_ID_HERE"
  guildId: "YOUR_GUILD_ID_HERE" # Optional: for guild-specific commands
  prefix: "!"

database:
  type: "mongodb"
  url: "mongodb://localhost:27017"  # Your MongoDB connection string
  name: "discord_bot"               # Database name
  options:
    maxPoolSize: 10                 # Connection pool settings
    serverSelectionTimeoutMS: 5000

logging:
  level: "info" # debug, info, warn, error
  console: true
  file: 
    enabled: true
    path: "logs"
    maxSize: "10m"
    maxFiles: 5

addons:
  enabled: true
  directory: "src/addons"
  autoReload: true
```

### MongoDB Connection Examples
- **Local**: `mongodb://localhost:27017`
- **With Authentication**: `*******************************************`
- **MongoDB Atlas**: `mongodb+srv://username:<EMAIL>`
- **Replica Set**: `mongodb://host1:27017,host2:27017,host3:27017/database?replicaSet=rs0`

## MongoDB Features

### Database Manager
- **Connection Pooling** - Configurable connection pool with automatic management
- **Auto-Reconnection** - Automatic reconnection with exponential backoff
- **Health Monitoring** - Built-in health checks and connection monitoring
- **Index Management** - Automatic index creation for optimal performance
- **Error Handling** - Comprehensive error handling and logging

### Example Commands
The included example addon provides:
- `/ping` - Basic ping command showing bot latency
- `/dbtest` - Test database operations (insert, query, count)

## Creating Database-Enabled Addons

```typescript
import { SlashCommandBuilder } from 'discord.js';
import { Addon, BotInstance } from '@types/index';
import { Logger } from '@core/Logger';

const addon: Addon = {
  info: {
    name: 'my-addon',
    version: '1.0.0',
    description: 'Database-enabled addon',
    author: 'Your Name'
  },

  commands: [
    {
      data: new SlashCommandBuilder()
        .setName('savedata')
        .setDescription('Save user data to MongoDB'),
      
      execute: async (interaction) => {
        const botInstance = (interaction.client as any).botInstance as BotInstance;
        
        if (!botInstance?.database) {
          await interaction.reply('Database not available!');
          return;
        }

        const db = botInstance.database.db;
        const collection = db.collection('user_data');
        
        try {
          await collection.insertOne({
            userId: interaction.user.id,
            username: interaction.user.username,
            timestamp: new Date(),
            guildId: interaction.guildId
          });
          
          await interaction.reply('✅ Data saved to MongoDB!');
        } catch (error) {
          console.error('Database error:', error);
          await interaction.reply('❌ Failed to save data!');
        }
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    if (bot.database) {
      // Create indexes when addon loads
      await bot.database.db.collection('user_data')
        .createIndex({ userId: 1 }, { unique: true });
    }
  }
};

export default addon;
```

## Available Scripts

- `pnpm dev` - Start in development mode with hot reload
- `pnpm build` - Build the TypeScript project
- `pnpm start` - Run the built bot
- `pnpm clean` - Clean build directory
- `pnpm type-check` - Run TypeScript type checking

## Architecture

### Core Components

- **ConfigManager** - Handles YAML configuration loading and validation
- **Logger** - Winston-based logging system with file rotation
- **DatabaseManager** - MongoDB connection management with pooling and reconnection
- **AddonManager** - Loads, unloads, and manages addons with hot-reloading
- **CommandHandler** - Handles slash commands and context menus with cooldowns

### Addon System

The addon system allows for:
- Slash commands and context menus
- Event listeners
- Lifecycle hooks (onLoad/onUnload)
- Database access through the bot instance
- Hot reloading during development
- Individual logging per addon

### MongoDB Integration

Comprehensive MongoDB support includes:
- Connection pooling with configurable settings
- Automatic reconnection with exponential backoff
- Health checks and connection monitoring
- Index management for performance
- Transaction support (for replica sets)
- Structured logging of database operations

### Logging

Comprehensive logging includes:
- Separate log files for different levels
- Structured logging with timestamps
- Per-addon logging capabilities
- Database operation logging
- Configurable log rotation

## Development

### Project Structure

```
404-bot/
├── src/
│   ├── addons/          # Bot addons
│   │   └── example/     # Example addon with MongoDB operations
│   ├── core/            # Core bot components
│   │   ├── DatabaseManager.ts # MongoDB connection and operations
│   │   ├── AddonManager.ts     # Addon system
│   │   ├── CommandHandler.ts   # Command handling
│   │   ├── ConfigManager.ts    # Configuration management
│   │   └── Logger.ts           # Logging system
│   ├── types/           # TypeScript type definitions
│   └── index.ts         # Main entry point
├── dist/                # Built JavaScript files
├── logs/                # Log files
├── config.yml           # Configuration file
└── package.json
```

### TypeScript Configuration

The project uses strict TypeScript settings with path mapping:
- `@/*` - Maps to `src/*`
- `@core/*` - Maps to `src/core/*`
- `@addons/*` - Maps to `src/addons/*`
- `@types/*` - Maps to `src/types/*`

### MongoDB Best Practices

1. **Indexes** - Create indexes for frequently queried fields
2. **Connection Pooling** - Use the configured pool settings
3. **Error Handling** - Always wrap database operations in try-catch
4. **Data Validation** - Validate data before inserting
5. **Logging** - Use the addon logger for database operations

## Troubleshooting

### Database Connection Issues
- Verify MongoDB is running and accessible
- Check connection string format
- For Atlas: verify IP whitelist and credentials
- Check firewall settings for port 27017

### Bot Issues
- Check bot token in configuration
- Verify Discord permissions
- Check logs for specific errors

## Production Deployment

### MongoDB Atlas (Recommended)
1. Create MongoDB Atlas cluster
2. Configure network access (IP whitelist)
3. Create database user with appropriate permissions
4. Use connection string in configuration

### Environment-Specific Configs
```yaml
# Development
database:
  url: "mongodb://localhost:27017"
  name: "discord_bot_dev"

# Production
database:
  url: "mongodb+srv://user:<EMAIL>"
  name: "discord_bot_prod"
  options:
    maxPoolSize: 20
    serverSelectionTimeoutMS: 10000
```

## License

MIT License - see LICENSE file for details. 