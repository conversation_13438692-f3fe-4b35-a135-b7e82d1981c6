import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  if (!session) {
    // Not logged in → send directly to Discord login
    return {
      redirect: {
        destination: '/signin',
        permanent: false,
      },
    };
  }

  const isAdmin = (session.user as any)?.isAdmin;
  return {
    redirect: {
              destination: '/overview',
      permanent: false,
    },
  };
};

export default function Home() {
  // This component will never be rendered because of redirects.
  return null;
} 