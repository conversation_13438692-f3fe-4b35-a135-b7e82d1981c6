import { MongoClient, Db, MongoClientOptions } from 'mongodb';
import type { BotConfig, DatabaseStats } from '@/types/index.js';
import { Logger } from './Logger.js';

export class DatabaseManager {
  public client!: MongoClient;
  public db!: Db;
  private logger: any;
  private config: any;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private isConnecting = false;
  private connectionPromise: Promise<void> | null = null;

  constructor(config: any) {
    this.config = config;
    try {
      this.logger = Logger.createLogger();
    } catch (error) {
      // Fallback logger for dashboard environment
      this.logger = {
        info: console.log,
        error: console.error,
        warn: console.warn,
        debug: console.debug
      };
    }
  }

  public async connect(): Promise<void> {
    // Prevent multiple connection attempts
    if (this.connectionPromise) {
      return this.connectionPromise;
    }

    if (this.client && this.db) {
      // Already connected
      return Promise.resolve();
    }

    this.connectionPromise = this._connect();
    return this.connectionPromise;
  }

  private async _connect(): Promise<void> {
    try {
      this.isConnecting = true;

      // Optimized MongoDB connection options
      const options: MongoClientOptions = {
        // Connection Pool Settings
        maxPoolSize: 20, // Maximum number of connections in the pool
        minPoolSize: 5,  // Minimum number of connections in the pool
        maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
        
        // Connection Timeout Settings
        connectTimeoutMS: 10000, // 10 seconds to establish connection
        socketTimeoutMS: 45000,  // 45 seconds for socket operations
        serverSelectionTimeoutMS: 10000, // 10 seconds to select a server
        
        // Performance Optimizations
        retryWrites: true,
        retryReads: true,
        compressors: ['zlib'], // Enable compression for better network performance
        
        // Monitoring and Health
        heartbeatFrequencyMS: 10000, // Check server health every 10 seconds
        
        // Additional optimizations from config
        ...this.config.database.options
      };

      this.client = new MongoClient(this.config.database.url, options);
      await this.client.connect();
      
      this.db = this.client.db(this.config.database.name);
      
      // Set up connection event listeners for better monitoring
      this.client.on('connectionPoolCreated', () => {
        this.logger.debug('MongoDB connection pool created');
      });
      
      this.client.on('connectionPoolClosed', () => {
        this.logger.debug('MongoDB connection pool closed');
      });
      
      this.client.on('connectionCreated', () => {
        this.logger.debug('New MongoDB connection created');
      });

      // Test the connection
      await this.db.admin().ping();
      
      this.logger.info(`✅ Connected to MongoDB: ${this.config.database.name}`);
      this.reconnectAttempts = 0;
      
      // Create indexes for better query performance
      await this.createIndexes();
      
    } catch (error) {
      this.logger.error('Failed to connect to MongoDB:', error);
      this.reconnectAttempts++;
      
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.logger.info(`Retrying connection (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds before retry
        return this._connect();
      }
      
      throw error;
    } finally {
      this.isConnecting = false;
      this.connectionPromise = null;
    }
  }

  public async disconnect(): Promise<void> {
    if (this.client) {
      await this.client.close();
      this.logger.info('Disconnected from MongoDB');
    }
  }

  // Helper method to ensure connection before operations
  private async ensureConnection(): Promise<void> {
    if (!this.client || !this.db) {
      await this.connect();
    }
  }

  // Optimized helper methods for common database operations
  public async createIndexes(): Promise<void> {
    try {
      // Create indexes for performance optimization
      const indexPromises = [
        // Command logs - for analytics and response time tracking
        this.db.collection('command_logs').createIndex(
          { timestamp: -1 }, 
          { background: true, name: 'timestamp_desc' }
        ),
        this.db.collection('command_logs').createIndex(
          { commandName: 1, timestamp: -1 }, 
          { background: true, name: 'command_timestamp' }
        ),
        this.db.collection('command_logs').createIndex(
          { userId: 1, timestamp: -1 }, 
          { background: true, name: 'user_timestamp' }
        ),
        
        // Error logs
        this.db.collection('error_logs').createIndex(
          { timestamp: -1 }, 
          { background: true, name: 'error_timestamp_desc' }
        ),
        this.db.collection('error_logs').createIndex(
          { type: 1, timestamp: -1 }, 
          { background: true, name: 'error_type_timestamp' }
        ),
        
        // Command states - for quick command enable/disable checks
        this.db.collection('command_states').createIndex(
          { commandId: 1 }, 
          { unique: true, background: true, name: 'command_id_unique' }
        ),
        
        // Bot status
        this.db.collection('bot_status').createIndex(
          { key: 1 }, 
          { unique: true, background: true, name: 'status_key_unique' }
        ),
        
        // Guilds collection
        this.db.collection('guilds').createIndex(
          { guildId: 1 }, 
          { unique: true, background: true, name: 'guild_id_unique' }
        ),
        
        // Guild configurations
        this.db.collection('guild_configs').createIndex(
          { guildId: 1 },
          { unique: true, background: true, name: 'guild_id_unique' }
        ),

        // Users collection
        this.db.collection('users').createIndex(
          { userId: 1 }, 
          { background: true, name: 'user_id' }
        ),
        this.db.collection('users').createIndex(
          { userId: 1, guildId: 1 }, 
          { background: true, name: 'user_guild' }
        )
      ];

      await Promise.allSettled(indexPromises);
      this.logger.debug('✅ Database indexes created/verified');
      
    } catch (error) {
      this.logger.error('Error creating database indexes:', error);
    }
  }

  // Optimized query methods with connection checking
  public async findOne(collection: string, query: any, options?: any): Promise<any> {
    await this.ensureConnection();
    return this.db.collection(collection).findOne(query, options);
  }

  public async find(collection: string, query: any, options?: any): Promise<any[]> {
    await this.ensureConnection();
    return this.db.collection(collection).find(query, options).toArray();
  }

  public async insertOne(collection: string, document: any, options?: any): Promise<any> {
    await this.ensureConnection();
    return this.db.collection(collection).insertOne(document, options);
  }

  public async updateOne(collection: string, filter: any, update: any, options?: any): Promise<any> {
    await this.ensureConnection();
    return this.db.collection(collection).updateOne(filter, update, options);
  }

  public async deleteOne(collection: string, filter: any, options?: any): Promise<any> {
    await this.ensureConnection();
    return this.db.collection(collection).deleteOne(filter, options);
  }

  public async countDocuments(collection: string, query: any, options?: any): Promise<number> {
    await this.ensureConnection();
    return this.db.collection(collection).countDocuments(query, options);
  }

  // Health check method with connection pool stats
  public getStatus(): any {
    return {
      details: {
        database: this.config.database.name,
        connected: this.client !== null && this.db !== null,
        reconnectAttempts: this.reconnectAttempts,
        isConnecting: this.isConnecting,
        hasClient: !!this.client,
        hasDatabase: !!this.db
      }
    };
  }

  // Method to get database statistics
  public async getStats(): Promise<DatabaseStats> {
    await this.ensureConnection();

    try {
      const stats = await this.db.stats();
      return {
        collections: stats['collections'],
        documents: stats['objects'],
        dataSize: stats['dataSize'],
        storageSize: stats['storageSize'],
        indexes: stats['indexes'],
        indexSize: stats['indexSize']
      };
    } catch (error) {
      this.logger.error('Failed to get database stats:', error);
      throw error;
    }
  }

  // Method to warm up the connection pool
  public async warmupConnections(): Promise<void> {
    await this.ensureConnection();
    
    try {
      // Perform a few lightweight operations to establish connections
      const warmupPromises = Array.from({ length: 3 }, () => 
        this.db.admin().ping()
      );
      
      await Promise.all(warmupPromises);
      this.logger.debug('Database connection pool warmed up');
    } catch (error) {
      this.logger.warn('Failed to warm up connection pool:', error);
    }
  }
}
