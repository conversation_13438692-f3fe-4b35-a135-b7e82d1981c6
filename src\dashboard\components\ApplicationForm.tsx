import { VStack, FormControl, FormLabel, Input, Textarea, Select, Card, CardBody, SimpleGrid, Box } from '@chakra-ui/react';
import React, { useState, useCallback, useRef, useEffect } from 'react';

// Timezone data
const TIMEZONES = [
  { value: 'GMT-12:00', label: '(GMT-12:00) International Date Line West' },
  { value: 'GMT-11:00', label: '(GMT-11:00) Midway Island, Samoa' },
  { value: 'GMT-10:00', label: '(GMT-10:00) Hawaii' },
  { value: 'GMT-09:00', label: '(GMT-09:00) Alaska' },
  { value: 'GMT-08:00', label: '(GMT-08:00) Pacific Time (US & Canada)' },
  { value: 'GMT-07:00', label: '(GMT-07:00) Mountain Time (US & Canada)' },
  { value: 'GMT-06:00', label: '(GMT-06:00) Central Time (US & Canada)' },
  { value: 'GMT-05:00', label: '(GMT-05:00) Eastern Time (US & Canada)' },
  { value: 'GMT-04:00', label: '(GMT-04:00) Atlantic Time (Canada)' },
  { value: 'GMT-03:00', label: '(GMT-03:00) Buenos Aires, Georgetown' },
  { value: 'GMT-02:00', label: '(GMT-02:00) Mid-Atlantic' },
  { value: 'GMT-01:00', label: '(GMT-01:00) Azores, Cape Verde Islands' },
  { value: 'GMT+00:00', label: '(GMT+00:00) London, Dublin, Edinburgh' },
  { value: 'GMT+01:00', label: '(GMT+01:00) Paris, Amsterdam, Berlin' },
  { value: 'GMT+02:00', label: '(GMT+02:00) Athens, Istanbul, Helsinki' },
  { value: 'GMT+03:00', label: '(GMT+03:00) Moscow, Baghdad, Kuwait' },
  { value: 'GMT+04:00', label: '(GMT+04:00) Abu Dhabi, Dubai, Baku' },
  { value: 'GMT+05:00', label: '(GMT+05:00) Karachi, Tashkent' },
  { value: 'GMT+06:00', label: '(GMT+06:00) Dhaka, Almaty' },
  { value: 'GMT+07:00', label: '(GMT+07:00) Bangkok, Jakarta' },
  { value: 'GMT+08:00', label: '(GMT+08:00) Beijing, Singapore, Hong Kong' },
  { value: 'GMT+09:00', label: '(GMT+09:00) Tokyo, Seoul, Osaka' },
  { value: 'GMT+10:00', label: '(GMT+10:00) Sydney, Melbourne, Brisbane' },
  { value: 'GMT+11:00', label: '(GMT+11:00) Solomon Islands' },
  { value: 'GMT+12:00', label: '(GMT+12:00) Auckland, Wellington' }
];

interface FormState {
  age: string;
  hoursPerWeek: string;
  timezone: string;
  motivation: string;
}

interface ApplicationFormProps {
  session: any;
  onFormChange: (formData: FormState) => void;
  initialData?: FormState;
  motivationLabel?: string;
  motivationPlaceholder?: string;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({ session, onFormChange, initialData, motivationLabel="Why do you want to be a moderator?", motivationPlaceholder="Tell us about your motivation and what you can bring to the team..." }) => {
  const formRef = useRef<HTMLDivElement>(null);
  
  // Local state for form fields
  const [formState, setFormState] = useState<FormState>({
    age: initialData?.age || '',
    hoursPerWeek: initialData?.hoursPerWeek || '',
    timezone: initialData?.timezone || '',
    motivation: initialData?.motivation || ''
  });

  // Use a ref to track changes and avoid immediate updates
  const formStateRef = useRef(formState);
  formStateRef.current = formState;

  // Scroll to top when step changes
  useEffect(() => {
    if (formRef.current) {
      formRef.current.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Notify parent of changes using useEffect
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      onFormChange(formStateRef.current);
    }, 100); // Small delay to batch updates

    return () => clearTimeout(timeoutId);
  }, [formState, onFormChange]);

  // Handle input changes efficiently
  const handleInputChange = useCallback((field: keyof FormState, value: string) => {
    setFormState(prev => ({
      ...prev,
      [field]: value
    }));
  }, []);

  return (
    <Box ref={formRef}>
      <VStack spacing={6} align="stretch">
        <Card bg="whiteAlpha.50" border="1px solid" borderColor="whiteAlpha.200">
          <CardBody>
            <SimpleGrid columns={2} spacing={4}>
              <FormControl>
                <FormLabel>Discord Username</FormLabel>
                <Input
                  value={session?.user?.name ?? ''}
                  isReadOnly
                  bg="whiteAlpha.100"
                  _hover={{ bg: "whiteAlpha.200" }}
                />
              </FormControl>

              <FormControl>
                <FormLabel>Discord User ID</FormLabel>
                <Input
                  value={(session?.user as any)?.id ?? ''}
                  isReadOnly
                  bg="whiteAlpha.100"
                  _hover={{ bg: "whiteAlpha.200" }}
                />
              </FormControl>
            </SimpleGrid>
          </CardBody>
        </Card>

        <Card bg="whiteAlpha.50" border="1px solid" borderColor="whiteAlpha.200" mt={4}>
          <CardBody>
            <SimpleGrid columns={3} spacing={4}>
              <FormControl isRequired>
                <FormLabel>Age</FormLabel>
                <Input
                  type="number"
                  value={formState.age}
                  onChange={(e) => handleInputChange('age', e.target.value)}
                  min={13}
                  placeholder="Enter your age"
                  bg="whiteAlpha.100"
                  _hover={{ bg: "whiteAlpha.200" }}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Hours per Week</FormLabel>
                <Input
                  type="number"
                  value={formState.hoursPerWeek}
                  onChange={(e) => handleInputChange('hoursPerWeek', e.target.value)}
                  min={1}
                  placeholder="Hours available"
                  bg="whiteAlpha.100"
                  _hover={{ bg: "whiteAlpha.200" }}
                />
              </FormControl>

              <FormControl isRequired>
                <FormLabel>Timezone</FormLabel>
                <Select
                  value={formState.timezone}
                  onChange={(e) => handleInputChange('timezone', e.target.value)}
                  placeholder="Select timezone"
                  bg="whiteAlpha.100"
                  _hover={{ bg: "whiteAlpha.200" }}
                >
                  {TIMEZONES.map((tz) => (
                    <option key={tz.value} value={tz.value}>
                      {tz.label}
                    </option>
                  ))}
                </Select>
              </FormControl>
            </SimpleGrid>
          </CardBody>
        </Card>

        <Card bg="whiteAlpha.50" border="1px solid" borderColor="whiteAlpha.200" mt={4}>
          <CardBody>
            <FormControl isRequired>
              <FormLabel>{motivationLabel}</FormLabel>
              <Textarea
                value={formState.motivation}
                onChange={(e) => handleInputChange('motivation', e.target.value)}
                placeholder={motivationPlaceholder}
                minH="200px"
                bg="whiteAlpha.100"
                _hover={{ bg: "whiteAlpha.200" }}
                resize="vertical"
              />
            </FormControl>
          </CardBody>
        </Card>
      </VStack>
    </Box>
  );
};

export default React.memo(ApplicationForm); 