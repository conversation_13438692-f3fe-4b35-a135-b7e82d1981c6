(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7991],{97991:(e,t,n)=>{e=n.nmd(e),ace.define("ace/mode/yaml_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text_highlight_rules").TextHighlightRules,o=function(){this.$rules={start:[{token:"comment",regex:"#.*$"},{token:"list.markup",regex:/^(?:-{3}|\.{3})\s*(?=#|$)/},{token:"list.markup",regex:/^\s*[\-?](?:$|\s)/},{token:"constant",regex:"!![\\w//]+"},{token:"constant.language",regex:"[&\\*][a-zA-Z0-9-_]+"},{token:["meta.tag","keyword"],regex:/^(\s*\w[^\s:]*?)(:(?=\s|$))/},{token:["meta.tag","keyword"],regex:/(\w[^\s:]*?)(\s*:(?=\s|$))/},{token:"keyword.operator",regex:"<<\\w*:\\w*"},{token:"keyword.operator",regex:"-\\s*(?=[{])"},{token:"string",regex:'["](?:(?:\\\\.)|(?:[^"\\\\]))*?["]'},{token:"string",regex:/[|>][-+\d]*(?:$|\s+(?:$|#))/,onMatch:function(e,t,n,r){r=r.replace(/ #.*/,"");var i=/^ *((:\s*)?-(\s*[^|>])?)?/.exec(r)[0].replace(/\S\s*$/,"").length,o=parseInt(/\d+[\s+-]*$/.exec(r));return o?(i+=o-1,this.next="mlString"):this.next="mlStringPre",n.length?(n[0]=this.next,n[1]=i):(n.push(this.next),n.push(i)),this.token},next:"mlString"},{token:"string",regex:"['](?:(?:\\\\.)|(?:[^'\\\\]))*?[']"},{token:"constant.numeric",regex:/(\b|[+\-\.])[\d_]+(?:(?:\.[\d_]*)?(?:[eE][+\-]?[\d_]+)?)(?=[^\d-\w]|$)$/},{token:"constant.numeric",regex:/[+\-]?\.inf\b|NaN\b|0x[\dA-Fa-f_]+|0b[10_]+/},{token:"constant.language.boolean",regex:"\\b(?:true|false|TRUE|FALSE|True|False|yes|no)\\b"},{token:"paren.lparen",regex:"[[({]"},{token:"paren.rparen",regex:"[\\])}]"},{token:"text",regex:/[^\s,:\[\]\{\}]+/}],mlStringPre:[{token:"indent",regex:/^ *$/},{token:"indent",regex:/^ */,onMatch:function(e,t,n){return n[1]>=e.length?(this.next="start",n.shift(),n.shift()):(n[1]=e.length-1,this.next=n[0]="mlString"),this.token},next:"mlString"},{defaultToken:"string"}],mlString:[{token:"indent",regex:/^ *$/},{token:"indent",regex:/^ */,onMatch:function(e,t,n){return n[1]>=e.length?(this.next="start",n.splice(0)):this.next="mlString",this.token},next:"mlString"},{token:"string",regex:".+"}]},this.normalizeRules()};r.inherits(o,i),t.YamlHighlightRules=o}),ace.define("ace/mode/matching_brace_outdent",["require","exports","module","ace/range"],function(e,t,n){"use strict";var r=e("../range").Range,i=function(){};(function(){this.checkOutdent=function(e,t){return!!/^\s+$/.test(e)&&/^\s*\}/.test(t)},this.autoOutdent=function(e,t){var n=e.getLine(t).match(/^(\s*\})/);if(!n)return 0;var i=n[1].length,o=e.findMatchingBracket({row:t,column:i});if(!o||o.row==t)return 0;var a=this.$getIndent(e.getLine(o.row));e.replace(new r(t,0,t,i-1),a)},this.$getIndent=function(e){return e.match(/^\s*/)[0]}}).call(i.prototype),t.MatchingBraceOutdent=i}),ace.define("ace/mode/folding/coffee",["require","exports","module","ace/lib/oop","ace/mode/folding/fold_mode","ace/range"],function(e,t,n){"use strict";var r=e("../../lib/oop"),i=e("./fold_mode").FoldMode,o=e("../../range").Range,a=t.FoldMode=function(){};r.inherits(a,i),(function(){this.commentBlock=function(e,t){var n=/\S/,r=e.getLine(t),i=r.search(n);if(-1!=i&&"#"==r[i]){for(var a=r.length,s=e.getLength(),g=t,c=t;++t<s;){var l=(r=e.getLine(t)).search(n);if(-1!=l){if("#"!=r[l])break;c=t}}if(c>g){var d=e.getLine(c).length;return new o(g,a,c,d)}}},this.getFoldWidgetRange=function(e,t,n){var r=this.indentationBlock(e,n);if(r||(r=this.commentBlock(e,n)))return r},this.getFoldWidget=function(e,t,n){var r=e.getLine(n),i=r.search(/\S/),o=e.getLine(n+1),a=e.getLine(n-1),s=a.search(/\S/),g=o.search(/\S/);if(-1==i)return e.foldWidgets[n-1]=-1!=s&&s<g?"start":"","";if(-1==s){if(i==g&&"#"==r[i]&&"#"==o[i])return e.foldWidgets[n-1]="",e.foldWidgets[n+1]="","start"}else if(s==i&&"#"==r[i]&&"#"==a[i]&&-1==e.getLine(n-2).search(/\S/))return e.foldWidgets[n-1]="start",e.foldWidgets[n+1]="","";return(-1!=s&&s<i?e.foldWidgets[n-1]="start":e.foldWidgets[n-1]="",i<g)?"start":""}}).call(a.prototype)}),ace.define("ace/mode/folding/yaml",["require","exports","module","ace/lib/oop","ace/mode/folding/coffee","ace/range"],function(e,t,n){"use strict";var r=e("../../lib/oop"),i=e("./coffee").FoldMode,o=e("../../range").Range,a=t.FoldMode=function(){};r.inherits(a,i),(function(){this.getFoldWidgetRange=function(e,t,n){var r=/\S/,i=e.getLine(n),a=i.search(r),s="#"===i[a],g="-"===i[a];if(-1!=a){var c=i.length,l=e.getLength(),d=n,u=n;if(s){var h=this.commentBlock(e,n);if(h)return h}else if(g){var h=this.indentationBlock(e,n);if(h)return h}else for(;++n<l;){var i=e.getLine(n),f=i.search(r);if(-1!=f){if(f<=a&&"-"!==i[a]){var m=e.getTokenAt(n,0);if(!m||"string"!==m.type)break}u=n}}if(u>d){var k=e.getLine(u).length;return new o(d,c,u,k)}}},this.getFoldWidget=function(e,t,n){var r=e.getLine(n),i=r.search(/\S/),o=e.getLine(n+1),a=e.getLine(n-1),s=a.search(/\S/),g=o.search(/\S/),c="-"===r[i];if(-1==i)return e.foldWidgets[n-1]=-1!=s&&s<g?"start":"","";if(-1==s){if(i==g&&"#"==r[i]&&"#"==o[i])return e.foldWidgets[n-1]="",e.foldWidgets[n+1]="","start"}else if(s==i&&"#"==r[i]&&"#"==a[i]&&-1==e.getLine(n-2).search(/\S/))return e.foldWidgets[n-1]="start",e.foldWidgets[n+1]="","";return(-1!=s&&s<i||-1!=s&&s==i&&c?e.foldWidgets[n-1]="start":e.foldWidgets[n-1]="",i<g)?"start":""}}).call(a.prototype)}),ace.define("ace/mode/yaml",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/yaml_highlight_rules","ace/mode/matching_brace_outdent","ace/mode/folding/yaml","ace/worker/worker_client"],function(e,t,n){"use strict";var r=e("../lib/oop"),i=e("./text").Mode,o=e("./yaml_highlight_rules").YamlHighlightRules,a=e("./matching_brace_outdent").MatchingBraceOutdent,s=e("./folding/yaml").FoldMode,g=e("../worker/worker_client").WorkerClient,c=function(){this.HighlightRules=o,this.$outdent=new a,this.foldingRules=new s,this.$behaviour=this.$defaultBehaviour};r.inherits(c,i),(function(){this.lineCommentStart=["#"],this.getNextLineIndent=function(e,t,n){var r=this.$getIndent(t);return"start"==e&&t.match(/^.*[\{\(\[]\s*$/)&&(r+=n),r},this.checkOutdent=function(e,t,n){return this.$outdent.checkOutdent(t,n)},this.autoOutdent=function(e,t,n){this.$outdent.autoOutdent(t,n)},this.createWorker=function(e){var t=new g(["ace"],"ace/mode/yaml_worker","YamlWorker");return t.attachToDocument(e.getDocument()),t.on("annotate",function(t){e.setAnnotations(t.data)}),t.on("terminate",function(){e.clearAnnotations()}),t},this.$id="ace/mode/yaml"}).call(c.prototype),t.Mode=c}),ace.require(["ace/mode/yaml"],function(t){e&&(e.exports=t)})}}]);