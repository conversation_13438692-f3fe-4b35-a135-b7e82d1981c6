(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8963],{21191:(e,n,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/experimental/addon-builder",function(){return s(79101)}])},76857:(e,n,s)=>{"use strict";s.d(n,{o:()=>a});var r=s(94513),l=s(33225),t=s(2923);let a=(0,l.B)("div",{baseStyle:{display:"flex",alignItems:"center",justifyContent:"center"}});a.displayName="Center";let o={horizontal:{insetStart:"50%",transform:"translateX(-50%)"},vertical:{top:"50%",transform:"translateY(-50%)"},both:{insetStart:"50%",top:"50%",transform:"translate(-50%, -50%)"}};(0,t.R)(function(e,n){let{axis:s="both",...t}=e;return(0,r.jsx)(l.B.div,{ref:n,__css:o[s],...t,position:"absolute"})})},79101:(e,n,s)=>{"use strict";s.r(n),s.d(n,{__N_SSP:()=>er,default:()=>el});var r=s(94513),l=s(79156),t=s(51961),a=s(31678),o=s(26977),i=s(49451),c=s(52216),d=s(60341),h=s(94285),m=s(22907),u=s(95845),x=s(44327),j=s(41611),p=s(78902),g=s(62690),v=s(55920),C=s(91047),b=s(83881),f=s(47402),y=s(99820),w=s(72671),S=s(40443),E=s(63730),z=s(64057),k=s(7627),T=s(75697),A=s(86293),F=s(22680),_=s(71601),R=s(84443),D=s(90020),N=s(61481),O=s(22237),$=s(25964),M=s(71185),J=s(9557),q=s(7680),I=s(52922),B=s(47847),Y=s(59365),L=s(85104),P=s(79961),G=s(28245),H=s(76857),Q=s(57561),W=s(25680),K=s(5095),U=s(17842),V=s(53424),X=s(58686);let Z={name:"",description:"",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:"// Your command code here\nawait interaction.reply({\n  content: 'Hello from your new command!',\n  ephemeral: true\n});"},ee={name:"ready",once:!0,code:"// Your event code here\nconsole.log('Bot is ready!');"},en=["ready","messageCreate","interactionCreate","guildMemberAdd","guildMemberRemove","voiceStateUpdate","messageReactionAdd","messageReactionRemove","channelCreate","channelDelete"];function es(){var e;let{data:n}=(0,V.useSession)(),s=(0,X.useRouter)(),d=(0,m.d)(),{isOpen:es,onOpen:er,onClose:el}=(0,u.j)(),{isOpen:et,onOpen:ea,onClose:eo}=(0,u.j)(),{isOpen:ei,onOpen:ec,onClose:ed}=(0,u.j)(),[eh,em]=(0,h.useState)(""),[eu,ex]=(0,h.useState)(!1),[ej,ep]=(0,h.useState)([]),[eg,ev]=(0,h.useState)([]),[eC,eb]=(0,h.useState)(!1),ef=h.useRef(null),[ey,ew]=(0,h.useState)({name:"",version:"1.0.0",description:"",author:(null==n||null==(e=n.user)?void 0:e.name)||"",commands:[],events:[],settings:{embedColor:"#0099FF"}}),eS=(0,x.dU)("white","gray.800"),eE=(0,x.dU)("gray.200","gray.600"),ez=(0,h.useCallback)((e,n)=>{ew(s=>({...s,[e]:n}))},[]),ek=(0,h.useCallback)((e,n)=>{ew(s=>({...s,settings:{...s.settings,[e]:n}}))},[]),eT=(0,h.useCallback)(()=>{ew(e=>({...e,commands:[...e.commands,{...Z}]}))},[]),eA=(0,h.useCallback)((e,n,s)=>{ew(r=>({...r,commands:r.commands.map((r,l)=>l===e?{...r,[n]:s}:r)}))},[]),eF=(0,h.useCallback)(e=>{ew(n=>({...n,commands:n.commands.filter((n,s)=>s!==e)}))},[]),e_=(0,h.useCallback)(()=>{ew(e=>({...e,events:[...e.events,{...ee}]}))},[]),eR=(0,h.useCallback)((e,n,s)=>{ew(r=>({...r,events:r.events.map((r,l)=>l===e?{...r,[n]:s}:r)}))},[]),eD=(0,h.useCallback)(e=>{ew(n=>({...n,events:n.events.filter((n,s)=>s!==e)}))},[]),eN=(0,h.useCallback)(()=>{var e;let n=[];return(!ey.name||ey.name.length<2)&&n.push("Addon name must be at least 2 characters long"),/^[a-z0-9-]+$/.test(ey.name)||n.push("Addon name must contain only lowercase letters, numbers, and hyphens"),ey.version&&/^\d+\.\d+\.\d+$/.test(ey.version)||n.push("Version must be in semver format (e.g., 1.0.0)"),(!ey.description||ey.description.length<10)&&n.push("Description must be at least 10 characters long"),(!ey.author||ey.author.length<2)&&n.push("Author name must be at least 2 characters long"),(null==(e=ey.settings)?void 0:e.embedColor)&&/^#[0-9a-fA-F]{6}$/.test(ey.settings.embedColor)||n.push("Embed color must be a valid hex color (e.g., #0099FF)"),ey.commands.forEach((e,s)=>{e.name&&/^[a-z0-9-]+$/.test(e.name)||n.push("Command ".concat(s+1,": Invalid name format")),(!e.description||e.description.length<1)&&n.push("Command ".concat(s+1,": Description is required")),(!e.code||e.code.trim().length<10)&&n.push("Command ".concat(s+1,": Code implementation is required"))}),ey.events.forEach((e,s)=>{e.name||n.push("Event ".concat(s+1,": Name is required")),(!e.code||e.code.trim().length<5)&&n.push("Event ".concat(s+1,": Code implementation is required"))}),n},[ey]),eO=(0,h.useCallback)(()=>{let e=eN();if(e.length>0)return void ep(e);em("// ".concat(ey.name," - Generated Addon Preview\n// This is a preview of your addon's main structure\n\nexport default {\n  info: {\n    name: \"").concat(ey.name,'",\n    version: "').concat(ey.version,'",\n    description: "').concat(ey.description,'",\n    author: "').concat(ey.author,'"\n  },\n\n  commands: ').concat(ey.commands.length," command").concat(1!==ey.commands.length?"s":"",",\n  events: ").concat(ey.events.length," event").concat(1!==ey.events.length?"s":"",',\n  \n  settings: {\n    embedColor: "').concat(ey.settings.embedColor,'"\n  }\n};\n\n// Commands: ').concat(ey.commands.map(e=>e.name).join(", ")||"None","\n// Events: ").concat(ey.events.map(e=>e.name).join(", ")||"None","\n")),er()},[ey,eN,er]),e$=(0,h.useCallback)(async()=>{eb(!0);try{let e=await fetch("/api/experimental/addon-builder/templates"),n=await e.json();if(e.ok)ev(n.templates);else throw Error(n.error||"Failed to load templates")}catch(e){d({title:"Error Loading Templates",description:e instanceof Error?e.message:"Failed to load templates",status:"error",duration:3e3,isClosable:!0})}finally{eb(!1)}},[d]),eM=(0,h.useCallback)(e=>{var s;let r={...e.config};r.author=(null==n||null==(s=n.user)?void 0:s.name)||r.author,ew(r),eo(),d({title:"Template Loaded!",description:"".concat(e.name," template has been applied."),status:"success",duration:3e3,isClosable:!0})},[n,eo,d]),eJ=(0,h.useCallback)(async()=>{let e=eN();if(e.length>0)return void ep(e);ex(!0),ep([]);try{var s;let e=await fetch("/api/experimental/addon-builder/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(ey)}),r=await e.json();if(!e.ok)throw Error(r.error||"Failed to create addon");d({title:"Addon Created Successfully!",description:"".concat(ey.name," has been created with ").concat(r.files.length," files."),status:"success",duration:5e3,isClosable:!0}),ew({name:"",version:"1.0.0",description:"",author:(null==n||null==(s=n.user)?void 0:s.name)||"",commands:[],events:[],settings:{embedColor:"#0099FF"}})}catch(e){d({title:"Error Creating Addon",description:e instanceof Error?e.message:"An unknown error occurred",status:"error",duration:5e3,isClosable:!0})}finally{ex(!1)}},[ey,eN,d,n]),eq=(0,h.useCallback)(()=>{var e;""!==ey.name.trim()||""!==ey.description.trim()||ey.commands.length>0||ey.events.length>0||ey.author!==((null==n||null==(e=n.user)?void 0:e.name)||"")?ec():s.push("/admin/addons")},[ey,n,ec,s]),eI=(0,h.useCallback)(()=>{ed(),s.push("/admin/addons")},[ed,s]);return(0,r.jsxs)(t.a,{maxW:"4xl",mx:"auto",p:6,children:[(0,r.jsxs)(l.T,{spacing:6,align:"stretch",children:[(0,r.jsxs)(t.a,{textAlign:"center",children:[(0,r.jsx)(a.D,{size:"lg",mb:2,children:"\uD83D\uDEE0️ Addon Builder"}),(0,r.jsx)(j.E,{color:"gray.500",mb:4,children:"Create custom addons for your Discord bot with a visual interface"}),(0,r.jsxs)(p.z,{spacing:3,justify:"center",children:[(0,r.jsx)(g.$,{leftIcon:(0,r.jsx)(U.QVr,{}),onClick:eq,colorScheme:"gray",variant:"outline",size:"sm",children:"Go Back"}),(0,r.jsx)(g.$,{leftIcon:(0,r.jsx)(U.FSj,{}),onClick:()=>{e$(),ea()},colorScheme:"purple",variant:"outline",size:"sm",children:"Start from Template"})]})]}),ej.length>0&&(0,r.jsxs)(o.F,{status:"error",children:[(0,r.jsx)(i._,{}),(0,r.jsxs)(t.a,{children:[(0,r.jsx)(v.X,{children:"Validation Errors:"}),(0,r.jsx)(c.T,{children:(0,r.jsx)(l.T,{align:"start",spacing:1,children:ej.map((e,n)=>(0,r.jsxs)(j.E,{fontSize:"sm",children:["• ",e]},n))})})]})]}),(0,r.jsxs)(C.t,{colorScheme:"blue",variant:"enclosed",children:[(0,r.jsxs)(b.w,{children:[(0,r.jsx)(f.o,{children:"Basic Info"}),(0,r.jsxs)(f.o,{children:["Commands (",ey.commands.length,")"]}),(0,r.jsxs)(f.o,{children:["Events (",ey.events.length,")"]}),(0,r.jsx)(f.o,{children:"Settings"})]}),(0,r.jsxs)(y.T,{children:[(0,r.jsx)(w.K,{children:(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Addon Name"}),(0,r.jsx)(z.p,{value:ey.name,onChange:e=>ez("name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"my-awesome-addon"}),(0,r.jsx)(j.E,{fontSize:"sm",color:"gray.500",children:"Only lowercase letters, numbers, and hyphens allowed"})]}),(0,r.jsxs)(p.z,{spacing:4,children:[(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Version"}),(0,r.jsx)(z.p,{value:ey.version,onChange:e=>ez("version",e.target.value),placeholder:"1.0.0"})]}),(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Author"}),(0,r.jsx)(z.p,{value:ey.author,onChange:e=>ez("author",e.target.value),placeholder:"Your Name"})]})]}),(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Description"}),(0,r.jsx)(k.T,{value:ey.description,onChange:e=>ez("description",e.target.value),placeholder:"A brief description of what your addon does...",minH:"100px"})]})]})}),(0,r.jsx)(w.K,{children:(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(p.z,{justify:"space-between",children:[(0,r.jsx)(a.D,{size:"md",children:"Commands"}),(0,r.jsx)(g.$,{leftIcon:(0,r.jsx)(U.OiG,{}),onClick:eT,colorScheme:"blue",children:"Add Command"})]}),0===ey.commands.length?(0,r.jsxs)(o.F,{status:"info",children:[(0,r.jsx)(i._,{}),(0,r.jsx)(c.T,{children:"No commands yet. Add your first command to get started!"})]}):(0,r.jsx)(T.n,{allowMultiple:!0,children:ey.commands.map((e,n)=>(0,r.jsxs)(A.A,{children:[(0,r.jsxs)(F.J,{children:[(0,r.jsxs)(t.a,{flex:"1",textAlign:"left",children:[(0,r.jsxs)(p.z,{children:[(0,r.jsx)(j.E,{fontWeight:"bold",children:e.name||"Command ".concat(n+1)}),(0,r.jsx)(_.E,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Enabled":"Disabled"})]}),(0,r.jsx)(j.E,{fontSize:"sm",color:"gray.500",children:e.description||"No description"})]}),(0,r.jsx)(R.Q,{})]}),(0,r.jsx)(D.v,{pb:4,children:(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(p.z,{children:[(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Command Name"}),(0,r.jsx)(z.p,{value:e.name,onChange:e=>eA(n,"name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"ping"})]}),(0,r.jsxs)(S.MJ,{children:[(0,r.jsx)(E.l,{children:"Cooldown (ms)"}),(0,r.jsxs)(N.Q7,{value:e.cooldown,onChange:(e,s)=>eA(n,"cooldown",s),min:1e3,max:3e5,children:[(0,r.jsx)(N.OO,{}),(0,r.jsxs)(N.lw,{children:[(0,r.jsx)(N.Q0,{}),(0,r.jsx)(N.Sh,{})]})]})]})]}),(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Description"}),(0,r.jsx)(z.p,{value:e.description,onChange:e=>eA(n,"description",e.target.value),placeholder:"Shows bot ping"})]}),(0,r.jsxs)(S.MJ,{children:[(0,r.jsx)(E.l,{children:"Command Code"}),(0,r.jsx)(k.T,{value:e.code,onChange:e=>eA(n,"code",e.target.value),placeholder:"// Your command implementation here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,r.jsxs)(p.z,{children:[(0,r.jsx)(O.S,{isChecked:e.enabled,onChange:e=>eA(n,"enabled",e.target.checked),children:"Enabled"}),(0,r.jsx)(g.$,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,r.jsx)(U.qbC,{}),onClick:()=>eF(n),children:"Remove"})]})]})})]},n))})]})}),(0,r.jsx)(w.K,{children:(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(p.z,{justify:"space-between",children:[(0,r.jsx)(a.D,{size:"md",children:"Events"}),(0,r.jsx)(g.$,{leftIcon:(0,r.jsx)(U.OiG,{}),onClick:e_,colorScheme:"blue",children:"Add Event"})]}),0===ey.events.length?(0,r.jsxs)(o.F,{status:"info",children:[(0,r.jsx)(i._,{}),(0,r.jsx)(c.T,{children:"No events yet. Add event handlers to respond to Discord events!"})]}):(0,r.jsx)(T.n,{allowMultiple:!0,children:ey.events.map((e,n)=>(0,r.jsxs)(A.A,{children:[(0,r.jsxs)(F.J,{children:[(0,r.jsx)(t.a,{flex:"1",textAlign:"left",children:(0,r.jsxs)(p.z,{children:[(0,r.jsx)(j.E,{fontWeight:"bold",children:e.name||"Event ".concat(n+1)}),(0,r.jsx)(_.E,{colorScheme:e.once?"blue":"green",children:e.once?"Once":"Recurring"})]})}),(0,r.jsx)(R.Q,{})]}),(0,r.jsx)(D.v,{pb:4,children:(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsxs)(p.z,{children:[(0,r.jsxs)(S.MJ,{isRequired:!0,children:[(0,r.jsx)(E.l,{children:"Event Name"}),(0,r.jsxs)($.l,{value:e.name,onChange:e=>eR(n,"name",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"Select an event"}),en.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)(S.MJ,{children:[(0,r.jsx)(E.l,{children:"Trigger Type"}),(0,r.jsxs)($.l,{value:e.once?"once":"recurring",onChange:e=>eR(n,"once","once"===e.target.value),children:[(0,r.jsx)("option",{value:"once",children:"Once"}),(0,r.jsx)("option",{value:"recurring",children:"Recurring"})]})]})]}),(0,r.jsxs)(S.MJ,{children:[(0,r.jsx)(E.l,{children:"Event Code"}),(0,r.jsx)(k.T,{value:e.code,onChange:e=>eR(n,"code",e.target.value),placeholder:"// Your event handler code here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,r.jsx)(g.$,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,r.jsx)(U.qbC,{}),onClick:()=>eD(n),alignSelf:"flex-start",children:"Remove Event"})]})})]},n))})]})}),(0,r.jsx)(w.K,{children:(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsx)(a.D,{size:"md",children:"Settings"}),(0,r.jsxs)(S.MJ,{children:[(0,r.jsx)(E.l,{children:"Embed Color"}),(0,r.jsxs)(p.z,{children:[(0,r.jsx)(z.p,{type:"color",value:ey.settings.embedColor,onChange:e=>ek("embedColor",e.target.value),w:"80px"}),(0,r.jsx)(z.p,{value:ey.settings.embedColor,onChange:e=>ek("embedColor",e.target.value),placeholder:"#0099FF"})]})]}),(0,r.jsxs)(o.F,{status:"info",children:[(0,r.jsx)(i._,{}),(0,r.jsx)(c.T,{children:"More settings will be added in future updates!"})]})]})})]})]}),(0,r.jsx)(M.c,{}),(0,r.jsxs)(p.z,{spacing:4,justify:"center",children:[(0,r.jsx)(g.$,{leftIcon:(0,r.jsx)(U.Ny1,{}),onClick:eO,variant:"outline",colorScheme:"blue",children:"Preview"}),(0,r.jsx)(g.$,{leftIcon:(0,r.jsx)(U.uoG,{}),onClick:eJ,colorScheme:"blue",size:"lg",isLoading:eu,loadingText:"Creating...",children:"Create Addon"})]})]}),(0,r.jsxs)(J.aF,{isOpen:es,onClose:el,size:"xl",children:[(0,r.jsx)(q.m,{}),(0,r.jsxs)(I.$,{children:[(0,r.jsx)(B.r,{children:"Addon Preview"}),(0,r.jsx)(Y.s,{}),(0,r.jsx)(L.c,{children:(0,r.jsx)(P.C,{as:"pre",p:4,fontSize:"sm",overflow:"auto",maxH:"400px",children:eh})}),(0,r.jsx)(G.j,{children:(0,r.jsx)(g.$,{onClick:el,children:"Close"})})]})]}),(0,r.jsxs)(J.aF,{isOpen:et,onClose:eo,size:"4xl",children:[(0,r.jsx)(q.m,{}),(0,r.jsxs)(I.$,{children:[(0,r.jsx)(B.r,{children:"Choose a Template"}),(0,r.jsx)(Y.s,{}),(0,r.jsx)(L.c,{children:eC?(0,r.jsx)(H.o,{py:8,children:(0,r.jsxs)(l.T,{spacing:4,children:[(0,r.jsx)(Q.y,{size:"lg"}),(0,r.jsx)(j.E,{children:"Loading templates..."})]})}):(0,r.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,r.jsx)(j.E,{color:"gray.500",children:"Start with a pre-built template to save time and learn best practices."}),(0,r.jsx)(W.r,{columns:{base:1,md:2},spacing:4,children:eg.map(e=>(0,r.jsx)(t.a,{bg:eS,border:"1px",borderColor:eE,borderRadius:"md",p:4,cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",transform:"translateY(-2px)",boxShadow:"md"},onClick:()=>eM(e),children:(0,r.jsxs)(l.T,{align:"start",spacing:3,children:[(0,r.jsxs)(p.z,{justify:"space-between",w:"full",children:[(0,r.jsx)(a.D,{size:"sm",children:e.name}),(0,r.jsx)(_.E,{colorScheme:"blue",size:"sm",children:e.category})]}),(0,r.jsx)(j.E,{fontSize:"sm",color:"gray.500",children:e.description}),(0,r.jsxs)(p.z,{spacing:2,children:[(0,r.jsxs)(_.E,{variant:"outline",size:"xs",children:[e.config.commands.length," commands"]}),(0,r.jsxs)(_.E,{variant:"outline",size:"xs",children:[e.config.events.length," events"]})]})]})},e.id))})]})}),(0,r.jsx)(G.j,{children:(0,r.jsx)(g.$,{onClick:eo,children:"Cancel"})})]})]}),(0,r.jsx)(K.Lt,{isOpen:ei,leastDestructiveRef:ef,onClose:ed,isCentered:!0,children:(0,r.jsx)(q.m,{children:(0,r.jsxs)(K.EO,{children:[(0,r.jsx)(B.r,{fontSize:"lg",fontWeight:"bold",children:"Leave Addon Builder?"}),(0,r.jsx)(L.c,{children:"You have unsaved changes to your addon. Are you sure you want to go back? All your work will be lost."}),(0,r.jsxs)(G.j,{children:[(0,r.jsx)(g.$,{ref:ef,onClick:ed,children:"Cancel"}),(0,r.jsx)(g.$,{colorScheme:"red",onClick:eI,ml:3,children:"Yes, Go Back"})]})]})})})]})}var er=!0;function el(){return(0,r.jsx)(d.A,{children:(0,r.jsxs)(l.T,{spacing:8,p:8,align:"stretch",maxW:"100%",overflow:"hidden",children:[(0,r.jsxs)(t.a,{textAlign:"center",children:[(0,r.jsx)(a.D,{size:"xl",mb:2,children:"⚗️ Addon Builder"}),(0,r.jsxs)(o.F,{status:"info",mb:4,children:[(0,r.jsx)(i._,{}),(0,r.jsxs)(c.T,{children:[(0,r.jsx)("strong",{children:"Experimental Feature:"})," This is a beta feature for creating custom Discord bot addons. Use with caution and report any issues you encounter."]})]})]}),(0,r.jsx)(es,{})]})})}}},e=>{var n=n=>e(e.s=n);e.O(0,[4108,3256,9998,4976,217,2965,3177,3035,2044,341,636,6593,8792],()=>n(21191)),_N_E=e.O()}]);