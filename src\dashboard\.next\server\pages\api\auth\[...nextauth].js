"use strict";(()=>{var e={};e.id=4651,e.ids=[4651],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},1863:(e,t,o)=>{o.r(t),o.d(t,{config:()=>d,default:()=>r,routeModule:()=>l});var a=o(3433),i=o(264),s=o(584),n=o(8525);let r=(0,s.M)(n,"default"),d=(0,s.M)(n,"config"),l=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/auth/[...nextauth]",pathname:"/api/auth/[...nextauth]",bundlePath:"",filename:""},userland:n})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var a=o(5542),i=o.n(a);let s=require("next-auth/providers/discord");var n=o.n(s),r=o(8580);let d={providers:[n()({clientId:r.dashboardConfig.bot.clientId,clientSecret:r.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let i=!1;if(o)if((r.dashboardConfig.dashboard.admins||[]).includes(o))i=!0;else{let e=r.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&r.dashboardConfig.bot.token&&r.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${r.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${r.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();i=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:r.dashboardConfig.dashboard.session.secret||r.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),i=o(2115),s=o.n(i),n=o(3873);let r={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");r=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:r.bot.token,clientId:r.bot.clientId,clientSecret:r.bot.clientSecret,guildId:r.bot.guildId,ticketCategoryId:r.bot.ticketCategoryId||null,ticketLogChannelId:r.bot.ticketLogChannelId||null,prefix:r.bot.prefix},dashboard:{admins:r.dashboard?.admins||[],adminRoleIds:r.dashboard?.adminRoleIds||[],session:{secret:r.dashboard?.session?.secret||r.bot.clientSecret}},database:{url:r.database.url,name:r.database.name,options:{maxPoolSize:r.database.options?.maxPoolSize||10,minPoolSize:r.database.options?.minPoolSize||1,maxIdleTimeMS:r.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:r.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:r.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:r.database.options?.connectTimeoutMS||1e4,retryWrites:r.database.options?.retryWrites!==!1,retryReads:r.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=1863);module.exports=o})();