// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // For sensitive operations like creating roles we require admin.
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    if (req.method === 'POST') {
      try {
        const { name, color, permissions = [], hoist, mentionable } = req.body;

        if (!name) {
          return res.status(400).json({ error: 'Role name is required' });
        }

        // Helper map of Discord permission flags to their bit values
        const PERMISSION_BIT_MAP: Record<string, bigint> = {
          // General
          CREATE_INSTANT_INVITE: 1n << 0n,
          KICK_MEMBERS: 1n << 1n,
          BAN_MEMBERS: 1n << 2n,
          ADMINISTRATOR: 1n << 3n,
          MANAGE_CHANNELS: 1n << 4n,
          MANAGE_GUILD: 1n << 5n,
          ADD_REACTIONS: 1n << 6n,
          VIEW_AUDIT_LOG: 1n << 7n,
          PRIORITY_SPEAKER: 1n << 8n,
          STREAM: 1n << 9n,
          VIEW_CHANNEL: 1n << 10n,
          SEND_MESSAGES: 1n << 11n,
          SEND_TTS_MESSAGES: 1n << 12n,
          MANAGE_MESSAGES: 1n << 13n,
          EMBED_LINKS: 1n << 14n,
          ATTACH_FILES: 1n << 15n,
          READ_MESSAGE_HISTORY: 1n << 16n,
          MENTION_EVERYONE: 1n << 17n,
          USE_EXTERNAL_EMOJIS: 1n << 18n,
          // 1n << 19n is currently VIEW_GUILD_INSIGHTS which is not used in UI
          CONNECT: 1n << 20n,
          SPEAK: 1n << 21n,
          MUTE_MEMBERS: 1n << 22n,
          DEAFEN_MEMBERS: 1n << 23n,
          MOVE_MEMBERS: 1n << 24n,
          USE_VAD: 1n << 25n,
          CHANGE_NICKNAME: 1n << 26n,
          MANAGE_NICKNAMES: 1n << 27n,
          MANAGE_ROLES: 1n << 28n,
          MANAGE_WEBHOOKS: 1n << 29n,
          MANAGE_EMOJIS_AND_STICKERS: 1n << 30n,
          USE_APPLICATION_COMMANDS: 1n << 31n,
          // Skipping some less-common permissions for brevity
        };

        // Calculate the permission bitfield from the provided permission names (if any)
        let permissionBits = 0n;
        if (Array.isArray(permissions)) {
          for (const perm of permissions) {
            const bit = PERMISSION_BIT_MAP[perm];
            if (typeof bit === 'bigint') {
              permissionBits |= bit;
            }
          }
        }

        // Build the role payload for Discord
        const roleData: any = {
          name,
          hoist: !!hoist,
          mentionable: !!mentionable,
        };

        // Only include color if provided
        if (color) {
          const colorDecimal = parseInt(color.replace('#', ''), 16);
          if (!isNaN(colorDecimal)) {
            roleData.color = colorDecimal;
          }
        }

        // Only include permissions if at least one permission flag was set
        if (permissionBits !== 0n) {
          // Discord expects the permissions value to be sent as a string-encoded integer
          roleData.permissions = permissionBits.toString();
        }

        console.log('Creating role with data:', roleData);

        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles`, {
          method: 'POST',
          headers: {
            Authorization: `Bot ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(roleData),
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        const newRole = await response.json();
        return res.status(201).json(newRole);
      } catch (error) {
        console.error('Error creating role:', error);
        return res.status(500).json({ error: 'Failed to create role' });
      }
    }

    if (req.method === 'GET') {
      try {
        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles`, {
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch roles');
        }

        const roles = await response.json();
        return res.status(200).json(roles);
      } catch (error) {
        console.error('Error fetching roles:', error);
        return res.status(500).json({ error: 'Failed to fetch roles' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in roles handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 