import { useState, useEffect } from 'react';
import {
  Box,
  Container,
  VStack,
  HStack,
  Heading,
  Text,
  Card,
  CardHeader,
  CardBody,
  Badge,
  Button,
  Input,
  Select,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  useToast,
  IconButton,
  Tooltip,
  Flex,
  Spacer,
  useColorModeValue,
  Code,
  Collapse,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Icon, // Import Icon
} from '@chakra-ui/react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBug } from '@fortawesome/free-solid-svg-icons';
import {
  FiAlertCircle,
  FiRefreshCw,
  FiTrash2,
  FiEye,
  FiFilter,
  FiDownload,
  FiSearch,
  FiChevronDown,
  FiChevronUp,
} from 'react-icons/fi';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth';
import { authOptions } from '../api/auth/[...nextauth]';
import Layout from '../../components/Layout';
import { useTheme } from '../../contexts/ThemeContext';

interface ErrorLog {
  id: string;
  timestamp: string;
  level: 'error' | 'warn' | 'info';
  message: string;
  stack?: string;
  source: string;
  userId?: string;
  guildId?: string;
  metadata?: Record<string, any>;
}

export default function ErrorsPage() {
  const [errors, setErrors] = useState<ErrorLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedError, setSelectedError] = useState<ErrorLog | null>(null);
  const [filterLevel, setFilterLevel] = useState<string>('all');
  const [filterSource, setFilterSource] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  const { isOpen: isDetailOpen, onOpen: onDetailOpen, onClose: onDetailClose } = useDisclosure();
  const toast = useToast();
  const { currentScheme } = useTheme();

  // Use color mode value for card background and border
  const cardBg = useColorModeValue('whiteAlpha.100', 'gray.800'); // Use whiteAlpha for backdrop effect
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  useEffect(() => {
    fetchErrors();
  }, []);

  const fetchErrors = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/errors');
      if (response.ok) {
        const data = await response.json();
        setErrors(data.errors || []);
      } else {
        throw new Error('Failed to fetch errors');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch error logs',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  const clearErrors = async () => {
    try {
      const response = await fetch('/api/admin/errors', {
        method: 'DELETE',
      });

      if (response.ok) {
        setErrors([]);
        toast({
          title: 'Success',
          description: 'All error logs have been cleared',
          status: 'success',
          duration: 3000,
        });
      } else {
        throw new Error('Failed to clear errors');
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to clear error logs',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const exportErrors = () => {
    const dataStr = JSON.stringify(filteredErrors, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);

    const exportFileDefaultName = `error-logs-${new Date().toISOString().split('T')[0]}.json`;

    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  const toggleRowExpansion = (errorId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(errorId)) {
      newExpanded.delete(errorId);
    } else {
      newExpanded.add(errorId);
    }
    setExpandedRows(newExpanded);
  };

  const viewErrorDetails = (error: ErrorLog) => {
    setSelectedError(error);
    onDetailOpen();
  };

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'red';
      case 'warn': return 'yellow';
      case 'info': return 'blue';
      default: return 'gray';
    }
  };

  const filteredErrors = errors.filter(error => {
    const matchesLevel = filterLevel === 'all' || error.level === filterLevel;
    const matchesSource = filterSource === 'all' || error.source === filterSource;
    const matchesSearch = searchQuery === '' ||
      error.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      error.source.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesLevel && matchesSource && matchesSearch;
  });

  const uniqueSources = [...new Set(errors.map(error => error.source))];

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack spacing={6} align="stretch">
          {/* Header */}
          <HStack justify="space-between" align="center">
            <VStack align="start" spacing={1}>
              <Heading size="lg" display="flex" alignItems="center"> {/* Use flex to align icon and text */}
                <FontAwesomeIcon icon={faBug} style={{ marginRight: '0.5rem', color: '#ff4d4d' }} /> {/* Replace FiBug with FontAwesomeIcon */}
                <Box as="span" bgGradient="linear(to-r, red.400, orange.400)" bgClip="text"> {/* Apply gradient text */}
                  Error Logs
                </Box>
              </Heading>
              <Text color={currentScheme.colors.textSecondary} fontSize="sm">
                Monitor and manage system errors and warnings
              </Text>
            </VStack>

            <HStack spacing={3}>
              <Button
                leftIcon={<FiDownload />}
                variant="outline"
                size="sm"
                onClick={exportErrors}
                isDisabled={filteredErrors.length === 0}
              >
                Export
              </Button>
              <Button
                leftIcon={<FiRefreshCw />}
                colorScheme="blue"
                size="sm"
                onClick={fetchErrors}
                isLoading={loading}
              >
                Refresh
              </Button>
              <Button
                leftIcon={<FiTrash2 />}
                colorScheme="red"
                variant="outline"
                size="sm"
                onClick={clearErrors}
                isDisabled={errors.length === 0}
              >
                Clear All
              </Button>
            </HStack>
          </HStack>

          {/* Filters */}
          <Card bg={cardBg} borderColor={borderColor} backdropFilter="blur(10px)"> {/* Apply backdrop filter */}
            <CardBody>
              <HStack spacing={4} wrap="wrap">
                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2} color={currentScheme.colors.text}>Search</Text> {/* Use theme color */}
                  <Input
                    placeholder="Search errors..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    size="sm"
                    w="200px"
                  />
                </Box>

                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2} color={currentScheme.colors.text}>Level</Text> {/* Use theme color */}
                  <Select
                    value={filterLevel}
                    onChange={(e) => setFilterLevel(e.target.value)}
                    size="sm"
                    w="120px"
                  >
                    <option value="all">All Levels</option>
                    <option value="error">Error</option>
                    <option value="warn">Warning</option>
                    <option value="info">Info</option>
                  </Select>
                </Box>

                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2} color={currentScheme.colors.text}>Source</Text> {/* Use theme color */}
                  <Select
                    value={filterSource}
                    onChange={(e) => setFilterSource(e.target.value)}
                    size="sm"
                    w="150px"
                  >
                    <option value="all">All Sources</option>
                    {uniqueSources.map(source => (
                      <option key={source} value={source}>{source}</option>
                    ))}
                  </Select>
                </Box>

                <Spacer />

                <Box>
                  <Text fontSize="sm" fontWeight="medium" mb={2} color={currentScheme.colors.text}>Results</Text> {/* Use theme color */}
                  <Badge colorScheme="blue" fontSize="sm" px={2} py={1}>
                    {filteredErrors.length} of {errors.length}
                  </Badge>
                </Box>
              </HStack>
            </CardBody>
          </Card>

          {/* Error Logs Table */}
          <Card bg={cardBg} borderColor={borderColor} backdropFilter="blur(10px)"> {/* Apply backdrop filter */}
            <CardHeader>
              <Heading size="md" color={currentScheme.colors.text}>Recent Errors</Heading> {/* Use theme color */}
            </CardHeader>
            <CardBody>
              {loading ? (
                <Flex justify="center" py={8}>
                  <Spinner size="lg" color={currentScheme.colors.primary} />
                </Flex>
              ) : filteredErrors.length === 0 ? (
                <Alert status="info">
                  <AlertIcon />
                  <AlertTitle>No errors found!</AlertTitle>
                  <AlertDescription>
                    {errors.length === 0
                      ? "No error logs are currently recorded."
                      : "No errors match your current filters."
                    }
                  </AlertDescription>
                </Alert>
              ) : (
                <Box overflowX="auto">
                  <Table variant="simple" size="sm">
                    <Thead>
                      <Tr>
                        <Th color={currentScheme.colors.textSecondary}>Time</Th> {/* Use theme color */}
                        <Th color={currentScheme.colors.textSecondary}>Level</Th> {/* Use theme color */}
                        <Th color={currentScheme.colors.textSecondary}>Source</Th> {/* Use theme color */}
                        <Th color={currentScheme.colors.textSecondary}>Message</Th> {/* Use theme color */}
                        <Th color={currentScheme.colors.textSecondary}>Actions</Th> {/* Use theme color */}
                      </Tr>
                    </Thead>
                    <Tbody>
                      {filteredErrors.map((error) => (
                        <>
                          <Tr key={error.id}>
                            <Td>
                              <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                                {new Date(error.timestamp).toLocaleString()}
                              </Text>
                            </Td>
                            <Td>
                              <Badge colorScheme={getLevelColor(error.level)} size="sm">
                                {error.level.toUpperCase()}
                              </Badge>
                            </Td>
                            <Td>
                              <Code fontSize="xs">{error.source}</Code>
                            </Td>
                            <Td>
                              <Text fontSize="sm" noOfLines={2} maxW="300px" color={currentScheme.colors.text}> {/* Use theme color */}
                                {error.message}
                              </Text>
                            </Td>
                            <Td>
                              <HStack spacing={1}>
                                <Tooltip label="View Details">
                                  <IconButton
                                    aria-label="View details"
                                    icon={<FiEye />}
                                    size="xs"
                                    variant="ghost"
                                    onClick={() => viewErrorDetails(error)}
                                  />
                                </Tooltip>
                                <Tooltip label={expandedRows.has(error.id) ? "Collapse" : "Expand"}>
                                  <IconButton
                                    aria-label="Toggle expansion"
                                    icon={expandedRows.has(error.id) ? <FiChevronUp /> : <FiChevronDown />}
                                    size="xs"
                                    variant="ghost"
                                    onClick={() => toggleRowExpansion(error.id)}
                                  />
                                </Tooltip>
                              </HStack>
                            </Td>
                          </Tr>
                          <Tr>
                            <Td colSpan={5} p={0}>
                              <Collapse in={expandedRows.has(error.id)} animateOpacity>
                                <Box p={4} bg={useColorModeValue('gray.50', 'gray.700')}>
                                  {error.stack && (
                                    <Box mb={3}>
                                      <Text fontSize="sm" fontWeight="medium" mb={2} color={currentScheme.colors.text}>Stack Trace:</Text> {/* Use theme color */}
                                      <Code display="block" whiteSpace="pre-wrap" fontSize="xs" p={2}>
                                        {error.stack}
                                      </Code>
                                    </Box>
                                  )}
                                  {error.metadata && Object.keys(error.metadata).length > 0 && (
                                    <Box>
                                      <Text fontSize="sm" fontWeight="medium" mb={2} color={currentScheme.colors.text}>Metadata:</Text> {/* Use theme color */}
                                      <Code display="block" whiteSpace="pre-wrap" fontSize="xs" p={2}>
                                        {JSON.stringify(error.metadata, null, 2)}
                                      </Code>
                                    </Box>
                                  )}
                                </Box>
                              </Collapse>
                            </Td>
                          </Tr>
                        </>
                      ))}
                    </Tbody>
                  </Table>
                </Box>
              )}
            </CardBody>
          </Card>
        </VStack>

        {/* Error Detail Modal */}
        <Modal isOpen={isDetailOpen} onClose={onDetailClose} size="xl">
          <ModalOverlay />
          <ModalContent bg={useColorModeValue('white', 'gray.800')}> {/* Match modal background */}
            <ModalHeader>Error Details</ModalHeader>
            <ModalCloseButton />
            <ModalBody>
              {selectedError && (
                <VStack spacing={4} align="stretch">
                  <HStack>
                    <Badge colorScheme={getLevelColor(selectedError.level)}>
                      {selectedError.level.toUpperCase()}
                    </Badge>
                    <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                      {new Date(selectedError.timestamp).toLocaleString()}
                    </Text>
                  </HStack>

                  <Box>
                    <Text fontWeight="medium" mb={2} color={currentScheme.colors.text}>Source:</Text> {/* Use theme color */}
                    <Code>{selectedError.source}</Code>
                  </Box>

                  <Box>
                    <Text fontWeight="medium" mb={2} color={currentScheme.colors.text}>Message:</Text> {/* Use theme color */}
                    <Text color={currentScheme.colors.text}>{selectedError.message}</Text> {/* Use theme color */}
                  </Box>

                  {selectedError.stack && (
                    <Box>
                      <Text fontWeight="medium" mb={2} color={currentScheme.colors.text}>Stack Trace:</Text> {/* Use theme color */}
                      <Code display="block" whiteSpace="pre-wrap" fontSize="sm" p={3}>
                        {selectedError.stack}
                      </Code>
                    </Box>
                  )}

                  {selectedError.metadata && (
                    <Box>
                      <Text fontWeight="medium" mb={2} color={currentScheme.colors.text}>Additional Data:</Text> {/* Use theme color */}
                      <Code display="block" whiteSpace="pre-wrap" fontSize="sm" p={3}>
                        {JSON.stringify(selectedError.metadata, null, 2)}
                      </Code>
                    </Box>
                  )}
                </VStack>
              )}
            </ModalBody>
            <ModalFooter>
              <Button onClick={onDetailClose}>Close</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Container>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  if (!session || !(session.user as any)?.isAdmin) {
    return {
      redirect: {
        destination: '/auth/signin',
        permanent: false,
      },
    };
  }

  return {
    props: {},
  };
};
