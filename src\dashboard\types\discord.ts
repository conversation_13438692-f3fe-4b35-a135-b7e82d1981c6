export interface DiscordEmbed {
  title?: string;
  description?: string;
  url?: string;
  color?: string;
  author?: {
    name?: string;
    url?: string;
    icon_url?: string;
  };
  thumbnail?: {
    url?: string;
  };
  image?: {
    url?: string;
  };
  footer?: {
    text?: string;
    icon_url?: string;
  };
  fields: {
    name: string;
    value: string;
    inline: boolean;
  }[];
  timestamp?: string;
}

export type ButtonStyle = 1 | 2 | 3 | 4 | 5; // Primary, Secondary, Success, Danger, Link

export interface DiscordButton {
  type: 2; // Button type
  style: ButtonStyle;
  label: string;
  custom_id?: string;
  url?: string;
  disabled?: boolean;
  emoji?: {
    name: string;
    id?: string;
    animated?: boolean;
  };
}

export interface DiscordSelectOption {
  label: string;
  value: string;
  description?: string;
  emoji?: {
    name: string;
    id?: string;
    animated?: boolean;
  };
  default?: boolean;
}

export interface DiscordSelectMenu {
  type: 3; // Select menu type
  custom_id: string;
  options: DiscordSelectOption[];
  placeholder?: string;
  min_values?: number;
  max_values?: number;
  disabled?: boolean;
}

export interface DiscordActionRow {
  type: 1; // Action row type
  components: (DiscordButton | DiscordSelectMenu)[];
}

export interface DiscordMessage {
  content?: string;
  embeds?: DiscordEmbed[];
  components?: DiscordActionRow[];
  flags?: number; // For container v2 support
} 