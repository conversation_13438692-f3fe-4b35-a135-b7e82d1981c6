(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5126],{63465:(e,t,n)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/signin",function(){return n(83765)}])},83765:(e,t,n)=>{"use strict";function o(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}n.r(t),n.d(t,{default:()=>v});var a=n(94513),i=n(95845),r=n(19521),s=n(51961),l=n(49217),c=n(79156),d=n(31678),x=n(41611),u=n(62690),b=n(610),f=n(53424),h=n(17842),p=n(94285);function g(){let e=o(["\n  0% { transform: translateY(0px) rotate(0deg); }\n  50% { transform: translateY(-20px) rotate(5deg); }\n  100% { transform: translateY(0px) rotate(0deg); }\n"]);return g=function(){return e},e}function m(){let e=o(["\n  0% { transform: scale(1); opacity: 0.8; }\n  50% { transform: scale(1.05); opacity: 1; }\n  100% { transform: scale(1); opacity: 0.8; }\n"]);return m=function(){return e},e}let j=["Welcome to 404 Chill Not Found - Where errors take a break!","Join our community of chill developers and gamers","Relax, you're in the right place","404 Error? More like 404 Fun Found!","Where coding meets community"],w=(0,b.i7)(g()),S=(0,b.i7)(m()),y="".concat(w," 3s ease-in-out infinite"),_="".concat(S," 3s ease-in-out infinite");function v(){let{isOpen:e,onOpen:t}=(0,i.j)(),[n,o]=(0,p.useState)(j[0]),{data:b}=(0,f.useSession)();return(0,p.useEffect)(()=>{o(j[Math.floor(Math.random()*j.length)]),t()},[t]),(0,a.jsxs)(r.s,{minH:"100vh",align:"center",justify:"center",bg:"gray.900",position:"relative",overflow:"hidden",children:[(0,a.jsx)(s.a,{position:"absolute",top:"10%",left:"5%",animation:y,opacity:.1,children:(0,a.jsx)(l.I,{as:h.FSj,boxSize:20})}),(0,a.jsx)(s.a,{position:"absolute",bottom:"15%",right:"10%",animation:y,opacity:.1,children:(0,a.jsx)(l.I,{as:h.Pcn,boxSize:16})}),(0,a.jsx)(s.a,{position:"absolute",top:"20%",right:"15%",animation:y,opacity:.1,children:(0,a.jsx)(l.I,{as:h.lHQ,boxSize:14})}),(0,a.jsx)(s.a,{position:"absolute",bottom:"20%",left:"15%",animation:y,opacity:.1,children:(0,a.jsx)(l.I,{as:h.vwk,boxSize:18})}),(0,a.jsxs)(c.T,{spacing:8,p:8,bg:"gray.800",borderRadius:"xl",boxShadow:"2xl",maxW:"md",w:"full",position:"relative",zIndex:1,children:[(0,a.jsx)(s.a,{animation:_,children:(0,a.jsx)(l.I,{as:h.y8Q,boxSize:20,color:"discord.500"})}),(0,a.jsx)(d.D,{size:"xl",textAlign:"center",bgGradient:"linear(to-r, discord.500, purple.500)",bgClip:"text",children:"404 Bot Dashboard"}),(0,a.jsx)(x.E,{fontSize:"lg",color:"gray.300",textAlign:"center",opacity:+!!e,transform:e?"translateY(0)":"translateY(20px)",transition:"all 0.5s ease",children:n}),b?(0,a.jsx)(u.$,{leftIcon:(0,a.jsx)(l.I,{as:h.O4U,boxSize:5}),size:"lg",w:"full",bg:"red.500",color:"white",onClick:()=>(0,f.signOut)({callbackUrl:"/signin"}),_hover:{bg:"red.600",transform:"translateY(-2px)",boxShadow:"0 0 20px rgba(229,62,62,0.6)"},_active:{transform:"translateY(0)",boxShadow:"0 0 10px rgba(229,62,62,0.4)"},transition:"all 0.2s ease",children:"Sign Out"}):(0,a.jsx)(u.$,{leftIcon:(0,a.jsx)(l.I,{as:h.O4U,boxSize:5}),size:"lg",w:"full",bg:"discord.500",color:"white",onClick:()=>(0,f.signIn)("discord",{callbackUrl:"/overview"}),_hover:{bg:"discord.600",transform:"translateY(-2px)",boxShadow:"0 0 20px rgba(88,101,242,0.6)"},_active:{transform:"translateY(0)",boxShadow:"0 0 10px rgba(88,101,242,0.4)"},transition:"all 0.2s ease",children:"Sign in with Discord"})]})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[3256,4976,636,6593,8792],()=>t(63465)),_N_E=e.O()}]);