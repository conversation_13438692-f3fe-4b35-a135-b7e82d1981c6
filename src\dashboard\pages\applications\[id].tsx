import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../pages/api/auth/[...nextauth]';
import Layout from '../../components/Layout';
import { Box, Heading, Text, VStack, Spinner, Center, Alert, AlertIcon, AlertDescription, SimpleGrid, Card, CardBody, FormControl, FormLabel, Input, Textarea, Select, RadioGroup, Radio, Checkbox, Button, useToast, HStack } from '@chakra-ui/react';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import React from 'react';

interface ApplicationData {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  enabled: boolean;
  questions: Question[];
  settings: ApplicationSettings;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

interface Question {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'number' | 'email';
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

interface ApplicationSettings {
  allowMultipleSubmissions: boolean;
  requireApproval: boolean;
  autoResponse: boolean;
  notificationChannels: string[];
  openingSchedule?: {
    enabled: boolean;
    startDate: string;
    endDate: string;
  };
}

export default function ApplicationPage() {
  const router = useRouter();
  const { id } = router.query;
  const [application, setApplication] = useState<ApplicationData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<{[key: string]: any}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const toast = useToast();

  useEffect(() => {
    if (id) {
      const fetchApplication = async () => {
        try {
          const res = await fetch(`/api/admin/applications-builder/${id}`);
          if (!res.ok) {
            throw new Error(`Failed to fetch application: ${res.statusText}`);
          }
          const data = await res.json();
          if (data.application) {
            setApplication(data.application);
            // Initialize form data with empty strings for text/textarea, or default for others
            const initialFormData: {[key: string]: any} = {};
            data.application.questions.forEach((q: Question) => {
              if (q.type === 'checkbox') {
                initialFormData[q.id] = [];
              } else if (q.type === 'select' || q.type === 'radio') {
                initialFormData[q.id] = q.options && q.options.length > 0 ? q.options[0] : '';
              } else {
                initialFormData[q.id] = '';
              }
            });
            setFormData(initialFormData);
          } else {
            setError('Application not found');
          }
        } catch (err: any) {
          setError(err.message);
        } finally {
          setLoading(false);
        }
      };
      fetchApplication();
    }
  }, [id]);

  const handleChange = (questionId: string, value: any, type: string) => {
    setFormData(prev => {
      if (type === 'checkbox') {
        const currentValues = prev[questionId] || [];
        if (currentValues.includes(value)) {
          return { ...prev, [questionId]: currentValues.filter((v: string) => v !== value) };
        } else {
          return { ...prev, [questionId]: [...currentValues, value] };
        }
      }
      return { ...prev, [questionId]: value };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    if (!application) {
      setError('Application data not loaded.');
      setIsSubmitting(false);
      return;
    }

    // Basic validation
    for (const question of application.questions) {
      if (question.required && (formData[question.id] === undefined || formData[question.id] === '' || (Array.isArray(formData[question.id]) && formData[question.id].length === 0))) {
        toast({
          title: 'Missing Required Field',
          description: `Please fill in the '${question.label}' field.`,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
        setIsSubmitting(false);
        return;
      }
    }

    try {
      const res = await fetch(`/api/applications/submit`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          applicationId: application.id,
          answers: formData,
        }),
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to submit application');
      }

      toast({
        title: 'Application Submitted',
        description: 'Your application has been successfully submitted!',
        status: 'success',
        duration: 5000,
        isClosable: true,
      });
      // Optionally redirect or clear form
      setFormData({}); // Clear form
    } catch (err: any) {
      setError(err.message);
      toast({
        title: 'Submission Failed',
        description: err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Layout>
        <Center p={8}>
          <VStack spacing={4}>
            <Spinner size="xl" />
            <Heading size="md">Loading application...</Heading>
          </VStack>
        </Center>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <Box p={8}>
          <Alert status="error">
            <AlertIcon />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        </Box>
      </Layout>
    );
  }

  if (!application) {
    return (
      <Layout>
        <Box p={8}>
          <Alert status="warning">
            <AlertIcon />
            <AlertDescription>Application data could not be loaded.</AlertDescription>
          </Alert>
        </Box>
      </Layout>
    );
  }

  return (
    <Layout>
      <VStack spacing={8} p={8} align="stretch">
        <Heading size="xl" textAlign="center" bgGradient="linear(to-r, blue.400, purple.400)" bgClip="text">
          {application.title}
        </Heading>
        <Text fontSize="lg" textAlign="center" color="gray.400">
          {application.description}
        </Text>

        <Box as="form" onSubmit={handleSubmit} mt={8} p={6} borderWidth="1px" borderRadius="lg" borderColor="whiteAlpha.300" bg="whiteAlpha.05">
          <Heading size="md" mb={6}>Application Form</Heading>
          <VStack spacing={6} align="stretch">
            {application.questions.map((q) => (
              <FormControl key={q.id} isRequired={q.required}>
                <FormLabel>{q.label}</FormLabel>
                {q.type === 'text' && (
                  <Input
                    type="text"
                    placeholder={q.placeholder}
                    value={formData[q.id] || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(q.id, e.target.value, q.type)}
                  />
                )}
                {q.type === 'number' && (
                  <Input
                    type="number"
                    placeholder={q.placeholder}
                    value={formData[q.id] || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleChange(q.id, e.target.value, q.type)}
                  />
                )}
                {q.type === 'textarea' && (
                  <Textarea
                    placeholder={q.placeholder}
                    value={formData[q.id] || ''}
                    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => handleChange(q.id, e.target.value, q.type)}
                  />
                )}
                {q.type === 'select' && q.options && (
                  <Select
                    placeholder={q.placeholder || 'Select an option'}
                    value={formData[q.id] || ''}
                    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => handleChange(q.id, e.target.value, q.type)}
                  >
                    {q.options.map((option) => (
                      <option key={option} value={option}>
                        {option}
                      </option>
                    ))}
                  </Select>
                )}
                {q.type === 'radio' && q.options && (
                  <RadioGroup
                    value={formData[q.id] || ''}
                    onChange={(val: string) => handleChange(q.id, val, q.type)}
                  >
                    <HStack spacing="24px">
                      {q.options.map((option) => (
                        <Radio key={option} value={option}>
                          {option}
                        </Radio>
                      ))}
                    </HStack>
                  </RadioGroup>
                )}
                {q.type === 'checkbox' && q.options && (
                  <VStack align="stretch">
                    {q.options.map((option) => (
                      <Checkbox
                        key={option}
                        isChecked={(formData[q.id] || []).includes(option)}
                        onChange={() => handleChange(q.id, option, q.type)}
                      >
                        {option}
                      </Checkbox>
                    ))}
                  </VStack>
                )}
                {/* Add other input types as needed (number, email, etc.) */}
              </FormControl>
            ))}
            <Button
              type="submit"
              colorScheme="blue"
              size="lg"
              isLoading={isSubmitting}
              loadingText="Submitting..."
              alignSelf="flex-end"
              mt={8}
            >
              Submit Application
            </Button>
          </VStack>
        </Box>

        {/* Removed Questions and Settings display cards as they are replaced by the form. Uncomment if needed elsewhere. */}
        {/*
        <SimpleGrid columns={{ base: 1, md: 2 }} spacing={8}>
          <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
            <CardBody>
              <Heading size="md" mb={4}>Questions</Heading>
              {application.questions.length > 0 ? (
                <VStack align="stretch" spacing={3}>
                  {application.questions.map((q, index) => (
                    <Box key={q.id || index} p={3} borderWidth="1px" borderRadius="md" borderColor="whiteAlpha.300">
                      <Text fontWeight="bold">{q.label}</Text>
                      <Text fontSize="sm" color="gray.400">Type: {q.type}</Text>
                      {q.required && <Text fontSize="xs" color="red.400">Required</Text>}
                    </Box>
                  ))}
                </VStack>
              ) : (
                <Text>No questions defined for this application.</Text>
              )}
            </CardBody>
          </Card>

          <Card bg="whiteAlpha.100" backdropFilter="blur(10px)">
            <CardBody>
              <Heading size="md" mb={4}>Settings</Heading>
              <VStack align="stretch" spacing={3}>
                <Text>Allow Multiple Submissions: {application.settings.allowMultipleSubmissions ? 'Yes' : 'No'}</Text>
                <Text>Require Approval: {application.settings.requireApproval ? 'Yes' : 'No'}</Text>
                <Text>Auto Response: {application.settings.autoResponse ? 'Yes' : 'No'}</Text>
                {application.settings.notificationChannels.length > 0 && (
                  <Text>Notification Channels: {application.settings.notificationChannels.join(', ')}</Text>
                )}
                {application.settings.openingSchedule?.enabled && (
                  <VStack align="stretch" spacing={1}>
                    <Text fontWeight="bold">Opening Schedule:</Text>
                    <Text>Start: {new Date(application.settings.openingSchedule.startDate).toLocaleString()}</Text>
                    <Text>End: {new Date(application.settings.openingSchedule.endDate).toLocaleString()}</Text>
                  </VStack>
                )}
              </VStack>
            </CardBody>
          </Card>
        </SimpleGrid>
        */}

      </VStack>
    </Layout>
  );
}

export const getServerSideProps: GetServerSideProps = async (context) => {
  const session = await getServerSession(context.req, context.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=' + encodeURIComponent(context.resolvedUrl),
        permanent: false,
      },
    };
  }

  // Check for admin access for this page
  const isAdmin = (session.user as any)?.isAdmin;
  if (!isAdmin) {
    return {
      redirect: {
        destination: '/unauthorized',
        permanent: false,
      },
    };
  }

  return { props: {} };
}; 