(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5449],{37771:(e,r,o)=>{"use strict";o.r(r),o.d(r,{__N_SSP:()=>O,default:()=>P});var t=o(94513),i=o(22907),l=o(51961),n=o(79156),a=o(31678),s=o(25680),d=o(68443),c=o(59818),h=o(7746),x=o(12183),p=o(78902),u=o(49217),b=o(56966),g=o(86153),m=o(84467),f=o(41611),j=o(98703),v=o(97146),y=o(60341);let w=[{id:"overview",title:"Overview",description:"View server statistics and general information.",icon:v.z1n,href:"/overview",color:"blue",gradient:{from:"rgba(49, 130, 206, 0.4)",to:"rgba(49, 130, 206, 0.1)"},accentColor:"#63B3ED"},{id:"gameservers",title:"Game Servers",description:"Manage and monitor your game servers. View status, add or edit server configurations.",icon:v.ufi,href:"/gameservers",color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accentColor:"#68D391"},{id:"applications",title:"Applications",description:"Review and manage guild applications. Process new members and handle requests.",icon:v.est,href:"/applications",color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accentColor:"#B794F4"},{id:"tickets",title:"Support Tickets",description:"Track and manage support tickets. Respond to user inquiries and resolve issues.",icon:v.lrG,href:"/tickets",color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accentColor:"#F6AD55"},{id:"moderation",title:"Moderation",description:"Tools and features for server moderators.",icon:v.F5$,href:"/moderation",color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"},accentColor:"#4FD1C5",requiredRole:"moderator"},{id:"experimental",title:"Experimental Features",description:"Try out new features that are still in development. These may not work as expected.",icon:v.VSk,href:"#",color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accentColor:"#F6E05E",experimental:!0,disabled:!0}];var S=o(94285),k=o(38618),C=o(39318),A=o(28482),z=o(92933),T=o(19483),E=o(7507),F=o(41460),D=o(61719),_=o(34144),R=o(36077),I=o(53424),M=o(24251),L=o.n(M);let N=e=>{let{title:r,description:o,icon:i,href:s,color:d,gradient:c,accentColor:h,disabled:x=!1,experimental:b=!1}=e,g=s&&"#"!==s&&!x,m=(0,t.jsx)(l.a,{px:10,py:5,bg:c?"linear-gradient(135deg, ".concat(c.from,", ").concat(c.to,")"):"gray.800",borderRadius:"lg",border:"1px solid",borderColor:x?"whiteAlpha.100":"whiteAlpha.200",transition:"all 0.3s",h:"140px",minW:"360px",w:"full",overflow:"hidden",display:"flex",flexDirection:"column",cursor:g?"pointer":x?"not-allowed":"default",position:"relative",opacity:x?.6:1,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:b?"repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)":"none",opacity:.5,pointerEvents:"none"},_hover:g?{transform:"translateY(-3px)",boxShadow:"0 6px 14px ".concat(h||"var(--chakra-colors-".concat(d,"-900)"),"40"),borderColor:"".concat(d,".400"),_before:{opacity:.7}}:{},children:(0,t.jsxs)(n.T,{spacing:4,align:"start",flex:"1",justify:"flex-start",h:"full",position:"relative",zIndex:1,children:[(0,t.jsxs)(p.z,{spacing:3,children:[(0,t.jsx)(u.I,{as:i,boxSize:6,color:h||"".concat(d,".300"),filter:b?"drop-shadow(0 0 2px currentColor)":"none"}),(0,t.jsx)(a.D,{size:"md",color:"white",noOfLines:1,whiteSpace:"nowrap",children:r})]}),(0,t.jsx)(f.E,{color:x?"gray.500":"gray.300",fontSize:"sm",lineHeight:"1.4",noOfLines:3,overflow:"hidden",textOverflow:"ellipsis",flex:"1",children:o})]})});return g?(0,t.jsx)(L(),{href:s,passHref:!0,children:m}):m};var B=o(65319),O=!0;function P(){let{data:e}=(0,I.useSession)(),[r,o]=(0,S.useState)(null),[M,O]=(0,S.useState)(!0),P=(0,i.d)();(0,S.useEffect)(()=>{(async()=>{try{let[e,r]=await Promise.all([fetch("/api/analytics/server"),fetch("/api/analytics/bot")]);if(!e.ok||!r.ok)throw Error("Failed to fetch analytics");let[t,i]=await Promise.all([e.json(),r.json()]);o({serverStats:t.serverStats,botStats:i.botStats})}catch(e){P({title:"Error",description:"Failed to load analytics data",status:"error",duration:5e3}),o({serverStats:{totalMembers:0,onlineMembers:0,totalChannels:0,totalRoles:0},botStats:{commandsToday:0,uptime:"Unknown",responseTime:"0ms",activeAddons:0,inactiveAddons:0}})}finally{O(!1)}})()},[P]);let Z=['"Talk is cheap. Show me the code." – Linus Torvalds','"Programs must be written for people to read, and only incidentally for machines to execute." – Harold Abelson','"Any fool can write code that a computer can understand. Good programmers write code that humans can understand." – Martin Fowler','"First, solve the problem. Then, write the code." – John Johnson','"404 Chill Not Found? Keep calm and debug on." – Unknown',"It's not a bug – it's an undocumented feature.",'"The best error message is the one that never shows up." – Thomas Fuchs',"Code is like humor. When you have to explain it, it's bad.",'"Experience is the name everyone gives to their mistakes." – Oscar Wilde','"In order to be irreplaceable, one must always be different." – Coco Chanel'];Z[new Date().getDate()%Z.length];let W=w.filter(r=>{var o,t;return"admin"===r.requiredRole?null==e||null==(o=e.user)?void 0:o.isAdmin:"moderator"===r.requiredRole?null==e||null==(t=e.user)?void 0:t.isModerator:!["overview","experimental","gameservers","applications","tickets"].includes(r.id)}),G=r?[{name:"Text",value:r.serverStats.textChannels||0,color:"#4299E1"},{name:"Voice",value:r.serverStats.voiceChannels||0,color:"#48BB78"},{name:"Categories",value:r.serverStats.categories||0,color:"#9F7AEA"}]:[],K=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],q=K.map(e=>({day:e,commands:0,joins:0,leaves:0})),U=r?K.map(e=>{var o,t,i,l;let n=(null==(t=r.botStats)||null==(o=t.weeklyActivity)?void 0:o.find(r=>r.day===e))||{},a=(null==(l=r.serverStats)||null==(i=l.weeklyMembers)?void 0:i.find(r=>r.day===e))||{};return{day:e,commands:n.commands||0,joins:a.joins||0,leaves:a.leaves||0}}):q;return(0,t.jsx)(y.A,{children:(0,t.jsxs)(l.a,{p:8,position:"relative",_before:{content:'""',position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"100%",height:"100%",background:"radial-gradient(circle at center, rgba(66, 153, 225, 0.1) 0%, transparent 70%)",pointerEvents:"none"},children:[(0,t.jsxs)(n.T,{spacing:8,mb:8,children:[(0,t.jsxs)(a.D,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,t.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"chart",children:"\uD83D\uDCCA"}),(0,t.jsx)(l.a,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Server Analytics"})]}),(0,t.jsx)(s.r,{columns:{base:1,md:2,lg:4},spacing:6,w:"full",children:M?Array.from({length:4}).map((e,r)=>(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsx)(h.E,{height:"80px"})})},r)):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(x.r,{children:[(0,t.jsxs)(p.z,{children:[(0,t.jsx)(u.I,{as:v.cfS,color:"blue.400",boxSize:6}),(0,t.jsx)(b.v,{color:"gray.300",children:"Total Members"})]}),(0,t.jsx)(g.k,{color:"white",fontSize:"2xl",children:(null==r?void 0:r.serverStats.totalMembers.toLocaleString())||"0"}),(0,t.jsxs)(m.h,{color:"green.400",children:[(0,t.jsx)(u.I,{as:v.ARf,mr:1}),(null==r?void 0:r.serverStats.onlineMembers)||"0"," online"]}),(0,t.jsxs)(m.h,{color:"green.300",children:["+",(null==r?void 0:r.serverStats.newMembersToday)||0," joined"]}),(0,t.jsxs)(m.h,{color:"red.400",children:["-",(null==r?void 0:r.serverStats.leftMembersToday)||0," left"]})]})})}),(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(x.r,{children:[(0,t.jsxs)(p.z,{children:[(0,t.jsx)(u.I,{as:v.mEP,color:"green.400",boxSize:6}),(0,t.jsx)(b.v,{color:"gray.300",children:"Channels"})]}),(0,t.jsx)(g.k,{color:"white",fontSize:"2xl",children:(null==r?void 0:r.serverStats.totalChannels)||"0"}),(0,t.jsxs)(m.h,{color:"gray.400",children:[(null==r?void 0:r.serverStats.totalRoles)||"0"," roles"]})]})})}),(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(x.r,{children:[(0,t.jsxs)(p.z,{children:[(0,t.jsx)(u.I,{as:v.z1n,color:"purple.400",boxSize:6}),(0,t.jsx)(b.v,{color:"gray.300",children:"Commands Today"})]}),(0,t.jsx)(g.k,{color:"white",fontSize:"2xl",children:(null==r?void 0:r.botStats.commandsToday)||"0"}),(0,t.jsxs)(m.h,{color:"gray.400",children:[(null==r?void 0:r.botStats.responseTime)||"0ms"," avg response"]})]})})}),(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(x.r,{children:[(0,t.jsxs)(p.z,{children:[(0,t.jsx)(u.I,{as:v.LIi,color:"orange.400",boxSize:6}),(0,t.jsx)(b.v,{color:"gray.300",children:"Bot Uptime"})]}),(0,t.jsx)(g.k,{color:"white",fontSize:"xl",children:(null==r?void 0:r.botStats.uptime)||"Unknown"}),(0,t.jsxs)(m.h,{color:"green.400",children:[(null==r?void 0:r.botStats.activeAddons)||"0"," addons active"]}),(0,t.jsxs)(m.h,{color:"red.400",children:[(null==r?void 0:r.botStats.inactiveAddons)||"0"," addons inactive"]})]})})}),(null==r?void 0:r.botStats.errorsToday)>0&&(0,t.jsx)(B.N,{as:L(),href:"/admin/errors",_hover:{textDecoration:"none"},w:"full",children:(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",borderColor:"red.400",borderWidth:"1px",cursor:"pointer",_hover:{transform:"translateY(-4px)",boxShadow:"0 4px 12px rgba(0,0,0,0.2)",borderColor:"red.500"},children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(x.r,{children:[(0,t.jsxs)(p.z,{children:[(0,t.jsx)(u.I,{as:v.y3G,color:"red.400",boxSize:6}),(0,t.jsx)(b.v,{color:"gray.300",children:"Errors Today"})]}),(0,t.jsx)(g.k,{color:"red.400",fontSize:"2xl",children:r.botStats.errorsToday}),(0,t.jsx)(m.h,{color:"red.300",children:"Needs attention"})]})})})})]})})]}),!M&&r&&(0,t.jsxs)(n.T,{spacing:8,mb:8,children:[(0,t.jsxs)(a.D,{size:"lg",textAlign:"center",display:"flex",alignItems:"center",justifyContent:"center",children:[(0,t.jsx)("span",{style:{marginRight:"0.5rem"},role:"img","aria-label":"graph",children:"\uD83D\uDCC8"}),(0,t.jsx)(l.a,{as:"span",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:"Activity Overview"})]}),(0,t.jsxs)(s.r,{columns:{base:1,lg:2},spacing:8,w:"full",children:[(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(n.T,{spacing:4,children:[(0,t.jsx)(a.D,{size:"md",color:"white",children:"Channel Distribution"}),(0,t.jsx)(l.a,{h:"200px",w:"full",children:(0,t.jsx)(k.u,{width:"100%",height:"100%",children:(0,t.jsxs)(C.r,{children:[(0,t.jsx)(A.F,{data:G,cx:"50%",cy:"50%",innerRadius:40,outerRadius:80,paddingAngle:5,dataKey:"value",children:G.map((e,r)=>(0,t.jsx)(z.f,{fill:e.color},"cell-".concat(r)))}),(0,t.jsx)(T.m,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"}})]})})}),(0,t.jsx)(p.z,{spacing:4,justify:"center",children:G.map((e,r)=>(0,t.jsxs)(p.z,{spacing:2,children:[(0,t.jsx)(l.a,{w:"3",h:"3",bg:e.color,rounded:"full"}),(0,t.jsxs)(f.E,{fontSize:"sm",color:"gray.300",children:[e.name,": ",e.value]})]},r))})]})})}),(0,t.jsx)(d.Z,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",children:(0,t.jsx)(c.b,{children:(0,t.jsxs)(n.T,{spacing:4,children:[(0,t.jsx)(a.D,{size:"md",color:"white",children:"Weekly Activity"}),(0,t.jsx)(l.a,{h:"200px",w:"full",children:(0,t.jsx)(k.u,{width:"100%",height:"100%",children:(0,t.jsxs)(E.E,{data:U,children:[(0,t.jsx)(F.d,{strokeDasharray:"3 3",stroke:"rgba(255,255,255,0.1)"}),(0,t.jsx)(D.W,{dataKey:"day",axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,t.jsx)(_.h,{axisLine:!1,tickLine:!1,tick:{fill:"#A0AEC0",fontSize:12}}),(0,t.jsx)(T.m,{wrapperStyle:{backgroundColor:"transparent"},contentStyle:{backgroundColor:"rgba(26, 32, 44, 0.9)",border:"1px solid rgba(255,255,255,0.2)",borderRadius:"8px",color:"#fff"},itemStyle:{color:"#fff"},labelStyle:{color:"#fff"},cursor:{fill:"rgba(255,255,255,0.08)"}}),(0,t.jsx)(R.y,{dataKey:"commands",fill:"#4299E1",name:"Commands"}),(0,t.jsx)(R.y,{dataKey:"joins",fill:"#48BB78",name:"Joins"}),(0,t.jsx)(R.y,{dataKey:"leaves",fill:"#E53E3E",name:"Leaves"})]})})})]})})})]})]}),(0,t.jsx)(j.B,{spacing:"24px",justify:"start",children:W.map(e=>(0,t.jsx)(j.Q,{flex:"1 0 260px",children:(0,t.jsx)(N,{...e})},e.id))})]})})}},54067:(e,r,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/overview",function(){return o(37771)}])}},e=>{var r=r=>e(e.s=r);e.O(0,[4108,4976,217,2965,6476,237,341,636,6593,8792],()=>r(54067)),_N_E=e.O()}]);