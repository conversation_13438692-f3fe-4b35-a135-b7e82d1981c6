import {
  Box,
  Heading,
  Text,
  HStack,
  VStack,
  Badge,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  Stack,
  IconButton,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  useToast,
  Switch,
  FormControl,
  FormLabel,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
  Avatar,
  Button,
  Divider,
  List,
  ListItem,
  Icon,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Spinner,
  Alert,
  AlertIcon,
  AlertDescription,
} from '@chakra-ui/react';
import Layout from '../../components/Layout';
import { useState, useEffect } from 'react';
import { FiMoreVertical, FiCheckCircle, FiXCircle, FiClock, FiUser, FiCalendar, FiGlobe, FiClock as FiTime, FiTrash2, FiSettings } from 'react-icons/fi';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../api/auth/[...nextauth]';
import { FaFileAlt, FaFlask } from 'react-icons/fa';

interface ApplicationSubmission {
  _id: string;
  applicationId: string;
  submittedBy: string;
  submittedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  answers: Record<string, string | string[]>;
  reviewedBy?: string;
  reviewedAt?: string;
}

interface ApplicationStats {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  recentIncrease: number;
}

export default function Applications() {
  const [applications, setApplications] = useState<ApplicationSubmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<ApplicationStats>({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    recentIncrease: 0
  });
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [selectedApp, setSelectedApp] = useState<ApplicationSubmission | null>(null);

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      const response = await fetch('/api/admin/applications');
      if (response.ok) {
        const data = await response.json();
        setApplications(data.applications || []);
        setStats(data.stats || stats);
      }
    } catch (error) {
      console.error('Failed to fetch applications:', error);
      toast({
        title: 'Error',
        description: 'Failed to load applications',
        status: 'error',
        duration: 3000,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApplicationAction = async (submissionId: string, action: 'approve' | 'reject') => {
    try {
      const endpoint = '/api/admin/applications';
      const response = await fetch(`${endpoint}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ applicationSubmissionId: submissionId, action }),
      });

      if (response.ok) {
        fetchApplications();
        toast({
          title: 'Success',
          description: `Application ${action}d successfully`,
          status: 'success',
          duration: 3000,
        });
        onClose();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: `Failed to ${action} application`,
        status: 'error',
        duration: 3000,
      });
    }
  };

  const viewApplication = (app: ApplicationSubmission) => {
    setSelectedApp(app);
    onOpen();
  };

  if (loading) {
    return (
      <Layout>
        <Box p={8} display="flex" justifyContent="center" alignItems="center" minH="400px">
          <Spinner size="xl" />
        </Box>
      </Layout>
    );
  }

  return (
    <Layout>
      <Box p={8}>
        <VStack align="stretch" spacing={6}>
          <HStack>
            <Icon as={FaFileAlt} boxSize={6} color="blue.500" />
            <Heading size="lg">Applications Management</Heading>
          </HStack>

          <Text color="gray.600" _dark={{ color: 'gray.300' }}>
            Manage and review all user applications submitted through the application builder.
          </Text>

          {/* Stats Cards */}
          <SimpleGrid columns={{ base: 1, md: 4 }} spacing={6}>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Total Applications</StatLabel>
                  <StatNumber>{stats.total}</StatNumber>
                  <StatHelpText>
                    <StatArrow type={stats.recentIncrease >= 0 ? 'increase' : 'decrease'} />
                    {Math.abs(stats.recentIncrease)}% this month
                  </StatHelpText>
                </Stat>
              </CardBody>
            </Card>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Pending Review</StatLabel>
                  <StatNumber color="yellow.500">{stats.pending}</StatNumber>
                  <StatHelpText>Requires attention</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Approved</StatLabel>
                  <StatNumber color="green.500">{stats.approved}</StatNumber>
                  <StatHelpText>Accepted applications</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
            <Card>
              <CardBody>
                <Stat>
                  <StatLabel>Rejected</StatLabel>
                  <StatNumber color="red.500">{stats.rejected}</StatNumber>
                  <StatHelpText>Declined applications</StatHelpText>
                </Stat>
              </CardBody>
            </Card>
          </SimpleGrid>

          <Box mt={8}>
            {applications.length === 0 ? (
              <Alert status="info">
                <AlertIcon />
                <AlertDescription>
                  No applications found. Applications submitted through the builder will appear here.
                </AlertDescription>
              </Alert>
            ) : (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>User</Th>
                    <Th>Application Type</Th>
                    <Th>Submitted On</Th>
                    <Th>Status</Th>
                    <Th>Actions</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {applications.map((app) => (
                    <Tr key={app._id}>
                      <Td>
                        <HStack>
                          <Avatar size="sm" name={app.submittedBy} />
                          <Text>{app.submittedBy}</Text>
                        </HStack>
                      </Td>
                      <Td>
                        <Badge colorScheme="purple">Custom Application: {app.applicationId}</Badge>
                      </Td>
                      <Td>{new Date(app.submittedAt).toLocaleDateString()}</Td>
                      <Td>
                        <Badge
                          colorScheme={
                            app.status === 'approved' ? 'green' :
                            app.status === 'rejected' ? 'red' : 'yellow'
                          }
                        >
                          {app.status}
                        </Badge>
                      </Td>
                      <Td>
                        <HStack spacing={2}>
                          <Button size="sm" colorScheme="blue" onClick={() => viewApplication(app)}>
                            View
                          </Button>
                          {app.status === 'pending' && (
                            <>
                              <Button
                                size="sm"
                                colorScheme="green"
                                onClick={() => handleApplicationAction(app._id, 'approve')}
                              >
                                Accept
                              </Button>
                              <Button
                                size="sm"
                                colorScheme="red"
                                onClick={() => handleApplicationAction(app._id, 'reject')}
                              >
                                Reject
                              </Button>
                            </>
                          )}
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </Box>
        </VStack>
      </Box>

      {/* Application Detail Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Application Details</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedApp && (
              <VStack align="stretch" spacing={4}>
                <HStack justifyContent="space-between">
                  <Text fontWeight="bold">Submitted By:</Text>
                  <Text>{selectedApp.submittedBy}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontWeight="bold">Submitted On:</Text>
                  <Text>{new Date(selectedApp.submittedAt).toLocaleDateString()}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontWeight="bold">Application Type:</Text>
                  <Text>Custom Application: {selectedApp.applicationId}</Text>
                </HStack>
                <HStack justifyContent="space-between">
                  <Text fontWeight="bold">Status:</Text>
                  <Badge
                    colorScheme={
                      selectedApp.status === 'approved' ? 'green' :
                      selectedApp.status === 'rejected' ? 'red' : 'yellow'
                    }
                  >
                    {selectedApp.status}
                  </Badge>
                </HStack>
                <Divider />
                <Text fontWeight="bold">Answers:</Text>
                <List spacing={2}>
                  {Object.entries(selectedApp.answers).map(([question, answer]) => (
                    <ListItem key={question}>
                      <Text><strong>{question}:</strong> {Array.isArray(answer) ? answer.join(', ') : answer}</Text>
                    </ListItem>
                  ))}
                </List>
                {selectedApp.reviewedBy && (
                  <HStack justifyContent="space-between">
                    <Text fontWeight="bold">Reviewed By:</Text>
                    <Text>{selectedApp.reviewedBy}</Text>
                  </HStack>
                )}
                {selectedApp.reviewedAt && (
                  <HStack justifyContent="space-between">
                    <Text fontWeight="bold">Reviewed At:</Text>
                    <Text>{new Date(selectedApp.reviewedAt).toLocaleDateString()}</Text>
                  </HStack>
                )}
              </VStack>
            )}
          </ModalBody>
        </ModalContent>
      </Modal>
    </Layout>
  );
}

// Server-side guard
export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);

  if (!session) {
    return {
      redirect: {
        destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fapplications',
        permanent: false,
      },
    };
  }

  return { props: { session } };
}; 