(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[217],{3539:(e,t,n)=>{"use strict";n.d(t,{bq:()=>a,mD:()=>o});var r=n(93487);function o(e){return i(e)?.defaultView??window}function i(e){return(0,r.sb)(e)?e.ownerDocument:document}function a(e){return i(e).activeElement}},7753:(e,t,n)=>{"use strict";n.d(t,{Xu:()=>b,BV:()=>w,b:()=>k,db:()=>C,KZ:()=>O,Os:()=>S,jy:()=>N,F9:()=>j});var r=n(95845),o=n(76362),i=n(31524),a=n(89630),s=n(94285),l=n(50227),u=n(78961),c=n(29035),d=n(81405),f=n(50614),p=n(18654),m=n(87888),h=n(88550),v=n(70011);let[b,y,g,x]=(0,m.D)(),[w,O]=(0,c.q)({strict:!1,name:"MenuContext"});function E(e){return e?.ownerDocument??document}function k(e={}){let{id:t,closeOnSelect:n=!0,closeOnBlur:u=!0,initialFocusRef:c,autoSelect:d=!0,isLazy:f,isOpen:p,defaultIsOpen:m,onClose:v,onOpen:b,placement:y="bottom-start",lazyBehavior:x="unmount",direction:w,computePositionOnMount:O=!1,...C}=e,D=(0,s.useRef)(null),N=(0,s.useRef)(null),j=(0,s.useRef)(!0),S=g(),P=(0,s.useCallback)(()=>{requestAnimationFrame(()=>{D.current?.focus({preventScroll:!1})})},[]),M=(0,s.useCallback)(()=>{let e=setTimeout(()=>{if(c)c.current?.focus();else if(S.count()){let e=S.firstEnabled();e&&H(e.index)}else D.current?.focus({preventScroll:!1})});U.current.add(e)},[S,c]),I=(0,s.useCallback)(()=>{let e=setTimeout(()=>{if(S.count()){let e=S.lastEnabled();e&&H(e.index)}else D.current?.focus({preventScroll:!1})});U.current.add(e)},[S]),T=(0,s.useCallback)(()=>{b?.(),d?M():P()},[d,M,P,b]),{isOpen:_,onOpen:R,onClose:A,onToggle:L}=(0,r.j)({isOpen:p,defaultIsOpen:m,onClose:v,onOpen:T});(0,o.j)({enabled:_&&u,ref:D,handler:e=>{let t=e.composedPath?.()?.[0]??e.target;N.current?.contains(t)||A()}});let B=(0,h.E)({...C,enabled:_||O,placement:y,direction:w}),[W,H]=(0,s.useState)(-1);(0,i.X)(D,{focusRef:N,visible:_,shouldFocus:!0});let z=(0,a.v)({isOpen:_,ref:D}),[V,q]=function(e,...t){let n=function(e,t){let n=(0,s.useId)();return(0,s.useMemo)(()=>e||[void 0,n].filter(Boolean).join("-"),[e,void 0,n])}(e);return(0,s.useMemo)(()=>t.map(e=>`${e}-${n}`),[n,t])}(t,"menu-button","menu-list"),F=(0,s.useCallback)(()=>{R(),P()},[R,P]),U=(0,s.useRef)(new Set([]));(0,s.useEffect)(()=>{let e=U.current;return()=>{e.forEach(e=>clearTimeout(e)),e.clear()}},[]),(0,l.w)(()=>{_||(H(-1),D.current?.scrollTo(0,0))},[_]),(0,l.w)(()=>{_&&-1===W&&P()},[W,_]),(0,s.useEffect)(()=>{if(!_)return;let e=S.item(W);e?.node?.focus({preventScroll:!j.current})},[S,W,_]);let $=(0,s.useCallback)(()=>{R(),M()},[M,R]);return{openAndFocusMenu:F,openAndFocusFirstItem:$,openAndFocusLastItem:(0,s.useCallback)(()=>{j.current=!0,R(),I()},[R,I]),onTransitionEnd:(0,s.useCallback)(()=>{let e=E(D.current),t=D.current?.contains(e.activeElement);if(!(_&&!t))return;let n=S.item(W)?.node;n?.focus({preventScroll:!j.current})},[_,W,S]),unstable__animationState:z,descendants:S,popper:B,buttonId:V,menuId:q,forceUpdate:B.forceUpdate,orientation:"vertical",isOpen:_,onToggle:L,onOpen:R,onClose:A,menuRef:D,buttonRef:N,focusedIndex:W,closeOnSelect:n,closeOnBlur:u,autoSelect:d,setFocusedIndex:H,isLazy:f,lazyBehavior:x,initialFocusRef:c,scrollIntoViewRef:j}}function C(e={},t=null){let n=O(),{onToggle:r,popper:o,openAndFocusFirstItem:i,openAndFocusLastItem:a,scrollIntoViewRef:l}=n,c=(0,s.useCallback)(e=>{let t={Enter:i,ArrowDown:i,ArrowUp:a}[e.key];t&&(l.current=!0,e.preventDefault(),e.stopPropagation(),t(e))},[i,a,l]);return{...e,ref:(0,u.Px)(n.buttonRef,t,o.referenceRef),id:n.buttonId,"data-active":(0,d.s)(n.isOpen),"aria-expanded":n.isOpen,"aria-haspopup":"menu","aria-controls":n.menuId,onClick:(0,f.H)(e.onClick,r),onKeyDown:(0,f.H)(e.onKeyDown,c)}}function D(e){return function(e){var t;if(!(null!=(t=e)&&"object"==typeof t&&"nodeType"in t&&t.nodeType===Node.ELEMENT_NODE))return!1;let n=e.ownerDocument.defaultView??window;return e instanceof n.HTMLElement}(e)&&!!e?.getAttribute("role")?.startsWith("menuitem")}function N(e={},t=null){let n=O();if(!n)throw Error("useMenuContext: context is undefined. Seems you forgot to wrap component within <Menu>");let{focusedIndex:r,setFocusedIndex:o,menuRef:i,isOpen:a,onClose:l,menuId:c,isLazy:d,lazyBehavior:m,scrollIntoViewRef:h,unstable__animationState:v}=n,b=y(),g=function(e={}){let{timeout:t=300,preventDefault:n=()=>!0}=e,[r,o]=(0,s.useState)([]),i=(0,s.useRef)(void 0),a=()=>{i.current&&(clearTimeout(i.current),i.current=null)},l=()=>{a(),i.current=setTimeout(()=>{o([]),i.current=null},t)};return(0,s.useEffect)(()=>a,[]),function(e){return t=>{if("Backspace"===t.key){let e=[...r];e.pop(),o(e);return}if(function(e){let{key:t}=e;return 1===t.length||t.length>1&&/[^a-zA-Z0-9]/.test(t)}(t)){let i=r.concat(t.key);n(t)&&(t.preventDefault(),t.stopPropagation()),o(i),e(i.join("")),l()}}}}({preventDefault:e=>" "!==e.key&&D(e.target)}),x=(0,s.useCallback)(e=>{if(!e.currentTarget.contains(e.target))return;let t={Tab:e=>e.preventDefault(),Escape:e=>{e.stopPropagation(),l()},ArrowDown:()=>{h.current=!0;let e=b.nextEnabled(r)??b.firstEnabled();e&&o(e.index)},ArrowUp:()=>{h.current=!0;let e=b.prevEnabled(r)??b.firstEnabled();e&&o(e.index)}}[e.key];if(t){e.preventDefault(),t(e);return}let n=g(e=>{let t=function(e,t,n,r){if(null==t)return r;if(!r)return e.find(e=>n(e).toLowerCase().startsWith(t.toLowerCase()));let o=e.filter(e=>n(e).toLowerCase().startsWith(t.toLowerCase()));if(o.length>0){let t;return o.includes(r)?((t=o.indexOf(r)+1)===o.length&&(t=0),o[t]):(t=e.indexOf(o[0]),e[t])}return r}(b.values(),e,e=>e?.node?.textContent??"",b.item(r));t&&o(b.indexOf(t.node))});D(e.target)&&n(e)},[b,r,g,l,o,h]),w=(0,s.useRef)(!1);a&&(w.current=!0);let E=(0,p.q)({wasSelected:w.current,enabled:d,mode:m,isSelected:v.present});return{...e,ref:(0,u.Px)(i,t),children:E?e.children:null,tabIndex:-1,role:"menu",id:c,style:{...e.style,transformOrigin:"var(--popper-transform-origin)"},"aria-orientation":"vertical",onKeyDown:(0,f.H)(e.onKeyDown,x)}}function j(e={}){let{popper:t,isOpen:n}=O();return t.getPopperProps({...e,style:{visibility:n?"visible":"hidden",...e.style}})}function S(e={},t=null){let{onMouseEnter:n,onMouseMove:r,onMouseLeave:o,onClick:i,onFocus:a,isDisabled:l,isFocusable:c,closeOnSelect:d,type:f,...p}=e,{setFocusedIndex:m,focusedIndex:h,closeOnSelect:b,onClose:y,menuId:g,scrollIntoViewRef:w}=O(),k=(0,s.useRef)(null),C=`${g}-menuitem-${(0,s.useId)()}`,{index:N,register:j}=x({disabled:l&&!c}),P=(0,s.useCallback)(e=>{n?.(e),l||(w.current=!1,m(N))},[m,N,l,n,w]),M=(0,s.useCallback)(e=>{var t;r?.(e),k.current&&E(t=k.current).activeElement!==t&&P(e)},[P,r]),I=(0,s.useCallback)(e=>{o?.(e),l||m(-1)},[m,l,o]),T=(0,s.useCallback)(e=>{i?.(e),D(e.currentTarget)&&(d??b)&&y()},[y,i,b,d]),_=(0,s.useCallback)(e=>{a?.(e),m(N)},[m,a,N]),R=N===h,A=(0,v.I)({onClick:T,onFocus:_,onMouseEnter:P,onMouseMove:M,onMouseLeave:I,ref:(0,u.Px)(j,k,t),isDisabled:l,isFocusable:c});return{...p,...A,type:f??A.type,id:C,role:"menuitem",tabIndex:R?0:-1}}},15294:(e,t,n)=>{"use strict";n.d(t,{ep:()=>a});var r=n(62937);let o="input:not(:disabled):not([disabled]),select:not(:disabled):not([disabled]),textarea:not(:disabled):not([disabled]),embed,iframe,object,a[href],area[href],button:not(:disabled):not([disabled]),[tabindex],audio[controls],video[controls],*[tabindex]:not([aria-disabled]),*[contenteditable]",i=e=>e.offsetWidth>0&&e.offsetHeight>0;function a(e){let t=Array.from(e.querySelectorAll(o));return t.unshift(e),t.filter(e=>(0,r.tp)(e)&&i(e))}},18480:(e,t,n)=>{"use strict";n.d(t,{c:()=>p});var r=n(94513),o=n(22697),i=n(50614),a=n(67233),s=n(91169),l=n(7753),u=n(33225),c=n(2923);let d={enter:{visibility:"visible",opacity:1,scale:1,transition:{duration:.2,ease:[.4,0,.2,1]}},exit:{transitionEnd:{visibility:"hidden"},opacity:0,scale:.8,transition:{duration:.1,easings:"easeOut"}}},f=(0,u.B)(a.P.div),p=(0,c.R)(function(e,t){let{rootProps:n,motionProps:a,...c}=e,{isOpen:p,onTransitionEnd:m,unstable__animationState:h}=(0,l.KZ)(),v=(0,l.jy)(c,t),b=(0,l.F9)(n),y=(0,s.$)();return(0,r.jsx)(u.B.div,{...b,__css:{zIndex:e.zIndex??y.list?.zIndex},children:(0,r.jsx)(f,{variants:d,initial:!1,animate:p?"enter":"exit",__css:{outline:0,...y.list},...a,...v,className:(0,o.cx)("chakra-menu__menu-list",v.className),onUpdate:m,onAnimationComplete:(0,i.O)(h.onComplete,v.onAnimationComplete)})})});p.displayName="MenuList"},18654:(e,t,n)=>{"use strict";function r(e){let{wasSelected:t,enabled:n,isSelected:r,mode:o="unmount"}=e;return!n||!!r||"keepMounted"===o&&!!t}n.d(t,{q:()=>r})},24792:(e,t,n)=>{"use strict";n.d(t,{m:()=>j});var r=n(94513),o=n(75387),i=n(25195),a=n(60455),s=n(67233),l=n(63203),u=n(94285),c=n(29800);let d={exit:{scale:.85,opacity:0,transition:{opacity:{duration:.15,easings:"easeInOut"},scale:{duration:.2,easings:"easeInOut"}}},enter:{scale:1,opacity:1,transition:{opacity:{easings:"easeOut",duration:.2},scale:{duration:.2,ease:[.175,.885,.4,1.1]}}}};var f=n(95845),p=n(40747),m=n(78961),h=n(93487),v=n(50614),b=n(88550),y=n(27850);let g=e=>e.current?.ownerDocument||document,x=e=>e.current?.ownerDocument?.defaultView||window,w="chakra-ui:close-tooltip";var O=n(83745),E=n(52831),k=n(33225),C=n(2923),D=n(56915);let N=(0,k.B)(s.P.div),j=(0,C.R)((e,t)=>{let n,s=(0,D.V)("Tooltip",e),C=(0,o.M)(e),j=(0,O.D)(),{children:S,label:P,shouldWrapChildren:M,"aria-label":I,hasArrow:T,bg:_,portalProps:R,background:A,backgroundColor:L,bgColor:B,motionProps:W,animatePresenceProps:H,...z}=C,V=A??L??_??B;if(V){s.bg=V;let e=function(e,t,n){return e.__cssMap?.[`${t}.${n}`]?.varRef??n}(j,"colors",V);s[y.O3.arrowBg.var]=e}let q=function(e={}){var t,n;let{openDelay:r=0,closeDelay:o=0,closeOnClick:i=!0,closeOnMouseDown:a,closeOnScroll:s,closeOnPointerDown:l=a,closeOnEsc:c=!0,onOpen:d,onClose:O,placement:E,id:k,isOpen:C,defaultIsOpen:D,arrowSize:N=10,arrowShadowColor:j,arrowPadding:S,modifiers:P,isDisabled:M,gutter:I,offset:T,direction:_,...R}=e,{isOpen:A,onOpen:L,onClose:B}=(0,f.j)({isOpen:C,defaultIsOpen:D,onOpen:d,onClose:O}),{referenceRef:W,getPopperProps:H,getArrowInnerProps:z,getArrowProps:V}=(0,b.E)({enabled:A,placement:E,arrowPadding:S,modifiers:P,gutter:I,offset:T,direction:_}),q=(0,u.useId)(),F=`tooltip-${k??q}`,U=(0,u.useRef)(null),$=(0,u.useRef)(void 0),K=(0,u.useCallback)(()=>{$.current&&(clearTimeout($.current),$.current=void 0)},[]),X=(0,u.useRef)(void 0),Y=(0,u.useCallback)(()=>{X.current&&(clearTimeout(X.current),X.current=void 0)},[]),Z=(0,u.useCallback)(()=>{Y(),B()},[B,Y]),G=(t=U,n=Z,(0,u.useEffect)(()=>{let e=g(t);return e.addEventListener(w,n),()=>e.removeEventListener(w,n)},[n,t]),()=>{let e=g(t),n=x(t);e.dispatchEvent(new n.CustomEvent(w))}),Q=(0,u.useCallback)(()=>{M||$.current||(A&&G(),$.current=x(U).setTimeout(L,r))},[G,M,A,L,r]),J=(0,u.useCallback)(()=>{K(),X.current=x(U).setTimeout(Z,o)},[o,Z,K]),ee=(0,u.useCallback)(()=>{A&&i&&J()},[i,J,A]),et=(0,u.useCallback)(()=>{A&&l&&J()},[l,J,A]),en=(0,u.useCallback)(e=>{A&&"Escape"===e.key&&J()},[A,J]);(0,p.M)(()=>g(U),"keydown",c?en:void 0),(0,p.M)(()=>{if(!s)return null;let e=U.current;if(!e)return null;let t=function e(t){return["html","body","#document"].includes(t.localName)?t.ownerDocument.body:(0,h.sb)(t)&&function(e){let{overflow:t,overflowX:n,overflowY:r}=(e.ownerDocument.defaultView||window).getComputedStyle(e);return/auto|scroll|overlay|hidden/.test(t+r+n)}(t)?t:e("html"===t.localName?t:t.assignedSlot||t.parentElement||t.ownerDocument.documentElement)}(e);return"body"===t.localName?x(U):t},"scroll",()=>{A&&s&&Z()},{passive:!0,capture:!0}),(0,u.useEffect)(()=>{M&&(K(),A&&B())},[M,A,B,K]),(0,u.useEffect)(()=>()=>{K(),Y()},[K,Y]),(0,p.M)(()=>U.current,"pointerleave",J);let er=(0,u.useCallback)((e={},t=null)=>({...e,ref:(0,m.Px)(U,t,W),onPointerEnter:(0,v.H)(e.onPointerEnter,e=>{"touch"!==e.pointerType&&Q()}),onClick:(0,v.H)(e.onClick,ee),onPointerDown:(0,v.H)(e.onPointerDown,et),onFocus:(0,v.H)(e.onFocus,Q),onBlur:(0,v.H)(e.onBlur,J),"aria-describedby":A?F:void 0}),[Q,J,et,A,F,ee,W]),eo=(0,u.useCallback)((e={},t=null)=>H({...e,style:{...e.style,[y.O3.arrowSize.var]:N?`${N}px`:void 0,[y.O3.arrowShadowColor.var]:j}},t),[H,N,j]);return{isOpen:A,show:Q,hide:J,getTriggerProps:er,getTooltipProps:(0,u.useCallback)((e={},t=null)=>{let n={...e.style,position:"relative",transformOrigin:y.O3.transformOrigin.varRef};return{ref:t,...R,...e,id:F,role:"tooltip",style:n}},[R,F]),getTooltipPositionerProps:eo,getArrowProps:V,getArrowInnerProps:z}}({...z,direction:j.direction});if(!(0,u.isValidElement)(S)||M)n=(0,r.jsx)(k.B.span,{display:"inline-block",tabIndex:0,...q.getTriggerProps(),children:S});else{let e=u.Children.only(S);n=(0,u.cloneElement)(e,q.getTriggerProps(e.props,(0,c.Q)(e)))}let F=!!I,U=q.getTooltipProps({},t),$=F?(0,i.c)(U,["role","id"]):U,K=(0,a.U)(U,["role","id"]);return P?(0,r.jsxs)(r.Fragment,{children:[n,(0,r.jsx)(l.N,{...H,children:q.isOpen&&(0,r.jsx)(E.Z,{...R,children:(0,r.jsx)(k.B.div,{...q.getTooltipPositionerProps(),__css:{zIndex:s.zIndex,pointerEvents:"none"},children:(0,r.jsxs)(N,{variants:d,initial:"exit",animate:"enter",exit:"exit",...W,...$,__css:s,children:[P,F&&(0,r.jsx)(k.B.span,{srOnly:!0,...K,children:I}),T&&(0,r.jsx)(k.B.div,{"data-popper-arrow":!0,className:"chakra-tooltip__arrow-wrapper",children:(0,r.jsx)(k.B.div,{"data-popper-arrow-inner":!0,className:"chakra-tooltip__arrow",__css:{bg:s.bg}})})]})})})})]}):(0,r.jsx)(r.Fragment,{children:S})});j.displayName="Tooltip"},27850:(e,t,n)=>{"use strict";n.d(t,{Fs:()=>u,Ky:()=>i,O3:()=>o,ed:()=>s});let r=(e,t)=>({var:e,varRef:t?`var(${e}, ${t})`:`var(${e})`}),o={arrowShadowColor:r("--popper-arrow-shadow-color"),arrowSize:r("--popper-arrow-size","8px"),arrowSizeHalf:r("--popper-arrow-size-half"),arrowBg:r("--popper-arrow-bg"),transformOrigin:r("--popper-transform-origin"),arrowOffset:r("--popper-arrow-offset")};function i(e){return e.includes("top")?"1px 1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("bottom")?"-1px -1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("right")?"-1px 1px 0px 0 var(--popper-arrow-shadow-color)":e.includes("left")?"1px -1px 0px 0 var(--popper-arrow-shadow-color)":void 0}let a={top:"bottom center","top-start":"bottom left","top-end":"bottom right",bottom:"top center","bottom-start":"top left","bottom-end":"top right",left:"right center","left-start":"right top","left-end":"right bottom",right:"left center","right-start":"left top","right-end":"left bottom"},s=e=>a[e],l={scroll:!0,resize:!0};function u(e){let t;return"object"==typeof e?{enabled:!0,options:{...l,...e}}:{enabled:e,options:l}}},29800:(e,t,n)=>{"use strict";n.d(t,{Q:()=>o});var r=n(94285);function o(e){let t=r.version;return"string"!=typeof t||t.startsWith("18.")?e?.ref:e?.props?.ref}},31524:(e,t,n)=>{"use strict";n.d(t,{X:()=>c,w:()=>f});var r=n(3539),o=n(62937),i=n(15294),a=n(94285),s=n(40747),l=n(80222),u=n(50227);function c(e,t){let{shouldFocus:n,visible:i,focusRef:a}=t,s=n&&!i;(0,u.w)(()=>{let t;if(!s||function(e){let t=e.current;if(!t)return!1;let n=(0,r.bq)(t);return!(!n||t.contains(n))&&!!(0,o.AO)(n)}(e))return;let n=a?.current||e.current;if(n)return t=requestAnimationFrame(()=>{n.focus({preventScroll:!0})}),()=>{cancelAnimationFrame(t)}},[s,e,a])}let d={preventScroll:!0,shouldFocus:!1};function f(e,t=d){let{focusRef:n,preventScroll:r,shouldFocus:o,visible:c}=t,p="current"in e?e.current:e,m=o&&c,h=(0,a.useRef)(m),v=(0,a.useRef)(c);(0,l.U)(()=>{!v.current&&c&&(h.current=m),v.current=c},[c,m]);let b=(0,a.useCallback)(()=>{if(c&&p&&h.current&&(h.current=!1,!p.contains(document.activeElement)))if(n?.current)requestAnimationFrame(()=>{n.current?.focus({preventScroll:r})});else{let e=(0,i.ep)(p);e.length>0&&requestAnimationFrame(()=>{e[0].focus({preventScroll:r})})}},[c,r,p,n]);(0,u.w)(()=>{b()},[b]),(0,s.M)(p,"transitionend",b)}},40747:(e,t,n)=>{"use strict";n.d(t,{M:()=>i});var r=n(94285),o=n(65507);function i(e,t,n,i){let a=(0,o.c)(n);return(0,r.useEffect)(()=>{let r="function"==typeof e?e():e??document;if(n&&r)return r.addEventListener(t,a,i),()=>{r.removeEventListener(t,a,i)}},[t,e,i,a,n]),()=>{let n="function"==typeof e?e():e??document;n?.removeEventListener(t,a,i)}}},45617:(e,t,n)=>{"use strict";e.exports=n(81664)},50614:(e,t,n)=>{"use strict";function r(...e){return function(...t){e.forEach(e=>e?.(...t))}}function o(...e){return function(t){e.some(e=>(e?.(t),t?.defaultPrevented))}}n.d(t,{H:()=>o,O:()=>r})},51133:(e,t,n)=>{"use strict";n.d(t,{jd:()=>o,xf:()=>r,yA:()=>i});let r={ease:[.25,.1,.25,1],easeIn:[.4,0,1,1],easeOut:[0,0,.2,1],easeInOut:[.4,0,.2,1]},o={enter:{duration:.2,ease:r.easeOut},exit:{duration:.1,ease:r.easeIn}},i={enter:(e,t)=>({...e,delay:"number"==typeof t?t:t?.enter}),exit:(e,t)=>({...e,delay:"number"==typeof t?t:t?.exit})}},58686:(e,t,n)=>{e.exports=n(44559)},61060:(e,t,n)=>{"use strict";n.d(t,{R:()=>r});let r=e=>{let{condition:t,message:n}=e}},62937:(e,t,n)=>{"use strict";n.d(t,{AO:()=>s,tp:()=>a});var r=n(93487);let o=e=>e.hasAttribute("tabindex"),i=e=>o(e)&&-1===e.tabIndex;function a(e){if(!(0,r.sb)(e)||(0,r.N3)(e)||(0,r.pj)(e))return!1;let{localName:t}=e;if(["input","select","textarea","button"].indexOf(t)>=0)return!0;let n={a:()=>e.hasAttribute("href"),audio:()=>e.hasAttribute("controls"),video:()=>e.hasAttribute("controls")};return t in n?n[t]():!!(0,r.wu)(e)||o(e)}function s(e){return!!e&&(0,r.sb)(e)&&a(e)&&!i(e)}},65104:(e,t,n)=>{"use strict";n.d(t,{D:()=>p});var r=n(94513),o=n(22697),i=n(91169),a=n(2923),s=n(33225);let l=(0,a.R)((e,t)=>{let n=(0,i.$)();return(0,r.jsx)(s.B.span,{ref:t,...e,__css:n.command,className:"chakra-menu__command"})});l.displayName="MenuCommand";var u=n(94285);let c=e=>{let{className:t,children:n,...a}=e,l=(0,i.$)(),c=u.Children.only(n),d=(0,u.isValidElement)(c)?(0,u.cloneElement)(c,{focusable:"false","aria-hidden":!0,className:(0,o.cx)("chakra-menu__icon",c.props.className)}):null,f=(0,o.cx)("chakra-menu__icon-wrapper",t);return(0,r.jsx)(s.B.span,{className:f,...a,__css:l.icon,children:d})};c.displayName="MenuIcon";let d=(0,a.R)((e,t)=>{let{type:n,...o}=e,a=(0,i.$)(),l=o.as||n?n??void 0:"button",c=(0,u.useMemo)(()=>({textDecoration:"none",color:"inherit",userSelect:"none",display:"flex",width:"100%",alignItems:"center",textAlign:"start",flex:"0 0 auto",outline:0,...a.item}),[a.item]);return(0,r.jsx)(s.B.button,{ref:t,type:l,...o,__css:c})});var f=n(7753);let p=(0,a.R)((e,t)=>{let{icon:n,iconSpacing:i="0.75rem",command:a,commandSpacing:s="0.75rem",children:u,...p}=e,m=(0,f.Os)(p,t),h=n||a?(0,r.jsx)("span",{style:{pointerEvents:"none",flex:1},children:u}):u;return(0,r.jsxs)(d,{...m,className:(0,o.cx)("chakra-menu__menuitem",m.className),children:[n&&(0,r.jsx)(c,{fontSize:"0.8em",marginEnd:i,children:n}),h,a&&(0,r.jsx)(l,{marginStart:s,children:a})]})});p.displayName="MenuItem"},70011:(e,t,n)=>{"use strict";n.d(t,{I:()=>s});var r=n(78961),o=n(81405),i=n(94285);function a(e){let{tagName:t,isContentEditable:n}=e.composedPath?.()?.[0]??e.target;return"INPUT"!==t&&"TEXTAREA"!==t&&!0!==n}function s(e={}){let{ref:t,isDisabled:n,isFocusable:l,clickOnEnter:u=!0,clickOnSpace:c=!0,onMouseDown:d,onMouseUp:f,onClick:p,onKeyDown:m,onKeyUp:h,tabIndex:v,onMouseOver:b,onMouseLeave:y,...g}=e,[x,w]=(0,i.useState)(!0),[O,E]=(0,i.useState)(!1),k=function(){let e=(0,i.useRef)(new Map),t=e.current,n=(0,i.useCallback)((t,n,r,o)=>{e.current.set(r,{type:n,el:t,options:o}),t.addEventListener(n,r,o)},[]),r=(0,i.useCallback)((t,n,r,o)=>{t.removeEventListener(n,r,o),e.current.delete(r)},[]);return(0,i.useEffect)(()=>()=>{t.forEach((e,t)=>{r(e.el,e.type,t,e.options)})},[r,t]),{add:n,remove:r}}(),C=x?v:v||0,D=n&&!l,N=(0,i.useCallback)(e=>{if(n){e.stopPropagation(),e.preventDefault();return}e.currentTarget.focus(),p?.(e)},[n,p]),j=(0,i.useCallback)(e=>{O&&a(e)&&(e.preventDefault(),e.stopPropagation(),E(!1),k.remove(document,"keyup",j,!1))},[O,k]),S=(0,i.useCallback)(e=>{if(m?.(e),n||e.defaultPrevented||e.metaKey||!a(e.nativeEvent)||x)return;let t=u&&"Enter"===e.key;c&&" "===e.key&&(e.preventDefault(),E(!0)),t&&(e.preventDefault(),e.currentTarget.click()),k.add(document,"keyup",j,!1)},[n,x,m,u,c,k,j]),P=(0,i.useCallback)(e=>{h?.(e),!n&&!e.defaultPrevented&&!e.metaKey&&a(e.nativeEvent)&&!x&&c&&" "===e.key&&(e.preventDefault(),E(!1),e.currentTarget.click())},[c,x,n,h]),M=(0,i.useCallback)(e=>{0===e.button&&(E(!1),k.remove(document,"mouseup",M,!1))},[k]),I=(0,i.useCallback)(e=>{if(0===e.button){if(n){e.stopPropagation(),e.preventDefault();return}x||E(!0),e.currentTarget.focus({preventScroll:!0}),k.add(document,"mouseup",M,!1),d?.(e)}},[n,x,d,k,M]),T=(0,i.useCallback)(e=>{0===e.button&&(x||E(!1),f?.(e))},[f,x]),_=(0,i.useCallback)(e=>{if(n)return void e.preventDefault();b?.(e)},[n,b]),R=(0,i.useCallback)(e=>{O&&(e.preventDefault(),E(!1)),y?.(e)},[O,y]),A=(0,r.Px)(t,e=>{e&&"BUTTON"!==e.tagName&&w(!1)});return x?{...g,ref:A,type:"button","aria-disabled":D?void 0:n,disabled:D,onClick:N,onMouseDown:d,onMouseUp:f,onKeyUp:h,onKeyDown:m,onMouseOver:b,onMouseLeave:y}:{...g,ref:A,role:"button","data-active":(0,o.s)(O),"aria-disabled":n?"true":void 0,tabIndex:D?void 0:C,onClick:N,onMouseDown:I,onMouseUp:T,onKeyUp:P,onKeyDown:S,onMouseOver:_,onMouseLeave:R}}},71185:(e,t,n)=>{"use strict";n.d(t,{c:()=>u});var r=n(94513),o=n(75387),i=n(22697),a=n(2923),s=n(56915),l=n(33225);let u=(0,a.R)(function(e,t){let{borderLeftWidth:n,borderBottomWidth:a,borderTopWidth:u,borderRightWidth:c,borderWidth:d,borderStyle:f,borderColor:p,...m}=(0,s.V)("Divider",e),{className:h,orientation:v="horizontal",__css:b,...y}=(0,o.M)(e);return(0,r.jsx)(l.B.hr,{ref:t,"aria-orientation":v,...y,__css:{...m,border:"0",borderColor:p,borderStyle:f,...{vertical:{borderLeftWidth:n||c||d||"1px",height:"100%"},horizontal:{borderBottomWidth:a||u||d||"1px",width:"100%"}}[v],...b},className:(0,i.cx)("chakra-divider",h)})});u.displayName="Divider"},71601:(e,t,n)=>{"use strict";n.d(t,{E:()=>u});var r=n(94513),o=n(75387),i=n(22697),a=n(2923),s=n(56915),l=n(33225);let u=(0,a.R)(function(e,t){let n=(0,s.V)("Badge",e),{className:a,...u}=(0,o.M)(e);return(0,r.jsx)(l.B.span,{ref:t,className:(0,i.cx)("chakra-badge",e.className),...u,__css:{display:"inline-block",whiteSpace:"nowrap",verticalAlign:"middle",...n}})});u.displayName="Badge"},73011:(e,t,n)=>{"use strict";n.d(t,{K:()=>a});var r=n(94513),o=n(94285),i=n(62690);let a=(0,n(2923).R)((e,t)=>{let{icon:n,children:a,isRound:s,"aria-label":l,...u}=e,c=n||a,d=(0,o.isValidElement)(c)?(0,o.cloneElement)(c,{"aria-hidden":!0,focusable:!1}):null;return(0,r.jsx)(i.$,{px:"0",py:"0",borderRadius:s?"full":void 0,ref:t,"aria-label":l,...u,children:d})});a.displayName="IconButton"},76362:(e,t,n)=>{"use strict";n.d(t,{j:()=>i});var r=n(94285),o=n(65507);function i(e){let{ref:t,handler:n,enabled:i=!0}=e,l=(0,o.c)(n),u=(0,r.useRef)({isPointerDown:!1,ignoreEmulatedMouseEvents:!1}).current;(0,r.useEffect)(()=>{if(!i)return;let e=e=>{a(e,t)&&(u.isPointerDown=!0)},r=e=>{if(u.ignoreEmulatedMouseEvents){u.ignoreEmulatedMouseEvents=!1;return}u.isPointerDown&&n&&a(e,t)&&(u.isPointerDown=!1,l(e))},o=e=>{u.ignoreEmulatedMouseEvents=!0,n&&u.isPointerDown&&a(e,t)&&(u.isPointerDown=!1,l(e))},c=s(t.current);return c.addEventListener("mousedown",e,!0),c.addEventListener("mouseup",r,!0),c.addEventListener("touchstart",e,!0),c.addEventListener("touchend",o,!0),()=>{c.removeEventListener("mousedown",e,!0),c.removeEventListener("mouseup",r,!0),c.removeEventListener("touchstart",e,!0),c.removeEventListener("touchend",o,!0)}},[n,t,l,u,i])}function a(e,t){let n=e.composedPath?.()[0]??e.target;return(!n||!!s(n).contains(n))&&!t.current?.contains(n)}function s(e){return e?.ownerDocument??document}},78902:(e,t,n)=>{"use strict";n.d(t,{z:()=>i});var r=n(94513),o=n(70690);let i=(0,n(2923).R)((e,t)=>(0,r.jsx)(o.B,{align:"center",...e,direction:"row",ref:t}));i.displayName="HStack"},81664:(e,t,n)=>{"use strict";var r=n(94285),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return s(function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})},[e,n,t]),a(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},83901:(e,t,n)=>{"use strict";n.d(t,{S:()=>p});var r=n(94513),o=n(61060),i=n(22697),a=n(63203),s=n(67233),l=n(94285),u=n(51133);let c=e=>null!=e&&parseInt(e.toString(),10)>0,d={exit:{height:{duration:.2,ease:u.xf.ease},opacity:{duration:.3,ease:u.xf.ease}},enter:{height:{duration:.3,ease:u.xf.ease},opacity:{duration:.4,ease:u.xf.ease}}},f={exit:({animateOpacity:e,startingHeight:t,transition:n,transitionEnd:r,delay:o})=>({...e&&{opacity:+!!c(t)},height:t,transitionEnd:r?.exit,transition:n?.exit??u.yA.exit(d.exit,o)}),enter:({animateOpacity:e,endingHeight:t,transition:n,transitionEnd:r,delay:o})=>({...e&&{opacity:1},height:t,transitionEnd:r?.enter,transition:n?.enter??u.yA.enter(d.enter,o)})},p=(0,l.forwardRef)((e,t)=>{let{in:n,unmountOnExit:u,animateOpacity:c=!0,startingHeight:d=0,endingHeight:p="auto",style:m,className:h,transition:v,transitionEnd:b,animatePresenceProps:y,...g}=e,[x,w]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=setTimeout(()=>{w(!0)});return()=>clearTimeout(e)},[]),(0,o.R)({condition:Number(d)>0&&!!u,message:"startingHeight and unmountOnExit are mutually exclusive. You can't use them together"});let O=parseFloat(d.toString())>0,E={startingHeight:d,endingHeight:p,animateOpacity:c,transition:x?v:{enter:{duration:0}},transitionEnd:{enter:b?.enter,exit:u?b?.exit:{...b?.exit,display:O?"block":"none"}}},k=!u||n,C=n||u?"enter":"exit";return(0,r.jsx)(a.N,{...y,initial:!1,custom:E,children:k&&(0,r.jsx)(s.P.div,{ref:t,...g,className:(0,i.cx)("chakra-collapse",h),style:{overflow:"hidden",display:"block",...m},custom:E,variants:f,initial:!!u&&"exit",animate:C,exit:"exit"})})});p.displayName="Collapse"},87528:(e,t,n)=>{"use strict";n.d(t,{I:()=>c});var r=n(94513),o=n(22697),i=n(91169),a=n(7753),s=n(2923),l=n(33225);let u=(0,s.R)((e,t)=>{let n=(0,i.$)();return(0,r.jsx)(l.B.button,{ref:t,...e,__css:{display:"inline-flex",appearance:"none",alignItems:"center",outline:0,...n.button}})}),c=(0,s.R)((e,t)=>{let{children:n,as:i,...s}=e,c=(0,a.db)(s,t);return(0,r.jsx)(i||u,{...c,className:(0,o.cx)("chakra-menu__menu-button",e.className),children:(0,r.jsx)(l.B.span,{__css:{pointerEvents:"none",flex:"1 1 auto",minW:0},children:e.children})})});c.displayName="MenuButton"},87888:(e,t,n)=>{"use strict";n.d(t,{D:()=>v});var r=n(78961),o=n(29035),i=n(94285);function a(e){return e.sort((e,t)=>{let n=e.compareDocumentPosition(t);if(n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY)return -1;if(n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS)return 1;if(!(n&Node.DOCUMENT_POSITION_DISCONNECTED)&&!(n&Node.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC))return 0;throw Error("Cannot sort the given nodes.")})}let s=e=>"object"==typeof e&&"nodeType"in e&&e.nodeType===Node.ELEMENT_NODE;function l(e,t,n){let r=e+1;return n&&r>=t&&(r=0),r}function u(e,t,n){let r=e-1;return n&&r<0&&(r=t),r}let c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,d=e=>e;var f=Object.defineProperty,p=(e,t,n)=>t in e?f(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,m=(e,t,n)=>(p(e,"symbol"!=typeof t?t+"":t,n),n);class h{constructor(){m(this,"descendants",new Map),m(this,"register",e=>{if(null!=e)return s(e)?this.registerNode(e):t=>{this.registerNode(t,e)}}),m(this,"unregister",e=>{this.descendants.delete(e);let t=a(Array.from(this.descendants.keys()));this.assignIndex(t)}),m(this,"destroy",()=>{this.descendants.clear()}),m(this,"assignIndex",e=>{this.descendants.forEach(t=>{let n=e.indexOf(t.node);t.index=n,t.node.dataset.index=t.index.toString()})}),m(this,"count",()=>this.descendants.size),m(this,"enabledCount",()=>this.enabledValues().length),m(this,"values",()=>Array.from(this.descendants.values()).sort((e,t)=>e.index-t.index)),m(this,"enabledValues",()=>this.values().filter(e=>!e.disabled)),m(this,"item",e=>{if(0!==this.count())return this.values()[e]}),m(this,"enabledItem",e=>{if(0!==this.enabledCount())return this.enabledValues()[e]}),m(this,"first",()=>this.item(0)),m(this,"firstEnabled",()=>this.enabledItem(0)),m(this,"last",()=>this.item(this.descendants.size-1)),m(this,"lastEnabled",()=>{let e=this.enabledValues().length-1;return this.enabledItem(e)}),m(this,"indexOf",e=>e?this.descendants.get(e)?.index??-1:-1),m(this,"enabledIndexOf",e=>null==e?-1:this.enabledValues().findIndex(t=>t.node.isSameNode(e))),m(this,"next",(e,t=!0)=>{let n=l(e,this.count(),t);return this.item(n)}),m(this,"nextEnabled",(e,t=!0)=>{let n=this.item(e);if(!n)return;let r=l(this.enabledIndexOf(n.node),this.enabledCount(),t);return this.enabledItem(r)}),m(this,"prev",(e,t=!0)=>{let n=u(e,this.count()-1,t);return this.item(n)}),m(this,"prevEnabled",(e,t=!0)=>{let n=this.item(e);if(!n)return;let r=u(this.enabledIndexOf(n.node),this.enabledCount()-1,t);return this.enabledItem(r)}),m(this,"registerNode",(e,t)=>{if(!e||this.descendants.has(e))return;let n=a(Array.from(this.descendants.keys()).concat(e));t?.disabled&&(t.disabled=!!t.disabled);let r={node:e,index:-1,...t};this.descendants.set(e,r),this.assignIndex(n)})}}function v(){let[e,t]=(0,o.q)({name:"DescendantsProvider",errorMessage:"useDescendantsContext must be used within DescendantsProvider"});return[e,t,()=>{let e=(0,i.useRef)(new h);return c(()=>()=>e.current.destroy()),e.current},e=>{let n=t(),[o,a]=(0,i.useState)(-1),s=(0,i.useRef)(null);c(()=>()=>{s.current&&n.unregister(s.current)},[]),c(()=>{if(!s.current)return;let e=Number(s.current.dataset.index);o==e||Number.isNaN(e)||a(e)});let l=e?d(n.register(e)):d(n.register);return{descendants:n,index:o,enabledIndex:n.enabledIndexOf(s.current),register:(0,r.Px)(l,s)}}]}},88550:(e,t,n)=>{"use strict";n.d(t,{E:()=>ey});var r=n(78961);function o(e){if(null==e)return window;if("[object Window]"!==e.toString()){var t=e.ownerDocument;return t&&t.defaultView||window}return e}function i(e){var t=o(e).Element;return e instanceof t||e instanceof Element}function a(e){var t=o(e).HTMLElement;return e instanceof t||e instanceof HTMLElement}function s(e){if("undefined"==typeof ShadowRoot)return!1;var t=o(e).ShadowRoot;return e instanceof t||e instanceof ShadowRoot}var l=Math.max,u=Math.min,c=Math.round;function d(){var e=navigator.userAgentData;return null!=e&&e.brands&&Array.isArray(e.brands)?e.brands.map(function(e){return e.brand+"/"+e.version}).join(" "):navigator.userAgent}function f(){return!/^((?!chrome|android).)*safari/i.test(d())}function p(e,t,n){void 0===t&&(t=!1),void 0===n&&(n=!1);var r=e.getBoundingClientRect(),s=1,l=1;t&&a(e)&&(s=e.offsetWidth>0&&c(r.width)/e.offsetWidth||1,l=e.offsetHeight>0&&c(r.height)/e.offsetHeight||1);var u=(i(e)?o(e):window).visualViewport,d=!f()&&n,p=(r.left+(d&&u?u.offsetLeft:0))/s,m=(r.top+(d&&u?u.offsetTop:0))/l,h=r.width/s,v=r.height/l;return{width:h,height:v,top:m,right:p+h,bottom:m+v,left:p,x:p,y:m}}function m(e){var t=o(e);return{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function h(e){return e?(e.nodeName||"").toLowerCase():null}function v(e){return((i(e)?e.ownerDocument:e.document)||window.document).documentElement}function b(e){return p(v(e)).left+m(e).scrollLeft}function y(e){return o(e).getComputedStyle(e)}function g(e){var t=y(e),n=t.overflow,r=t.overflowX,o=t.overflowY;return/auto|scroll|overlay|hidden/.test(n+o+r)}function x(e){var t=p(e),n=e.offsetWidth,r=e.offsetHeight;return 1>=Math.abs(t.width-n)&&(n=t.width),1>=Math.abs(t.height-r)&&(r=t.height),{x:e.offsetLeft,y:e.offsetTop,width:n,height:r}}function w(e){return"html"===h(e)?e:e.assignedSlot||e.parentNode||(s(e)?e.host:null)||v(e)}function O(e,t){void 0===t&&(t=[]);var n,r=function e(t){return["html","body","#document"].indexOf(h(t))>=0?t.ownerDocument.body:a(t)&&g(t)?t:e(w(t))}(e),i=r===(null==(n=e.ownerDocument)?void 0:n.body),s=o(r),l=i?[s].concat(s.visualViewport||[],g(r)?r:[]):r,u=t.concat(l);return i?u:u.concat(O(w(l)))}function E(e){return a(e)&&"fixed"!==y(e).position?e.offsetParent:null}function k(e){for(var t=o(e),n=E(e);n&&["table","td","th"].indexOf(h(n))>=0&&"static"===y(n).position;)n=E(n);return n&&("html"===h(n)||"body"===h(n)&&"static"===y(n).position)?t:n||function(e){var t=/firefox/i.test(d());if(/Trident/i.test(d())&&a(e)&&"fixed"===y(e).position)return null;var n=w(e);for(s(n)&&(n=n.host);a(n)&&0>["html","body"].indexOf(h(n));){var r=y(n);if("none"!==r.transform||"none"!==r.perspective||"paint"===r.contain||-1!==["transform","perspective"].indexOf(r.willChange)||t&&"filter"===r.willChange||t&&r.filter&&"none"!==r.filter)return n;n=n.parentNode}return null}(e)||t}var C="bottom",D="right",N="left",j="auto",S=["top",C,D,N],P="start",M="viewport",I="popper",T=S.reduce(function(e,t){return e.concat([t+"-"+P,t+"-end"])},[]),_=[].concat(S,[j]).reduce(function(e,t){return e.concat([t,t+"-"+P,t+"-end"])},[]),R=["beforeRead","read","afterRead","beforeMain","main","afterMain","beforeWrite","write","afterWrite"],A={placement:"bottom",modifiers:[],strategy:"absolute"};function L(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return!t.some(function(e){return!(e&&"function"==typeof e.getBoundingClientRect)})}var B={passive:!0};function W(e){return e.split("-")[0]}function H(e){return e.split("-")[1]}function z(e){return["top","bottom"].indexOf(e)>=0?"x":"y"}function V(e){var t,n=e.reference,r=e.element,o=e.placement,i=o?W(o):null,a=o?H(o):null,s=n.x+n.width/2-r.width/2,l=n.y+n.height/2-r.height/2;switch(i){case"top":t={x:s,y:n.y-r.height};break;case C:t={x:s,y:n.y+n.height};break;case D:t={x:n.x+n.width,y:l};break;case N:t={x:n.x-r.width,y:l};break;default:t={x:n.x,y:n.y}}var u=i?z(i):null;if(null!=u){var c="y"===u?"height":"width";switch(a){case P:t[u]=t[u]-(n[c]/2-r[c]/2);break;case"end":t[u]=t[u]+(n[c]/2-r[c]/2)}}return t}var q={top:"auto",right:"auto",bottom:"auto",left:"auto"};function F(e){var t,n,r,i,a,s,l,u=e.popper,d=e.popperRect,f=e.placement,p=e.variation,m=e.offsets,h=e.position,b=e.gpuAcceleration,g=e.adaptive,x=e.roundOffsets,w=e.isFixed,O=m.x,E=void 0===O?0:O,j=m.y,S=void 0===j?0:j,P="function"==typeof x?x({x:E,y:S}):{x:E,y:S};E=P.x,S=P.y;var M=m.hasOwnProperty("x"),I=m.hasOwnProperty("y"),T=N,_="top",R=window;if(g){var A=k(u),L="clientHeight",B="clientWidth";A===o(u)&&"static"!==y(A=v(u)).position&&"absolute"===h&&(L="scrollHeight",B="scrollWidth"),("top"===f||(f===N||f===D)&&"end"===p)&&(_=C,S-=(w&&A===R&&R.visualViewport?R.visualViewport.height:A[L])-d.height,S*=b?1:-1),(f===N||("top"===f||f===C)&&"end"===p)&&(T=D,E-=(w&&A===R&&R.visualViewport?R.visualViewport.width:A[B])-d.width,E*=b?1:-1)}var W=Object.assign({position:h},g&&q),H=!0===x?(t={x:E,y:S},n=o(u),r=t.x,i=t.y,{x:c(r*(a=n.devicePixelRatio||1))/a||0,y:c(i*a)/a||0}):{x:E,y:S};return(E=H.x,S=H.y,b)?Object.assign({},W,((l={})[_]=I?"0":"",l[T]=M?"0":"",l.transform=1>=(R.devicePixelRatio||1)?"translate("+E+"px, "+S+"px)":"translate3d("+E+"px, "+S+"px, 0)",l)):Object.assign({},W,((s={})[_]=I?S+"px":"",s[T]=M?E+"px":"",s.transform="",s))}var U={left:"right",right:"left",bottom:"top",top:"bottom"};function $(e){return e.replace(/left|right|bottom|top/g,function(e){return U[e]})}var K={start:"end",end:"start"};function X(e){return e.replace(/start|end/g,function(e){return K[e]})}function Y(e,t){var n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&s(n)){var r=t;do{if(r&&e.isSameNode(r))return!0;r=r.parentNode||r.host}while(r)}return!1}function Z(e){return Object.assign({},e,{left:e.x,top:e.y,right:e.x+e.width,bottom:e.y+e.height})}function G(e,t,n){var r,a,s,u,c,d,h,g,x,w;return t===M?Z(function(e,t){var n=o(e),r=v(e),i=n.visualViewport,a=r.clientWidth,s=r.clientHeight,l=0,u=0;if(i){a=i.width,s=i.height;var c=f();(c||!c&&"fixed"===t)&&(l=i.offsetLeft,u=i.offsetTop)}return{width:a,height:s,x:l+b(e),y:u}}(e,n)):i(t)?((r=p(t,!1,"fixed"===n)).top=r.top+t.clientTop,r.left=r.left+t.clientLeft,r.bottom=r.top+t.clientHeight,r.right=r.left+t.clientWidth,r.width=t.clientWidth,r.height=t.clientHeight,r.x=r.left,r.y=r.top,r):Z((a=v(e),u=v(a),c=m(a),d=null==(s=a.ownerDocument)?void 0:s.body,h=l(u.scrollWidth,u.clientWidth,d?d.scrollWidth:0,d?d.clientWidth:0),g=l(u.scrollHeight,u.clientHeight,d?d.scrollHeight:0,d?d.clientHeight:0),x=-c.scrollLeft+b(a),w=-c.scrollTop,"rtl"===y(d||u).direction&&(x+=l(u.clientWidth,d?d.clientWidth:0)-h),{width:h,height:g,x:x,y:w}))}function Q(){return{top:0,right:0,bottom:0,left:0}}function J(e){return Object.assign({},Q(),e)}function ee(e,t){return t.reduce(function(t,n){return t[n]=e,t},{})}function et(e,t){void 0===t&&(t={});var n,r,o,s,c,d,f,m,b=t,g=b.placement,x=void 0===g?e.placement:g,E=b.strategy,N=void 0===E?e.strategy:E,j=b.boundary,P=b.rootBoundary,T=b.elementContext,_=void 0===T?I:T,R=b.altBoundary,A=b.padding,L=void 0===A?0:A,B=J("number"!=typeof L?L:ee(L,S)),W=e.rects.popper,H=e.elements[void 0!==R&&R?_===I?"reference":I:_],z=(n=i(H)?H:H.contextElement||v(e.elements.popper),r=void 0===j?"clippingParents":j,o=void 0===P?M:P,f=(d=[].concat("clippingParents"===r?(s=O(w(n)),!i(c=["absolute","fixed"].indexOf(y(n).position)>=0&&a(n)?k(n):n)?[]:s.filter(function(e){return i(e)&&Y(e,c)&&"body"!==h(e)})):[].concat(r),[o]))[0],(m=d.reduce(function(e,t){var r=G(n,t,N);return e.top=l(r.top,e.top),e.right=u(r.right,e.right),e.bottom=u(r.bottom,e.bottom),e.left=l(r.left,e.left),e},G(n,f,N))).width=m.right-m.left,m.height=m.bottom-m.top,m.x=m.left,m.y=m.top,m),q=p(e.elements.reference),F=V({reference:q,element:W,strategy:"absolute",placement:x}),U=Z(Object.assign({},W,F)),$=_===I?U:q,K={top:z.top-$.top+B.top,bottom:$.bottom-z.bottom+B.bottom,left:z.left-$.left+B.left,right:$.right-z.right+B.right},X=e.modifiersData.offset;if(_===I&&X){var Q=X[x];Object.keys(K).forEach(function(e){var t=[D,C].indexOf(e)>=0?1:-1,n=["top",C].indexOf(e)>=0?"y":"x";K[e]+=Q[n]*t})}return K}function en(e,t,n){return l(e,u(t,n))}function er(e,t,n){return void 0===n&&(n={x:0,y:0}),{top:e.top-t.height-n.y,right:e.right-t.width+n.x,bottom:e.bottom-t.height+n.y,left:e.left-t.width-n.x}}function eo(e){return["top",D,C,N].some(function(t){return e[t]>=0})}var ei=function(e){void 0===e&&(e={});var t=e,n=t.defaultModifiers,r=void 0===n?[]:n,s=t.defaultOptions,l=void 0===s?A:s;return function(e,t,n){void 0===n&&(n=l);var s,u,d={placement:"bottom",orderedModifiers:[],options:Object.assign({},A,l),modifiersData:{},elements:{reference:e,popper:t},attributes:{},styles:{}},f=[],y=!1,w={state:d,setOptions:function(n){var o,a,s,u,c,p,m="function"==typeof n?n(d.options):n;E(),d.options=Object.assign({},l,d.options,m),d.scrollParents={reference:i(e)?O(e):e.contextElement?O(e.contextElement):[],popper:O(t)};var h=(a=Object.keys(o=[].concat(r,d.options.modifiers).reduce(function(e,t){var n=e[t.name];return e[t.name]=n?Object.assign({},n,t,{options:Object.assign({},n.options,t.options),data:Object.assign({},n.data,t.data)}):t,e},{})).map(function(e){return o[e]}),s=new Map,u=new Set,c=[],a.forEach(function(e){s.set(e.name,e)}),a.forEach(function(e){u.has(e.name)||function e(t){u.add(t.name),[].concat(t.requires||[],t.requiresIfExists||[]).forEach(function(t){if(!u.has(t)){var n=s.get(t);n&&e(n)}}),c.push(t)}(e)}),p=c,R.reduce(function(e,t){return e.concat(p.filter(function(e){return e.phase===t}))},[]));return d.orderedModifiers=h.filter(function(e){return e.enabled}),d.orderedModifiers.forEach(function(e){var t=e.name,n=e.options,r=e.effect;if("function"==typeof r){var o=r({state:d,name:t,instance:w,options:void 0===n?{}:n});f.push(o||function(){})}}),w.update()},forceUpdate:function(){if(!y){var e=d.elements,t=e.reference,n=e.popper;if(L(t,n)){d.rects={reference:(r=k(n),i="fixed"===d.options.strategy,s=a(r),O=a(r)&&(u=c((l=r.getBoundingClientRect()).width)/r.offsetWidth||1,f=c(l.height)/r.offsetHeight||1,1!==u||1!==f),E=v(r),C=p(t,O,i),D={scrollLeft:0,scrollTop:0},N={x:0,y:0},(s||!s&&!i)&&(("body"!==h(r)||g(E))&&(D=function(e){return e!==o(e)&&a(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:m(e)}(r)),a(r)?(N=p(r,!0),N.x+=r.clientLeft,N.y+=r.clientTop):E&&(N.x=b(E))),{x:C.left+D.scrollLeft-N.x,y:C.top+D.scrollTop-N.y,width:C.width,height:C.height}),popper:x(n)},d.reset=!1,d.placement=d.options.placement,d.orderedModifiers.forEach(function(e){return d.modifiersData[e.name]=Object.assign({},e.data)});for(var r,i,s,l,u,f,O,E,C,D,N,j=0;j<d.orderedModifiers.length;j++){if(!0===d.reset){d.reset=!1,j=-1;continue}var S=d.orderedModifiers[j],P=S.fn,M=S.options,I=void 0===M?{}:M,T=S.name;"function"==typeof P&&(d=P({state:d,options:I,name:T,instance:w})||d)}}}},update:(s=function(){return new Promise(function(e){w.forceUpdate(),e(d)})},function(){return u||(u=new Promise(function(e){Promise.resolve().then(function(){u=void 0,e(s())})})),u}),destroy:function(){E(),y=!0}};if(!L(e,t))return w;function E(){f.forEach(function(e){return e()}),f=[]}return w.setOptions(n).then(function(e){!y&&n.onFirstUpdate&&n.onFirstUpdate(e)}),w}}({defaultModifiers:[{name:"eventListeners",enabled:!0,phase:"write",fn:function(){},effect:function(e){var t=e.state,n=e.instance,r=e.options,i=r.scroll,a=void 0===i||i,s=r.resize,l=void 0===s||s,u=o(t.elements.popper),c=[].concat(t.scrollParents.reference,t.scrollParents.popper);return a&&c.forEach(function(e){e.addEventListener("scroll",n.update,B)}),l&&u.addEventListener("resize",n.update,B),function(){a&&c.forEach(function(e){e.removeEventListener("scroll",n.update,B)}),l&&u.removeEventListener("resize",n.update,B)}},data:{}},{name:"popperOffsets",enabled:!0,phase:"read",fn:function(e){var t=e.state,n=e.name;t.modifiersData[n]=V({reference:t.rects.reference,element:t.rects.popper,strategy:"absolute",placement:t.placement})},data:{}},{name:"computeStyles",enabled:!0,phase:"beforeWrite",fn:function(e){var t=e.state,n=e.options,r=n.gpuAcceleration,o=n.adaptive,i=n.roundOffsets,a=void 0===i||i,s={placement:W(t.placement),variation:H(t.placement),popper:t.elements.popper,popperRect:t.rects.popper,gpuAcceleration:void 0===r||r,isFixed:"fixed"===t.options.strategy};null!=t.modifiersData.popperOffsets&&(t.styles.popper=Object.assign({},t.styles.popper,F(Object.assign({},s,{offsets:t.modifiersData.popperOffsets,position:t.options.strategy,adaptive:void 0===o||o,roundOffsets:a})))),null!=t.modifiersData.arrow&&(t.styles.arrow=Object.assign({},t.styles.arrow,F(Object.assign({},s,{offsets:t.modifiersData.arrow,position:"absolute",adaptive:!1,roundOffsets:a})))),t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-placement":t.placement})},data:{}},{name:"applyStyles",enabled:!0,phase:"write",fn:function(e){var t=e.state;Object.keys(t.elements).forEach(function(e){var n=t.styles[e]||{},r=t.attributes[e]||{},o=t.elements[e];a(o)&&h(o)&&(Object.assign(o.style,n),Object.keys(r).forEach(function(e){var t=r[e];!1===t?o.removeAttribute(e):o.setAttribute(e,!0===t?"":t)}))})},effect:function(e){var t=e.state,n={popper:{position:t.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};return Object.assign(t.elements.popper.style,n.popper),t.styles=n,t.elements.arrow&&Object.assign(t.elements.arrow.style,n.arrow),function(){Object.keys(t.elements).forEach(function(e){var r=t.elements[e],o=t.attributes[e]||{},i=Object.keys(t.styles.hasOwnProperty(e)?t.styles[e]:n[e]).reduce(function(e,t){return e[t]="",e},{});a(r)&&h(r)&&(Object.assign(r.style,i),Object.keys(o).forEach(function(e){r.removeAttribute(e)}))})}},requires:["computeStyles"]},{name:"offset",enabled:!0,phase:"main",requires:["popperOffsets"],fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.offset,i=void 0===o?[0,0]:o,a=_.reduce(function(e,n){var r,o,a,s,l,u;return e[n]=(r=t.rects,a=[N,"top"].indexOf(o=W(n))>=0?-1:1,l=(s="function"==typeof i?i(Object.assign({},r,{placement:n})):i)[0],u=s[1],l=l||0,u=(u||0)*a,[N,D].indexOf(o)>=0?{x:u,y:l}:{x:l,y:u}),e},{}),s=a[t.placement],l=s.x,u=s.y;null!=t.modifiersData.popperOffsets&&(t.modifiersData.popperOffsets.x+=l,t.modifiersData.popperOffsets.y+=u),t.modifiersData[r]=a}},{name:"flip",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name;if(!t.modifiersData[r]._skip){for(var o=n.mainAxis,i=void 0===o||o,a=n.altAxis,s=void 0===a||a,l=n.fallbackPlacements,u=n.padding,c=n.boundary,d=n.rootBoundary,f=n.altBoundary,p=n.flipVariations,m=void 0===p||p,h=n.allowedAutoPlacements,v=t.options.placement,b=W(v)===v,y=l||(b||!m?[$(v)]:function(e){if(W(e)===j)return[];var t=$(e);return[X(e),t,X(t)]}(v)),g=[v].concat(y).reduce(function(e,n){var r,o,i,a,s,l,f,p,v,b,y,g;return e.concat(W(n)===j?(o=(r={placement:n,boundary:c,rootBoundary:d,padding:u,flipVariations:m,allowedAutoPlacements:h}).placement,i=r.boundary,a=r.rootBoundary,s=r.padding,l=r.flipVariations,p=void 0===(f=r.allowedAutoPlacements)?_:f,0===(y=(b=(v=H(o))?l?T:T.filter(function(e){return H(e)===v}):S).filter(function(e){return p.indexOf(e)>=0})).length&&(y=b),Object.keys(g=y.reduce(function(e,n){return e[n]=et(t,{placement:n,boundary:i,rootBoundary:a,padding:s})[W(n)],e},{})).sort(function(e,t){return g[e]-g[t]})):n)},[]),x=t.rects.reference,w=t.rects.popper,O=new Map,E=!0,k=g[0],M=0;M<g.length;M++){var I=g[M],R=W(I),A=H(I)===P,L=["top",C].indexOf(R)>=0,B=L?"width":"height",z=et(t,{placement:I,boundary:c,rootBoundary:d,altBoundary:f,padding:u}),V=L?A?D:N:A?C:"top";x[B]>w[B]&&(V=$(V));var q=$(V),F=[];if(i&&F.push(z[R]<=0),s&&F.push(z[V]<=0,z[q]<=0),F.every(function(e){return e})){k=I,E=!1;break}O.set(I,F)}if(E)for(var U=m?3:1,K=function(e){var t=g.find(function(t){var n=O.get(t);if(n)return n.slice(0,e).every(function(e){return e})});if(t)return k=t,"break"},Y=U;Y>0&&"break"!==K(Y);Y--);t.placement!==k&&(t.modifiersData[r]._skip=!0,t.placement=k,t.reset=!0)}},requiresIfExists:["offset"],data:{_skip:!1}},{name:"preventOverflow",enabled:!0,phase:"main",fn:function(e){var t=e.state,n=e.options,r=e.name,o=n.mainAxis,i=n.altAxis,a=n.boundary,s=n.rootBoundary,c=n.altBoundary,d=n.padding,f=n.tether,p=void 0===f||f,m=n.tetherOffset,h=void 0===m?0:m,v=et(t,{boundary:a,rootBoundary:s,padding:d,altBoundary:c}),b=W(t.placement),y=H(t.placement),g=!y,w=z(b),O="x"===w?"y":"x",E=t.modifiersData.popperOffsets,j=t.rects.reference,S=t.rects.popper,M="function"==typeof h?h(Object.assign({},t.rects,{placement:t.placement})):h,I="number"==typeof M?{mainAxis:M,altAxis:M}:Object.assign({mainAxis:0,altAxis:0},M),T=t.modifiersData.offset?t.modifiersData.offset[t.placement]:null,_={x:0,y:0};if(E){if(void 0===o||o){var R,A="y"===w?"top":N,L="y"===w?C:D,B="y"===w?"height":"width",V=E[w],q=V+v[A],F=V-v[L],U=p?-S[B]/2:0,$=y===P?j[B]:S[B],K=y===P?-S[B]:-j[B],X=t.elements.arrow,Y=p&&X?x(X):{width:0,height:0},Z=t.modifiersData["arrow#persistent"]?t.modifiersData["arrow#persistent"].padding:Q(),G=Z[A],J=Z[L],ee=en(0,j[B],Y[B]),er=g?j[B]/2-U-ee-G-I.mainAxis:$-ee-G-I.mainAxis,eo=g?-j[B]/2+U+ee+J+I.mainAxis:K+ee+J+I.mainAxis,ei=t.elements.arrow&&k(t.elements.arrow),ea=ei?"y"===w?ei.clientTop||0:ei.clientLeft||0:0,es=null!=(R=null==T?void 0:T[w])?R:0,el=en(p?u(q,V+er-es-ea):q,V,p?l(F,V+eo-es):F);E[w]=el,_[w]=el-V}if(void 0!==i&&i){var eu,ec,ed="x"===w?"top":N,ef="x"===w?C:D,ep=E[O],em="y"===O?"height":"width",eh=ep+v[ed],ev=ep-v[ef],eb=-1!==["top",N].indexOf(b),ey=null!=(ec=null==T?void 0:T[O])?ec:0,eg=eb?eh:ep-j[em]-S[em]-ey+I.altAxis,ex=eb?ep+j[em]+S[em]-ey-I.altAxis:ev,ew=p&&eb?(eu=en(eg,ep,ex))>ex?ex:eu:en(p?eg:eh,ep,p?ex:ev);E[O]=ew,_[O]=ew-ep}t.modifiersData[r]=_}},requiresIfExists:["offset"]},{name:"arrow",enabled:!0,phase:"main",fn:function(e){var t,n=e.state,r=e.name,o=e.options,i=n.elements.arrow,a=n.modifiersData.popperOffsets,s=W(n.placement),l=z(s),u=[N,D].indexOf(s)>=0?"height":"width";if(i&&a){var c,d=(c=o.padding,J("number"!=typeof(c="function"==typeof c?c(Object.assign({},n.rects,{placement:n.placement})):c)?c:ee(c,S))),f=x(i),p="y"===l?"top":N,m="y"===l?C:D,h=n.rects.reference[u]+n.rects.reference[l]-a[l]-n.rects.popper[u],v=a[l]-n.rects.reference[l],b=k(i),y=b?"y"===l?b.clientHeight||0:b.clientWidth||0:0,g=d[p],w=y-f[u]-d[m],O=y/2-f[u]/2+(h/2-v/2),E=en(g,O,w);n.modifiersData[r]=((t={})[l]=E,t.centerOffset=E-O,t)}},effect:function(e){var t=e.state,n=e.options.element,r=void 0===n?"[data-popper-arrow]":n;if(null!=r)("string"!=typeof r||(r=t.elements.popper.querySelector(r)))&&Y(t.elements.popper,r)&&(t.elements.arrow=r)},requires:["popperOffsets"],requiresIfExists:["preventOverflow"]},{name:"hide",enabled:!0,phase:"main",requiresIfExists:["preventOverflow"],fn:function(e){var t=e.state,n=e.name,r=t.rects.reference,o=t.rects.popper,i=t.modifiersData.preventOverflow,a=et(t,{elementContext:"reference"}),s=et(t,{altBoundary:!0}),l=er(a,r),u=er(s,o,i),c=eo(l),d=eo(u);t.modifiersData[n]={referenceClippingOffsets:l,popperEscapeOffsets:u,isReferenceHidden:c,hasPopperEscaped:d},t.attributes.popper=Object.assign({},t.attributes.popper,{"data-popper-reference-hidden":c,"data-popper-escaped":d})}}]}),ea=n(94285),es=n(27850);let el={name:"matchWidth",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:({state:e})=>{e.styles.popper.width=`${e.rects.reference.width}px`},effect:({state:e})=>()=>{let t=e.elements.reference;e.elements.popper.style.width=`${t.offsetWidth}px`}},eu={name:"transformOrigin",enabled:!0,phase:"write",fn:({state:e})=>{ec(e)},effect:({state:e})=>()=>{ec(e)}},ec=e=>{e.elements.popper.style.setProperty(es.O3.transformOrigin.var,(0,es.ed)(e.placement))},ed={name:"positionArrow",enabled:!0,phase:"afterWrite",fn:({state:e})=>{ef(e)}},ef=e=>{if(!e.placement)return;let t=ep(e.placement);if(e.elements?.arrow&&t){Object.assign(e.elements.arrow.style,{[t.property]:t.value,width:es.O3.arrowSize.varRef,height:es.O3.arrowSize.varRef,zIndex:-1});let n={[es.O3.arrowSizeHalf.var]:`calc(${es.O3.arrowSize.varRef} / 2 - 1px)`,[es.O3.arrowOffset.var]:`calc(${es.O3.arrowSizeHalf.varRef} * -1)`};for(let t in n)e.elements.arrow.style.setProperty(t,n[t])}},ep=e=>e.startsWith("top")?{property:"bottom",value:es.O3.arrowOffset.varRef}:e.startsWith("bottom")?{property:"top",value:es.O3.arrowOffset.varRef}:e.startsWith("left")?{property:"right",value:es.O3.arrowOffset.varRef}:e.startsWith("right")?{property:"left",value:es.O3.arrowOffset.varRef}:void 0,em={name:"innerArrow",enabled:!0,phase:"main",requires:["arrow"],fn:({state:e})=>{eh(e)},effect:({state:e})=>()=>{eh(e)}},eh=e=>{if(!e.elements.arrow)return;let t=e.elements.arrow.querySelector("[data-popper-arrow-inner]");if(!t)return;let n=(0,es.Ky)(e.placement);n&&t.style.setProperty("--popper-arrow-default-shadow",n),Object.assign(t.style,{transform:"rotate(45deg)",background:es.O3.arrowBg.varRef,top:0,left:0,width:"100%",height:"100%",position:"absolute",zIndex:"inherit",boxShadow:"var(--popper-arrow-shadow, var(--popper-arrow-default-shadow))"})},ev={"start-start":{ltr:"left-start",rtl:"right-start"},"start-end":{ltr:"left-end",rtl:"right-end"},"end-start":{ltr:"right-start",rtl:"left-start"},"end-end":{ltr:"right-end",rtl:"left-end"},start:{ltr:"left",rtl:"right"},end:{ltr:"right",rtl:"left"}},eb={"auto-start":"auto-end","auto-end":"auto-start","top-start":"top-end","top-end":"top-start","bottom-start":"bottom-end","bottom-end":"bottom-start"};function ey(e={}){let{enabled:t=!0,modifiers:n,placement:o="bottom",strategy:i="absolute",arrowPadding:a=8,eventListeners:s=!0,offset:l,gutter:u=8,flip:c=!0,boundary:d="clippingParents",preventOverflow:f=!0,matchWidth:p,direction:m="ltr"}=e,h=(0,ea.useRef)(null),v=(0,ea.useRef)(null),b=(0,ea.useRef)(null),y=function(e,t="ltr"){let n=ev[e]?.[t]||e;return"ltr"===t?n:eb[e]??n}(o,m),g=(0,ea.useRef)(()=>{}),x=(0,ea.useCallback)(()=>{t&&h.current&&v.current&&(g.current?.(),b.current=ei(h.current,v.current,{placement:y,modifiers:[em,ed,eu,{...el,enabled:!!p},{name:"eventListeners",...(0,es.Fs)(s)},{name:"arrow",options:{padding:a}},{name:"offset",options:{offset:l??[0,u]}},{name:"flip",enabled:!!c,options:{padding:8}},{name:"preventOverflow",enabled:!!f,options:{boundary:d}},...n??[]],strategy:i}),b.current.forceUpdate(),g.current=b.current.destroy)},[y,t,n,p,s,a,l,u,c,f,d,i]);(0,ea.useEffect)(()=>()=>{h.current||v.current||(b.current?.destroy(),b.current=null)},[]);let w=(0,ea.useCallback)(e=>{h.current=e,x()},[x]),O=(0,ea.useCallback)((e={},t=null)=>({...e,ref:(0,r.Px)(w,t)}),[w]),E=(0,ea.useCallback)(e=>{v.current=e,x()},[x]),k=(0,ea.useCallback)((e={},t=null)=>({...e,ref:(0,r.Px)(E,t),style:{...e.style,position:i,minWidth:p?void 0:"max-content",inset:"0 auto auto 0"}}),[i,E,p]),C=(0,ea.useCallback)((e={},t=null)=>{let{size:n,shadowColor:r,bg:o,style:i,...a}=e;return{...a,ref:t,"data-popper-arrow":"",style:function(e){let{size:t,shadowColor:n,bg:r,style:o}=e,i={...o,position:"absolute"};return t&&(i["--popper-arrow-size"]=t),n&&(i["--popper-arrow-shadow-color"]=n),r&&(i["--popper-arrow-bg"]=r),i}(e)}},[]),D=(0,ea.useCallback)((e={},t=null)=>({...e,ref:t,"data-popper-arrow-inner":""}),[]);return{update(){b.current?.update()},forceUpdate(){b.current?.forceUpdate()},transformOrigin:es.O3.transformOrigin.varRef,referenceRef:w,popperRef:E,getPopperProps:k,getArrowProps:C,getArrowInnerProps:D,getReferenceProps:O}}},89630:(e,t,n)=>{"use strict";n.d(t,{v:()=>a});var r=n(3539),o=n(94285),i=n(40747);function a(e){let{isOpen:t,ref:n}=e,[a,s]=(0,o.useState)(t),[l,u]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{l||(s(t),u(!0))},[t,l,a]),(0,i.M)(()=>n.current,"animationend",()=>{s(t)}),{present:!(!t&&!a),onComplete(){let e=new((0,r.mD)(n.current)).CustomEvent("animationend",{bubbles:!0});n.current?.dispatchEvent(e)}}}},91169:(e,t,n)=>{"use strict";n.d(t,{$:()=>f,W:()=>p});var r=n(94513),o=n(75387),i=n(29035),a=n(69012),s=n(94285),l=n(7753),u=n(83745),c=n(56915);let[d,f]=(0,i.q)({name:"MenuStylesContext",errorMessage:"useMenuStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Menu />\" "}),p=e=>{let{children:t}=e,n=(0,c.o)("Menu",e),i=(0,o.M)(e),{direction:f}=(0,u.D)(),{descendants:p,...m}=(0,l.b)({...i,direction:f}),h=(0,s.useMemo)(()=>m,[m]),{isOpen:v,onClose:b,forceUpdate:y}=h;return(0,r.jsx)(l.Xu,{value:p,children:(0,r.jsx)(l.BV,{value:h,children:(0,r.jsx)(d,{value:n,children:(0,a.J)(t,{isOpen:v,onClose:b,forceUpdate:y})})})})};p.displayName="Menu"}}]);