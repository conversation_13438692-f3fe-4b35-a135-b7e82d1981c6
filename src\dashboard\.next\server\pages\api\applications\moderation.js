"use strict";(()=>{var e={};e.id=6413,e.ids=[6413],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var o=r(5542),s=r.n(o);let a=require("next-auth/providers/discord");var n=r.n(a),i=r(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,o=t.accessToken||null;e.user.id=r,e.user.accessToken=o;let s=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))s=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();s=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),o=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var o=r(9021),s=r(2115),a=r.n(s),n=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");i=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")},9244:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>b,routeModule:()=>g});var o={};r.r(o),r.d(o,{default:()=>p});var s=r(3433),a=r(264),n=r(584),i=r(5806),d=r(8525),l=r(2518),u=r(8580);let c=null;async function f(){if(c)return c;try{let e=await l.MongoClient.connect(u.dashboardConfig.database.url,{...u.dashboardConfig.database.options});return c=e,e}catch(e){throw e}}async function p(e,t){let r=await (0,i.getServerSession)(e,t,d.authOptions);if(!r)return t.status(401).json({error:"Unauthorized"});if("POST"!==e.method&&!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});try{let r=(await f()).db(u.dashboardConfig.database.name).collection("applications");if("POST"===e.method){let{userId:o,username:s,answers:a,quizAnswers:n,timezone:i,age:d,hoursPerWeek:l,extraInfo:u}=e.body;if(!o||!a?.statement||!n||!i||!d||!l)return t.status(400).json({error:"Missing required fields"});try{if(await r.findOne({userId:o}))return t.status(400).json({error:"You have already submitted an application"});let e=await r.insertOne({userId:o,username:s,answers:a,quizAnswers:n,timezone:i,age:d,hoursPerWeek:l,extraInfo:u,date:new Date,status:"pending"});return t.status(200).json({success:!0,id:e.insertedId})}catch(e){return t.status(500).json({error:"Failed to save application"})}}if("GET"===e.method){let e=await r.find().sort({date:-1}).toArray();return t.status(200).json(e)}if("PUT"===e.method){let{applicationId:o}=e.query,{status:s}=e.body;if(!o||"string"!=typeof o||!s)return t.status(400).json({error:"Missing required fields"});let a=await r.updateOne({_id:new l.ObjectId(o)},{$set:{status:s}});if(0===a.matchedCount)return t.status(404).json({error:"Application not found"});return t.status(200).json({message:"Application updated successfully"})}if("DELETE"===e.method){let{applicationId:o}=e.query;if(!o||"string"!=typeof o)return t.status(400).json({error:"Missing application ID"});let s=await r.deleteOne({_id:new l.ObjectId(o)});if(0===s.deletedCount)return t.status(404).json({error:"Application not found"});return t.status(200).json({message:"Application deleted successfully"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let b=(0,n.M)(o,"default"),m=(0,n.M)(o,"config"),g=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/applications/moderation",pathname:"/api/applications/moderation",bundlePath:"",filename:""},userland:o})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=9244);module.exports=r})();