"use strict";(()=>{var e={};e.id=8111,e.ids=[8111],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2115:e=>{e.exports=require("yaml")},2218:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>p,getServerSideProps:()=>x});var i=r(8732),o=r(9733),a=r(2015),n=r(1011),l=r(4173),c=r(8079),d=r(5806),u=r(3762),h=e([o,n,l]);function p({isAdmin:e}){let[t,r]=(0,a.useState)([]),[s,d]=(0,a.useState)(!0),{isOpen:u,onOpen:h,onClose:p}=(0,o.useDisclosure)(),x=async()=>{d(!0);try{let e=await fetch("/api/discord/tickets");if(!e.ok)throw Error("Failed to fetch tickets");let t=await e.json();r(t)}catch(e){}finally{d(!1)}};return(0,i.jsx)(n.A,{children:(0,i.jsxs)(o.Container,{maxW:"container.xl",py:8,children:[(0,i.jsxs)(o.VStack,{spacing:8,align:"stretch",children:[(0,i.jsxs)(o.Box,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",textAlign:"center",children:[(0,i.jsx)(o.Heading,{size:"2xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",mb:4,children:"Support Tickets"}),(0,i.jsx)(o.Text,{color:"gray.300",fontSize:"lg",children:"Open new tickets or review existing ones"})]}),(0,i.jsx)(o.HStack,{justify:"flex-end",children:(0,i.jsx)(o.Button,{leftIcon:(0,i.jsx)(o.Icon,{as:c.GGD}),colorScheme:"blue",onClick:h,children:"Create Ticket"})}),(0,i.jsx)(o.Box,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",rounded:"lg",p:4,children:s?(0,i.jsx)(o.HStack,{justify:"center",py:10,children:(0,i.jsx)(o.Spinner,{size:"lg"})}):0===t.length?(0,i.jsx)(o.Text,{textAlign:"center",py:10,color:"gray.300",children:"You have no tickets yet."}):(0,i.jsxs)(o.Table,{variant:"simple",children:[(0,i.jsx)(o.Thead,{children:(0,i.jsxs)(o.Tr,{children:[(0,i.jsx)(o.Th,{children:"ID"}),(0,i.jsx)(o.Th,{children:"Reason"}),(0,i.jsx)(o.Th,{children:"Status"}),(0,i.jsx)(o.Th,{children:"Created"}),(0,i.jsx)(o.Th,{children:"Actions"})]})}),(0,i.jsx)(o.Tbody,{children:t.map(t=>(0,i.jsxs)(o.Tr,{children:[(0,i.jsx)(o.Td,{children:t._id.toString().slice(-6)}),(0,i.jsx)(o.Td,{maxW:"300px",children:(0,i.jsx)(o.Text,{isTruncated:!0,title:t.reason,children:t.reason||"No reason provided"})}),(0,i.jsx)(o.Td,{children:(0,i.jsx)(o.Badge,{colorScheme:"open"===t.status?"green":"red",children:t.status})}),(0,i.jsx)(o.Td,{children:new Date(t.createdAt).toLocaleString()}),(0,i.jsx)(o.Td,{children:(0,i.jsxs)(o.HStack,{spacing:2,children:[t.discordLink&&(0,i.jsx)(o.Button,{as:"a",href:t.discordLink,target:"_blank",size:"sm",leftIcon:(0,i.jsx)(o.Icon,{as:c.HaR}),children:"Discord"}),"open"===t.status&&(0,i.jsx)(o.Button,{size:"sm",colorScheme:"yellow",onClick:async()=>{if(window.confirm("Close this ticket?"))try{if(!(await fetch(`/api/discord/tickets/${t._id}`,{method:"PATCH"})).ok)throw Error("Failed to close ticket");x()}catch(e){}},children:"Close"}),e&&"closed"===t.status&&(0,i.jsx)(o.Button,{size:"sm",colorScheme:"red",onClick:async()=>{if(window.confirm("Delete this ticket? This is irreversible."))try{if(!(await fetch(`/api/discord/tickets/${t._id}`,{method:"DELETE"})).ok)throw Error("Failed to delete ticket");x()}catch(e){}},children:"Delete"}),"closed"===t.status&&(0,i.jsx)(o.Button,{as:"a",href:`/api/discord/tickets/${t._id}/transcript`,size:"sm",colorScheme:"green",leftIcon:(0,i.jsx)(o.Icon,{as:c.HaR}),target:"_blank",children:"Transcript"})]})})]},t._id))})]})})]}),(0,i.jsx)(l.A,{isOpen:u,onClose:p,onSuccess:x})]})})}[o,n,l]=h.then?(await h)():h;let x=async e=>{let t=await (0,d.getServerSession)(e.req,e.res,u.N);return t?{props:{isAdmin:t.user.isAdmin||!1}}:{redirect:{destination:"/signin",permanent:!1}}};s()}catch(e){s(e)}})},2326:e=>{e.exports=require("react-dom")},3762:(e,t,r)=>{r.d(t,{N:()=>p});var s=r(5542),i=r.n(s);let o=require("next-auth/providers/discord");var a=r.n(o),n=r(9021),l=r(2115),c=r.n(l),d=r(3873);let u={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d.resolve(process.cwd(),e)).find(e=>n.existsSync(e));if(!e){let t=d.resolve(__dirname,"../../../config.yml");n.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=n.readFileSync(e,"utf8");u=c().parse(t)}catch(e){process.exit(1)}let h={bot:{token:u.bot.token,clientId:u.bot.clientId,clientSecret:u.bot.clientSecret,guildId:u.bot.guildId,ticketCategoryId:u.bot.ticketCategoryId||null,ticketLogChannelId:u.bot.ticketLogChannelId||null,prefix:u.bot.prefix},dashboard:{admins:u.dashboard?.admins||[],adminRoleIds:u.dashboard?.adminRoleIds||[],session:{secret:u.dashboard?.session?.secret||u.bot.clientSecret}},database:{url:u.database.url,name:u.database.name,options:{maxPoolSize:u.database.options?.maxPoolSize||10,minPoolSize:u.database.options?.minPoolSize||1,maxIdleTimeMS:u.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:u.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:u.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:u.database.options?.connectTimeoutMS||1e4,retryWrites:u.database.options?.retryWrites!==!1,retryReads:u.database.options?.retryReads!==!1}}};h.bot.token||process.exit(1),h.bot.clientId&&h.bot.clientSecret||process.exit(1),h.bot.guildId||process.exit(1),h.database.url&&h.database.name||process.exit(1);let p={providers:[a()({clientId:h.bot.clientId,clientSecret:h.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,s=t.accessToken||null;e.user.id=r,e.user.accessToken=s;let i=!1;if(r)if((h.dashboard.admins||[]).includes(r))i=!0;else{let e=h.dashboard.adminRoleIds||[];if(e.length&&h.bot.token&&h.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${h.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${h.bot.token}`}});if(t.ok){let r=await t.json();i=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),s=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:h.dashboard.session.secret||h.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};i()(p)},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4173:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.d(t,{A:()=>l});var i=r(8732),o=r(9733),a=r(2015),n=e([o]);function l({isOpen:e,onClose:t,onSuccess:r}){let s=(0,o.useToast)(),[n,l]=(0,a.useState)(""),[c,d]=(0,a.useState)("support"),[u,h]=(0,a.useState)(!1),p=async()=>{h(!0);try{let e=await fetch("/api/discord/tickets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:n,category:c})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to create ticket")}s({title:"Ticket Created",description:"Your support ticket has been opened.",status:"success",duration:3e3}),l(""),d("support"),r(),t()}catch(e){s({title:"Error",description:e.message||"Failed to create ticket",status:"error",duration:5e3})}finally{h(!1)}};return(0,i.jsxs)(o.Modal,{isOpen:e,onClose:t,size:"lg",scrollBehavior:"inside",children:[(0,i.jsx)(o.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,i.jsxs)(o.ModalContent,{bg:"gray.800",children:[(0,i.jsx)(o.ModalHeader,{children:"Create Support Ticket"}),(0,i.jsx)(o.ModalCloseButton,{}),(0,i.jsxs)(o.ModalBody,{children:[(0,i.jsxs)(o.FormControl,{mb:4,children:[(0,i.jsx)(o.FormLabel,{children:"Category"}),(0,i.jsxs)(o.Select,{value:c,onChange:e=>d(e.target.value),children:[(0,i.jsx)("option",{value:"support",children:"Support"}),(0,i.jsx)("option",{value:"18plus",children:"18+"}),(0,i.jsx)("option",{value:"other",children:"Other"})]})]}),(0,i.jsxs)(o.FormControl,{children:[(0,i.jsx)(o.FormLabel,{children:"Describe your issue"}),(0,i.jsx)(o.Textarea,{placeholder:"I need help with...",value:n,onChange:e=>l(e.target.value),rows:5})]})]}),(0,i.jsxs)(o.ModalFooter,{children:[(0,i.jsx)(o.Button,{mr:3,variant:"ghost",onClick:t,children:"Cancel"}),(0,i.jsx)(o.Button,{colorScheme:"blue",onClick:p,isLoading:u,children:"Create Ticket"})]})]})]})}o=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},4486:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>p,getStaticProps:()=>h,reportWebVitals:()=>b,routeModule:()=>y,unstable_getServerProps:()=>k,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>g});var i=r(1292),o=r(8834),a=r(786),n=r(3567),l=r(8077),c=r(2218),d=e([l,c]);[l,c]=d.then?(await d)():d;let u=(0,a.M)(c,"default"),h=(0,a.M)(c,"getStaticProps"),p=(0,a.M)(c,"getStaticPaths"),x=(0,a.M)(c,"getServerSideProps"),m=(0,a.M)(c,"config"),b=(0,a.M)(c,"reportWebVitals"),g=(0,a.M)(c,"unstable_getStaticProps"),S=(0,a.M)(c,"unstable_getStaticPaths"),j=(0,a.M)(c,"unstable_getStaticParams"),k=(0,a.M)(c,"unstable_getServerProps"),f=(0,a.M)(c,"unstable_getServerSideProps"),y=new i.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/tickets",pathname:"/tickets",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:c});s()}catch(e){s(e)}})},4722:e=>{e.exports=require("next-auth/react")},5542:e=>{e.exports=require("next-auth")},5806:e=>{e.exports=require("next-auth/next")},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8270,4874,752,6281,5333],()=>r(4486));module.exports=s})();