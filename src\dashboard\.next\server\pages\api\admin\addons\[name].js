"use strict";(()=>{var e={};e.id=4390,e.ids=[4390],e.modules={264:(e,s)=>{Object.defineProperty(s,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,s)=>{Object.defineProperty(s,"M",{enumerable:!0,get:function(){return function e(s,t){return t in s?s[t]:"then"in s&&"function"==typeof s.then?s.then(s=>e(s,t)):"function"==typeof s&&"default"===t?s:void 0}}})},842:(e,s,t)=>{t.r(s),t.d(s,{config:()=>h,default:()=>g,routeModule:()=>S});var n={};t.r(n),t.d(n,{default:()=>p});var o=t(3433),d=t(264),r=t(584),i=t(5806),a=t(8525),l=t(9021),c=t.n(l),u=t(3873),f=t.n(u),m=t(2115),y=t.n(m);function b(e=process.cwd()){let s=e;for(;s!==f().parse(s).root;){if(c().existsSync(f().join(s,"package.json")))return s;s=f().dirname(s)}return e}async function p(e,s){let t=await (0,i.getServerSession)(e,s,a.authOptions);if(!t)return s.status(401).json({error:"Unauthorized"});if(!t.user.isAdmin)return s.status(403).json({error:"Forbidden - Admin access required"});let{name:n}=e.query;if(!n||"string"!=typeof n)return s.status(400).json({error:"Invalid addon name"});try{let o=function(){let e=["404-bot/config.yml","config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>f().resolve(process.cwd(),e)),s=e.find(e=>c().existsSync(e));if(!s&&(s=e.map(e=>e.replace("config.yml","config.example.yml")).find(e=>c().existsSync(e))))try{let e=s.replace("config.example.yml","config.yml");c().copyFileSync(s,e),s=e}catch(e){}if(!s){let e=f().resolve(process.cwd(),"config.yml");try{c().writeFileSync(e,y().stringify({addons:{enabled:!0,disabled:[]}})),s=e}catch{}}if(!s)throw Error("config.yml not found and could not create default");return s}(),d=c().readFileSync(o,"utf8"),r=y().parse(d),i=function(e){let s=[`404-bot/src/addons/${e}/config.yml`,`src/addons/${e}/config.yml`,`../src/addons/${e}/config.yml`,`../../src/addons/${e}/config.yml`,`../../../src/addons/${e}/config.yml`,`../../../../src/addons/${e}/config.yml`].map(e=>f().resolve(process.cwd(),e)).find(e=>c().existsSync(e));if(!s){let t=f().resolve(__dirname,`../../../../../../../src/addons/${e}/config.yml`);c().existsSync(t)&&(s=t)}return s||null}(n);switch(e.method){case"GET":{let e=r.addons?.enabled&&(!r.addons.disabled||!r.addons.disabled.includes(n)),t="";return t=i?c().readFileSync(i,"utf8"):y().stringify({addon:{name:n,version:"1.0.0",description:"No description available",author:"Unknown",enabled:!0}}),s.status(200).json({name:n,enabled:e,configYaml:t})}case"PATCH":{let{enabled:d}=e.body;if("boolean"!=typeof d)return s.status(400).json({error:"Invalid request body"});r.addons||(r.addons={}),r.addons.disabled||(r.addons.disabled=[]),d?r.addons.disabled=r.addons.disabled.filter(e=>e!==n):r.addons.disabled.includes(n)||r.addons.disabled.push(n),c().writeFileSync(o,y().stringify(r));try{let e=b(),s=f().join(e,"addon-reload.signal");c().writeFileSync(s,JSON.stringify({timestamp:Date.now(),requestedBy:t.user?.email||"dashboard"}))}catch{}return s.status(200).json({name:n,enabled:d,configYaml:i?c().readFileSync(i,"utf8"):""})}case"PUT":{let{configYaml:t}=e.body;if(!t||"string"!=typeof t)return s.status(400).json({error:"Invalid configuration"});try{y().parse(t);let e=f().dirname(i||f().resolve(process.cwd(),`src/addons/${n}`));return c().existsSync(e)||c().mkdirSync(e,{recursive:!0}),c().writeFileSync(i||f().resolve(e,"config.yml"),t),s.status(200).json({name:n,enabled:!r.addons.disabled?.includes(n),configYaml:t})}catch(e){return s.status(400).json({error:"Invalid YAML syntax"})}}case"DELETE":{let e=function(e){let s=function(){let e=["404-bot/src/addons","src/addons","../src/addons","../../src/addons","../../../src/addons","../../../../src/addons","404-bot/dist/addons","dist/addons","dist/dashboard/dist/addons","dashboard/dist/addons","../dist/addons","../dist/dashboard/dist/addons","../../dist/addons","../../dist/dashboard/dist/addons","../../../dist/addons","../../../../dist/addons"].map(e=>f().resolve(process.cwd(),e)),s=[];e.forEach(e=>{c().existsSync(e)&&s.push(e)});let t=f().resolve(__dirname,"../../../../../../../src/addons"),n=f().resolve(__dirname,"../../../../../../../dist/addons");if(c().existsSync(t)&&s.push(t),c().existsSync(n)&&s.push(n),0===s.length)throw Error("No addons directories found");return Array.from(new Set(s))}(),t=e.replace(/\s+/g,"-");for(let n of s)for(let s of[f().join(n,e),f().join(n,t)])if(c().existsSync(s))return s;return null}(n);if(!e){let s=process.cwd().includes("dashboard")?f().resolve(process.cwd(),"..",".."):process.cwd();for(let t of[f().join(s,"src","addons",n),f().join(process.cwd(),"..","..","src","addons",n),f().join(process.cwd(),"src","addons",n)])if(c().existsSync(t)){e=t;break}}if(!e)return s.status(404).json({error:"Addon not found",details:`Could not locate addon "${n}" in any expected directories`});let d=f().join(e,"flow.json"),i=!1;if(c().existsSync(d)&&(i=!0),!i){let s=f().join(e,"index.ts");if(c().existsSync(s)){let e=c().readFileSync(s,"utf8");i=e.includes("Generated addon from visual builder")&&e.includes("author: 'Addon Builder'")}if(!i&&c().existsSync(f().join(e,"config.yml"))){let s=c().readFileSync(f().join(e,"config.yml"),"utf8"),t=y().parse(s);i=t?.author==="Addon Builder"||t?.description==="Generated addon from visual builder"}}if(!i)return s.status(403).json({error:"Cannot delete built-in addon",details:"Only custom addons created by the addon builder can be deleted"});r.addons?.disabled?.includes(n)&&(r.addons.disabled=r.addons.disabled.filter(e=>e!==n),c().writeFileSync(o,y().stringify(r)));let a=e=>{if(c().existsSync(e))try{c().readdirSync(e).forEach(s=>{let t=f().join(e,s);c().lstatSync(t).isDirectory()?a(t):c().unlinkSync(t)}),c().rmdirSync(e)}catch(e){throw Error(`Failed to delete addon directory: ${e}`)}};if(a(e),c().existsSync(e))throw Error(`Addon directory still exists after deletion attempt: ${e}`);let l=process.cwd().includes("dashboard")?f().resolve(process.cwd(),"..",".."):process.cwd();for(let e of[f().join(l,"dist","addons",n),f().join(process.cwd(),"..","..","dist","addons",n),f().join(process.cwd(),"dist","addons",n)])if(c().existsSync(e))try{a(e)}catch(e){}try{let e=b(),s=f().join(e,"addon-reload.signal");c().writeFileSync(s,JSON.stringify({timestamp:Date.now(),requestedBy:t.user?.email||"dashboard",action:"addon-deleted",addonName:n}))}catch{}return s.status(200).json({message:"Custom addon deleted successfully",name:n})}default:return s.status(405).json({error:"Method not allowed"})}}catch(e){return s.status(500).json({error:"Internal server error",details:e.message})}}let g=(0,r.M)(n,"default"),h=(0,r.M)(n,"config"),S=new o.PagesAPIRouteModule({definition:{kind:d.A.PAGES_API,page:"/api/admin/addons/[name]",pathname:"/api/admin/addons/[name]",bundlePath:"",filename:""},userland:n})},2115:e=>{e.exports=require("yaml")},3433:(e,s,t)=>{e.exports=t(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,s,t)=>{t.r(s),t.d(s,{authOptions:()=>a,default:()=>l});var n=t(5542),o=t.n(n);let d=require("next-auth/providers/discord");var r=t.n(d),i=t(8580);let a={providers:[r()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:s,profile:t})=>(s&&t&&(e.accessToken=s.access_token||null,e.id=t.id||null),e),async session({session:e,token:s}){if(e?.user){let t=s.id||null,n=s.accessToken||null;e.user.id=t,e.user.accessToken=n;let o=!1;if(t)if((i.dashboardConfig.dashboard.admins||[]).includes(t))o=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let s=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${t}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(s.ok){let t=await s.json();o=e.some(e=>t.roles?.includes(e))||!1}else await s.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:s}){let t=new URL(s),n=`${t.protocol}//localhost${t.port?`:${t.port}`:""}`;return e.startsWith(s)||e.startsWith(n)?e:s}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,s)=>{},warn:e=>{},debug:(e,s)=>{}}},l=o()(a)},8580:(e,s,t)=>{t.r(s),t.d(s,{dashboardConfig:()=>a,default:()=>l});var n=t(9021),o=t(2115),d=t.n(o),r=t(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>n.existsSync(e));if(!e){let s=r.resolve(__dirname,"../../../config.yml");n.existsSync(s)&&(e=s)}if(!e)throw Error("config.yml not found");let s=n.readFileSync(e,"utf8");i=d().parse(s)}catch(e){process.exit(1)}let a={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};a.bot.token||process.exit(1),a.bot.clientId&&a.bot.clientSecret||process.exit(1),a.bot.guildId||process.exit(1),a.database.url&&a.database.name||process.exit(1);let l=a},9021:e=>{e.exports=require("fs")}};var s=require("../../../../webpack-api-runtime.js");s.C(e);var t=s(s.s=842);module.exports=t})();