import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../../core/config';

// Hardcoded developer ID
const DEVELOPER_ID = '933023999770918932';

let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';
async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, { ...(dashboardConfig.database?.options || {}) });
  }
  return cachedClient.db(dbName);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ hasAccess: false, reason: 'unauthenticated' });
  }
  const userId = (session.user as any).id;
  if (userId === DEVELOPER_ID) {
    return res.status(200).json({ hasAccess: true, reason: 'developer' });
  }

  try {
    const db = await getDb();
    // Check if user already approved as tester
    const tester = await db.collection('experimental_testers').findOne({ userId });
    if (tester) {
      return res.status(200).json({ hasAccess: true, reason: 'tester' });
    }
    // Check if applications are currently open
    const setting = await db.collection('experimental_settings').findOne({ key: 'applications_enabled' });
    const applicationsEnabled = setting?.enabled === true;
    return res.status(200).json({ hasAccess: applicationsEnabled, reason: applicationsEnabled ? 'open' : 'closed' });
  } catch (error) {
    console.error('Error checking experimental access:', error);
    return res.status(500).json({ hasAccess: false, reason: 'error' });
  }
} 