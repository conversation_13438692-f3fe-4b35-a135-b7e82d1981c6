"use strict";(()=>{var e={};e.id=3689,e.ids=[3689],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2115:e=>{e.exports=require("yaml")},2326:e=>{e.exports=require("react-dom")},3762:(e,t,i)=>{i.d(t,{N:()=>h});var r=i(5542),s=i.n(r);let a=require("next-auth/providers/discord");var n=i.n(a),o=i(9021),l=i(2115),d=i.n(l),c=i(3873);let u={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>c.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=c.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");u=d().parse(t)}catch(e){process.exit(1)}let p={bot:{token:u.bot.token,clientId:u.bot.clientId,clientSecret:u.bot.clientSecret,guildId:u.bot.guildId,ticketCategoryId:u.bot.ticketCategoryId||null,ticketLogChannelId:u.bot.ticketLogChannelId||null,prefix:u.bot.prefix},dashboard:{admins:u.dashboard?.admins||[],adminRoleIds:u.dashboard?.adminRoleIds||[],session:{secret:u.dashboard?.session?.secret||u.bot.clientSecret}},database:{url:u.database.url,name:u.database.name,options:{maxPoolSize:u.database.options?.maxPoolSize||10,minPoolSize:u.database.options?.minPoolSize||1,maxIdleTimeMS:u.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:u.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:u.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:u.database.options?.connectTimeoutMS||1e4,retryWrites:u.database.options?.retryWrites!==!1,retryReads:u.database.options?.retryReads!==!1}}};p.bot.token||process.exit(1),p.bot.clientId&&p.bot.clientSecret||process.exit(1),p.bot.guildId||process.exit(1),p.database.url&&p.database.name||process.exit(1);let h={providers:[n()({clientId:p.bot.clientId,clientSecret:p.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:i})=>(t&&i&&(e.accessToken=t.access_token||null,e.id=i.id||null),e),async session({session:e,token:t}){if(e?.user){let i=t.id||null,r=t.accessToken||null;e.user.id=i,e.user.accessToken=r;let s=!1;if(i)if((p.dashboard.admins||[]).includes(i))s=!0;else{let e=p.dashboard.adminRoleIds||[];if(e.length&&p.bot.token&&p.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${p.bot.guildId}/members/${i}`,{headers:{Authorization:`Bot ${p.bot.token}`}});if(t.ok){let i=await t.json();s=e.some(e=>i.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let i=new URL(t),r=`${i.protocol}//localhost${i.port?`:${i.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:p.dashboard.session.secret||p.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};s()(h)},3873:e=>{e.exports=require("path")},3926:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{config:()=>b,default:()=>u,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>p,reportWebVitals:()=>g,routeModule:()=>v,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>x});var s=i(1292),a=i(8834),n=i(786),o=i(3567),l=i(8077),d=i(6060),c=e([l,d]);[l,d]=c.then?(await c)():c;let u=(0,n.M)(d,"default"),p=(0,n.M)(d,"getStaticProps"),h=(0,n.M)(d,"getStaticPaths"),m=(0,n.M)(d,"getServerSideProps"),b=(0,n.M)(d,"config"),g=(0,n.M)(d,"reportWebVitals"),x=(0,n.M)(d,"unstable_getStaticProps"),S=(0,n.M)(d,"unstable_getStaticPaths"),y=(0,n.M)(d,"unstable_getStaticParams"),f=(0,n.M)(d,"unstable_getServerProps"),j=(0,n.M)(d,"unstable_getServerSideProps"),v=new s.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/applications/[id]",pathname:"/applications/[id]",bundlePath:"",filename:""},components:{App:l.default,Document:o.default},userland:d});r()}catch(e){r(e)}})},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},5542:e=>{e.exports=require("next-auth")},5806:e=>{e.exports=require("next-auth/next")},6060:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{default:()=>p,getServerSideProps:()=>h});var s=i(8732),a=i(5806),n=i(3762),o=i(1011),l=i(9733),d=i(8358),c=i(2015),u=e([o,l]);function p(){let{id:e}=(0,d.useRouter)().query,[t,i]=(0,c.useState)(null),[r,a]=(0,c.useState)(!0),[n,u]=(0,c.useState)(null),[p,h]=(0,c.useState)({}),[m,b]=(0,c.useState)(!1),g=(0,l.useToast)(),x=(e,t,i)=>{h(r=>{if("checkbox"===i){let i=r[e]||[];return i.includes(t)?{...r,[e]:i.filter(e=>e!==t)}:{...r,[e]:[...i,t]}}return{...r,[e]:t}})},S=async e=>{if(e.preventDefault(),b(!0),u(null),!t){u("Application data not loaded."),b(!1);return}for(let e of t.questions)if(e.required&&(void 0===p[e.id]||""===p[e.id]||Array.isArray(p[e.id])&&0===p[e.id].length)){g({title:"Missing Required Field",description:`Please fill in the '${e.label}' field.`,status:"error",duration:5e3,isClosable:!0}),b(!1);return}try{let e=await fetch("/api/applications/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationId:t.id,answers:p})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to submit application")}g({title:"Application Submitted",description:"Your application has been successfully submitted!",status:"success",duration:5e3,isClosable:!0}),h({})}catch(e){u(e.message),g({title:"Submission Failed",description:e.message,status:"error",duration:5e3,isClosable:!0})}finally{b(!1)}};return r?(0,s.jsx)(o.A,{children:(0,s.jsx)(l.Center,{p:8,children:(0,s.jsxs)(l.VStack,{spacing:4,children:[(0,s.jsx)(l.Spinner,{size:"xl"}),(0,s.jsx)(l.Heading,{size:"md",children:"Loading application..."})]})})}):n?(0,s.jsx)(o.A,{children:(0,s.jsx)(l.Box,{p:8,children:(0,s.jsxs)(l.Alert,{status:"error",children:[(0,s.jsx)(l.AlertIcon,{}),(0,s.jsx)(l.AlertDescription,{children:n})]})})}):t?(0,s.jsx)(o.A,{children:(0,s.jsxs)(l.VStack,{spacing:8,p:8,align:"stretch",children:[(0,s.jsx)(l.Heading,{size:"xl",textAlign:"center",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",children:t.title}),(0,s.jsx)(l.Text,{fontSize:"lg",textAlign:"center",color:"gray.400",children:t.description}),(0,s.jsxs)(l.Box,{as:"form",onSubmit:S,mt:8,p:6,borderWidth:"1px",borderRadius:"lg",borderColor:"whiteAlpha.300",bg:"whiteAlpha.05",children:[(0,s.jsx)(l.Heading,{size:"md",mb:6,children:"Application Form"}),(0,s.jsxs)(l.VStack,{spacing:6,align:"stretch",children:[t.questions.map(e=>(0,s.jsxs)(l.FormControl,{isRequired:e.required,children:[(0,s.jsx)(l.FormLabel,{children:e.label}),"text"===e.type&&(0,s.jsx)(l.Input,{type:"text",placeholder:e.placeholder,value:p[e.id]||"",onChange:t=>x(e.id,t.target.value,e.type)}),"number"===e.type&&(0,s.jsx)(l.Input,{type:"number",placeholder:e.placeholder,value:p[e.id]||"",onChange:t=>x(e.id,t.target.value,e.type)}),"textarea"===e.type&&(0,s.jsx)(l.Textarea,{placeholder:e.placeholder,value:p[e.id]||"",onChange:t=>x(e.id,t.target.value,e.type)}),"select"===e.type&&e.options&&(0,s.jsx)(l.Select,{placeholder:e.placeholder||"Select an option",value:p[e.id]||"",onChange:t=>x(e.id,t.target.value,e.type),children:e.options.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),"radio"===e.type&&e.options&&(0,s.jsx)(l.RadioGroup,{value:p[e.id]||"",onChange:t=>x(e.id,t,e.type),children:(0,s.jsx)(l.HStack,{spacing:"24px",children:e.options.map(e=>(0,s.jsx)(l.Radio,{value:e,children:e},e))})}),"checkbox"===e.type&&e.options&&(0,s.jsx)(l.VStack,{align:"stretch",children:e.options.map(t=>(0,s.jsx)(l.Checkbox,{isChecked:(p[e.id]||[]).includes(t),onChange:()=>x(e.id,t,e.type),children:t},t))})]},e.id)),(0,s.jsx)(l.Button,{type:"submit",colorScheme:"blue",size:"lg",isLoading:m,loadingText:"Submitting...",alignSelf:"flex-end",mt:8,children:"Submit Application"})]})]})]})}):(0,s.jsx)(o.A,{children:(0,s.jsx)(l.Box,{p:8,children:(0,s.jsxs)(l.Alert,{status:"warning",children:[(0,s.jsx)(l.AlertIcon,{}),(0,s.jsx)(l.AlertDescription,{children:"Application data could not be loaded."})]})})})}[o,l]=u.then?(await u)():u;let h=async e=>{let t=await (0,a.getServerSession)(e.req,e.res,n.N);return t?t.user?.isAdmin?{props:{}}:{redirect:{destination:"/unauthorized",permanent:!1}}:{redirect:{destination:"/api/auth/signin?callbackUrl="+encodeURIComponent(e.resolvedUrl),permanent:!1}}};r()}catch(e){r(e)}})},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[8270,4874,752,6281,5333],()=>i(3926));module.exports=r})();