import React from 'react';
import { Box } from '@chakra-ui/react';
import dynamic from 'next/dynamic';

// Ace editor is not SSR-friendly, so we load it dynamically.
const AceEditor = dynamic(
  async () => {
    const ace = await import('react-ace');
    // Import required modes, themes, and extensions
    await import('ace-builds/src-noconflict/mode-yaml');
    await import('ace-builds/src-noconflict/theme-twilight');
    await import('ace-builds/src-noconflict/ext-language_tools'); // For autocompletion
    return ace;
  },
  { ssr: false }
);

interface YamlEditorProps {
  value: string;
  onChange: (value: string) => void; // Ace onChange provides string directly
  height?: string | number;
}

const YamlEditor: React.FC<YamlEditorProps> = ({ value, onChange, height = '60vh' }) => {
  return (
    <Box
      borderWidth="1px"
      borderColor="purple.600"
      borderRadius="md"
      overflow="hidden"
      height={height}
      sx={{
        '.ace_editor': {
          fontFamily: "'Fira Code', 'Consolas', 'Monaco', 'monospace' !important",
          fontSize: '14px !important',
          lineHeight: '1.6 !important',
        },
        '.ace_gutter': {
          background: '#232323', // Match twilight theme background
        },
        '.ace_scroller': {
          backgroundColor: '#1e1e1e', // Match twilight theme background
        },
      }}
    >
      {typeof window !== 'undefined' && AceEditor && (
        <AceEditor
          mode="yaml"
          theme="twilight"
          onChange={onChange}
          value={value}
          name="YAML_EDITOR"
          editorProps={{ $blockScrolling: true }}
          width="100%"
          height="100%"
          setOptions={{
            enableBasicAutocompletion: true,
            enableLiveAutocompletion: true,
            enableSnippets: true,
            showLineNumbers: true,
            tabSize: 2,
            useWorker: false, // Disable worker to avoid issues in some environments
            showPrintMargin: false,
          }}
        />
      )}
    </Box>
  );
};

export default YamlEditor;
