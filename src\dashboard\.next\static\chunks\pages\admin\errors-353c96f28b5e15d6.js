(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2437],{41763:(e,r,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/errors",function(){return s(92455)}])},92455:(e,r,s)=>{"use strict";s.r(r),s.d(r,{__N_SSP:()=>B,default:()=>H});var l=s(94513),o=s(94285),t=s(95845),i=s(22907),n=s(44327),c=s(59001),a=s(79156),d=s(78902),h=s(31678),x=s(51961),j=s(41611),m=s(62690),p=s(68443),u=s(59818),g=s(64057),f=s(25964),S=s(75635),w=s(71601),b=s(20429),y=s(19521),E=s(57561),z=s(26977),C=s(49451),v=s(55920),T=s(52216),k=s(15373),_=s(51927),L=s(8595),N=s(1341),W=s(35981),D=s(95497),O=s(79961),A=s(24792),F=s(73011),I=s(83901),U=s(9557),X=s(7680),R=s(52922),$=s(47847),M=s(59365),P=s(85104),J=s(28245),K=s(6523),V=s(35164),Z=s(97146),G=s(60341),q=s(12772),B=!0;function H(){let[e,r]=(0,o.useState)([]),[s,B]=(0,o.useState)(!0),[H,Q]=(0,o.useState)(null),[Y,ee]=(0,o.useState)("all"),[er,es]=(0,o.useState)("all"),[el,eo]=(0,o.useState)(""),[et,ei]=(0,o.useState)(new Set),{isOpen:en,onOpen:ec,onClose:ea}=(0,t.j)(),ed=(0,i.d)(),{currentScheme:eh}=(0,q.DP)(),ex=(0,n.dU)("whiteAlpha.100","gray.800"),ej=(0,n.dU)("gray.200","gray.600");(0,o.useEffect)(()=>{em()},[]);let em=async()=>{try{B(!0);let e=await fetch("/api/admin/errors");if(e.ok){let s=await e.json();r(s.errors||[])}else throw Error("Failed to fetch errors")}catch(e){ed({title:"Error",description:"Failed to fetch error logs",status:"error",duration:5e3})}finally{B(!1)}},ep=async()=>{try{if((await fetch("/api/admin/errors",{method:"DELETE"})).ok)r([]),ed({title:"Success",description:"All error logs have been cleared",status:"success",duration:3e3});else throw Error("Failed to clear errors")}catch(e){ed({title:"Error",description:"Failed to clear error logs",status:"error",duration:5e3})}},eu=e=>{let r=new Set(et);r.has(e)?r.delete(e):r.add(e),ei(r)},eg=e=>{Q(e),ec()},ef=e=>{switch(e){case"error":return"red";case"warn":return"yellow";case"info":return"blue";default:return"gray"}},eS=e.filter(e=>{let r="all"===Y||e.level===Y,s="all"===er||e.source===er,l=""===el||e.message.toLowerCase().includes(el.toLowerCase())||e.source.toLowerCase().includes(el.toLowerCase());return r&&s&&l}),ew=[...new Set(e.map(e=>e.source))];return(0,l.jsx)(G.A,{children:(0,l.jsxs)(c.m,{maxW:"container.xl",py:8,children:[(0,l.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,l.jsxs)(d.z,{justify:"space-between",align:"center",children:[(0,l.jsxs)(a.T,{align:"start",spacing:1,children:[(0,l.jsxs)(h.D,{size:"lg",display:"flex",alignItems:"center",children:[" ",(0,l.jsx)(K.g,{icon:V.wG0,style:{marginRight:"0.5rem",color:"#ff4d4d"}})," ",(0,l.jsxs)(x.a,{as:"span",bgGradient:"linear(to-r, red.400, orange.400)",bgClip:"text",children:[" ","Error Logs"]})]}),(0,l.jsx)(j.E,{color:eh.colors.textSecondary,fontSize:"sm",children:"Monitor and manage system errors and warnings"})]}),(0,l.jsxs)(d.z,{spacing:3,children:[(0,l.jsx)(m.$,{leftIcon:(0,l.jsx)(Z.a4x,{}),variant:"outline",size:"sm",onClick:()=>{let e="data:application/json;charset=utf-8,"+encodeURIComponent(JSON.stringify(eS,null,2)),r="error-logs-".concat(new Date().toISOString().split("T")[0],".json"),s=document.createElement("a");s.setAttribute("href",e),s.setAttribute("download",r),s.click()},isDisabled:0===eS.length,children:"Export"}),(0,l.jsx)(m.$,{leftIcon:(0,l.jsx)(Z.jTZ,{}),colorScheme:"blue",size:"sm",onClick:em,isLoading:s,children:"Refresh"}),(0,l.jsx)(m.$,{leftIcon:(0,l.jsx)(Z.IXo,{}),colorScheme:"red",variant:"outline",size:"sm",onClick:ep,isDisabled:0===e.length,children:"Clear All"})]})]}),(0,l.jsxs)(p.Z,{bg:ex,borderColor:ej,backdropFilter:"blur(10px)",children:[" ",(0,l.jsx)(u.b,{children:(0,l.jsxs)(d.z,{spacing:4,wrap:"wrap",children:[(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontSize:"sm",fontWeight:"medium",mb:2,color:eh.colors.text,children:"Search"})," ",(0,l.jsx)(g.p,{placeholder:"Search errors...",value:el,onChange:e=>eo(e.target.value),size:"sm",w:"200px"})]}),(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontSize:"sm",fontWeight:"medium",mb:2,color:eh.colors.text,children:"Level"})," ",(0,l.jsxs)(f.l,{value:Y,onChange:e=>ee(e.target.value),size:"sm",w:"120px",children:[(0,l.jsx)("option",{value:"all",children:"All Levels"}),(0,l.jsx)("option",{value:"error",children:"Error"}),(0,l.jsx)("option",{value:"warn",children:"Warning"}),(0,l.jsx)("option",{value:"info",children:"Info"})]})]}),(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontSize:"sm",fontWeight:"medium",mb:2,color:eh.colors.text,children:"Source"})," ",(0,l.jsxs)(f.l,{value:er,onChange:e=>es(e.target.value),size:"sm",w:"150px",children:[(0,l.jsx)("option",{value:"all",children:"All Sources"}),ew.map(e=>(0,l.jsx)("option",{value:e,children:e},e))]})]}),(0,l.jsx)(S.h,{}),(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontSize:"sm",fontWeight:"medium",mb:2,color:eh.colors.text,children:"Results"})," ",(0,l.jsxs)(w.E,{colorScheme:"blue",fontSize:"sm",px:2,py:1,children:[eS.length," of ",e.length]})]})]})})]}),(0,l.jsxs)(p.Z,{bg:ex,borderColor:ej,backdropFilter:"blur(10px)",children:[" ",(0,l.jsxs)(b.a,{children:[(0,l.jsx)(h.D,{size:"md",color:eh.colors.text,children:"Recent Errors"})," "]}),(0,l.jsx)(u.b,{children:s?(0,l.jsx)(y.s,{justify:"center",py:8,children:(0,l.jsx)(E.y,{size:"lg",color:eh.colors.primary})}):0===eS.length?(0,l.jsxs)(z.F,{status:"info",children:[(0,l.jsx)(C._,{}),(0,l.jsx)(v.X,{children:"No errors found!"}),(0,l.jsx)(T.T,{children:0===e.length?"No error logs are currently recorded.":"No errors match your current filters."})]}):(0,l.jsx)(x.a,{overflowX:"auto",children:(0,l.jsxs)(k.X,{variant:"simple",size:"sm",children:[(0,l.jsx)(_.d,{children:(0,l.jsxs)(L.Tr,{children:[(0,l.jsx)(N.Th,{color:eh.colors.textSecondary,children:"Time"})," ",(0,l.jsx)(N.Th,{color:eh.colors.textSecondary,children:"Level"})," ",(0,l.jsx)(N.Th,{color:eh.colors.textSecondary,children:"Source"})," ",(0,l.jsx)(N.Th,{color:eh.colors.textSecondary,children:"Message"})," ",(0,l.jsx)(N.Th,{color:eh.colors.textSecondary,children:"Actions"})," "]})}),(0,l.jsx)(W.N,{children:eS.map(e=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)(L.Tr,{children:[(0,l.jsx)(D.Td,{children:(0,l.jsx)(j.E,{fontSize:"xs",color:eh.colors.textSecondary,children:new Date(e.timestamp).toLocaleString()})}),(0,l.jsx)(D.Td,{children:(0,l.jsx)(w.E,{colorScheme:ef(e.level),size:"sm",children:e.level.toUpperCase()})}),(0,l.jsx)(D.Td,{children:(0,l.jsx)(O.C,{fontSize:"xs",children:e.source})}),(0,l.jsx)(D.Td,{children:(0,l.jsxs)(j.E,{fontSize:"sm",noOfLines:2,maxW:"300px",color:eh.colors.text,children:[" ",e.message]})}),(0,l.jsx)(D.Td,{children:(0,l.jsxs)(d.z,{spacing:1,children:[(0,l.jsx)(A.m,{label:"View Details",children:(0,l.jsx)(F.K,{"aria-label":"View details",icon:(0,l.jsx)(Z.Vap,{}),size:"xs",variant:"ghost",onClick:()=>eg(e)})}),(0,l.jsx)(A.m,{label:et.has(e.id)?"Collapse":"Expand",children:(0,l.jsx)(F.K,{"aria-label":"Toggle expansion",icon:et.has(e.id)?(0,l.jsx)(Z.wAb,{}):(0,l.jsx)(Z.fK4,{}),size:"xs",variant:"ghost",onClick:()=>eu(e.id)})})]})})]},e.id),(0,l.jsx)(L.Tr,{children:(0,l.jsx)(D.Td,{colSpan:5,p:0,children:(0,l.jsx)(I.S,{in:et.has(e.id),animateOpacity:!0,children:(0,l.jsxs)(x.a,{p:4,bg:(0,n.dU)("gray.50","gray.700"),children:[e.stack&&(0,l.jsxs)(x.a,{mb:3,children:[(0,l.jsx)(j.E,{fontSize:"sm",fontWeight:"medium",mb:2,color:eh.colors.text,children:"Stack Trace:"})," ",(0,l.jsx)(O.C,{display:"block",whiteSpace:"pre-wrap",fontSize:"xs",p:2,children:e.stack})]}),e.metadata&&Object.keys(e.metadata).length>0&&(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontSize:"sm",fontWeight:"medium",mb:2,color:eh.colors.text,children:"Metadata:"})," ",(0,l.jsx)(O.C,{display:"block",whiteSpace:"pre-wrap",fontSize:"xs",p:2,children:JSON.stringify(e.metadata,null,2)})]})]})})})})]}))})]})})})]})]}),(0,l.jsxs)(U.aF,{isOpen:en,onClose:ea,size:"xl",children:[(0,l.jsx)(X.m,{}),(0,l.jsxs)(R.$,{bg:(0,n.dU)("white","gray.800"),children:[" ",(0,l.jsx)($.r,{children:"Error Details"}),(0,l.jsx)(M.s,{}),(0,l.jsx)(P.c,{children:H&&(0,l.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,l.jsxs)(d.z,{children:[(0,l.jsx)(w.E,{colorScheme:ef(H.level),children:H.level.toUpperCase()}),(0,l.jsx)(j.E,{fontSize:"sm",color:eh.colors.textSecondary,children:new Date(H.timestamp).toLocaleString()})]}),(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontWeight:"medium",mb:2,color:eh.colors.text,children:"Source:"})," ",(0,l.jsx)(O.C,{children:H.source})]}),(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontWeight:"medium",mb:2,color:eh.colors.text,children:"Message:"})," ",(0,l.jsx)(j.E,{color:eh.colors.text,children:H.message})," "]}),H.stack&&(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontWeight:"medium",mb:2,color:eh.colors.text,children:"Stack Trace:"})," ",(0,l.jsx)(O.C,{display:"block",whiteSpace:"pre-wrap",fontSize:"sm",p:3,children:H.stack})]}),H.metadata&&(0,l.jsxs)(x.a,{children:[(0,l.jsx)(j.E,{fontWeight:"medium",mb:2,color:eh.colors.text,children:"Additional Data:"})," ",(0,l.jsx)(O.C,{display:"block",whiteSpace:"pre-wrap",fontSize:"sm",p:3,children:JSON.stringify(H.metadata,null,2)})]})]})}),(0,l.jsx)(J.j,{children:(0,l.jsx)(m.$,{onClick:ea,children:"Close"})})]})]})]})})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4108,6981,9998,4976,217,2965,5795,341,636,6593,8792],()=>r(41763)),_N_E=e.O()}]);