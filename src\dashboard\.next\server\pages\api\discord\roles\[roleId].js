"use strict";(()=>{var e={};e.id=2671,e.ids=[2671],e.modules={86:(e,t,o)=>{o.r(t),o.d(t,{config:()=>f,default:()=>c,routeModule:()=>m});var r={};o.r(r),o.d(r,{default:()=>u});var s=o(3433),a=o(264),i=o(584),n=o(5806),d=o(8525),l=o(8580);async function u(e,t){try{let o=await (0,n.getServerSession)(e,t,d.authOptions);if(!o?.user)return t.status(401).json({error:"Unauthorized"});if(!o.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:r,token:s}=l.dashboardConfig.bot;if(!s||!r)return t.status(500).json({error:"Bot configuration missing"});let{roleId:a}=e.query;if(!a||"string"!=typeof a)return t.status(400).json({error:"Role ID is required"});if("PATCH"===e.method)try{let{color:o,...i}=e.body;o&&(i.color=o=parseInt(o.replace("#",""),16));let n=await fetch(`https://discord.com/api/v10/guilds/${r}/roles/${a}`,{method:"PATCH",headers:{Authorization:`Bot ${s}`,"Content-Type":"application/json"},body:JSON.stringify(i)});if(!n.ok){let e;try{e=await n.json()}catch{e=await n.text()}return t.status(n.status).json(e)}let d=await n.json();return t.status(200).json(d)}catch(e){return t.status(500).json({error:"Failed to update role"})}if("DELETE"===e.method)try{let e=await fetch(`https://discord.com/api/v10/guilds/${r}/roles/${a}`,{method:"DELETE",headers:{Authorization:`Bot ${s}`}});if(!e.ok){let o;try{o=await e.json()}catch{o=await e.text()}return t.status(e.status).json(o)}return t.status(200).json({message:"Role deleted successfully"})}catch(e){return t.status(500).json({error:"Failed to delete role"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let c=(0,i.M)(r,"default"),f=(0,i.M)(r,"config"),m=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/roles/[roleId]",pathname:"/api/discord/roles/[roleId]",bundlePath:"",filename:""},userland:r})},264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var r=o(5542),s=o.n(r);let a=require("next-auth/providers/discord");var i=o.n(a),n=o(8580);let d={providers:[i()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,r=t.accessToken||null;e.user.id=o,e.user.accessToken=r;let s=!1;if(o)if((n.dashboardConfig.dashboard.admins||[]).includes(o))s=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();s=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),r=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var r=o(9021),s=o(2115),a=o.n(s),i=o(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");n=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=86);module.exports=o})();