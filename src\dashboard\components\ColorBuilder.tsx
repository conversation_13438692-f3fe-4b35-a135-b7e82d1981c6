import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Input,
  Button,
  FormControl,
  FormLabel,
  Card,
  CardBody,
  Badge,
  Icon,
  useToast,
  Divider,
  SimpleGrid,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  useDisclosure,
} from '@chakra-ui/react';
import { FiSave, FiEye, FiTrash2, FiEdit3, FiPlus } from 'react-icons/fi';
import { FaHome, FaUsers, FaCheck } from 'react-icons/fa';
import { ColorScheme, useTheme } from '../contexts/ThemeContext';

interface ColorBuilderProps {
  isOpen: boolean;
  onClose: () => void;
}

const COLOR_EXPLANATIONS = {
  primary: { 
    label: 'Primary Color', 
    description: 'Main accent color for buttons, active states, and highlights',
    example: 'Active sidebar items, primary buttons'
  },
  primaryLight: { 
    label: 'Primary Light', 
    description: 'Lighter version of primary color for hover states',
    example: 'Button hover effects, gradients'
  },
  primaryDark: { 
    label: 'Primary Dark', 
    description: 'Darker version of primary color for pressed states',
    example: 'Button active states, deeper accents'
  },
  secondary: { 
    label: 'Secondary Color', 
    description: 'Supporting accent color for variety',
    example: 'Secondary buttons, alternative highlights'
  },
  accent: { 
    label: 'Accent Color', 
    description: 'Decorative color for gradients and special elements',
    example: 'Gradient endpoints, special indicators'
  },
  background: { 
    label: 'Background', 
    description: 'Main dashboard background color',
    example: 'Page background, main layout'
  },
  surface: { 
    label: 'Surface', 
    description: 'Color for cards, sidebar, and elevated elements',
    example: 'Cards, sidebar, modals'
  },
  text: { 
    label: 'Primary Text', 
    description: 'Main text color for headings and important content',
    example: 'Headings, active text, important labels'
  },
  textSecondary: { 
    label: 'Secondary Text', 
    description: 'Subdued text color for descriptions and less important content',
    example: 'Descriptions, inactive states, subtitles'
  },
  border: { 
    label: 'Border Color', 
    description: 'Color for borders, dividers, and outlines',
    example: 'Card borders, input outlines, dividers'
  },
  success: { 
    label: 'Success Color', 
    description: 'Color for success states and positive actions',
    example: 'Success messages, completed states'
  },
  warning: { 
    label: 'Warning Color', 
    description: 'Color for warning states and caution',
    example: 'Warning messages, pending states'
  },
  error: { 
    label: 'Error Color', 
    description: 'Color for error states and destructive actions',
    example: 'Error messages, delete buttons'
  },
  info: { 
    label: 'Info Color', 
    description: 'Color for informational content',
    example: 'Info messages, helpful hints'
  },
};

export default function ColorBuilder({ isOpen, onClose }: ColorBuilderProps) {
  const { currentScheme, addCustomScheme, customSchemes, deleteCustomScheme } = useTheme();
  const toast = useToast();
  const { isOpen: isNameModalOpen, onOpen: onNameModalOpen, onClose: onNameModalClose } = useDisclosure();
  
  const [previewColors, setPreviewColors] = useState<ColorScheme['colors']>(currentScheme.colors);
  const [schemeName, setSchemeName] = useState('');
  const [schemeDescription, setSchemeDescription] = useState('');

  const handleColorChange = (colorKey: keyof ColorScheme['colors'], value: string) => {
    setPreviewColors(prev => ({
      ...prev,
      [colorKey]: value,
    }));
  };

  const handleSaveScheme = () => {
    if (!schemeName.trim()) {
      toast({
        title: 'Name Required',
        description: 'Please enter a name for your custom theme',
        status: 'warning',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const newScheme: ColorScheme = {
      id: `custom-${Date.now()}`,
      name: schemeName.trim(),
      description: schemeDescription.trim() || `Custom theme: ${schemeName.trim()}`,
      isCustom: true,
      colors: { ...previewColors },
    };

    addCustomScheme(newScheme);
    setSchemeName('');
    setSchemeDescription('');
    onNameModalClose();
    onClose();

    toast({
      title: 'Theme Saved',
      description: `Your custom theme "${newScheme.name}" has been saved and applied`,
      status: 'success',
      duration: 4000,
      isClosable: true,
    });
  };

  const resetToBase = () => {
    setPreviewColors(currentScheme.colors);
    toast({
      title: 'Colors Reset',
      description: 'Colors have been reset to the current theme',
      status: 'info',
      duration: 2000,
      isClosable: true,
    });
  };

  const handleDeleteCustomScheme = (schemeId: string, schemeName: string) => {
    deleteCustomScheme(schemeId);
    toast({
      title: 'Theme Deleted',
      description: `Custom theme "${schemeName}" has been deleted`,
      status: 'info',
      duration: 3000,
      isClosable: true,
    });
  };

  // Live Preview Components
  const PreviewCard = ({ title, children }: { title: string; children: React.ReactNode }) => (
    <Card bg={previewColors.surface} borderColor={previewColors.border} borderWidth="1px">
      <CardBody p={4}>
        <VStack align="stretch" spacing={3}>
          <Text fontWeight="bold" color={previewColors.text} fontSize="sm">
            {title}
          </Text>
          {children}
        </VStack>
      </CardBody>
    </Card>
  );

  return (
    <>
      <Modal isOpen={isOpen} onClose={onClose} size="6xl">
        <ModalOverlay bg="blackAlpha.700" backdropFilter="blur(10px)" />
        <ModalContent 
          bg={previewColors.background}
          borderColor={previewColors.border}
          borderWidth="1px"
          boxShadow={`0 20px 25px -5px ${previewColors.background}40, 0 10px 10px -5px ${previewColors.background}40`}
          maxH="90vh"
          overflow="hidden"
        >
          <ModalHeader color={previewColors.text} borderBottom="1px solid" borderColor={previewColors.border}>
            <HStack>
              <Icon as={FiEdit3} />
              <Text>Custom Color Builder</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color={previewColors.textSecondary} />
          
          <ModalBody overflow="auto" p={6}>
            <VStack spacing={6} align="stretch">
              {/* Instructions */}
              <Alert status="info" variant="subtle" rounded="md" bg={`${previewColors.info}20`} borderColor={previewColors.info}>
                <AlertIcon color={previewColors.info} />
                <VStack align="start" spacing={1}>
                  <AlertTitle color={previewColors.text}>Color Builder</AlertTitle>
                  <AlertDescription color={previewColors.textSecondary}>
                    Adjust colors and see live preview. Click "Save Theme" when you're happy with your design.
                  </AlertDescription>
                </VStack>
              </Alert>

              <SimpleGrid columns={{ base: 1, xl: 2 }} spacing={8}>
                {/* Color Controls */}
                <VStack spacing={4} align="stretch">
                  <Text fontSize="lg" fontWeight="bold" color={previewColors.text}>
                    Color Settings
                  </Text>
                  
                  <VStack spacing={4} align="stretch" maxH="500px" overflow="auto" pr={2}>
                    {Object.entries(COLOR_EXPLANATIONS).map(([key, info]) => (
                      <Card key={key} bg={previewColors.surface} borderColor={previewColors.border} borderWidth="1px">
                        <CardBody p={4}>
                          <VStack align="stretch" spacing={3}>
                            <VStack align="start" spacing={1}>
                              <HStack>
                                <Box 
                                  w={4} 
                                  h={4} 
                                  bg={previewColors[key as keyof ColorScheme['colors']]} 
                                  rounded="md" 
                                  border="1px solid"
                                  borderColor={previewColors.border}
                                />
                                <Text fontWeight="bold" color={previewColors.text} fontSize="sm">
                                  {info.label}
                                </Text>
                              </HStack>
                              <Text fontSize="xs" color={previewColors.textSecondary}>
                                {info.description}
                              </Text>
                              <Text fontSize="xs" color={previewColors.textSecondary} fontStyle="italic">
                                Used for: {info.example}
                              </Text>
                            </VStack>
                            
                            <HStack>
                              <Input
                                type="color"
                                value={previewColors[key as keyof ColorScheme['colors']]}
                                onChange={(e) => handleColorChange(key as keyof ColorScheme['colors'], e.target.value)}
                                w={12}
                                h={8}
                                p={0}
                                border="none"
                                rounded="md"
                              />
                              <Input
                                value={previewColors[key as keyof ColorScheme['colors']]}
                                onChange={(e) => handleColorChange(key as keyof ColorScheme['colors'], e.target.value)}
                                placeholder="#000000"
                                color={previewColors.text}
                                borderColor={previewColors.border}
                                fontSize="sm"
                                fontFamily="mono"
                              />
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    ))}
                  </VStack>
                </VStack>

                {/* Live Preview */}
                <VStack spacing={4} align="stretch">
                  <Text fontSize="lg" fontWeight="bold" color={previewColors.text}>
                    Live Preview
                  </Text>
                  
                  <Box 
                    bg={previewColors.background}
                    p={4}
                    rounded="lg"
                    border="2px solid"
                    borderColor={previewColors.border}
                    minH="500px"
                  >
                    <VStack spacing={4} align="stretch">
                      {/* Preview Header */}
                      <Box bg={previewColors.surface} p={3} rounded="md" borderColor={previewColors.border} borderWidth="1px">
                        <HStack justify="space-between">
                          <Text fontSize="md" fontWeight="bold" color={previewColors.text}>
                            Dashboard Preview
                          </Text>
                          <Badge bg={previewColors.primary} color="white" px={2} py={1} rounded="md">
                            Live
                          </Badge>
                        </HStack>
                      </Box>

                      {/* Preview Cards */}
                      <SimpleGrid columns={2} spacing={3}>
                        <PreviewCard title="Statistics">
                          <VStack align="start" spacing={2}>
                            <Text fontSize="xl" fontWeight="bold" color={previewColors.text}>
                              1,234
                            </Text>
                            <Text fontSize="xs" color={previewColors.textSecondary}>
                              Total Users
                            </Text>
                            <Text fontSize="xs" color={previewColors.success}>
                              +12% this month
                            </Text>
                          </VStack>
                        </PreviewCard>

                        <PreviewCard title="Activity">
                          <VStack align="start" spacing={2}>
                            <Text fontSize="xl" fontWeight="bold" color={previewColors.text}>
                              89
                            </Text>
                            <Text fontSize="xs" color={previewColors.textSecondary}>
                              Active Sessions
                            </Text>
                            <Text fontSize="xs" color={previewColors.warning}>
                              Monitoring
                            </Text>
                          </VStack>
                        </PreviewCard>
                      </SimpleGrid>

                      {/* Preview Buttons */}
                      <VStack spacing={2} align="stretch">
                        <Text fontSize="sm" fontWeight="bold" color={previewColors.text}>
                          Button Examples
                        </Text>
                        <HStack spacing={2}>
                          <Button
                            bg={previewColors.primary}
                            color="white"
                            size="sm"
                            leftIcon={<Icon as={FaHome} />}
                          >
                            Primary
                          </Button>
                          <Button
                            variant="outline"
                            borderColor={previewColors.border}
                            color={previewColors.text}
                            size="sm"
                            leftIcon={<Icon as={FaUsers} />}
                          >
                            Secondary
                          </Button>
                          <Button
                            variant="ghost"
                            color={previewColors.textSecondary}
                            size="sm"
                            leftIcon={<Icon as={FiEye} />}
                          >
                            Ghost
                          </Button>
                        </HStack>
                      </VStack>

                      {/* Preview Status Messages */}
                      <VStack spacing={2} align="stretch">
                        <Text fontSize="sm" fontWeight="bold" color={previewColors.text}>
                          Status Examples
                        </Text>
                        <HStack spacing={2} fontSize="xs">
                          <Badge bg={previewColors.success} color="white" px={2} py={1} rounded="md">
                            Success
                          </Badge>
                          <Badge bg={previewColors.warning} color="white" px={2} py={1} rounded="md">
                            Warning
                          </Badge>
                          <Badge bg={previewColors.error} color="white" px={2} py={1} rounded="md">
                            Error
                          </Badge>
                          <Badge bg={previewColors.info} color="white" px={2} py={1} rounded="md">
                            Info
                          </Badge>
                        </HStack>
                      </VStack>
                    </VStack>
                  </Box>
                </VStack>
              </SimpleGrid>

              {/* Custom Schemes Management */}
              {customSchemes.length > 0 && (
                <>
                  <Divider borderColor={previewColors.border} />
                  <VStack spacing={3} align="stretch">
                    <Text fontSize="lg" fontWeight="bold" color={previewColors.text}>
                      Your Custom Themes
                    </Text>
                    <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={3}>
                      {customSchemes.map((scheme) => (
                        <Card key={scheme.id} bg={previewColors.surface} borderColor={previewColors.border} borderWidth="1px">
                          <CardBody p={3}>
                            <VStack align="stretch" spacing={2}>
                              <HStack justify="space-between">
                                <Text fontWeight="bold" color={previewColors.text} fontSize="sm" noOfLines={1}>
                                  {scheme.name}
                                </Text>
                                <Button
                                  size="xs"
                                  variant="ghost"
                                  color={previewColors.error}
                                  onClick={() => handleDeleteCustomScheme(scheme.id, scheme.name)}
                                >
                                  <Icon as={FiTrash2} />
                                </Button>
                              </HStack>
                              <Text fontSize="xs" color={previewColors.textSecondary} noOfLines={2}>
                                {scheme.description}
                              </Text>
                              <HStack spacing={1}>
                                <Box w={3} h={3} bg={scheme.colors.primary} rounded="full" />
                                <Box w={3} h={3} bg={scheme.colors.secondary} rounded="full" />
                                <Box w={3} h={3} bg={scheme.colors.accent} rounded="full" />
                                <Box w={3} h={3} bg={scheme.colors.success} rounded="full" />
                              </HStack>
                            </VStack>
                          </CardBody>
                        </Card>
                      ))}
                    </SimpleGrid>
                  </VStack>
                </>
              )}
            </VStack>
          </ModalBody>

          <ModalFooter borderTop="1px solid" borderColor={previewColors.border}>
            <HStack spacing={3}>
              <Button variant="ghost" onClick={resetToBase} color={previewColors.textSecondary}>
                Reset Colors
              </Button>
              <Button variant="ghost" onClick={onClose} color={previewColors.textSecondary}>
                Cancel
              </Button>
              <Button
                bg={previewColors.primary}
                color="white"
                _hover={{ bg: previewColors.primaryDark }}
                leftIcon={<Icon as={FiSave} />}
                onClick={onNameModalOpen}
              >
                Save Theme
              </Button>
            </HStack>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Save Theme Name Modal */}
      <Modal isOpen={isNameModalOpen} onClose={onNameModalClose}>
        <ModalOverlay bg="blackAlpha.600" backdropFilter="blur(10px)" />
        <ModalContent bg={previewColors.background} borderColor={previewColors.border}>
          <ModalHeader color={previewColors.text}>
            <HStack>
              <Icon as={FiPlus} />
              <Text>Save Custom Theme</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton color={previewColors.textSecondary} />
          <ModalBody>
            <VStack spacing={4} align="stretch">
              <FormControl>
                <FormLabel color={previewColors.text}>Theme Name</FormLabel>
                <Input
                  value={schemeName}
                  onChange={(e) => setSchemeName(e.target.value)}
                  placeholder="My Custom Theme"
                  color={previewColors.text}
                  borderColor={previewColors.border}
                />
              </FormControl>
              <FormControl>
                <FormLabel color={previewColors.text}>Description (optional)</FormLabel>
                <Input
                  value={schemeDescription}
                  onChange={(e) => setSchemeDescription(e.target.value)}
                  placeholder="A beautiful custom color scheme"
                  color={previewColors.text}
                  borderColor={previewColors.border}
                />
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onNameModalClose}>
              Cancel
            </Button>
            <Button
              bg={previewColors.primary}
              color="white"
              _hover={{ bg: previewColors.primaryDark }}
              leftIcon={<Icon as={FiSave} />}
              onClick={handleSaveScheme}
            >
              Save Theme
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  );
} 