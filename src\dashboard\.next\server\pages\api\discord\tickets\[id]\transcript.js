"use strict";(()=>{var e={};e.id=6236,e.ids=[6236],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,a)=>{e.exports=a(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},7479:(e,t,a)=>{a.r(t),a.d(t,{config:()=>v,default:()=>x,routeModule:()=>y});var r={};a.r(r),a.d(r,{default:()=>b});var i=a(3433),s=a(264),o=a(584),n=a(5806),d=a(8525),l=a(2518),c=a(8580);let{url:p,name:m}=c.dashboardConfig.database,u=null;async function g(){return u||(u=await l.MongoClient.connect(p)),u}async function b(e,t){let a,r;if("GET"!==e.method)return t.setHeader("Allow",["GET"]),t.status(405).json({error:"Method not allowed"});let i=await (0,n.getServerSession)(e,t,d.authOptions);if(!i)return t.status(401).json({error:"Unauthorized"});let{id:s}=e.query;if(!s||Array.isArray(s))return t.status(400).json({error:"Invalid ticket id"});let{token:o}=c.dashboardConfig.bot;try{a=await g()}catch{return t.status(500).json({error:"DB connect failed"})}let p=a.db(m).collection("tickets"),u=await p.findOne({_id:new l.ObjectId(s)});if(!u)return t.status(404).json({error:"Ticket not found"});let b=i.user.isAdmin,x=u.creatorId===i.user.id;if(!b&&!x)return t.status(403).json({error:"Forbidden"});if(u.transcriptHtml&&(u.transcriptHtml.includes("<style")||u.transcriptHtml.includes("<!DOCTYPE html>")))return t.setHeader("Content-Type","text/html"),t.status(200).send(u.transcriptHtml);let v=[];try{for(;;){let e=`https://discord.com/api/v10/channels/${u.channelId}/messages?limit=100${r?`&before=${r}`:""}`,t=await fetch(e,{headers:{Authorization:`Bot ${o}`}});if(!t.ok){if(404===t.status)throw Error("Channel not found - ticket channel may have been deleted");if(403===t.status)throw Error("Access denied - bot may not have permission to read channel history");throw Error(`Discord API error: ${t.status} ${t.statusText}`)}let a=await t.json();if(v=v.concat(a),a.length<100)break;r=a[a.length-1].id}}catch(e){return t.status(500).json({error:"Failed to fetch messages",details:e.message||"Unknown error occurred while fetching Discord messages",suggestion:"The ticket channel may have been deleted. Transcripts should be saved when tickets are closed."})}v.reverse();let y=[...new Set(v.map(e=>e.author.id))].map(e=>{let t=v.find(t=>t.author.id===e);return{id:e,username:t?.author.username||"Unknown User",avatar:t?.author.avatar?`https://cdn.discordapp.com/avatars/${e}/${t.author.avatar}.png?size=32`:`https://cdn.discordapp.com/embed/avatars/${parseInt(t.author.discriminator||"0")%5}.png`,role:e===u.creatorId?"Creator":"Staff"}}).map(e=>`
    <div class="participant">
      <img src="${e.avatar}" alt="${e.username}'s avatar"/>
      <div>
        <div class="participant-name">${e.username}</div>
        <div class="participant-role">${e.role}</div>
      </div>
    </div>
  `).join("");async function k(e){try{let t=await fetch(`https://discord.com/api/v10/users/${e}`,{headers:{Authorization:`Bot ${o}`}});if(!t.ok)return`<@${e}>`;let a=await t.json();return`@${a.username}`}catch{return`<@${e}>`}}async function w(e){if(!e.content_type?.startsWith("image/"))return`<a href="${e.url}" target="_blank">${e.filename||e.url}</a>`;if("image/gif"===e.content_type||e.size>1048576)return`<img class="attach" src="${e.url}" />`;try{let t=await (await fetch(e.url)).arrayBuffer(),a=Buffer.from(t).toString("base64");return`<img class="attach" src="data:${e.content_type};base64,${a}" />`}catch{return`<img class="attach" src="${e.url}" />`}}let $=v.map(async e=>{let t=new Date(e.timestamp).toLocaleString(),a=e.author.avatar?`https://cdn.discordapp.com/avatars/${e.author.id}/${e.author.avatar}.png?size=32`:`https://cdn.discordapp.com/embed/avatars/${parseInt(e.author.discriminator||"0")%5}.png`,r=(e.content||"").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\n/g,"<br/>"),i=r.match(/<@!?(\d+)>/g);if(i)for(let e of i){let t=e.match(/\d+/)?.[0];if(t){let a=await k(t);r=r.replace(e,a)}}if(e===v[0]&&e.embeds?.length>0){let t=e.embeds[0];r=`
<div class="ticket-embed">
  <div class="ticket-embed-header">
    <span class="ticket-embed-icon">📝</span>
    <div class="ticket-embed-title">${t.title||"Support Ticket"}</div>
  </div>
  ${t.fields?.map(e=>`
    <div class="ticket-embed-field">
      <div class="ticket-embed-label">${e.name}</div>
      <div class="ticket-embed-value">${e.value}</div>
    </div>
  `).join("")||""}
  ${t.description?`<div class="ticket-embed-description">${t.description}</div>`:""}
</div>`}r=await f(r,/<a?:([\w]+):(\d+)>/g,async(e,t,a)=>{let r=e.startsWith("<a:")?"gif":"png",i=`https://cdn.discordapp.com/emojis/${a}.${r}`;if("gif"===r)return`<img class="emoji" src="${i}" />`;let s=await h(i,`image/${r}`);return`<img class="emoji" src="${s}" />`}),r=await f(r,/(https?:\/\/tenor\.com\/view\/[^\s]+)/g,async e=>{let t=e.match(/-(\d+)(?:\?.*)?$/);if(!t)return"";let a=t[1];try{let e,t=await fetch(`https://g.tenor.com/v1/gifs?ids=${a}&key=LIVDSRZULELA`),r=await t.json();if(!r?.results?.length)throw 0;let i=r.results[0].media_formats||r.results[0].media?.[0];for(let t of["mediumgif","gif","tinygif","nanogif"])if(i?.[t]?.url){e=i[t].url;break}if(!e)throw 0;return e}catch{return""}}),r=await f(r,/(https?:\/\/\S+\.(?:png|jpe?g|gif|webp))/gi,async e=>e);let s="";return e.attachments?.length&&(s=(await Promise.all(e.attachments.map(w))).join("")),`<div class="msg">
      <img class="avatar" src="${a}"/>
      <div class="bubble">
        <div class="meta">
          <span class="name">${e.author.username}</span>
          <span class="time">${t}</span>
        </div>
        <div class="content">${r}${s}</div>
      </div>
    </div>`}),S=(await Promise.all($)).join("\n"),I=`<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${s} Transcript</title>
<style>
/* Reset and base styles */
*{box-sizing:border-box;margin:0;padding:0}
:root {
  --bg-primary: #0f172a;
  --bg-secondary: rgba(255,255,255,0.08);
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  --accent-blue: #3b82f6;
  --accent-purple: #a855f7;
  --accent-red: #ef4444;
}

/* Main layout */
body{
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  display: grid;
  grid-template-columns: 280px 1fr;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(29,78,216,.15), transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(60,46,123,.15), transparent 50%);
}

/* Sidebar */
.sidebar {
  background: var(--bg-secondary);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255,255,255,0.1);
  padding: 24px;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

.sidebar-section {
  margin-bottom: 32px;
}

.sidebar-section h3 {
  color: var(--text-secondary);
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}

/* Main content */
.main {
  padding: 40px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* Header */
.header {
  margin-bottom: 32px;
  text-align: center;
}

h1 {
  font-size: 32px;
  background: linear-gradient(135deg, #60a5fa 0%, #a78bfa 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 16px;
}

/* Support details */
.support-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.detail-item {
  text-align: center;
  padding: 12px;
  background: rgba(255,255,255,0.02);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: rgba(255,255,255,0.04);
  transform: translateY(-2px);
}

.detail-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-weight: 500;
}

.detail-value {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 600;
}

/* Messages */
.messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.msg {
  display: flex;
  gap: 16px;
  animation: fadeIn 0.3s ease;
  padding: 4px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.msg:hover {
  background: rgba(255,255,255,0.03);
}

.avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.msg:hover .avatar {
  transform: scale(1.05);
}

.bubble {
  background: rgba(255,255,255,0.04);
  padding: 16px;
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.06);
  max-width: calc(100% - 58px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.msg:hover .bubble {
  border-color: rgba(255,255,255,0.1);
  background: rgba(255,255,255,0.05);
}

.meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name {
  font-size: 15px;
  font-weight: 500;
  color: #60a5fa;
}

.time {
  font-size: 12px;
  color: var(--text-secondary);
}

.content {
  font-size: 15px;
  line-height: 1.5;
  color: var(--text-primary);
  word-break: break-word;
}

/* Attachments */
.attach {
  display: block;
  margin-top: 12px;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: transform 0.2s ease;
}

.attach:hover {
  transform: scale(1.02);
}

.emoji {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  margin: 0 2px;
  transition: transform 0.15s ease;
  cursor: pointer;
}

.emoji:hover {
  transform: scale(1.2);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Category badge */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 99px;
  font-size: 13px;
  font-weight: 500;
  background: var(--badge-bg);
  color: white;
  margin-bottom: 24px;
}

/* Ticket embed */
.ticket-embed {
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px;
  padding: 24px;
  margin-top: 12px;
  transition: all 0.2s ease;
}

.ticket-embed:hover {
  background: rgba(255,255,255,0.04);
  border-color: rgba(255,255,255,0.15);
  transform: translateY(-2px);
}

.ticket-embed-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.ticket-embed-icon {
  font-size: 24px;
}

.ticket-embed-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--accent-blue);
  background: linear-gradient(135deg, #60a5fa 0%, #818cf8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.ticket-embed-field {
  margin-bottom: 12px;
  padding-left: 28px;
}

.ticket-embed-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 4px;
  font-weight: 500;
}

.ticket-embed-value {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
}

.ticket-embed-description {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(255,255,255,0.1);
  color: var(--text-primary);
  font-size: 14px;
  line-height: 1.5;
  padding-left: 28px;
}

/* Participants */
.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}
</style></head><body>
  <div class="sidebar">
    <div class="sidebar-section">
      <h3>Participants</h3>
      ${y}
    </div>
    <div class="sidebar-section">
      <h3>Support Info</h3>
      <div class="detail-item">
        <div class="detail-label">Status</div>
        <div class="detail-value">Closed</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Category</div>
        <div class="detail-value">${u.category||"Support"}</div>
      </div>
    </div>
  </div>

  <div class="main">
    <div class="header">
      <h1>Ticket Transcript</h1>
      <span class="badge" style="--badge-bg:${{support:"#3b82f6","18plus":"#ef4444",other:"#a855f7"}[u.category||"other"]||"#6b7280"}">${u.category||"Support"}</span>
    </div>

    <div class="support-details">
      <div class="detail-item">
        <div class="detail-label">Ticket ID</div>
        <div class="detail-value">${s}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Created</div>
        <div class="detail-value">${new Date(u.createdAt).toLocaleString()}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Closed</div>
        <div class="detail-value">${u.closedAt?new Date(u.closedAt).toLocaleString():"N/A"}</div>
      </div>
    </div>

    <div class="messages">
      ${S}
    </div>
  </div>
</body></html>`;try{await p.updateOne({_id:new l.ObjectId(s)},{$set:{transcriptHtml:I}})}catch{}t.setHeader("Content-Type","text/html"),t.status(200).send(I)}function f(e,t,a){let r=[];return e.replace(t,(...e)=>{let t=a(...e);return r.push(t),e[0]}),Promise.all(r).then(a=>e.replace(t,()=>a.shift()))}async function h(e,t){try{let a=await fetch(e),r=await a.arrayBuffer();if(r.byteLength>1048576)return e;let i=Buffer.from(r).toString("base64");return`data:${t};base64,${i}`}catch{return e}}let x=(0,o.M)(r,"default"),v=(0,o.M)(r,"config"),y=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/tickets/[id]/transcript",pathname:"/api/discord/tickets/[id]/transcript",bundlePath:"",filename:""},userland:r})},8525:(e,t,a)=>{a.r(t),a.d(t,{authOptions:()=>d,default:()=>l});var r=a(5542),i=a.n(r);let s=require("next-auth/providers/discord");var o=a.n(s),n=a(8580);let d={providers:[o()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:a})=>(t&&a&&(e.accessToken=t.access_token||null,e.id=a.id||null),e),async session({session:e,token:t}){if(e?.user){let a=t.id||null,r=t.accessToken||null;e.user.id=a,e.user.accessToken=r;let i=!1;if(a)if((n.dashboardConfig.dashboard.admins||[]).includes(a))i=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${a}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let a=await t.json();i=e.some(e=>a.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let a=new URL(t),r=`${a.protocol}//localhost${a.port?`:${a.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(d)},8580:(e,t,a)=>{a.r(t),a.d(t,{dashboardConfig:()=>d,default:()=>l});var r=a(9021),i=a(2115),s=a.n(i),o=a(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>o.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=o.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");n=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var a=t(t.s=7479);module.exports=a})();