import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';
import YAML from 'yaml';

// Helper function to locate config.yml
function locateConfig(): string {
  const attempts = [
    '404-bot/config.yml',
    'config.yml',
    '../config.yml',
    '../../config.yml',
    '../../../config.yml',
    '../../../../config.yml',
  ].map(rel => path.resolve(process.cwd(), rel));
  
  let found = attempts.find(p => fs.existsSync(p));
  if (!found) {
    // Try example config
    const exampleAttempts = attempts.map(p => p.replace('config.yml', 'config.example.yml'));
    found = exampleAttempts.find(p => fs.existsSync(p));

    if (found) {
      // Copy example to real config so future look-ups succeed
      try {
        const dest = found.replace('config.example.yml', 'config.yml');
        fs.copyFileSync(found, dest);
        found = dest;
      } catch (copyErr) {
        // Just use the example path directly if copy fails
      }
    }
  }

  // As absolute last resort create a minimal config.yml so dashboard can work in dev
  if (!found) {
    const defaultPath = path.resolve(process.cwd(), 'config.yml');
    try {
      fs.writeFileSync(
        defaultPath,
        YAML.stringify({
          addons: { enabled: true, disabled: [] },
        })
      );
      found = defaultPath;
    } catch {}
  }

  if (!found) throw new Error('config.yml not found and could not create default');
  return found;
}

// Helper function to locate addon config
function locateAddonConfig(addonName: string): string | null {
  const attempts = [
    `404-bot/src/addons/${addonName}/config.yml`,
    `src/addons/${addonName}/config.yml`,
    `../src/addons/${addonName}/config.yml`,
    `../../src/addons/${addonName}/config.yml`,
    `../../../src/addons/${addonName}/config.yml`,
    `../../../../src/addons/${addonName}/config.yml`,
  ].map(rel => path.resolve(process.cwd(), rel));
  
  let found = attempts.find(p => fs.existsSync(p));
  if (!found) {
    const dirBased = path.resolve(__dirname, `../../../../../../../src/addons/${addonName}/config.yml`);
    if (fs.existsSync(dirBased)) found = dirBased;
  }
  return found || null;
}

// Helper to find project root (folder containing package.json)
function findProjectRoot(start = process.cwd()): string {
  let dir = start;
  while (dir !== path.parse(dir).root) {
    if (fs.existsSync(path.join(dir, 'package.json'))) return dir;
    dir = path.dirname(dir);
  }
  return start; // fallback
}

// Helper function to locate addons directories
function locateAddonsDirs(): string[] {
  const attempts = [
    // Source directories (TypeScript)
    '404-bot/src/addons',
    'src/addons',
    '../src/addons',
    '../../src/addons',
    '../../../src/addons',
    '../../../../src/addons',

    // Compiled directories (JavaScript)
    '404-bot/dist/addons',
    'dist/addons',
    'dist/dashboard/dist/addons',
    'dashboard/dist/addons',
    '../dist/addons',
    '../dist/dashboard/dist/addons',
    '../../dist/addons',
    '../../dist/dashboard/dist/addons',
    '../../../dist/addons',
    '../../../../dist/addons',
  ].map(rel => path.resolve(process.cwd(), rel));
  
  const foundDirs: string[] = [];
  attempts.forEach(attempt => {
    if (fs.existsSync(attempt)) {
      foundDirs.push(attempt);
    }
  });
  
  // Also check final fallbacks
  const dirBasedSrc = path.resolve(__dirname, '../../../../../../../src/addons');
  const dirBasedDist = path.resolve(__dirname, '../../../../../../../dist/addons');
  
  if (fs.existsSync(dirBasedSrc)) foundDirs.push(dirBasedSrc);
  if (fs.existsSync(dirBasedDist)) foundDirs.push(dirBasedDist);
  
  if (foundDirs.length === 0) throw new Error('No addons directories found');
  
  // Remove duplicates
  const uniqueDirs = Array.from(new Set(foundDirs));
  return uniqueDirs;
}

// Helper function to find addon path
function findAddonPath(name: string): string | null {
  const addonsDirs = locateAddonsDirs();
  
  const slug = name.replace(/\s+/g, '-');
  for (const dir of addonsDirs) {
    const candidates = [path.join(dir, name), path.join(dir, slug)];
    for (const addonPath of candidates) {
      if (fs.existsSync(addonPath)) {
        return addonPath;
      }
    }
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check admin permission
  const isAdmin = (session.user as any).isAdmin;
  if (!isAdmin) {
    return res.status(403).json({ error: 'Forbidden - Admin access required' });
  }

  const { name } = req.query;
  if (!name || typeof name !== 'string') {
    return res.status(400).json({ error: 'Invalid addon name' });
  }

  try {
    const configPath = locateConfig();
    const configFile = fs.readFileSync(configPath, 'utf8');
    const config = YAML.parse(configFile);

    // Get addon-specific config path
    const addonConfigPath = locateAddonConfig(name);
    
    switch (req.method) {
      case 'GET': {
        // Get addon status and configuration
        const isEnabled = config.addons?.enabled && 
          (!config.addons.disabled || !config.addons.disabled.includes(name));

        let addonConfig = '';
        if (addonConfigPath) {
          addonConfig = fs.readFileSync(addonConfigPath, 'utf8');
        } else {
          // Create default config if it doesn't exist
          addonConfig = YAML.stringify({
            addon: {
              name: name,
              version: "1.0.0",
              description: "No description available",
              author: "Unknown",
              enabled: true
            }
          });
        }

        return res.status(200).json({
          name,
          enabled: isEnabled,
          configYaml: addonConfig
        });
      }

      case 'PATCH': {
        // Toggle addon enabled/disabled state
        const { enabled } = req.body;
        
        if (typeof enabled !== 'boolean') {
          return res.status(400).json({ error: 'Invalid request body' });
        }

        // Initialize addons section if it doesn't exist
        if (!config.addons) config.addons = {};
        if (!config.addons.disabled) config.addons.disabled = [];

        // Update disabled list
        if (enabled) {
          config.addons.disabled = config.addons.disabled.filter((n: string) => n !== name);
        } else {
          if (!config.addons.disabled.includes(name)) {
            config.addons.disabled.push(name);
          }
        }

        // Save main config
        fs.writeFileSync(configPath, YAML.stringify(config));

        // Emit reload signal so bot hot-reloads addons automatically
        try {
          const projectRoot = findProjectRoot();
          const signalPath = path.join(projectRoot, 'addon-reload.signal');
          fs.writeFileSync(signalPath, JSON.stringify({ timestamp: Date.now(), requestedBy: session.user?.email || 'dashboard' }));
        } catch {}

        return res.status(200).json({
          name,
          enabled,
          configYaml: addonConfigPath ? fs.readFileSync(addonConfigPath, 'utf8') : ''
        });
      }

      case 'PUT': {
        // Update addon configuration
        const { configYaml } = req.body;

        if (!configYaml || typeof configYaml !== 'string') {
          return res.status(400).json({ error: 'Invalid configuration' });
        }

        try {
          // Validate YAML syntax
          YAML.parse(configYaml);

          // Create addons directory if it doesn't exist
          const addonDir = path.dirname(addonConfigPath || path.resolve(process.cwd(), `src/addons/${name}`));
          if (!fs.existsSync(addonDir)) {
            fs.mkdirSync(addonDir, { recursive: true });
          }

          // Save addon config
          fs.writeFileSync(addonConfigPath || path.resolve(addonDir, 'config.yml'), configYaml);

          return res.status(200).json({
            name,
            enabled: !config.addons.disabled?.includes(name),
            configYaml
          });
        } catch (e) {
          return res.status(400).json({ error: 'Invalid YAML syntax' });
        }
      }

      case 'DELETE': {
        // Delete custom addon (only addons created by addon builder)
        
        // Find the addon path with enhanced detection
        let addonPath = findAddonPath(name);
        
        // If not found, try direct path construction as fallback
        if (!addonPath) {
          const projectRoot = process.cwd().includes('dashboard') 
            ? path.resolve(process.cwd(), '..', '..')
            : process.cwd();
          
          const possiblePaths = [
            path.join(projectRoot, 'src', 'addons', name),
            path.join(process.cwd(), '..', '..', 'src', 'addons', name),
            path.join(process.cwd(), 'src', 'addons', name)
          ];
          
          for (const possiblePath of possiblePaths) {
            if (fs.existsSync(possiblePath)) {
              addonPath = possiblePath;
              break;
            }
          }
        }
        
        // Check if addon directory exists
        if (!addonPath) {
          return res.status(404).json({ 
            error: 'Addon not found', 
            details: `Could not locate addon "${name}" in any expected directories`
          });
        }
        
        console.log(`Found addon to delete at: ${addonPath}`);

        // Check if this is a custom addon by looking for flow.json (most reliable indicator)
        const flowJsonPath = path.join(addonPath, 'flow.json');
        let isCustomAddon = false;
        
        // First check: presence of flow.json file indicates it was created with visual builder
        if (fs.existsSync(flowJsonPath)) {
          isCustomAddon = true;
          console.log(`Addon ${name} identified as custom addon due to flow.json file`);
        }

        // Fallback check: look for specific markers in index.ts and config.yml
        if (!isCustomAddon) {
          const indexPath = path.join(addonPath, 'index.ts');
          if (fs.existsSync(indexPath)) {
            const indexContent = fs.readFileSync(indexPath, 'utf8');
            isCustomAddon = indexContent.includes('Generated addon from visual builder') && 
                            indexContent.includes('author: \'Addon Builder\'');
          }

          // Also check config.yml for custom addon markers
          if (!isCustomAddon && fs.existsSync(path.join(addonPath, 'config.yml'))) {
            const configContent = fs.readFileSync(path.join(addonPath, 'config.yml'), 'utf8');
            const addonConfig = YAML.parse(configContent);
            isCustomAddon = addonConfig?.author === 'Addon Builder' || 
                            addonConfig?.description === 'Generated addon from visual builder';
          }
        }

        if (!isCustomAddon) {
          return res.status(403).json({ 
            error: 'Cannot delete built-in addon', 
            details: 'Only custom addons created by the addon builder can be deleted' 
          });
        }

        // Remove from disabled list if present
        if (config.addons?.disabled?.includes(name)) {
          config.addons.disabled = config.addons.disabled.filter((n: string) => n !== name);
          fs.writeFileSync(configPath, YAML.stringify(config));
        }

        // Remove addon directory recursively with better error handling
        const rimraf = (dir: string) => {
          if (fs.existsSync(dir)) {
            try {
              fs.readdirSync(dir).forEach((file) => {
                const curPath = path.join(dir, file);
                if (fs.lstatSync(curPath).isDirectory()) {
                  rimraf(curPath);
                } else {
                  fs.unlinkSync(curPath);
                }
              });
              fs.rmdirSync(dir);
              console.log(`Successfully deleted directory: ${dir}`);
            } catch (error) {
              console.error(`Error deleting directory ${dir}:`, error);
              throw new Error(`Failed to delete addon directory: ${error}`);
            }
          } else {
            console.log(`Directory does not exist: ${dir}`);
          }
        };

        console.log(`Attempting to delete addon directory: ${addonPath}`);
        rimraf(addonPath);
        
        // Verify the addon was actually deleted
        if (fs.existsSync(addonPath)) {
          throw new Error(`Addon directory still exists after deletion attempt: ${addonPath}`);
        }
        
        console.log(`Successfully deleted source addon: ${name}`);
        
        // Also delete from dist/addons if it exists
        const projectRoot = process.cwd().includes('dashboard') 
          ? path.resolve(process.cwd(), '..', '..')
          : process.cwd();
        
        const distPaths = [
          path.join(projectRoot, 'dist', 'addons', name),
          path.join(process.cwd(), '..', '..', 'dist', 'addons', name),
          path.join(process.cwd(), 'dist', 'addons', name)
        ];
        
        for (const distPath of distPaths) {
          if (fs.existsSync(distPath)) {
            console.log(`Found compiled addon at: ${distPath}`);
            try {
              rimraf(distPath);
              console.log(`Successfully deleted compiled addon: ${distPath}`);
            } catch (error) {
              console.warn(`Could not delete compiled addon ${distPath}:`, error);
              // Don't throw here - source deletion is more important
            }
          }
        }
        
        console.log(`Successfully deleted custom addon: ${name}`);

        // Emit reload signal so bot hot-reloads addons automatically
        try {
          const projectRoot = findProjectRoot();
          const signalPath = path.join(projectRoot, 'addon-reload.signal');
          fs.writeFileSync(signalPath, JSON.stringify({ 
            timestamp: Date.now(), 
            requestedBy: session.user?.email || 'dashboard',
            action: 'addon-deleted',
            addonName: name
          }));
        } catch {}

        return res.status(200).json({
          message: 'Custom addon deleted successfully',
          name
        });
      }

      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error: any) {
    console.error('Error managing addon:', error);
    return res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 