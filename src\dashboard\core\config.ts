// @ts-nocheck
import * as fs from 'fs';
import YAML from 'yaml';
import * as path from 'path';

let config: any = {};

try {
  // Locate config.yml by walking up directories (dashboard may run from nested cwd)
  const possible = [
    'config.yml',
    '../config.yml',
    '../../config.yml',
    '../../../config.yml',
    '../../../../config.yml',
  ].map(rel => path.resolve(process.cwd(), rel));

  let configPath = possible.find(p => fs.existsSync(p));

  if (!configPath) {
    // fallback relative to file location
    const dirBased = path.resolve(__dirname, '../../../config.yml');
    if (fs.existsSync(dirBased)) configPath = dirBased;
  }

  if (!configPath) {
    throw new Error('config.yml not found');
  }

  const fileContents = fs.readFileSync(configPath, 'utf8');
  config = YAML.parse(fileContents);
} catch (error) {
  console.error('Error: Failed to load config.yml:', error);
  process.exit(1); // Exit if we can't load the config
}

// Export the dashboard-specific config
export const dashboardConfig = {
  bot: {
    token: config.bot.token,
    clientId: config.bot.clientId,
    clientSecret: config.bot.clientSecret,
    guildId: config.bot.guildId,
    ticketCategoryId: config.bot.ticketCategoryId || null,
    ticketLogChannelId: config.bot.ticketLogChannelId || null,
    prefix: config.bot.prefix,
  },
  dashboard: {
    admins: config.dashboard?.admins || [],
    adminRoleIds: config.dashboard?.adminRoleIds || [],
    session: {
      secret: config.dashboard?.session?.secret || config.bot.clientSecret, // Fallback to clientSecret if no session secret
    },
  },
  database: {
    url: config.database.url,
    name: config.database.name,
    options: {
      maxPoolSize: config.database.options?.maxPoolSize || 10,
      minPoolSize: config.database.options?.minPoolSize || 1,
      maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,
      serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,
      socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,
      connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,
      retryWrites: config.database.options?.retryWrites !== false,
      retryReads: config.database.options?.retryReads !== false,
    },
  },
};

// Validate required configuration
if (!dashboardConfig.bot.token) {
  console.error('Error: Discord bot token is required in config.yml');
  process.exit(1);
}

if (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {
  console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');
  process.exit(1);
}

if (!dashboardConfig.bot.guildId) {
  console.error('Error: Guild ID is required in config.yml');
  process.exit(1);
}

if (!dashboardConfig.database.url || !dashboardConfig.database.name) {
  console.error('Error: Database configuration (url and name) is required in config.yml');
  process.exit(1);
}

export default dashboardConfig; 