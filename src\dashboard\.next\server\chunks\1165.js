"use strict";exports.id=1165,exports.ids=[1165],exports.modules={1165:(e,s,o)=>{o.a(e,async(e,i)=>{try{o.r(s),o.d(s,{default:()=>c});var n=o(8732),r=o(9733),l=o(2015),t=o(8079),a=e([r]);r=(a.then?(await a)():a)[0];let d={General:{icon:t.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:t.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","MANAGE_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:t.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:t.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function c({isOpen:e,onClose:s,onSuccess:o,role:i}){let t=(0,r.useToast)(),[a,c]=(0,l.useState)(!1),[E,h]=(0,l.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),S=async()=>{c(!0);try{let e=await fetch(`/api/discord/roles/${i.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(E)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update role")}t({title:"Success",description:"Role updated successfully",status:"success",duration:3e3}),o(),s()}catch(e){t({title:"Error",description:e.message||"Failed to update role",status:"error",duration:5e3})}finally{c(!1)}},A=e=>{h(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},m=(e,s)=>{h(o=>({...o,[e]:s}))};return(0,n.jsxs)(r.Modal,{isOpen:e,onClose:s,size:"xl",scrollBehavior:"inside",children:[(0,n.jsx)(r.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,n.jsxs)(r.ModalContent,{bg:"gray.800",children:[(0,n.jsx)(r.ModalHeader,{children:"Edit Role"}),(0,n.jsx)(r.ModalCloseButton,{}),(0,n.jsx)(r.ModalBody,{children:(0,n.jsxs)(r.VStack,{spacing:6,children:[(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Role Name"}),(0,n.jsx)(r.Input,{placeholder:"Enter role name",value:E.name,onChange:e=>m("name",e.target.value)})]}),(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Role Color"}),(0,n.jsx)(r.Input,{type:"color",value:E.color,onChange:e=>m("color",e.target.value)})]}),(0,n.jsx)(r.FormControl,{children:(0,n.jsxs)(r.HStack,{spacing:4,children:[(0,n.jsx)(r.Checkbox,{isChecked:E.hoist,onChange:e=>m("hoist",e.target.checked),children:"Display role separately"}),(0,n.jsx)(r.Checkbox,{isChecked:E.mentionable,onChange:e=>m("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,n.jsx)(r.Divider,{}),(0,n.jsx)(r.Text,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(d).map(([e,s])=>(0,n.jsxs)(r.Box,{w:"full",children:[(0,n.jsxs)(r.HStack,{mb:2,children:[(0,n.jsx)(r.Icon,{as:s.icon}),(0,n.jsx)(r.Text,{fontWeight:"semibold",children:e})]}),(0,n.jsx)(r.SimpleGrid,{columns:2,spacing:2,children:s.permissions.map(e=>(0,n.jsx)(r.Checkbox,{isChecked:E.permissions.includes(e),onChange:()=>A(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},e))]})}),(0,n.jsxs)(r.ModalFooter,{children:[(0,n.jsx)(r.Button,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,n.jsx)(r.Button,{colorScheme:"blue",onClick:S,isLoading:a,children:"Save Changes"})]})]})]})}i()}catch(e){i(e)}})}};