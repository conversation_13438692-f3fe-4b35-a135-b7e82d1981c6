// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';

const BATCH_SIZE = 5; // Process 5 roles at a time
const MAX_RETRIES = 2; // Number of retries per role
const RETRY_DELAY = 1000; // 1 second delay between retries

async function deleteRole(token: string, guildId: string, roleId: string, retryCount = 0): Promise<boolean> {
  try {
    const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/roles/${roleId}`, {
      method: 'DELETE',
      headers: {
        Authorization: `Bot ${token}`,
      },
    });

    if (response.ok) {
      return true;
    }

    // If we get a rate limit response, wait and retry
    if (response.status === 429) {
      const data = await response.json();
      const retryAfter = (data.retry_after || 1) * 1000;
      await new Promise(resolve => setTimeout(resolve, retryAfter));
      if (retryCount < MAX_RETRIES) {
        return deleteRole(token, guildId, roleId, retryCount + 1);
      }
    }

    console.error(`Failed to delete role ${roleId}:`, await response.text());
    return false;
  } catch (error) {
    console.error(`Error deleting role ${roleId}:`, error);
    if (retryCount < MAX_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, RETRY_DELAY));
      return deleteRole(token, guildId, roleId, retryCount + 1);
    }
    return false;
  }
}

async function processBatch(token: string, guildId: string, roleIds: string[]): Promise<{ succeeded: string[], failed: string[] }> {
  const results = await Promise.all(
    roleIds.map(async roleId => {
      const success = await deleteRole(token, guildId, roleId);
      return { roleId, success };
    })
  );

  return results.reduce(
    (acc, { roleId, success }) => {
      if (success) {
        acc.succeeded.push(roleId);
      } else {
        acc.failed.push(roleId);
      }
      return acc;
    },
    { succeeded: [], failed: [] }
  );
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // For sensitive operations like deleting roles we require admin.
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    if (req.method === 'POST') {
      try {
        const { roleIds } = req.body;

        if (!Array.isArray(roleIds) || roleIds.length === 0) {
          return res.status(400).json({ error: 'Role IDs array is required' });
        }

        console.log(`Starting bulk delete of ${roleIds.length} roles`);

        const results = {
          succeeded: [] as string[],
          failed: [] as string[],
        };

        // Process roles in batches
        for (let i = 0; i < roleIds.length; i += BATCH_SIZE) {
          const batch = roleIds.slice(i, i + BATCH_SIZE);
          console.log(`Processing batch ${i / BATCH_SIZE + 1}:`, batch);

          const batchResults = await processBatch(token, guildId, batch);
          results.succeeded.push(...batchResults.succeeded);
          results.failed.push(...batchResults.failed);

          // Add a small delay between batches to avoid rate limits
          if (i + BATCH_SIZE < roleIds.length) {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }

        console.log('Bulk delete completed:', results);

        return res.status(200).json({
          message: `Successfully deleted ${results.succeeded.length} roles${
            results.failed.length > 0 ? `, failed to delete ${results.failed.length} roles` : ''
          }`,
          succeeded: results.succeeded,
          failed: results.failed,
        });
      } catch (error) {
        console.error('Error in bulk role deletion:', error);
        return res.status(500).json({ error: 'Failed to delete roles' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in bulk role deletion handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 