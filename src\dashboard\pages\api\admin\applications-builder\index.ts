import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { getDb } from '../../../../apiHelpers/db';

interface ApplicationType {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  enabled: boolean;
  questions: Question[];
  settings: ApplicationSettings;
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

interface Question {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'number' | 'email';
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[];
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

interface ApplicationSettings {
  allowMultipleSubmissions: boolean;
  requireApproval: boolean;
  autoResponse: boolean;
  notificationChannels: string[];
  openingSchedule?: {
    enabled: boolean;
    startDate: string;
    endDate: string;
  };
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check if user is admin
  const isAdmin = (session.user as any)?.isAdmin;
  if (!isAdmin) {
    return res.status(403).json({ error: 'Admin access required' });
  }

  const db = await getDb();
  const collection = db.collection('custom_applications');

  if (req.method === 'GET') {
    try {
      const applications = await collection.find({}).toArray();
      // Remove MongoDB _id field from response to avoid update conflicts
      const cleanApplications = applications.map(({ _id, ...app }) => app);
      return res.status(200).json({ applications: cleanApplications });
    } catch (error) {
      console.error('Error fetching applications:', error);
      return res.status(500).json({ error: 'Failed to fetch applications' });
    }
  }

  if (req.method === 'POST') {
    try {
      const applicationData = req.body as ApplicationType;
      
      // Validate required fields
      if (!applicationData.title || !applicationData.description) {
        return res.status(400).json({ error: 'Title and description are required' });
      }

      if (!applicationData.id) {
        return res.status(400).json({ error: 'Application ID is required' });
      }

      // Check if application ID already exists
      const existingApp = await collection.findOne({ id: applicationData.id });
      
      if (existingApp) {
        // Update existing application - exclude _id field
        const updatedApp = {
          id: applicationData.id,
          title: applicationData.title,
          description: applicationData.description,
          color: applicationData.color,
          icon: applicationData.icon,
          enabled: applicationData.enabled,
          questions: applicationData.questions,
          settings: applicationData.settings,
          updatedAt: new Date(),
        };
        
        const result = await collection.updateOne(
          { id: applicationData.id },
          { $set: updatedApp }
        );
        
        return res.status(200).json({ application: updatedApp });
      } else {
        // Create new application
        const newApp = {
          ...applicationData,
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: session.user.id,
        };
        
        const result = await collection.insertOne(newApp);
        return res.status(201).json({ application: newApp });
      }
    } catch (error) {
      console.error('Error saving application:', error);
      return res.status(500).json({ error: 'Failed to save application' });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
} 