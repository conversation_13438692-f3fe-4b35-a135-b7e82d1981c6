"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7090],{28245:(e,r,o)=>{o.d(r,{j:()=>a});var s=o(94513),t=o(55100),n=o(22697),i=o(9557),l=o(2923),c=o(33225);let a=(0,l.R)((e,r)=>{let{className:o,...l}=e,a=(0,n.cx)("chakra-modal__footer",o),d=(0,i.x5)(),x=(0,t.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...d.footer});return(0,s.jsx)(c.B.footer,{ref:r,...l,__css:x,className:a})});a.displayName="ModalFooter"},59365:(e,r,o)=>{o.d(r,{s:()=>c});var s=o(94513),t=o(22697),n=o(50614),i=o(9557),l=o(33021);let c=(0,o(2923).R)((e,r)=>{let{onClick:o,className:c,...a}=e,{onClose:d}=(0,i.k3)(),x=(0,t.cx)("chakra-modal__close-btn",c),h=(0,i.x5)();return(0,s.jsx)(l.J,{ref:r,__css:h.closeButton,className:x,onClick:(0,n.H)(o,e=>{e.stopPropagation(),d()}),...a})});c.displayName="ModalCloseButton"},67090:(e,r,o)=>{o.r(r),o.d(r,{default:()=>F});var s=o(94513),t=o(94285),n=o(22907),i=o(95845),l=o(68443),c=o(59818),a=o(79156),d=o(41611),x=o(9557),h=o(7680),p=o(52922),b=o(47847),g=o(78902),m=o(49217),u=o(59365),j=o(85104),f=o(26977),C=o(49451),y=o(55920),v=o(52216),S=o(25680),z=o(51961),k=o(64057),w=o(71601),E=o(62690),T=o(71185),W=o(28245),I=o(40443),_=o(63730),B=o(97146),D=o(17842),P=o(12772);let $={primary:{label:"Primary Color",description:"Main accent color for buttons, active states, and highlights",example:"Active sidebar items, primary buttons"},primaryLight:{label:"Primary Light",description:"Lighter version of primary color for hover states",example:"Button hover effects, gradients"},primaryDark:{label:"Primary Dark",description:"Darker version of primary color for pressed states",example:"Button active states, deeper accents"},secondary:{label:"Secondary Color",description:"Supporting accent color for variety",example:"Secondary buttons, alternative highlights"},accent:{label:"Accent Color",description:"Decorative color for gradients and special elements",example:"Gradient endpoints, special indicators"},background:{label:"Background",description:"Main dashboard background color",example:"Page background, main layout"},surface:{label:"Surface",description:"Color for cards, sidebar, and elevated elements",example:"Cards, sidebar, modals"},text:{label:"Primary Text",description:"Main text color for headings and important content",example:"Headings, active text, important labels"},textSecondary:{label:"Secondary Text",description:"Subdued text color for descriptions and less important content",example:"Descriptions, inactive states, subtitles"},border:{label:"Border Color",description:"Color for borders, dividers, and outlines",example:"Card borders, input outlines, dividers"},success:{label:"Success Color",description:"Color for success states and positive actions",example:"Success messages, completed states"},warning:{label:"Warning Color",description:"Color for warning states and caution",example:"Warning messages, pending states"},error:{label:"Error Color",description:"Color for error states and destructive actions",example:"Error messages, delete buttons"},info:{label:"Info Color",description:"Color for informational content",example:"Info messages, helpful hints"}};function F(e){let{isOpen:r,onClose:o}=e,{currentScheme:F,addCustomScheme:M,customSchemes:A,deleteCustomScheme:N}=(0,P.DP)(),L=(0,n.d)(),{isOpen:O,onOpen:H,onClose:R}=(0,i.j)(),[G,J]=(0,t.useState)(F.colors),[X,Y]=(0,t.useState)(""),[Z,U]=(0,t.useState)(""),q=(e,r)=>{J(o=>({...o,[e]:r}))},Q=(e,r)=>{N(e),L({title:"Theme Deleted",description:'Custom theme "'.concat(r,'" has been deleted'),status:"info",duration:3e3,isClosable:!0})},V=e=>{let{title:r,children:o}=e;return(0,s.jsx)(l.Z,{bg:G.surface,borderColor:G.border,borderWidth:"1px",children:(0,s.jsx)(c.b,{p:4,children:(0,s.jsxs)(a.T,{align:"stretch",spacing:3,children:[(0,s.jsx)(d.E,{fontWeight:"bold",color:G.text,fontSize:"sm",children:r}),o]})})})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(x.aF,{isOpen:r,onClose:o,size:"6xl",children:[(0,s.jsx)(h.m,{bg:"blackAlpha.700",backdropFilter:"blur(10px)"}),(0,s.jsxs)(p.$,{bg:G.background,borderColor:G.border,borderWidth:"1px",boxShadow:"0 20px 25px -5px ".concat(G.background,"40, 0 10px 10px -5px ").concat(G.background,"40"),maxH:"90vh",overflow:"hidden",children:[(0,s.jsx)(b.r,{color:G.text,borderBottom:"1px solid",borderColor:G.border,children:(0,s.jsxs)(g.z,{children:[(0,s.jsx)(m.I,{as:B.Pj4}),(0,s.jsx)(d.E,{children:"Custom Color Builder"})]})}),(0,s.jsx)(u.s,{color:G.textSecondary}),(0,s.jsx)(j.c,{overflow:"auto",p:6,children:(0,s.jsxs)(a.T,{spacing:6,align:"stretch",children:[(0,s.jsxs)(f.F,{status:"info",variant:"subtle",rounded:"md",bg:"".concat(G.info,"20"),borderColor:G.info,children:[(0,s.jsx)(C._,{color:G.info}),(0,s.jsxs)(a.T,{align:"start",spacing:1,children:[(0,s.jsx)(y.X,{color:G.text,children:"Color Builder"}),(0,s.jsx)(v.T,{color:G.textSecondary,children:'Adjust colors and see live preview. Click "Save Theme" when you\'re happy with your design.'})]})]}),(0,s.jsxs)(S.r,{columns:{base:1,xl:2},spacing:8,children:[(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(d.E,{fontSize:"lg",fontWeight:"bold",color:G.text,children:"Color Settings"}),(0,s.jsx)(a.T,{spacing:4,align:"stretch",maxH:"500px",overflow:"auto",pr:2,children:Object.entries($).map(e=>{let[r,o]=e;return(0,s.jsx)(l.Z,{bg:G.surface,borderColor:G.border,borderWidth:"1px",children:(0,s.jsx)(c.b,{p:4,children:(0,s.jsxs)(a.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(a.T,{align:"start",spacing:1,children:[(0,s.jsxs)(g.z,{children:[(0,s.jsx)(z.a,{w:4,h:4,bg:G[r],rounded:"md",border:"1px solid",borderColor:G.border}),(0,s.jsx)(d.E,{fontWeight:"bold",color:G.text,fontSize:"sm",children:o.label})]}),(0,s.jsx)(d.E,{fontSize:"xs",color:G.textSecondary,children:o.description}),(0,s.jsxs)(d.E,{fontSize:"xs",color:G.textSecondary,fontStyle:"italic",children:["Used for: ",o.example]})]}),(0,s.jsxs)(g.z,{children:[(0,s.jsx)(k.p,{type:"color",value:G[r],onChange:e=>q(r,e.target.value),w:12,h:8,p:0,border:"none",rounded:"md"}),(0,s.jsx)(k.p,{value:G[r],onChange:e=>q(r,e.target.value),placeholder:"#000000",color:G.text,borderColor:G.border,fontSize:"sm",fontFamily:"mono"})]})]})})},r)})})]}),(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(d.E,{fontSize:"lg",fontWeight:"bold",color:G.text,children:"Live Preview"}),(0,s.jsx)(z.a,{bg:G.background,p:4,rounded:"lg",border:"2px solid",borderColor:G.border,minH:"500px",children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(z.a,{bg:G.surface,p:3,rounded:"md",borderColor:G.border,borderWidth:"1px",children:(0,s.jsxs)(g.z,{justify:"space-between",children:[(0,s.jsx)(d.E,{fontSize:"md",fontWeight:"bold",color:G.text,children:"Dashboard Preview"}),(0,s.jsx)(w.E,{bg:G.primary,color:"white",px:2,py:1,rounded:"md",children:"Live"})]})}),(0,s.jsxs)(S.r,{columns:2,spacing:3,children:[(0,s.jsx)(V,{title:"Statistics",children:(0,s.jsxs)(a.T,{align:"start",spacing:2,children:[(0,s.jsx)(d.E,{fontSize:"xl",fontWeight:"bold",color:G.text,children:"1,234"}),(0,s.jsx)(d.E,{fontSize:"xs",color:G.textSecondary,children:"Total Users"}),(0,s.jsx)(d.E,{fontSize:"xs",color:G.success,children:"+12% this month"})]})}),(0,s.jsx)(V,{title:"Activity",children:(0,s.jsxs)(a.T,{align:"start",spacing:2,children:[(0,s.jsx)(d.E,{fontSize:"xl",fontWeight:"bold",color:G.text,children:"89"}),(0,s.jsx)(d.E,{fontSize:"xs",color:G.textSecondary,children:"Active Sessions"}),(0,s.jsx)(d.E,{fontSize:"xs",color:G.warning,children:"Monitoring"})]})})]}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(d.E,{fontSize:"sm",fontWeight:"bold",color:G.text,children:"Button Examples"}),(0,s.jsxs)(g.z,{spacing:2,children:[(0,s.jsx)(E.$,{bg:G.primary,color:"white",size:"sm",leftIcon:(0,s.jsx)(m.I,{as:D.rQ8}),children:"Primary"}),(0,s.jsx)(E.$,{variant:"outline",borderColor:G.border,color:G.text,size:"sm",leftIcon:(0,s.jsx)(m.I,{as:D.YXz}),children:"Secondary"}),(0,s.jsx)(E.$,{variant:"ghost",color:G.textSecondary,size:"sm",leftIcon:(0,s.jsx)(m.I,{as:B.Vap}),children:"Ghost"})]})]}),(0,s.jsxs)(a.T,{spacing:2,align:"stretch",children:[(0,s.jsx)(d.E,{fontSize:"sm",fontWeight:"bold",color:G.text,children:"Status Examples"}),(0,s.jsxs)(g.z,{spacing:2,fontSize:"xs",children:[(0,s.jsx)(w.E,{bg:G.success,color:"white",px:2,py:1,rounded:"md",children:"Success"}),(0,s.jsx)(w.E,{bg:G.warning,color:"white",px:2,py:1,rounded:"md",children:"Warning"}),(0,s.jsx)(w.E,{bg:G.error,color:"white",px:2,py:1,rounded:"md",children:"Error"}),(0,s.jsx)(w.E,{bg:G.info,color:"white",px:2,py:1,rounded:"md",children:"Info"})]})]})]})})]})]}),A.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(T.c,{borderColor:G.border}),(0,s.jsxs)(a.T,{spacing:3,align:"stretch",children:[(0,s.jsx)(d.E,{fontSize:"lg",fontWeight:"bold",color:G.text,children:"Your Custom Themes"}),(0,s.jsx)(S.r,{columns:{base:1,md:2,lg:3},spacing:3,children:A.map(e=>(0,s.jsx)(l.Z,{bg:G.surface,borderColor:G.border,borderWidth:"1px",children:(0,s.jsx)(c.b,{p:3,children:(0,s.jsxs)(a.T,{align:"stretch",spacing:2,children:[(0,s.jsxs)(g.z,{justify:"space-between",children:[(0,s.jsx)(d.E,{fontWeight:"bold",color:G.text,fontSize:"sm",noOfLines:1,children:e.name}),(0,s.jsx)(E.$,{size:"xs",variant:"ghost",color:G.error,onClick:()=>Q(e.id,e.name),children:(0,s.jsx)(m.I,{as:B.IXo})})]}),(0,s.jsx)(d.E,{fontSize:"xs",color:G.textSecondary,noOfLines:2,children:e.description}),(0,s.jsxs)(g.z,{spacing:1,children:[(0,s.jsx)(z.a,{w:3,h:3,bg:e.colors.primary,rounded:"full"}),(0,s.jsx)(z.a,{w:3,h:3,bg:e.colors.secondary,rounded:"full"}),(0,s.jsx)(z.a,{w:3,h:3,bg:e.colors.accent,rounded:"full"}),(0,s.jsx)(z.a,{w:3,h:3,bg:e.colors.success,rounded:"full"})]})]})})},e.id))})]})]})]})}),(0,s.jsx)(W.j,{borderTop:"1px solid",borderColor:G.border,children:(0,s.jsxs)(g.z,{spacing:3,children:[(0,s.jsx)(E.$,{variant:"ghost",onClick:()=>{J(F.colors),L({title:"Colors Reset",description:"Colors have been reset to the current theme",status:"info",duration:2e3,isClosable:!0})},color:G.textSecondary,children:"Reset Colors"}),(0,s.jsx)(E.$,{variant:"ghost",onClick:o,color:G.textSecondary,children:"Cancel"}),(0,s.jsx)(E.$,{bg:G.primary,color:"white",_hover:{bg:G.primaryDark},leftIcon:(0,s.jsx)(m.I,{as:B.Bc_}),onClick:H,children:"Save Theme"})]})})]})]}),(0,s.jsxs)(x.aF,{isOpen:O,onClose:R,children:[(0,s.jsx)(h.m,{bg:"blackAlpha.600",backdropFilter:"blur(10px)"}),(0,s.jsxs)(p.$,{bg:G.background,borderColor:G.border,children:[(0,s.jsx)(b.r,{color:G.text,children:(0,s.jsxs)(g.z,{children:[(0,s.jsx)(m.I,{as:B.GGD}),(0,s.jsx)(d.E,{children:"Save Custom Theme"})]})}),(0,s.jsx)(u.s,{color:G.textSecondary}),(0,s.jsx)(j.c,{children:(0,s.jsxs)(a.T,{spacing:4,align:"stretch",children:[(0,s.jsxs)(I.MJ,{children:[(0,s.jsx)(_.l,{color:G.text,children:"Theme Name"}),(0,s.jsx)(k.p,{value:X,onChange:e=>Y(e.target.value),placeholder:"My Custom Theme",color:G.text,borderColor:G.border})]}),(0,s.jsxs)(I.MJ,{children:[(0,s.jsx)(_.l,{color:G.text,children:"Description (optional)"}),(0,s.jsx)(k.p,{value:Z,onChange:e=>U(e.target.value),placeholder:"A beautiful custom color scheme",color:G.text,borderColor:G.border})]})]})}),(0,s.jsxs)(W.j,{children:[(0,s.jsx)(E.$,{variant:"ghost",mr:3,onClick:R,children:"Cancel"}),(0,s.jsx)(E.$,{bg:G.primary,color:"white",_hover:{bg:G.primaryDark},leftIcon:(0,s.jsx)(m.I,{as:B.Bc_}),onClick:()=>{if(!X.trim())return void L({title:"Name Required",description:"Please enter a name for your custom theme",status:"warning",duration:3e3,isClosable:!0});let e={id:"custom-".concat(Date.now()),name:X.trim(),description:Z.trim()||"Custom theme: ".concat(X.trim()),isCustom:!0,colors:{...G}};M(e),Y(""),U(""),R(),o(),L({title:"Theme Saved",description:'Your custom theme "'.concat(e.name,'" has been saved and applied'),status:"success",duration:4e3,isClosable:!0})},children:"Save Theme"})]})]})]})]})}}}]);