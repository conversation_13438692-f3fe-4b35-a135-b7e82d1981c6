import YAML from 'yaml';
import fs from 'fs';
import path from 'path';

// Load the config file
function locateConfig(): string {
  const attempts = [
    'config.yml',
    '../config.yml',
    '../../config.yml',
    '../../../config.yml',
    '../../../../config.yml',
  ].map(rel => path.resolve(process.cwd(), rel));
  let found = attempts.find(p => fs.existsSync(p));
  if (!found) {
    const dirBased = path.resolve(__dirname, '../../../config.yml');
    if (fs.existsSync(dirBased)) found = dirBased;
  }
  if (!found) throw new Error('config.yml not found');
  return found;
}

const configPath = locateConfig();
const config = YAML.parse(fs.readFileSync(configPath, 'utf8')) as any;

// Allow overriding dashboard URL (used by NextAuth) via environment variables so
// users don't have to edit the YAML when deploying in Pterodactyl / Docker.
// If any of these env vars are present they take precedence.
const NEXTAUTH_URL_ENV =
  process.env.NEXTAUTH_URL ||
  process.env.DASHBOARD_URL ||
  null;

const parsedUrl = new URL(config.dashboard?.url || 'http://localhost:3000');
const LOCALHOST_URL = `${parsedUrl.protocol}//localhost:${parsedUrl.port || '3000'}`;

// Export environment variables for Next.js
export const env = {
  DISCORD_BOT_TOKEN: config.bot.token,
  DISCORD_CLIENT_ID: config.bot.clientId,
  DISCORD_CLIENT_SECRET: config.bot.clientSecret,
  DISCORD_GUILD_ID: config.bot.guildId,
  // Priority: explicit env var -> YAML nextAuthUrl -> YAML url -> localhost
  NEXTAUTH_URL: NEXTAUTH_URL_ENV ||
    config.dashboard?.nextAuthUrl ||
    config.dashboard?.url ||
    LOCALHOST_URL,
  // Provide a secondary host (localhost) that callbacks can allow
  NEXTAUTH_LOCALHOST_URL: LOCALHOST_URL
};

// Export the dashboard config
export const dashboardConfig = {
  bot: {
    token: config.bot.token,
    clientId: config.bot.clientId,
    clientSecret: config.bot.clientSecret,
    guildId: config.bot.guildId,
    presence: config.bot.presence || {},
  },
  dashboard: {
    url: NEXTAUTH_URL_ENV || config.dashboard?.url || 'http://localhost:3000',
    nextAuthUrl: NEXTAUTH_URL_ENV || config.dashboard?.nextAuthUrl || config.dashboard?.url || 'http://localhost:3000',
    admins: config.dashboard?.admins || [],
    adminRoleIds: config.dashboard?.adminRoleIds || [],
    session: {
      secret: config.dashboard?.session?.secret || config.bot.clientSecret,
    }
  }
};

// Make sure required variables are present
Object.entries(env).forEach(([key, value]) => {
  if (!value) {
    throw new Error(`Missing required environment variable: ${key}`);
  }
}); 