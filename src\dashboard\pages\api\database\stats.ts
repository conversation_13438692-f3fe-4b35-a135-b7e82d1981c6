import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const client = await connectToDatabase();
    const db = client.db(dbName);
    
    // Get list of collections
    const collections = await db.listCollections().toArray();
    
    // Get total document count across all collections
    let totalDocuments = 0;
    for (const collection of collections) {
      const count = await db.collection(collection.name).countDocuments();
      totalDocuments += count;
    }

    // Get database stats
    const stats = await db.stats();

    return res.status(200).json({
      collectionsCount: collections.length,
      documentsCount: totalDocuments,
      databaseSize: stats.dataSize,
      storageSize: stats.storageSize,
      avgObjSize: stats.avgObjSize,
      indexes: stats.indexes,
      indexSize: stats.indexSize
    });
  } catch (error) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 