import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Icon,
  useToast,
  Card,
  CardBody,
  Badge,
  Button,
  Skeleton,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
  useDisclosure,
  Switch,
  Input,
  InputGroup,
  InputLeftElement,
  Select,
  Wrap,
  WrapItem,
  Flex,
} from '@chakra-ui/react';
import { useEffect, useState, useRef } from 'react';
import Layout from '../../components/Layout';
import { FiCommand, FiTrash2, FiRefreshCw, FiSearch, FiFilter } from 'react-icons/fi';

interface CommandInfo {
  id: string;
  name: string;
  description: string;
  type: number;
  scope: 'GLOBAL' | 'GUILD';
  enabled: boolean;
  addon: string;
  category: string;
}

// Color schemes for different addons
const ADDON_COLORS: Record<string, { color: string; gradient: { from: string; to: string } }> = {
  moderation: {
    color: 'red',
    gradient: { from: 'rgba(245, 101, 101, 0.4)', to: 'rgba(245, 101, 101, 0.1)' }
  },
  example: {
    color: 'blue',
    gradient: { from: 'rgba(66, 153, 225, 0.4)', to: 'rgba(66, 153, 225, 0.1)' }
  },
  tickets: {
    color: 'purple',
    gradient: { from: 'rgba(159, 122, 234, 0.4)', to: 'rgba(159, 122, 234, 0.1)' }
  },
  'voice-mistress': {
    color: 'pink',
    gradient: { from: 'rgba(237, 137, 179, 0.4)', to: 'rgba(237, 137, 179, 0.1)' }
  },
  'welcome-goodbye': {
    color: 'green',
    gradient: { from: 'rgba(72, 187, 120, 0.4)', to: 'rgba(72, 187, 120, 0.1)' }
  },
  Unknown: {
    color: 'gray',
    gradient: { from: 'rgba(113, 128, 150, 0.4)', to: 'rgba(113, 128, 150, 0.1)' }
  }
};

const CommandCard: React.FC<{
  command: CommandInfo;
  onDelete: (command: CommandInfo) => void;
  onToggle: (command: CommandInfo, enabled: boolean) => void;
}> = ({ command, onDelete, onToggle }) => {
  const colorScheme = ADDON_COLORS[command.addon] || ADDON_COLORS.Unknown;
  const [isToggling, setIsToggling] = useState(false);

  const handleToggle = async () => {
    setIsToggling(true);
    await onToggle(command, !command.enabled);
    setIsToggling(false);
  };

  return (
    <Card
      bg={`linear-gradient(135deg, ${colorScheme.gradient.from}, ${colorScheme.gradient.to})`}
      backdropFilter="blur(10px)"
      borderWidth={2}
      borderColor={`${colorScheme.color}.400`}
      rounded="xl"
      overflow="hidden"
      transition="all 0.2s"
      opacity={command.enabled ? 1 : 0.7}
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: `0 4px 20px ${colorScheme.gradient.from}`,
      }}
    >
      <CardBody>
        <VStack align="stretch" spacing={4}>
          <HStack justify="space-between">
            <HStack>
              <Icon 
                as={FiCommand}
                color={`${colorScheme.color}.400`}
                boxSize={5}
              />
              <Heading size="md" color="white">
                /{command.name}
              </Heading>
            </HStack>
            <VStack spacing={1} align="end">
              <Badge colorScheme={colorScheme.color} size="sm">
                {command.category}
              </Badge>
              <Badge variant="outline" colorScheme="gray" size="sm">
                {command.scope}
              </Badge>
            </VStack>
          </HStack>

          <Text color="gray.300" fontSize="sm" noOfLines={2}>
            {command.description}
          </Text>

          <Text color="gray.400" fontSize="xs">
            ID: {command.id}
          </Text>

          <HStack justify="space-between">
            <Switch
              isChecked={command.enabled}
              onChange={handleToggle}
              isDisabled={isToggling}
              colorScheme={colorScheme.color}
            />
            <Button
              size="sm"
              leftIcon={<Icon as={FiTrash2} />}
              variant="ghost"
              colorScheme={colorScheme.color}
              onClick={() => onDelete(command)}
            >
              Remove
            </Button>
          </HStack>
        </VStack>
      </CardBody>
    </Card>
  );
};

export default function CommandsPage() {
  const [commands, setCommands] = useState<CommandInfo[]>([]);
  const [filteredCommands, setFilteredCommands] = useState<CommandInfo[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedCommand, setSelectedCommand] = useState<CommandInfo | null>(null);
  const { isOpen, onOpen, onClose } = useDisclosure();
  const cancelRef = useRef<HTMLButtonElement>(null);
  const toast = useToast();

  // Get unique categories from commands
  const categories = Array.from(new Set(commands.map(cmd => cmd.category))).sort();

  // Filter commands based on search and category
  useEffect(() => {
    let filtered = commands;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(cmd => 
        cmd.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cmd.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        cmd.category.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(cmd => cmd.category === selectedCategory);
    }

    setFilteredCommands(filtered);
  }, [commands, searchQuery, selectedCategory]);

  const fetchCommands = async () => {
    try {
      setLoading(true);
      const res = await fetch('/api/admin/commands');
      if (res.ok) {
        const data = await res.json();
        setCommands(data);
      } else {
        throw new Error('Failed to fetch commands');
      }
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to fetch commands',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCommands();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchCommands();
    setRefreshing(false);
  };

  const handleDelete = async (command: CommandInfo) => {
    setSelectedCommand(command);
    onOpen();
  };

  const handleToggle = async (command: CommandInfo, enabled: boolean) => {
    try {
      const res = await fetch('/api/admin/commands', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          commandId: command.id,
          enabled,
        }),
      });

      if (!res.ok) {
        throw new Error('Failed to update command state');
      }

      setCommands(commands.map(c => 
        c.id === command.id ? { ...c, enabled } : c
      ));

      toast({
        title: 'Success',
        description: `Command /${command.name} has been ${enabled ? 'enabled' : 'disabled'}`,
        status: 'success',
        duration: 3000,
      });
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to update command state',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const confirmDelete = async () => {
    if (!selectedCommand) return;

    try {
      const res = await fetch(`/api/admin/commands?commandId=${selectedCommand.id}&scope=${selectedCommand.scope}`, {
        method: 'DELETE',
      });

      if (!res.ok) {
        throw new Error('Failed to delete command');
      }

      setCommands(commands.filter(c => c.id !== selectedCommand.id));
      toast({
        title: 'Success',
        description: `Command /${selectedCommand.name} has been removed`,
        status: 'success',
        duration: 3000,
      });
    } catch (err: any) {
      toast({
        title: 'Error',
        description: err.message || 'Failed to delete command',
        status: 'error',
        duration: 5000,
      });
    } finally {
      onClose();
      setSelectedCommand(null);
    }
  };

  return (
    <Layout>
      <Container maxW="7xl" py={6}>
        <VStack spacing={6} align="stretch">
          <Flex direction={{ base: 'column', lg: 'row' }} justify="space-between" align={{ base: 'start', lg: 'center' }} gap={4}>
            <Box>
              <Heading size="lg" mb={2} bgGradient="linear(to-r, pink.500, purple.500)" bgClip="text">
                Bot Commands
              </Heading>
              <Text color="gray.400">
                Manage your Discord bot's slash commands ({filteredCommands.length} of {commands.length})
              </Text>
            </Box>
            <Button
              leftIcon={<Icon as={FiRefreshCw} />}
              colorScheme="purple"
              variant="outline"
              onClick={handleRefresh}
              isLoading={refreshing}
            >
              Refresh
            </Button>
          </Flex>

          {/* Search and Filter Controls */}
          <Flex direction={{ base: 'column', md: 'row' }} gap={4}>
            <InputGroup flex={1}>
              <InputLeftElement pointerEvents="none">
                <Icon as={FiSearch} color="gray.400" />
              </InputLeftElement>
              <Input
                placeholder="Search commands..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                bg="whiteAlpha.50"
                border="1px solid"
                borderColor="whiteAlpha.200"
                _hover={{ borderColor: 'whiteAlpha.300' }}
                _focus={{ borderColor: 'purple.400', boxShadow: '0 0 0 1px var(--chakra-colors-purple-400)' }}
              />
            </InputGroup>
            <Select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              w={{ base: 'full', md: '200px' }}
              bg="whiteAlpha.50"
              border="1px solid"
              borderColor="whiteAlpha.200"
              _hover={{ borderColor: 'whiteAlpha.300' }}
              _focus={{ borderColor: 'purple.400', boxShadow: '0 0 0 1px var(--chakra-colors-purple-400)' }}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </Select>
          </Flex>

          {/* Category Stats */}
          <Wrap spacing={3}>
            {categories.map(category => {
              const count = commands.filter(cmd => cmd.category === category).length;
              const colorScheme = ADDON_COLORS[category.toLowerCase()] || ADDON_COLORS.Unknown;
              return (
                <WrapItem key={category}>
                  <Badge
                    colorScheme={colorScheme.color}
                    variant={selectedCategory === category ? 'solid' : 'outline'}
                    cursor="pointer"
                    onClick={() => setSelectedCategory(selectedCategory === category ? 'all' : category)}
                    px={3}
                    py={1}
                    rounded="full"
                  >
                    {category} ({count})
                  </Badge>
                </WrapItem>
              );
            })}
          </Wrap>

          {loading ? (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {[1, 2, 3].map(i => (
                <Skeleton key={i} height="200px" rounded="xl" />
              ))}
            </SimpleGrid>
          ) : (
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {filteredCommands.map(command => (
                <CommandCard
                  key={command.id}
                  command={command}
                  onDelete={handleDelete}
                  onToggle={handleToggle}
                />
              ))}
            </SimpleGrid>
          )}

          {!loading && filteredCommands.length === 0 && (
            <Box textAlign="center" py={12}>
              <Icon as={FiFilter} boxSize={12} color="gray.400" mb={4} />
              <Heading size="md" color="gray.400" mb={2}>
                No commands found
              </Heading>
              <Text color="gray.500">
                Try adjusting your search or category filter
              </Text>
            </Box>
          )}
        </VStack>

        <AlertDialog
          isOpen={isOpen}
          leastDestructiveRef={cancelRef}
          onClose={onClose}
        >
          <AlertDialogOverlay>
            <AlertDialogContent bg="gray.800" borderColor="whiteAlpha.200" borderWidth={1}>
              <AlertDialogHeader fontSize="lg" fontWeight="bold">
                Delete Command
              </AlertDialogHeader>

              <AlertDialogBody>
                Are you sure you want to remove the command /{selectedCommand?.name}? This action cannot be undone.
              </AlertDialogBody>

              <AlertDialogFooter>
                <Button ref={cancelRef} onClick={onClose}>
                  Cancel
                </Button>
                <Button colorScheme="red" onClick={confirmDelete} ml={3}>
                  Delete
                </Button>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialogOverlay>
        </AlertDialog>
      </Container>
    </Layout>
  );
} 