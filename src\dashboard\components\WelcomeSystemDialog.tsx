// @ts-nocheck
import {
    <PERSON><PERSON>,
    <PERSON>dal<PERSON><PERSON>lay,
    Modal<PERSON>ontent,
    <PERSON>dal<PERSON>eader,
    Modal<PERSON>ooter,
    ModalBody,
    ModalCloseButton,
    Button,
    useToast,
    VStack,
    Text,
    Spinner,
    FormControl,
    FormLabel,
    Switch,
    Select,
    Textarea,
    Tabs,
    TabList,
    Tab,
    TabPanels,
    TabPanel,
    SimpleGrid,
    Checkbox,
    CheckboxGroup,
    Input,
    HStack,
    Box,
    Icon,
  } from '@chakra-ui/react';
  import { useState, useEffect } from 'react';
  import { FiMessageSquare, FiLogOut } from 'react-icons/fi';
  
  const defaultSettings = {
    welcome: {
      enabled: false,
      channelId: '',
      message: 'Welcome {user} to {guild}! You are the {memberCount}th member.',
      autoRoles: [],
      embedColor: '#00FF00',
    },
    goodbye: {
      enabled: false,
      channelId: '',
      message: 'Goodbye {user}! We will miss you.',
      embedColor: '#FF0000',
    },
  };
  
  export default function WelcomeSystemDialog({ isOpen, onClose, channels = [], roles = [] }) {
    const toast = useToast();
    const [settings, setSettings] = useState(defaultSettings);
    const [isLoading, setIsLoading] = useState(true);
    const [isSaving, setIsSaving] = useState(false);
  
    const textChannels = channels.filter(c => c.type === 0);
    const manageableRoles = roles.filter(r => r.name !== '@everyone');
  
    const fetchSettings = async () => {
      setIsLoading(true);
      try {
        const res = await fetch('/api/automation/welcome');
        if (!res.ok) throw new Error('Failed to fetch settings');
        const data = await res.json();
        setSettings(data);
      } catch (error) {
        toast({
          title: 'Error loading settings',
          description: error.message,
          status: 'error',
          duration: 5000,
        });
      } finally {
        setIsLoading(false);
      }
    };
  
    const handleSave = async () => {
      setIsSaving(true);
      try {
        const res = await fetch('/api/automation/welcome', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(settings),
        });
        if (!res.ok) throw new Error('Failed to save settings');
        toast({
          title: 'Settings Saved',
          status: 'success',
          duration: 3000,
        });
        onClose();
      } catch (error) {
        toast({
          title: 'Error saving settings',
          description: error.message,
          status: 'error',
          duration: 5000,
        });
      } finally {
        setIsSaving(false);
      }
    };
  
    useEffect(() => {
      if (isOpen) {
        fetchSettings();
      }
    }, [isOpen]);
  
    const handleWelcomeChange = (field, value) => {
      setSettings(prev => ({ ...prev, welcome: { ...prev.welcome, [field]: value } }));
    };
  
    const handleGoodbyeChange = (field, value) => {
      setSettings(prev => ({ ...prev, goodbye: { ...prev.goodbye, [field]: value } }));
    };
  
    return (
      <Modal isOpen={isOpen} onClose={onClose} size="3xl" scrollBehavior="inside">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Welcome & Goodbye System</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {isLoading ? (
              <VStack justify="center" h="400px">
                <Spinner size="xl" />
                <Text>Loading Settings...</Text>
              </VStack>
            ) : (
              <Tabs isFitted variant="enclosed">
                <TabList>
                  <Tab><Icon as={FiMessageSquare} mr={2} /> Welcome Messages</Tab>
                  <Tab><Icon as={FiLogOut} mr={2} /> Goodbye Messages</Tab>
                </TabList>
                <TabPanels>
                  <TabPanel>
                    <VStack spacing={6} align="stretch">
                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="welcome-enabled" mb="0">
                          Enable Welcome Messages
                        </FormLabel>
                        <Switch
                          id="welcome-enabled"
                          isChecked={settings.welcome.enabled}
                          onChange={(e) => handleWelcomeChange('enabled', e.target.checked)}
                        />
                      </FormControl>
  
                      <FormControl isDisabled={!settings.welcome.enabled}>
                        <FormLabel>Welcome Channel</FormLabel>
                        <Select
                          placeholder="Select a channel"
                          value={settings.welcome.channelId || ''}
                          onChange={(e) => handleWelcomeChange('channelId', e.target.value)}
                        >
                          {textChannels.map(channel => (
                            <option key={channel.id} value={channel.id}>
                              #{channel.name}
                            </option>
                          ))}
                        </Select>
                      </FormControl>
  
                      <FormControl isDisabled={!settings.welcome.enabled}>
                        <FormLabel>Welcome Message</FormLabel>
                        <Textarea
                          value={settings.welcome.message}
                          onChange={(e) => handleWelcomeChange('message', e.target.value)}
                          placeholder="Enter your welcome message..."
                          rows={5}
                        />
                        <Text fontSize="xs" color="gray.500" mt={1}>
                          Placeholders: {'{user}'}, {'{username}'}, {'{userTag}'}, {'{guild}'}, {'{memberCount}'}
                        </Text>
                      </FormControl>
  
                      <FormControl isDisabled={!settings.welcome.enabled}>
                        <FormLabel>Auto-Assign Roles</FormLabel>
                        <Box p={3} borderWidth={1} borderRadius="md" maxH="200px" overflowY="auto">
                          <CheckboxGroup
                            value={settings.welcome.autoRoles}
                            onChange={(values) => handleWelcomeChange('autoRoles', values)}
                          >
                            <SimpleGrid columns={{ base: 1, md: 2 }} spacing={2}>
                              {manageableRoles.map(role => (
                                <Checkbox key={role.id} value={role.id}>
                                  {role.name}
                                </Checkbox>
                              ))}
                            </SimpleGrid>
                          </CheckboxGroup>
                        </Box>
                      </FormControl>
                    </VStack>
                  </TabPanel>
                  <TabPanel>
                    <VStack spacing={6} align="stretch">
                      <FormControl display="flex" alignItems="center">
                        <FormLabel htmlFor="goodbye-enabled" mb="0">
                          Enable Goodbye Messages
                        </FormLabel>
                        <Switch
                          id="goodbye-enabled"
                          isChecked={settings.goodbye.enabled}
                          onChange={(e) => handleGoodbyeChange('enabled', e.target.checked)}
                        />
                      </FormControl>
  
                      <FormControl isDisabled={!settings.goodbye.enabled}>
                        <FormLabel>Goodbye Channel</FormLabel>
                        <Select
                          placeholder="Select a channel"
                          value={settings.goodbye.channelId || ''}
                          onChange={(e) => handleGoodbyeChange('channelId', e.target.value)}
                        >
                          {textChannels.map(channel => (
                            <option key={channel.id} value={channel.id}>
                              #{channel.name}
                            </option>
                          ))}
                        </Select>
                      </FormControl>
  
                      <FormControl isDisabled={!settings.goodbye.enabled}>
                        <FormLabel>Goodbye Message</FormLabel>
                        <Textarea
                          value={settings.goodbye.message}
                          onChange={(e) => handleGoodbyeChange('message', e.target.value)}
                          placeholder="Enter your goodbye message..."
                          rows={5}
                        />
                         <Text fontSize="xs" color="gray.500" mt={1}>
                          Placeholders: {'{user}'}, {'{username}'}, {'{userTag}'}, {'{guild}'}, {'{memberCount}'}
                        </Text>
                      </FormControl>
                    </VStack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleSave} isLoading={isSaving} isDisabled={isLoading}>
              Save Settings
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    );
  }
  