self.__REACT_LOADABLE_MANIFEST="{\"..\\\\..\\\\node_modules\\\\.pnpm\\\\next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0\\\\node_modules\\\\next\\\\dist\\\\client\\\\index.js -> ../pages/_app\":{\"id\":\"..\\\\..\\\\node_modules\\\\.pnpm\\\\next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0\\\\node_modules\\\\next\\\\dist\\\\client\\\\index.js -> ../pages/_app\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_-79a483.js\"]},\"..\\\\..\\\\node_modules\\\\.pnpm\\\\next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0\\\\node_modules\\\\next\\\\dist\\\\client\\\\index.js -> ../pages/_error\":{\"id\":\"..\\\\..\\\\node_modules\\\\.pnpm\\\\next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0\\\\node_modules\\\\next\\\\dist\\\\client\\\\index.js -> ../pages/_error\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_pnpm_next_15_3_5_react-dom_19_1_0_react_19_1_0__react_19_1_0_-67f391.js\"]},\"components\\\\YamlEditor.tsx -> ace-builds/src-noconflict/ext-language_tools\":{\"id\":\"components\\\\YamlEditor.tsx -> ace-builds/src-noconflict/ext-language_tools\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-749589.js\"]},\"components\\\\YamlEditor.tsx -> ace-builds/src-noconflict/mode-yaml\":{\"id\":\"components\\\\YamlEditor.tsx -> ace-builds/src-noconflict/mode-yaml\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-5fc50a.js\"]},\"components\\\\YamlEditor.tsx -> ace-builds/src-noconflict/theme-twilight\":{\"id\":\"components\\\\YamlEditor.tsx -> ace-builds/src-noconflict/theme-twilight\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-0a6f04.js\"]},\"components\\\\YamlEditor.tsx -> react-ace\":{\"id\":\"components\\\\YamlEditor.tsx -> react-ace\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_pnpm_react-ace_11_0_1_react-dom_19_1_0_react_19_1_0__react_19-f0b51d.js\"]},\"pages\\\\admin\\\\addons.tsx -> ../../components/YamlEditor\":{\"id\":\"pages\\\\admin\\\\addons.tsx -> ../../components/YamlEditor\",\"files\":[\"static/chunks/_pages-dir-browser_components_YamlEditor_tsx.js\"]},\"pages\\\\admin\\\\guilds.tsx -> ../../components/ColorBuilder\":{\"id\":\"pages\\\\admin\\\\guilds.tsx -> ../../components/ColorBuilder\",\"files\":[\"static/chunks/_pages-dir-browser_components_ColorBuilder_tsx.js\"]},\"pages\\\\admin\\\\guilds.tsx -> ../../components/CreateChannelDialog\":{\"id\":\"pages\\\\admin\\\\guilds.tsx -> ../../components/CreateChannelDialog\",\"files\":[\"static/chunks/_pages-dir-browser_components_CreateChannelDialog_tsx.js\"]},\"pages\\\\admin\\\\guilds.tsx -> ../../components/CreateRoleDialog\":{\"id\":\"pages\\\\admin\\\\guilds.tsx -> ../../components/CreateRoleDialog\",\"files\":[\"static/chunks/_pages-dir-browser_components_CreateRoleDialog_tsx.js\"]},\"pages\\\\admin\\\\guilds.tsx -> ../../components/EditChannelDialog\":{\"id\":\"pages\\\\admin\\\\guilds.tsx -> ../../components/EditChannelDialog\",\"files\":[\"static/chunks/_pages-dir-browser_components_EditChannelDialog_tsx.js\"]},\"pages\\\\admin\\\\guilds.tsx -> ../../components/EditRoleDialog\":{\"id\":\"pages\\\\admin\\\\guilds.tsx -> ../../components/EditRoleDialog\",\"files\":[\"static/chunks/_pages-dir-browser_components_EditRoleDialog_tsx.js\"]},\"pages\\\\admin\\\\guilds.tsx -> ../../components/WelcomeSystemDialog\":{\"id\":\"pages\\\\admin\\\\guilds.tsx -> ../../components/WelcomeSystemDialog\",\"files\":[\"static/chunks/_pages-dir-browser_components_WelcomeSystemDialog_tsx.js\"]}}"