"use strict";exports.id=5039,exports.ids=[5039],exports.modules={5039:(e,o,r)=>{r.a(e,async(e,t)=>{try{r.r(o),r.d(o,{default:()=>x});var s=r(8732),n=r(2015),i=r(9733),l=r(8079),a=r(2695),c=r(3001),d=e([i,c]);[i,c]=d.then?(await d)():d;let h={primary:{label:"Primary Color",description:"Main accent color for buttons, active states, and highlights",example:"Active sidebar items, primary buttons"},primaryLight:{label:"Primary Light",description:"Lighter version of primary color for hover states",example:"Button hover effects, gradients"},primaryDark:{label:"Primary Dark",description:"Darker version of primary color for pressed states",example:"Button active states, deeper accents"},secondary:{label:"Secondary Color",description:"Supporting accent color for variety",example:"Secondary buttons, alternative highlights"},accent:{label:"Accent Color",description:"Decorative color for gradients and special elements",example:"Gradient endpoints, special indicators"},background:{label:"Background",description:"Main dashboard background color",example:"Page background, main layout"},surface:{label:"Surface",description:"Color for cards, sidebar, and elevated elements",example:"Cards, sidebar, modals"},text:{label:"Primary Text",description:"Main text color for headings and important content",example:"Headings, active text, important labels"},textSecondary:{label:"Secondary Text",description:"Subdued text color for descriptions and less important content",example:"Descriptions, inactive states, subtitles"},border:{label:"Border Color",description:"Color for borders, dividers, and outlines",example:"Card borders, input outlines, dividers"},success:{label:"Success Color",description:"Color for success states and positive actions",example:"Success messages, completed states"},warning:{label:"Warning Color",description:"Color for warning states and caution",example:"Warning messages, pending states"},error:{label:"Error Color",description:"Color for error states and destructive actions",example:"Error messages, delete buttons"},info:{label:"Info Color",description:"Color for informational content",example:"Info messages, helpful hints"}};function x({isOpen:e,onClose:o}){let{currentScheme:r,addCustomScheme:t,customSchemes:d,deleteCustomScheme:x}=(0,c.DP)(),p=(0,i.useToast)(),{isOpen:g,onOpen:u,onClose:b}=(0,i.useDisclosure)(),[m,j]=(0,n.useState)(r.colors),[f,S]=(0,n.useState)(""),[C,y]=(0,n.useState)(""),v=(e,o)=>{j(r=>({...r,[e]:o}))},k=(e,o)=>{x(e),p({title:"Theme Deleted",description:`Custom theme "${o}" has been deleted`,status:"info",duration:3e3,isClosable:!0})},B=({title:e,children:o})=>(0,s.jsx)(i.Card,{bg:m.surface,borderColor:m.border,borderWidth:"1px",children:(0,s.jsx)(i.CardBody,{p:4,children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:3,children:[(0,s.jsx)(i.Text,{fontWeight:"bold",color:m.text,fontSize:"sm",children:e}),o]})})});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.Modal,{isOpen:e,onClose:o,size:"6xl",children:[(0,s.jsx)(i.ModalOverlay,{bg:"blackAlpha.700",backdropFilter:"blur(10px)"}),(0,s.jsxs)(i.ModalContent,{bg:m.background,borderColor:m.border,borderWidth:"1px",boxShadow:`0 20px 25px -5px ${m.background}40, 0 10px 10px -5px ${m.background}40`,maxH:"90vh",overflow:"hidden",children:[(0,s.jsx)(i.ModalHeader,{color:m.text,borderBottom:"1px solid",borderColor:m.border,children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.Icon,{as:l.Pj4}),(0,s.jsx)(i.Text,{children:"Custom Color Builder"})]})}),(0,s.jsx)(i.ModalCloseButton,{color:m.textSecondary}),(0,s.jsx)(i.ModalBody,{overflow:"auto",p:6,children:(0,s.jsxs)(i.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(i.Alert,{status:"info",variant:"subtle",rounded:"md",bg:`${m.info}20`,borderColor:m.info,children:[(0,s.jsx)(i.AlertIcon,{color:m.info}),(0,s.jsxs)(i.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(i.AlertTitle,{color:m.text,children:"Color Builder"}),(0,s.jsx)(i.AlertDescription,{color:m.textSecondary,children:'Adjust colors and see live preview. Click "Save Theme" when you\'re happy with your design.'})]})]}),(0,s.jsxs)(i.SimpleGrid,{columns:{base:1,xl:2},spacing:8,children:[(0,s.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(i.Text,{fontSize:"lg",fontWeight:"bold",color:m.text,children:"Color Settings"}),(0,s.jsx)(i.VStack,{spacing:4,align:"stretch",maxH:"500px",overflow:"auto",pr:2,children:Object.entries(h).map(([e,o])=>(0,s.jsx)(i.Card,{bg:m.surface,borderColor:m.border,borderWidth:"1px",children:(0,s.jsx)(i.CardBody,{p:4,children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:3,children:[(0,s.jsxs)(i.VStack,{align:"start",spacing:1,children:[(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.Box,{w:4,h:4,bg:m[e],rounded:"md",border:"1px solid",borderColor:m.border}),(0,s.jsx)(i.Text,{fontWeight:"bold",color:m.text,fontSize:"sm",children:o.label})]}),(0,s.jsx)(i.Text,{fontSize:"xs",color:m.textSecondary,children:o.description}),(0,s.jsxs)(i.Text,{fontSize:"xs",color:m.textSecondary,fontStyle:"italic",children:["Used for: ",o.example]})]}),(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.Input,{type:"color",value:m[e],onChange:o=>v(e,o.target.value),w:12,h:8,p:0,border:"none",rounded:"md"}),(0,s.jsx)(i.Input,{value:m[e],onChange:o=>v(e,o.target.value),placeholder:"#000000",color:m.text,borderColor:m.border,fontSize:"sm",fontFamily:"mono"})]})]})})},e))})]}),(0,s.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(i.Text,{fontSize:"lg",fontWeight:"bold",color:m.text,children:"Live Preview"}),(0,s.jsx)(i.Box,{bg:m.background,p:4,rounded:"lg",border:"2px solid",borderColor:m.border,minH:"500px",children:(0,s.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(i.Box,{bg:m.surface,p:3,rounded:"md",borderColor:m.border,borderWidth:"1px",children:(0,s.jsxs)(i.HStack,{justify:"space-between",children:[(0,s.jsx)(i.Text,{fontSize:"md",fontWeight:"bold",color:m.text,children:"Dashboard Preview"}),(0,s.jsx)(i.Badge,{bg:m.primary,color:"white",px:2,py:1,rounded:"md",children:"Live"})]})}),(0,s.jsxs)(i.SimpleGrid,{columns:2,spacing:3,children:[(0,s.jsx)(B,{title:"Statistics",children:(0,s.jsxs)(i.VStack,{align:"start",spacing:2,children:[(0,s.jsx)(i.Text,{fontSize:"xl",fontWeight:"bold",color:m.text,children:"1,234"}),(0,s.jsx)(i.Text,{fontSize:"xs",color:m.textSecondary,children:"Total Users"}),(0,s.jsx)(i.Text,{fontSize:"xs",color:m.success,children:"+12% this month"})]})}),(0,s.jsx)(B,{title:"Activity",children:(0,s.jsxs)(i.VStack,{align:"start",spacing:2,children:[(0,s.jsx)(i.Text,{fontSize:"xl",fontWeight:"bold",color:m.text,children:"89"}),(0,s.jsx)(i.Text,{fontSize:"xs",color:m.textSecondary,children:"Active Sessions"}),(0,s.jsx)(i.Text,{fontSize:"xs",color:m.warning,children:"Monitoring"})]})})]}),(0,s.jsxs)(i.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(i.Text,{fontSize:"sm",fontWeight:"bold",color:m.text,children:"Button Examples"}),(0,s.jsxs)(i.HStack,{spacing:2,children:[(0,s.jsx)(i.Button,{bg:m.primary,color:"white",size:"sm",leftIcon:(0,s.jsx)(i.Icon,{as:a.rQ8}),children:"Primary"}),(0,s.jsx)(i.Button,{variant:"outline",borderColor:m.border,color:m.text,size:"sm",leftIcon:(0,s.jsx)(i.Icon,{as:a.YXz}),children:"Secondary"}),(0,s.jsx)(i.Button,{variant:"ghost",color:m.textSecondary,size:"sm",leftIcon:(0,s.jsx)(i.Icon,{as:l.Vap}),children:"Ghost"})]})]}),(0,s.jsxs)(i.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(i.Text,{fontSize:"sm",fontWeight:"bold",color:m.text,children:"Status Examples"}),(0,s.jsxs)(i.HStack,{spacing:2,fontSize:"xs",children:[(0,s.jsx)(i.Badge,{bg:m.success,color:"white",px:2,py:1,rounded:"md",children:"Success"}),(0,s.jsx)(i.Badge,{bg:m.warning,color:"white",px:2,py:1,rounded:"md",children:"Warning"}),(0,s.jsx)(i.Badge,{bg:m.error,color:"white",px:2,py:1,rounded:"md",children:"Error"}),(0,s.jsx)(i.Badge,{bg:m.info,color:"white",px:2,py:1,rounded:"md",children:"Info"})]})]})]})})]})]}),d.length>0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.Divider,{borderColor:m.border}),(0,s.jsxs)(i.VStack,{spacing:3,align:"stretch",children:[(0,s.jsx)(i.Text,{fontSize:"lg",fontWeight:"bold",color:m.text,children:"Your Custom Themes"}),(0,s.jsx)(i.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:3,children:d.map(e=>(0,s.jsx)(i.Card,{bg:m.surface,borderColor:m.border,borderWidth:"1px",children:(0,s.jsx)(i.CardBody,{p:3,children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:2,children:[(0,s.jsxs)(i.HStack,{justify:"space-between",children:[(0,s.jsx)(i.Text,{fontWeight:"bold",color:m.text,fontSize:"sm",noOfLines:1,children:e.name}),(0,s.jsx)(i.Button,{size:"xs",variant:"ghost",color:m.error,onClick:()=>k(e.id,e.name),children:(0,s.jsx)(i.Icon,{as:l.IXo})})]}),(0,s.jsx)(i.Text,{fontSize:"xs",color:m.textSecondary,noOfLines:2,children:e.description}),(0,s.jsxs)(i.HStack,{spacing:1,children:[(0,s.jsx)(i.Box,{w:3,h:3,bg:e.colors.primary,rounded:"full"}),(0,s.jsx)(i.Box,{w:3,h:3,bg:e.colors.secondary,rounded:"full"}),(0,s.jsx)(i.Box,{w:3,h:3,bg:e.colors.accent,rounded:"full"}),(0,s.jsx)(i.Box,{w:3,h:3,bg:e.colors.success,rounded:"full"})]})]})})},e.id))})]})]})]})}),(0,s.jsx)(i.ModalFooter,{borderTop:"1px solid",borderColor:m.border,children:(0,s.jsxs)(i.HStack,{spacing:3,children:[(0,s.jsx)(i.Button,{variant:"ghost",onClick:()=>{j(r.colors),p({title:"Colors Reset",description:"Colors have been reset to the current theme",status:"info",duration:2e3,isClosable:!0})},color:m.textSecondary,children:"Reset Colors"}),(0,s.jsx)(i.Button,{variant:"ghost",onClick:o,color:m.textSecondary,children:"Cancel"}),(0,s.jsx)(i.Button,{bg:m.primary,color:"white",_hover:{bg:m.primaryDark},leftIcon:(0,s.jsx)(i.Icon,{as:l.Bc_}),onClick:u,children:"Save Theme"})]})})]})]}),(0,s.jsxs)(i.Modal,{isOpen:g,onClose:b,children:[(0,s.jsx)(i.ModalOverlay,{bg:"blackAlpha.600",backdropFilter:"blur(10px)"}),(0,s.jsxs)(i.ModalContent,{bg:m.background,borderColor:m.border,children:[(0,s.jsx)(i.ModalHeader,{color:m.text,children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.Icon,{as:l.GGD}),(0,s.jsx)(i.Text,{children:"Save Custom Theme"})]})}),(0,s.jsx)(i.ModalCloseButton,{color:m.textSecondary}),(0,s.jsx)(i.ModalBody,{children:(0,s.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(i.FormControl,{children:[(0,s.jsx)(i.FormLabel,{color:m.text,children:"Theme Name"}),(0,s.jsx)(i.Input,{value:f,onChange:e=>S(e.target.value),placeholder:"My Custom Theme",color:m.text,borderColor:m.border})]}),(0,s.jsxs)(i.FormControl,{children:[(0,s.jsx)(i.FormLabel,{color:m.text,children:"Description (optional)"}),(0,s.jsx)(i.Input,{value:C,onChange:e=>y(e.target.value),placeholder:"A beautiful custom color scheme",color:m.text,borderColor:m.border})]})]})}),(0,s.jsxs)(i.ModalFooter,{children:[(0,s.jsx)(i.Button,{variant:"ghost",mr:3,onClick:b,children:"Cancel"}),(0,s.jsx)(i.Button,{bg:m.primary,color:"white",_hover:{bg:m.primaryDark},leftIcon:(0,s.jsx)(i.Icon,{as:l.Bc_}),onClick:()=>{if(!f.trim())return void p({title:"Name Required",description:"Please enter a name for your custom theme",status:"warning",duration:3e3,isClosable:!0});let e={id:`custom-${Date.now()}`,name:f.trim(),description:C.trim()||`Custom theme: ${f.trim()}`,isCustom:!0,colors:{...m}};t(e),S(""),y(""),b(),o(),p({title:"Theme Saved",description:`Your custom theme "${e.name}" has been saved and applied`,status:"success",duration:4e3,isClosable:!0})},children:"Save Theme"})]})]})]})]})}t()}catch(e){t(e)}})}};