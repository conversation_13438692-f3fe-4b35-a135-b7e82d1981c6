"use strict";(()=>{var e={};e.id=5127,e.ids=[5127],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},7158:(e,t,r)=>{r.r(t),r.d(t,{config:()=>h,default:()=>m,routeModule:()=>g});var o={};r.r(o),r.d(o,{default:()=>b});var s=r(3433),n=r(264),a=r(584),i=r(5806),d=r(8525),l=r(2518);let{url:u,name:c}=r(8580).dashboardConfig.database,f=null;async function p(){if(f)return f;let e=await l.MongoClient.connect(u);return f=e,e}async function b(e,t){if(!await (0,i.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});try{let r=(await p()).db(c).collection("gameservers");switch(e.method){case"GET":let o=await r.find({}).toArray();return t.status(200).json(o);case"POST":let s=e.body;if(!s.type||!s.host||!s.port)return t.status(400).json({error:"Missing required fields"});if(s.port<1||s.port>65535)return t.status(400).json({error:"Invalid port number"});if(await r.findOne({host:s.host,port:s.port}))return t.status(409).json({error:"Server already exists"});let n=await r.insertOne(s);return t.status(201).json({...s,_id:n.insertedId});case"PUT":let{id:a,...i}=e.body;if(!i.type||!i.host||!i.port)return t.status(400).json({error:"Missing required fields"});if(i.port<1||i.port>65535)return t.status(400).json({error:"Invalid port number"});try{let e=null;if(a)try{e={_id:new l.ObjectId(a)}}catch(e){}e||(e={host:i.host,port:i.port});let o=await r.findOne(e);if(!o)return t.status(404).json({error:"Server not found"});if((o.host!==i.host||o.port!==i.port)&&await r.findOne({_id:{$ne:o._id},host:i.host,port:i.port}))return t.status(409).json({error:"Another server with this host and port already exists"});let s=await r.updateOne({_id:o._id},{$set:i});if(0===s.matchedCount)return t.status(404).json({error:"Server not found after update"});let n=await r.findOne({_id:o._id});return t.status(200).json(n)}catch(e){if(e instanceof Error&&e.message.includes("ObjectId"))return t.status(400).json({error:"Invalid server ID format"});return t.status(500).json({error:"Failed to update server"})}case"DELETE":let d,u;try{d="string"==typeof e.body?JSON.parse(e.body):e.body}catch(e){return t.status(400).json({error:"Invalid request body format"})}if(d.id)try{u={_id:new l.ObjectId(d.id)}}catch(e){return t.status(400).json({error:"Invalid server ID format"})}else{if(!d.host||void 0===d.port)return t.status(400).json({error:"Missing server identification (id or host/port)"});u={host:d.host.toString(),port:parseInt(d.port.toString())}}try{let e=await r.findOneAndDelete(u);if(!e?.value)return t.status(404).json({error:"Server not found"});return t.status(200).json({message:"Server deleted successfully",deletedServer:e.value})}catch(e){return t.status(500).json({error:"Failed to delete server from database"})}default:return t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).json({error:`Method ${e.method} not allowed`})}}catch(e){return t.status(500).json({error:"Internal server error"})}}let m=(0,a.M)(o,"default"),h=(0,a.M)(o,"config"),g=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/gameservers/manage",pathname:"/api/gameservers/manage",bundlePath:"",filename:""},userland:o})},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var o=r(5542),s=r.n(o);let n=require("next-auth/providers/discord");var a=r.n(n),i=r(8580);let d={providers:[a()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,o=t.accessToken||null;e.user.id=r,e.user.accessToken=o;let s=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))s=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();s=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),o=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var o=r(9021),s=r(2115),n=r.n(s),a=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");i=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=7158);module.exports=r})();