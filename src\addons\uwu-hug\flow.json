{"name": "uwu hug", "description": "fucking please work", "author": "onedeyepete", "version": "1.0.0", "nodes": [{"id": "1", "type": "trigger", "position": {"x": 97.5, "y": 41.5}, "data": {"label": "Start Here"}, "width": 80, "height": 80, "selected": false, "positionAbsolute": {"x": 97.5, "y": 41.5}, "dragging": false}, {"id": "command-1751927470790", "type": "command", "position": {"x": -97, "y": 201.75}, "data": {"label": "/hug", "commandName": "hug", "description": "uwu fuckl", "options": [{"name": "", "description": "", "type": "string", "required": false, "choices": []}], "permissions": [], "cooldown": 0, "guildOnly": false, "adminOnly": false, "allowDMs": false, "category": "general", "examples": [], "ephemeral": false, "deferReply": false}, "width": 140, "height": 110, "selected": false, "positionAbsolute": {"x": -97, "y": 201.75}, "dragging": false}, {"id": "apiRequest-1751927570411", "type": "apiRequest", "position": {"x": 52.5, "y": 346.75}, "data": {"label": "GET Request", "url": "https://nekos.best/api/v2/hug", "method": "GET", "headers": [], "bodyType": "json", "timeout": 5000, "errorHandling": "log", "retryCount": 0, "retryDelay": 1000, "followRedirects": true, "validateSSL": true}, "width": 159, "height": 110, "selected": false, "positionAbsolute": {"x": 52.5, "y": 346.75}, "dragging": false}, {"id": "action-1751927610388", "type": "action", "position": {"x": -35, "y": 529.75}, "data": {"label": "📋 Send Embed", "actionType": "sendEmbed", "message": "uwu", "channel": "", "embed": {"fields": [], "author": {"name": ""}, "footer": {"text": ""}, "description": "{response.results}", "color": "#EB459E"}}, "width": 140, "height": 92, "selected": true, "positionAbsolute": {"x": -35, "y": 529.75}, "dragging": false}], "edges": [{"source": "1", "sourceHandle": null, "target": "command-1751927470790", "targetHandle": null, "type": "gradient", "animated": false, "markerEnd": {"type": "arrowclosed", "color": "#3b82f6"}, "style": {"strokeWidth": 2}, "data": {"sourceColor": "#6b7280", "targetColor": "#3b82f6"}, "id": "reactflow__edge-1-command-1751927470790"}, {"source": "command-1751927470790", "sourceHandle": null, "target": "event-1751927476119", "targetHandle": null, "type": "gradient", "animated": false, "markerEnd": {"type": "arrowclosed", "color": "#10b981"}, "style": {"strokeWidth": 2}, "data": {"sourceColor": "#3b82f6", "targetColor": "#10b981"}, "id": "reactflow__edge-command-1751927470790-event-1751927476119"}, {"source": "command-1751927470790", "sourceHandle": null, "target": "apiRequest-1751927570411", "targetHandle": null, "type": "gradient", "animated": false, "markerEnd": {"type": "arrowclosed", "color": "#06b6d4"}, "style": {"strokeWidth": 2}, "data": {"sourceColor": "#3b82f6", "targetColor": "#06b6d4"}, "id": "reactflow__edge-command-1751927470790-apiRequest-1751927570411"}, {"source": "apiRequest-1751927570411", "sourceHandle": null, "target": "action-1751927610388", "targetHandle": null, "type": "gradient", "animated": false, "markerEnd": {"type": "arrowclosed", "color": "#a855f7"}, "style": {"strokeWidth": 2}, "data": {"sourceColor": "#06b6d4", "targetColor": "#a855f7"}, "id": "reactflow__edge-apiRequest-1751927570411-action-1751927610388"}], "metadata": {"createdAt": "2025-07-07T22:34:12.479Z", "builderVersion": "1.0.0", "originalFlow": true, "createdBy": "<EMAIL>"}}