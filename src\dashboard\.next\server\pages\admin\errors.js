"use strict";(()=>{var e={};e.id=2437,e.ids=[2437],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2053:e=>{e.exports=require("@fortawesome/react-fontawesome")},2115:e=>{e.exports=require("yaml")},2326:e=>{e.exports=require("react-dom")},3762:(e,t,r)=>{r.d(t,{N:()=>u});var s=r(5542),o=r.n(s);let i=require("next-auth/providers/discord");var l=r.n(i),a=r(9021),n=r(2115),c=r.n(n),d=r(3873);let x={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=d.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");x=c().parse(t)}catch(e){process.exit(1)}let h={bot:{token:x.bot.token,clientId:x.bot.clientId,clientSecret:x.bot.clientSecret,guildId:x.bot.guildId,ticketCategoryId:x.bot.ticketCategoryId||null,ticketLogChannelId:x.bot.ticketLogChannelId||null,prefix:x.bot.prefix},dashboard:{admins:x.dashboard?.admins||[],adminRoleIds:x.dashboard?.adminRoleIds||[],session:{secret:x.dashboard?.session?.secret||x.bot.clientSecret}},database:{url:x.database.url,name:x.database.name,options:{maxPoolSize:x.database.options?.maxPoolSize||10,minPoolSize:x.database.options?.minPoolSize||1,maxIdleTimeMS:x.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:x.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:x.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:x.database.options?.connectTimeoutMS||1e4,retryWrites:x.database.options?.retryWrites!==!1,retryReads:x.database.options?.retryReads!==!1}}};h.bot.token||process.exit(1),h.bot.clientId&&h.bot.clientSecret||process.exit(1),h.bot.guildId||process.exit(1),h.database.url&&h.database.name||process.exit(1);let u={providers:[l()({clientId:h.bot.clientId,clientSecret:h.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,s=t.accessToken||null;e.user.id=r,e.user.accessToken=s;let o=!1;if(r)if((h.dashboard.admins||[]).includes(r))o=!0;else{let e=h.dashboard.adminRoleIds||[];if(e.length&&h.bot.token&&h.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${h.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${h.bot.token}`}});if(t.ok){let r=await t.json();o=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),s=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:h.dashboard.session.secret||h.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};o()(u)},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4131:e=>{e.exports=import("@fortawesome/free-solid-svg-icons")},4550:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{config:()=>m,default:()=>x,getServerSideProps:()=>p,getStaticPaths:()=>u,getStaticProps:()=>h,reportWebVitals:()=>g,routeModule:()=>w,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>b,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>j});var o=r(1292),i=r(8834),l=r(786),a=r(3567),n=r(8077),c=r(8942),d=e([n,c]);[n,c]=d.then?(await d)():d;let x=(0,l.M)(c,"default"),h=(0,l.M)(c,"getStaticProps"),u=(0,l.M)(c,"getStaticPaths"),p=(0,l.M)(c,"getServerSideProps"),m=(0,l.M)(c,"config"),g=(0,l.M)(c,"reportWebVitals"),j=(0,l.M)(c,"unstable_getStaticProps"),S=(0,l.M)(c,"unstable_getStaticPaths"),b=(0,l.M)(c,"unstable_getStaticParams"),f=(0,l.M)(c,"unstable_getServerProps"),y=(0,l.M)(c,"unstable_getServerSideProps"),w=new o.PagesRouteModule({definition:{kind:i.A.PAGES,page:"/admin/errors",pathname:"/admin/errors",bundlePath:"",filename:""},components:{App:n.default,Document:a.default},userland:c});s()}catch(e){s(e)}})},4722:e=>{e.exports=require("next-auth/react")},5542:e=>{e.exports=require("next-auth")},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},8942:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>m,getServerSideProps:()=>g});var o=r(8732),i=r(2015),l=r(9733),a=r(2053),n=r(4131),c=r(8079),d=r(5542),x=r(3762),h=r(1011),u=r(3001),p=e([l,n,h,u]);function m(){let[e,t]=(0,i.useState)([]),[r,s]=(0,i.useState)(!0),[d,x]=(0,i.useState)(null),[p,m]=(0,i.useState)("all"),[g,j]=(0,i.useState)("all"),[S,b]=(0,i.useState)(""),[f,y]=(0,i.useState)(new Set),{isOpen:w,onOpen:T,onClose:k}=(0,l.useDisclosure)(),v=(0,l.useToast)(),{currentScheme:C}=(0,u.DP)(),z=(0,l.useColorModeValue)("whiteAlpha.100","gray.800"),M=(0,l.useColorModeValue)("gray.200","gray.600"),I=async()=>{try{s(!0);let e=await fetch("/api/admin/errors");if(e.ok){let r=await e.json();t(r.errors||[])}else throw Error("Failed to fetch errors")}catch(e){v({title:"Error",description:"Failed to fetch error logs",status:"error",duration:5e3})}finally{s(!1)}},B=async()=>{try{if((await fetch("/api/admin/errors",{method:"DELETE"})).ok)t([]),v({title:"Success",description:"All error logs have been cleared",status:"success",duration:3e3});else throw Error("Failed to clear errors")}catch(e){v({title:"Error",description:"Failed to clear error logs",status:"error",duration:5e3})}},P=e=>{let t=new Set(f);t.has(e)?t.delete(e):t.add(e),y(t)},A=e=>{x(e),T()},W=e=>{switch(e){case"error":return"red";case"warn":return"yellow";case"info":return"blue";default:return"gray"}},E=e.filter(e=>{let t="all"===p||e.level===p,r="all"===g||e.source===g,s=""===S||e.message.toLowerCase().includes(S.toLowerCase())||e.source.toLowerCase().includes(S.toLowerCase());return t&&r&&s}),L=[...new Set(e.map(e=>e.source))];return(0,o.jsx)(h.A,{children:(0,o.jsxs)(l.Container,{maxW:"container.xl",py:8,children:[(0,o.jsxs)(l.VStack,{spacing:6,align:"stretch",children:[(0,o.jsxs)(l.HStack,{justify:"space-between",align:"center",children:[(0,o.jsxs)(l.VStack,{align:"start",spacing:1,children:[(0,o.jsxs)(l.Heading,{size:"lg",display:"flex",alignItems:"center",children:[" ",(0,o.jsx)(a.FontAwesomeIcon,{icon:n.faBug,style:{marginRight:"0.5rem",color:"#ff4d4d"}})," ",(0,o.jsxs)(l.Box,{as:"span",bgGradient:"linear(to-r, red.400, orange.400)",bgClip:"text",children:[" ","Error Logs"]})]}),(0,o.jsx)(l.Text,{color:C.colors.textSecondary,fontSize:"sm",children:"Monitor and manage system errors and warnings"})]}),(0,o.jsxs)(l.HStack,{spacing:3,children:[(0,o.jsx)(l.Button,{leftIcon:(0,o.jsx)(c.a4x,{}),variant:"outline",size:"sm",onClick:()=>{let e=JSON.stringify(E,null,2),t="data:application/json;charset=utf-8,"+encodeURIComponent(e),r=`error-logs-${new Date().toISOString().split("T")[0]}.json`,s=document.createElement("a");s.setAttribute("href",t),s.setAttribute("download",r),s.click()},isDisabled:0===E.length,children:"Export"}),(0,o.jsx)(l.Button,{leftIcon:(0,o.jsx)(c.jTZ,{}),colorScheme:"blue",size:"sm",onClick:I,isLoading:r,children:"Refresh"}),(0,o.jsx)(l.Button,{leftIcon:(0,o.jsx)(c.IXo,{}),colorScheme:"red",variant:"outline",size:"sm",onClick:B,isDisabled:0===e.length,children:"Clear All"})]})]}),(0,o.jsxs)(l.Card,{bg:z,borderColor:M,backdropFilter:"blur(10px)",children:[" ",(0,o.jsx)(l.CardBody,{children:(0,o.jsxs)(l.HStack,{spacing:4,wrap:"wrap",children:[(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontSize:"sm",fontWeight:"medium",mb:2,color:C.colors.text,children:"Search"})," ",(0,o.jsx)(l.Input,{placeholder:"Search errors...",value:S,onChange:e=>b(e.target.value),size:"sm",w:"200px"})]}),(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontSize:"sm",fontWeight:"medium",mb:2,color:C.colors.text,children:"Level"})," ",(0,o.jsxs)(l.Select,{value:p,onChange:e=>m(e.target.value),size:"sm",w:"120px",children:[(0,o.jsx)("option",{value:"all",children:"All Levels"}),(0,o.jsx)("option",{value:"error",children:"Error"}),(0,o.jsx)("option",{value:"warn",children:"Warning"}),(0,o.jsx)("option",{value:"info",children:"Info"})]})]}),(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontSize:"sm",fontWeight:"medium",mb:2,color:C.colors.text,children:"Source"})," ",(0,o.jsxs)(l.Select,{value:g,onChange:e=>j(e.target.value),size:"sm",w:"150px",children:[(0,o.jsx)("option",{value:"all",children:"All Sources"}),L.map(e=>(0,o.jsx)("option",{value:e,children:e},e))]})]}),(0,o.jsx)(l.Spacer,{}),(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontSize:"sm",fontWeight:"medium",mb:2,color:C.colors.text,children:"Results"})," ",(0,o.jsxs)(l.Badge,{colorScheme:"blue",fontSize:"sm",px:2,py:1,children:[E.length," of ",e.length]})]})]})})]}),(0,o.jsxs)(l.Card,{bg:z,borderColor:M,backdropFilter:"blur(10px)",children:[" ",(0,o.jsxs)(l.CardHeader,{children:[(0,o.jsx)(l.Heading,{size:"md",color:C.colors.text,children:"Recent Errors"})," "]}),(0,o.jsx)(l.CardBody,{children:r?(0,o.jsx)(l.Flex,{justify:"center",py:8,children:(0,o.jsx)(l.Spinner,{size:"lg",color:C.colors.primary})}):0===E.length?(0,o.jsxs)(l.Alert,{status:"info",children:[(0,o.jsx)(l.AlertIcon,{}),(0,o.jsx)(l.AlertTitle,{children:"No errors found!"}),(0,o.jsx)(l.AlertDescription,{children:0===e.length?"No error logs are currently recorded.":"No errors match your current filters."})]}):(0,o.jsx)(l.Box,{overflowX:"auto",children:(0,o.jsxs)(l.Table,{variant:"simple",size:"sm",children:[(0,o.jsx)(l.Thead,{children:(0,o.jsxs)(l.Tr,{children:[(0,o.jsx)(l.Th,{color:C.colors.textSecondary,children:"Time"})," ",(0,o.jsx)(l.Th,{color:C.colors.textSecondary,children:"Level"})," ",(0,o.jsx)(l.Th,{color:C.colors.textSecondary,children:"Source"})," ",(0,o.jsx)(l.Th,{color:C.colors.textSecondary,children:"Message"})," ",(0,o.jsx)(l.Th,{color:C.colors.textSecondary,children:"Actions"})," "]})}),(0,o.jsx)(l.Tbody,{children:E.map(e=>(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(l.Tr,{children:[(0,o.jsx)(l.Td,{children:(0,o.jsx)(l.Text,{fontSize:"xs",color:C.colors.textSecondary,children:new Date(e.timestamp).toLocaleString()})}),(0,o.jsx)(l.Td,{children:(0,o.jsx)(l.Badge,{colorScheme:W(e.level),size:"sm",children:e.level.toUpperCase()})}),(0,o.jsx)(l.Td,{children:(0,o.jsx)(l.Code,{fontSize:"xs",children:e.source})}),(0,o.jsx)(l.Td,{children:(0,o.jsxs)(l.Text,{fontSize:"sm",noOfLines:2,maxW:"300px",color:C.colors.text,children:[" ",e.message]})}),(0,o.jsx)(l.Td,{children:(0,o.jsxs)(l.HStack,{spacing:1,children:[(0,o.jsx)(l.Tooltip,{label:"View Details",children:(0,o.jsx)(l.IconButton,{"aria-label":"View details",icon:(0,o.jsx)(c.Vap,{}),size:"xs",variant:"ghost",onClick:()=>A(e)})}),(0,o.jsx)(l.Tooltip,{label:f.has(e.id)?"Collapse":"Expand",children:(0,o.jsx)(l.IconButton,{"aria-label":"Toggle expansion",icon:f.has(e.id)?(0,o.jsx)(c.wAb,{}):(0,o.jsx)(c.fK4,{}),size:"xs",variant:"ghost",onClick:()=>P(e.id)})})]})})]},e.id),(0,o.jsx)(l.Tr,{children:(0,o.jsx)(l.Td,{colSpan:5,p:0,children:(0,o.jsx)(l.Collapse,{in:f.has(e.id),animateOpacity:!0,children:(0,o.jsxs)(l.Box,{p:4,bg:(0,l.useColorModeValue)("gray.50","gray.700"),children:[e.stack&&(0,o.jsxs)(l.Box,{mb:3,children:[(0,o.jsx)(l.Text,{fontSize:"sm",fontWeight:"medium",mb:2,color:C.colors.text,children:"Stack Trace:"})," ",(0,o.jsx)(l.Code,{display:"block",whiteSpace:"pre-wrap",fontSize:"xs",p:2,children:e.stack})]}),e.metadata&&Object.keys(e.metadata).length>0&&(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontSize:"sm",fontWeight:"medium",mb:2,color:C.colors.text,children:"Metadata:"})," ",(0,o.jsx)(l.Code,{display:"block",whiteSpace:"pre-wrap",fontSize:"xs",p:2,children:JSON.stringify(e.metadata,null,2)})]})]})})})})]}))})]})})})]})]}),(0,o.jsxs)(l.Modal,{isOpen:w,onClose:k,size:"xl",children:[(0,o.jsx)(l.ModalOverlay,{}),(0,o.jsxs)(l.ModalContent,{bg:(0,l.useColorModeValue)("white","gray.800"),children:[" ",(0,o.jsx)(l.ModalHeader,{children:"Error Details"}),(0,o.jsx)(l.ModalCloseButton,{}),(0,o.jsx)(l.ModalBody,{children:d&&(0,o.jsxs)(l.VStack,{spacing:4,align:"stretch",children:[(0,o.jsxs)(l.HStack,{children:[(0,o.jsx)(l.Badge,{colorScheme:W(d.level),children:d.level.toUpperCase()}),(0,o.jsx)(l.Text,{fontSize:"sm",color:C.colors.textSecondary,children:new Date(d.timestamp).toLocaleString()})]}),(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontWeight:"medium",mb:2,color:C.colors.text,children:"Source:"})," ",(0,o.jsx)(l.Code,{children:d.source})]}),(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontWeight:"medium",mb:2,color:C.colors.text,children:"Message:"})," ",(0,o.jsx)(l.Text,{color:C.colors.text,children:d.message})," "]}),d.stack&&(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontWeight:"medium",mb:2,color:C.colors.text,children:"Stack Trace:"})," ",(0,o.jsx)(l.Code,{display:"block",whiteSpace:"pre-wrap",fontSize:"sm",p:3,children:d.stack})]}),d.metadata&&(0,o.jsxs)(l.Box,{children:[(0,o.jsx)(l.Text,{fontWeight:"medium",mb:2,color:C.colors.text,children:"Additional Data:"})," ",(0,o.jsx)(l.Code,{display:"block",whiteSpace:"pre-wrap",fontSize:"sm",p:3,children:JSON.stringify(d.metadata,null,2)})]})]})}),(0,o.jsx)(l.ModalFooter,{children:(0,o.jsx)(l.Button,{onClick:k,children:"Close"})})]})]})]})})}[l,n,h,u]=p.then?(await p)():p;let g=async e=>{let t=await (0,d.getServerSession)(e.req,e.res,x.N);return t&&t.user?.isAdmin?{props:{}}:{redirect:{destination:"/auth/signin",permanent:!1}}};s()}catch(e){s(e)}})},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[8270,4874,752,6281,5333],()=>r(4550));module.exports=s})();