import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';

const DEVELOPER_ID = '933023999770918932';

interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: any;
}

interface FlowEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle?: string;
  targetHandle?: string;
}

interface AddonStructure {
  name: string;
  description?: string;
  author?: string;
  version?: string;
  nodes: FlowNode[];
  edges: FlowEdge[];
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

function validateFlowConfiguration(nodes: FlowNode[], edges: FlowEdge[]): ValidationResult {
  const errors: string[] = [];
  
  // Check for command nodes without proper configuration
  const commandNodes = nodes.filter(node => node.type === 'command');
  commandNodes.forEach(node => {
    if (!node.data.commandName || node.data.commandName.trim() === '') {
      errors.push(`Command node "${node.data.label || node.id}" is missing a command name`);
    }
    if (!node.data.description || node.data.description.trim() === '') {
      errors.push(`Command node "${node.data.label || node.id}" is missing a description`);
    }
  });
  
  // Check for API request nodes without proper configuration
  const apiRequestNodes = nodes.filter(node => node.type === 'apiRequest');
  apiRequestNodes.forEach(node => {
    if (!node.data.url || node.data.url.trim() === '' || node.data.url === 'https://api.example.com') {
      errors.push(`API Request node "${node.data.label || node.id}" needs a valid URL. Please configure the API endpoint.`);
    }
    // Check for invalid URLs
    if (node.data.url && node.data.url.trim() !== '') {
      try {
        new URL(node.data.url);
      } catch (e) {
        errors.push(`API Request node "${node.data.label || node.id}" has an invalid URL: ${node.data.url}`);
      }
    }
  });
  
  // Check for action nodes without proper configuration
  const actionNodes = nodes.filter(node => node.type === 'action');
  actionNodes.forEach(node => {
    if (!node.data.actionType || node.data.actionType.trim() === '') {
      errors.push(`Action node "${node.data.label || node.id}" is missing an action type`);
    }
    if ((node.data.actionType === 'sendMessage' || node.data.actionType === 'sendEmbed') && 
        (!node.data.message || node.data.message.trim() === '')) {
      errors.push(`Action node "${node.data.label || node.id}" needs a message to send`);
    }
  });
  
  // Check if there are any command nodes (at least one is required)
  if (commandNodes.length === 0) {
    errors.push('Your addon must have at least one command node');
  }
  
  // Check for disconnected nodes (except trigger nodes)
  const connectedNodeIds = new Set<string>();
  edges.forEach(edge => {
    connectedNodeIds.add(edge.source);
    connectedNodeIds.add(edge.target);
  });
  
  const disconnectedNodes = nodes.filter(node => 
    node.type !== 'trigger' && !connectedNodeIds.has(node.id)
  );
  
  if (disconnectedNodes.length > 0) {
    errors.push(`Found ${disconnectedNodes.length} disconnected nodes. All nodes (except triggers) must be connected to the flow.`);
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session || (session.user as any)?.id !== DEVELOPER_ID) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    const { nodes, edges, name, description, author, version } = req.body as AddonStructure;

    // Validate flow before building
    const validationResult = validateFlowConfiguration(nodes, edges);
    if (!validationResult.isValid) {
      return res.status(400).json({ 
        message: 'Flow validation failed', 
        errors: validationResult.errors,
        details: 'Please fix the configuration issues before building'
      });
    }

    // Generate addon code from flow
    const addonCode = generateAddonFromFlow(nodes, edges, name, description, author, version);
    
    // Create addon directory
    const addonName = name.toLowerCase().replace(/[^a-z0-9]/g, '-');
    
    // Find the project root (go up from dashboard to main project)
    const projectRoot = process.cwd().includes('dashboard') 
      ? path.resolve(process.cwd(), '..', '..')
      : process.cwd();
    
    const addonPath = path.join(projectRoot, 'src', 'addons', addonName);
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(addonPath)) {
      fs.mkdirSync(addonPath, { recursive: true });
    }
    
    // Create commands directory
    const commandsPath = path.join(addonPath, 'commands');
    if (!fs.existsSync(commandsPath)) {
      fs.mkdirSync(commandsPath, { recursive: true });
    }
    
    // Write addon files
    fs.writeFileSync(path.join(addonPath, 'index.ts'), addonCode.index);
    fs.writeFileSync(path.join(addonPath, 'config.yml'), addonCode.config);
    
    // Save original flow data for recovery
    const flowData = {
      name: name,
      description: description || 'Generated addon from visual builder',
      author: author || (session.user as any)?.name || 'Addon Builder',
      version: version || '1.0.0',
      nodes: nodes,
      edges: edges,
      metadata: {
        createdAt: new Date().toISOString(),
        builderVersion: '1.0.0',
        originalFlow: true,
        createdBy: (session.user as any)?.email || 'unknown'
      }
    };
    fs.writeFileSync(path.join(addonPath, 'flow.json'), JSON.stringify(flowData, null, 2));
    
    // Write command files
    addonCode.commands.forEach(command => {
      fs.writeFileSync(path.join(commandsPath, `${command.name}.ts`), command.code);
    });
    
    // Write event files if any
    if (addonCode.events.length > 0) {
      const eventsPath = path.join(addonPath, 'events');
      if (!fs.existsSync(eventsPath)) {
        fs.mkdirSync(eventsPath, { recursive: true });
      }
      
      addonCode.events.forEach(event => {
        fs.writeFileSync(path.join(eventsPath, `${event.name}.ts`), event.code);
      });
    }

    // Create addon reload signal
    const reloadSignalPath = path.join(projectRoot, 'src', 'dashboard', 'addon-reload.signal');
    fs.writeFileSync(reloadSignalPath, JSON.stringify({
      requestedBy: (session.user as any)?.email || 'addon-builder',
      timestamp: Date.now(),
      action: 'addon-built',
      addonName: addonName
    }));
    
    // Compile TypeScript to JavaScript in dist/addons
    try {
      const { exec } = require('child_process');
      const util = require('util');
      const execPromise = util.promisify(exec);

      // Run TypeScript compiler for the specific addon
      console.log(`Compiling addon: ${addonName}`);
      const compileCommand = process.platform === 'win32' 
        ? `cd "${projectRoot}" && npx tsc --project tsconfig.json --outDir dist --rootDir src`
        : `cd "${projectRoot}" && npx tsc --project tsconfig.json --outDir dist --rootDir src`;
      
      await execPromise(compileCommand);
      
      // Copy config.yml and flow.json to dist folder
      const distAddonPath = path.join(projectRoot, 'dist', 'addons', addonName);
      if (fs.existsSync(distAddonPath)) {
        // Copy config.yml
        const srcConfig = path.join(addonPath, 'config.yml');
        const distConfig = path.join(distAddonPath, 'config.yml');
        if (fs.existsSync(srcConfig)) {
          fs.copyFileSync(srcConfig, distConfig);
        }
        
        // Copy flow.json
        const srcFlow = path.join(addonPath, 'flow.json');
        const distFlow = path.join(distAddonPath, 'flow.json');
        if (fs.existsSync(srcFlow)) {
          fs.copyFileSync(srcFlow, distFlow);
        }
        
        console.log(`Successfully compiled addon to: ${distAddonPath}`);
      }
      
    } catch (compileError) {
      console.error('Error compiling addon:', compileError);
      // Don't fail the whole process if compilation fails
    }

    // Trigger addon reload
    const signalPath = path.join(projectRoot, 'addon-reload.signal');
    const reloadSignal = {
      timestamp: Date.now(),
      requestedBy: 'addon-builder',
      action: 'addon-built',
      addonName: addonName
    };
    fs.writeFileSync(signalPath, JSON.stringify(reloadSignal, null, 2));

    return res.status(200).json({ 
      message: 'Addon built and compiled successfully',
      addonName,
      files: {
        index: addonCode.index,
        config: addonCode.config,
        commands: addonCode.commands,
        events: addonCode.events
      }
    });
    
  } catch (error) {
    console.error('Error building addon:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
}

function generateAddonFromFlow(nodes: FlowNode[], edges: FlowEdge[], name: string, description?: string, author?: string, version?: string) {
  const commands: Array<{ name: string; code: string }> = [];
  const events: Array<{ name: string; code: string }> = [];
  
  // Find all command nodes
  const commandNodes = nodes.filter(node => node.type === 'command');
  const eventNodes = nodes.filter(node => node.type === 'event');
  
  // Generate commands
  commandNodes.forEach(node => {
    const commandName = node.data.commandName || `command${node.id}`;
    const description = node.data.description || 'Generated command';
    const cooldown = node.data.cooldown || 0;
    
    // Find connected actions
    const connectedActions = findConnectedActions(node.id, nodes, edges);
    
    const commandCode = generateCommandCode(commandName, description, cooldown, connectedActions);
    commands.push({ name: commandName, code: commandCode });
  });
  
  // Generate events
  eventNodes.forEach(node => {
    const eventType = node.data.eventType || 'messageCreate';
    const eventName = `${eventType}Handler`;
    
    // Find connected actions
    const connectedActions = findConnectedActions(node.id, nodes, edges);
    
    const eventCode = generateEventCode(eventType, connectedActions);
    events.push({ name: eventName, code: eventCode });
  });
  
  // Generate index.ts
  const indexCode = generateIndexCode(name, description, author, version, commands, events);
  
  // Generate config.yml
  const configCode = generateConfigCode(name, description, author, version, commands, events);
  
  return {
    index: indexCode,
    config: configCode,
    commands,
    events
  };
}

function findConnectedActions(nodeId: string, nodes: FlowNode[], edges: FlowEdge[]): FlowNode[] {
  const visitedNodes = new Set<string>();
  const flowChain: FlowNode[] = [];
  
  function traverseFlow(currentNodeId: string) {
    if (visitedNodes.has(currentNodeId)) return;
    visitedNodes.add(currentNodeId);
    
    // Find all edges from this node
    const outgoingEdges = edges.filter(edge => edge.source === currentNodeId);
    
    outgoingEdges.forEach(edge => {
      const targetNode = nodes.find(node => node.id === edge.target);
      if (targetNode) {
        // Add this node to the flow chain if it's an action or API request
        if (targetNode.type === 'action' || targetNode.type === 'apiRequest') {
          flowChain.push(targetNode);
        }
        
        // Continue traversing from this node to find the rest of the flow
        traverseFlow(targetNode.id);
      }
    });
  }
  
  // Start traversal from the given node
  traverseFlow(nodeId);
  
  return flowChain;
}

function generateCommandCode(name: string, description: string, cooldown: number, actions: FlowNode[]): string {
  // Separate API requests and actions
  const apiRequests = actions.filter(action => action.type === 'apiRequest');
  const otherActions = actions.filter(action => action.type !== 'apiRequest');
  
  // Generate API request code (these run first)
  const apiRequestCode = apiRequests.map(action => generateApiRequestCode(action)).join('\n\n');
  
  // Generate action code (these run after API requests)
  const actionCode = otherActions.map(action => {
    switch (action.data.actionType) {
      case 'sendMessage':
        return `    // Action: Send Message
    await interaction.reply(interpolateVariables('${action.data.message || 'Hello!'}', context));`;
      case 'sendEmbed':
        return `    // Action: Send Embed
    try {
      const response = (context as any).response;
      
      if (response && response.results && response.results.length > 0) {
        const item = response.results[0];
        
        const embed = new EmbedBuilder()
          .setTitle('🤗 ${action.data.title || 'Hug!'}')
          .setDescription(\`\${interaction.user.username} sends a warm hug! 💕\`)
          .setColor('#FF69B4');
        
        // Add image if available
        if (item.url) {
          embed.setImage(item.url);
        }
        
        // Add footer with source info
        if (item.anime_name) {
          embed.setFooter({ text: \`From: \${item.anime_name}\` });
        }
        
        await interaction.reply({ embeds: [embed] });
      } else {
        // Fallback if no API data available
        const fallbackMessage = interpolateVariables('${action.data.message || '🤗 *sends a virtual hug* 💕'}', context);
        
        const embed = new EmbedBuilder()
          .setTitle('${action.data.title || 'Message'}')
          .setDescription(fallbackMessage)
          .setColor(0x00ff00);
        
        await interaction.reply({ embeds: [embed] });
      }
    } catch (error) {
      console.error('Error creating embed:', error);
      await interaction.reply({ content: '🤗 *sends a virtual hug* 💕', ephemeral: true });
    }`;
      default:
        return `    // Action: ${action.data.actionType}
    await interaction.reply('Action: ${action.data.actionType}');`;
    }
  }).join('\n\n');

  // Combine all code in proper execution order
  const allActionCode = [apiRequestCode, actionCode].filter(code => code.trim()).join('\n\n');

  return `import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\\{([^}]+)\\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const data = new SlashCommandBuilder()
  .setName('${name}')
  .setDescription('${description}');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    // Create context with Discord data and API responses
    const context = {
      user: {
        id: interaction.user.id,
        username: interaction.user.username,
        displayName: interaction.user.displayName,
        tag: interaction.user.tag,
      },
      channel: {
        id: interaction.channel?.id,
        name: interaction.channel?.type === 0 ? interaction.channel.name : 'dm',
      },
      guild: {
        id: interaction.guild?.id,
        name: interaction.guild?.name,
      },
      // API response data will be added here by API request blocks
    };

${allActionCode || '    await interaction.reply("Command executed successfully!");'}
  } catch (error) {
    console.error('Error executing ${name} command:', error);
    await interaction.reply({ content: 'An error occurred while executing this command.', ephemeral: true });
  }
}

export const cooldown = ${cooldown * 1000};`;
}

function generateApiRequestCode(node: FlowNode): string {
  const data = node.data;
  const method = data.method || 'GET';
  const url = data.url;
  const timeout = data.timeout || 5000;
  const headers = data.headers || [];
  const body = data.body || '';
  const bodyType = data.bodyType || 'json';
  const saveToVariable = data.saveToVariable || 'response';
  const errorHandling = data.errorHandling || 'log';

  // If no URL is configured, generate a placeholder that explains the issue
  if (!url || url.trim() === '' || url === 'https://api.example.com') {
    return `    // API Request: ${method} (URL not configured)
    console.warn('API Request node is not properly configured. Please set a valid URL in the visual builder.');
    
    // Placeholder response for unconfigured API request
    const ${saveToVariable}Data = {
      error: 'API endpoint not configured',
      message: 'Please configure the API request node in the visual builder',
      configured: false
    };
    
    // Store placeholder response in context
    (context as any).${saveToVariable} = ${saveToVariable}Data;`;
  }

  // Generate headers object
  const headersCode = headers.length > 0 
    ? `      headers: {
${headers.map((header: any) => `        '${header.key}': interpolateVariables('${header.value}', context),`).join('\n')}
      },`
    : '';

  // Generate body code
  let bodyCode = '';
  if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    if (bodyType === 'json') {
      bodyCode = `      body: JSON.stringify(JSON.parse(interpolateVariables('${body}', context))),`;
    } else if (bodyType === 'form') {
      bodyCode = `      body: new URLSearchParams(interpolateVariables('${body}', context)),`;
    } else {
      bodyCode = `      body: interpolateVariables('${body}', context),`;
    }
  }

  // Generate error handling code
  const errorHandlingCode = errorHandling === 'ignore' 
    ? '// Errors ignored'
    : errorHandling === 'log' 
    ? `console.error('API request failed:', error);`
    : `throw error;`;

  return `    // API Request: ${method} ${url}
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), ${timeout});
      
      const ${saveToVariable}Response = await fetch(interpolateVariables('${url}', context), {
        method: '${method}',
${headersCode}
${bodyCode}
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!${saveToVariable}Response.ok) {
        throw new Error(\`HTTP error! status: \${${saveToVariable}Response.status}\`);
      }
      
      const ${saveToVariable}Data = await ${saveToVariable}Response.json();
      console.log('API Response:', ${saveToVariable}Data);
      
      // Store response in context for use in other blocks
      (context as any).${saveToVariable} = ${saveToVariable}Data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('API request timed out after ${timeout}ms');
      } else {
        ${errorHandlingCode}
      }
    }`;
}

function generateEventCode(eventType: string, actions: FlowNode[]): string {
  const actionCode = actions.map(action => {
    if (action.type === 'apiRequest') {
      return generateApiRequestCode(action);
    } else {
      switch (action.data.actionType) {
        case 'sendMessage':
          return `    // Send message action
    const channel = bot.client.channels.cache.get(interpolateVariables('${action.data.channel || 'CHANNEL_ID'}', context));
    if (channel && channel.isTextBased()) {
      await channel.send(interpolateVariables('${action.data.message || 'Hello!'}', context));
    }`;
        default:
          return `    // Action: ${action.data.actionType}
    console.log('Executing action:', '${action.data.actionType}');`;
      }
    }
  }).join('\n');

  return `import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\\{([^}]+)\\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const eventName = '${eventType}';

export async function execute(bot: BotInstance, ...args: any[]) {
  try {
    // Create context with event data and API responses
    const context = {
      event: {
        type: '${eventType}',
        args: args,
      },
      // API response data will be added here by API request blocks
    };

${actionCode || '    console.log("Event triggered:", eventName);'}
  } catch (error) {
    console.error('Error in event handler:', error);
  }
}`;
}

function generateIndexCode(name: string, description: string | undefined, author: string | undefined, version: string | undefined, commands: Array<{ name: string; code: string }>, events: Array<{ name: string; code: string }>): string {
  return `import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Dynamically load commands from commands folder
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: '${name}',
    version: '${version || '1.0.0'}',
    description: '${description || 'Generated addon from visual builder'}',
    author: '${author || 'Addon Builder'}'
  },

  commands: await loadCommands(),

  events: [],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger('${name}');
    logger.info(\`Loading ${name} with \${addon.commands?.length || 0} commands...\`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger('${name}');
    logger.info(\`Unloading ${name}...\`);
  }
};

export default addon;`;
}

function generateConfigCode(name: string, description: string | undefined, author: string | undefined, version: string | undefined, commands: Array<{ name: string; code: string }>, events: Array<{ name: string; code: string }>): string {
  return `name: "${name}"
version: "${version || '1.0.0'}"
description: "${description || 'Generated addon from visual builder'}"
author: "${author || 'Addon Builder'}"
main: "index.js"
dependencies: []
commands:
${commands.map(cmd => `  - ${cmd.name}`).join('\n')}
events:
${events.map(evt => `  - ${evt.name}`).join('\n')}`;
} 