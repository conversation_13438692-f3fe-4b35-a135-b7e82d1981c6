import { NextApiRequest, NextApiResponse } from 'next';
import fs from 'fs/promises';
import path from 'path';
import { getServerSession } from 'next-auth';
import { authOptions } from '../../../auth/[...nextauth]';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    // Check authentication
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { name } = req.query;
    if (!name || typeof name !== 'string') {
      return res.status(400).json({ error: 'Invalid addon name' });
    }

    // Next.js API routes run from the dashboard directory. We need to find the addons directory.
    // The dashboard is at src/dashboard, so we need to go up to the project root first.
    const projectRoot = process.cwd().includes('dashboard') 
      ? path.resolve(process.cwd(), '..', '..')
      : process.cwd();
    
    console.log('Process cwd:', process.cwd());
    console.log('Project root:', projectRoot);
    
    const possibleAddonsPaths = [
      // Check src/addons first
      path.join(projectRoot, 'src', 'addons'),
      path.join(projectRoot, '404-bot', 'src', 'addons'),
      // Then check dist/addons
      path.join(projectRoot, 'dist', 'addons'),
      path.join(projectRoot, '404-bot', 'dist', 'addons'),
    ];
    
    console.log('Checking these addon paths:', possibleAddonsPaths);
    
    let addonsPath = '';
    for (const possiblePath of possibleAddonsPaths) {
      try {
        await fs.access(possiblePath);
        addonsPath = possiblePath;
        break;
      } catch {
        continue;
      }
    }
    
    if (!addonsPath) {
      return res.status(500).json({ 
        error: 'Addons directory not found',
        checkedPaths: possibleAddonsPaths
      });
    }
    
    console.log('Found addons directory at:', addonsPath);
    
    const addonPath = path.join(addonsPath, name);
    console.log('Full addon path:', addonPath);
    
    // Log whether this is a src or dist path
    if (addonsPath.includes('src')) {
      console.log('Using source (src/addons) directory');
    } else if (addonsPath.includes('dist')) {
      console.log('Using compiled (dist/addons) directory');
    }
    
    // Look for config files in both the addon root and example directories
    const possibleConfigPaths = [
      path.join(addonPath, 'config.yml'),
      path.join(addonPath, 'config.example.yml'),
      path.join(addonPath, 'example', 'config.yml'),
      path.join(addonPath, 'example', 'config.example.yml')
    ];
    
    console.log('Checking config paths:', possibleConfigPaths);

    if (req.method === 'GET') {
      // First check if the addon directory exists
      try {
        await fs.access(addonPath);
      } catch {
        return res.status(404).json({ 
          error: `Addon directory not found: ${name}`,
          path: addonPath
        });
      }

      // Try each possible config path in order
      for (const configPath of possibleConfigPaths) {
        try {
          const configContent = await fs.readFile(configPath, 'utf-8');
          return res.status(200).json({ 
            config: configContent,
            path: configPath // Include path for debugging
          });
        } catch (error) {
          // Continue to next path
          continue;
        }
      }

      // If we get here, no config file was found
      return res.status(404).json({ 
        error: 'Configuration not found',
        checkedPaths: possibleConfigPaths,
        addonPath
      });
    }

    if (req.method === 'POST') {
      const { config } = req.body;
      if (!config) {
        return res.status(400).json({ error: 'No configuration provided' });
      }

      // Ensure the addon directory exists
      try {
        await fs.access(addonPath);
      } catch {
        return res.status(404).json({ 
          error: `Addon directory not found: ${name}`,
          path: addonPath
        });
      }

      // Write to both src/addons and dist/addons directories
      const srcConfigPath = possibleConfigPaths[0]; // src/addons/{name}/config.yml
      const distAddonsPath = path.join(projectRoot, 'dist', 'addons');
      const distConfigPath = path.join(distAddonsPath, name, 'config.yml');
      
      const pathsToWrite = [srcConfigPath];
      
      // Check if dist directory exists and add it to paths
      try {
        await fs.access(distAddonsPath);
        const distAddonPath = path.join(distAddonsPath, name);
        try {
          await fs.access(distAddonPath);
          pathsToWrite.push(distConfigPath);
        } catch {
          // Create dist addon directory if it doesn't exist
          await fs.mkdir(distAddonPath, { recursive: true });
          pathsToWrite.push(distConfigPath);
        }
      } catch {
        // dist/addons doesn't exist, only write to src
        console.log('dist/addons directory not found, writing only to src');
      }
      
      const results = [];
      let hasError = false;
      
      // Write to all paths
      for (const configPath of pathsToWrite) {
        try {
          await fs.writeFile(configPath, config, 'utf-8');
          results.push({ path: configPath, success: true });
        } catch (error: any) {
          results.push({ path: configPath, success: false, error: error.message });
          hasError = true;
        }
      }
      
      if (hasError) {
        return res.status(500).json({
          error: 'Failed to save configuration to some locations',
          results
        });
      }
      
      return res.status(200).json({ 
        message: 'Configuration saved successfully to all locations',
        results
      });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error: any) {
    console.error('Error handling addon config:', error);
    return res.status(500).json({ 
      error: error.message || 'Internal server error',
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
} 