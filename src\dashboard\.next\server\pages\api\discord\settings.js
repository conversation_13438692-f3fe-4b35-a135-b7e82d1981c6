"use strict";(()=>{var e={};e.id=6223,e.ids=[6223],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2295:(e,t,o)=>{o.r(t),o.d(t,{config:()=>h,default:()=>y,routeModule:()=>S});var s={};o.r(s),o.d(s,{default:()=>g});var n=o(3433),r=o(264),i=o(584),a=o(5806),d=o(8525),l=o(2115),c=o.n(l),u=o(9021),f=o.n(u),m=o(3873),p=o.n(m);let b={};try{let{dashboardConfig:e}=o(8580);b=e??{}}catch{}async function g(e,t){if(!await (0,a.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});if("GET"===e.method)try{let e={prefix:b.bot?.prefix||"!",presence:{status:b.bot?.presence?.status||"online",activityType:b.bot?.presence?.activity?.type||"PLAYING",activityName:b.bot?.presence?.activity?.name||""},welcomeChannel:"",moderationChannel:"",logChannel:"",autoRole:"",enableWelcome:!1,enableModLog:!1,enableAutoRole:!1};return t.status(200).json(e)}catch(e){return t.status(500).json({error:"Failed to fetch settings"})}if("POST"===e.method)try{let{prefix:o,presence:s}=e.body;if(o&&("string"!=typeof o||0===o.length))return t.status(400).json({error:"Invalid prefix"});let n=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>p().resolve(process.cwd(),e)).find(e=>f().existsSync(e));if(n||(n=p().resolve(__dirname,"../../../../config.yml")),!f().existsSync(n))return t.status(500).json({error:"config.yml not found on server"});try{let e=f().readFileSync(n,"utf8"),r=c().parse(e);return o&&(r.bot.prefix=o),s&&(r.bot.presence={status:s.status||"online",activity:{type:s.activityType||"PLAYING",name:s.activityName||""}}),f().writeFileSync(n,c().stringify(r)),t.status(200).json({message:"Settings updated successfully"})}catch(e){return t.status(500).json({error:"Failed to update settings"})}}catch(e){return t.status(500).json({error:"Failed to update settings"})}return t.status(405).json({error:"Method not allowed"})}let y=(0,i.M)(s,"default"),h=(0,i.M)(s,"config"),S=new n.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/discord/settings",pathname:"/api/discord/settings",bundlePath:"",filename:""},userland:s})},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var s=o(5542),n=o.n(s);let r=require("next-auth/providers/discord");var i=o.n(r),a=o(8580);let d={providers:[i()({clientId:a.dashboardConfig.bot.clientId,clientSecret:a.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,s=t.accessToken||null;e.user.id=o,e.user.accessToken=s;let n=!1;if(o)if((a.dashboardConfig.dashboard.admins||[]).includes(o))n=!0;else{let e=a.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&a.dashboardConfig.bot.token&&a.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${a.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${a.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();n=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=n,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),s=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:a.dashboardConfig.dashboard.session.secret||a.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=n()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var s=o(9021),n=o(2115),r=o.n(n),i=o(3873);let a={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");a=r().parse(t)}catch(e){process.exit(1)}let d={bot:{token:a.bot.token,clientId:a.bot.clientId,clientSecret:a.bot.clientSecret,guildId:a.bot.guildId,ticketCategoryId:a.bot.ticketCategoryId||null,ticketLogChannelId:a.bot.ticketLogChannelId||null,prefix:a.bot.prefix},dashboard:{admins:a.dashboard?.admins||[],adminRoleIds:a.dashboard?.adminRoleIds||[],session:{secret:a.dashboard?.session?.secret||a.bot.clientSecret}},database:{url:a.database.url,name:a.database.name,options:{maxPoolSize:a.database.options?.maxPoolSize||10,minPoolSize:a.database.options?.minPoolSize||1,maxIdleTimeMS:a.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:a.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:a.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:a.database.options?.connectTimeoutMS||1e4,retryWrites:a.database.options?.retryWrites!==!1,retryReads:a.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=2295);module.exports=o})();