{"version": 1, "files": ["../../../../../webpack-api-runtime.js", "../../../../../../../../../package.json", "../../../../../../../../../config.yml", "../../../../../../../../addons/example/config.yml", "../../../../../../../../addons/example/commands/ping.ts", "../../../../../../../../addons/moderation/commands/kick.ts", "../../../../../../../../addons/example/index.ts", "../../../../../../../../addons/moderation/commands/purge.ts", "../../../../../../../../addons/moderation/commands/lockdown.ts", "../../../../../../../../addons/moderation/commands/slowmode.ts", "../../../../../../../../addons/moderation/commands/timeout.ts", "../../../../../../../../addons/moderation/commands/untimeout.ts", "../../../../../../../../addons/moderation/commands/ban.ts", "../../../../../../../../addons/moderation/commands/warnings.ts", "../../../../../../../../addons/moderation/commands/warn.ts", "../../../../../../../../addons/moderation/config.yml", "../../../../../../../../addons/server-hub/index.ts", "../../../../../../../../addons/tickets/config.yml", "../../../../../../../../addons/server-hub/config.yml", "../../../../../../../../addons/tickets/index WORKING.ts", "../../../../../../../../addons/uwu-hug/commands/hug.ts", "../../../../../../../../addons/tickets/index.ts", "../../../../../../../../addons/moderation/index.ts", "../../../../../../../../addons/uwu-hug/config.yml", "../../../../../../../../addons/uwu-hug/flow.json", "../../../../../../../../addons/uwu-hug/index.ts", "../../../../../../../../addons/voice-mistress/events/buttonHandler.ts", "../../../../../../../../addons/voice-mistress/index.ts", "../../../../../../../../addons/voice-mistress/utils/channelUtils.ts", "../../../../../../../../addons/welcome-goodbye/config.yml", "../../../../../../../../addons/welcome-goodbye/index.ts", "../../../../../../../../addons/voice-mistress/config.yml", "../../../../../../../../addons/welcome-goodbye/types.ts", "../../../../../../../../addons/welcome-goodbye/utils/numberUtils.ts"]}