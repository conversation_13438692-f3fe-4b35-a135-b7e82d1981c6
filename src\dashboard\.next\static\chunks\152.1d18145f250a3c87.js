"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[152],{10152:(e,s,i)=>{i.r(s),i.d(s,{default:()=>I});var r=i(94513),o=i(22907),n=i(9557),l=i(7680),t=i(52922),a=i(47847),c=i(59365),E=i(85104),d=i(79156),h=i(40443),m=i(63730),A=i(64057),S=i(78902),p=i(22237),_=i(71185),N=i(41611),x=i(51961),u=i(49217),j=i(25680),M=i(28245),C=i(62690),R=i(94285),g=i(97146);let f={General:{icon:g.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:g.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","MANAGE_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:g.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:g.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function I(e){let{isOpen:s,onClose:i,onSuccess:g}=e,I=(0,o.d)(),[T,O]=(0,R.useState)(!1),[b,k]=(0,R.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),y=async()=>{if(!b.name.trim())return void I({title:"Error",description:"Role name is required",status:"error",duration:3e3});O(!0);try{let e=await fetch("/api/discord/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(b)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create role")}I({title:"Success",description:"Role created successfully",status:"success",duration:3e3}),k({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),g(),i()}catch(e){I({title:"Error",description:e.message||"Failed to create role",status:"error",duration:5e3})}finally{O(!1)}},B=e=>{k(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},G=(e,s)=>{k(i=>({...i,[e]:s}))};return(0,r.jsxs)(n.aF,{isOpen:s,onClose:i,size:"xl",scrollBehavior:"inside",children:[(0,r.jsx)(l.m,{backdropFilter:"blur(10px)"}),(0,r.jsxs)(t.$,{bg:"gray.800",children:[(0,r.jsx)(a.r,{children:"Create New Role"}),(0,r.jsx)(c.s,{}),(0,r.jsx)(E.c,{children:(0,r.jsxs)(d.T,{spacing:6,children:[(0,r.jsxs)(h.MJ,{isRequired:!0,children:[(0,r.jsx)(m.l,{children:"Role Name"}),(0,r.jsx)(A.p,{placeholder:"Enter role name",value:b.name,onChange:e=>G("name",e.target.value)})]}),(0,r.jsxs)(h.MJ,{children:[(0,r.jsx)(m.l,{children:"Role Color"}),(0,r.jsx)(A.p,{type:"color",value:b.color,onChange:e=>G("color",e.target.value)})]}),(0,r.jsx)(h.MJ,{children:(0,r.jsxs)(S.z,{spacing:4,children:[(0,r.jsx)(p.S,{isChecked:b.hoist,onChange:e=>G("hoist",e.target.checked),children:"Display role separately"}),(0,r.jsx)(p.S,{isChecked:b.mentionable,onChange:e=>G("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,r.jsx)(_.c,{}),(0,r.jsx)(N.E,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(f).map(e=>{let[s,i]=e;return(0,r.jsxs)(x.a,{w:"full",children:[(0,r.jsxs)(S.z,{mb:2,children:[(0,r.jsx)(u.I,{as:i.icon}),(0,r.jsx)(N.E,{fontWeight:"semibold",children:s})]}),(0,r.jsx)(j.r,{columns:2,spacing:2,children:i.permissions.map(e=>(0,r.jsx)(p.S,{isChecked:b.permissions.includes(e),onChange:()=>B(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},s)})]})}),(0,r.jsxs)(M.j,{children:[(0,r.jsx)(C.$,{variant:"ghost",mr:3,onClick:i,children:"Cancel"}),(0,r.jsx)(C.$,{colorScheme:"blue",onClick:y,isLoading:T,children:"Create Role"})]})]})]})}},28245:(e,s,i)=>{i.d(s,{j:()=>c});var r=i(94513),o=i(55100),n=i(22697),l=i(9557),t=i(2923),a=i(33225);let c=(0,t.R)((e,s)=>{let{className:i,...t}=e,c=(0,n.cx)("chakra-modal__footer",i),E=(0,l.x5)(),d=(0,o.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...E.footer});return(0,r.jsx)(a.B.footer,{ref:s,...t,__css:d,className:c})});c.displayName="ModalFooter"},59365:(e,s,i)=>{i.d(s,{s:()=>a});var r=i(94513),o=i(22697),n=i(50614),l=i(9557),t=i(33021);let a=(0,i(2923).R)((e,s)=>{let{onClick:i,className:a,...c}=e,{onClose:E}=(0,l.k3)(),d=(0,o.cx)("chakra-modal__close-btn",a),h=(0,l.x5)();return(0,r.jsx)(t.J,{ref:s,__css:h.closeButton,className:d,onClick:(0,n.H)(i,e=>{e.stopPropagation(),E()}),...c})});a.displayName="ModalCloseButton"}}]);