// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import YAML from 'yaml';
import fs from 'fs';
import path from 'path';

// Load config
let dashboardConfig: any = {};
try {
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  const { dashboardConfig: config } = require('../../../core/config');
  dashboardConfig = config ?? {};
} catch {
  // config.yml not present
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Handle GET request - Return current settings
  if (req.method === 'GET') {
    try {
      // In a real implementation, these would come from your database
      // For now, we'll return default values based on config.yml
      const settings = {
        prefix: dashboardConfig.bot?.prefix || '!',
        presence: {
          status: dashboardConfig.bot?.presence?.status || 'online',
          activityType: dashboardConfig.bot?.presence?.activity?.type || 'PLAYING',
          activityName: dashboardConfig.bot?.presence?.activity?.name || '',
        },
        welcomeChannel: '',  // placeholder for future db values
        moderationChannel: '',
        logChannel: '',
        autoRole: '',
        enableWelcome: false,
        enableModLog: false,
        enableAutoRole: false,
      };

      return res.status(200).json(settings);
    } catch (error) {
      console.error('Error fetching settings:', error);
      return res.status(500).json({ error: 'Failed to fetch settings' });
    }
  }

  // Handle POST request - Update settings
  if (req.method === 'POST') {
    try {
      const {
        prefix,
        presence,
      } = req.body;

      // Validate prefix
      if (prefix && (typeof prefix !== 'string' || prefix.length === 0)) {
        return res.status(400).json({ error: 'Invalid prefix' });
      }

      // Locate config.yml (reuse logic from core/config)
      const attempts = [
        'config.yml',
        '../config.yml',
        '../../config.yml',
        '../../../config.yml',
        '../../../../config.yml',
      ].map(rel => path.resolve(process.cwd(), rel));

      let configPath = attempts.find(p => fs.existsSync(p));
      if (!configPath) configPath = path.resolve(__dirname, '../../../../config.yml');

      if (!fs.existsSync(configPath)) {
        return res.status(500).json({ error: 'config.yml not found on server' });
      }

      try {
        const configRaw = fs.readFileSync(configPath, 'utf8');
        const configYaml: any = YAML.parse(configRaw);

        if (prefix) configYaml.bot.prefix = prefix;

        if (presence) {
          configYaml.bot.presence = {
            status: presence.status || 'online',
            activity: {
              type: presence.activityType || 'PLAYING',
              name: presence.activityName || '',
            },
          };
        }

        fs.writeFileSync(configPath, YAML.stringify(configYaml));

        return res.status(200).json({ message: 'Settings updated successfully' });
      } catch (err) {
        console.error('Failed to update config.yml:', err);
        return res.status(500).json({ error: 'Failed to update settings' });
      }
    } catch (error) {
      console.error('Error updating settings:', error);
      return res.status(500).json({ error: 'Failed to update settings' });
    }
  }

  // Handle unsupported methods
  return res.status(405).json({ error: 'Method not allowed' });
} 