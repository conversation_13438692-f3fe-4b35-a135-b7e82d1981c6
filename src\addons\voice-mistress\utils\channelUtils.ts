import { VoiceChannel, ChannelType, PermissionFlagsBits, MessageFlags } from 'discord.js';

export interface TempChannelData {
  channelId: string;
  ownerId: string;
  guildId: string;
  settings: {
    locked: boolean;
    hidden: boolean;
    password?: string;
    blocked: {
      view: string[]; // User IDs blocked from viewing
      join: string[]; // User IDs blocked from joining
    };
    allowed: string[]; // User IDs explicitly allowed (overrides blocks)
    userLimit: number;
    bitrate: number;
    nsfw: boolean;
  };
  createdAt: Date;
  lastActivity: Date;
}

export interface WaitingRoomData {
  waitingRoomId: string;
  mainChannelId: string;
  waitingUserId: string;
  createdAt: Date;
}

export interface PasswordCacheEntry {
  password: string;
  timestamp: Date;
}

export interface KickProtectionEntry {
  kickedAt: Date;
  expiresAt: Date;
}

export class ChannelUtils {
  private static tempChannels = new Map<string, TempChannelData>();
  private static waitingRooms = new Map<string, WaitingRoomData>();
  private static authorizedJoins = new Map<string, Set<string>>(); // channelId -> Set of userIds
  private static passwordCache = new Map<string, Map<string, PasswordCacheEntry>>(); // channelId -> userId -> PasswordCacheEntry
  private static kickProtection = new Map<string, Map<string, KickProtectionEntry>>(); // channelId -> userId -> KickProtectionEntry

  static addTempChannel(channelId: string, ownerId: string, guildId: string): void {
    this.tempChannels.set(channelId, {
      channelId,
      ownerId,
      guildId,
      settings: {
        locked: false,
        hidden: false,
        blocked: {
          view: [],
          join: []
        },
        allowed: [],
        userLimit: 0,
        bitrate: 64000,
        nsfw: false
      },
      createdAt: new Date(),
      lastActivity: new Date()
    });
  }

  static removeTempChannel(channelId: string): void {
    this.tempChannels.delete(channelId);
  }

  static getTempChannel(channelId: string): TempChannelData | undefined {
    return this.tempChannels.get(channelId);
  }

  static isTempChannel(channelId: string): boolean {
    return this.tempChannels.has(channelId);
  }

  static isChannelOwner(channelId: string, userId: string): boolean {
    const tempChannel = this.tempChannels.get(channelId);
    return tempChannel?.ownerId === userId;
  }

  static async toggleLock(channel: VoiceChannel): Promise<boolean> {
    try {
      const tempChannel = this.tempChannels.get(channel.id);
      if (!tempChannel) return false;

      const newLockState = !tempChannel.settings.locked;
      
      // Get roles to update (everyone + any configured roles)
      const rolesToUpdate = [channel.guild.roles.everyone];
      
      for (const role of rolesToUpdate) {
        if (newLockState) {
          // Locking: Deny Connect but keep ViewChannel
          await channel.permissionOverwrites.edit(role, {
            Connect: false,
            ViewChannel: true
          });
        } else {
          // Unlocking: Reset Connect to default but handle individually blocked users
          await channel.permissionOverwrites.edit(role, {
            Connect: null,
            ViewChannel: true
          });
        }
      }

      // Ensure owner always has access
      const owner = await channel.guild.members.fetch(tempChannel.ownerId).catch(() => null);
      if (owner) {
        await channel.permissionOverwrites.edit(owner, {
          Connect: true,
          ViewChannel: true,
          ManageChannels: true,
          MoveMembers: true,
          MuteMembers: true,
          DeafenMembers: true
        });
      }

      // When unlocking, handle individually blocked users
      if (!newLockState) {
        const joinBlockedUsers = tempChannel.settings.blocked.join;
        for (const userId of joinBlockedUsers) {
          const member = await channel.guild.members.fetch(userId).catch(() => null);
          if (member) {
            // Keep them blocked from joining even when channel is unlocked
            await channel.permissionOverwrites.edit(member, {
              Connect: false
            });
          }
        }
      }

      tempChannel.settings.locked = newLockState;
      tempChannel.lastActivity = new Date();
      
      return true;
    } catch (error) {
      console.error('Failed to toggle lock:', error);
      return false;
    }
  }

  static async toggleHide(channel: VoiceChannel): Promise<boolean> {
    try {
      const tempChannel = this.tempChannels.get(channel.id);
      if (!tempChannel) return false;

      const newHiddenState = !tempChannel.settings.hidden;
      
      // Apply permission-based hiding for @everyone
      await channel.permissionOverwrites.edit(channel.guild.roles.everyone, {
        ViewChannel: newHiddenState ? false : true
      });

      // Ensure owner can always see the channel
      const owner = await channel.guild.members.fetch(tempChannel.ownerId).catch(() => null);
      if (owner) {
        await channel.permissionOverwrites.edit(owner, {
          ViewChannel: true,
          Connect: true,
          ManageChannels: true,
          MoveMembers: true,
          MuteMembers: true,
          DeafenMembers: true
        });
      }

      // Handle individually blocked view users
      const viewBlockedUsers = tempChannel.settings.blocked.view;
      for (const userId of viewBlockedUsers) {
        const member = await channel.guild.members.fetch(userId).catch(() => null);
        if (member) {
          await channel.permissionOverwrites.edit(member, {
            ViewChannel: false
          });
        }
      }

      // Handle explicitly allowed users
      for (const userId of tempChannel.settings.allowed) {
        const member = await channel.guild.members.fetch(userId).catch(() => null);
        if (member) {
          await channel.permissionOverwrites.edit(member, {
            ViewChannel: true,
            Connect: true
          });
        }
      }

      tempChannel.settings.hidden = newHiddenState;
      tempChannel.lastActivity = new Date();
      
      return true;
    } catch (error) {
      console.error('Failed to toggle hide:', error);
      return false;
    }
  }

  static async blockUser(channel: VoiceChannel, userId: string, blockType: 'view' | 'join' | 'both'): Promise<boolean> {
    try {
      const tempChannel = this.tempChannels.get(channel.id);
      if (!tempChannel) return false;

      // Don't block the owner
      if (userId === tempChannel.ownerId) return false;

      const member = await channel.guild.members.fetch(userId).catch(() => null);
      if (!member) return false;

      // Remove from allowed list if present
      tempChannel.settings.allowed = tempChannel.settings.allowed.filter(id => id !== userId);

      if (blockType === 'view' || blockType === 'both') {
        if (!tempChannel.settings.blocked.view.includes(userId)) {
          tempChannel.settings.blocked.view.push(userId);
        }
        await channel.permissionOverwrites.edit(member, {
          ViewChannel: false
        });

        // If they can't view, they also can't join
        if (!tempChannel.settings.blocked.join.includes(userId)) {
          tempChannel.settings.blocked.join.push(userId);
        }
      }

      if (blockType === 'join') {
        if (!tempChannel.settings.blocked.join.includes(userId)) {
          tempChannel.settings.blocked.join.push(userId);
        }
        await channel.permissionOverwrites.edit(member, {
          Connect: false
        });
      }

      // Kick the user if they're in the channel
      if (member.voice.channelId === channel.id) {
        await member.voice.disconnect('Blocked from channel');
      }

      tempChannel.lastActivity = new Date();
      return true;
    } catch (error) {
      console.error('Failed to block user:', error);
      return false;
    }
  }

  static async unblockUser(channel: VoiceChannel, userId: string, unblockType: 'view' | 'join' | 'both'): Promise<boolean> {
    try {
      const tempChannel = this.tempChannels.get(channel.id);
      if (!tempChannel) return false;

      const member = await channel.guild.members.fetch(userId).catch(() => null);
      if (!member) return false;

      if (unblockType === 'view' || unblockType === 'both') {
        tempChannel.settings.blocked.view = tempChannel.settings.blocked.view.filter(id => id !== userId);
      }

      if (unblockType === 'join' || unblockType === 'both') {
        tempChannel.settings.blocked.join = tempChannel.settings.blocked.join.filter(id => id !== userId);
      }

      // Reset their permissions based on current channel state
      const permissions: any = {};

      // Handle view permission
      if (!tempChannel.settings.blocked.view.includes(userId)) {
        permissions.ViewChannel = tempChannel.settings.hidden ? null : true;
      }

      // Handle connect permission
      if (!tempChannel.settings.blocked.join.includes(userId)) {
        permissions.Connect = tempChannel.settings.locked ? null : true;
      }

      // If user has no blocks left, reset to default permissions
      const hasAnyBlocks = tempChannel.settings.blocked.view.includes(userId) || 
                          tempChannel.settings.blocked.join.includes(userId);
      
      if (!hasAnyBlocks) {
        // Check if we can remove the permission overwrite entirely
        const currentOverwrite = channel.permissionOverwrites.cache.get(userId);
        if (currentOverwrite) {
          const allowArray = currentOverwrite.allow.toArray();
          const denyArray = currentOverwrite.deny.toArray();
          
          // If removing blocks leaves no custom permissions, delete the overwrite
          if (allowArray.length === 0 && denyArray.length <= 2) { // ViewChannel and Connect
            await channel.permissionOverwrites.delete(member);
          } else {
            await channel.permissionOverwrites.edit(member, permissions);
          }
        }
      } else {
        await channel.permissionOverwrites.edit(member, permissions);
      }

      tempChannel.lastActivity = new Date();
      return true;
    } catch (error) {
      console.error('Failed to unblock user:', error);
      return false;
    }
  }

  static async allowUser(channel: VoiceChannel, userId: string): Promise<boolean> {
    try {
      const tempChannel = this.tempChannels.get(channel.id);
      if (!tempChannel) return false;

      if (userId === tempChannel.ownerId) return true; // Owner is always allowed

      const member = await channel.guild.members.fetch(userId).catch(() => null);
      if (!member) return false;

      // Add to allowed list (remove duplicates)
      if (!tempChannel.settings.allowed.includes(userId)) {
        tempChannel.settings.allowed.push(userId);
      }

      // Remove from block lists
      tempChannel.settings.blocked.view = tempChannel.settings.blocked.view.filter(id => id !== userId);
      tempChannel.settings.blocked.join = tempChannel.settings.blocked.join.filter(id => id !== userId);

      // Give them explicit permissions
      await channel.permissionOverwrites.edit(member, {
        ViewChannel: true,
        Connect: true
      });

      tempChannel.lastActivity = new Date();
      return true;
    } catch (error) {
      console.error('Failed to allow user:', error);
      return false;
    }
  }

  static async setUserLimit(channel: VoiceChannel, limit: number): Promise<boolean> {
    try {
      await channel.setUserLimit(limit);
      
      const tempChannel = this.tempChannels.get(channel.id);
      if (tempChannel) {
        tempChannel.settings.userLimit = limit;
        tempChannel.lastActivity = new Date();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to set user limit:', error);
      return false;
    }
  }

  static async setBitrate(channel: VoiceChannel, bitrate: number): Promise<boolean> {
    try {
      await channel.setBitrate(bitrate);
      
      const tempChannel = this.tempChannels.get(channel.id);
      if (tempChannel) {
        tempChannel.settings.bitrate = bitrate;
        tempChannel.lastActivity = new Date();
      }
      
      return true;
    } catch (error) {
      console.error('Failed to set bitrate:', error);
      return false;
    }
  }

  static setPassword(channelId: string, password: string): void {
    const tempChannel = this.tempChannels.get(channelId);
    if (tempChannel) {
      tempChannel.settings.password = password;
      tempChannel.lastActivity = new Date();
      
      // Clear password cache when password changes
      this.passwordCache.delete(channelId);
    }
  }

  static removePassword(channelId: string): void {
    const tempChannel = this.tempChannels.get(channelId);
    if (tempChannel) {
      delete tempChannel.settings.password;
      tempChannel.lastActivity = new Date();
      
      // Clear password cache when password is removed
      this.passwordCache.delete(channelId);
    }
  }

  static verifyPassword(channelId: string, password: string): boolean {
    const tempChannel = this.tempChannels.get(channelId);
    return tempChannel?.settings.password === password;
  }

  static hasPassword(channelId: string): boolean {
    const tempChannel = this.tempChannels.get(channelId);
    return !!tempChannel?.settings.password;
  }

  static getBlockedUsers(channelId: string): { view: string[], join: string[] } {
    const tempChannel = this.tempChannels.get(channelId);
    return tempChannel?.settings.blocked || { view: [], join: [] };
  }

  static getAllowedUsers(channelId: string): string[] {
    const tempChannel = this.tempChannels.get(channelId);
    return tempChannel?.settings.allowed || [];
  }

  static getAllTempChannels(): Map<string, TempChannelData> {
    return new Map(this.tempChannels);
  }

  static getChannelStats(): { 
    total: number; 
    locked: number; 
    hidden: number; 
    withPassword: number;
    withBlocks: number;
  } {
    const channels = Array.from(this.tempChannels.values());
    return {
      total: channels.length,
      locked: channels.filter(c => c.settings.locked).length,
      hidden: channels.filter(c => c.settings.hidden).length,
      withPassword: channels.filter(c => !!c.settings.password).length,
      withBlocks: channels.filter(c => 
        c.settings.blocked.view.length > 0 || c.settings.blocked.join.length > 0
      ).length
    };
  }

  static updateActivity(channelId: string): void {
    const tempChannel = this.tempChannels.get(channelId);
    if (tempChannel) {
      tempChannel.lastActivity = new Date();
    }
  }

  // Waiting Room Management
  static async createWaitingRoom(member: any, mainChannel: VoiceChannel): Promise<VoiceChannel | null> {
    try {
      const tempChannelData = this.getTempChannel(mainChannel.id);
      if (!tempChannelData) return null;

      const owner = await mainChannel.guild.members.fetch(tempChannelData.ownerId).catch(() => null);
      if (!owner) return null;

      const waitingRoom = await mainChannel.guild.channels.create({
        name: `🚪 Waiting for ${mainChannel.name.substring(0, 20)}...`,
        type: ChannelType.GuildVoice,
        parent: mainChannel.parent,
        permissionOverwrites: [
          { id: mainChannel.guild.id, deny: [PermissionFlagsBits.ViewChannel] },
          { id: member.id, allow: [PermissionFlagsBits.ViewChannel, PermissionFlagsBits.Connect, PermissionFlagsBits.SendMessages] },
          { id: owner.id, allow: [PermissionFlagsBits.ViewChannel] },
          { id: mainChannel.guild.members.me!.id, allow: [PermissionFlagsBits.ManageChannels, PermissionFlagsBits.MoveMembers, PermissionFlagsBits.SendMessages] }
        ]
      });

      this.waitingRooms.set(waitingRoom.id, {
        waitingRoomId: waitingRoom.id,
        mainChannelId: mainChannel.id,
        waitingUserId: member.id,
        createdAt: new Date()
      });

      console.log('Waiting room created:', {
        waitingRoomId: waitingRoom.id,
        mainChannelId: mainChannel.id,
        customId: `vc_enter_password:${waitingRoom.id}:${mainChannel.id}`
      });

      await member.voice.setChannel(waitingRoom);

      // Send Component v2 waiting room interface
      const waitingContainer = {
        type: 17, // Container
        accent_color: 0x3498db, // Blue theme
        components: [
          {
            type: 10, // Text
            content: `# 🔒 Waiting Room: ${mainChannel.name}\nHey <@${member.id}>, welcome to the waiting room! <@${owner.id}> has been notified.`
          },
          {
            type: 10, // Text
            content: `🛡️ **Password Protection Active**\nThis voice channel is password protected. Please enter the password to join.`
          },
          {
            type: 10, // Text
            content: `👑 **Channel Owner:** <@${owner.id}>\n⏰ **Time:** <t:${Math.floor(Date.now() / 1000)}:R>\n💡 *You will be moved automatically upon correct password entry*`
          },
          {
            type: 1, // ActionRow
            components: [
              {
                type: 2, // Button
                custom_id: `vc_enter_password:${waitingRoom.id}:${mainChannel.id}`,
                label: 'Enter Password',
                style: 1, // Primary
                emoji: { name: '🔑' }
              }
            ]
          }
        ]
      };

      await waitingRoom.send({
        components: [waitingContainer],
        flags: MessageFlags.IsComponentsV2
      });

      return waitingRoom;
    } catch (error) {
      console.error('Failed to create waiting room:', error);
      return null;
    }
  }

  static getWaitingRoomData(waitingRoomId: string): WaitingRoomData | undefined {
    return this.waitingRooms.get(waitingRoomId);
  }

  static isWaitingRoom(channelId: string): boolean {
    return this.waitingRooms.has(channelId);
  }

  static async cleanupWaitingRoom(channel: VoiceChannel): Promise<void> {
    if (this.waitingRooms.has(channel.id)) {
      try {
        await channel.delete('Waiting room cleanup');
        this.waitingRooms.delete(channel.id);
      } catch (error) {
        console.error('Failed to cleanup waiting room:', error);
      }
    }
  }

  // Authorization Management
  static authorizeJoin(channelId: string, userId: string): void {
    if (!this.authorizedJoins.has(channelId)) {
      this.authorizedJoins.set(channelId, new Set());
    }
    const userSet = this.authorizedJoins.get(channelId)!;
    userSet.add(userId);

    // Auto-expire authorization after 10 seconds
    setTimeout(() => {
      if (userSet.has(userId)) {
        userSet.delete(userId);
        if (userSet.size === 0) {
          this.authorizedJoins.delete(channelId);
        }
      }
    }, 10000);
  }

  static isJoinAuthorized(channelId: string, userId: string): boolean {
    const userSet = this.authorizedJoins.get(channelId);
    if (userSet && userSet.has(userId)) {
      // One-time pass, consume it
      userSet.delete(userId);
      if (userSet.size === 0) {
        this.authorizedJoins.delete(channelId);
      }
      return true;
    }
    return false;
  }

  // Password Cache Management
  static cachePassword(channelId: string, userId: string, password: string): void {
    if (!this.passwordCache.has(channelId)) {
      this.passwordCache.set(channelId, new Map());
    }
    
    const channelCache = this.passwordCache.get(channelId)!;
    channelCache.set(userId, {
      password,
      timestamp: new Date()
    });
  }

  static getCachedPassword(channelId: string, userId: string): string | null {
    const channelCache = this.passwordCache.get(channelId);
    if (!channelCache) return null;
    
    const entry = channelCache.get(userId);
    if (!entry) return null;
    
    // Check if the cached password matches the current channel password
    const currentPassword = this.tempChannels.get(channelId)?.settings.password;
    if (entry.password === currentPassword) {
      return entry.password;
    }
    
    // Remove outdated cache entry
    channelCache.delete(userId);
    if (channelCache.size === 0) {
      this.passwordCache.delete(channelId);
    }
    
    return null;
  }

  static hasCachedPassword(channelId: string, userId: string): boolean {
    return this.getCachedPassword(channelId, userId) !== null;
  }

  // Kick Protection Management
  static addKickProtection(channelId: string, userId: string): void {
    if (!this.kickProtection.has(channelId)) {
      this.kickProtection.set(channelId, new Map());
    }
    
    const now = new Date();
    const expiresAt = new Date(now.getTime() + 60000); // 60 seconds
    
    const channelProtection = this.kickProtection.get(channelId)!;
    channelProtection.set(userId, {
      kickedAt: now,
      expiresAt
    });
    
    // Auto-cleanup after expiration
    setTimeout(() => {
      const protection = this.kickProtection.get(channelId);
      if (protection) {
        protection.delete(userId);
        if (protection.size === 0) {
          this.kickProtection.delete(channelId);
        }
      }
    }, 60000);
  }

  static isKickProtected(channelId: string, userId: string): boolean {
    const channelProtection = this.kickProtection.get(channelId);
    if (!channelProtection) return false;
    
    const protection = channelProtection.get(userId);
    if (!protection) return false;
    
    const now = new Date();
    if (now > protection.expiresAt) {
      // Expired, remove it
      channelProtection.delete(userId);
      if (channelProtection.size === 0) {
        this.kickProtection.delete(channelId);
      }
      return false;
    }
    
    return true;
  }

  static getKickProtectionTimeLeft(channelId: string, userId: string): number {
    const channelProtection = this.kickProtection.get(channelId);
    if (!channelProtection) return 0;
    
    const protection = channelProtection.get(userId);
    if (!protection) return 0;
    
    const now = new Date();
    const timeLeft = protection.expiresAt.getTime() - now.getTime();
    return Math.max(0, Math.ceil(timeLeft / 1000)); // Return seconds
  }

  // Enhanced password verification with cache
  static verifyPasswordWithCache(channelId: string, userId: string, password: string): boolean {
    const tempChannel = this.tempChannels.get(channelId);
    if (!tempChannel?.settings.password) return false;
    
    const isCorrect = tempChannel.settings.password === password;
    
    if (isCorrect) {
      // Cache the correct password for this user
      this.cachePassword(channelId, userId, password);
    }
    
    return isCorrect;
  }
} 