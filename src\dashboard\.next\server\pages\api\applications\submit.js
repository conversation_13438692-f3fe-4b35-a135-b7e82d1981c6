"use strict";(()=>{var e={};e.id=4037,e.ids=[4037],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},7138:(e,t,o)=>{o.r(t),o.d(t,{config:()=>g,default:()=>f,routeModule:()=>h});var s={};o.r(s),o.d(s,{default:()=>p});var i=o(3433),r=o(264),a=o(584),n=o(5806),d=o(8525),l=o(2518);let{url:u,name:c}=o(8580).dashboardConfig.database,b=null;async function m(){if(b)return b;let e=await l.MongoClient.connect(u);return b=e,e}async function p(e,t){if("POST"!==e.method)return t.setHeader("Allow",["POST"]),t.status(405).json({error:"Method not allowed"});let o=await (0,n.getServerSession)(e,t,d.authOptions);if(!o||!o.user)return t.status(401).json({error:"Unauthorized"});let{applicationId:s,answers:i}=e.body;if(!s||!i)return t.status(400).json({error:"Missing applicationId or answers"});try{let e=(await m()).db(c).collection("application_submissions"),r={applicationId:s,answers:i,submittedBy:o.user.id,submittedAt:new Date,status:"pending"};return await e.insertOne(r),t.status(201).json({message:"Application submitted successfully!",submission:r})}catch(e){return t.status(500).json({error:"Internal server error"})}}let f=(0,a.M)(s,"default"),g=(0,a.M)(s,"config"),h=new i.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/applications/submit",pathname:"/api/applications/submit",bundlePath:"",filename:""},userland:s})},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var s=o(5542),i=o.n(s);let r=require("next-auth/providers/discord");var a=o.n(r),n=o(8580);let d={providers:[a()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,s=t.accessToken||null;e.user.id=o,e.user.accessToken=s;let i=!1;if(o)if((n.dashboardConfig.dashboard.admins||[]).includes(o))i=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();i=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),s=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var s=o(9021),i=o(2115),r=o.n(i),a=o(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");n=r().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=7138);module.exports=o})();