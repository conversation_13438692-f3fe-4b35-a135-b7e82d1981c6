// @ts-nocheck

import { Box, Flex, Button, Heading, Avatar, Menu, MenuButton, MenuList, MenuItem, HStack, Icon, Text, Badge } from '@chakra-ui/react';
import { signIn, signOut, useSession } from 'next-auth/react';
import { FiLogOut } from 'react-icons/fi';
import { FaFlask } from 'react-icons/fa';
import useGuildInfo from '../hooks/useGuildInfo';
import NotificationCenter from './NotificationCenter';

export default function Navbar() {
  const { data: session } = useSession();
  const { displayName } = useGuildInfo();
  const headingText = displayName ? `${displayName} Dashboard` : 'Bot Dashboard';
  
  // Show experimental announcement if applications are open and user is not a developer
  
  return (
    <Box
      px={6}
      py={2}
      bg="rgba(255,255,255,0.05)"
      backdropFilter="blur(20px)"
      borderBottom="1px solid"
      borderColor="whiteAlpha.200"
      position="sticky"
      top={0}
      zIndex={1000}
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: 'linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))',
        zIndex: -1,
      }}
    >
      <Flex h={16} alignItems="center" justifyContent="space-between">
        {/* Left Section - Title */}
        <Box flex="1">
          <Heading
            as="h1"
            fontSize="xl"
            bgGradient="linear(to-r, blue.400, purple.400)"
            bgClip="text"
            _hover={{
              bgGradient: "linear(to-r, blue.300, purple.300)",
              transform: "scale(1.02)",
            }}
            transition="all 0.2s"
          >
            {headingText}
          </Heading>
        </Box>
        
        {/* Center Section - Experimental Features Announcement */}
        
        {/* Right Section - User Menu */}
        <Box flex="1" display="flex" justifyContent="flex-end">
          <Flex alignItems="center" gap={4}>
          {session?.user ? (
            <HStack spacing={4}>
              {/* Notification Center */}
              <NotificationCenter />
              <Menu>
                <MenuButton
                  as={Button}
                  variant="ghost"
                  size="sm"
                  px={2}
                  py={1}
                  borderRadius="full"
                  _hover={{
                    bg: "whiteAlpha.200",
                  }}
                >
                  <HStack spacing={2}>
                    <Avatar 
                      size="sm" 
                      name={session.user.name ?? undefined} 
                      src={session.user.image ?? undefined}
                      borderWidth={2}
                      borderColor="blue.400"
                      _hover={{
                        borderColor: "purple.400",
                        transform: "scale(1.05)",
                      }}
                      transition="all 0.2s"
                    />
                    <Text color="gray.300" display={{ base: "none", md: "block" }}>
                      {session.user.name}
                    </Text>
                  </HStack>
                </MenuButton>
                <MenuList
                  bg="gray.800"
                  borderColor="whiteAlpha.200"
                  boxShadow="lg"
                  _hover={{ borderColor: "blue.400" }}
                >
                  <MenuItem
                    icon={<FiLogOut />}
                    onClick={() => signOut()}
                    _hover={{ bg: "whiteAlpha.200", color: "red.400" }}
                  >
                    Sign out
                  </MenuItem>
                </MenuList>
              </Menu>
            </HStack>
          ) : (
            <Button
              onClick={() => signIn('discord', { callbackUrl: '/overview' })}
              bgGradient="linear(to-r, blue.500, purple.500)"
              color="white"
              _hover={{
                bgGradient: "linear(to-r, blue.400, purple.400)",
                transform: "translateY(-1px)",
              }}
              _active={{
                bgGradient: "linear(to-r, blue.600, purple.600)",
                transform: "translateY(1px)",
              }}
              transition="all 0.2s"
            >
              Login with Discord
            </Button>
          )}
          </Flex>
        </Box>
      </Flex>
    </Box>
  );
} 