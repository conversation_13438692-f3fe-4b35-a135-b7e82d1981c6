(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7469],{1341:(e,t,r)=>{"use strict";r.d(t,{Th:()=>i});var a=r(94513),l=r(15373),s=r(2923),n=r(33225);let i=(0,s.R)(({isNumeric:e,...t},r)=>{let s=(0,l.k)();return(0,a.jsx)(n.B.th,{...t,ref:r,__css:s.th,"data-is-numeric":e})})},7746:(e,t,r)=>{"use strict";r.d(t,{E:()=>y});var a=r(94513),l=r(94285),s=r(13215),n=r(75387),i=r(22697),o=r(610),c=r(79364),u=r(33225),d=r(2923),f=r(56915);let h=(0,u.B)("div",{baseStyle:{boxShadow:"none",backgroundClip:"padding-box",cursor:"default",color:"transparent",pointerEvents:"none",userSelect:"none","&::before, &::after, *":{visibility:"hidden"}}}),m=(0,s.Vg)("skeleton-start-color"),b=(0,s.Vg)("skeleton-end-color"),p=(0,o.i7)({from:{opacity:0},to:{opacity:1}}),_=(0,o.i7)({from:{borderColor:m.reference,background:m.reference},to:{borderColor:b.reference,background:b.reference}}),y=(0,d.R)((e,t)=>{let r={...e,fadeDuration:"number"==typeof e.fadeDuration?e.fadeDuration:.4,speed:"number"==typeof e.speed?e.speed:.8},s=(0,f.V)("Skeleton",r),o=function(){let e=(0,l.useRef)(!0);return(0,l.useEffect)(()=>{e.current=!1},[]),e.current}(),{startColor:d="",endColor:y="",isLoaded:x,fadeDuration:v,speed:k,className:g,fitContent:j,animation:w,...C}=(0,n.M)(r),[N,S]=(0,c.rd)("colors",[d,y]),T=function(e){let t=(0,l.useRef)(void 0);return(0,l.useEffect)(()=>{t.current=e},[e]),t.current}(x),R=(0,i.cx)("chakra-skeleton",g),B={...N&&{[m.variable]:N},...S&&{[b.variable]:S}};if(x){let e=o||T?"none":`${p} ${v}s`;return(0,a.jsx)(u.B.div,{ref:t,className:R,__css:{animation:e},...C})}return(0,a.jsx)(h,{ref:t,className:R,...C,__css:{width:j?"fit-content":void 0,...s,...B,_dark:{...s._dark,...B},animation:w||`${k}s linear infinite alternate ${_}`}})});y.displayName="Skeleton"},8595:(e,t,r)=>{"use strict";r.d(t,{Tr:()=>i});var a=r(94513),l=r(15373),s=r(2923),n=r(33225);let i=(0,s.R)((e,t)=>{let r=(0,l.k)();return(0,a.jsx)(n.B.tr,{...e,ref:t,__css:r.tr})})},14205:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return i},noSSR:function(){return n}});let a=r(34007);r(94513),r(94285);let l=a._(r(34138));function s(e){return{default:(null==e?void 0:e.default)||e}}function n(e,t){return delete t.webpack,delete t.modules,e(t)}function i(e,t){let r=l.default,a={loading:e=>{let{error:t,isLoading:r,pastDelay:a}=e;return null}};e instanceof Promise?a.loader=()=>e:"function"==typeof e?a.loader=e:"object"==typeof e&&(a={...a,...e});let i=(a={...a,...t}).loader;return(a.loadableGenerated&&(a={...a,...a.loadableGenerated},delete a.loadableGenerated),"boolean"!=typeof a.ssr||a.ssr)?r({...a,loader:()=>null!=i?i().then(s):Promise.resolve(s(()=>null))}):(delete a.webpack,delete a.modules,n(r,a))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},15373:(e,t,r)=>{"use strict";r.d(t,{X:()=>f,k:()=>d});var a=r(94513),l=r(75387),s=r(29035),n=r(22697),i=r(2923),o=r(56915),c=r(33225);let[u,d]=(0,s.q)({name:"TableStylesContext",errorMessage:"useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Table />\" "}),f=(0,i.R)((e,t)=>{let r=(0,o.o)("Table",e),{className:s,layout:i,...d}=(0,l.M)(e);return(0,a.jsx)(u,{value:r,children:(0,a.jsx)(c.B.table,{ref:t,__css:{tableLayout:i,...r.table},className:(0,n.cx)("chakra-table",s),...d})})});f.displayName="Table"},18303:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var a=r(94285),l=r(65507);function s(e){let{value:t,defaultValue:r,onChange:s,shouldUpdate:n=(e,t)=>e!==t}=e,i=(0,l.c)(s),o=(0,l.c)(n),[c,u]=(0,a.useState)(r),d=void 0!==t,f=d?t:c,h=(0,l.c)(e=>{let t="function"==typeof e?e(f):e;o(f,t)&&(d||u(t),i(t))},[d,i,f,o]);return[f,h]}},20429:(e,t,r)=>{"use strict";r.d(t,{a:()=>o});var a=r(94513),l=r(22697),s=r(51413),n=r(2923),i=r(33225);let o=(0,n.R)(function(e,t){let{className:r,...n}=e,o=(0,s.Q)();return(0,a.jsx)(i.B.div,{ref:t,className:(0,l.cx)("chakra-card__header",r),__css:o.header,...n})})},22237:(e,t,r)=>{"use strict";r.d(t,{S:()=>j});var a=r(94513),l=r(75387),s=r(50614),n=r(72097),i=r(22697),o=r(610),c=r(94285),u=r(70423),d=r(33225);function f(e){return(0,a.jsx)(d.B.svg,{width:"1.2em",viewBox:"0 0 12 10",style:{fill:"none",strokeWidth:2,stroke:"currentColor",strokeDasharray:16},...e,children:(0,a.jsx)("polyline",{points:"1.5 6 4.5 9 10.5 1"})})}function h(e){return(0,a.jsx)(d.B.svg,{width:"1.2em",viewBox:"0 0 24 24",style:{stroke:"currentColor",strokeWidth:4},...e,children:(0,a.jsx)("line",{x1:"21",x2:"3",y1:"12",y2:"12"})})}function m(e){let{isIndeterminate:t,isChecked:r,...l}=e;return r||t?(0,a.jsx)(d.B.div,{style:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},children:(0,a.jsx)(t?h:f,{...l})}):null}var b=r(96027),p=r(2923),_=r(56915);let y={display:"inline-flex",alignItems:"center",justifyContent:"center",verticalAlign:"top",userSelect:"none",flexShrink:0},x={cursor:"pointer",display:"inline-flex",alignItems:"center",verticalAlign:"top",position:"relative"},v=(0,o.i7)({from:{opacity:0,strokeDashoffset:16,transform:"scale(0.95)"},to:{opacity:1,strokeDashoffset:0,transform:"scale(1)"}}),k=(0,o.i7)({from:{opacity:0},to:{opacity:1}}),g=(0,o.i7)({from:{transform:"scaleX(0.65)"},to:{transform:"scaleX(1)"}}),j=(0,p.R)(function(e,t){let r=(0,u.L)(),o={...r,...e},f=(0,_.o)("Checkbox",o),h=(0,l.M)(e),{spacing:p="0.5rem",className:j,children:w,iconColor:C,iconSize:N,icon:S=(0,a.jsx)(m,{}),isChecked:T,isDisabled:R=r?.isDisabled,onChange:B,inputProps:E,...D}=h,I=T;r?.value&&h.value&&(I=r.value.includes(h.value));let M=B;r?.onChange&&h.value&&(M=(0,s.O)(r.onChange,B));let{state:O,getInputProps:P,getCheckboxProps:$,getLabelProps:A,getRootProps:L}=(0,b.v)({...D,isDisabled:R,isChecked:I,onChange:M}),V=function(e){let[t,r]=(0,c.useState)(e),[a,l]=(0,c.useState)(!1);return e!==t&&(l(!0),r(e)),a}(O.isChecked),H=(0,c.useMemo)(()=>({animation:V?O.isIndeterminate?`${k} 20ms linear, ${g} 200ms linear`:`${v} 200ms linear`:void 0,...f.icon,...(0,n.o)({fontSize:N,color:C})}),[C,N,V,O.isIndeterminate,f.icon]),F=(0,c.cloneElement)(S,{__css:H,isIndeterminate:O.isIndeterminate,isChecked:O.isChecked});return(0,a.jsxs)(d.B.label,{__css:{...x,...f.container},className:(0,i.cx)("chakra-checkbox",j),...L(),children:[(0,a.jsx)("input",{className:"chakra-checkbox__input",...P(E,t)}),(0,a.jsx)(d.B.span,{__css:{...y,...f.control},className:"chakra-checkbox__control",...$(),children:F}),w&&(0,a.jsx)(d.B.span,{className:"chakra-checkbox__label",...A(),__css:{marginStart:p,...f.label},children:w})]})});j.displayName="Checkbox"},25964:(e,t,r)=>{"use strict";r.d(t,{l:()=>b});var a=r(94513),l=r(75387),s=r(16229),n=r(54338),i=r(81405),o=r(94285),c=r(22697),u=r(2923),d=r(33225);let f=(0,u.R)(function(e,t){let{children:r,placeholder:l,className:s,...n}=e;return(0,a.jsxs)(d.B.select,{...n,ref:t,className:(0,c.cx)("chakra-select",s),children:[l&&(0,a.jsx)("option",{value:"",children:l}),r]})});f.displayName="SelectField";var h=r(44637),m=r(56915);let b=(0,u.R)((e,t)=>{let r=(0,m.o)("Select",e),{rootProps:o,placeholder:c,icon:u,color:b,height:p,h:_,minH:x,minHeight:v,iconColor:k,iconSize:g,...j}=(0,l.M)(e),[w,C]=(0,n.l)(j,s.GF),N=(0,h.t)(C),S={paddingEnd:"2rem",...r.field,_focus:{zIndex:"unset",...r.field?._focus}};return(0,a.jsxs)(d.B.div,{className:"chakra-select__wrapper",__css:{width:"100%",height:"fit-content",position:"relative",color:b},...w,...o,children:[(0,a.jsx)(f,{ref:t,height:_??p,minH:x??v,placeholder:c,...N,__css:S,children:e.children}),(0,a.jsx)(y,{"data-disabled":(0,i.s)(N.disabled),...(k||b)&&{color:k||b},__css:r.icon,...g&&{fontSize:g},children:u})]})});b.displayName="Select";let p=e=>(0,a.jsx)("svg",{viewBox:"0 0 24 24",...e,children:(0,a.jsx)("path",{fill:"currentColor",d:"M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z"})}),_=(0,d.B)("div",{baseStyle:{position:"absolute",display:"inline-flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",top:"50%",transform:"translateY(-50%)"}}),y=e=>{let{children:t=(0,a.jsx)(p,{}),...r}=e,l=(0,o.cloneElement)(t,{role:"presentation",className:"chakra-select__icon",focusable:!1,"aria-hidden":!0,style:{width:"1em",height:"1em",color:"currentColor"}});return(0,a.jsx)(_,{...r,className:"chakra-select__icon-wrapper",children:(0,o.isValidElement)(t)?l:null})};y.displayName="SelectIcon"},31637:(e,t,r)=>{"use strict";r.d(t,{$c:()=>x,Jn:()=>w,O_:()=>_,Vh:()=>v,at:()=>f,uc:()=>p,uo:()=>j});var a=r(18303),l=r(78961),s=r(29035),n=r(50614),i=r(47133),o=r(18654),c=r(94285),u=r(87888),d=r(70011);let[f,h,m,b]=(0,u.D)();function p(e){let{defaultIndex:t,onChange:r,index:l,isManual:s,isLazy:n,lazyBehavior:i="unmount",orientation:o="horizontal",direction:u="ltr",...d}=e,[f,h]=(0,c.useState)(t??0),[b,p]=(0,a.i)({defaultValue:t??0,value:l,onChange:r});(0,c.useEffect)(()=>{null!=l&&h(l)},[l]);let _=m(),y=(0,c.useId)(),x=e.id??y;return{id:`tabs-${x}`,selectedIndex:b,focusedIndex:f,setSelectedIndex:p,setFocusedIndex:h,isManual:s,isLazy:n,lazyBehavior:i,orientation:o,descendants:_,direction:u,htmlProps:d}}let[_,y]=(0,s.q)({name:"TabsContext",errorMessage:"useTabsContext: `context` is undefined. Seems you forgot to wrap all tabs components within <Tabs />"});function x(e){let{focusedIndex:t,orientation:r,direction:a}=y(),l=h(),s=(0,c.useCallback)(e=>{let s=()=>{let e=l.nextEnabled(t);e&&e.node?.focus()},n=()=>{let e=l.prevEnabled(t);e&&e.node?.focus()},i="horizontal"===r,o="vertical"===r,c=e.key,u={["ltr"===a?"ArrowLeft":"ArrowRight"]:()=>i&&n(),["ltr"===a?"ArrowRight":"ArrowLeft"]:()=>i&&s(),ArrowDown:()=>o&&s(),ArrowUp:()=>o&&n(),Home:()=>{let e=l.firstEnabled();e&&e.node?.focus()},End:()=>{let e=l.lastEnabled();e&&e.node?.focus()}}[c];u&&(e.preventDefault(),u(e))},[l,t,r,a]);return{...e,role:"tablist","aria-orientation":r,onKeyDown:(0,n.H)(e.onKeyDown,s)}}function v(e){let{isDisabled:t=!1,isFocusable:r=!1,...a}=e,{setSelectedIndex:s,isManual:i,id:o,setFocusedIndex:c,selectedIndex:u}=y(),{index:f,register:h}=b({disabled:t&&!r}),m=f===u;return{...(0,d.I)({...a,ref:(0,l.Px)(h,e.ref),isDisabled:t,isFocusable:r,onClick:(0,n.H)(e.onClick,()=>{s(f)})}),id:C(o,f),role:"tab",tabIndex:m?0:-1,type:"button","aria-selected":m,"aria-controls":N(o,f),onFocus:t?void 0:(0,n.H)(e.onFocus,()=>{c(f);let e=t&&r;i||e||s(f)})}}let[k,g]=(0,s.q)({});function j(e){let{id:t,selectedIndex:r}=y(),a=(0,i.a)(e.children).map((e,a)=>(0,c.createElement)(k,{key:e.key??a,value:{isSelected:a===r,id:N(t,a),tabId:C(t,a),selectedIndex:r}},e));return{...e,children:a}}function w(e){let{children:t,...r}=e,{isLazy:a,lazyBehavior:l}=y(),{isSelected:s,id:n,tabId:i}=g(),u=(0,c.useRef)(!1);s&&(u.current=!0);let d=(0,o.q)({wasSelected:u.current,isSelected:s,enabled:a,mode:l});return{tabIndex:0,...r,children:d?t:null,role:"tabpanel","aria-labelledby":i,hidden:!s,id:n}}function C(e,t){return`${e}--tab-${t}`}function N(e,t){return`${e}--tabpanel-${t}`}},34138:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return f}});let a=r(34007)._(r(94285)),l=r(89532),s=[],n=[],i=!1;function o(e){let t=e(),r={loading:!0,loaded:null,error:null};return r.promise=t.then(e=>(r.loading=!1,r.loaded=e,e)).catch(e=>{throw r.loading=!1,r.error=e,e}),r}class c{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function u(e){return function(e,t){let r=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),s=null;function o(){if(!s){let t=new c(e,r);s={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return s.promise()}if(!i){let e=r.webpack&&1?r.webpack():r.modules;e&&n.push(t=>{for(let r of e)if(t.includes(r))return o()})}function u(e,t){o();let n=a.default.useContext(l.LoadableContext);n&&Array.isArray(r.modules)&&r.modules.forEach(e=>{n(e)});let i=a.default.useSyncExternalStore(s.subscribe,s.getCurrentValue,s.getCurrentValue);return a.default.useImperativeHandle(t,()=>({retry:s.retry}),[]),a.default.useMemo(()=>{var t;return i.loading||i.error?a.default.createElement(r.loading,{isLoading:i.loading,pastDelay:i.pastDelay,timedOut:i.timedOut,error:i.error,retry:s.retry}):i.loaded?a.default.createElement((t=i.loaded)&&t.default?t.default:t,e):null},[e,i])}return u.preload=()=>o(),u.displayName="LoadableComponent",a.default.forwardRef(u)}(o,e)}function d(e,t){let r=[];for(;e.length;){let a=e.pop();r.push(a(t))}return Promise.all(r).then(()=>{if(e.length)return d(e,t)})}u.preloadAll=()=>new Promise((e,t)=>{d(s).then(e,t)}),u.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let r=()=>(i=!0,t());d(n,e).then(r,r)})),window.__NEXT_PRELOADREADY=u.preloadReady;let f=u},35981:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var a=r(94513),l=r(15373),s=r(2923),n=r(33225);let i=(0,s.R)((e,t)=>{let r=(0,l.k)();return(0,a.jsx)(n.B.tbody,{...e,ref:t,__css:r.tbody})})},47402:(e,t,r)=>{"use strict";r.d(t,{o:()=>u});var a=r(94513),l=r(55100),s=r(22697),n=r(91047),i=r(31637),o=r(2923),c=r(33225);let u=(0,o.R)(function(e,t){let r=(0,n.e)(),o=(0,i.Vh)({...e,ref:t}),u=(0,l.H2)({outline:"0",display:"flex",alignItems:"center",justifyContent:"center",...r.tab});return(0,a.jsx)(c.B.button,{...o,className:(0,s.cx)("chakra-tabs__tab",e.className),__css:u})});u.displayName="Tab"},51413:(e,t,r)=>{"use strict";r.d(t,{Q:()=>l,s:()=>a});let[a,l]=(0,r(1e3).Wh)("Card")},51927:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var a=r(94513),l=r(15373),s=r(2923),n=r(33225);let i=(0,s.R)((e,t)=>{let r=(0,l.k)();return(0,a.jsx)(n.B.thead,{...e,ref:t,__css:r.thead})})},54338:(e,t,r)=>{"use strict";function a(e,t){let r={},a={};for(let[l,s]of Object.entries(e))t.includes(l)?r[l]=s:a[l]=s;return[r,a]}r.d(t,{l:()=>a})},59818:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var a=r(94513),l=r(22697),s=r(51413),n=r(2923),i=r(33225);let o=(0,n.R)(function(e,t){let{className:r,...n}=e,o=(0,s.Q)();return(0,a.jsx)(i.B.div,{ref:t,className:(0,l.cx)("chakra-card__body",r),__css:o.body,...n})})},68443:(e,t,r)=>{"use strict";r.d(t,{Z:()=>u});var a=r(94513),l=r(75387),s=r(22697),n=r(51413),i=r(2923),o=r(56915),c=r(33225);let u=(0,i.R)(function(e,t){let{className:r,children:i,direction:u="column",justify:d,align:f,...h}=(0,l.M)(e),m=(0,o.o)("Card",e);return(0,a.jsx)(c.B.div,{ref:t,className:(0,s.cx)("chakra-card",r),__css:{display:"flex",flexDirection:u,justifyContent:d,alignItems:f,position:"relative",minWidth:0,wordWrap:"break-word",...m.container},...h,children:(0,a.jsx)(n.s,{value:m,children:i})})})},70423:(e,t,r)=>{"use strict";r.d(t,{L:()=>l,a:()=>a});let[a,l]=(0,r(29035).q)({name:"CheckboxGroupContext",strict:!1})},72671:(e,t,r)=>{"use strict";r.d(t,{K:()=>c});var a=r(94513),l=r(22697),s=r(91047),n=r(31637),i=r(2923),o=r(33225);let c=(0,i.R)(function(e,t){let r=(0,n.Jn)({...e,ref:t}),i=(0,s.e)();return(0,a.jsx)(o.B.div,{outline:"0",...r,className:(0,l.cx)("chakra-tabs__tab-panel",e.className),__css:i.tabpanel})});c.displayName="TabPanel"},77072:(e,t,r)=>{e.exports=r(14205)},83881:(e,t,r)=>{"use strict";r.d(t,{w:()=>u});var a=r(94513),l=r(55100),s=r(22697),n=r(91047),i=r(31637),o=r(2923),c=r(33225);let u=(0,o.R)(function(e,t){let r=(0,i.$c)({...e,ref:t}),o=(0,n.e)(),u=(0,l.H2)({display:"flex",...o.tablist});return(0,a.jsx)(c.B.div,{...r,className:(0,s.cx)("chakra-tabs__tablist",e.className),__css:u})});u.displayName="TabList"},89532:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"LoadableContext",{enumerable:!0,get:function(){return a}});let a=r(34007)._(r(94285)).default.createContext(null)},91047:(e,t,r)=>{"use strict";r.d(t,{e:()=>h,t:()=>m});var a=r(94513),l=r(75387),s=r(29035),n=r(22697),i=r(94285),o=r(31637),c=r(2923),u=r(56915),d=r(33225);let[f,h]=(0,s.q)({name:"TabsStylesContext",errorMessage:"useTabsStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Tabs />\" "}),m=(0,c.R)(function(e,t){let r=(0,u.o)("Tabs",e),{children:s,className:c,...h}=(0,l.M)(e),{htmlProps:m,descendants:b,...p}=(0,o.uc)(h),_=(0,i.useMemo)(()=>p,[p]),{isFitted:y,...x}=m,v={position:"relative",...r.root};return(0,a.jsx)(o.at,{value:b,children:(0,a.jsx)(o.O_,{value:_,children:(0,a.jsx)(f,{value:r,children:(0,a.jsx)(d.B.div,{className:(0,n.cx)("chakra-tabs",c),ref:t,...x,__css:v,children:s})})})})});m.displayName="Tabs"},95497:(e,t,r)=>{"use strict";r.d(t,{Td:()=>i});var a=r(94513),l=r(15373),s=r(2923),n=r(33225);let i=(0,s.R)(({isNumeric:e,...t},r)=>{let s=(0,l.k)();return(0,a.jsx)(n.B.td,{...t,ref:r,__css:s.td,"data-is-numeric":e})})},99820:(e,t,r)=>{"use strict";r.d(t,{T:()=>c});var a=r(94513),l=r(22697),s=r(91047),n=r(31637),i=r(2923),o=r(33225);let c=(0,i.R)(function(e,t){let r=(0,n.uo)(e),i=(0,s.e)();return(0,a.jsx)(o.B.div,{...r,width:"100%",ref:t,className:(0,l.cx)("chakra-tabs__tab-panels",e.className),__css:i.tabpanels})});c.displayName="TabPanels"}}]);