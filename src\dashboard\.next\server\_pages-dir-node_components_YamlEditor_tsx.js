"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_YamlEditor_tsx";
exports.ids = ["_pages-dir-node_components_YamlEditor_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/YamlEditor.tsx":
/*!***********************************!*\
  !*** ./components/YamlEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// Ace editor is not SSR-friendly, so we load it dynamically.\nconst AceEditor = next_dynamic__WEBPACK_IMPORTED_MODULE_3___default()(async ()=>{\n    const ace = await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! react-ace */ \"react-ace\", 23));\n    // Import required modes, themes, and extensions\n    await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! ace-builds/src-noconflict/mode-yaml */ \"ace-builds/src-noconflict/mode-yaml\", 23));\n    await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! ace-builds/src-noconflict/theme-twilight */ \"ace-builds/src-noconflict/theme-twilight\", 23));\n    await Promise.resolve(/*! import() */).then(__webpack_require__.t.bind(__webpack_require__, /*! ace-builds/src-noconflict/ext-language_tools */ \"ace-builds/src-noconflict/ext-language_tools\", 23)); // For autocompletion\n    return ace;\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\YamlEditor.tsx -> \" + \"ace-builds/src-noconflict/ext-language_tools\"\n        ]\n    },\n    ssr: false\n});\nconst YamlEditor = ({ value, onChange, height = '60vh' })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n        borderWidth: \"1px\",\n        borderColor: \"purple.600\",\n        borderRadius: \"md\",\n        overflow: \"hidden\",\n        height: height,\n        sx: {\n            '.ace_editor': {\n                fontFamily: \"'Fira Code', 'Consolas', 'Monaco', 'monospace' !important\",\n                fontSize: '14px !important',\n                lineHeight: '1.6 !important'\n            },\n            '.ace_gutter': {\n                background: '#232323'\n            },\n            '.ace_scroller': {\n                backgroundColor: '#1e1e1e'\n            }\n        },\n        children:  false && /*#__PURE__*/ 0\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\YamlEditor.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (YamlEditor);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/YamlEditor.tsx\n");

/***/ })

};
;