"use strict";(()=>{var e={};e.id=3324,e.ids=[3324],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,s){return s in t?t[s]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,s)):"function"==typeof t&&"default"===s?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,s)=>{e.exports=s(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5619:(e,t,s)=>{s.r(t),s.d(t,{config:()=>c,default:()=>u,routeModule:()=>m});var o={};s.r(o),s.d(o,{default:()=>l});var r=s(3433),i=s(264),a=s(584),n=s(5806),d=s(8525);async function l(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let s=await (0,n.getServerSession)(e,t,d.authOptions);if(!s)return t.status(401).json({error:"Unauthorized",hasSession:!1});let o=s.user;return t.status(200).json({hasSession:!0,userId:o.id,isAdmin:o.isAdmin,name:o.name,email:o.email,debug:{sessionKeys:Object.keys(s),userKeys:Object.keys(o),fullUser:o}})}catch(e){return t.status(500).json({error:"Internal server error",details:e.message})}}let u=(0,a.M)(o,"default"),c=(0,a.M)(o,"config"),m=new r.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/auth/permissions",pathname:"/api/auth/permissions",bundlePath:"",filename:""},userland:o})},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,s)=>{s.r(t),s.d(t,{authOptions:()=>d,default:()=>l});var o=s(5542),r=s.n(o);let i=require("next-auth/providers/discord");var a=s.n(i),n=s(8580);let d={providers:[a()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:s})=>(t&&s&&(e.accessToken=t.access_token||null,e.id=s.id||null),e),async session({session:e,token:t}){if(e?.user){let s=t.id||null,o=t.accessToken||null;e.user.id=s,e.user.accessToken=o;let r=!1;if(s)if((n.dashboardConfig.dashboard.admins||[]).includes(s))r=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${s}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let s=await t.json();r=e.some(e=>s.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let s=new URL(t),o=`${s.protocol}//localhost${s.port?`:${s.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=r()(d)},8580:(e,t,s)=>{s.r(t),s.d(t,{dashboardConfig:()=>d,default:()=>l});var o=s(9021),r=s(2115),i=s.n(r),a=s(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");n=i().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var s=t(t.s=5619);module.exports=s})();