import fs from 'fs';
import path from 'path';
import yaml from 'yaml';

export interface BotConfig {
  database?: {
    url?: string;
    name?: string;
    options?: any;
  };
  [key: string]: any;
}

export function loadBotConfig(): BotConfig {
  try {
    // Look for config.yml starting from the current working directory and searching upwards
    let currentPath = process.cwd();
    for (let i = 0; i < 5; i++) { // Search up to 5 levels
      const configPath = path.join(currentPath, 'config.yml');
      if (fs.existsSync(configPath)) {
        const configContent = fs.readFileSync(configPath, 'utf8');
        return yaml.parse(configContent);
      }
      currentPath = path.join(currentPath, '..');
    }
    throw new Error('Fatal: Could not find config.yml. The bot cannot start.');
  } catch (error) {
    console.error('Error loading config:', error);
    throw error;
  }
} 