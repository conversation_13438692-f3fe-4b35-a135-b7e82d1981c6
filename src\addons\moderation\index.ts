import { <PERSON><PERSON><PERSON>ber, Message, EmbedBuilder } from 'discord.js';
import type { Addon } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';

// ------------------------------
// Load YAML configuration
// ------------------------------
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let config: any = {};
try {
  const configRaw = fs.readFileSync(path.join(__dirname, 'config.yml'), 'utf8');
  config = YAML.parse(configRaw);
} catch (err) {
  // Fallback to defaults when config is missing or malformed
  console.error('[moderation] Could not read config.yml – using built-in defaults', err);
  config = {};
}

// ------------------------------
// Resolve settings with fallbacks
// ------------------------------
const RAID_JOIN_THRESHOLD = config.raidProtection?.joinThreshold ?? 5;
const RAID_TIME_WINDOW_MS = config.raidProtection?.timeWindowMs ?? 10_000;
const RAID_MITIGATION_DURATION_MS = config.raidProtection?.mitigationDurationMs ?? 5 * 60_000;

const SPAM_MSG_THRESHOLD = config.spamPrevention?.msgThreshold ?? 5;
const SPAM_TIME_WINDOW_MS = config.spamPrevention?.timeWindowMs ?? 10_000;
const SPAM_TIMEOUT_DURATION_MS = config.spamPrevention?.timeoutMs ?? 10 * 60_000;

// ------------------------------
// Logging settings
// ------------------------------
const LOGGING_ENABLED: boolean = config.logging?.enabled ?? false;
const LOG_CHANNEL_ID: string | null = config.logging?.channelId ?? null;

// ------------------------------
// Internal state stores
// ------------------------------
// guildId -> array of join timestamps (epoch ms)
const guildJoinLogs = new Map<string, number[]>();
// guildId -> raid status (active flag & expiry timestamp)
const raidState = new Map<string, { active: boolean; until: number }>();

// userId -> array of message timestamps (epoch ms)
const userMessageLogs = new Map<string, number[]>();

const logger = Logger.createAddonLogger('moderation');

function registerJoin(member: GuildMember) {
  const now = Date.now();
  const guildId = member.guild.id;
  const logs = guildJoinLogs.get(guildId) ?? [];
  // keep only timestamps within window
  const fresh = logs.filter(ts => now - ts <= RAID_TIME_WINDOW_MS);
  fresh.push(now);
  guildJoinLogs.set(guildId, fresh);

  // Clean up expired raid flag
  const state = raidState.get(guildId);
  if (state && state.active && now >= state.until) {
    raidState.delete(guildId);
    logger.info(`Raid mitigation period ended for guild ${guildId}`);
  }

  // If raid already active, immediately mitigate this member
  const activeRaid = raidState.get(guildId)?.active;
  if (activeRaid) {
    mitigateRaid(member, 'Raid still active');
    return;
  }

  // Detect new raid
  if (fresh.length >= RAID_JOIN_THRESHOLD) {
    raidState.set(guildId, { active: true, until: now + RAID_MITIGATION_DURATION_MS });
    logger.warn(`Potential raid detected in guild ${guildId}. Activating mitigation for ${RAID_MITIGATION_DURATION_MS / 1000}s`);
    // Mitigate all recent members in the burst (including current)
    mitigateRaid(member, 'Raid mitigation – burst');
  }
}

async function mitigateRaid(member: GuildMember, reason: string) {
  try {
    if (member.bannable) {
      await member.ban({ reason });
      logger.warn(`Banned ${member.user.tag} (${member.id}) – ${reason}`);
      await sendLog(member.client, {
        embeds: [new EmbedBuilder()
          .setTitle('Member Banned (Raid Protection)')
          .setColor(0xff0000)
          .addFields([
            { name: 'User', value: `${member.user.tag} (${member.id})`, inline: false },
            { name: 'Guild', value: member.guild.name, inline: true },
            { name: 'Reason', value: reason, inline: true }
          ])
          .setTimestamp()]
      });
    } else if (member.kickable) {
      await member.kick(reason);
      logger.warn(`Kicked ${member.user.tag} (${member.id}) – ${reason}`);
      await sendLog(member.client, {
        embeds: [new EmbedBuilder()
          .setTitle('Member Kicked (Raid Protection)')
          .setColor(0xffa500)
          .addFields([
            { name: 'User', value: `${member.user.tag} (${member.id})`, inline: false },
            { name: 'Guild', value: member.guild.name, inline: true },
            { name: 'Reason', value: reason, inline: true }
          ])
          .setTimestamp()]
      });
    } else {
      logger.warn(`Cannot kick/ban ${member.user.tag} (${member.id}) – insufficient permissions`);
    }
  } catch (err) {
    logger.error(`Failed to mitigate raid member ${member.id}:`, err as any);
  }
}

function registerMessage(message: Message) {
  const now = Date.now();
  const userId = message.author.id;
  const logs = userMessageLogs.get(userId) ?? [];
  const fresh = logs.filter(ts => now - ts <= SPAM_TIME_WINDOW_MS);
  fresh.push(now);
  userMessageLogs.set(userId, fresh);
  return fresh.length;
}

async function handleSpam(message: Message, count: number) {
  try {
    // Delete the offending message
    await message.delete().catch(() => {});

    // Timeout user if possible (Discord.js v14 –  member.timeout)
    if (message.member?.moderatable) {
      await message.member.timeout(SPAM_TIMEOUT_DURATION_MS, 'Spam prevention').catch(() => {});
      logger.warn(`Timed out ${message.author.tag} for spam (${count} msgs/${SPAM_TIME_WINDOW_MS}ms)`);
      await sendLog(message.client, {
        embeds: [new EmbedBuilder()
          .setTitle('User Timed Out (Spam Prevention)')
          .setColor(0x3b82f6)
          .addFields([
            { name: 'User', value: `${message.author.tag} (${message.author.id})`, inline: false },
            { name: 'Channel', value: `${message.channel?.toString()}`, inline: true },
            { name: 'Count', value: `${count} msgs/${SPAM_TIME_WINDOW_MS}ms`, inline: true }
          ])
          .setTimestamp()]
      });
    }

    // Optionally, notify the channel if the channel supports sending (i.e. text-based channels)
    if ('send' in message.channel) {
      // Cast as any to satisfy TypeScript union limitations
      await (message.channel as any).send({ content: `${message.author}, please slow down – you are sending messages too quickly!` }).catch(() => {});
    }
  } catch (err) {
    logger.error(`Failed to handle spam for user ${message.author.id}:`, err as any);
  }
}

// Dynamically load slash commands from the commands directory
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  if (!fs.existsSync(commandsPath)) return commands;

  const commandFiles = fs.readdirSync(commandsPath).filter(file => file.endsWith('.js') || file.endsWith('.ts'));

  for (const file of commandFiles) {
    try {
      const cmdPath = path.join(commandsPath, file);
      const cmdUrl = pathToFileURL(cmdPath).href;
      const cmdModule = await import(cmdUrl);
      if (cmdModule.data && cmdModule.execute) {
        commands.push({
          data: cmdModule.data,
          execute: cmdModule.execute,
          cooldown: cmdModule.cooldown || 3000
        });
      }
    } catch (err) {
      logger.error(`Failed to load command ${file}:`, err as any);
    }
  }
  return commands;
}

const addon: Addon = {
  info: {
    name: config.addon?.name ?? 'moderation',
    version: config.addon?.version ?? '1.0.0',
    description: config.addon?.description ?? 'Basic raid protection and spam prevention',
    author: config.addon?.author ?? 'auto'
  },

  // No slash commands yet
  commands: await loadCommands(),

  events: [
    {
      name: 'guildMemberAdd',
      once: false,
      execute: async (member: GuildMember) => {
        registerJoin(member);
      }
    },
    {
      name: 'messageCreate',
      once: false,
      execute: async (message: Message) => {
        if (message.author.bot || !message.guild) return;

        const count = registerMessage(message);
        if (count >= SPAM_MSG_THRESHOLD) {
          await handleSpam(message, count);
        }
      }
    },
  ],

  onLoad: async () => {
    logger.info('Moderation addon loaded.');
  },

  onUnload: async () => {
    logger.info('Moderation addon unloaded.');
  }
};

async function sendLog(client: any, payload: string | { embeds: any[] }) {
  if (!LOGGING_ENABLED || !LOG_CHANNEL_ID) return;
  try {
    const channel = client.channels.cache.get(LOG_CHANNEL_ID) ?? await client.channels.fetch(LOG_CHANNEL_ID);
    if (channel && 'send' in channel) {
      await (channel as any).send(payload);
    }
  } catch (err) {
    logger.warn('Failed to send moderation log', err as any);
  }
}

export default addon; 