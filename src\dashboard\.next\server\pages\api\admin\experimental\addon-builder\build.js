"use strict";(()=>{var e={};e.id=5241,e.ids=[5241],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,n)=>{e.exports=n(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8354:e=>{e.exports=require("util")},8525:(e,t,n)=>{n.r(t),n.d(t,{authOptions:()=>d,default:()=>c});var a=n(5542),o=n.n(a);let r=require("next-auth/providers/discord");var i=n.n(r),s=n(8580);let d={providers:[i()({clientId:s.dashboardConfig.bot.clientId,clientSecret:s.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:n})=>(t&&n&&(e.accessToken=t.access_token||null,e.id=n.id||null),e),async session({session:e,token:t}){if(e?.user){let n=t.id||null,a=t.accessToken||null;e.user.id=n,e.user.accessToken=a;let o=!1;if(n)if((s.dashboardConfig.dashboard.admins||[]).includes(n))o=!0;else{let e=s.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&s.dashboardConfig.bot.token&&s.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${s.dashboardConfig.bot.guildId}/members/${n}`,{headers:{Authorization:`Bot ${s.dashboardConfig.bot.token}`}});if(t.ok){let n=await t.json();o=e.some(e=>n.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let n=new URL(t),a=`${n.protocol}//localhost${n.port?`:${n.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:s.dashboardConfig.dashboard.session.secret||s.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},c=o()(d)},8543:(e,t,n)=>{n.r(t),n.d(t,{config:()=>b,default:()=>h,routeModule:()=>y});var a={};n.r(a),n.d(a,{default:()=>p});var o=n(3433),r=n(264),i=n(584),s=n(5806),d=n(8525),c=n(9021),l=n.n(c),m=n(3873),u=n.n(m);async function p(e,t){if("POST"!==e.method)return t.status(405).json({message:"Method not allowed"});try{let a=await (0,s.getServerSession)(e,t,d.authOptions);if(!a||a.user?.id!=="933023999770918932")return t.status(403).json({message:"Unauthorized"});let{nodes:o,edges:r,name:i,description:c,author:m,version:p}=e.body,h=function(e,t){let n=[],a=e.filter(e=>"command"===e.type);a.forEach(e=>{e.data.commandName&&""!==e.data.commandName.trim()||n.push(`Command node "${e.data.label||e.id}" is missing a command name`),e.data.description&&""!==e.data.description.trim()||n.push(`Command node "${e.data.label||e.id}" is missing a description`)}),e.filter(e=>"apiRequest"===e.type).forEach(e=>{if(e.data.url&&""!==e.data.url.trim()&&"https://api.example.com"!==e.data.url||n.push(`API Request node "${e.data.label||e.id}" needs a valid URL. Please configure the API endpoint.`),e.data.url&&""!==e.data.url.trim())try{new URL(e.data.url)}catch(t){n.push(`API Request node "${e.data.label||e.id}" has an invalid URL: ${e.data.url}`)}}),e.filter(e=>"action"===e.type).forEach(e=>{e.data.actionType&&""!==e.data.actionType.trim()||n.push(`Action node "${e.data.label||e.id}" is missing an action type`),"sendMessage"!==e.data.actionType&&"sendEmbed"!==e.data.actionType||e.data.message&&""!==e.data.message.trim()||n.push(`Action node "${e.data.label||e.id}" needs a message to send`)}),0===a.length&&n.push("Your addon must have at least one command node");let o=new Set;t.forEach(e=>{o.add(e.source),o.add(e.target)});let r=e.filter(e=>"trigger"!==e.type&&!o.has(e.id));return r.length>0&&n.push(`Found ${r.length} disconnected nodes. All nodes (except triggers) must be connected to the flow.`),{isValid:0===n.length,errors:n}}(o,r);if(!h.isValid)return t.status(400).json({message:"Flow validation failed",errors:h.errors,details:"Please fix the configuration issues before building"});let b=function(e,t,n,a,o,r){var i,s,d,c,l,m,u,p,h,b,y,x;let $=[],S=[],v=e.filter(e=>"command"===e.type),w=e.filter(e=>"event"===e.type);return v.forEach(n=>{let a=n.data.commandName||`command${n.id}`,o=n.data.description||"Generated command",r=n.data.cooldown||0,i=function(e,t,n,a){let o=a.filter(e=>"apiRequest"===e.type),r=a.filter(e=>"apiRequest"!==e.type),i=[o.map(e=>g(e)).join("\n\n"),r.map(e=>{switch(e.data.actionType){case"sendMessage":return`    // Action: Send Message
    await interaction.reply(interpolateVariables('${e.data.message||"Hello!"}', context));`;case"sendEmbed":return`    // Action: Send Embed
    try {
      const response = (context as any).response;
      
      if (response && response.results && response.results.length > 0) {
        const item = response.results[0];
        
        const embed = new EmbedBuilder()
          .setTitle('🤗 ${e.data.title||"Hug!"}')
          .setDescription(\`\${interaction.user.username} sends a warm hug! 💕\`)
          .setColor('#FF69B4');
        
        // Add image if available
        if (item.url) {
          embed.setImage(item.url);
        }
        
        // Add footer with source info
        if (item.anime_name) {
          embed.setFooter({ text: \`From: \${item.anime_name}\` });
        }
        
        await interaction.reply({ embeds: [embed] });
      } else {
        // Fallback if no API data available
        const fallbackMessage = interpolateVariables('${e.data.message||"\uD83E\uDD17 *sends a virtual hug* \uD83D\uDC95"}', context);
        
        const embed = new EmbedBuilder()
          .setTitle('${e.data.title||"Message"}')
          .setDescription(fallbackMessage)
          .setColor(0x00ff00);
        
        await interaction.reply({ embeds: [embed] });
      }
    } catch (error) {
      console.error('Error creating embed:', error);
      await interaction.reply({ content: '🤗 *sends a virtual hug* 💕', ephemeral: true });
    }`;default:return`    // Action: ${e.data.actionType}
    await interaction.reply('Action: ${e.data.actionType}');`}}).join("\n\n")].filter(e=>e.trim()).join("\n\n");return`import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\\{([^}]+)\\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const data = new SlashCommandBuilder()
  .setName('${e}')
  .setDescription('${t}');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    // Create context with Discord data and API responses
    const context = {
      user: {
        id: interaction.user.id,
        username: interaction.user.username,
        displayName: interaction.user.displayName,
        tag: interaction.user.tag,
      },
      channel: {
        id: interaction.channel?.id,
        name: interaction.channel?.type === 0 ? interaction.channel.name : 'dm',
      },
      guild: {
        id: interaction.guild?.id,
        name: interaction.guild?.name,
      },
      // API response data will be added here by API request blocks
    };

${i||'    await interaction.reply("Command executed successfully!");'}
  } catch (error) {
    console.error('Error executing ${e} command:', error);
    await interaction.reply({ content: 'An error occurred while executing this command.', ephemeral: true });
  }
}

export const cooldown = ${1e3*n};`}(a,o,r,f(n.id,e,t));$.push({name:a,code:i})}),w.forEach(n=>{let a=n.data.eventType||"messageCreate",o=`${a}Handler`,r=function(e,t){let n=t.map(e=>"apiRequest"===e.type?g(e):"sendMessage"===e.data.actionType?`    // Send message action
    const channel = bot.client.channels.cache.get(interpolateVariables('${e.data.channel||"CHANNEL_ID"}', context));
    if (channel && channel.isTextBased()) {
      await channel.send(interpolateVariables('${e.data.message||"Hello!"}', context));
    }`:`    // Action: ${e.data.actionType}
    console.log('Executing action:', '${e.data.actionType}');`).join("\n");return`import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\\{([^}]+)\\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const eventName = '${e}';

export async function execute(bot: BotInstance, ...args: any[]) {
  try {
    // Create context with event data and API responses
    const context = {
      event: {
        type: '${e}',
        args: args,
      },
      // API response data will be added here by API request blocks
    };

${n||'    console.log("Event triggered:", eventName);'}
  } catch (error) {
    console.error('Error in event handler:', error);
  }
}`}(a,f(n.id,e,t));S.push({name:o,code:r})}),{index:(i=n,s=a,d=o,c=r,l=0,m=0,`import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Dynamically load commands from commands folder
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: '${i}',
    version: '${c||"1.0.0"}',
    description: '${s||"Generated addon from visual builder"}',
    author: '${d||"Addon Builder"}'
  },

  commands: await loadCommands(),

  events: [],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger('${i}');
    logger.info(\`Loading ${i} with \${addon.commands?.length || 0} commands...\`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger('${i}');
    logger.info(\`Unloading ${i}...\`);
  }
};

export default addon;`),config:(u=n,p=a,h=o,b=r,y=$,x=S,`name: "${u}"
version: "${b||"1.0.0"}"
description: "${p||"Generated addon from visual builder"}"
author: "${h||"Addon Builder"}"
main: "index.js"
dependencies: []
commands:
${y.map(e=>`  - ${e.name}`).join("\n")}
events:
${x.map(e=>`  - ${e.name}`).join("\n")}`),commands:$,events:S}}(o,r,i,c,m,p),y=i.toLowerCase().replace(/[^a-z0-9]/g,"-"),x=process.cwd().includes("dashboard")?u().resolve(process.cwd(),"..",".."):process.cwd(),$=u().join(x,"src","addons",y);l().existsSync($)||l().mkdirSync($,{recursive:!0});let S=u().join($,"commands");l().existsSync(S)||l().mkdirSync(S,{recursive:!0}),l().writeFileSync(u().join($,"index.ts"),b.index),l().writeFileSync(u().join($,"config.yml"),b.config);let v={name:i,description:c||"Generated addon from visual builder",author:m||a.user?.name||"Addon Builder",version:p||"1.0.0",nodes:o,edges:r,metadata:{createdAt:new Date().toISOString(),builderVersion:"1.0.0",originalFlow:!0,createdBy:a.user?.email||"unknown"}};if(l().writeFileSync(u().join($,"flow.json"),JSON.stringify(v,null,2)),b.commands.forEach(e=>{l().writeFileSync(u().join(S,`${e.name}.ts`),e.code)}),b.events.length>0){let e=u().join($,"events");l().existsSync(e)||l().mkdirSync(e,{recursive:!0}),b.events.forEach(t=>{l().writeFileSync(u().join(e,`${t.name}.ts`),t.code)})}let w=u().join(x,"src","dashboard","addon-reload.signal");l().writeFileSync(w,JSON.stringify({requestedBy:a.user?.email||"addon-builder",timestamp:Date.now(),action:"addon-built",addonName:y}));try{let{exec:e}=n(9646),t=n(8354).promisify(e),a=(process.platform,`cd "${x}" && npx tsc --project tsconfig.json --outDir dist --rootDir src`);await t(a);let o=u().join(x,"dist","addons",y);if(l().existsSync(o)){let e=u().join($,"config.yml"),t=u().join(o,"config.yml");l().existsSync(e)&&l().copyFileSync(e,t);let n=u().join($,"flow.json"),a=u().join(o,"flow.json");l().existsSync(n)&&l().copyFileSync(n,a)}}catch(e){}let A=u().join(x,"addon-reload.signal"),P={timestamp:Date.now(),requestedBy:"addon-builder",action:"addon-built",addonName:y};return l().writeFileSync(A,JSON.stringify(P,null,2)),t.status(200).json({message:"Addon built and compiled successfully",addonName:y,files:{index:b.index,config:b.config,commands:b.commands,events:b.events}})}catch(e){return t.status(500).json({message:"Internal server error"})}}function f(e,t,n){let a=new Set,o=[];return!function e(r){a.has(r)||(a.add(r),n.filter(e=>e.source===r).forEach(n=>{let a=t.find(e=>e.id===n.target);a&&(("action"===a.type||"apiRequest"===a.type)&&o.push(a),e(a.id))}))}(e),o}function g(e){let t=e.data,n=t.method||"GET",a=t.url,o=t.timeout||5e3,r=t.headers||[],i=t.body||"",s=t.bodyType||"json",d=t.saveToVariable||"response",c=t.errorHandling||"log";if(!a||""===a.trim()||"https://api.example.com"===a)return`    // API Request: ${n} (URL not configured)
    console.warn('API Request node is not properly configured. Please set a valid URL in the visual builder.');
    
    // Placeholder response for unconfigured API request
    const ${d}Data = {
      error: 'API endpoint not configured',
      message: 'Please configure the API request node in the visual builder',
      configured: false
    };
    
    // Store placeholder response in context
    (context as any).${d} = ${d}Data;`;let l=r.length>0?`      headers: {
${r.map(e=>`        '${e.key}': interpolateVariables('${e.value}', context),`).join("\n")}
      },`:"",m="";return i&&("POST"===n||"PUT"===n||"PATCH"===n)&&(m="json"===s?`      body: JSON.stringify(JSON.parse(interpolateVariables('${i}', context))),`:"form"===s?`      body: new URLSearchParams(interpolateVariables('${i}', context)),`:`      body: interpolateVariables('${i}', context),`),`    // API Request: ${n} ${a}
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), ${o});
      
      const ${d}Response = await fetch(interpolateVariables('${a}', context), {
        method: '${n}',
${l}
${m}
        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!${d}Response.ok) {
        throw new Error(\`HTTP error! status: \${${d}Response.status}\`);
      }
      
      const ${d}Data = await ${d}Response.json();
      console.log('API Response:', ${d}Data);
      
      // Store response in context for use in other blocks
      (context as any).${d} = ${d}Data;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('API request timed out after ${o}ms');
      } else {
        ${"ignore"===c?"// Errors ignored":"log"===c?"console.error('API request failed:', error);":"throw error;"}
      }
    }`}let h=(0,i.M)(a,"default"),b=(0,i.M)(a,"config"),y=new o.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/admin/experimental/addon-builder/build",pathname:"/api/admin/experimental/addon-builder/build",bundlePath:"",filename:""},userland:a})},8580:(e,t,n)=>{n.r(t),n.d(t,{dashboardConfig:()=>d,default:()=>c});var a=n(9021),o=n(2115),r=n.n(o),i=n(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");s=r().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let c=d},9021:e=>{e.exports=require("fs")},9646:e=>{e.exports=require("child_process")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var n=t(t.s=8543);module.exports=n})();