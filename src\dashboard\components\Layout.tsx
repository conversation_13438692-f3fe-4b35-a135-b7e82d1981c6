import { Box, Container } from '@chakra-ui/react';
import Navbar from './Navbar';
import Sidebar from './Sidebar';
import { useTheme } from '../contexts/ThemeContext';

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const { currentScheme } = useTheme();

  return (
    <Box
      minH="100vh"
      bg={currentScheme.colors.background}
      position="relative"
      overflow="hidden"
      _before={{
        content: '""',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bgImage: `
          radial-gradient(circle at 15% 50%, ${currentScheme.colors.primary}15 0%, transparent 25%),
          radial-gradient(circle at 85% 30%, ${currentScheme.colors.accent}15 0%, transparent 25%)
        `,
        zIndex: 0,
      }}
      _after={{
        content: '""',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backdropFilter: 'blur(100px)',
        zIndex: 0,
      }}
    >
      <Box position="relative" zIndex={1} display="flex" flexDirection="column" minH="100vh">
        <Box
          position="fixed"
          top={0}
          left={0}
          right={0}
          zIndex={30}
        >
          <Navbar />
        </Box>
        <Box display="flex" flex="1" position="relative" pt="4rem">
          <Box
            position="fixed"
            top="4rem"
            bottom={0}
            left={0}
            w="64"
            zIndex={20}
          >
            <Sidebar />
          </Box>
          <Box
            flex="1"
            ml="64"
            p={{ base: 4, md: 8 }}
            maxW="100%"
            transition="all 0.3s"
            position="relative"
            _before={{
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bg: 'linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)',
              pointerEvents: 'none',
              zIndex: -1,
            }}
          >
            <Container maxW="container.xl">
              {children}
            </Container>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default Layout; 