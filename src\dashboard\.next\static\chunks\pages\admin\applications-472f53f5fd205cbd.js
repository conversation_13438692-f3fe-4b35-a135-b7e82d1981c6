(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7453],{1341:(e,t,s)=>{"use strict";s.d(t,{Th:()=>a});var r=s(94513),i=s(15373),n=s(2923),l=s(33225);let a=(0,n.R)(({isNumeric:e,...t},s)=>{let n=(0,i.k)();return(0,r.jsx)(l.B.th,{...t,ref:s,__css:n.th,"data-is-numeric":e})})},8595:(e,t,s)=>{"use strict";s.d(t,{Tr:()=>a});var r=s(94513),i=s(15373),n=s(2923),l=s(33225);let a=(0,n.R)((e,t)=>{let s=(0,i.k)();return(0,r.jsx)(l.B.tr,{...e,ref:t,__css:s.tr})})},9875:(e,t,s)=>{"use strict";s.r(t),s.d(t,{__N_SSP:()=>U,default:()=>J});var r=s(94513),i=s(22907),n=s(95845),l=s(51961),a=s(57561),c=s(79156),d=s(78902),o=s(49217),u=s(31678),h=s(41611),p=s(25680),x=s(68443),j=s(59818),m=s(12183),y=s(56966),b=s(86153),f=s(84467),g=s(33225);let _=e=>(0,r.jsx)(o.I,{color:"red.400",...e,children:(0,r.jsx)("path",{fill:"currentColor",d:"M21,5H3C2.621,5,2.275,5.214,2.105,5.553C1.937,5.892,1.973,6.297,2.2,6.6l9,12 c0.188,0.252,0.485,0.4,0.8,0.4s0.611-0.148,0.8-0.4l9-12c0.228-0.303,0.264-0.708,0.095-1.047C21.725,5.214,21.379,5,21,5z"})});function v(e){return(0,r.jsx)(o.I,{color:"green.400",...e,children:(0,r.jsx)("path",{fill:"currentColor",d:"M12.8,5.4c-0.377-0.504-1.223-0.504-1.6,0l-9,12c-0.228,0.303-0.264,0.708-0.095,1.047 C2.275,18.786,2.621,19,3,19h18c0.379,0,0.725-0.214,0.895-0.553c0.169-0.339,0.133-0.744-0.095-1.047L12.8,5.4z"})})}function S(e){let{type:t,"aria-label":s,...i}=e,n=(0,m.E)(),l="increase"===t?v:_;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.B.span,{srOnly:!0,children:s||("increase"===t?"increased by":"decreased by")}),(0,r.jsx)(l,{"aria-hidden":!0,...i,__css:n.icon})]})}_.displayName="StatDownArrow",v.displayName="StatUpArrow",S.displayName="StatArrow";var w=s(26977),N=s(49451),T=s(52216),k=s(15373),A=s(51927),C=s(8595),E=s(1341),R=s(35981),B=s(95497),z=s(58517),L=s(71601),I=s(62690),M=s(9557),D=s(7680),W=s(52922),Z=s(47847),O=s(59365),F=s(85104),G=s(71185),P=s(50691),$=s(60341),q=s(94285),H=s(17842),U=!0;function J(){let[e,t]=(0,q.useState)([]),[s,g]=(0,q.useState)(!0),[_,v]=(0,q.useState)({total:0,pending:0,approved:0,rejected:0,recentIncrease:0}),U=(0,i.d)(),{isOpen:J,onOpen:X,onClose:Q}=(0,n.j)(),[V,K]=(0,q.useState)(null);(0,q.useEffect)(()=>{Y()},[]);let Y=async()=>{try{let e=await fetch("/api/admin/applications");if(e.ok){let s=await e.json();t(s.applications||[]),v(s.stats||_)}}catch(e){U({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{g(!1)}},ee=async(e,t)=>{try{(await fetch("".concat("/api/admin/applications"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationSubmissionId:e,action:t})})).ok&&(Y(),U({title:"Success",description:"Application ".concat(t,"d successfully"),status:"success",duration:3e3}),Q())}catch(e){U({title:"Error",description:"Failed to ".concat(t," application"),status:"error",duration:3e3})}},et=e=>{K(e),X()};return s?(0,r.jsx)($.A,{children:(0,r.jsx)(l.a,{p:8,display:"flex",justifyContent:"center",alignItems:"center",minH:"400px",children:(0,r.jsx)(a.y,{size:"xl"})})}):(0,r.jsxs)($.A,{children:[(0,r.jsx)(l.a,{p:8,children:(0,r.jsxs)(c.T,{align:"stretch",spacing:6,children:[(0,r.jsxs)(d.z,{children:[(0,r.jsx)(o.I,{as:H.t69,boxSize:6,color:"blue.500"}),(0,r.jsx)(u.D,{size:"lg",children:"Applications Management"})]}),(0,r.jsx)(h.E,{color:"gray.600",_dark:{color:"gray.300"},children:"Manage and review all user applications submitted through the application builder."}),(0,r.jsxs)(p.r,{columns:{base:1,md:4},spacing:6,children:[(0,r.jsx)(x.Z,{children:(0,r.jsx)(j.b,{children:(0,r.jsxs)(m.r,{children:[(0,r.jsx)(y.v,{children:"Total Applications"}),(0,r.jsx)(b.k,{children:_.total}),(0,r.jsxs)(f.h,{children:[(0,r.jsx)(S,{type:_.recentIncrease>=0?"increase":"decrease"}),Math.abs(_.recentIncrease),"% this month"]})]})})}),(0,r.jsx)(x.Z,{children:(0,r.jsx)(j.b,{children:(0,r.jsxs)(m.r,{children:[(0,r.jsx)(y.v,{children:"Pending Review"}),(0,r.jsx)(b.k,{color:"yellow.500",children:_.pending}),(0,r.jsx)(f.h,{children:"Requires attention"})]})})}),(0,r.jsx)(x.Z,{children:(0,r.jsx)(j.b,{children:(0,r.jsxs)(m.r,{children:[(0,r.jsx)(y.v,{children:"Approved"}),(0,r.jsx)(b.k,{color:"green.500",children:_.approved}),(0,r.jsx)(f.h,{children:"Accepted applications"})]})})}),(0,r.jsx)(x.Z,{children:(0,r.jsx)(j.b,{children:(0,r.jsxs)(m.r,{children:[(0,r.jsx)(y.v,{children:"Rejected"}),(0,r.jsx)(b.k,{color:"red.500",children:_.rejected}),(0,r.jsx)(f.h,{children:"Declined applications"})]})})})]}),(0,r.jsx)(l.a,{mt:8,children:0===e.length?(0,r.jsxs)(w.F,{status:"info",children:[(0,r.jsx)(N._,{}),(0,r.jsx)(T.T,{children:"No applications found. Applications submitted through the builder will appear here."})]}):(0,r.jsxs)(k.X,{variant:"simple",children:[(0,r.jsx)(A.d,{children:(0,r.jsxs)(C.Tr,{children:[(0,r.jsx)(E.Th,{children:"User"}),(0,r.jsx)(E.Th,{children:"Application Type"}),(0,r.jsx)(E.Th,{children:"Submitted On"}),(0,r.jsx)(E.Th,{children:"Status"}),(0,r.jsx)(E.Th,{children:"Actions"})]})}),(0,r.jsx)(R.N,{children:e.map(e=>(0,r.jsxs)(C.Tr,{children:[(0,r.jsx)(B.Td,{children:(0,r.jsxs)(d.z,{children:[(0,r.jsx)(z.e,{size:"sm",name:e.submittedBy}),(0,r.jsx)(h.E,{children:e.submittedBy})]})}),(0,r.jsx)(B.Td,{children:(0,r.jsxs)(L.E,{colorScheme:"purple",children:["Custom Application: ",e.applicationId]})}),(0,r.jsx)(B.Td,{children:new Date(e.submittedAt).toLocaleDateString()}),(0,r.jsx)(B.Td,{children:(0,r.jsx)(L.E,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",children:e.status})}),(0,r.jsx)(B.Td,{children:(0,r.jsxs)(d.z,{spacing:2,children:[(0,r.jsx)(I.$,{size:"sm",colorScheme:"blue",onClick:()=>et(e),children:"View"}),"pending"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(I.$,{size:"sm",colorScheme:"green",onClick:()=>ee(e._id,"approve"),children:"Accept"}),(0,r.jsx)(I.$,{size:"sm",colorScheme:"red",onClick:()=>ee(e._id,"reject"),children:"Reject"})]})]})})]},e._id))})]})})]})}),(0,r.jsxs)(M.aF,{isOpen:J,onClose:Q,size:"xl",children:[(0,r.jsx)(D.m,{}),(0,r.jsxs)(W.$,{children:[(0,r.jsx)(Z.r,{children:"Application Details"}),(0,r.jsx)(O.s,{}),(0,r.jsx)(F.c,{children:V&&(0,r.jsxs)(c.T,{align:"stretch",spacing:4,children:[(0,r.jsxs)(d.z,{justifyContent:"space-between",children:[(0,r.jsx)(h.E,{fontWeight:"bold",children:"Submitted By:"}),(0,r.jsx)(h.E,{children:V.submittedBy})]}),(0,r.jsxs)(d.z,{justifyContent:"space-between",children:[(0,r.jsx)(h.E,{fontWeight:"bold",children:"Submitted On:"}),(0,r.jsx)(h.E,{children:new Date(V.submittedAt).toLocaleDateString()})]}),(0,r.jsxs)(d.z,{justifyContent:"space-between",children:[(0,r.jsx)(h.E,{fontWeight:"bold",children:"Application Type:"}),(0,r.jsxs)(h.E,{children:["Custom Application: ",V.applicationId]})]}),(0,r.jsxs)(d.z,{justifyContent:"space-between",children:[(0,r.jsx)(h.E,{fontWeight:"bold",children:"Status:"}),(0,r.jsx)(L.E,{colorScheme:"approved"===V.status?"green":"rejected"===V.status?"red":"yellow",children:V.status})]}),(0,r.jsx)(G.c,{}),(0,r.jsx)(h.E,{fontWeight:"bold",children:"Answers:"}),(0,r.jsx)(P.B8,{spacing:2,children:Object.entries(V.answers).map(e=>{let[t,s]=e;return(0,r.jsx)(P.ck,{children:(0,r.jsxs)(h.E,{children:[(0,r.jsxs)("strong",{children:[t,":"]})," ",Array.isArray(s)?s.join(", "):s]})},t)})}),V.reviewedBy&&(0,r.jsxs)(d.z,{justifyContent:"space-between",children:[(0,r.jsx)(h.E,{fontWeight:"bold",children:"Reviewed By:"}),(0,r.jsx)(h.E,{children:V.reviewedBy})]}),V.reviewedAt&&(0,r.jsxs)(d.z,{justifyContent:"space-between",children:[(0,r.jsx)(h.E,{fontWeight:"bold",children:"Reviewed At:"}),(0,r.jsx)(h.E,{children:new Date(V.reviewedAt).toLocaleDateString()})]})]})})]})]})]})}},12183:(e,t,s)=>{"use strict";s.d(t,{E:()=>u,r:()=>h});var r=s(94513),i=s(75387),n=s(29035),l=s(22697),a=s(2923),c=s(56915),d=s(33225);let[o,u]=(0,n.q)({name:"StatStylesContext",errorMessage:"useStatStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Stat />\" "}),h=(0,a.R)(function(e,t){let s=(0,c.o)("Stat",e),n={position:"relative",flex:"1 1 0%",...s.container},{className:a,children:u,...h}=(0,i.M)(e);return(0,r.jsx)(o,{value:s,children:(0,r.jsx)(d.B.div,{ref:t,...h,className:(0,l.cx)("chakra-stat",a),__css:n,children:(0,r.jsx)("dl",{children:u})})})});h.displayName="Stat"},15373:(e,t,s)=>{"use strict";s.d(t,{X:()=>h,k:()=>u});var r=s(94513),i=s(75387),n=s(29035),l=s(22697),a=s(2923),c=s(56915),d=s(33225);let[o,u]=(0,n.q)({name:"TableStylesContext",errorMessage:"useTableStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Table />\" "}),h=(0,a.R)((e,t)=>{let s=(0,c.o)("Table",e),{className:n,layout:a,...u}=(0,i.M)(e);return(0,r.jsx)(o,{value:s,children:(0,r.jsx)(d.B.table,{ref:t,__css:{tableLayout:a,...s.table},className:(0,l.cx)("chakra-table",n),...u})})});h.displayName="Table"},22907:(e,t,s)=>{"use strict";s.d(t,{d:()=>d});var r=s(94285),i=s(69012),n=s(96481),l=s(84860),a=s(18859),c=s(79364);function d(e){let{theme:t}=(0,c.UQ)(),s=(0,a.NU)();return(0,r.useMemo)(()=>(function(e,t){let s=s=>({...t,...s,position:function(e,t){let s=e??"bottom";return({"top-start":{ltr:"top-left",rtl:"top-right"},"top-end":{ltr:"top-right",rtl:"top-left"},"bottom-start":{ltr:"bottom-left",rtl:"bottom-right"},"bottom-end":{ltr:"bottom-right",rtl:"bottom-left"}})[s]?.[t]??s}(s?.position??t?.position,e)}),r=e=>{let t=s(e),r=(0,n.q)(t);return l.Z.notify(r,t)};return r.update=(e,t)=>{l.Z.update(e,s(t))},r.promise=(e,t)=>{let s=r({...t.loading,status:"loading",duration:null});e.then(e=>r.update(s,{status:"success",duration:5e3,...(0,i.J)(t.success,e)})).catch(e=>r.update(s,{status:"error",duration:5e3,...(0,i.J)(t.error,e)}))},r.closeAll=l.Z.closeAll,r.close=l.Z.close,r.isActive=l.Z.isActive,r})(t.direction,{...s,...e}),[e,t.direction,s])}},25680:(e,t,s)=>{"use strict";s.d(t,{r:()=>o});var r=s(94513),i=s(58714),n=s(2923),l=s(33225);let a=(0,n.R)(function(e,t){let{templateAreas:s,gap:i,rowGap:n,columnGap:a,column:c,row:d,autoFlow:o,autoRows:u,templateRows:h,autoColumns:p,templateColumns:x,...j}=e;return(0,r.jsx)(l.B.div,{ref:t,__css:{display:"grid",gridTemplateAreas:s,gridGap:i,gridRowGap:n,gridColumnGap:a,gridAutoColumns:p,gridColumn:c,gridRow:d,gridAutoFlow:o,gridAutoRows:u,gridTemplateRows:h,gridTemplateColumns:x},...j})});a.displayName="Grid";var c=s(83745),d=s(79364);let o=(0,n.R)(function(e,t){var s,n,l;let{columns:o,spacingX:u,spacingY:h,spacing:p,minChildWidth:x,...j}=e,m=(0,c.D)(),y=x?(s=x,n=m,(0,i.bk)(s,e=>{let t=(0,d.gf)("sizes",e,"number"==typeof e?`${e}px`:e)(n);return null===e?null:`repeat(auto-fit, minmax(${t}, 1fr))`})):(l=o,(0,i.bk)(l,e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`));return(0,r.jsx)(a,{ref:t,gap:p,columnGap:u,rowGap:h,templateColumns:y,...j})});o.displayName="SimpleGrid"},35981:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});var r=s(94513),i=s(15373),n=s(2923),l=s(33225);let a=(0,n.R)((e,t)=>{let s=(0,i.k)();return(0,r.jsx)(l.B.tbody,{...e,ref:t,__css:s.tbody})})},50691:(e,t,s)=>{"use strict";s.d(t,{B8:()=>p,ck:()=>x});var r=s(94513),i=s(75387),n=s(29035),l=s(47133),a=s(49217),c=s(2923),d=s(56915),o=s(33225);let[u,h]=(0,n.q)({name:"ListStylesContext",errorMessage:"useListStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<List />\" "}),p=(0,c.R)(function(e,t){let s=(0,d.o)("List",e),{children:n,styleType:a="none",stylePosition:c,spacing:h,...p}=(0,i.M)(e),x=(0,l.a)(n);return(0,r.jsx)(u,{value:s,children:(0,r.jsx)(o.B.ul,{ref:t,listStyleType:a,listStylePosition:c,role:"list",__css:{...s.container,...h?{"& > *:not(style) ~ *:not(style)":{mt:h}}:{}},...p,children:x})})});p.displayName="List",(0,c.R)((e,t)=>{let{as:s,...i}=e;return(0,r.jsx)(p,{ref:t,as:"ol",styleType:"decimal",marginStart:"1em",...i})}).displayName="OrderedList",(0,c.R)(function(e,t){let{as:s,...i}=e;return(0,r.jsx)(p,{ref:t,as:"ul",styleType:"initial",marginStart:"1em",...i})}).displayName="UnorderedList";let x=(0,c.R)(function(e,t){let s=h();return(0,r.jsx)(o.B.li,{ref:t,...e,__css:s.item})});x.displayName="ListItem",(0,c.R)(function(e,t){let s=h();return(0,r.jsx)(a.I,{ref:t,role:"presentation",...e,__css:s.icon})}).displayName="ListIcon"},51413:(e,t,s)=>{"use strict";s.d(t,{Q:()=>i,s:()=>r});let[r,i]=(0,s(1e3).Wh)("Card")},51927:(e,t,s)=>{"use strict";s.d(t,{d:()=>a});var r=s(94513),i=s(15373),n=s(2923),l=s(33225);let a=(0,n.R)((e,t)=>{let s=(0,i.k)();return(0,r.jsx)(l.B.thead,{...e,ref:t,__css:s.thead})})},56966:(e,t,s)=>{"use strict";s.d(t,{v:()=>c});var r=s(94513),i=s(22697),n=s(12183),l=s(2923),a=s(33225);let c=(0,l.R)(function(e,t){let s=(0,n.E)();return(0,r.jsx)(a.B.dt,{ref:t,...e,className:(0,i.cx)("chakra-stat__label",e.className),__css:s.label})});c.displayName="StatLabel"},59365:(e,t,s)=>{"use strict";s.d(t,{s:()=>c});var r=s(94513),i=s(22697),n=s(50614),l=s(9557),a=s(33021);let c=(0,s(2923).R)((e,t)=>{let{onClick:s,className:c,...d}=e,{onClose:o}=(0,l.k3)(),u=(0,i.cx)("chakra-modal__close-btn",c),h=(0,l.x5)();return(0,r.jsx)(a.J,{ref:t,__css:h.closeButton,className:u,onClick:(0,n.H)(s,e=>{e.stopPropagation(),o()}),...d})});c.displayName="ModalCloseButton"},59818:(e,t,s)=>{"use strict";s.d(t,{b:()=>c});var r=s(94513),i=s(22697),n=s(51413),l=s(2923),a=s(33225);let c=(0,l.R)(function(e,t){let{className:s,...l}=e,c=(0,n.Q)();return(0,r.jsx)(a.B.div,{ref:t,className:(0,i.cx)("chakra-card__body",s),__css:c.body,...l})})},68443:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var r=s(94513),i=s(75387),n=s(22697),l=s(51413),a=s(2923),c=s(56915),d=s(33225);let o=(0,a.R)(function(e,t){let{className:s,children:a,direction:o="column",justify:u,align:h,...p}=(0,i.M)(e),x=(0,c.o)("Card",e);return(0,r.jsx)(d.B.div,{ref:t,className:(0,n.cx)("chakra-card",s),__css:{display:"flex",flexDirection:o,justifyContent:u,alignItems:h,position:"relative",minWidth:0,wordWrap:"break-word",...x.container},...p,children:(0,r.jsx)(l.s,{value:x,children:a})})})},77087:(e,t,s)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/applications",function(){return s(9875)}])},84467:(e,t,s)=>{"use strict";s.d(t,{h:()=>c});var r=s(94513),i=s(22697),n=s(12183),l=s(2923),a=s(33225);let c=(0,l.R)(function(e,t){let s=(0,n.E)();return(0,r.jsx)(a.B.dd,{ref:t,...e,className:(0,i.cx)("chakra-stat__help-text",e.className),__css:s.helpText})});c.displayName="StatHelpText"},86153:(e,t,s)=>{"use strict";s.d(t,{k:()=>c});var r=s(94513),i=s(22697),n=s(12183),l=s(2923),a=s(33225);let c=(0,l.R)(function(e,t){let s=(0,n.E)();return(0,r.jsx)(a.B.dd,{ref:t,...e,className:(0,i.cx)("chakra-stat__number",e.className),__css:{...s.number,fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums"}})});c.displayName="StatNumber"},95497:(e,t,s)=>{"use strict";s.d(t,{Td:()=>a});var r=s(94513),i=s(15373),n=s(2923),l=s(33225);let a=(0,n.R)(({isNumeric:e,...t},s)=>{let n=(0,i.k)();return(0,r.jsx)(l.B.td,{...t,ref:s,__css:n.td,"data-is-numeric":e})})}},e=>{var t=t=>e(e.s=t);e.O(0,[4108,3256,9998,4976,217,2965,341,636,6593,8792],()=>t(77087)),_N_E=e.O()}]);