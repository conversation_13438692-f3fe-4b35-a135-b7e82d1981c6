import React from 'react';
import { Box, VStack, HStack, Text, Heading, Icon } from '@chakra-ui/react';
import Link from 'next/link';

export interface OverviewCardProps {
  title: string;
  description: string;
  icon: any;
  href: string;
  color: string;
  gradient?: {
    from: string;
    to: string;
  };
  accentColor?: string;
  disabled?: boolean;
  experimental?: boolean;
}

export const OverviewCard: React.FC<OverviewCardProps> = ({ 
  title, 
  description, 
  icon, 
  href, 
  color,
  gradient,
  accentColor,
  disabled = false,
  experimental = false
}) => {
  const isClickable = href && href !== '#' && !disabled;
  
  const cardContent = (
    <Box
      px={10}
      py={5}
      bg={gradient ? `linear-gradient(135deg, ${gradient.from}, ${gradient.to})` : "gray.800"}
      borderRadius="lg"
      border="1px solid"
      borderColor={disabled ? "whiteAlpha.100" : "whiteAlpha.200"}
      transition="all 0.3s"
      h="140px"
      minW="360px"
      w="full"
      overflow="hidden"
      display="flex"
      flexDirection="column"
      cursor={isClickable ? "pointer" : disabled ? "not-allowed" : "default"}
      position="relative"
      opacity={disabled ? 0.6 : 1}
      _before={{
        content: '""',
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: experimental ? 
          "repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(0,0,0,0.1) 10px, rgba(0,0,0,0.1) 20px)" : 
          "none",
        opacity: 0.5,
        pointerEvents: "none"
      }}
      _hover={isClickable ? { 
        transform: 'translateY(-3px)', 
        boxShadow: `0 6px 14px ${accentColor || `var(--chakra-colors-${color}-900)`}40`,
        borderColor: `${color}.400`,
        _before: {
          opacity: 0.7
        }
      } : {}}
    >
      <VStack spacing={4} align="start" flex="1" justify="flex-start" h="full" position="relative" zIndex={1}>
        <HStack spacing={3}>
          <Icon 
            as={icon} 
            boxSize={6} 
            color={accentColor || `${color}.300`}
            filter={experimental ? "drop-shadow(0 0 2px currentColor)" : "none"}
          />
          <Heading size="md" color="white" noOfLines={1} whiteSpace="nowrap">
            {title}
          </Heading>
        </HStack>
        <Text 
          color={disabled ? "gray.500" : "gray.300"}
          fontSize="sm" 
          lineHeight="1.4" 
          noOfLines={3}
          overflow="hidden"
          textOverflow="ellipsis"
          flex="1"
        >
          {description}
        </Text>
      </VStack>
    </Box>
  );

  if (isClickable) {
    return (
      <Link href={href} passHref>
        {cardContent}
      </Link>
    );
  }

  return cardContent;
};

 