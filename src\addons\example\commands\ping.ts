import { <PERSON><PERSON><PERSON><PERSON>mand<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('ping')
  .setDescription('Shows bot latency and status information');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  const startTime = Date.now();
  
  // Get basic info quickly
  const wsLatency = bot.client.ws.ping;
  const memoryUsage = process.memoryUsage();
  const uptime = process.uptime();
  
  // Determine latency quality
  const getLatencyStatus = (ping: number) => {
    if (ping < 100) return { emoji: '🟢', status: 'Excellent' };
    if (ping < 200) return { emoji: '🟡', status: 'Good' };
    if (ping < 500) return { emoji: '🟠', status: 'Fair' };
    return { emoji: '🔴', status: 'Poor' };
  };
  
  const latencyInfo = getLatencyStatus(wsLatency);
  
  // Send initial response to measure API latency safely
  try {
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({ content: 'Calculating latency...' });
    } else {
      await interaction.followUp({ content: 'Calculating latency...' });
    }
  } catch (err: any) {
    // If the interaction was already acknowledged, fall back to followUp
    if (err?.name === 'InteractionAlreadyReplied' || err?.code === 40060) {
      await interaction.followUp({ content: 'Calculating latency...' });
    } else {
      throw err;
    }
  }
  
  // Calculate response time after sending the reply
  const responseTime = Date.now() - startTime;
  
  const embed = new EmbedBuilder()
    .setColor(wsLatency < 100 ? 0x00FF00 : wsLatency < 200 ? 0xFFFF00 : 0xFF8C00)
    .setTitle('🏓 Pong! - Bot Status')
    .setDescription(`${latencyInfo.emoji} **${latencyInfo.status}** connection quality`)
    .addFields(
      {
        name: '⚡ Latency Information',
        value: `\`\`\`yaml
WebSocket: ${wsLatency}ms
Response:  ${responseTime}ms
Status:    ${latencyInfo.status}
\`\`\``,
        inline: true
      },
      {
        name: '📊 System Status',
        value: `\`\`\`yaml
Uptime:    ${formatUptime(uptime)}
Memory:    ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB
Guilds:    ${bot.client.guilds.cache.size}
Users:     ${bot.client.users.cache.size}
\`\`\``,
        inline: true
      },
      {
        name: '🔧 Bot Information',
        value: `\`\`\`yaml
Commands:  ${bot.commands.size}
Addons:    ${bot.addons.size}
Database:  ${bot.database ? '🟢 Online' : '🔴 Offline'}
Node.js:   ${process.version}
\`\`\``,
        inline: true
      }
    )
    .setFooter({ 
      text: `Requested by ${interaction.user.tag}`,
      iconURL: interaction.user.displayAvatarURL()
    })
    .setTimestamp();

  // Edit the reply with the embed
  try {
    if (interaction.replied && !interaction.deferred) {
      // If we were able to reply initially, edit that message
      await interaction.editReply({ content: null, embeds: [embed] });
    } else {
      // Otherwise send a follow-up with the embed
      await interaction.followUp({ embeds: [embed] });
    }
  } catch (err: any) {
    // Fallback to followUp in case the original message cannot be edited
    await interaction.followUp({ embeds: [embed] });
  }
}

function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) return `${days}d ${hours}h ${minutes}m`;
  if (hours > 0) return `${hours}h ${minutes}m`;
  if (minutes > 0) return `${minutes}m`;
  return `${Math.floor(seconds)}s`;
}

export const cooldown = 3000; 