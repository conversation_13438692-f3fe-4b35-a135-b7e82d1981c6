(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3689],{35107:(e,i,t)=>{"use strict";t.r(i),t.d(i,{__N_SSP:()=>T,default:()=>k});var l=t(94513),a=t(60341),n=t(22907),s=t(76857),r=t(79156),o=t(57561),d=t(31678),c=t(51961),p=t(26977),u=t(49451),h=t(52216),x=t(41611),g=t(40443),j=t(63730),b=t(64057),y=t(7627),f=t(25964),m=t(73496),S=t(78902),_=t(3177),w=t(22237),A=t(62690),v=t(58686),C=t(94285),T=!0;function k(){let{id:e}=(0,v.useRouter)().query,[i,t]=(0,C.useState)(null),[T,k]=(0,C.useState)(!0),[E,q]=(0,C.useState)(null),[z,F]=(0,C.useState)({}),[N,P]=(0,C.useState)(!1),D=(0,n.d)();(0,C.useEffect)(()=>{e&&(async()=>{try{let i=await fetch("/api/admin/applications-builder/".concat(e));if(!i.ok)throw Error("Failed to fetch application: ".concat(i.statusText));let l=await i.json();if(l.application){t(l.application);let e={};l.application.questions.forEach(i=>{"checkbox"===i.type?e[i.id]=[]:"select"===i.type||"radio"===i.type?e[i.id]=i.options&&i.options.length>0?i.options[0]:"":e[i.id]=""}),F(e)}else q("Application not found")}catch(e){q(e.message)}finally{k(!1)}})()},[e]);let O=(e,i,t)=>{F(l=>{if("checkbox"===t){let t=l[e]||[];return t.includes(i)?{...l,[e]:t.filter(e=>e!==i)}:{...l,[e]:[...t,i]}}return{...l,[e]:i}})},R=async e=>{if(e.preventDefault(),P(!0),q(null),!i){q("Application data not loaded."),P(!1);return}for(let e of i.questions)if(e.required&&(void 0===z[e.id]||""===z[e.id]||Array.isArray(z[e.id])&&0===z[e.id].length)){D({title:"Missing Required Field",description:"Please fill in the '".concat(e.label,"' field."),status:"error",duration:5e3,isClosable:!0}),P(!1);return}try{let e=await fetch("/api/applications/submit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationId:i.id,answers:z})});if(!e.ok){let i=await e.json();throw Error(i.error||"Failed to submit application")}D({title:"Application Submitted",description:"Your application has been successfully submitted!",status:"success",duration:5e3,isClosable:!0}),F({})}catch(e){q(e.message),D({title:"Submission Failed",description:e.message,status:"error",duration:5e3,isClosable:!0})}finally{P(!1)}};return T?(0,l.jsx)(a.A,{children:(0,l.jsx)(s.o,{p:8,children:(0,l.jsxs)(r.T,{spacing:4,children:[(0,l.jsx)(o.y,{size:"xl"}),(0,l.jsx)(d.D,{size:"md",children:"Loading application..."})]})})}):E?(0,l.jsx)(a.A,{children:(0,l.jsx)(c.a,{p:8,children:(0,l.jsxs)(p.F,{status:"error",children:[(0,l.jsx)(u._,{}),(0,l.jsx)(h.T,{children:E})]})})}):i?(0,l.jsx)(a.A,{children:(0,l.jsxs)(r.T,{spacing:8,p:8,align:"stretch",children:[(0,l.jsx)(d.D,{size:"xl",textAlign:"center",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",children:i.title}),(0,l.jsx)(x.E,{fontSize:"lg",textAlign:"center",color:"gray.400",children:i.description}),(0,l.jsxs)(c.a,{as:"form",onSubmit:R,mt:8,p:6,borderWidth:"1px",borderRadius:"lg",borderColor:"whiteAlpha.300",bg:"whiteAlpha.05",children:[(0,l.jsx)(d.D,{size:"md",mb:6,children:"Application Form"}),(0,l.jsxs)(r.T,{spacing:6,align:"stretch",children:[i.questions.map(e=>(0,l.jsxs)(g.MJ,{isRequired:e.required,children:[(0,l.jsx)(j.l,{children:e.label}),"text"===e.type&&(0,l.jsx)(b.p,{type:"text",placeholder:e.placeholder,value:z[e.id]||"",onChange:i=>O(e.id,i.target.value,e.type)}),"number"===e.type&&(0,l.jsx)(b.p,{type:"number",placeholder:e.placeholder,value:z[e.id]||"",onChange:i=>O(e.id,i.target.value,e.type)}),"textarea"===e.type&&(0,l.jsx)(y.T,{placeholder:e.placeholder,value:z[e.id]||"",onChange:i=>O(e.id,i.target.value,e.type)}),"select"===e.type&&e.options&&(0,l.jsx)(f.l,{placeholder:e.placeholder||"Select an option",value:z[e.id]||"",onChange:i=>O(e.id,i.target.value,e.type),children:e.options.map(e=>(0,l.jsx)("option",{value:e,children:e},e))}),"radio"===e.type&&e.options&&(0,l.jsx)(m.z,{value:z[e.id]||"",onChange:i=>O(e.id,i,e.type),children:(0,l.jsx)(S.z,{spacing:"24px",children:e.options.map(e=>(0,l.jsx)(_.s,{value:e,children:e},e))})}),"checkbox"===e.type&&e.options&&(0,l.jsx)(r.T,{align:"stretch",children:e.options.map(i=>(0,l.jsx)(w.S,{isChecked:(z[e.id]||[]).includes(i),onChange:()=>O(e.id,i,e.type),children:i},i))})]},e.id)),(0,l.jsx)(A.$,{type:"submit",colorScheme:"blue",size:"lg",isLoading:N,loadingText:"Submitting...",alignSelf:"flex-end",mt:8,children:"Submit Application"})]})]})]})}):(0,l.jsx)(a.A,{children:(0,l.jsx)(c.a,{p:8,children:(0,l.jsxs)(p.F,{status:"warning",children:[(0,l.jsx)(u._,{}),(0,l.jsx)(h.T,{children:"Application data could not be loaded."})]})})})}},71425:(e,i,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/applications/[id]",function(){return t(35107)}])}},e=>{var i=i=>e(e.s=i);e.O(0,[4108,4976,217,2965,3177,145,341,636,6593,8792],()=>i(71425)),_N_E=e.O()}]);