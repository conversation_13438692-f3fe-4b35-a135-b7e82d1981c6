"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[341],{52826:(e,r,o)=>{o.d(r,{A:()=>l});var t=o(69776),n=o(94285),a=o(53424);let i=async e=>{let r=await fetch(e);if(!r.ok){if(401===r.status)return{name:"404 Bot",botName:"404 Bot"};throw Error("Failed to fetch guild info")}return r.json()};function l(){let{data:e,status:r}=(0,a.useSession)(),o="authenticated"===r,{data:l,error:s}=(0,t.Ay)(o?"/api/discord/guild":null,i,{revalidateOnFocus:!1,revalidateOnReconnect:!1}),[c,d]=(0,n.useState)(()=>localStorage.getItem("dashboardDisplayNamePref")||"guild"),p=(0,n.useCallback)(e=>{d(e),localStorage.setItem("dashboardDisplayNamePref",e),window.dispatchEvent(new CustomEvent("displayNamePrefChanged",{detail:e}))},[]);(0,n.useEffect)(()=>{let e=e=>{(null==e?void 0:e.detail)&&d(e.detail)},r=e=>{"dashboardDisplayNamePref"===e.key&&d(e.newValue||"guild")};return window.addEventListener("displayNamePrefChanged",e),window.addEventListener("storage",r),()=>{window.removeEventListener("displayNamePrefChanged",e),window.removeEventListener("storage",r)}},[]);let h="404 Bot Dashboard",x=h;return l&&(x="bot"===c&&l.botName?l.botName:l.name||h),{guild:l,displayName:x,pref:c,updatePreference:p,isLoading:o&&!s&&!l,isError:!!s}}},60341:(e,r,o)=>{o.d(r,{A:()=>T});var t=o(94513),n=o(51961),a=o(59001),i=o(19521),l=o(31678),s=o(78902),c=o(91169),d=o(87528),p=o(62690),h=o(58517),x=o(41611),g=o(18480),m=o(65104),f=o(53424),b=o(97146),u=o(52826),y=o(49980),j=o(70486),v=o(24792),w=o(73011),S=o(71601),k=o(88437),C=o(23635),_=o(63124),z=o(79156),I=o(94285);function E(){let{data:e}=(0,f.useSession)(),[r]=(0,I.useState)([]);return(null==e?void 0:e.user)?(0,t.jsxs)(y.A,{placement:"bottom-end",children:[(0,t.jsx)(j.W,{children:(0,t.jsxs)(n.a,{position:"relative",children:[(0,t.jsx)(v.m,{label:"Notifications",placement:"bottom",children:(0,t.jsx)(w.K,{"aria-label":"Notifications",icon:(0,t.jsx)(b.zd,{}),variant:"ghost",size:"sm",color:"gray.300",_hover:{bg:"whiteAlpha.200",color:"white",transform:"scale(1.05)"},transition:"all 0.2s"})}),!1]})}),(0,t.jsxs)(k.h,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"2xl",maxW:"400px",_focus:{boxShadow:"2xl"},children:[(0,t.jsx)(C.D,{borderBottomColor:"whiteAlpha.200",fontWeight:"semibold",fontSize:"lg",color:"white",children:"Notifications"}),(0,t.jsx)(_.e,{maxH:"400px",overflowY:"auto",children:(0,t.jsx)(z.T,{spacing:0,align:"stretch",children:r&&0!==r.length?(r||[]).map(e=>(0,t.jsxs)(n.a,{p:3,borderBottom:"1px",borderColor:"whiteAlpha.100",children:[(0,t.jsx)(x.E,{fontSize:"sm",color:"white",fontWeight:"medium",children:e.title}),(0,t.jsx)(x.E,{fontSize:"xs",color:"gray.400",mt:1,children:e.message})]},e.id)):(0,t.jsx)(n.a,{py:8,textAlign:"center",children:(0,t.jsx)(x.E,{color:"gray.400",fontSize:"sm",children:"No notifications yet"})})})})]})]}):null}function A(){var e,r;let{data:o}=(0,f.useSession)(),{displayName:a}=(0,u.A)();return(0,t.jsx)(n.a,{px:6,py:2,bg:"rgba(255,255,255,0.05)",backdropFilter:"blur(20px)",borderBottom:"1px solid",borderColor:"whiteAlpha.200",position:"sticky",top:0,zIndex:1e3,_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))",zIndex:-1},children:(0,t.jsxs)(i.s,{h:16,alignItems:"center",justifyContent:"space-between",children:[(0,t.jsx)(n.a,{flex:"1",children:(0,t.jsx)(l.D,{as:"h1",fontSize:"xl",bgGradient:"linear(to-r, blue.400, purple.400)",bgClip:"text",_hover:{bgGradient:"linear(to-r, blue.300, purple.300)",transform:"scale(1.02)"},transition:"all 0.2s",children:a?"".concat(a," Dashboard"):"Bot Dashboard"})}),(0,t.jsx)(n.a,{flex:"1",display:"flex",justifyContent:"flex-end",children:(0,t.jsx)(i.s,{alignItems:"center",gap:4,children:(null==o?void 0:o.user)?(0,t.jsxs)(s.z,{spacing:4,children:[(0,t.jsx)(E,{}),(0,t.jsxs)(c.W,{children:[(0,t.jsx)(d.I,{as:p.$,variant:"ghost",size:"sm",px:2,py:1,borderRadius:"full",_hover:{bg:"whiteAlpha.200"},children:(0,t.jsxs)(s.z,{spacing:2,children:[(0,t.jsx)(h.e,{size:"sm",name:null!=(e=o.user.name)?e:void 0,src:null!=(r=o.user.image)?r:void 0,borderWidth:2,borderColor:"blue.400",_hover:{borderColor:"purple.400",transform:"scale(1.05)"},transition:"all 0.2s"}),(0,t.jsx)(x.E,{color:"gray.300",display:{base:"none",md:"block"},children:o.user.name})]})}),(0,t.jsx)(g.c,{bg:"gray.800",borderColor:"whiteAlpha.200",boxShadow:"lg",_hover:{borderColor:"blue.400"},children:(0,t.jsx)(m.D,{icon:(0,t.jsx)(b.QeK,{}),onClick:()=>(0,f.signOut)(),_hover:{bg:"whiteAlpha.200",color:"red.400"},children:"Sign out"})})]})]}):(0,t.jsx)(p.$,{onClick:()=>(0,f.signIn)("discord",{callbackUrl:"/overview"}),bgGradient:"linear(to-r, blue.500, purple.500)",color:"white",_hover:{bgGradient:"linear(to-r, blue.400, purple.400)",transform:"translateY(-1px)"},_active:{bgGradient:"linear(to-r, blue.600, purple.600)",transform:"translateY(1px)"},transition:"all 0.2s",children:"Login with Discord"})})})]})})}var D=o(49217),N=o(83901),W=o(65319),G=o(71185),L=o(24251),O=o.n(L),B=o(58686),P=o(12772);let R="1.0.0";function H(){var e,r;let{data:o}=(0,f.useSession)(),a=(0,B.useRouter)(),i=null==o||null==(e=o.user)?void 0:e.isAdmin;null==o||null==(r=o.user)||r.id;let[l,s]=(0,I.useState)(!1),[c,d]=(0,I.useState)(!1),[p,h]=(0,I.useState)(!1),[g,m]=(0,I.useState)([]),{displayName:y}=(0,u.A)(),{currentScheme:j}=(0,P.DP)();(0,I.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/applications/config"),r=await e.json();if(h(r.isOpen||r.open),r.isOpen||r.open){let e=await fetch("/api/admin/applications-builder"),r=await e.json(),o=new Date,t=r.applications.filter(e=>{var r,t;if(!e.enabled)return!1;if(null==(t=e.settings)||null==(r=t.openingSchedule)?void 0:r.enabled){let r=new Date(e.settings.openingSchedule.startDate),t=new Date(e.settings.openingSchedule.endDate);return o>=r&&o<=t}return!0});m(t)}}catch(e){h(!1),m([])}})()},[]);let w=[{name:"Overview",icon:b.V5Y,href:"/overview"},{name:"Applications",icon:b.est,href:"/applications"},{name:"Tickets",icon:b.lrG,href:"/tickets"},{name:"Game Servers",icon:b.ufi,href:"/gameservers"}],k=[{name:"Server Management",href:"/admin/guilds",icon:b.VSk},{name:"Addons",href:"/admin/addons",icon:b.X3y},{name:"Errors",href:"/admin/errors",icon:b.y3G}],C=e=>"/overview"===e?a.pathname===e:a.pathname.startsWith(e);return(0,t.jsxs)(n.a,{as:"nav",h:"100%",bg:j.colors.surface,backdropFilter:"blur(20px)",borderRight:"1px solid",borderColor:j.colors.border,py:8,display:"flex",flexDirection:"column",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(180deg, ".concat(j.colors.primary,"15 0%, ").concat(j.colors.accent,"15 100%)"),zIndex:-1},children:[(0,t.jsxs)(z.T,{spacing:2,align:"stretch",flex:"1",children:[w.map(e=>{let r=C(e.href);return"Applications"===e.name?(0,t.jsxs)(z.T,{spacing:0,align:"stretch",children:[(0,t.jsxs)(n.a,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:c||C(e.href)?j.colors.text:j.colors.textSecondary,bg:C(e.href)?"".concat(j.colors.primary,"30"):"transparent",_hover:{bg:C(e.href)?"".concat(j.colors.primary,"40"):j.colors.surface,color:j.colors.text,transform:"translateX(4px)"},_active:{bg:"".concat(j.colors.primary,"50")},borderRight:C(e.href)?"2px solid":"none",borderColor:C(e.href)?j.colors.primary:"transparent",transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>d(!c),children:[(0,t.jsx)(D.I,{as:e.icon,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(x.E,{display:{base:"none",lg:"block"},bgGradient:C(e.href)?"linear(to-r, ".concat(j.colors.primaryLight,", ").concat(j.colors.accent,")"):"none",bgClip:C(e.href)?"text":"none",transition:"all 0.2s",flex:"1",children:e.name}),(0,t.jsx)(D.I,{as:b.fK4,w:4,h:4,ml:2,transition:"all 0.2s",transform:c?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,t.jsx)(N.S,{in:c,animateOpacity:!0,children:(0,t.jsx)(z.T,{spacing:1,align:"stretch",pl:4,py:2,children:g.length>0?g.map(e=>(0,t.jsx)(W.N,{as:O(),href:"/applications/".concat(e.id),display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:C("/applications/".concat(e.id))?j.colors.text:j.colors.textSecondary,bg:C("/applications/".concat(e.id))?"".concat(j.colors.primary,"20"):"transparent",_hover:{bg:j.colors.surface,color:j.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:(0,t.jsx)(x.E,{display:{base:"none",lg:"block"},children:e.title})},e.id)):(0,t.jsx)(x.E,{px:4,py:2,fontSize:"xs",color:j.colors.textSecondary,display:{base:"none",lg:"block"},children:"No open applications"})})})]},e.name):(0,t.jsx)(v.m,{label:e.name,placement:"right",hasArrow:!0,gutter:20,openDelay:500,display:{base:"block","2xl":"none"},children:(0,t.jsxs)(W.N,{as:O(),href:e.href,display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:r?j.colors.text:j.colors.textSecondary,bg:r?"".concat(j.colors.primary,"30"):"transparent",_hover:{bg:r?"".concat(j.colors.primary,"40"):j.colors.surface,color:j.colors.text,transform:"translateX(4px)"},_active:{bg:"".concat(j.colors.primary,"50")},borderRight:r?"2px solid":"none",borderColor:r?j.colors.primary:"transparent",transition:"all 0.2s",role:"group",children:[(0,t.jsx)(D.I,{as:e.icon,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(x.E,{display:{base:"none",lg:"block"},bgGradient:r?"linear(to-r, ".concat(j.colors.primaryLight,", ").concat(j.colors.accent,")"):"none",bgClip:r?"text":"none",transition:"all 0.2s",children:e.name}),"Applications"===e.name&&!p&&(0,t.jsx)(S.E,{ml:2,colorScheme:"red",variant:"solid",fontSize:"xs",display:{base:"none",lg:"block"},children:"Closed"}),"Applications"===e.name&&g.length>0&&(0,t.jsxs)(S.E,{ml:2,colorScheme:"green",variant:"solid",fontSize:"xs",display:{base:"none",lg:"block"},children:[g.length," Open"]})]})},e.name)}),i&&(0,t.jsxs)(z.T,{spacing:0,align:"stretch",children:[(0,t.jsxs)(n.a,{display:"flex",alignItems:"center",px:4,py:3,fontSize:"sm",fontWeight:"medium",color:j.colors.textSecondary,bg:"transparent",_hover:{bg:j.colors.surface,color:j.colors.text,transform:"translateX(4px)"},transition:"all 0.2s",cursor:"pointer",role:"group",onClick:()=>s(!l),children:[(0,t.jsx)(D.I,{as:b.LIi,w:5,h:5,mr:3,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(x.E,{display:{base:"none",lg:"block"},transition:"all 0.2s",flex:"1",children:"Admin"}),(0,t.jsx)(D.I,{as:b.fK4,w:4,h:4,ml:2,transition:"all 0.2s",transform:l?"rotate(180deg)":"rotate(0deg)",opacity:.6})]}),(0,t.jsx)(N.S,{in:l,animateOpacity:!0,children:(0,t.jsx)(z.T,{spacing:1,align:"stretch",pl:4,py:2,children:k.map(e=>(0,t.jsxs)(W.N,{as:O(),href:e.href,display:"flex",alignItems:"center",px:4,py:2,fontSize:"xs",fontWeight:"medium",color:C(e.href)?j.colors.text:j.colors.textSecondary,bg:C(e.href)?"".concat(j.colors.primary,"20"):"transparent",_hover:{bg:j.colors.surface,color:j.colors.text,transform:"translateX(2px)"},borderRadius:"md",transition:"all 0.2s",role:"group",children:[(0,t.jsx)(D.I,{as:e.icon,w:4,h:4,mr:2,transition:"all 0.2s",_groupHover:{transform:"scale(1.1)"}}),(0,t.jsx)(x.E,{display:{base:"none",lg:"block"},children:e.name})]},e.href))})})]})]}),(0,t.jsxs)(n.a,{px:4,pt:4,mt:"auto",children:[(0,t.jsx)(G.c,{borderColor:j.colors.border,mb:4}),(0,t.jsx)(x.E,{fontSize:"xs",color:j.colors.textSecondary,textAlign:"center",bgGradient:"linear(to-r, ".concat(j.colors.primaryLight,", ").concat(j.colors.accent,")"),bgClip:"text",opacity:.7,_hover:{opacity:1,transform:"scale(1.05)"},transition:"all 0.2s",children:y?"".concat(y," v").concat(R):"Bot v".concat(R)})]})]})}let T=e=>{let{children:r}=e,{currentScheme:o}=(0,P.DP)();return(0,t.jsx)(n.a,{minH:"100vh",bg:o.colors.background,position:"relative",overflow:"hidden",_before:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,bgImage:"\n          radial-gradient(circle at 15% 50%, ".concat(o.colors.primary,"15 0%, transparent 25%),\n          radial-gradient(circle at 85% 30%, ").concat(o.colors.accent,"15 0%, transparent 25%)\n        "),zIndex:0},_after:{content:'""',position:"fixed",top:0,left:0,right:0,bottom:0,backdropFilter:"blur(100px)",zIndex:0},children:(0,t.jsxs)(n.a,{position:"relative",zIndex:1,display:"flex",flexDirection:"column",minH:"100vh",children:[(0,t.jsx)(n.a,{position:"fixed",top:0,left:0,right:0,zIndex:30,children:(0,t.jsx)(A,{})}),(0,t.jsxs)(n.a,{display:"flex",flex:"1",position:"relative",pt:"4rem",children:[(0,t.jsx)(n.a,{position:"fixed",top:"4rem",bottom:0,left:0,w:"64",zIndex:20,children:(0,t.jsx)(H,{})}),(0,t.jsx)(n.a,{flex:"1",ml:"64",p:{base:4,md:8},maxW:"100%",transition:"all 0.3s",position:"relative",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)",pointerEvents:"none",zIndex:-1},children:(0,t.jsx)(a.m,{maxW:"container.xl",children:r})})]})]})})}}}]);