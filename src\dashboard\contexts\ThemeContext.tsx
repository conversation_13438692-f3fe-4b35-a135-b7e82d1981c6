import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { extendTheme, ChakraProvider } from '@chakra-ui/react';
import defaultTheme from '../styles/theme';

// Define color scheme presets
export interface ColorScheme {
  id: string;
  name: string;
  description: string;
  isCustom?: boolean;
  colors: {
    primary: string;
    primaryLight: string;
    primaryDark: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    success: string;
    warning: string;
    error: string;
    info: string;
  };
}

export const COLOR_SCHEMES: ColorScheme[] = [
  {
    id: 'default',
    name: 'Default Purple',
    description: 'Classic purple and blue gradient theme',
    colors: {
      primary: '#8b5cf6',
      primaryLight: '#a78bfa',
      primaryDark: '#7c3aed',
      secondary: '#5865F2',
      accent: '#06b6d4',
      background: '#1a202c',
      surface: 'rgba(255,255,255,0.03)',
      text: '#f7fafc',
      textSecondary: '#a0aec0',
      border: 'rgba(255,255,255,0.2)',
      success: '#68d391',
      warning: '#fbb6ce',
      error: '#fc8181',
      info: '#63b3ed',
    },
  },
  {
    id: 'ocean',
    name: 'Ocean Blue',
    description: 'Deep blue ocean-inspired theme',
    colors: {
      primary: '#0ea5e9',
      primaryLight: '#38bdf8',
      primaryDark: '#0284c7',
      secondary: '#06b6d4',
      accent: '#8b5cf6',
      background: '#0f172a',
      surface: 'rgba(59, 130, 246, 0.05)',
      text: '#f1f5f9',
      textSecondary: '#94a3b8',
      border: 'rgba(59, 130, 246, 0.2)',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
  },
  {
    id: 'forest',
    name: 'Forest Green',
    description: 'Nature-inspired green theme',
    colors: {
      primary: '#059669',
      primaryLight: '#10b981',
      primaryDark: '#047857',
      secondary: '#065f46',
      accent: '#8b5cf6',
      background: '#0f1419',
      surface: 'rgba(16, 185, 129, 0.05)',
      text: '#f0fdf4',
      textSecondary: '#86efac',
      border: 'rgba(16, 185, 129, 0.2)',
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4',
    },
  },
  {
    id: 'sunset',
    name: 'Sunset Orange',
    description: 'Warm sunset-inspired theme',
    colors: {
      primary: '#ea580c',
      primaryLight: '#fb923c',
      primaryDark: '#c2410c',
      secondary: '#dc2626',
      accent: '#8b5cf6',
      background: '#1c1917',
      surface: 'rgba(251, 146, 60, 0.05)',
      text: '#fef7ed',
      textSecondary: '#fdba74',
      border: 'rgba(251, 146, 60, 0.2)',
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4',
    },
  },
  {
    id: 'rose',
    name: 'Rose Pink',
    description: 'Elegant rose and pink theme',
    colors: {
      primary: '#e11d48',
      primaryLight: '#f43f5e',
      primaryDark: '#be123c',
      secondary: '#ec4899',
      accent: '#8b5cf6',
      background: '#1f1720',
      surface: 'rgba(244, 63, 94, 0.05)',
      text: '#fdf2f8',
      textSecondary: '#fda4af',
      border: 'rgba(244, 63, 94, 0.2)',
      success: '#22c55e',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4',
    },
  },
  {
    id: 'midnight',
    name: 'Midnight Blue',
    description: 'Dark midnight blue theme',
    colors: {
      primary: '#1e40af',
      primaryLight: '#3b82f6',
      primaryDark: '#1e3a8a',
      secondary: '#4338ca',
      accent: '#06b6d4',
      background: '#0c0a1f',
      surface: 'rgba(59, 130, 246, 0.05)',
      text: '#f8fafc',
      textSecondary: '#94a3b8',
      border: 'rgba(59, 130, 246, 0.2)',
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#3b82f6',
    },
  },
];

interface ThemeContextType {
  currentScheme: ColorScheme;
  setColorScheme: (schemeId: string) => void;
  colorSchemes: ColorScheme[];
  customSchemes: ColorScheme[];
  addCustomScheme: (scheme: ColorScheme) => void;
  deleteCustomScheme: (schemeId: string) => void;
  resetToDefault: () => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [currentScheme, setCurrentScheme] = useState<ColorScheme>(COLOR_SCHEMES[0]);
  const [customSchemes, setCustomSchemes] = useState<ColorScheme[]>([]);

  // Load theme from localStorage on mount
  useEffect(() => {
    const savedSchemeId = localStorage.getItem('dashboard-color-scheme');
    const savedCustomSchemes = localStorage.getItem('dashboard-custom-schemes');
    
    if (savedCustomSchemes) {
      try {
        const parsedCustomSchemes = JSON.parse(savedCustomSchemes);
        setCustomSchemes(parsedCustomSchemes);
      } catch (error) {
        console.error('Failed to parse custom schemes:', error);
      }
    }
    
    if (savedSchemeId) {
      // First check built-in schemes
      const builtInScheme = COLOR_SCHEMES.find(s => s.id === savedSchemeId);
      if (builtInScheme) {
        setCurrentScheme(builtInScheme);
      } else {
        // Check custom schemes
        const savedCustomSchemes = localStorage.getItem('dashboard-custom-schemes');
        if (savedCustomSchemes) {
          try {
            const parsedCustomSchemes = JSON.parse(savedCustomSchemes);
            const customScheme = parsedCustomSchemes.find((s: ColorScheme) => s.id === savedSchemeId);
            if (customScheme) {
              setCurrentScheme(customScheme);
            }
          } catch (error) {
            console.error('Failed to find custom scheme:', error);
          }
        }
      }
    }
  }, []);

  // Save theme to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('dashboard-color-scheme', currentScheme.id);
  }, [currentScheme]);

  // Save custom schemes to localStorage when they change
  useEffect(() => {
    localStorage.setItem('dashboard-custom-schemes', JSON.stringify(customSchemes));
  }, [customSchemes]);

  const setColorScheme = (schemeId: string) => {
    // First check built-in schemes
    const builtInScheme = COLOR_SCHEMES.find(s => s.id === schemeId);
    if (builtInScheme) {
      setCurrentScheme(builtInScheme);
      return;
    }
    
    // Check custom schemes
    const customScheme = customSchemes.find(s => s.id === schemeId);
    if (customScheme) {
      setCurrentScheme(customScheme);
    }
  };

  const addCustomScheme = (scheme: ColorScheme) => {
    setCustomSchemes(prev => {
      const filtered = prev.filter(s => s.id !== scheme.id);
      return [...filtered, scheme];
    });
    setCurrentScheme(scheme);
  };

  const deleteCustomScheme = (schemeId: string) => {
    setCustomSchemes(prev => prev.filter(s => s.id !== schemeId));
    // If the deleted scheme is currently active, switch to default
    if (currentScheme.id === schemeId) {
      setCurrentScheme(COLOR_SCHEMES[0]);
    }
  };

  const resetToDefault = () => {
    setCurrentScheme(COLOR_SCHEMES[0]);
  };

  // Get all schemes (built-in + custom)
  const allSchemes = [...COLOR_SCHEMES, ...customSchemes];

  // Create dynamic Chakra UI theme based on current colors
  const dynamicTheme = extendTheme({
    ...defaultTheme,
    colors: {
      ...defaultTheme.colors,
      brand: {
        50: currentScheme.colors.primaryLight + '20',
        100: currentScheme.colors.primaryLight + '40',
        200: currentScheme.colors.primaryLight + '60',
        300: currentScheme.colors.primaryLight + '80',
        400: currentScheme.colors.primaryLight,
        500: currentScheme.colors.primary,
        600: currentScheme.colors.primaryDark,
        700: currentScheme.colors.primaryDark + 'CC',
        800: currentScheme.colors.primaryDark + 'AA',
        900: currentScheme.colors.primaryDark + '88',
      },
      custom: {
        primary: currentScheme.colors.primary,
        primaryLight: currentScheme.colors.primaryLight,
        primaryDark: currentScheme.colors.primaryDark,
        secondary: currentScheme.colors.secondary,
        accent: currentScheme.colors.accent,
        background: currentScheme.colors.background,
        surface: currentScheme.colors.surface,
        text: currentScheme.colors.text,
        textSecondary: currentScheme.colors.textSecondary,
        border: currentScheme.colors.border,
        success: currentScheme.colors.success,
        warning: currentScheme.colors.warning,
        error: currentScheme.colors.error,
        info: currentScheme.colors.info,
      },
    },
    styles: {
      global: {
        body: {
          bg: currentScheme.colors.background,
          color: currentScheme.colors.text,
        },
      },
    },
  });

  const contextValue: ThemeContextType = {
    currentScheme,
    setColorScheme,
    colorSchemes: allSchemes,
    customSchemes,
    addCustomScheme,
    deleteCustomScheme,
    resetToDefault,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <ChakraProvider theme={dynamicTheme}>
        {children}
      </ChakraProvider>
    </ThemeContext.Provider>
  );
}; 