// @ts-nocheck
import {
  <PERSON><PERSON>,
  <PERSON>dal<PERSON>lay,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Textarea,
  Select,
  useToast,
} from '@chakra-ui/react';
import { useState } from 'react';

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateTicketDialog({ isOpen, onClose, onSuccess }: Props) {
  const toast = useToast();
  const [reason, setReason] = useState('');
  const [category, setCategory] = useState('support');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/discord/tickets', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ reason, category }),
      });

      if (!res.ok) {
        const err = await res.json();
        throw new Error(err.error || 'Failed to create ticket');
      }

      toast({
        title: 'Ticket Created',
        description: 'Your support ticket has been opened.',
        status: 'success',
        duration: 3000,
      });

      setReason('');
      setCategory('support');
      onSuccess();
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create ticket',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg" scrollBehavior="inside">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent bg="gray.800">
        <ModalHeader>Create Support Ticket</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <FormControl mb={4}>
            <FormLabel>Category</FormLabel>
            <Select value={category} onChange={(e) => setCategory(e.target.value)}>
              <option value="support">Support</option>
              <option value="18plus">18+</option>
              <option value="other">Other</option>
            </Select>
          </FormControl>

          <FormControl>
            <FormLabel>Describe your issue</FormLabel>
            <Textarea
              placeholder="I need help with..."
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={5}
            />
          </FormControl>
        </ModalBody>
        <ModalFooter>
          <Button mr={3} variant="ghost" onClick={onClose}>
            Cancel
          </Button>
          <Button colorScheme="blue" onClick={handleSubmit} isLoading={isLoading}>
            Create Ticket
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 