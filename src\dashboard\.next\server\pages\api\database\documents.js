"use strict";(()=>{var e={};e.id=9719,e.ids=[9719],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,a)=>{e.exports=a(5600)},3873:e=>{e.exports=require("path")},4991:(e,t,a)=>{a.r(t),a.d(t,{config:()=>y,default:()=>h,routeModule:()=>S});var o={};a.r(o),a.d(o,{default:()=>g});var r=a(3433),n=a(264),s=a(584),i=a(5806),d=a(8525),l=a(2518),u=a(8580),c=a(6732);let{url:b,name:m}=u.dashboardConfig.database,f=null;async function p(){if(f)return f;let e=await l.MongoClient.connect(b);return f=e,e}async function g(e,t){if(!await (0,i.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});let{collection:a,id:o}=e.query;if(!a)return t.status(400).json({error:"Collection name is required"});try{let r=(await p()).db(m).collection(a);switch(e.method){case"GET":let n=await r.find({}).toArray();return await (0,c.logDatabaseOperation)("query",a,{count:n.length}),t.status(200).json({documents:n});case"POST":let s=await r.insertOne(e.body);return await (0,c.logDatabaseOperation)("insert",a,{document:e.body}),t.status(201).json({result:s});case"PUT":if(!o)return t.status(400).json({error:"Document ID is required"});let i=await r.updateOne({_id:new l.ObjectId(o)},{$set:e.body});return await (0,c.logDatabaseOperation)("update",a,{id:o,document:e.body}),t.status(200).json({result:i});case"DELETE":if(!o)return t.status(400).json({error:"Document ID is required"});let d=await r.deleteOne({_id:new l.ObjectId(o)});return await (0,c.logDatabaseOperation)("delete",a,{id:o}),t.status(200).json({result:d});default:return t.status(405).json({error:"Method not allowed"})}}catch(e){return t.status(500).json({error:"Internal server error"})}}let h=(0,s.M)(o,"default"),y=(0,s.M)(o,"config"),S=new r.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/database/documents",pathname:"/api/database/documents",bundlePath:"",filename:""},userland:o})},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},6732:(e,t,a)=>{a.r(t),a.d(t,{logDatabaseOperation:()=>s});var o=a(2518);let{url:r,name:n}=a(8580).dashboardConfig.database;async function s(e,t,a){let s=null;try{let i=(s=await o.MongoClient.connect(r)).db(n).collection("database_logs");await i.insertOne({timestamp:new Date,operation:e,collection:t,details:a,id:new Date().getTime().toString()})}catch(e){}finally{s&&await s.close()}}},8525:(e,t,a)=>{a.r(t),a.d(t,{authOptions:()=>d,default:()=>l});var o=a(5542),r=a.n(o);let n=require("next-auth/providers/discord");var s=a.n(n),i=a(8580);let d={providers:[s()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:a})=>(t&&a&&(e.accessToken=t.access_token||null,e.id=a.id||null),e),async session({session:e,token:t}){if(e?.user){let a=t.id||null,o=t.accessToken||null;e.user.id=a,e.user.accessToken=o;let r=!1;if(a)if((i.dashboardConfig.dashboard.admins||[]).includes(a))r=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${a}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let a=await t.json();r=e.some(e=>a.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let a=new URL(t),o=`${a.protocol}//localhost${a.port?`:${a.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=r()(d)},8580:(e,t,a)=>{a.r(t),a.d(t,{dashboardConfig:()=>d,default:()=>l});var o=a(9021),r=a(2115),n=a.n(r),s=a(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>s.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=s.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");i=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=t(t.s=4991);module.exports=a})();