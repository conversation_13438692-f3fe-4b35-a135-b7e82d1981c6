import YAML from 'yaml';

export interface AddonConfig {
  name: string;
  version: string;
  description: string;
  author: string;
  commands: CommandConfig[];
  events: EventConfig[];
  settings: {
    embedColor: string;
    [key: string]: any;
  };
  database?: {
    collections: string[];
  };
}

export interface CommandConfig {
  name: string;
  description: string;
  type: 'slash' | 'context-menu';
  options?: SlashCommandOption[];
  permissions?: string[];
  cooldown?: number;
  enabled: boolean;
  code: string;
}

export interface SlashCommandOption {
  name: string;
  description: string;
  type: 'string' | 'integer' | 'boolean' | 'user' | 'channel' | 'role' | 'mentionable' | 'number' | 'attachment';
  required: boolean;
  choices?: { name: string; value: string | number }[];
}

export interface EventConfig {
  name: string;
  once: boolean;
  code: string;
}

export class AddonCodeGenerator {
  static generateConfigYaml(config: AddonConfig): string {
    const configObj = {
      addon: {
        name: config.name,
        version: config.version,
        description: config.description,
        author: config.author,
        enabled: true
      },
      commands: {} as any,
      settings: {
        ...config.settings,
        embedColor: config.settings.embedColor.startsWith('#') 
          ? parseInt(config.settings.embedColor.slice(1), 16)
          : parseInt(config.settings.embedColor, 16)
      },
      database: config.database || { collections: [] },
      logging: {
        enabled: true,
        logCommands: true,
        logLevel: 'info'
      }
    };

    // Add command configurations
    config.commands.forEach(cmd => {
      configObj.commands[cmd.name] = {
        enabled: cmd.enabled,
        cooldown: cmd.cooldown || 3000,
        permissions: cmd.permissions || [],
        description: cmd.description
      };
    });

    return YAML.stringify(configObj, { indent: 2 });
  }

  static generateIndexTs(config: AddonConfig): string {
    const imports = [
      `import fs from 'node:fs';`,
      `import path from 'node:path';`,
      `import { fileURLToPath, pathToFileURL } from 'node:url';`,
      `import YAML from 'yaml';`,
      `import type { Addon, BotInstance } from '../../types/index.js';`,
      `import { Logger } from '../../core/Logger.js';`
    ];

    const loadCommandsFunction = `
// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Dynamically load commands from commands folder
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || config.commands[commandModule.data.name]?.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}`;

    const events = config.events.map(event => `
    {
      name: '${event.name}',
      once: ${event.once},
      execute: async (...args) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        try {
          ${event.code}
        } catch (error) {
          logger.error(\`Error in ${event.name} event:\`, error);
        }
      }
    }`).join(',');

    const onLoadFunction = `
  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Loading \${config.addon.name} addon with \${addon.commands?.length || 0} commands...\`);
    
    // Initialize database collections if needed
    if (bot.database && config.database?.collections) {
      try {
        for (const collectionName of config.database.collections) {
          const collection = bot.database.db.collection(collectionName);
          
          // Create basic indexes based on collection type
          if (collectionName.includes('usage')) {
            await collection.createIndex({ userId: 1, timestamp: -1 });
          } else if (collectionName.includes('stats')) {
            await collection.createIndex({ userId: 1, guildId: 1 });
          }
        }
        logger.info('Database collections and indexes initialized');
      } catch (error) {
        logger.warn('Failed to initialize database collections:', error);
      }
    }

    logger.info(\`\${config.addon.name} addon loaded successfully!\`);
  }`;

    const onUnloadFunction = `
  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Unloading \${config.addon.name} addon...\`);
  }`;

    return `${imports.join('\n')}

${loadCommandsFunction}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  commands: await loadCommands(),

  events: [${events}
  ],

  ${onLoadFunction},

  ${onUnloadFunction}
};

export default addon;`;
  }

  static generateCommandFile(command: CommandConfig): string {
    const imports = [
      `import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';`,
      `import type { BotInstance } from '../../../types/index.js';`
    ];

    const commandBuilder = this.generateCommandBuilder(command);
    const executeFunction = this.generateExecuteFunction(command);

    return `${imports.join('\n')}

${commandBuilder}

${executeFunction}

export const cooldown = ${command.cooldown || 3000};`;
  }

  private static generateCommandBuilder(command: CommandConfig): string {
    let builder = `export const data = new SlashCommandBuilder()
  .setName('${command.name}')
  .setDescription('${command.description}')`;

    if (command.options) {
      command.options.forEach(option => {
        const methodName = this.getOptionMethodName(option.type);
        builder += `
  .${methodName}(option =>
    option.setName('${option.name}')
      .setDescription('${option.description}')
      .setRequired(${option.required})`;
        
        if (option.choices && option.choices.length > 0) {
          builder += `
      .addChoices(${option.choices.map(choice => 
            `{ name: '${choice.name}', value: ${typeof choice.value === 'string' ? `'${choice.value}'` : choice.value} }`
          ).join(', ')})`;
        }
        
        builder += `
  )`;
      });
    }

    builder += ';';
    return builder;
  }

  private static getOptionMethodName(type: string): string {
    const typeMap: { [key: string]: string } = {
      'string': 'addStringOption',
      'integer': 'addIntegerOption',
      'boolean': 'addBooleanOption',
      'user': 'addUserOption',
      'channel': 'addChannelOption',
      'role': 'addRoleOption',
      'mentionable': 'addMentionableOption',
      'number': 'addNumberOption',
      'attachment': 'addAttachmentOption'
    };
    return typeMap[type] || 'addStringOption';
  }

  private static generateExecuteFunction(command: CommandConfig): string {
    return `export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    ${command.code}
  } catch (error) {
    console.error(\`Error executing ${command.name} command:\`, error);
    
    const errorMessage = 'There was an error executing this command!';
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({ content: errorMessage, ephemeral: true });
    } else if (interaction.deferred) {
      await interaction.editReply({ content: errorMessage });
    } else {
      await interaction.followUp({ content: errorMessage, ephemeral: true });
    }
  }
}`;
  }

  static generateReadme(config: AddonConfig): string {
    const commandsList = config.commands.map(cmd => 
      `- **/${cmd.name}** - ${cmd.description}`
    ).join('\n');

    const eventsList = config.events.map(event => 
      `- **${event.name}** - ${event.once ? 'Once' : 'Recurring'} event`
    ).join('\n');

    return `# ${config.name}

${config.description}

**Author:** ${config.author}  
**Version:** ${config.version}

## Commands

${commandsList || 'No commands available.'}

## Events

${eventsList || 'No events configured.'}

## Database Collections

${config.database?.collections?.map(col => `- ${col}`).join('\n') || 'No database collections.'}

## Settings

- **Embed Color:** ${config.settings.embedColor}

---

*This addon was generated using the 404 Bot Addon Builder.*`;
  }
} 