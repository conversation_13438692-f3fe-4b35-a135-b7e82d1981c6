"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9998],{7680:(e,n,t)=>{t.d(n,{m:()=>m});var r=t(94513),o=t(22697),a=t(67233),i=t(9557),u=t(63203),c=t(94285),l=t(51133);let d={initial:"exit",animate:"enter",exit:"exit",variants:{enter:({transition:e,transitionEnd:n,delay:t}={})=>({opacity:1,transition:e?.enter??l.yA.enter(l.jd.enter,t),transitionEnd:n?.enter}),exit:({transition:e,transitionEnd:n,delay:t}={})=>({opacity:0,transition:e?.exit??l.yA.exit(l.jd.exit,t),transitionEnd:n?.exit})}};(0,c.forwardRef)(function(e,n){let{unmountOnExit:t,in:i,className:c,transition:l,transitionEnd:s,delay:f,animatePresenceProps:v,...m}=e,p=i||t?"enter":"exit",h=!t||i&&t,y={transition:l,transitionEnd:s,delay:f};return(0,r.jsx)(u.N,{...v,custom:y,children:h&&(0,r.jsx)(a.P.div,{ref:n,className:(0,o.cx)("chakra-fade",c),custom:y,...d,animate:p,...m})})}).displayName="Fade";var s=t(33225),f=t(2923);let v=(0,s.B)(a.P.div),m=(0,f.R)((e,n)=>{let{className:t,transition:a,motionProps:u,...c}=e,l=(0,o.cx)("chakra-modal__overlay",t),s={pos:"fixed",left:"0",top:"0",w:"100vw",h:"100vh",...(0,i.x5)().overlay},{motionPreset:f}=(0,i.k3)();return(0,r.jsx)(v,{...u||("none"===f?{}:d),__css:s,ref:n,className:l,...c})});m.displayName="ModalOverlay"},9557:(e,n,t)=>{t.d(n,{aF:()=>N,k3:()=>k,x5:()=>w});var r=t(94513),o=t(29035),a=t(63203),i=t(78961),u=t(50614),c=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},l=new WeakMap,d=new WeakMap,s={},f=0,v=function(e){return e&&(e.host||v(e.parentNode))},m=function(e,n,t,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(n.contains(e))return e;var t=v(e);return t&&n.contains(t)?t:(console.error("aria-hidden",e,"in not contained inside",n,". Doing nothing"),null)}).filter(function(e){return!!e});s[t]||(s[t]=new WeakMap);var a=s[t],i=[],u=new Set,c=new Set(o),m=function(e){!e||u.has(e)||(u.add(e),m(e.parentNode))};o.forEach(m);var p=function(e){!e||c.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))p(e);else try{var n=e.getAttribute(r),o=null!==n&&"false"!==n,c=(l.get(e)||0)+1,s=(a.get(e)||0)+1;l.set(e,c),a.set(e,s),i.push(e),1===c&&o&&d.set(e,!0),1===s&&e.setAttribute(t,"true"),o||e.setAttribute(r,"true")}catch(n){console.error("aria-hidden: cannot operate on ",e,n)}})};return p(n),u.clear(),f++,function(){i.forEach(function(e){var n=l.get(e)-1,o=a.get(e)-1;l.set(e,n),a.set(e,o),n||(d.has(e)||e.removeAttribute(r),d.delete(e)),o||e.removeAttribute(t)}),--f||(l=new WeakMap,l=new WeakMap,d=new WeakMap,s={})}},p=function(e,n,t){void 0===t&&(t="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=n||c(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),m(r,o,t,"aria-hidden")):function(){return null}},h=t(94285),y=t(82531),b=t(52831),g=t(56915);let[x,w]=(0,o.q)({name:"ModalStylesContext",errorMessage:"useModalStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Modal />\" "}),[E,k]=(0,o.q)({strict:!0,name:"ModalContext",errorMessage:"useModalContext: `context` is undefined. Seems you forgot to wrap modal components in `<Modal />`"}),N=e=>{let n={scrollBehavior:"outside",autoFocus:!0,trapFocus:!0,returnFocusOnClose:!0,blockScrollOnMount:!0,allowPinchZoom:!1,preserveScrollBarGap:!0,motionPreset:"scale",...e,lockFocusAcrossFrames:e.lockFocusAcrossFrames??!0},{portalProps:t,children:o,autoFocus:c,trapFocus:l,initialFocusRef:d,finalFocusRef:s,returnFocusOnClose:f,blockScrollOnMount:v,allowPinchZoom:m,preserveScrollBarGap:w,motionPreset:k,lockFocusAcrossFrames:N,animatePresenceProps:M,onCloseComplete:S}=n,O=(0,g.o)("Modal",n),C={...function(e){let{isOpen:n,onClose:t,id:r,closeOnOverlayClick:o=!0,closeOnEsc:a=!0,useInert:c=!0,onOverlayClick:l,onEsc:d}=e,s=(0,h.useRef)(null),f=(0,h.useRef)(null),[v,m,b]=function(e,...n){let t=(0,h.useId)(),r=e||t;return(0,h.useMemo)(()=>n.map(e=>`${e}-${r}`),[r,n])}(r,"chakra-modal","chakra-modal--header","chakra-modal--body");var g=s,x=n&&c;let w=g.current;(0,h.useEffect)(()=>{if(g.current&&x)return p(g.current)},[x,g,w]);let E=(0,y.y)(s,n),k=(0,h.useRef)(null),N=(0,h.useCallback)(e=>{k.current=e.target},[]),M=(0,h.useCallback)(e=>{"Escape"===e.key&&(e.stopPropagation(),a&&t?.(),d?.())},[a,t,d]),[S,O]=(0,h.useState)(!1),[C,A]=(0,h.useState)(!1),T=(0,h.useCallback)((e={},n=null)=>({role:"dialog",...e,ref:(0,i.Px)(n,s),id:v,tabIndex:-1,"aria-modal":!0,"aria-labelledby":S?m:void 0,"aria-describedby":C?b:void 0,onClick:(0,u.H)(e.onClick,e=>e.stopPropagation())}),[b,C,v,m,S]),R=(0,h.useCallback)(e=>{e.stopPropagation(),k.current===e.target&&y.J.isTopModal(s.current)&&(o&&t?.(),l?.())},[t,o,l]),P=(0,h.useCallback)((e={},n=null)=>({...e,ref:(0,i.Px)(n,f),onClick:(0,u.H)(e.onClick,R),onKeyDown:(0,u.H)(e.onKeyDown,M),onMouseDown:(0,u.H)(e.onMouseDown,N)}),[M,N,R]);return{isOpen:n,onClose:t,headerId:m,bodyId:b,setBodyMounted:A,setHeaderMounted:O,dialogRef:s,overlayRef:f,getDialogProps:T,getDialogContainerProps:P,index:E}}(n),autoFocus:c,trapFocus:l,initialFocusRef:d,finalFocusRef:s,returnFocusOnClose:f,blockScrollOnMount:v,allowPinchZoom:m,preserveScrollBarGap:w,motionPreset:k,lockFocusAcrossFrames:N};return(0,r.jsx)(E,{value:C,children:(0,r.jsx)(x,{value:O,children:(0,r.jsx)(a.N,{...M,onExitComplete:S,children:C.isOpen&&(0,r.jsx)(b.Z,{...t,children:o})})})})};N.displayName="Modal"},47847:(e,n,t)=>{t.d(n,{r:()=>d});var r=t(94513),o=t(55100),a=t(22697),i=t(94285),u=t(9557),c=t(2923),l=t(33225);let d=(0,c.R)((e,n)=>{let{className:t,...c}=e,{headerId:d,setHeaderMounted:s}=(0,u.k3)();(0,i.useEffect)(()=>(s(!0),()=>s(!1)),[s]);let f=(0,a.cx)("chakra-modal__header",t),v=(0,u.x5)(),m=(0,o.H2)({flex:0,...v.header});return(0,r.jsx)(l.B.header,{ref:n,className:f,id:d,...c,__css:m})});d.displayName="ModalHeader"},52922:(e,n,t)=>{t.d(n,{$:()=>n$});var r,o,a,i,u=t(94513),c=t(55100),l=t(22697),d=t(9557),s=t(12638),f=t(94285),v=function(){return(v=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)};function m(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>n.indexOf(r)&&(t[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>n.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(t[r[o]]=e[r[o]]);return t}Object.create;Object.create;var p=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),h="width-before-scroll-bar";function y(e,n){return"function"==typeof e?e(n):e&&(e.current=n),e}var b="undefined"!=typeof window?f.useLayoutEffect:f.useEffect,g=new WeakMap;function x(e,n){var t,r,o,a=(t=n||null,r=function(n){return e.forEach(function(e){return y(e,n)})},(o=(0,f.useState)(function(){return{value:t,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,o.facade);return b(function(){var n=g.get(a);if(n){var t=new Set(n),r=new Set(e),o=a.current;t.forEach(function(e){r.has(e)||y(e,null)}),r.forEach(function(e){t.has(e)||y(e,o)})}g.set(a,e)},[e]),a}function w(e){return e}function E(e,n){void 0===n&&(n=w);var t=[],r=!1;return{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return t.length?t[t.length-1]:e},useMedium:function(e){var o=n(e,r);return t.push(o),function(){t=t.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;t.length;){var n=t;t=[],n.forEach(e)}t={push:function(n){return e(n)},filter:function(){return t}}},assignMedium:function(e){r=!0;var n=[];if(t.length){var o=t;t=[],o.forEach(e),n=t}var a=function(){var t=n;n=[],t.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),t={push:function(e){n.push(e),i()},filter:function(e){return n=n.filter(e),t}}}}}function k(e,n){return void 0===n&&(n=w),E(e,n)}function N(e){void 0===e&&(e={});var n=E(null);return n.options=v({async:!0,ssr:!1},e),n}var M=N(),S=function(){},O=f.forwardRef(function(e,n){var t=f.useRef(null),r=f.useState({onScrollCapture:S,onWheelCapture:S,onTouchMoveCapture:S}),o=r[0],a=r[1],i=e.forwardProps,u=e.children,c=e.className,l=e.removeScrollBar,d=e.enabled,s=e.shards,p=e.sideCar,h=e.noRelative,y=e.noIsolation,b=e.inert,g=e.allowPinchZoom,w=e.as,E=e.gapMode,k=m(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=x([t,n]),O=v(v({},k),o);return f.createElement(f.Fragment,null,d&&f.createElement(p,{sideCar:M,removeScrollBar:l,shards:s,noRelative:h,noIsolation:y,inert:b,setCallbacks:a,allowPinchZoom:!!g,lockRef:t,gapMode:E}),i?f.cloneElement(f.Children.only(u),v(v({},O),{ref:N})):f.createElement(void 0===w?"div":w,v({},O,{className:c,ref:N}),u))});O.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},O.classNames={fullWidth:h,zeroRight:p};var C=function(e){var n=e.sideCar,t=m(e,["sideCar"]);if(!n)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=n.read();if(!r)throw Error("Sidecar medium not found");return f.createElement(r,v({},t))};C.isSideCarExport=!0;var A=function(){var e=0,n=null;return{add:function(r){if(0==e&&(n=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var n=i||t.nc;return n&&e.setAttribute("nonce",n),e}())){var o,a;(o=n).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),a=n,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!n||(n.parentNode&&n.parentNode.removeChild(n),n=null)}}},T=function(){var e=A();return function(n,t){f.useEffect(function(){return e.add(n),function(){e.remove()}},[n&&t])}},R=function(){var e=T();return function(n){return e(n.styles,n.dynamic),null}},P={left:0,top:0,right:0,gap:0},j=function(e){return parseInt(e||"",10)||0},F=function(e){var n=window.getComputedStyle(document.body),t=n["padding"===e?"paddingLeft":"marginLeft"],r=n["padding"===e?"paddingTop":"marginTop"],o=n["padding"===e?"paddingRight":"marginRight"];return[j(t),j(r),j(o)]},I=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return P;var n=F(e),t=document.documentElement.clientWidth,r=window.innerWidth;return{left:n[0],top:n[1],right:n[2],gap:Math.max(0,r-t+n[2]-n[0])}},_=R(),D="data-scroll-locked",L=function(e,n,t,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===t&&(t="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(D,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([n&&"position: relative ".concat(r,";"),"margin"===t&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===t&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(p," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(h," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(h," .").concat(h," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(D,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},B=function(){var e=parseInt(document.body.getAttribute(D)||"0",10);return isFinite(e)?e:0},W=function(){f.useEffect(function(){return document.body.setAttribute(D,(B()+1).toString()),function(){var e=B()-1;e<=0?document.body.removeAttribute(D):document.body.setAttribute(D,e.toString())}},[])},H=function(e){var n=e.noRelative,t=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;W();var a=f.useMemo(function(){return I(o)},[o]);return f.createElement(_,{styles:L(a,!n,o,t?"":"!important")})},G=!1;if("undefined"!=typeof window)try{var q=Object.defineProperty({},"passive",{get:function(){return G=!0,!0}});window.addEventListener("test",q,q),window.removeEventListener("test",q,q)}catch(e){G=!1}var U=!!G&&{passive:!1},Y=function(e,n){if(!(e instanceof Element))return!1;var t=window.getComputedStyle(e);return"hidden"!==t[n]&&(t.overflowY!==t.overflowX||"TEXTAREA"===e.tagName||"visible"!==t[n])},X=function(e,n){var t=n.ownerDocument,r=n;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),Z(e,r)){var o=z(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==t.body);return!1},Z=function(e,n){return"v"===e?Y(n,"overflowY"):Y(n,"overflowX")},z=function(e,n){return"v"===e?[n.scrollTop,n.scrollHeight,n.clientHeight]:[n.scrollLeft,n.scrollWidth,n.clientWidth]},K=function(e,n,t,r,o){var a,i=(a=window.getComputedStyle(n).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,c=t.target,l=n.contains(c),d=!1,s=u>0,f=0,v=0;do{if(!c)break;var m=z(e,c),p=m[0],h=m[1]-m[2]-i*p;(p||h)&&Z(e,c)&&(f+=h,v+=p);var y=c.parentNode;c=y&&y.nodeType===Node.DOCUMENT_FRAGMENT_NODE?y.host:y}while(!l&&c!==document.body||l&&(n.contains(c)||n===c));return s&&(o&&1>Math.abs(f)||!o&&u>f)?d=!0:!s&&(o&&1>Math.abs(v)||!o&&-u>v)&&(d=!0),d},$=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},V=function(e){return[e.deltaX,e.deltaY]},J=function(e){return e&&"current"in e?e.current:e},Q=0,ee=[];let en=(r=function(e){var n=f.useRef([]),t=f.useRef([0,0]),r=f.useRef(),o=f.useState(Q++)[0],a=f.useState(R)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var n=(function(e,n,t){if(t||2==arguments.length)for(var r,o=0,a=n.length;o<a;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))})([e.lockRef.current],(e.shards||[]).map(J),!0).filter(Boolean);return n.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),n.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=f.useCallback(function(e,n){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=$(e),u=t.current,c="deltaX"in e?e.deltaX:u[0]-a[0],l="deltaY"in e?e.deltaY:u[1]-a[1],d=e.target,s=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===s&&"range"===d.type)return!1;var f=X(s,d);if(!f)return!0;if(f?o=s:(o="v"===s?"h":"v",f=X(s,d)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var v=r.current||o;return K(v,n,e,"h"===v?c:l,!0)},[]),c=f.useCallback(function(e){if(ee.length&&ee[ee.length-1]===a){var t="deltaY"in e?V(e):$(e),r=n.current.filter(function(n){var r;return n.name===e.type&&(n.target===e.target||e.target===n.shadowParent)&&(r=n.delta,r[0]===t[0]&&r[1]===t[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(i.current.shards||[]).map(J).filter(Boolean).filter(function(n){return n.contains(e.target)});(o.length>0?u(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),l=f.useCallback(function(e,t,r,o){var a={name:e,delta:t,target:r,should:o,shadowParent:function(e){for(var n=null;null!==e;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}(r)};n.current.push(a),setTimeout(function(){n.current=n.current.filter(function(e){return e!==a})},1)},[]),d=f.useCallback(function(e){t.current=$(e),r.current=void 0},[]),s=f.useCallback(function(n){l(n.type,V(n),n.target,u(n,e.lockRef.current))},[]),v=f.useCallback(function(n){l(n.type,$(n),n.target,u(n,e.lockRef.current))},[]);f.useEffect(function(){return ee.push(a),e.setCallbacks({onScrollCapture:s,onWheelCapture:s,onTouchMoveCapture:v}),document.addEventListener("wheel",c,U),document.addEventListener("touchmove",c,U),document.addEventListener("touchstart",d,U),function(){ee=ee.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,U),document.removeEventListener("touchmove",c,U),document.removeEventListener("touchstart",d,U)}},[]);var m=e.removeScrollBar,p=e.inert;return f.createElement(f.Fragment,null,p?f.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?f.createElement(H,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},M.useMedium(r),C);var et=f.forwardRef(function(e,n){return f.createElement(O,v({},e,{ref:n,sideCar:en}))});et.classNames=O.classNames;var er=t(82531),eo=t(63535),ea="data-focus-lock",ei="data-focus-lock-disabled",eu={width:"1px",height:"0px",padding:0,overflow:"hidden",position:"fixed",top:"1px",left:"1px"},ec=k({},function(e){return{target:e.target,currentTarget:e.currentTarget}}),el=k(),ed=k(),es=N({async:!0,ssr:"undefined"!=typeof document}),ef=(0,f.createContext)(void 0),ev=[],em=(0,f.forwardRef)(function(e,n){var t,r=(0,f.useState)(),o=r[0],a=r[1],i=(0,f.useRef)(),u=(0,f.useRef)(!1),c=(0,f.useRef)(null),l=(0,f.useState)({})[1],d=e.children,s=e.disabled,v=void 0!==s&&s,m=e.noFocusGuards,p=void 0!==m&&m,h=e.persistentFocus,y=e.crossFrame,b=e.autoFocus,g=(e.allowTextSelection,e.group),w=e.className,E=e.whiteList,k=e.hasPositiveIndices,N=e.shards,M=void 0===N?ev:N,S=e.as,O=e.lockProps,C=e.sideCar,A=e.returnFocus,T=void 0!==A&&A,R=e.focusOptions,P=e.onActivation,j=e.onDeactivation,F=(0,f.useState)({})[0],I=(0,f.useCallback)(function(e){var n=e.captureFocusRestore;if(!c.current){var t,r=null==(t=document)?void 0:t.activeElement;c.current=r,r!==document.body&&(c.current=n(r))}i.current&&P&&P(i.current),u.current=!0,l()},[P]),_=(0,f.useCallback)(function(){u.current=!1,j&&j(i.current),l()},[j]),D=(0,f.useCallback)(function(e){var n=c.current;if(n){var t=("function"==typeof n?n():n)||document.body,r="function"==typeof T?T(t):T;if(r){var o="object"==typeof r?r:void 0;c.current=null,e?Promise.resolve().then(function(){return t.focus(o)}):t.focus(o)}}},[T]),L=(0,f.useCallback)(function(e){u.current&&ec.useMedium(e)},[]),B=el.useMedium,W=(0,f.useCallback)(function(e){i.current!==e&&(i.current=e,a(e))},[]),H=(0,eo.A)(((t={})[ei]=v&&"disabled",t[ea]=g,t),void 0===O?{}:O),G=!0!==p,q=G&&"tail"!==p,U=x([n,W]),Y=(0,f.useMemo)(function(){return{observed:i,shards:M,enabled:!v,active:u.current}},[v,u.current,M,o]);return f.createElement(f.Fragment,null,G&&[f.createElement("div",{key:"guard-first","data-focus-guard":!0,tabIndex:v?-1:0,style:eu}),k?f.createElement("div",{key:"guard-nearest","data-focus-guard":!0,tabIndex:v?-1:1,style:eu}):null],!v&&f.createElement(C,{id:F,sideCar:es,observed:o,disabled:v,persistentFocus:void 0!==h&&h,crossFrame:void 0===y||y,autoFocus:void 0===b||b,whiteList:E,shards:M,onActivation:I,onDeactivation:_,returnFocus:D,focusOptions:R,noFocusGuards:p}),f.createElement(void 0===S?"div":S,(0,eo.A)({ref:U},H,{className:w,onBlur:B,onFocus:L}),f.createElement(ef.Provider,{value:Y},d)),q&&f.createElement("div",{"data-focus-guard":!0,tabIndex:v?-1:0,style:eu}))});function ep(e,n){return(ep=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,n){return e.__proto__=n,e})(e,n)}function eh(e){return(eh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}em.propTypes={};var ey=function(e){for(var n=Array(e.length),t=0;t<e.length;++t)n[t]=e[t];return n},eb=function(e){return Array.isArray(e)?e:[e]},eg=function(e){return Array.isArray(e)?e[0]:e},ex=function(e){if(e.nodeType!==Node.ELEMENT_NODE)return!1;var n=window.getComputedStyle(e,null);return!!n&&!!n.getPropertyValue&&("none"===n.getPropertyValue("display")||"hidden"===n.getPropertyValue("visibility"))},ew=function(e){return e.parentNode&&e.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE?e.parentNode.host:e.parentNode},eE=function(e){return e===document||e&&e.nodeType===Node.DOCUMENT_NODE},ek=function(e,n){var t,r,o=e.get(n);if(void 0!==o)return o;var a=(t=n,r=ek.bind(void 0,e),!t||eE(t)||!ex(t)&&!t.hasAttribute("inert")&&r(ew(t)));return e.set(n,a),a},eN=function(e,n){var t,r=e.get(n);if(void 0!==r)return r;var o=(t=eN.bind(void 0,e),!n||!!eE(n)||!!eC(n)&&t(ew(n)));return e.set(n,o),o},eM=function(e){return e.dataset},eS=function(e){return"INPUT"===e.tagName},eO=function(e){return eS(e)&&"radio"===e.type},eC=function(e){return![!0,"true",""].includes(e.getAttribute("data-no-autofocus"))},eA=function(e){var n;return!!(e&&(null==(n=eM(e))?void 0:n.focusGuard))},eT=function(e){return!eA(e)},eR=function(e){return!!e},eP=function(e,n){var t=Math.max(0,e.tabIndex),r=Math.max(0,n.tabIndex),o=t-r,a=e.index-n.index;if(o){if(!t)return 1;if(!r)return -1}return o||a},ej=function(e,n,t){return ey(e).map(function(e,n){var r=e.tabIndex<0&&!e.hasAttribute("tabindex")?0:e.tabIndex;return{node:e,index:n,tabIndex:t&&-1===r?(e.dataset||{}).focusGuard?0:-1:r}}).filter(function(e){return!n||e.tabIndex>=0}).sort(eP)},eF="button:enabled,select:enabled,textarea:enabled,input:enabled,a[href],area[href],summary,iframe,object,embed,audio[controls],video[controls],[tabindex],[contenteditable],[autofocus]",eI="".concat(eF,", [data-focus-guard]"),e_=function(e,n){return ey((e.shadowRoot||e).children).reduce(function(e,t){return e.concat(t.matches(n?eI:eF)?[t]:[],e_(t))},[])},eD=function(e,n){var t;return e instanceof HTMLIFrameElement&&(null==(t=e.contentDocument)?void 0:t.body)?eL([e.contentDocument.body],n):[e]},eL=function(e,n){return e.reduce(function(e,t){var r,o=e_(t,n),a=(r=[]).concat.apply(r,o.map(function(e){return eD(e,n)}));return e.concat(a,t.parentNode?ey(t.parentNode.querySelectorAll(eF)).filter(function(e){return e===t}):[])},[])},eB=function(e,n){return ey(e).filter(function(e){return ek(n,e)}).filter(function(e){var n;return!((eS(n=e)||"BUTTON"===n.tagName)&&("hidden"===n.type||n.disabled))})},eW=function(e,n){return void 0===n&&(n=new Map),ey(e).filter(function(e){return eN(n,e)})},eH=function(e,n,t){return ej(eB(eL(e,t),n),!0,t)},eG=function(e,n){return ej(eB(eL(e),n),!1)},eq=function(e,n){return e.shadowRoot?eq(e.shadowRoot,n):!!(void 0!==Object.getPrototypeOf(e).contains&&Object.getPrototypeOf(e).contains.call(e,n))||ey(e.children).some(function(e){var t;if(e instanceof HTMLIFrameElement){var r=null==(t=e.contentDocument)?void 0:t.body;return!!r&&eq(r,n)}return eq(e,n)})},eU=function(e){try{return e()}catch(e){return}},eY=function(e){if(void 0===e&&(e=document),e&&e.activeElement){var n=e.activeElement;return n.shadowRoot?eY(n.shadowRoot):n instanceof HTMLIFrameElement&&eU(function(){return n.contentWindow.document})?eY(n.contentWindow.document):n}},eX=function(e){void 0===e&&(e=document);var n=eY(e);return!!n&&ey(e.querySelectorAll("[".concat("data-no-focus-lock","]"))).some(function(e){return eq(e,n)})},eZ=function(e){for(var n=new Set,t=e.length,r=0;r<t;r+=1)for(var o=r+1;o<t;o+=1){var a=e[r].compareDocumentPosition(e[o]);(a&Node.DOCUMENT_POSITION_CONTAINED_BY)>0&&n.add(o),(a&Node.DOCUMENT_POSITION_CONTAINS)>0&&n.add(r)}return e.filter(function(e,t){return!n.has(t)})},ez=function(e){return e.parentNode?ez(e.parentNode):e},eK=function(e){return eb(e).filter(Boolean).reduce(function(e,n){var t=n.getAttribute(ea);return e.push.apply(e,t?eZ(ey(ez(n).querySelectorAll("[".concat(ea,'="').concat(t,'"]:not([').concat(ei,'="disabled"])')))):[n]),e},[])},e$=function(e,n){return void 0===n&&(n=eY(eg(e).ownerDocument)),!!n&&(!n.dataset||!n.dataset.focusGuard)&&eK(e).some(function(e){var t;return eq(e,n)||(t=n,!!ey(e.querySelectorAll("iframe")).some(function(e){return e===t}))})},eV=function(e,n){e&&("focus"in e&&e.focus(n),"contentWindow"in e&&e.contentWindow&&e.contentWindow.focus())},eJ=function(e,n){if(eO(e)&&e.name)return n.filter(eO).filter(function(n){return n.name===e.name}).filter(function(e){return e.checked})[0]||e;return e},eQ=function(e){var n=new Set;return e.forEach(function(t){return n.add(eJ(t,e))}),e.filter(function(e){return n.has(e)})},e0=function(e){return e[0]&&e.length>1?eJ(e[0],e):e[0]},e1=function(e,n){return e.indexOf(eJ(n,e))},e2="NEW_FOCUS",e5=function(e,n,t,r,o){var a=e.length,i=e[0],u=e[a-1],c=eA(r);if(!(r&&e.indexOf(r)>=0)){var l=void 0!==r?t.indexOf(r):-1,d=o?t.indexOf(o):l,s=o?e.indexOf(o):-1;if(-1===l)return -1!==s?s:e2;if(-1===s)return e2;var f=l-d,v=t.indexOf(i),m=t.indexOf(u),p=eQ(t),h=void 0!==r?p.indexOf(r):-1,y=o?p.indexOf(o):h,b=p.filter(function(e){return e.tabIndex>=0}),g=void 0!==r?b.indexOf(r):-1,x=o?b.indexOf(o):g;if(!f&&s>=0||0===n.length)return s;var w=e1(e,n[0]),E=e1(e,n[n.length-1]);if(l<=v&&c&&Math.abs(f)>1)return E;if(l>=m&&c&&Math.abs(f)>1)return w;if(f&&Math.abs(g>=0&&x>=0?x-g:y-h)>1)return s;if(l<=v)return E;if(l>m)return w;if(f)return Math.abs(f)>1?s:(a+s+f)%a}},e3=function(e,n,t){var r=eW(e.map(function(e){return e.node}).filter(function(e){var n,r=null==(n=eM(e))?void 0:n.autofocus;return e.autofocus||void 0!==r&&"false"!==r||t.indexOf(e)>=0}));return r&&r.length?e0(r):e0(eW(n))},e9=function(e,n){return void 0===n&&(n=[]),n.push(e),e.parentNode&&e9(e.parentNode.host||e.parentNode,n),n},e6=function(e,n){for(var t=e9(e),r=e9(n),o=0;o<t.length;o+=1){var a=t[o];if(r.indexOf(a)>=0)return a}return!1},e8=function(e,n,t){var r=eb(e),o=eb(n),a=r[0],i=!1;return o.filter(Boolean).forEach(function(e){i=e6(i||e,e)||i,t.filter(Boolean).forEach(function(e){var n=e6(a,e);n&&(i=!i||eq(n,i)?n:e6(n,i))})}),i},e4=function(e,n){return e.reduce(function(e,t){var r,o;return e.concat((r=t,o=n,eB(ey(r.querySelectorAll("[".concat("data-autofocus-inside","]"))).map(function(e){return eL([e])}).reduce(function(e,n){return e.concat(n)},[]),o)))},[])},e7=function(e,n){var t=new Map;return n.forEach(function(e){return t.set(e.node,e)}),e.map(function(e){return t.get(e)}).filter(eR)},ne=function(e,n){var t=eY(eb(e).length>0?document:eg(e).ownerDocument),r=eK(e).filter(eT),o=e8(t||e,e,r),a=new Map,i=eG(r,a),u=i.filter(function(e){return eT(e.node)});if(u[0]){var c=eG([o],a).map(function(e){return e.node}),l=e7(c,u),d=l.map(function(e){return e.node}),s=l.filter(function(e){return e.tabIndex>=0}).map(function(e){return e.node}),f=e5(d,s,c,t,n);if(f===e2){var v=e3(i,s,e4(r,a))||e3(i,d,e4(r,a));return v?{node:v}:void console.warn("focus-lock: cannot find any node to move focus into")}return void 0===f?f:l[f]}},nn=0,nt=!1,nr=function(e,n,t){void 0===t&&(t={});var r=ne(e,n);if(!nt&&r){if(nn>2){console.error("FocusLock: focus-fighting detected. Only one focus management system could be active. See https://github.com/theKashey/focus-lock/#focus-fighting"),nt=!0,setTimeout(function(){nt=!1},1);return}nn++,eV(r.node,t.focusOptions),nn--}};function no(e){if(!e)return null;if("undefined"==typeof WeakRef)return function(){return e||null};var n=e?new WeakRef(e):null;return function(){return(null==n?void 0:n.deref())||null}}var na=function(e){if(!e)return null;for(var n=[],t=e;t&&t!==document.body;)n.push({current:no(t),parent:no(t.parentElement),left:no(t.previousElementSibling),right:no(t.nextElementSibling)}),t=t.parentElement;return{element:no(e),stack:n,ownerDocument:e.ownerDocument}},ni=function(e){if(e)for(var n,t,r,o,a,i=e.stack,u=e.ownerDocument,c=new Map,l=0;l<i.length;l++){var d=i[l],s=null==(n=d.parent)?void 0:n.call(d);if(s&&u.contains(s)){for(var f=null==(t=d.left)?void 0:t.call(d),v=d.current(),m=s.contains(v)?v:void 0,p=null==(r=d.right)?void 0:r.call(d),h=eH([s],c),y=null!=(a=null!=(o=null!=m?m:null==f?void 0:f.nextElementSibling)?o:p)?a:f;y;){for(var b=0;b<h.length;b++){var g=h[b];if(null==y?void 0:y.contains(g.node))return g.node}y=y.nextElementSibling}if(h.length)return h[0].node}}},nu=function(e){var n=na(e);return function(){return ni(n)}},nc=function(e){var n=eK(e).filter(eT),t=ej(eL([e8(e,e,n)],!0),!0,!0),r=eL(n,!1);return t.map(function(e){var n=e.node;return{node:n,index:e.index,lockItem:r.indexOf(n)>=0,guard:eA(n)}})},nl=function(e,n,t){if(!e||!n)return console.error("no element or scope given"),{};var r=eb(n);if(r.every(function(n){return!eq(n,e)}))return console.error("Active element is not contained in the scope"),{};var o=t?eH(r,new Map):eG(r,new Map),a=o.findIndex(function(n){return n.node===e});if(-1!==a)return{prev:o[a-1],next:o[a+1],first:o[0],last:o[o.length-1]}},nd=function(e,n){var t=n?eH(eb(e),new Map):eG(eb(e),new Map);return{first:t[0],last:t[t.length-1]}},ns=function(e,n,t){void 0===n&&(n={});var r,o=(r=n,Object.assign({scope:document.body,cycle:!0,onlyTabbable:!0},r)),a=nl(e,o.scope,o.onlyTabbable);if(a){var i=t(a,o.cycle);i&&eV(i.node,o.focusOptions)}},nf=function(e,n,t){var r,o=nd(e,null==(r=n.onlyTabbable)||r)[t];o&&eV(o.node,n.focusOptions)};function nv(e){setTimeout(e,1)}var nm=function(e){return e&&"current"in e?e.current:e},np=function(){return document&&document.activeElement===document.body},nh=null,ny=null,nb=function(){return null},ng=null,nx=!1,nw=!1,nE=function(e,n){ng={observerNode:e,portaledElement:n}};function nk(e,n,t,r){var o=null,a=e;do{var i=r[a];if(i.guard)i.node.dataset.focusAutoGuard&&(o=i);else if(i.lockItem){if(a!==e)return;o=null}else break}while((a+=t)!==n);o&&(o.node.tabIndex=0)}var nN=function(e){return eG(e,new Map)},nM=function(){var e=!1;if(nh){var n=nh,t=n.observed,r=n.persistentFocus,o=n.autoFocus,a=n.shards,i=n.crossFrame,u=n.focusOptions,c=n.noFocusGuards,l=t||ng&&ng.portaledElement;if(np()&&ny&&ny!==document.body&&(!document.body.contains(ny)||!nN([(f=ny).parentNode]).some(function(e){return e.node===f}))){var d=nb();d&&d.focus()}var s=document&&document.activeElement;if(l){var f,v=[l].concat(a.map(nm).filter(Boolean));if((!s||(nh.whiteList||function(){return!0})(s))&&(r||function(){if(!(i?!!nx:"meanwhile"===nx)||!c||!ny||nw)return!1;var e=nN(v),n=e.findIndex(function(e){return e.node===ny});return 0===n||n===e.length-1}()||!(np()||eX())||!ny&&o)&&(l&&!(e$(v)||s&&v.some(function(e){return function e(n,t,r){return t&&(t.host===n&&(!t.activeElement||r.contains(t.activeElement))||t.parentNode&&e(n,t.parentNode,r))}(s,e,e)})||ng&&ng.portaledElement===s)&&(document&&!ny&&s&&!o?(s.blur&&s.blur(),document.body.focus()):(e=nr(v,ny,{focusOptions:u}),ng={})),(ny=document&&document.activeElement)!==document.body&&(nb=nu(ny)),nx=!1),document&&s!==document.activeElement&&document.querySelector("[data-focus-auto-guard]")){var m=document&&document.activeElement,p=nc(v),h=p.map(function(e){return e.node}).indexOf(m);h>-1&&(p.filter(function(e){var n=e.guard,t=e.node;return n&&t.dataset.focusAutoGuard}).forEach(function(e){return e.node.removeAttribute("tabIndex")}),nk(h,p.length,1,p),nk(h,-1,-1,p))}}}return e},nS=function(e){nM()&&e&&(e.stopPropagation(),e.preventDefault())},nO=function(){return nv(nM)},nC=function(){nw=!0},nA=function(){nw=!1,nx="just",nv(function(){nx="meanwhile"})},nT=function(){document.addEventListener("focusin",nS),document.addEventListener("focusout",nO),window.addEventListener("focus",nC),window.addEventListener("blur",nA)},nR=function(){document.removeEventListener("focusin",nS),document.removeEventListener("focusout",nO),window.removeEventListener("focus",nC),window.removeEventListener("blur",nA)},nP={moveFocusInside:nr,focusInside:e$,focusNextElement:function(e,n){void 0===n&&(n={}),ns(e,n,function(e,n){var t=e.next,r=e.first;return t||n&&r})},focusPrevElement:function(e,n){void 0===n&&(n={}),ns(e,n,function(e,n){var t=e.prev,r=e.last;return t||n&&r})},focusFirstElement:function(e,n){void 0===n&&(n={}),nf(e,n,"first")},focusLastElement:function(e,n){void 0===n&&(n={}),nf(e,n,"last")},captureFocusRestore:nu};ec.assignSyncMedium(function(e){var n=e.target,t=e.currentTarget;t.contains(n)||nE(t,n)}),el.assignMedium(nO),ed.assignMedium(function(e){return e(nP)});let nj=(o=function(e){return e.filter(function(e){return!e.disabled})},a=function(e){var n=e.slice(-1)[0];n&&!nh&&nT();var t=nh,r=t&&n&&n.id===t.id;nh=n,t&&!r&&(t.onDeactivation(),e.filter(function(e){return e.id===t.id}).length||t.returnFocus(!n)),n?(ny=null,r&&t.observed===n.observed||n.onActivation(nP),nM(!0),nv(nM)):(nR(),ny=null)},function(e){var n,t,r,i,u,c=[];function l(){a(u=o(c.map(function(e){return e.props})))}var d=function(n){function t(){return n.apply(this,arguments)||this}t.prototype=Object.create(n.prototype),t.prototype.constructor=t,ep(t,n),t.peek=function(){return u};var r=t.prototype;return r.componentDidMount=function(){c.push(this),l()},r.componentDidUpdate=function(){l()},r.componentWillUnmount=function(){var e=c.indexOf(this);c.splice(e,1),l()},r.render=function(){return f.createElement(e,this.props)},t}(f.PureComponent);return n=d,t="displayName",r="SideEffect("+(e.displayName||e.name||"Component")+")",(i=function(e,n){if("object"!=eh(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var r=t.call(e,n||"default");if("object"!=eh(r))return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(t,"string"),(t="symbol"==eh(i)?i:i+"")in n)?Object.defineProperty(n,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):n[t]=r,d})(function(){return null});var nF=(0,f.forwardRef)(function(e,n){return f.createElement(em,(0,eo.A)({sideCar:nj,ref:n},e))}),nI=em.propTypes||{};nI.sideCar,function(e,n){if(null!=e){var t={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(-1!==n.indexOf(r))continue;t[r]=e[r]}}}(nI,["sideCar"]),nF.propTypes={};var n_=t(15294);let nD=nF.default??nF,nL=e=>{let{initialFocusRef:n,finalFocusRef:t,contentRef:r,restoreFocus:o,children:a,isDisabled:i,autoFocus:c,persistentFocus:l,lockFocusAcrossFrames:d}=e,s=(0,f.useCallback)(()=>{n?.current?n.current.focus():r?.current&&0===(0,n_.ep)(r.current).length&&requestAnimationFrame(()=>{r.current?.focus()})},[n,r]),v=(0,f.useCallback)(()=>{t?.current?.focus()},[t]),m=o&&!t;return(0,u.jsx)(nD,{crossFrame:d,persistentFocus:l,autoFocus:c,disabled:i,onActivation:s,onDeactivation:v,returnFocus:m,children:a})};function nB(e){let{autoFocus:n,trapFocus:t,dialogRef:r,initialFocusRef:o,blockScrollOnMount:a,allowPinchZoom:i,finalFocusRef:c,returnFocusOnClose:l,preserveScrollBarGap:v,lockFocusAcrossFrames:m,isOpen:p}=(0,d.k3)(),[h,y]=(0,s.xQ)();(0,f.useEffect)(()=>{!h&&y&&setTimeout(y)},[h,y]);let b=(0,er.y)(r,p);return(0,u.jsx)(nL,{autoFocus:n,isDisabled:!t,initialFocusRef:o,finalFocusRef:c,restoreFocus:l,contentRef:r,lockFocusAcrossFrames:m,children:(0,u.jsx)(et,{removeScrollBar:!v,allowPinchZoom:i,enabled:1===b&&a,forwardProps:!0,children:e.children})})}nL.displayName="FocusLock";var nW=t(67233),nH=t(63203),nG=t(51133);let nq={initial:"initial",animate:"enter",exit:"exit",variants:{initial:({offsetX:e,offsetY:n,transition:t,transitionEnd:r,delay:o})=>({opacity:0,x:e,y:n,transition:t?.exit??nG.yA.exit(nG.jd.exit,o),transitionEnd:r?.exit}),enter:({transition:e,transitionEnd:n,delay:t})=>({opacity:1,x:0,y:0,transition:e?.enter??nG.yA.enter(nG.jd.enter,t),transitionEnd:n?.enter}),exit:({offsetY:e,offsetX:n,transition:t,transitionEnd:r,reverse:o,delay:a})=>{let i={x:n,y:e};return{opacity:0,transition:t?.exit??nG.yA.exit(nG.jd.exit,a),...o?{...i,transitionEnd:r?.exit}:{transitionEnd:{...i,...r?.exit}}}}}};(0,f.forwardRef)(function(e,n){let{unmountOnExit:t,in:r,reverse:o=!0,className:a,offsetX:i=0,offsetY:c=8,transition:d,transitionEnd:s,delay:f,animatePresenceProps:v,...m}=e,p=!t||r&&t,h=r||t?"enter":"exit",y={offsetX:i,offsetY:c,reverse:o,transition:d,transitionEnd:s,delay:f};return(0,u.jsx)(nH.N,{...v,custom:y,children:p&&(0,u.jsx)(nW.P.div,{ref:n,className:(0,l.cx)("chakra-offset-slide",a),custom:y,...nq,animate:h,...m})})}).displayName="SlideFade";let nU={initial:"exit",animate:"enter",exit:"exit",variants:{exit:({reverse:e,initialScale:n,transition:t,transitionEnd:r,delay:o})=>({opacity:0,...e?{scale:n,transitionEnd:r?.exit}:{transitionEnd:{scale:n,...r?.exit}},transition:t?.exit??nG.yA.exit(nG.jd.exit,o)}),enter:({transitionEnd:e,transition:n,delay:t})=>({opacity:1,scale:1,transition:n?.enter??nG.yA.enter(nG.jd.enter,t),transitionEnd:e?.enter})}};(0,f.forwardRef)(function(e,n){let{unmountOnExit:t,in:r,reverse:o=!0,initialScale:a=.95,className:i,transition:c,transitionEnd:d,delay:s,animatePresenceProps:f,...v}=e,m=!t||r&&t,p=r||t?"enter":"exit",h={initialScale:a,reverse:o,transition:c,transitionEnd:d,delay:s};return(0,u.jsx)(nH.N,{...f,custom:h,children:m&&(0,u.jsx)(nW.P.div,{ref:n,className:(0,l.cx)("chakra-offset-slide",i),...nU,animate:p,custom:h,...v})})}).displayName="ScaleFade";var nY=t(33225);let nX={slideInBottom:{...nq,custom:{offsetY:16,reverse:!0}},slideInRight:{...nq,custom:{offsetX:16,reverse:!0}},slideInTop:{...nq,custom:{offsetY:-16,reverse:!0}},slideInLeft:{...nq,custom:{offsetX:-16,reverse:!0}},scale:{...nU,custom:{initialScale:.95,reverse:!0}},none:{}},nZ=(0,nY.B)(nW.P.section),nz=e=>nX[e||"none"],nK=(0,f.forwardRef)((e,n)=>{let{preset:t,motionProps:r=nz(t),...o}=e;return(0,u.jsx)(nZ,{ref:n,...r,...o})});nK.displayName="ModalTransition";let n$=(0,t(2923).R)((e,n)=>{let{className:t,children:r,containerProps:o,motionProps:a,...i}=e,{getDialogProps:s,getDialogContainerProps:f}=(0,d.k3)(),v=s(i,n),m=f(o),p=(0,l.cx)("chakra-modal__content",t),h=(0,d.x5)(),y=(0,c.H2)({display:"flex",flexDirection:"column",position:"relative",width:"100%",outline:0,...h.dialog}),b=(0,c.H2)({display:"flex",width:"100vw",height:"$100vh",position:"fixed",left:0,top:0,...h.dialogContainer}),{motionPreset:g}=(0,d.k3)();return(0,u.jsx)(nB,{children:(0,u.jsx)(nY.B.div,{...m,className:"chakra-modal__content-container",tabIndex:-1,__css:b,children:(0,u.jsx)(nK,{preset:g,motionProps:a,className:p,...v,__css:y,children:r})})})});n$.displayName="ModalContent"},82531:(e,n,t)=>{t.d(n,{J:()=>c,y:()=>l});var r=t(94285),o=Object.defineProperty,a=(e,n,t)=>n in e?o(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,i=(e,n,t)=>(a(e,"symbol"!=typeof n?n+"":n,t),t);class u{constructor(){i(this,"modals"),this.modals=new Set}add(e){return this.modals.add(e),this.modals.size}remove(e){this.modals.delete(e)}isTopModal(e){return!!e&&e===Array.from(this.modals)[this.modals.size-1]}}let c=new u;function l(e,n){let[t,o]=(0,r.useState)(0);return(0,r.useEffect)(()=>{let t=e.current;if(t)return n&&o(c.add(t)),()=>{c.remove(t),o(0)}},[n,e]),t}},85104:(e,n,t)=>{t.d(n,{c:()=>l});var r=t(94513),o=t(22697),a=t(94285),i=t(9557),u=t(2923),c=t(33225);let l=(0,u.R)((e,n)=>{let{className:t,...u}=e,{bodyId:l,setBodyMounted:d}=(0,i.k3)();(0,a.useEffect)(()=>(d(!0),()=>d(!1)),[d]);let s=(0,o.cx)("chakra-modal__body",t),f=(0,i.x5)();return(0,r.jsx)(c.B.div,{ref:n,className:s,id:l,...u,__css:f.body})});l.displayName="ModalBody"}}]);