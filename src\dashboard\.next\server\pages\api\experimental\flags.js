"use strict";(()=>{var e={};e.id=9361,e.ids=[9361],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2290:(e,t,o)=>{o.d(t,{L:()=>s});var a=o(8580),r=o(2518);let n=null,i=null;async function s(){if(i)return i;let e=a.dashboardConfig.database?.url||"mongodb://localhost:27017",t=a.dashboardConfig.database?.name||"discord_bot";return n||(n=await r.MongoClient.connect(e,{...a.dashboardConfig.database?.options||{}})),i=n.db(t)}},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),r=o(2115),n=o.n(r),i=o(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");s=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")},9216:(e,t,o)=>{o.r(t),o.d(t,{config:()=>c,default:()=>u,routeModule:()=>f});var a={};o.r(a),o.d(a,{default:()=>l});var r=o(3433),n=o(264),i=o(584),s=o(5806),d=o(2290);async function l(e,t){let o=await (0,s.getServerSession)(e,t,Object(function(){var e=Error("Cannot find module '../auth/[...nextauth]'");throw e.code="MODULE_NOT_FOUND",e}()));if(!o)return t.status(401).json({error:"Unauthorized"});let a=(await (0,d.L)()).collection("experimental_flags");if("GET"===e.method){let e=await a.find({}).toArray();return t.status(200).json({flags:e})}if("POST"===e.method){if(o.user?.id!=="933023999770918932")return t.status(403).json({error:"Forbidden"});let{feature:r,enabled:n=!1}=e.body;if(!r)return t.status(400).json({error:"feature required"});await a.updateOne({feature:r},{$set:{feature:r,enabled:n,updatedBy:o.user.id,updatedAt:new Date}},{upsert:!0});let i=await a.findOne({feature:r});return t.status(200).json({flag:i})}return t.status(405).json({error:"Method not allowed"})}!function(){var e=Error("Cannot find module '../auth/[...nextauth]'");throw e.code="MODULE_NOT_FOUND",e}();let u=(0,i.M)(a,"default"),c=(0,i.M)(a,"config"),f=new r.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/experimental/flags",pathname:"/api/experimental/flags",bundlePath:"",filename:""},userland:a})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=9216);module.exports=o})();