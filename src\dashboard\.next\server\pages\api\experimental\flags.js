"use strict";(()=>{var e={};e.id=9361,e.ids=[9361],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2290:(e,t,o)=>{o.d(t,{L:()=>s});var a=o(8580),r=o(2518);let n=null,i=null;async function s(){if(i)return i;let e=a.dashboardConfig.database?.url||"mongodb://localhost:27017",t=a.dashboardConfig.database?.name||"discord_bot";return n||(n=await r.MongoClient.connect(e,{...a.dashboardConfig.database?.options||{}})),i=n.db(t)}},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var a=o(5542),r=o.n(a);let n=require("next-auth/providers/discord");var i=o.n(n),s=o(8580);let d={providers:[i()({clientId:s.dashboardConfig.bot.clientId,clientSecret:s.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let r=!1;if(o)if((s.dashboardConfig.dashboard.admins||[]).includes(o))r=!0;else{let e=s.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&s.dashboardConfig.bot.token&&s.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${s.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${s.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();r=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:s.dashboardConfig.dashboard.session.secret||s.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=r()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),r=o(2115),n=o.n(r),i=o(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");s=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")},9216:(e,t,o)=>{o.r(t),o.d(t,{config:()=>b,default:()=>c,routeModule:()=>f});var a={};o.r(a),o.d(a,{default:()=>u});var r=o(3433),n=o(264),i=o(584),s=o(5806),d=o(8525),l=o(2290);async function u(e,t){let o=await (0,s.getServerSession)(e,t,d.authOptions);if(!o)return t.status(401).json({error:"Unauthorized"});let a=(await (0,l.L)()).collection("experimental_flags");if("GET"===e.method){let e=await a.find({}).toArray();return t.status(200).json({flags:e})}if("POST"===e.method){if(o.user?.id!=="933023999770918932")return t.status(403).json({error:"Forbidden"});let{feature:r,enabled:n=!1}=e.body;if(!r)return t.status(400).json({error:"feature required"});await a.updateOne({feature:r},{$set:{feature:r,enabled:n,updatedBy:o.user.id,updatedAt:new Date}},{upsert:!0});let i=await a.findOne({feature:r});return t.status(200).json({flag:i})}return t.status(405).json({error:"Method not allowed"})}let c=(0,i.M)(a,"default"),b=(0,i.M)(a,"config"),f=new r.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/experimental/flags",pathname:"/api/experimental/flags",bundlePath:"",filename:""},userland:a})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=9216);module.exports=o})();