// @ts-nocheck
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dal<PERSON>ontent,
  <PERSON>dal<PERSON>eader,
  <PERSON>dalBody,
  Modal<PERSON>ooter,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  Select,
  Switch,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberI<PERSON><PERSON>entStepper,
  NumberDecrementStepper,
  VStack,
  useToast,
} from '@chakra-ui/react';
import { useState, useEffect } from 'react';

const CHANNEL_TYPES = {
  GUILD_TEXT: 0,
  GUILD_VOICE: 2,
  GUILD_CATEGORY: 4,
  GUILD_ANNOUNCEMENT: 5,
};

interface EditChannelDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  channel: any;
  categories: any[];
}

export default function EditChannelDialog({
  isOpen,
  onClose,
  onSuccess,
  channel,
  categories,
}: EditChannelDialogProps) {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    type: 0,
    topic: '',
    nsfw: false,
    bitrate: 64000,
    userLimit: 0,
    parent: '',
    rateLimitPerUser: 0,
  });

  useEffect(() => {
    if (channel) {
      setFormData({
        name: channel.name || '',
        type: channel.raw_type || 0,
        topic: channel.topic || '',
        nsfw: channel.nsfw || false,
        bitrate: channel.bitrate || 64000,
        userLimit: channel.user_limit || 0,
        parent: channel.parent_id || '',
        rateLimitPerUser: channel.rate_limit_per_user || 0,
      });
    }
  }, [channel]);

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/discord/channels/${channel.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          topic: formData.topic,
          nsfw: formData.nsfw,
          bitrate: formData.type === CHANNEL_TYPES.GUILD_VOICE ? formData.bitrate : undefined,
          user_limit: formData.type === CHANNEL_TYPES.GUILD_VOICE ? formData.userLimit : undefined,
          parent_id: formData.parent || null,
          rate_limit_per_user: formData.type === CHANNEL_TYPES.GUILD_TEXT ? formData.rateLimitPerUser : undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update channel');
      }

      toast({
        title: 'Success',
        description: 'Channel updated successfully',
        status: 'success',
        duration: 3000,
      });

      onSuccess();
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update channel',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent bg="gray.800">
        <ModalHeader>Edit Channel</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={4}>
            <FormControl>
              <FormLabel>Channel Name</FormLabel>
              <Input
                placeholder="Enter channel name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
              />
            </FormControl>

            {formData.type === CHANNEL_TYPES.GUILD_TEXT && (
              <>
                <FormControl>
                  <FormLabel>Channel Topic</FormLabel>
                  <Input
                    placeholder="Enter channel topic"
                    value={formData.topic}
                    onChange={(e) => handleChange('topic', e.target.value)}
                  />
                </FormControl>

                <FormControl>
                  <FormLabel>Slowmode (seconds)</FormLabel>
                  <NumberInput
                    min={0}
                    max={21600}
                    value={formData.rateLimitPerUser}
                    onChange={(value) => handleChange('rateLimitPerUser', parseInt(value))}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>

                <FormControl display="flex" alignItems="center">
                  <FormLabel htmlFor="nsfw" mb="0">
                    Age-Restricted (NSFW)
                  </FormLabel>
                  <Switch
                    id="nsfw"
                    isChecked={formData.nsfw}
                    onChange={(e) => handleChange('nsfw', e.target.checked)}
                  />
                </FormControl>
              </>
            )}

            {formData.type === CHANNEL_TYPES.GUILD_VOICE && (
              <>
                <FormControl>
                  <FormLabel>Bitrate (kbps)</FormLabel>
                  <NumberInput
                    min={8}
                    max={96}
                    value={formData.bitrate / 1000}
                    onChange={(value) => handleChange('bitrate', parseInt(value) * 1000)}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>

                <FormControl>
                  <FormLabel>User Limit</FormLabel>
                  <NumberInput
                    min={0}
                    max={99}
                    value={formData.userLimit}
                    onChange={(value) => handleChange('userLimit', parseInt(value))}
                  >
                    <NumberInputField />
                    <NumberInputStepper>
                      <NumberIncrementStepper />
                      <NumberDecrementStepper />
                    </NumberInputStepper>
                  </NumberInput>
                </FormControl>
              </>
            )}

            {formData.type !== CHANNEL_TYPES.GUILD_CATEGORY && (
              <FormControl>
                <FormLabel>Parent Category</FormLabel>
                <Select
                  value={formData.parent}
                  onChange={(e) => handleChange('parent', e.target.value)}
                >
                  <option value="">None</option>
                  {(categories || []).map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </Select>
              </FormControl>
            )}
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={isLoading}
          >
            Save Changes
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 