// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { dashboardConfig } from '../../../../core/config';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // For sensitive operations like editing/deleting channels we require admin.
    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Forbidden - Admin access required' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    const { channelId } = req.query;
    if (!channelId || typeof channelId !== 'string') {
      return res.status(400).json({ error: 'Channel ID is required' });
    }

    if (req.method === 'PATCH') {
      try {
        console.log('Updating channel:', channelId, 'with data:', req.body);
        const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bot ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(req.body),
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        const updatedChannel = await response.json();
        return res.status(200).json(updatedChannel);
      } catch (error) {
        console.error('Error updating channel:', error);
        return res.status(500).json({ error: 'Failed to update channel' });
      }
    }

    if (req.method === 'DELETE') {
      try {
        console.log('Deleting channel:', channelId);
        const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {
          method: 'DELETE',
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!response.ok) {
          let error;
          try {
            error = await response.json();
          } catch {
            error = await response.text();
          }
          console.error('Discord API error:', error);
          return res.status(response.status).json(error);
        }

        return res.status(200).json({ message: 'Channel deleted successfully' });
      } catch (error) {
        console.error('Error deleting channel:', error);
        return res.status(500).json({ error: 'Failed to delete channel' });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in channel handler:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 