(()=>{var e={};e.id=2731,e.ids=[2731,3220],e.modules={361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},786:(e,t)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},1195:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return d}});let n=r(2403),o=r(8732),a=n._(r(2015)),s=n._(r(8193)),i={400:"Bad Request",404:"This page could not be found",405:"Method Not Allowed",500:"Internal Server Error"};function c(e){let t,{req:n,res:o,err:a}=e,s=o&&o.statusCode?o.statusCode:a?a.statusCode:404;if(n){let{getRequestMeta:e}=r(4507),o=e(n,"initURL");o&&(t=new URL(o).hostname)}return{statusCode:s,hostname:t}}let l={error:{fontFamily:'system-ui,"Segoe UI",Roboto,Helvetica,Arial,sans-serif,"Apple Color Emoji","Segoe UI Emoji"',height:"100vh",textAlign:"center",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center"},desc:{lineHeight:"48px"},h1:{display:"inline-block",margin:"0 20px 0 0",paddingRight:23,fontSize:24,fontWeight:500,verticalAlign:"top"},h2:{fontSize:14,fontWeight:400,lineHeight:"28px"},wrap:{display:"inline-block"}};class d extends a.default.Component{render(){let{statusCode:e,withDarkMode:t=!0}=this.props,r=this.props.title||i[e]||"An unexpected error has occurred";return(0,o.jsxs)("div",{style:l.error,children:[(0,o.jsx)(s.default,{children:(0,o.jsx)("title",{children:e?e+": "+r:"Application error: a client-side exception has occurred"})}),(0,o.jsxs)("div",{style:l.desc,children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}"+(t?"@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}":"")}}),e?(0,o.jsx)("h1",{className:"next-error-h1",style:l.h1,children:e}):null,(0,o.jsx)("div",{style:l.wrap,children:(0,o.jsxs)("h2",{style:l.h2,children:[this.props.title||e?r:(0,o.jsxs)(o.Fragment,{children:["Application error: a client-side exception has occurred"," ",!!this.props.hostname&&(0,o.jsxs)(o.Fragment,{children:["while loading ",this.props.hostname]})," ","(see the browser console for more information)"]}),"."]})})]})]})}}d.displayName="ErrorPage",d.getInitialProps=c,d.origGetInitialProps=c,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2015:e=>{"use strict";e.exports=require("react")},2742:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}t._=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var i=a?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(o,s,i):o[s]=e[s]}return o.default=e,n&&n.set(e,o),o}},3001:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{DP:()=>u,NP:()=>f,nk:()=>l});var o=r(8732),a=r(2015),s=r(9733),i=r(6390),c=e([s,i]);[s,i]=c.then?(await c)():c;let l=[{id:"default",name:"Default Purple",description:"Classic purple and blue gradient theme",colors:{primary:"#8b5cf6",primaryLight:"#a78bfa",primaryDark:"#7c3aed",secondary:"#5865F2",accent:"#06b6d4",background:"#1a202c",surface:"rgba(255,255,255,0.03)",text:"#f7fafc",textSecondary:"#a0aec0",border:"rgba(255,255,255,0.2)",success:"#68d391",warning:"#fbb6ce",error:"#fc8181",info:"#63b3ed"}},{id:"ocean",name:"Ocean Blue",description:"Deep blue ocean-inspired theme",colors:{primary:"#0ea5e9",primaryLight:"#38bdf8",primaryDark:"#0284c7",secondary:"#06b6d4",accent:"#8b5cf6",background:"#0f172a",surface:"rgba(59, 130, 246, 0.05)",text:"#f1f5f9",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}},{id:"forest",name:"Forest Green",description:"Nature-inspired green theme",colors:{primary:"#059669",primaryLight:"#10b981",primaryDark:"#047857",secondary:"#065f46",accent:"#8b5cf6",background:"#0f1419",surface:"rgba(16, 185, 129, 0.05)",text:"#f0fdf4",textSecondary:"#86efac",border:"rgba(16, 185, 129, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"sunset",name:"Sunset Orange",description:"Warm sunset-inspired theme",colors:{primary:"#ea580c",primaryLight:"#fb923c",primaryDark:"#c2410c",secondary:"#dc2626",accent:"#8b5cf6",background:"#1c1917",surface:"rgba(251, 146, 60, 0.05)",text:"#fef7ed",textSecondary:"#fdba74",border:"rgba(251, 146, 60, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"rose",name:"Rose Pink",description:"Elegant rose and pink theme",colors:{primary:"#e11d48",primaryLight:"#f43f5e",primaryDark:"#be123c",secondary:"#ec4899",accent:"#8b5cf6",background:"#1f1720",surface:"rgba(244, 63, 94, 0.05)",text:"#fdf2f8",textSecondary:"#fda4af",border:"rgba(244, 63, 94, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"midnight",name:"Midnight Blue",description:"Dark midnight blue theme",colors:{primary:"#1e40af",primaryLight:"#3b82f6",primaryDark:"#1e3a8a",secondary:"#4338ca",accent:"#06b6d4",background:"#0c0a1f",surface:"rgba(59, 130, 246, 0.05)",text:"#f8fafc",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}}],d=(0,a.createContext)(void 0),u=()=>{let e=(0,a.useContext)(d);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},f=({children:e})=>{let[t,r]=(0,a.useState)(l[0]),[n,c]=(0,a.useState)([]);(0,a.useEffect)(()=>{let e=localStorage.getItem("dashboard-color-scheme"),t=localStorage.getItem("dashboard-custom-schemes");if(t)try{let e=JSON.parse(t);c(e)}catch(e){}if(e){let t=l.find(t=>t.id===e);if(t)r(t);else{let t=localStorage.getItem("dashboard-custom-schemes");if(t)try{let n=JSON.parse(t).find(t=>t.id===e);n&&r(n)}catch(e){}}}},[]),(0,a.useEffect)(()=>{localStorage.setItem("dashboard-color-scheme",t.id)},[t]),(0,a.useEffect)(()=>{localStorage.setItem("dashboard-custom-schemes",JSON.stringify(n))},[n]);let u=[...l,...n],f=(0,s.extendTheme)({...i.A,colors:{...i.A.colors,brand:{50:t.colors.primaryLight+"20",100:t.colors.primaryLight+"40",200:t.colors.primaryLight+"60",300:t.colors.primaryLight+"80",400:t.colors.primaryLight,500:t.colors.primary,600:t.colors.primaryDark,700:t.colors.primaryDark+"CC",800:t.colors.primaryDark+"AA",900:t.colors.primaryDark+"88"},custom:{primary:t.colors.primary,primaryLight:t.colors.primaryLight,primaryDark:t.colors.primaryDark,secondary:t.colors.secondary,accent:t.colors.accent,background:t.colors.background,surface:t.colors.surface,text:t.colors.text,textSecondary:t.colors.textSecondary,border:t.colors.border,success:t.colors.success,warning:t.colors.warning,error:t.colors.error,info:t.colors.info}},styles:{global:{body:{bg:t.colors.background,color:t.colors.text}}}});return(0,o.jsx)(d.Provider,{value:{currentScheme:t,setColorScheme:e=>{let t=l.find(t=>t.id===e);if(t)return void r(t);let o=n.find(t=>t.id===e);o&&r(o)},colorSchemes:u,customSchemes:n,addCustomScheme:e=>{c(t=>[...t.filter(t=>t.id!==e.id),e]),r(e)},deleteCustomScheme:e=>{c(t=>t.filter(t=>t.id!==e)),t.id===e&&r(l[0])},resetToDefault:()=>{r(l[0])}},children:(0,o.jsx)(s.ChakraProvider,{theme:f,children:e})})};n()}catch(e){n(e)}})},3118:(e,t,r)=>{"use strict";e.exports=r(1292).vendored.contexts.AmpContext},3320:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{config:()=>b,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>p,getStaticProps:()=>f,reportWebVitals:()=>g,routeModule:()=>v,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>m});var o=r(1292),a=r(8834),s=r(786),i=r(3567),c=r(8077),l=r(1195),d=e([c]);c=(d.then?(await d)():d)[0];let u=(0,s.M)(l,"default"),f=(0,s.M)(l,"getStaticProps"),p=(0,s.M)(l,"getStaticPaths"),h=(0,s.M)(l,"getServerSideProps"),b=(0,s.M)(l,"config"),g=(0,s.M)(l,"reportWebVitals"),m=(0,s.M)(l,"unstable_getStaticProps"),y=(0,s.M)(l,"unstable_getStaticPaths"),x=(0,s.M)(l,"unstable_getStaticParams"),S=(0,s.M)(l,"unstable_getServerProps"),P=(0,s.M)(l,"unstable_getServerSideProps"),v=new o.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/_error",pathname:"/_error",bundlePath:"",filename:""},components:{App:c.default,Document:i.default},userland:l});n()}catch(e){n(e)}})},3567:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(8732),o=r(8270);function a(){return(0,n.jsxs)(o.Html,{lang:"en",children:[(0,n.jsx)(o.Head,{}),(0,n.jsxs)("body",{children:[(0,n.jsx)(o.Main,{}),(0,n.jsx)(o.NextScript,{})]})]})}},3873:e=>{"use strict";e.exports=require("path")},4507:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{NEXT_REQUEST_META:function(){return r},addRequestMeta:function(){return a},getRequestMeta:function(){return n},removeRequestMeta:function(){return s},setRequestMeta:function(){return o}});let r=Symbol.for("NextInternalRequestMeta");function n(e,t){let n=e[r]||{};return"string"==typeof t?n[t]:n}function o(e,t){return e[r]=t,t}function a(e,t,r){let a=n(e);return a[t]=r,o(e,a)}function s(e,t){let r=n(e);return delete r[t],o(e,r)}},4722:e=>{"use strict";e.exports=require("next-auth/react")},4959:(e,t,r)=>{e.exports=r(8193)},5100:(e,t,r)=>{"use strict";e.exports=r(1292).vendored.contexts.HeadManagerContext},5416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let n=r(2015),o=()=>{},a=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function i(){if(r&&r.mountedInstances){let t=n.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),i(),o(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),o(()=>(r&&(r._pendingUpdate=i),()=>{r&&(r._pendingUpdate=i)})),a(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},5817:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6390:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.d(t,{A:()=>s});var o=r(9733),a=e([o]);o=(a.then?(await a)():a)[0];let s=(0,o.extendTheme)({config:{initialColorMode:"dark",useSystemColorMode:!1},fonts:{heading:"'Inter', sans-serif",body:"'Inter', sans-serif"},colors:{brand:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},discord:{50:"#e8e9fd",100:"#d1d3fc",200:"#b9bcfa",300:"#a2a5f9",400:"#8b8ef7",500:"#5865F2",600:"#4752c4",700:"#363f97",800:"#242c69",900:"#12193c"}},styles:{global:{body:{bg:"gray.900",color:"gray.100"}}},components:{Button:{defaultProps:{colorScheme:"brand"},variants:{solid:e=>({bg:`${e.colorScheme}.500`,color:"white",_hover:{bg:`${e.colorScheme}.600`,transform:"translateY(-2px)",boxShadow:"lg"},_active:{bg:`${e.colorScheme}.700`,transform:"translateY(0)"},transition:"all 0.2s ease"})}},Link:{baseStyle:{_hover:{textDecoration:"none"}}},Box:{baseStyle:{transition:"all 0.2s ease"}}}});n()}catch(e){n(e)}})},6665:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:n=!1}=void 0===e?{}:e;return t||r&&n}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},8077:(e,t,r)=>{"use strict";r.a(e,async(e,n)=>{try{r.r(t),r.d(t,{default:()=>u});var o=r(8732),a=r(4722),s=r(4959),i=r.n(s),c=r(3001),l=e([c]);function d({Component:e,pageProps:t}){return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsxs)(i(),{children:[(0,o.jsx)("title",{children:"404 Bot Dashboard"}),(0,o.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,o.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,o.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,o.jsx)("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,o.jsx)(e,{...t})]})}function u({Component:e,pageProps:{session:t,...r}}){return(0,o.jsx)(a.SessionProvider,{session:t,children:(0,o.jsx)(c.NP,{children:(0,o.jsx)(d,{Component:e,pageProps:r})})})}c=(l.then?(await l)():l)[0],n()}catch(e){n(e)}})},8193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},defaultHead:function(){return u}});let n=r(2403),o=r(2742),a=r(8732),s=o._(r(2015)),i=n._(r(5416)),c=r(3118),l=r(5100),d=r(6665);function u(e){void 0===e&&(e=!1);let t=[(0,a.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,a.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(5817);let p=["name","httpEquiv","charSet","itemProp"];function h(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,n={};return o=>{let a=!0,s=!1;if(o.key&&"number"!=typeof o.key&&o.key.indexOf("$")>0){s=!0;let t=o.key.slice(o.key.indexOf("$")+1);e.has(t)?a=!1:e.add(t)}switch(o.type){case"title":case"base":t.has(o.type)?a=!1:t.add(o.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(o.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?a=!1:r.add(t);else{let e=o.props[t],r=n[t]||new Set;("name"!==t||!s)&&r.has(e)?a=!1:(r.add(e),n[t]=r)}}}return a}}()).reverse().map((e,t)=>{let n=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:n})})}let b=function(e){let{children:t}=e,r=(0,s.useContext)(c.AmpStateContext),n=(0,s.useContext)(l.HeadManagerContext);return(0,a.jsx)(i.default,{reduceComponentsToState:h,headManager:n,inAmpMode:(0,d.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},8834:(e,t)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[8270],()=>r(3320));module.exports=n})();