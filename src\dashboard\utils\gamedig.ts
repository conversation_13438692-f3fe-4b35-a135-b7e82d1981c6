const gamedig = require('gamedig');

interface GameField {
  name: string;
  type: 'string' | 'number' | 'boolean';
  required: boolean;
  default?: any;
  description: string;
}

interface GameConfig {
  name: string;
  fields: GameField[];
  defaultPort?: number;
}

// Game-specific configurations
const gameConfigs: { [key: string]: GameConfig } = {
  minecraft: {
    name: 'Minecraft',
    defaultPort: 25565,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 25565,
        description: 'Port number of the server'
      }
    ]
  },
  minecraftbe: {
    name: 'Minecraft: Bedrock Edition',
    defaultPort: 19132,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 19132,
        description: 'Port number of the server'
      }
    ]
  },
  minecraftpe: {
    name: 'Minecraft: Pocket Edition',
    defaultPort: 19132,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 19132,
        description: 'Port number of the server'
      }
    ]
  },
  fivem: {
    name: 'FiveM (GTA V)',
    defaultPort: 30120,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 30120,
        description: 'Port number of the server'
      }
    ]
  },
  csgo: {
    name: 'Counter-Strike: Global Offensive',
    defaultPort: 27015,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 27015,
        description: 'Port number of the server'
      }
    ]
  },
  cs2: {
    name: 'Counter-Strike 2',
    defaultPort: 27015,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 27015,
        description: 'Port number of the server'
      }
    ]
  },
  valheim: {
    name: 'Valheim',
    defaultPort: 2456,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 2456,
        description: 'Port number of the server'
      },
      {
        name: 'queryPort',
        type: 'number',
        required: false,
        description: 'Query port (if different from server port)'
      }
    ]
  },
  sdtd: {
    name: '7 Days to Die',
    defaultPort: 26900,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 26900,
        description: 'Game port (default: 26900)'
      },
      {
        name: 'queryPort',
        type: 'number',
        required: false,
        description: 'Query port (if different from game port)'
      }
    ]
  },
  teamspeak3: {
    name: 'TeamSpeak 3',
    defaultPort: 9987,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 9987,
        description: 'UDP port (default: 9987)'
      },
      {
        name: 'queryPort',
        type: 'number',
        required: false,
        default: 10011,
        description: 'TCP query port (default: 10011)'
      }
    ]
  },
  palworld: {
    name: 'Palworld',
    defaultPort: 8211,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 8211,
        description: 'Game port (default: 8211)'
      },
      {
        name: 'queryPort',
        type: 'number',
        required: false,
        description: 'Query port (if different from game port)'
      }
    ]
  },
  rust: {
    name: 'Rust',
    defaultPort: 28015,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 28015,
        description: 'Game port (default: 28015)'
      },
      {
        name: 'queryPort',
        type: 'number',
        required: false,
        description: 'Query port (if different from game port)'
      },
      {
        name: 'rconPort',
        type: 'number',
        required: false,
        default: 28016,
        description: 'RCON port (default: 28016)'
      },
      {
        name: 'rconPassword',
        type: 'string',
        required: false,
        description: 'RCON password (if RCON is enabled)'
      }
    ]
  },
  arkse: {
    name: 'ARK: Survival Evolved',
    defaultPort: 7777,
    fields: [
      {
        name: 'port',
        type: 'number',
        required: true,
        default: 7777,
        description: 'Game port (default: 7777)'
      },
      {
        name: 'queryPort',
        type: 'number',
        required: false,
        default: 27015,
        description: 'Query port (default: 27015)'
      }
    ]
  }
};

// Get all supported games from GameDig
export function getSupportedGames(): { id: string; name: string }[] {
  // Full list of supported games from GameDig
  const gameTypes = [
    { id: 'arkse', name: 'ARK: Survival Evolved' },
    { id: 'arma2', name: 'ARMA 2' },
    { id: 'arma3', name: 'ARMA 3' },
    { id: 'armar', name: 'ARMA Reforger' },
    { id: 'assettocorsa', name: 'Assetto Corsa' },
    { id: 'atlas', name: 'Atlas' },
    { id: 'avp2', name: 'Aliens vs. Predator 2' },
    { id: 'avp2010', name: 'Aliens vs. Predator 2010' },
    { id: 'baldursgate', name: "Baldur's Gate" },
    { id: 'battalion1944', name: 'Battalion 1944' },
    { id: 'bf1942', name: 'Battlefield 1942' },
    { id: 'bf2', name: 'Battlefield 2' },
    { id: 'bf2142', name: 'Battlefield 2142' },
    { id: 'bf3', name: 'Battlefield 3' },
    { id: 'bf4', name: 'Battlefield 4' },
    { id: 'bfh', name: 'Battlefield Hardline' },
    { id: 'bfv', name: 'Battlefield Vietnam' },
    { id: 'breach', name: 'Breach' },
    { id: 'breed', name: 'Breed' },
    { id: 'brink', name: 'Brink' },
    { id: 'buildandshoot', name: 'Build and Shoot' },
    { id: 'cod', name: 'Call of Duty' },
    { id: 'cod2', name: 'Call of Duty 2' },
    { id: 'cod3', name: 'Call of Duty 3' },
    { id: 'cod4', name: 'Call of Duty 4: Modern Warfare' },
    { id: 'codmw2', name: 'Call of Duty: Modern Warfare 2' },
    { id: 'codmw3', name: 'Call of Duty: Modern Warfare 3' },
    { id: 'coduo', name: 'Call of Duty: United Offensive' },
    { id: 'codwaw', name: 'Call of Duty: World at War' },
    { id: 'conanexiles', name: 'Conan Exiles' },
    { id: 'cs2', name: 'Counter-Strike 2' },
    { id: 'csgo', name: 'Counter-Strike: Global Offensive' },
    { id: 'css', name: 'Counter-Strike: Source' },
    { id: 'creativerse', name: 'Creativerse' },
    { id: 'crysis', name: 'Crysis' },
    { id: 'crysiswars', name: 'Crysis Wars' },
    { id: 'dayz', name: 'DayZ' },
    { id: 'dayzmod', name: 'DayZ Mod' },
    { id: 'dod', name: 'Day of Defeat' },
    { id: 'dods', name: 'Day of Defeat: Source' },
    { id: 'doi', name: 'Day of Infamy' },
    { id: 'doom3', name: 'Doom 3' },
    { id: 'dota2', name: 'Dota 2' },
    { id: 'eco', name: 'Eco' },
    { id: 'empyrion', name: 'Empyrion - Galactic Survival' },
    { id: 'enshrouded', name: 'Enshrouded' },
    { id: 'factorio', name: 'Factorio' },
    { id: 'farcry', name: 'Far Cry' },
    { id: 'farcry2', name: 'Far Cry 2' },
    { id: 'fear', name: 'F.E.A.R.' },
    { id: 'fivem', name: 'FiveM (GTA V)' },
    { id: 'forrest', name: 'The Forrest' },
    { id: 'fs22', name: 'Farming Simulator 22' },
    { id: 'garrysmod', name: "Garry's Mod" },
    { id: 'grav', name: 'GRAV Online' },
    { id: 'gta5m', name: 'GTA V Multiplayer' },
    { id: 'hl2dm', name: 'Half-Life 2: Deathmatch' },
    { id: 'hldm', name: 'Half-Life Deathmatch' },
    { id: 'hldms', name: 'Half-Life Deathmatch: Source' },
    { id: 'insurgency', name: 'Insurgency' },
    { id: 'insurgencysandstorm', name: 'Insurgency: Sandstorm' },
    { id: 'jc2mp', name: 'Just Cause 2 Multiplayer' },
    { id: 'jc3mp', name: 'Just Cause 3 Multiplayer' },
    { id: 'killingfloor', name: 'Killing Floor' },
    { id: 'killingfloor2', name: 'Killing Floor 2' },
    { id: 'l4d', name: 'Left 4 Dead' },
    { id: 'l4d2', name: 'Left 4 Dead 2' },
    { id: 'minecraft', name: 'Minecraft' },
    { id: 'minecraftbe', name: 'Minecraft: Bedrock Edition' },
    { id: 'minecraftpe', name: 'Minecraft: Pocket Edition' },
    { id: 'mordhau', name: 'Mordhau' },
    { id: 'mtasa', name: 'Multi Theft Auto: San Andreas' },
    { id: 'mumble', name: 'Mumble' },
    { id: 'nascarthunder2004', name: 'NASCAR Thunder 2004' },
    { id: 'naturalselection', name: 'Natural Selection' },
    { id: 'naturalselection2', name: 'Natural Selection 2' },
    { id: 'nwn', name: 'Neverwinter Nights' },
    { id: 'nwn2', name: 'Neverwinter Nights 2' },
    { id: 'onset', name: 'Onset' },
    { id: 'openttd', name: 'OpenTTD' },
    { id: 'palworld', name: 'Palworld' },
    { id: 'payday2', name: 'PAYDAY 2' },
    { id: 'pixark', name: 'PixARK' },
    { id: 'projectcars', name: 'Project CARS' },
    { id: 'projectzomboid', name: 'Project Zomboid' },
    { id: 'quake1', name: 'Quake 1' },
    { id: 'quake2', name: 'Quake 2' },
    { id: 'quake3', name: 'Quake 3' },
    { id: 'quake4', name: 'Quake 4' },
    { id: 'ragnarok', name: 'Ragnarok Online' },
    { id: 'redm', name: 'RedM (Red Dead Redemption 2)' },
    { id: 'rfactor', name: 'rFactor' },
    { id: 'ragemp', name: 'RAGE:MP' },
    { id: 'redorchestra', name: 'Red Orchestra' },
    { id: 'redorchestra2', name: 'Red Orchestra 2' },
    { id: 'redorchestraost', name: 'Red Orchestra: Ostfront 41-45' },
    { id: 'rust', name: 'Rust' },
    { id: 'samp', name: 'San Andreas Multiplayer' },
    { id: 'satisfactory', name: 'Satisfactory' },
    { id: 'sdtd', name: '7 Days to Die' },
    { id: 'ship', name: 'The Ship' },
    { id: 'sniperelitev2', name: 'Sniper Elite V2' },
    { id: 'soldat', name: 'Soldat' },
    { id: 'source', name: 'Source Engine Game' },
    { id: 'squad', name: 'Squad' },
    { id: 'starbound', name: 'Starbound' },
    { id: 'starmade', name: 'StarMade' },
    { id: 'stationeers', name: 'Stationeers' },
    { id: 'steamquery', name: 'Generic Steam Query' },
    { id: 'subnautica', name: 'Subnautica' },
    { id: 'swat4', name: 'SWAT 4' },
    { id: 'teamfactor', name: 'Team Factor' },
    { id: 'teamfortress2', name: 'Team Fortress 2' },
    { id: 'teamspeak2', name: 'TeamSpeak 2' },
    { id: 'teamspeak3', name: 'TeamSpeak 3' },
    { id: 'terminus', name: 'Terminus' },
    { id: 'terraria', name: 'Terraria' },
    { id: 'tf2', name: 'Team Fortress 2' },
    { id: 'tfc', name: 'Team Fortress Classic' },
    { id: 'tibia', name: 'Tibia' },
    { id: 'tshock', name: 'Terraria (TShock)' },
    { id: 'unreal', name: 'Unreal' },
    { id: 'unreal2', name: 'Unreal 2' },
    { id: 'unreal3', name: 'Unreal 3' },
    { id: 'unrealtournament', name: 'Unreal Tournament' },
    { id: 'unrealtournament2003', name: 'Unreal Tournament 2003' },
    { id: 'unrealtournament2004', name: 'Unreal Tournament 2004' },
    { id: 'unrealtournament3', name: 'Unreal Tournament 3' },
    { id: 'unturned', name: 'Unturned' },
    { id: 'ut', name: 'Unreal Tournament' },
    { id: 'ut2003', name: 'Unreal Tournament 2003' },
    { id: 'ut2004', name: 'Unreal Tournament 2004' },
    { id: 'ut3', name: 'Unreal Tournament 3' },
    { id: 'vrising', name: 'V Rising' },
    { id: 'warsow', name: 'Warsow' },
    { id: 'won', name: 'World Opponent Network' },
    { id: 'wurm', name: 'Wurm Unlimited' },
    { id: 'zomboid', name: 'Project Zomboid' }
  ];

  return gameTypes.sort((a, b) => a.name.localeCompare(b.name));
}

// Get a display name for a game type
function getGameDisplayName(id: string): string {
  const displayNames: { [key: string]: string } = {
    'minecraft': 'Minecraft',
    'csgo': 'Counter-Strike: Global Offensive',
    'cs2': 'Counter-Strike 2',
    'valheim': 'Valheim',
    'rust': 'Rust',
    'arkse': 'ARK: Survival Evolved',
    'garrysmod': "Garry's Mod",
    'tf2': 'Team Fortress 2',
    'sdtd': '7 Days to Die',
    'fivem': 'FiveM (GTA V)',
    'arma3': 'Arma 3',
    'dayz': 'DayZ',
    'factorio': 'Factorio',
    'left4dead2': 'Left 4 Dead 2',
    'mordhau': 'Mordhau',
    'terraria': 'Terraria',
    'unturned': 'Unturned',
    'vrising': 'V Rising',
    'conanexiles': 'Conan Exiles',
    'satisfactory': 'Satisfactory'
  };

  return displayNames[id] || id;
}

// Get game-specific configuration
export function getGameConfig(gameType: string): GameConfig | null {
  // First try direct lookup
  if (gameConfigs[gameType]) {
    return gameConfigs[gameType];
  }

  // Try to get the proper game type ID through aliases
  const properGameType = getGameTypeId(gameType);
  if (properGameType && gameConfigs[properGameType]) {
    return gameConfigs[properGameType];
  }

  return null;
}

// Get default port for a game type
export function getDefaultPort(gameType: string): number | null {
  const config = getGameConfig(gameType);
  return config?.defaultPort || null;
}

// Validate game server configuration
export function validateGameConfig(gameType: string, config: any): { isValid: boolean; errors: string[] } {
  const gameConfig = getGameConfig(gameType);
  const errors: string[] = [];

  if (!gameConfig) {
    return { isValid: false, errors: ['Unsupported game type'] };
  }

  // Check required fields
  for (const field of gameConfig.fields) {
    if (field.required) {
      if (config[field.name] === undefined || config[field.name] === null) {
        errors.push(`Missing required field: ${field.name}`);
      }
    }
  }

  // Validate field types
  for (const field of gameConfig.fields) {
    if (config[field.name] !== undefined && config[field.name] !== null) {
      if (field.type === 'number' && typeof config[field.name] !== 'number') {
        errors.push(`${field.name} must be a number`);
      } else if (field.type === 'string' && typeof config[field.name] !== 'string') {
        errors.push(`${field.name} must be a string`);
      } else if (field.type === 'boolean' && typeof config[field.name] !== 'boolean') {
        errors.push(`${field.name} must be a boolean`);
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Search for games by name or ID
export function searchGames(query: string): { id: string; name: string }[] {
  const games = getSupportedGames();
  const searchTerm = query.toLowerCase();
  
  return games.filter(game => 
    game.id.toLowerCase().includes(searchTerm) || 
    game.name.toLowerCase().includes(searchTerm)
  );
}

// Validate if a game type is supported
export function isGameSupported(gameType: string): boolean {
  return getSupportedGames().some(game => game.id === gameType);
}

// Get the proper game type ID (handles common aliases)
export function getGameTypeId(gameType: string): string | null {
  // Common aliases for game types
  const aliases: { [key: string]: string } = {
    '7d2d': 'sdtd',
    '7days': 'sdtd',
    '7daystodie': 'sdtd',
    'minecraft': 'minecraft',
    'mc': 'minecraft',
    'mcbe': 'minecraftbe',
    'mcpe': 'minecraftpe',
    'csgo': 'csgo',
    'cs2': 'cs2',
    'counterstrike': 'cs2',
    'rust': 'rust',
    'gmod': 'garrysmod',
    'garrysmod': 'garrysmod',
    'tf2': 'tf2',
    'teamfortress2': 'tf2',
    'ark': 'arkse',
    'arkse': 'arkse',
    'valheim': 'valheim',
    'fivem': 'fivem',
    'gtav': 'fivem',
    'gtamp': 'gta5m',
    'l4d': 'l4d',
    'l4d2': 'l4d2',
    'left4dead': 'l4d',
    'left4dead2': 'l4d2',
    'projectzomboid': 'zomboid',
    'pz': 'zomboid',
    'palworld': 'palworld',
    'pal': 'palworld'
  };

  // First check if it's a direct match
  if (isGameSupported(gameType)) {
    return gameType;
  }

  // Then check aliases
  const normalizedType = gameType.toLowerCase().replace(/[^a-z0-9]/g, '');
  if (aliases[normalizedType]) {
    return aliases[normalizedType];
  }

  // If no match found
  return null;
}

// Query a game server
export async function queryGameServer(type: string, host: string, port: number, options: any = {}) {
  try {
    // Get game configuration
    const gameConfig = getGameConfig(type);
    if (!gameConfig) {
      throw new Error('Unsupported game type');
    }

    // Validate configuration
    const validation = validateGameConfig(type, { port, ...options });
    if (!validation.isValid) {
      throw new Error(`Invalid configuration: ${validation.errors.join(', ')}`);
    }

    // Build query options
    const queryOptions = {
      type,
      host,
      port,
      ...options
    };

    const result = await gamedig.query(queryOptions);
    return result;
  } catch (error) {
    console.error('Error querying game server:', error);
    throw error;
  }
} 