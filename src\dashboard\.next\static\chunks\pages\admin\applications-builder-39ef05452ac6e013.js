(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4779],{75625:(e,i,t)=>{"use strict";t.r(i),t.d(i,{default:()=>V});var s=t(94513),l=t(94285),r=t(95845),n=t(22907),a=t(51961),o=t(79156),c=t(78902),d=t(31678),p=t(41611),u=t(62690),h=t(91047),x=t(83881),j=t(47402),b=t(99820),g=t(72671),m=t(26977),y=t(49451),q=t(25680),f=t(68443),w=t(59818),v=t(71601),C=t(91169),k=t(87528),S=t(73011),T=t(18480),E=t(65104),A=t(40443),z=t(63730),D=t(55631),M=t(64057),F=t(9557),P=t(7680),J=t(52922),R=t(47847),_=t(49217),O=t(59365),N=t(85104),W=t(28245),I=t(7627),H=t(25964),L=t(71185),$=t(17842),G=t(60341),B=t(53424),K=t(58686);let Q=[{value:"text",label:"Short Text",icon:$.uO9},{value:"textarea",label:"Long Text",icon:$.kkc},{value:"select",label:"Dropdown",icon:$.Vr3},{value:"radio",label:"Multiple Choice",icon:$.aQJ},{value:"checkbox",label:"Checkboxes",icon:$.aQJ},{value:"number",label:"Number",icon:$.Ph},{value:"email",label:"Email",icon:$.uoG}],U=["blue","green","red","purple","orange","pink","teal","cyan","yellow"];function V(){let[e,i]=(0,l.useState)([]),[t,I]=(0,l.useState)(null),[H,L]=(0,l.useState)(!0),[Q,U]=(0,l.useState)(!1),[V,ee]=(0,l.useState)(0),{isOpen:ei,onOpen:et,onClose:es}=(0,r.j)(),{isOpen:el,onOpen:er,onClose:en}=(0,r.j)(),ea=(0,n.d)(),{data:eo}=(0,B.useSession)();(0,K.useRouter)(),(0,l.useEffect)(()=>{ec()},[]);let ec=async()=>{try{L(!0);let e=await fetch("/api/admin/applications-builder");if(e.ok){let t=await e.json();i(t.applications||[])}}catch(e){ea({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{L(!1)}},ed=e=>{I(e),et()},ep=async e=>{try{(await fetch("/api/admin/applications-builder/".concat(e),{method:"DELETE"})).ok&&(i(i=>i.filter(i=>i.id!==e)),ea({title:"Success",description:"Application deleted successfully",status:"success",duration:3e3}))}catch(e){ea({title:"Error",description:"Failed to delete application",status:"error",duration:3e3})}},eu=async e=>{U(!0);try{let i=await fetch("/api/admin/applications-builder",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(i.ok)await i.json(),await ec(),es(),ea({title:"Success",description:"Application saved successfully",status:"success",duration:3e3});else{let e=await i.json();throw Error(e.error||"Failed to save application")}}catch(e){ea({title:"Error",description:e instanceof Error?e.message:"Failed to save application",status:"error",duration:5e3})}finally{U(!1)}},eh=async(i,t)=>{try{let s=e.find(e=>e.id===i);if(!s)return;let l={...s,enabled:t},r=await fetch("/api/admin/applications-builder",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)});if(r.ok)await ec(),ea({title:"Success",description:"Application ".concat(t?"activated":"deactivated"," successfully"),status:"success",duration:3e3});else{let e=await r.json();throw Error(e.error||"Failed to update application status")}}catch(e){ea({title:"Error",description:e instanceof Error?e.message:"Failed to update application status",status:"error",duration:3e3})}};return(0,s.jsxs)(G.A,{children:[(0,s.jsx)(a.a,{p:8,children:(0,s.jsxs)(o.T,{align:"stretch",spacing:6,children:[(0,s.jsxs)(c.z,{justify:"space-between",children:[(0,s.jsxs)(o.T,{align:"start",spacing:1,children:[(0,s.jsx)(d.D,{size:"lg",children:"Applications Builder"}),(0,s.jsx)(p.E,{color:"gray.600",_dark:{color:"gray.300"},children:"Create and manage custom application forms for your server"})]}),(0,s.jsx)(u.$,{colorScheme:"blue",leftIcon:(0,s.jsx)($.OiG,{}),onClick:()=>{I({id:"app-".concat(Date.now()),title:"New Application",description:"Description for new application",color:"blue",icon:"FaClipboardList",enabled:!1,questions:[],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!1,notificationChannels:[]}}),et()},children:"Create Application"})]}),(0,s.jsxs)(h.t,{index:V,onChange:ee,variant:"enclosed",children:[(0,s.jsxs)(x.w,{children:[(0,s.jsx)(j.o,{children:"Applications"}),(0,s.jsx)(j.o,{children:"Templates"}),(0,s.jsx)(j.o,{children:"Settings"})]}),(0,s.jsxs)(b.T,{children:[(0,s.jsx)(g.K,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(m.F,{status:"info",children:[(0,s.jsx)(y._,{}),(0,s.jsx)(p.E,{children:"Build custom application forms with drag-and-drop ease. Create different types of applications for your server members."})]}),(0,s.jsx)(q.r,{columns:{base:1,md:2,lg:3},spacing:4,children:e.map(e=>(0,s.jsx)(f.Z,{borderTop:"4px solid",borderTopColor:"".concat(e.color,".500"),children:(0,s.jsx)(w.b,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(c.z,{justify:"space-between",children:[(0,s.jsxs)(o.T,{align:"start",spacing:1,children:[(0,s.jsx)(d.D,{size:"md",children:e.title}),(0,s.jsx)(p.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,s.jsx)(v.E,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Active":"Inactive"})]}),(0,s.jsxs)(p.E,{fontSize:"sm",color:"gray.500",children:[e.questions.length," questions configured"]}),(0,s.jsxs)(c.z,{spacing:2,children:[(0,s.jsx)(u.$,{size:"sm",colorScheme:"blue",leftIcon:(0,s.jsx)($.uO9,{}),onClick:()=>ed(e),children:"Edit"}),(0,s.jsx)(u.$,{size:"sm",variant:"outline",leftIcon:(0,s.jsx)($.Ny1,{}),onClick:()=>{I(e),er()},children:"Preview"}),(0,s.jsxs)(C.W,{children:[(0,s.jsx)(k.I,{as:S.K,icon:(0,s.jsx)($.Pcn,{}),size:"sm"}),(0,s.jsxs)(T.c,{children:[(0,s.jsx)(E.D,{icon:(0,s.jsx)($.Pcn,{}),onClick:()=>eh(e.id,!e.enabled),children:e.enabled?"Deactivate":"Activate"}),(0,s.jsx)(E.D,{icon:(0,s.jsx)($.qbC,{}),color:"red.500",onClick:()=>ep(e.id),children:"Delete"})]})]})]})]})})},e.id))})]})}),(0,s.jsx)(g.K,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(m.F,{status:"info",children:[(0,s.jsx)(y._,{}),(0,s.jsx)(p.E,{children:"Pre-built templates to get you started quickly. Choose from common application types."})]}),(0,s.jsx)(q.r,{columns:{base:1,md:2,lg:3},spacing:4,children:(0,s.jsx)(X,{onSelectTemplate:e=>{I({...e,id:"app-".concat(Date.now()),enabled:!1}),et()}})})]})}),(0,s.jsx)(g.K,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(m.F,{status:"info",children:[(0,s.jsx)(y._,{}),(0,s.jsx)(p.E,{children:"Global settings for all applications. Configure defaults and system-wide preferences."})]}),(0,s.jsx)(f.Z,{children:(0,s.jsx)(w.b,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsx)(d.D,{size:"md",children:"Global Settings"}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Default Auto-Response"}),(0,s.jsx)(D.d,{})]}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Require Email Verification"}),(0,s.jsx)(D.d,{})]}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Application Cooldown (days)"}),(0,s.jsx)(M.p,{type:"number",defaultValue:30})]})]})})})]})})]})]})]})}),(0,s.jsxs)(F.aF,{isOpen:ei,onClose:es,size:"6xl",scrollBehavior:"inside",children:[(0,s.jsx)(P.m,{}),(0,s.jsxs)(J.$,{children:[(0,s.jsx)(R.r,{children:(0,s.jsxs)(c.z,{children:[(0,s.jsx)(_.I,{as:$.lV_,color:"blue.500"}),(0,s.jsxs)(p.E,{children:[(null==t?void 0:t.id.startsWith("app-"))?"Create":"Edit"," Application"]})]})}),(0,s.jsx)(O.s,{}),(0,s.jsx)(N.c,{children:t&&(0,s.jsx)(Z,{application:t,onSave:eu,onCancel:es,isSaving:Q})})]})]}),(0,s.jsxs)(F.aF,{isOpen:el,onClose:en,size:"4xl",children:[(0,s.jsx)(P.m,{}),(0,s.jsxs)(J.$,{children:[(0,s.jsx)(R.r,{children:(0,s.jsxs)(c.z,{children:[(0,s.jsx)(_.I,{as:$.Ny1,color:"green.500"}),(0,s.jsxs)(p.E,{children:["Preview: ",null==t?void 0:t.title]})]})}),(0,s.jsx)(O.s,{}),(0,s.jsx)(N.c,{children:t&&(0,s.jsx)(Y,{application:t})}),(0,s.jsx)(W.j,{children:(0,s.jsx)(u.$,{onClick:en,children:"Close"})})]})]})]})}function Z(e){let{application:i,onSave:t,onCancel:r,isSaving:n=!1}=e,[d,m]=(0,l.useState)(i),[y,q]=(0,l.useState)(0),v=e=>{m(i=>({...i,...e}))},C=(e,i)=>{m(t=>({...t,questions:t.questions.map(t=>t.id===e?{...t,...i}:t)}))},k=e=>{m(i=>({...i,questions:i.questions.filter(i=>i.id!==e)}))};return(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(h.t,{index:y,onChange:q,children:[(0,s.jsxs)(x.w,{children:[(0,s.jsx)(j.o,{children:"Basic Info"}),(0,s.jsx)(j.o,{children:"Questions"}),(0,s.jsx)(j.o,{children:"Settings"})]}),(0,s.jsxs)(b.T,{children:[(0,s.jsx)(g.K,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Application Title"}),(0,s.jsx)(M.p,{value:d.title,onChange:e=>v({title:e.target.value}),placeholder:"Enter application title"})]}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Description"}),(0,s.jsx)(I.T,{value:d.description,onChange:e=>v({description:e.target.value}),placeholder:"Enter application description"})]}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Color Scheme"}),(0,s.jsx)(c.z,{spacing:2,children:U.map(e=>(0,s.jsx)(a.a,{w:8,h:8,bg:"".concat(e,".500"),rounded:"md",cursor:"pointer",border:d.color===e?"3px solid":"1px solid",borderColor:d.color===e?"white":"gray.300",onClick:()=>v({color:e})},e))})]}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Status"}),(0,s.jsxs)(c.z,{children:[(0,s.jsx)(D.d,{isChecked:d.enabled,onChange:e=>v({enabled:e.target.checked})}),(0,s.jsx)(p.E,{children:d.enabled?"Active":"Inactive"})]})]})]})}),(0,s.jsx)(g.K,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(c.z,{justify:"space-between",children:[(0,s.jsxs)(p.E,{fontWeight:"bold",children:["Questions (",d.questions.length,")"]}),(0,s.jsx)(u.$,{size:"sm",colorScheme:"blue",leftIcon:(0,s.jsx)($.OiG,{}),onClick:()=>{let e={id:"q-".concat(Date.now()),type:"text",label:"New Question",required:!1};m(i=>({...i,questions:[...i.questions,e]}))},children:"Add Question"})]}),d.questions.map(e=>(0,s.jsx)(f.Z,{borderLeft:"4px solid",borderLeftColor:"blue.500",children:(0,s.jsx)(w.b,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(c.z,{justify:"space-between",children:[(0,s.jsxs)(A.MJ,{flex:1,children:[(0,s.jsx)(z.l,{children:"Question Label"}),(0,s.jsx)(M.p,{value:e.label,onChange:i=>C(e.id,{label:i.target.value}),placeholder:"Enter question label"})]}),(0,s.jsxs)(A.MJ,{w:"200px",children:[(0,s.jsx)(z.l,{children:"Type"}),(0,s.jsx)(H.l,{value:e.type,onChange:i=>C(e.id,{type:i.target.value}),children:Q.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsx)(S.K,{"aria-label":"Delete question",icon:(0,s.jsx)($.qbC,{}),colorScheme:"red",size:"sm",onClick:()=>k(e.id)})]}),(0,s.jsxs)(A.MJ,{children:[(0,s.jsx)(z.l,{children:"Placeholder"}),(0,s.jsx)(M.p,{value:e.placeholder||"",onChange:i=>C(e.id,{placeholder:i.target.value}),placeholder:"Enter placeholder text"})]}),(0,s.jsx)(c.z,{children:(0,s.jsx)(A.MJ,{children:(0,s.jsxs)(c.z,{children:[(0,s.jsx)(D.d,{isChecked:e.required,onChange:i=>C(e.id,{required:i.target.checked})}),(0,s.jsx)(p.E,{children:"Required"})]})})})]})})},e.id))]})}),(0,s.jsx)(g.K,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsx)(A.MJ,{children:(0,s.jsxs)(c.z,{children:[(0,s.jsx)(D.d,{isChecked:d.settings.allowMultipleSubmissions,onChange:e=>v({settings:{...d.settings,allowMultipleSubmissions:e.target.checked}})}),(0,s.jsx)(p.E,{children:"Allow Multiple Submissions"})]})}),(0,s.jsx)(A.MJ,{children:(0,s.jsxs)(c.z,{children:[(0,s.jsx)(D.d,{isChecked:d.settings.requireApproval,onChange:e=>v({settings:{...d.settings,requireApproval:e.target.checked}})}),(0,s.jsx)(p.E,{children:"Require Manual Approval"})]})}),(0,s.jsx)(A.MJ,{children:(0,s.jsxs)(c.z,{children:[(0,s.jsx)(D.d,{isChecked:d.settings.autoResponse,onChange:e=>v({settings:{...d.settings,autoResponse:e.target.checked}})}),(0,s.jsx)(p.E,{children:"Send Auto-Response"})]})})]})})]})]}),(0,s.jsxs)(c.z,{justify:"flex-end",spacing:4,children:[(0,s.jsx)(u.$,{onClick:r,isDisabled:n,children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:"blue",onClick:()=>t(d),isLoading:n,loadingText:"Saving...",children:"Save Application"})]})]})}function X(e){let{onSelectTemplate:i}=e;return(0,s.jsx)(s.Fragment,{children:[{id:"template-moderator",title:"Moderator Application",description:"Standard moderator application with experience and scenario questions",color:"blue",icon:"FaUserShield",enabled:!1,questions:[{id:"q1",type:"text",label:"What is your Discord username?",required:!0},{id:"q2",type:"number",label:"How old are you?",required:!0},{id:"q3",type:"text",label:"What timezone are you in?",required:!0},{id:"q4",type:"number",label:"How many hours per week can you dedicate to moderation?",required:!0},{id:"q5",type:"textarea",label:"Why do you want to be a moderator?",required:!0},{id:"q6",type:"textarea",label:"Do you have any previous moderation experience?",required:!1},{id:"q7",type:"textarea",label:"How would you handle a heated argument between two members?",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-developer",title:"Developer Application",description:"Technical application for developer positions",color:"green",icon:"FaCode",enabled:!1,questions:[{id:"q1",type:"text",label:"Full Name",required:!0},{id:"q2",type:"email",label:"Email Address",required:!0},{id:"q3",type:"textarea",label:"Tell us about your programming experience",required:!0},{id:"q4",type:"select",label:"Primary Programming Language",required:!0,options:["JavaScript","Python","Java","C#","Go","Other"]},{id:"q5",type:"textarea",label:"Describe a challenging project you worked on",required:!0},{id:"q6",type:"text",label:"GitHub/Portfolio URL",required:!1},{id:"q7",type:"radio",label:"Are you available for full-time work?",required:!0,options:["Yes","No","Part-time only"]}],settings:{allowMultipleSubmissions:!0,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-event-host",title:"Event Host Application",description:"Application for community event organizers",color:"purple",icon:"FaCalendar",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"text",label:"Preferred Name",required:!0},{id:"q3",type:"textarea",label:"What types of events would you like to host?",required:!0},{id:"q4",type:"textarea",label:"Do you have experience organizing events?",required:!1},{id:"q5",type:"select",label:"How often would you like to host events?",required:!0,options:["Weekly","Bi-weekly","Monthly","As needed"]},{id:"q6",type:"textarea",label:"Describe an event idea you have",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-support",title:"Support Team Application",description:"Customer support and help desk application",color:"orange",icon:"FaHeadset",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"text",label:"Age",required:!0},{id:"q3",type:"text",label:"Timezone",required:!0},{id:"q4",type:"textarea",label:"Why do you want to join the support team?",required:!0},{id:"q5",type:"checkbox",label:"Which areas can you help with?",required:!0,options:["Technical Issues","Account Problems","General Questions","Bug Reports","Feature Requests"]},{id:"q6",type:"textarea",label:"How would you help a frustrated user?",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-content",title:"Content Creator Application",description:"Application for content creators and influencers",color:"pink",icon:"FaVideo",enabled:!1,questions:[{id:"q1",type:"text",label:"Creator Name/Handle",required:!0},{id:"q2",type:"email",label:"Contact Email",required:!0},{id:"q3",type:"select",label:"Primary Content Platform",required:!0,options:["YouTube","Twitch","TikTok","Instagram","Twitter","Other"]},{id:"q4",type:"text",label:"Channel/Profile URL",required:!0},{id:"q5",type:"textarea",label:"What type of content do you create?",required:!0},{id:"q6",type:"number",label:"How many followers/subscribers do you have?",required:!1},{id:"q7",type:"textarea",label:"How would you promote our community?",required:!0}],settings:{allowMultipleSubmissions:!0,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-beta",title:"Beta Tester Application",description:"Application for beta testing programs",color:"teal",icon:"FaFlask",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"textarea",label:"What interests you about beta testing?",required:!0},{id:"q3",type:"textarea",label:"Do you have experience finding and reporting bugs?",required:!1},{id:"q4",type:"select",label:"How much time can you dedicate to testing?",required:!0,options:["1-2 hours/week","3-5 hours/week","6-10 hours/week","10+ hours/week"]},{id:"q5",type:"checkbox",label:"Which platforms do you have access to?",required:!0,options:["Windows","Mac","Linux","iOS","Android","Web Browser"]}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}}].map(e=>(0,s.jsx)(f.Z,{borderTop:"4px solid",borderTopColor:"".concat(e.color,".500"),children:(0,s.jsx)(w.b,{children:(0,s.jsxs)(o.T,{align:"stretch",spacing:3,children:[(0,s.jsxs)(o.T,{align:"start",spacing:1,children:[(0,s.jsx)(d.D,{size:"md",children:e.title}),(0,s.jsx)(p.E,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,s.jsxs)(p.E,{fontSize:"sm",color:"gray.500",children:[e.questions.length," pre-configured questions"]}),(0,s.jsx)(u.$,{size:"sm",colorScheme:e.color,onClick:()=>i(e),leftIcon:(0,s.jsx)($.uoG,{}),children:"Use Template"})]})})},e.id))})}function Y(e){let{application:i}=e;return(0,s.jsxs)(o.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(o.T,{align:"center",spacing:2,children:[(0,s.jsx)(d.D,{size:"lg",children:i.title}),(0,s.jsx)(p.E,{color:"gray.600",_dark:{color:"gray.400"},children:i.description})]}),(0,s.jsx)(L.c,{}),(0,s.jsx)(o.T,{align:"stretch",spacing:4,children:i.questions.map(e=>{var i;return(0,s.jsxs)(A.MJ,{isRequired:e.required,children:[(0,s.jsx)(z.l,{children:e.label}),"text"===e.type&&(0,s.jsx)(M.p,{placeholder:e.placeholder}),"textarea"===e.type&&(0,s.jsx)(I.T,{placeholder:e.placeholder}),"select"===e.type&&(0,s.jsx)(H.l,{placeholder:e.placeholder,children:null==(i=e.options)?void 0:i.map(e=>(0,s.jsx)("option",{value:e,children:e},e))}),"number"===e.type&&(0,s.jsx)(M.p,{type:"number",placeholder:e.placeholder}),"email"===e.type&&(0,s.jsx)(M.p,{type:"email",placeholder:e.placeholder})]},e.id)})}),(0,s.jsx)(u.$,{colorScheme:i.color,size:"lg",isDisabled:!0,children:"Submit Application (Preview)"})]})}},91295:(e,i,t)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/applications-builder",function(){return t(75625)}])}},e=>{var i=i=>e(e.s=i);e.O(0,[4108,3256,9998,4976,217,2965,3177,7147,341,636,6593,8792],()=>i(91295)),_N_E=e.O()}]);