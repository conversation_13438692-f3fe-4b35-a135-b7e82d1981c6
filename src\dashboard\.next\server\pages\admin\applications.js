"use strict";(()=>{var e={};e.id=7453,e.ids=[7453],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2115:e=>{e.exports=require("yaml")},2326:e=>{e.exports=require("react-dom")},3762:(e,t,s)=>{s.d(t,{N:()=>h});var i=s(5542),r=s.n(i);let a=require("next-auth/providers/discord");var n=s.n(a),l=s(9021),o=s(2115),c=s.n(o),d=s(3873);let p={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d.resolve(process.cwd(),e)).find(e=>l.existsSync(e));if(!e){let t=d.resolve(__dirname,"../../../config.yml");l.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=l.readFileSync(e,"utf8");p=c().parse(t)}catch(e){process.exit(1)}let u={bot:{token:p.bot.token,clientId:p.bot.clientId,clientSecret:p.bot.clientSecret,guildId:p.bot.guildId,ticketCategoryId:p.bot.ticketCategoryId||null,ticketLogChannelId:p.bot.ticketLogChannelId||null,prefix:p.bot.prefix},dashboard:{admins:p.dashboard?.admins||[],adminRoleIds:p.dashboard?.adminRoleIds||[],session:{secret:p.dashboard?.session?.secret||p.bot.clientSecret}},database:{url:p.database.url,name:p.database.name,options:{maxPoolSize:p.database.options?.maxPoolSize||10,minPoolSize:p.database.options?.minPoolSize||1,maxIdleTimeMS:p.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:p.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:p.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:p.database.options?.connectTimeoutMS||1e4,retryWrites:p.database.options?.retryWrites!==!1,retryReads:p.database.options?.retryReads!==!1}}};u.bot.token||process.exit(1),u.bot.clientId&&u.bot.clientSecret||process.exit(1),u.bot.guildId||process.exit(1),u.database.url&&u.database.name||process.exit(1);let h={providers:[n()({clientId:u.bot.clientId,clientSecret:u.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:s})=>(t&&s&&(e.accessToken=t.access_token||null,e.id=s.id||null),e),async session({session:e,token:t}){if(e?.user){let s=t.id||null,i=t.accessToken||null;e.user.id=s,e.user.accessToken=i;let r=!1;if(s)if((u.dashboard.admins||[]).includes(s))r=!0;else{let e=u.dashboard.adminRoleIds||[];if(e.length&&u.bot.token&&u.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${u.bot.guildId}/members/${s}`,{headers:{Authorization:`Bot ${u.bot.token}`}});if(t.ok){let s=await t.json();r=e.some(e=>s.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let s=new URL(t),i=`${s.protocol}//localhost${s.port?`:${s.port}`:""}`;return e.startsWith(t)||e.startsWith(i)?e:t}},secret:u.dashboard.session.secret||u.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};r()(h)},3814:(e,t,s)=>{s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{default:()=>u,getServerSideProps:()=>h});var r=s(8732),a=s(9733),n=s(1011),l=s(2015),o=s(5806),c=s(3762),d=s(2695),p=e([a,n]);function u(){let[e,t]=(0,l.useState)([]),[s,i]=(0,l.useState)(!0),[o,c]=(0,l.useState)({total:0,pending:0,approved:0,rejected:0,recentIncrease:0}),p=(0,a.useToast)(),{isOpen:u,onOpen:h,onClose:x}=(0,a.useDisclosure)(),[j,m]=(0,l.useState)(null),b=async()=>{try{let e=await fetch("/api/admin/applications");if(e.ok){let s=await e.json();t(s.applications||[]),c(s.stats||o)}}catch(e){p({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{i(!1)}},S=async(e,t)=>{try{(await fetch("/api/admin/applications",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({applicationSubmissionId:e,action:t})})).ok&&(b(),p({title:"Success",description:`Application ${t}d successfully`,status:"success",duration:3e3}),x())}catch(e){p({title:"Error",description:`Failed to ${t} application`,status:"error",duration:3e3})}},g=e=>{m(e),h()};return s?(0,r.jsx)(n.A,{children:(0,r.jsx)(a.Box,{p:8,display:"flex",justifyContent:"center",alignItems:"center",minH:"400px",children:(0,r.jsx)(a.Spinner,{size:"xl"})})}):(0,r.jsxs)(n.A,{children:[(0,r.jsx)(a.Box,{p:8,children:(0,r.jsxs)(a.VStack,{align:"stretch",spacing:6,children:[(0,r.jsxs)(a.HStack,{children:[(0,r.jsx)(a.Icon,{as:d.t69,boxSize:6,color:"blue.500"}),(0,r.jsx)(a.Heading,{size:"lg",children:"Applications Management"})]}),(0,r.jsx)(a.Text,{color:"gray.600",_dark:{color:"gray.300"},children:"Manage and review all user applications submitted through the application builder."}),(0,r.jsxs)(a.SimpleGrid,{columns:{base:1,md:4},spacing:6,children:[(0,r.jsx)(a.Card,{children:(0,r.jsx)(a.CardBody,{children:(0,r.jsxs)(a.Stat,{children:[(0,r.jsx)(a.StatLabel,{children:"Total Applications"}),(0,r.jsx)(a.StatNumber,{children:o.total}),(0,r.jsxs)(a.StatHelpText,{children:[(0,r.jsx)(a.StatArrow,{type:o.recentIncrease>=0?"increase":"decrease"}),Math.abs(o.recentIncrease),"% this month"]})]})})}),(0,r.jsx)(a.Card,{children:(0,r.jsx)(a.CardBody,{children:(0,r.jsxs)(a.Stat,{children:[(0,r.jsx)(a.StatLabel,{children:"Pending Review"}),(0,r.jsx)(a.StatNumber,{color:"yellow.500",children:o.pending}),(0,r.jsx)(a.StatHelpText,{children:"Requires attention"})]})})}),(0,r.jsx)(a.Card,{children:(0,r.jsx)(a.CardBody,{children:(0,r.jsxs)(a.Stat,{children:[(0,r.jsx)(a.StatLabel,{children:"Approved"}),(0,r.jsx)(a.StatNumber,{color:"green.500",children:o.approved}),(0,r.jsx)(a.StatHelpText,{children:"Accepted applications"})]})})}),(0,r.jsx)(a.Card,{children:(0,r.jsx)(a.CardBody,{children:(0,r.jsxs)(a.Stat,{children:[(0,r.jsx)(a.StatLabel,{children:"Rejected"}),(0,r.jsx)(a.StatNumber,{color:"red.500",children:o.rejected}),(0,r.jsx)(a.StatHelpText,{children:"Declined applications"})]})})})]}),(0,r.jsx)(a.Box,{mt:8,children:0===e.length?(0,r.jsxs)(a.Alert,{status:"info",children:[(0,r.jsx)(a.AlertIcon,{}),(0,r.jsx)(a.AlertDescription,{children:"No applications found. Applications submitted through the builder will appear here."})]}):(0,r.jsxs)(a.Table,{variant:"simple",children:[(0,r.jsx)(a.Thead,{children:(0,r.jsxs)(a.Tr,{children:[(0,r.jsx)(a.Th,{children:"User"}),(0,r.jsx)(a.Th,{children:"Application Type"}),(0,r.jsx)(a.Th,{children:"Submitted On"}),(0,r.jsx)(a.Th,{children:"Status"}),(0,r.jsx)(a.Th,{children:"Actions"})]})}),(0,r.jsx)(a.Tbody,{children:e.map(e=>(0,r.jsxs)(a.Tr,{children:[(0,r.jsx)(a.Td,{children:(0,r.jsxs)(a.HStack,{children:[(0,r.jsx)(a.Avatar,{size:"sm",name:e.submittedBy}),(0,r.jsx)(a.Text,{children:e.submittedBy})]})}),(0,r.jsx)(a.Td,{children:(0,r.jsxs)(a.Badge,{colorScheme:"purple",children:["Custom Application: ",e.applicationId]})}),(0,r.jsx)(a.Td,{children:new Date(e.submittedAt).toLocaleDateString()}),(0,r.jsx)(a.Td,{children:(0,r.jsx)(a.Badge,{colorScheme:"approved"===e.status?"green":"rejected"===e.status?"red":"yellow",children:e.status})}),(0,r.jsx)(a.Td,{children:(0,r.jsxs)(a.HStack,{spacing:2,children:[(0,r.jsx)(a.Button,{size:"sm",colorScheme:"blue",onClick:()=>g(e),children:"View"}),"pending"===e.status&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(a.Button,{size:"sm",colorScheme:"green",onClick:()=>S(e._id,"approve"),children:"Accept"}),(0,r.jsx)(a.Button,{size:"sm",colorScheme:"red",onClick:()=>S(e._id,"reject"),children:"Reject"})]})]})})]},e._id))})]})})]})}),(0,r.jsxs)(a.Modal,{isOpen:u,onClose:x,size:"xl",children:[(0,r.jsx)(a.ModalOverlay,{}),(0,r.jsxs)(a.ModalContent,{children:[(0,r.jsx)(a.ModalHeader,{children:"Application Details"}),(0,r.jsx)(a.ModalCloseButton,{}),(0,r.jsx)(a.ModalBody,{children:j&&(0,r.jsxs)(a.VStack,{align:"stretch",spacing:4,children:[(0,r.jsxs)(a.HStack,{justifyContent:"space-between",children:[(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Submitted By:"}),(0,r.jsx)(a.Text,{children:j.submittedBy})]}),(0,r.jsxs)(a.HStack,{justifyContent:"space-between",children:[(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Submitted On:"}),(0,r.jsx)(a.Text,{children:new Date(j.submittedAt).toLocaleDateString()})]}),(0,r.jsxs)(a.HStack,{justifyContent:"space-between",children:[(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Application Type:"}),(0,r.jsxs)(a.Text,{children:["Custom Application: ",j.applicationId]})]}),(0,r.jsxs)(a.HStack,{justifyContent:"space-between",children:[(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Status:"}),(0,r.jsx)(a.Badge,{colorScheme:"approved"===j.status?"green":"rejected"===j.status?"red":"yellow",children:j.status})]}),(0,r.jsx)(a.Divider,{}),(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Answers:"}),(0,r.jsx)(a.List,{spacing:2,children:Object.entries(j.answers).map(([e,t])=>(0,r.jsx)(a.ListItem,{children:(0,r.jsxs)(a.Text,{children:[(0,r.jsxs)("strong",{children:[e,":"]})," ",Array.isArray(t)?t.join(", "):t]})},e))}),j.reviewedBy&&(0,r.jsxs)(a.HStack,{justifyContent:"space-between",children:[(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Reviewed By:"}),(0,r.jsx)(a.Text,{children:j.reviewedBy})]}),j.reviewedAt&&(0,r.jsxs)(a.HStack,{justifyContent:"space-between",children:[(0,r.jsx)(a.Text,{fontWeight:"bold",children:"Reviewed At:"}),(0,r.jsx)(a.Text,{children:new Date(j.reviewedAt).toLocaleDateString()})]})]})})]})]})]})}[a,n]=p.then?(await p)():p;let h=async e=>{let t=await (0,o.getServerSession)(e.req,e.res,c.N);return t?{props:{session:t}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fadmin%2Fapplications",permanent:!1}}};i()}catch(e){i(e)}})},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},4934:(e,t,s)=>{s.a(e,async(e,i)=>{try{s.r(t),s.d(t,{config:()=>j,default:()=>p,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>u,reportWebVitals:()=>m,routeModule:()=>T,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>g,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>b});var r=s(1292),a=s(8834),n=s(786),l=s(3567),o=s(8077),c=s(3814),d=e([o,c]);[o,c]=d.then?(await d)():d;let p=(0,n.M)(c,"default"),u=(0,n.M)(c,"getStaticProps"),h=(0,n.M)(c,"getStaticPaths"),x=(0,n.M)(c,"getServerSideProps"),j=(0,n.M)(c,"config"),m=(0,n.M)(c,"reportWebVitals"),b=(0,n.M)(c,"unstable_getStaticProps"),S=(0,n.M)(c,"unstable_getStaticPaths"),g=(0,n.M)(c,"unstable_getStaticParams"),y=(0,n.M)(c,"unstable_getServerProps"),f=(0,n.M)(c,"unstable_getServerSideProps"),T=new r.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/admin/applications",pathname:"/admin/applications",bundlePath:"",filename:""},components:{App:o.default,Document:l.default},userland:c});i()}catch(e){i(e)}})},5542:e=>{e.exports=require("next-auth")},5806:e=>{e.exports=require("next-auth/next")},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[8270,4874,752,6281,2695,5333],()=>s(4934));module.exports=i})();