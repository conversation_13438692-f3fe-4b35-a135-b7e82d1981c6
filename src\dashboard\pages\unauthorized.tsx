// @ts-nocheck
import { Box, Heading, Text, Button } from '@chakra-ui/react';
import Layout from '../components/Layout';
import { signIn } from 'next-auth/react';

export default function Unauthorized() {
  return (
    <Layout>
      <Box textAlign="center" mt={20}>
        <Heading size="xl" mb={4}>Access Denied</Heading>
        <Text mb={6}>You do not have permission to view this page.</Text>
        <Button colorScheme="brand" onClick={() => signIn('discord', { callbackUrl: '/' })}>
          Sign in with a different account
        </Button>
      </Box>
    </Layout>
  );
} 