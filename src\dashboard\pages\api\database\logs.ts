import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const client = await connectToDatabase();
    const db = client.db(dbName);
    
    // Get logs from our custom logs collection
    // If it doesn't exist, return empty array
    const logsCollection = db.collection('database_logs');
    const logs = await logsCollection.find({})
      .sort({ timestamp: -1 })
      .limit(100)
      .toArray();

    // Return logs as is since they're already in our format
    return res.status(200).json({ logs });
  } catch (error) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 