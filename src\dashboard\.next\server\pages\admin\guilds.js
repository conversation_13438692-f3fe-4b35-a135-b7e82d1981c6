(()=>{var e={};e.id=9640,e.ids=[9640],e.modules={361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{"use strict";e.exports=require("react")},2115:e=>{"use strict";e.exports=require("yaml")},2326:e=>{"use strict";e.exports=require("react-dom")},2649:(e,s,t)=>{"use strict";t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{default:()=>S,getServerSideProps:()=>A});var n=t(8732),r=t(9733),l=t(1011),o=t(4715),a=t(2015),c=t(5806),d=t(3762),h=t(8079),u=t(2695),x=t(8364),m=t.n(x),p=t(3001),g=e([r,l,o,p]);[r,l,o,p]=g.then?(await g)():g;let f=m()(()=>t.e(856).then(t.bind(t,856)),{loadableGenerated:{modules:["pages\\admin\\guilds.tsx -> ../../components/CreateChannelDialog"]},loading:()=>(0,n.jsx)(r.Spinner,{size:"md"}),ssr:!1}),y=m()(()=>t.e(9820).then(t.bind(t,9820)),{loadableGenerated:{modules:["pages\\admin\\guilds.tsx -> ../../components/EditChannelDialog"]},loading:()=>(0,n.jsx)(r.Spinner,{size:"md"}),ssr:!1}),C=m()(()=>t.e(1165).then(t.bind(t,1165)),{loadableGenerated:{modules:["pages\\admin\\guilds.tsx -> ../../components/EditRoleDialog"]},loading:()=>(0,n.jsx)(r.Spinner,{size:"md"}),ssr:!1}),k=m()(()=>t.e(5039).then(t.bind(t,5039)),{loadableGenerated:{modules:["pages\\admin\\guilds.tsx -> ../../components/ColorBuilder"]},loading:()=>(0,n.jsx)(r.Spinner,{size:"md"}),ssr:!1}),v=m()(()=>t.e(5129).then(t.bind(t,5129)),{loadableGenerated:{modules:["pages\\admin\\guilds.tsx -> ../../components/CreateRoleDialog"]},loading:()=>(0,n.jsx)(r.Spinner,{size:"md"}),ssr:!1}),T=m()(()=>t.e(2234).then(t.bind(t,2234)),{loadableGenerated:{modules:["pages\\admin\\guilds.tsx -> ../../components/WelcomeSystemDialog"]},loading:()=>(0,n.jsx)(r.Spinner,{size:"md"}),ssr:!1});function j(e=2e3){let[s,t]=(0,a.useState)(!1),i=(0,a.useRef)(null),n=(0,a.useCallback)(()=>{i.current&&clearTimeout(i.current),t(!0),i.current=setTimeout(()=>{t(!1)},e)},[e]);return[s,n]}let I={0:{icon:h.mEP,color:"blue",label:"Text"},2:{icon:h.o77,color:"green",label:"Voice"},4:{icon:h.GsE,color:"purple",label:"Category"},5:{icon:h.DQs,color:"orange",label:"Announcement"},11:{icon:h.X6_,color:"cyan",label:"Public Thread"},12:{icon:h.F5$,color:"pink",label:"Private Thread"},13:{icon:h.Qz2,color:"teal",label:"Stage Voice"},15:{icon:h.Qz2,color:"gray",label:"Forum"}},B={ADMINISTRATOR:{color:"red",label:"Admin"},MANAGE_GUILD:{color:"orange",label:"Manage Server"},MANAGE_ROLES:{color:"yellow",label:"Manage Roles"},MANAGE_CHANNELS:{color:"green",label:"Manage Channels"},KICK_MEMBERS:{color:"purple",label:"Kick"},BAN_MEMBERS:{color:"pink",label:"Ban"},MANAGE_MESSAGES:{color:"blue",label:"Manage Messages"},MENTION_EVERYONE:{color:"cyan",label:"Mention @everyone"}},w={ADMINISTRATOR:1n<<3n,MANAGE_GUILD:1n<<5n,MANAGE_ROLES:1n<<28n,MANAGE_CHANNELS:1n<<4n,KICK_MEMBERS:1n<<1n,BAN_MEMBERS:1n<<2n,MANAGE_MESSAGES:1n<<13n,MENTION_EVERYONE:1n<<17n};function b(e){if(!e)return[];if(Array.isArray(e))return e;try{let s=[],t=BigInt(e);for(let[e,i]of Object.entries(w))(t&i)===i&&s.push(e);return s}catch(e){return[]}}function S(){let e=(0,r.useToast)(),{displayName:s}=(0,o.A)(),[t,i]=j(),[c,d]=j(5e3),{currentScheme:x,setColorScheme:m,customSchemes:g,deleteCustomScheme:S}=(0,p.DP)(),[w,A]=(0,a.useState)({prefix:"!",botName:"Bot",guildName:"",guildId:"",guildIcon:null,activities:[{type:"PLAYING",name:"with Discord.js"}],activityRotationInterval:60}),[z,E]=(0,a.useState)([]),[M,P]=(0,a.useState)([]),[N,R]=(0,a.useState)(!0),[_,D]=(0,a.useState)(!0),[H,O]=(0,a.useState)(!1),[G,F]=(0,a.useState)(!1),{isOpen:V,onOpen:L,onClose:W}=(0,r.useDisclosure)(),{isOpen:q,onOpen:$,onClose:X}=(0,r.useDisclosure)(),{isOpen:Y,onOpen:K,onClose:U}=(0,r.useDisclosure)(),{isOpen:J,onOpen:Q,onClose:Z}=(0,r.useDisclosure)(),{isOpen:ee,onOpen:es,onClose:et}=(0,r.useDisclosure)(),{isOpen:ei,onOpen:en,onClose:er}=(0,r.useDisclosure)(),[el,eo]=(0,a.useState)(null),[ea,ec]=(0,a.useState)(null),[ed,eh]=(0,a.useState)([]),[eu,ex]=(0,a.useState)([]),[em,ep]=(0,a.useState)(!1),[eg,ej]=(0,a.useState)(null),[eb,eS]=(0,a.useState)(null);(0,a.useRef)(null);let ef=async()=>{try{let[e,s,t]=await Promise.all([fetch("/api/discord/guild"),fetch("/api/discord/roles"),fetch("/api/discord/presence")]);if(e.ok){let s=await e.json();A(e=>({...e,guildName:s.name,guildId:s.id,guildIcon:s.icon,botName:s.botName||e.botName}))}if(s.ok){let e=await s.json(),t=Array.isArray(e)?e:e.roles||[];E(t.sort((e,s)=>s.position-e.position))}if(t.ok){let e=await t.json();A(s=>({...s,activities:e.activities||[],activityRotationInterval:e.activityRotationInterval||60}))}}catch(s){e({title:"Error",description:"Failed to fetch guild data",status:"error",duration:3e3})}finally{R(!1)}},ey=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let s=(await e.json()||[]).sort((e,s)=>4===e.type&&4!==s.type?-1:4!==e.type&&4===s.type?1:e.position-s.position);P(s)}catch(s){e({title:"Error",description:"Failed to fetch channels",status:"error",duration:5e3})}finally{D(!1)}},eC=(e,s)=>{A(t=>({...t,[e]:s}))},ek=async()=>{if(!H&&!t){O(!0),i();try{if((await fetch("/api/discord/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(w)})).ok)e({title:"Success",description:"Settings saved successfully",status:"success",duration:3e3});else throw Error("Failed to save settings")}catch(s){e({title:"Error",description:"Failed to save settings",status:"error",duration:3e3})}finally{O(!1)}}},ev=e=>{eo(e),Q()},eT=e=>{ec(e),$()},eI=async s=>{if(!t)try{i(),(await fetch(`/api/discord/channels/${s}`,{method:"DELETE"})).ok&&(await ey(),e({title:"Success",description:"Channel deleted successfully",status:"success",duration:3e3}))}catch(s){e({title:"Error",description:"Failed to delete channel",status:"error",duration:3e3})}},eB=e=>{if(!e||!M)return"-";let s=M.find(s=>s.id===e);return s?s.name:"-"},ew=async()=>{if(0!==ed.length&&!em&&!c){ep(!0),d();try{let s=await fetch("/api/discord/roles/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({roleIds:ed})}),t=await s.json();if(s.ok)e({title:"Success",description:t.message,status:"success",duration:5e3}),eh([]),ef();else throw Error(t.error||"Failed to delete roles")}catch(s){e({title:"Error",description:s.message||"Failed to delete roles",status:"error",duration:5e3})}finally{ep(!1)}}},eA=async()=>{if(0!==eu.length&&!em&&!c){ep(!0),d();try{let s=await fetch("/api/discord/channels/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({channelIds:eu})}),t=await s.json();if(s.ok)e({title:"Success",description:t.message,status:"success",duration:5e3}),ex([]),ey();else throw Error(t.error||"Failed to delete channels")}catch(s){e({title:"Error",description:s.message||"Failed to delete channels",status:"error",duration:5e3})}finally{ep(!1)}}},ez=e=>{eh(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},eE=e=>{ex(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},eM=(e,s,t)=>{let i=[...w.activities];i[e]={...i[e],[s]:t},A(e=>({...e,activities:i}))},eP=e=>{let s=w.activities.filter((s,t)=>t!==e);A(e=>({...e,activities:s}))},eN=async()=>{if(!G&&!t){F(!0),i();try{let s=await fetch("/api/discord/presence",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({activities:w.activities,activityRotationInterval:w.activityRotationInterval})});if(s.ok)e({title:"Success",description:"Presence settings saved successfully",status:"success",duration:3e3});else{let e=await s.json();throw Error(e.error||"Failed to save presence settings")}}catch(s){e({title:"Error",description:s.message,status:"error",duration:5e3})}finally{F(!1)}}};return N?(0,n.jsx)(l.A,{children:(0,n.jsx)(r.Container,{maxW:"container.xl",py:8,children:(0,n.jsxs)(r.VStack,{spacing:6,children:[(0,n.jsx)(r.Skeleton,{height:"60px"}),(0,n.jsx)(r.Skeleton,{height:"400px"})]})})}):(0,n.jsx)(l.A,{children:(0,n.jsxs)(r.Container,{maxW:"container.xl",py:8,children:[(0,n.jsxs)(r.VStack,{spacing:8,align:"stretch",children:[(0,n.jsx)(r.Box,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",children:(0,n.jsxs)(r.HStack,{justify:"space-between",align:"center",children:[(0,n.jsxs)(r.Box,{children:[(0,n.jsxs)(r.Heading,{size:"xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:[(0,n.jsx)(r.Icon,{as:h.LIi,mr:3}),"Server Management"]}),(0,n.jsxs)(r.Text,{color:"gray.300",mt:2,children:["Comprehensive management for ",s||w.guildName]})]}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.Bc_,{}),colorScheme:"blue",onClick:ek,isLoading:H,isDisabled:t,size:"lg",children:"Save Settings"})]})}),(0,n.jsxs)(r.Tabs,{colorScheme:"blue",isLazy:!0,children:[(0,n.jsxs)(r.TabList,{children:[(0,n.jsxs)(r.Tab,{children:[(0,n.jsx)(r.Icon,{as:h.VSk,mr:2}),"General Settings"]}),(0,n.jsxs)(r.Tab,{children:[(0,n.jsx)(r.Icon,{as:u.lV_,mr:2}),"Theme Builder"]}),(0,n.jsxs)(r.Tab,{children:[(0,n.jsx)(r.Icon,{as:h.i5c,mr:2}),"Builders"]}),(0,n.jsxs)(r.Tab,{children:[(0,n.jsx)(r.Icon,{as:h.FrA,mr:2}),"Automation"]})]}),(0,n.jsxs)(r.TabPanels,{children:[(0,n.jsx)(r.TabPanel,{children:(0,n.jsxs)(r.VStack,{spacing:8,align:"stretch",children:[(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsx)(r.Heading,{size:"md",children:"Basic Settings"})}),(0,n.jsx)(r.CardBody,{children:(0,n.jsxs)(r.SimpleGrid,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Bot Name"}),(0,n.jsx)(r.Input,{value:w.botName,onChange:e=>eC("botName",e.target.value),placeholder:"Enter bot name"})]}),(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Command Prefix"}),(0,n.jsx)(r.Input,{value:w.prefix,onChange:e=>eC("prefix",e.target.value),placeholder:"Enter command prefix"})]})]}),(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Server Name"}),(0,n.jsx)(r.Input,{value:w.guildName||"",isReadOnly:!0,bg:"gray.50",_dark:{bg:"gray.700"}})]}),(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Server ID"}),(0,n.jsx)(r.Input,{value:w.guildId||"",isReadOnly:!0,bg:"gray.50",_dark:{bg:"gray.700"},fontFamily:"mono",fontSize:"sm"})]})]})]})})]}),(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(r.HStack,{justify:"space-between",children:[(0,n.jsxs)(r.Heading,{size:"md",children:[(0,n.jsx)(r.Icon,{as:h.cfS,mr:2}),"Roles (",z.length,")"]}),(0,n.jsx)(r.HStack,{spacing:2,children:(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.GGD,{}),colorScheme:"green",onClick:()=>{K()},isDisabled:t,size:"sm",children:"Create Role"})})]}),ed.length>0&&(0,n.jsxs)(r.HStack,{justify:"space-between",p:3,bg:"blue.50",_dark:{bg:"blue.900"},borderRadius:"md",children:[(0,n.jsxs)(r.Text,{fontSize:"sm",fontWeight:"medium",children:[ed.length," role(s) selected"]}),(0,n.jsxs)(r.HStack,{spacing:2,children:[(0,n.jsx)(r.Button,{size:"sm",variant:"outline",onClick:()=>eh([]),children:"Clear Selection"}),(0,n.jsx)(r.Button,{size:"sm",colorScheme:"red",leftIcon:(0,n.jsx)(h.IXo,{}),onClick:ew,isLoading:em,isDisabled:c,children:"Delete Selected"})]})]})]})}),(0,n.jsx)(r.CardBody,{children:N?(0,n.jsx)(r.VStack,{spacing:3,children:[void 0,void 0,void 0].map((e,s)=>(0,n.jsx)(r.Skeleton,{height:"60px"},s))}):(0,n.jsx)(r.Box,{overflowX:"auto",children:(0,n.jsxs)(r.Table,{variant:"simple",size:"sm",children:[(0,n.jsx)(r.Thead,{children:(0,n.jsxs)(r.Tr,{children:[(0,n.jsx)(r.Th,{children:(0,n.jsx)(r.Checkbox,{isChecked:ed.length===z.filter(e=>"@everyone"!==e.name).length&&z.length>1,isIndeterminate:ed.length>0&&ed.length<z.filter(e=>"@everyone"!==e.name).length,onChange:()=>{let e=z.filter(e=>"@everyone"!==e.name).map(e=>e.id);eh(ed.length===e.length?[]:e)}})}),(0,n.jsx)(r.Th,{children:"Role"}),(0,n.jsx)(r.Th,{children:"Members"}),(0,n.jsx)(r.Th,{children:"Permissions"}),(0,n.jsx)(r.Th,{children:"Actions"})]})}),(0,n.jsx)(r.Tbody,{children:(z||[]).map(e=>(0,n.jsxs)(r.Tr,{children:[(0,n.jsx)(r.Td,{children:(0,n.jsx)(r.Checkbox,{isChecked:ed.includes(e.id),onChange:()=>ez(e.id),isDisabled:"@everyone"===e.name})}),(0,n.jsx)(r.Td,{children:(0,n.jsxs)(r.HStack,{children:[(0,n.jsx)(r.Box,{w:4,h:4,rounded:"full",bg:e.color?`#${e.color.toString(16).padStart(6,"0")}`:"gray.500"}),(0,n.jsx)(r.Text,{children:e.name})]})}),(0,n.jsx)(r.Td,{children:(0,n.jsx)(r.Badge,{colorScheme:"blue",children:"0"})}),(0,n.jsx)(r.Td,{children:(0,n.jsxs)(r.HStack,{wrap:"wrap",spacing:1,children:[(b(e.permissions)||[]).slice(0,3).map(e=>(0,n.jsx)(r.Badge,{colorScheme:B[e]?.color||"gray",size:"sm",children:B[e]?.label||e},e)),b(e.permissions).length>3&&(0,n.jsxs)(r.Badge,{colorScheme:"gray",size:"sm",children:["+",b(e.permissions).length-3]})]})}),(0,n.jsx)(r.Td,{children:(0,n.jsxs)(r.HStack,{spacing:2,children:[(0,n.jsx)(r.Tooltip,{label:"Edit Role",children:(0,n.jsx)(r.IconButton,{"aria-label":"Edit role",icon:(0,n.jsx)(h.WXf,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>ev(e),isDisabled:t})}),(0,n.jsx)(r.Tooltip,{label:"Delete Role",children:(0,n.jsx)(r.IconButton,{"aria-label":"Delete role",icon:(0,n.jsx)(h.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",isDisabled:t})})]})})]},e.id))})]})})})]}),(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsxs)(r.HStack,{justify:"space-between",children:[(0,n.jsxs)(r.Heading,{size:"md",children:[(0,n.jsx)(r.Icon,{as:h.Qz2,mr:2}),"Channels (",M.length,")"]}),(0,n.jsx)(r.HStack,{spacing:2,children:(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.GGD,{}),colorScheme:"blue",onClick:L,size:"sm",children:"Create Channel"})})]}),eu.length>0&&(0,n.jsxs)(r.HStack,{justify:"space-between",p:3,bg:"blue.50",_dark:{bg:"blue.900"},borderRadius:"md",children:[(0,n.jsxs)(r.Text,{fontSize:"sm",fontWeight:"medium",children:[eu.length," channel(s) selected"]}),(0,n.jsxs)(r.HStack,{spacing:2,children:[(0,n.jsx)(r.Button,{size:"sm",variant:"outline",onClick:()=>ex([]),children:"Clear Selection"}),(0,n.jsx)(r.Button,{size:"sm",colorScheme:"red",leftIcon:(0,n.jsx)(h.IXo,{}),onClick:eA,isLoading:em,isDisabled:c,children:"Delete Selected"})]})]})]})}),(0,n.jsx)(r.CardBody,{children:_?(0,n.jsx)(r.VStack,{spacing:3,children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,n.jsx)(r.Skeleton,{height:"50px"},s))}):0===M.length?(0,n.jsx)(r.Text,{color:"gray.500",textAlign:"center",py:8,children:"No channels found"}):(0,n.jsx)(r.Box,{overflowX:"auto",children:(0,n.jsxs)(r.Table,{variant:"simple",size:"sm",children:[(0,n.jsx)(r.Thead,{children:(0,n.jsxs)(r.Tr,{children:[(0,n.jsx)(r.Th,{children:(0,n.jsx)(r.Checkbox,{isChecked:eu.length===M.length&&M.length>0,isIndeterminate:eu.length>0&&eu.length<M.length,onChange:()=>{let e=M.map(e=>e.id);ex(eu.length===e.length?[]:e)}})}),(0,n.jsx)(r.Th,{children:"Name"}),(0,n.jsx)(r.Th,{children:"Type"}),(0,n.jsx)(r.Th,{children:"Category"}),(0,n.jsx)(r.Th,{children:"Position"}),(0,n.jsx)(r.Th,{children:"Actions"})]})}),(0,n.jsx)(r.Tbody,{children:(M||[]).map(e=>{let s=I[e.type]||{icon:h.mEP,color:"gray",label:"Other"};return(0,n.jsxs)(r.Tr,{children:[(0,n.jsx)(r.Td,{children:(0,n.jsx)(r.Checkbox,{isChecked:eu.includes(e.id),onChange:()=>eE(e.id)})}),(0,n.jsx)(r.Td,{children:(0,n.jsxs)(r.HStack,{children:[(0,n.jsx)(r.Icon,{as:s.icon,color:`${s.color}.400`}),(0,n.jsx)(r.Text,{children:e.name})]})}),(0,n.jsx)(r.Td,{children:(0,n.jsx)(r.Badge,{colorScheme:s.color,children:s.label})}),(0,n.jsx)(r.Td,{children:(0,n.jsx)(r.Text,{color:"gray.500",children:eB(e.parent_id)})}),(0,n.jsx)(r.Td,{children:(0,n.jsx)(r.Text,{color:"gray.500",children:e.position})}),(0,n.jsx)(r.Td,{children:(0,n.jsxs)(r.HStack,{spacing:2,children:[(0,n.jsx)(r.Tooltip,{label:"Edit Channel",children:(0,n.jsx)(r.IconButton,{"aria-label":"Edit channel",icon:(0,n.jsx)(h.WXf,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>eT(e),isDisabled:t})}),(0,n.jsx)(r.Tooltip,{label:"Delete Channel",children:(0,n.jsx)(r.IconButton,{"aria-label":"Delete channel",icon:(0,n.jsx)(h.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>eI(e.id),isDisabled:t})})]})})]},e.id)})})]})})})]})]})}),(0,n.jsx)(r.TabPanel,{children:(0,n.jsxs)(r.VStack,{spacing:6,align:"stretch",children:[(0,n.jsxs)(r.Box,{children:[(0,n.jsx)(r.Heading,{size:"md",mb:4,children:"\uD83C\uDFA8 Theme Builder"}),(0,n.jsx)(r.Text,{color:"gray.600",_dark:{color:"gray.300"},mb:6,children:"Create and customize your own themes with the advanced color builder"})]}),(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsxs)(r.HStack,{justify:"space-between",align:"center",children:[(0,n.jsx)(r.Heading,{size:"sm",children:"Theme Builder & Presets"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(u.lV_,{}),colorScheme:"purple",onClick:es,size:"sm",children:"Create Custom Theme"})]})}),(0,n.jsx)(r.CardBody,{children:(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(r.Text,{fontSize:"sm",color:"gray.500",mb:2,children:"Choose from pre-built themes or create your own custom theme"}),(0,n.jsxs)(r.VStack,{spacing:3,align:"stretch",children:[(0,n.jsx)(r.Text,{fontSize:"xs",fontWeight:"bold",color:"gray.400",textTransform:"uppercase",children:"Built-in Themes"}),(0,n.jsx)(r.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:3,children:p.nk.map(e=>(0,n.jsx)(r.Box,{p:3,border:"1px",borderColor:x.id===e.id?e.colors.primary:"gray.200",borderRadius:"md",cursor:"pointer",transition:"all 0.2s",bg:x.id===e.id?`${e.colors.primary}10`:"transparent",_hover:{borderColor:e.colors.primary,transform:"translateY(-1px)"},onClick:()=>m(e.id),children:(0,n.jsxs)(r.VStack,{spacing:2,align:"stretch",children:[(0,n.jsxs)(r.HStack,{justify:"space-between",children:[(0,n.jsx)(r.Text,{fontSize:"sm",fontWeight:"medium",children:e.name}),x.id===e.id&&(0,n.jsx)(r.Badge,{colorScheme:"green",size:"sm",children:"Active"})]}),(0,n.jsx)(r.Text,{fontSize:"xs",color:"gray.500",lineHeight:"1.3",children:e.description}),(0,n.jsxs)(r.HStack,{spacing:1,children:[(0,n.jsx)(r.Box,{w:3,h:3,bg:e.colors.primary,borderRadius:"full"}),(0,n.jsx)(r.Box,{w:3,h:3,bg:e.colors.secondary,borderRadius:"full"}),(0,n.jsx)(r.Box,{w:3,h:3,bg:e.colors.accent,borderRadius:"full"}),(0,n.jsx)(r.Box,{w:3,h:3,bg:e.colors.success,borderRadius:"full"})]})]})},e.id))})]}),g.length>0&&(0,n.jsxs)(r.VStack,{spacing:3,align:"stretch",children:[(0,n.jsx)(r.Text,{fontSize:"xs",fontWeight:"bold",color:"gray.400",textTransform:"uppercase",children:"Custom Themes"}),(0,n.jsx)(r.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:3,children:g.map(s=>(0,n.jsx)(r.Box,{p:3,border:"1px",borderColor:x.id===s.id?s.colors.primary:"gray.200",borderRadius:"md",position:"relative",transition:"all 0.2s",bg:x.id===s.id?`${s.colors.primary}10`:"transparent",_hover:{borderColor:s.colors.primary,transform:"translateY(-1px)"},children:(0,n.jsxs)(r.VStack,{spacing:2,align:"stretch",children:[(0,n.jsxs)(r.HStack,{justify:"space-between",children:[(0,n.jsx)(r.Text,{fontSize:"sm",fontWeight:"medium",cursor:"pointer",onClick:()=>m(s.id),flex:"1",children:s.name}),(0,n.jsxs)(r.HStack,{spacing:1,children:[x.id===s.id&&(0,n.jsx)(r.Badge,{colorScheme:"green",size:"sm",children:"Active"}),(0,n.jsx)(r.Badge,{colorScheme:"purple",size:"sm",children:"Custom"}),(0,n.jsx)(r.Tooltip,{label:"Delete Custom Theme",children:(0,n.jsx)(r.IconButton,{"aria-label":"Delete theme",icon:(0,n.jsx)(h.IXo,{}),size:"xs",variant:"ghost",colorScheme:"red",onClick:t=>{t.stopPropagation(),S(s.id),e({title:"Theme Deleted",description:`${s.name} has been deleted`,status:"success",duration:3e3})}})})]})]}),(0,n.jsx)(r.Text,{fontSize:"xs",color:"gray.500",lineHeight:"1.3",cursor:"pointer",onClick:()=>m(s.id),children:s.description}),(0,n.jsxs)(r.HStack,{spacing:1,cursor:"pointer",onClick:()=>m(s.id),children:[(0,n.jsx)(r.Box,{w:3,h:3,bg:s.colors.primary,borderRadius:"full"}),(0,n.jsx)(r.Box,{w:3,h:3,bg:s.colors.secondary,borderRadius:"full"}),(0,n.jsx)(r.Box,{w:3,h:3,bg:s.colors.accent,borderRadius:"full"}),(0,n.jsx)(r.Box,{w:3,h:3,bg:s.colors.success,borderRadius:"full"})]})]})},s.id))})]})]})})]})]})}),(0,n.jsx)(r.TabPanel,{children:(0,n.jsxs)(r.VStack,{spacing:6,align:"stretch",children:[(0,n.jsxs)(r.Box,{children:[(0,n.jsx)(r.Heading,{size:"md",mb:4,children:"\uD83D\uDEE0️ Builders & Tools"}),(0,n.jsx)(r.Text,{color:"gray.600",_dark:{color:"gray.300"},mb:6,children:"Create custom content and manage server features with powerful builders"})]}),(0,n.jsxs)(r.SimpleGrid,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsx)(r.Heading,{size:"sm",children:"Content Builders"})}),(0,n.jsx)(r.CardBody,{children:(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.FrA,{}),colorScheme:"green",onClick:()=>window.open("/admin/experimental/addon-builder","_blank"),children:"Addon Builder"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.mEP,{}),colorScheme:"blue",onClick:()=>window.open("/admin/applications-builder","_blank"),children:"Applications Builder"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.mEP,{}),colorScheme:"purple",onClick:()=>window.open("/admin/embed-builder","_blank"),isDisabled:!0,children:"Message Builder (Coming Soon)"})]})})]}),(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsx)(r.Heading,{size:"sm",children:"Management Tools"})}),(0,n.jsx)(r.CardBody,{children:(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.VSk,{}),colorScheme:"orange",onClick:()=>window.open("/admin/addons","_blank"),children:"Manage Addons"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.VSk,{}),colorScheme:"teal",onClick:()=>window.open("/admin/commands","_blank"),children:"Command Manager"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.VSk,{}),colorScheme:"cyan",onClick:()=>window.open("/admin/applications","_blank"),children:"Application Manager"})]})})]})]})]})}),(0,n.jsx)(r.TabPanel,{children:(0,n.jsxs)(r.VStack,{spacing:6,align:"stretch",children:[(0,n.jsxs)(r.Box,{children:[(0,n.jsx)(r.Heading,{size:"md",mb:4,children:"⚡ Automation & Activities"}),(0,n.jsx)(r.Text,{color:"gray.600",_dark:{color:"gray.300"},mb:6,children:"Set up automated features and server activities"})]}),(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsxs)(r.HStack,{justify:"space-between",children:[(0,n.jsx)(r.Heading,{size:"md",children:"Bot Presence"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.Bc_,{}),colorScheme:"blue",onClick:eN,isLoading:G,isDisabled:t,children:"Save Presence"})]})}),(0,n.jsx)(r.CardBody,{children:(0,n.jsxs)(r.VStack,{spacing:6,align:"stretch",children:[w.activities.map((e,s)=>(0,n.jsxs)(r.HStack,{spacing:4,align:"flex-end",children:[(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{fontSize:"sm",children:"Activity Type"}),(0,n.jsxs)(r.Select,{value:e.type,onChange:e=>eM(s,"type",e.target.value),children:[(0,n.jsx)("option",{value:"PLAYING",children:"Playing"}),(0,n.jsx)("option",{value:"STREAMING",children:"Streaming"}),(0,n.jsx)("option",{value:"LISTENING",children:"Listening"}),(0,n.jsx)("option",{value:"WATCHING",children:"Watching"}),(0,n.jsx)("option",{value:"COMPETING",children:"Competing"})]})]}),(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{fontSize:"sm",children:"Activity Name"}),(0,n.jsx)(r.Input,{value:e.name,onChange:e=>eM(s,"name",e.target.value),placeholder:"e.g., with discord.js"})]}),(0,n.jsx)(r.IconButton,{"aria-label":"Remove activity",icon:(0,n.jsx)(h.IXo,{}),colorScheme:"red",variant:"ghost",onClick:()=>eP(s)})]},s)),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.GGD,{}),onClick:()=>{A(e=>({...e,activities:[...e.activities,{type:"PLAYING",name:""}]}))},size:"sm",alignSelf:"flex-start",children:"Add Activity"}),(0,n.jsx)(r.Divider,{}),(0,n.jsxs)(r.FormControl,{children:[(0,n.jsx)(r.FormLabel,{children:"Activity Rotation Interval (seconds)"}),(0,n.jsxs)(r.NumberInput,{value:w.activityRotationInterval,onChange:e=>eC("activityRotationInterval",parseInt(e)||60),min:10,max:3600,children:[(0,n.jsx)(r.NumberInputField,{}),(0,n.jsxs)(r.NumberInputStepper,{children:[(0,n.jsx)(r.NumberIncrementStepper,{}),(0,n.jsx)(r.NumberDecrementStepper,{})]})]}),(0,n.jsx)(r.Text,{fontSize:"xs",color:"gray.500",mt:1,children:"Time between rotating activities (min 10s, max 1hr)."})]})]})})]}),(0,n.jsxs)(r.SimpleGrid,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsx)(r.Heading,{size:"sm",children:"Activity Templates"})}),(0,n.jsxs)(r.CardBody,{children:[(0,n.jsx)(r.Text,{color:"gray.500",fontSize:"sm",mb:4,children:"Pre-built activity templates to get you started quickly:"}),(0,n.jsxs)(r.VStack,{spacing:2,align:"stretch",children:[(0,n.jsx)(r.Text,{fontSize:"sm",children:"• Event Management System"}),(0,n.jsx)(r.Text,{fontSize:"sm",children:"• Welcome & Onboarding Flow"}),(0,n.jsx)(r.Text,{fontSize:"sm",children:"• Moderation Automation"}),(0,n.jsx)(r.Text,{fontSize:"sm",children:"• Custom Commands"}),(0,n.jsx)(r.Text,{fontSize:"sm",children:"• Auto-Role Assignment"}),(0,n.jsx)(r.Text,{fontSize:"sm",children:"• Scheduled Messages"})]})]})]}),(0,n.jsxs)(r.Card,{children:[(0,n.jsx)(r.CardHeader,{children:(0,n.jsx)(r.Heading,{size:"sm",children:"Automation Settings"})}),(0,n.jsx)(r.CardBody,{children:(0,n.jsxs)(r.VStack,{spacing:4,align:"stretch",children:[(0,n.jsx)(r.Text,{fontSize:"sm",color:"gray.500",children:"Configure automated server features"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.FrA,{}),colorScheme:"yellow",variant:"outline",isDisabled:!0,children:"Auto-Moderation (Coming Soon)"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.FrA,{}),colorScheme:"green",variant:"outline",onClick:en,children:"Welcome System"}),(0,n.jsx)(r.Button,{leftIcon:(0,n.jsx)(h.FrA,{}),colorScheme:"blue",variant:"outline",isDisabled:!0,children:"Event Scheduler (Coming Soon)"})]})})]})]})]})})]})]})]}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(r.Spinner,{}),children:(0,n.jsx)(f,{isOpen:V,onClose:W,onSuccess:ey})}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(r.Spinner,{}),children:(0,n.jsx)(y,{isOpen:q,onClose:X,channel:ea,categories:M.filter(e=>4===e.type),onSuccess:ey})}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(r.Spinner,{}),children:(0,n.jsx)(v,{isOpen:Y,onClose:U,onSuccess:ef})}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(r.Spinner,{}),children:(0,n.jsx)(C,{isOpen:J,onClose:Z,role:el,onSuccess:ef})}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(r.Spinner,{}),children:(0,n.jsx)(k,{isOpen:ee,onClose:et})}),(0,n.jsx)(a.Suspense,{fallback:(0,n.jsx)(r.Spinner,{}),children:(0,n.jsx)(T,{isOpen:ei,onClose:er,channels:M,roles:z})})]})})}let A=async e=>{let s=await (0,c.getServerSession)(e.req,e.res,d.N);return s?{props:{session:s}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fadmin%2Fguilds",permanent:!1}}};i()}catch(e){i(e)}})},3762:(e,s,t)=>{"use strict";t.d(s,{N:()=>x});var i=t(5542),n=t.n(i);let r=require("next-auth/providers/discord");var l=t.n(r),o=t(9021),a=t(2115),c=t.n(a),d=t(3873);let h={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let s=d.resolve(__dirname,"../../../config.yml");o.existsSync(s)&&(e=s)}if(!e)throw Error("config.yml not found");let s=o.readFileSync(e,"utf8");h=c().parse(s)}catch(e){process.exit(1)}let u={bot:{token:h.bot.token,clientId:h.bot.clientId,clientSecret:h.bot.clientSecret,guildId:h.bot.guildId,ticketCategoryId:h.bot.ticketCategoryId||null,ticketLogChannelId:h.bot.ticketLogChannelId||null,prefix:h.bot.prefix},dashboard:{admins:h.dashboard?.admins||[],adminRoleIds:h.dashboard?.adminRoleIds||[],session:{secret:h.dashboard?.session?.secret||h.bot.clientSecret}},database:{url:h.database.url,name:h.database.name,options:{maxPoolSize:h.database.options?.maxPoolSize||10,minPoolSize:h.database.options?.minPoolSize||1,maxIdleTimeMS:h.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:h.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:h.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:h.database.options?.connectTimeoutMS||1e4,retryWrites:h.database.options?.retryWrites!==!1,retryReads:h.database.options?.retryReads!==!1}}};u.bot.token||process.exit(1),u.bot.clientId&&u.bot.clientSecret||process.exit(1),u.bot.guildId||process.exit(1),u.database.url&&u.database.name||process.exit(1);let x={providers:[l()({clientId:u.bot.clientId,clientSecret:u.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:s,profile:t})=>(s&&t&&(e.accessToken=s.access_token||null,e.id=t.id||null),e),async session({session:e,token:s}){if(e?.user){let t=s.id||null,i=s.accessToken||null;e.user.id=t,e.user.accessToken=i;let n=!1;if(t)if((u.dashboard.admins||[]).includes(t))n=!0;else{let e=u.dashboard.adminRoleIds||[];if(e.length&&u.bot.token&&u.bot.guildId)try{let s=await fetch(`https://discord.com/api/v10/guilds/${u.bot.guildId}/members/${t}`,{headers:{Authorization:`Bot ${u.bot.token}`}});if(s.ok){let t=await s.json();n=e.some(e=>t.roles?.includes(e))||!1}else await s.text()}catch(e){}}e.user.isAdmin=n,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:s}){let t=new URL(s),i=`${t.protocol}//localhost${t.port?`:${t.port}`:""}`;return e.startsWith(s)||e.startsWith(i)?e:s}},secret:u.dashboard.session.secret||u.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,s)=>{},warn:e=>{},debug:(e,s)=>{}}};n()(x)},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4078:e=>{"use strict";e.exports=import("swr")},4722:e=>{"use strict";e.exports=require("next-auth/react")},5542:e=>{"use strict";e.exports=require("next-auth")},5806:e=>{"use strict";e.exports=require("next-auth/next")},7910:e=>{"use strict";e.exports=require("stream")},8364:(e,s,t)=>{e.exports=t(9554)},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},8914:(e,s,t)=>{"use strict";e.exports=t(1292).vendored.contexts.Loadable},9021:e=>{"use strict";e.exports=require("fs")},9474:(e,s,t)=>{"use strict";t.a(e,async(e,i)=>{try{t.r(s),t.d(s,{config:()=>p,default:()=>h,getServerSideProps:()=>m,getStaticPaths:()=>x,getStaticProps:()=>u,reportWebVitals:()=>g,routeModule:()=>C,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>y,unstable_getStaticParams:()=>S,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>j});var n=t(1292),r=t(8834),l=t(786),o=t(3567),a=t(8077),c=t(2649),d=e([a,c]);[a,c]=d.then?(await d)():d;let h=(0,l.M)(c,"default"),u=(0,l.M)(c,"getStaticProps"),x=(0,l.M)(c,"getStaticPaths"),m=(0,l.M)(c,"getServerSideProps"),p=(0,l.M)(c,"config"),g=(0,l.M)(c,"reportWebVitals"),j=(0,l.M)(c,"unstable_getStaticProps"),b=(0,l.M)(c,"unstable_getStaticPaths"),S=(0,l.M)(c,"unstable_getStaticParams"),f=(0,l.M)(c,"unstable_getServerProps"),y=(0,l.M)(c,"unstable_getServerSideProps"),C=new n.PagesRouteModule({definition:{kind:r.A.PAGES,page:"/admin/guilds",pathname:"/admin/guilds",bundlePath:"",filename:""},components:{App:a.default,Document:o.default},userland:c});i()}catch(e){i(e)}})},9554:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{default:function(){return a},noSSR:function(){return o}});let i=t(2403),n=t(8732);t(2015);let r=i._(t(8914));function l(e){return{default:(null==e?void 0:e.default)||e}}function o(e,s){delete s.webpack,delete s.modules;let t=s.loading;return()=>(0,n.jsx)(t,{error:null,isLoading:!0,pastDelay:!1,timedOut:!1})}function a(e,s){let t=r.default,i={loading:e=>{let{error:s,isLoading:t,pastDelay:i}=e;return null}};e instanceof Promise?i.loader=()=>e:"function"==typeof e?i.loader=e:"object"==typeof e&&(i={...i,...e});let n=(i={...i,...s}).loader;return(i.loadableGenerated&&(i={...i,...i.loadableGenerated},delete i.loadableGenerated),"boolean"!=typeof i.ssr||i.ssr)?t({...i,loader:()=>null!=n?n().then(l):Promise.resolve(l(()=>null))}):(delete i.webpack,delete i.modules,o(t,i))}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[8270,4874,752,6281,2695,5333],()=>t(9474));module.exports=i})();