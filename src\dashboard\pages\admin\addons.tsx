import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Heading,
  Text,
  SimpleGrid,
  VStack,
  HStack,
  Icon,
  useToast,
  Card,
  CardBody,
  Badge,
  Switch,
  Button,
  Skeleton,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  Textarea,
  Divider,
} from '@chakra-ui/react';
import Layout from '../../components/Layout';
import {
  FiPackage,
  FiSettings,
  FiRefreshCw,
  FiCode,
  FiSave,
  FiTrash2,
  FiZap,
} from 'react-icons/fi';
import * as YAML from 'yaml';
import dynamic from 'next/dynamic';

const YamlEditor = dynamic(() => import('../../components/YamlEditor'), {
  ssr: false,
  loading: () => <Skeleton height="400px" />,
});

// Color schemes for different addon types
const ADDON_COLORS = {
  'voice-mistress': {
    color: 'purple',
    gradient: {
      from: 'rgba(159, 122, 234, 0.4)',
      to: 'rgba(159, 122, 234, 0.1)'
    }
  },
  'tickets': {
    color: 'orange',
    gradient: {
      from: 'rgba(237, 137, 54, 0.4)',
      to: 'rgba(237, 137, 54, 0.1)'
    }
  },
  'welcome-goodbye': {
    color: 'blue',
    gradient: {
      from: 'rgba(66, 153, 225, 0.4)',
      to: 'rgba(66, 153, 225, 0.1)'
    }
  },
  'example': {
    color: 'green',
    gradient: {
      from: 'rgba(72, 187, 120, 0.4)',
      to: 'rgba(72, 187, 120, 0.1)'
    }
  },
  // Default color scheme
  default: {
    color: 'teal',
    gradient: {
      from: 'rgba(49, 151, 149, 0.4)',
      to: 'rgba(49, 151, 149, 0.1)'
    }
  }
};

interface AddonConfig {
  name: string;
  enabled: boolean;
  config?: string;
  description?: string;
  version?: string;
  author?: string;
  isCustomAddon?: boolean;
}

const AddonCard: React.FC<{
  addon: AddonConfig;
  onSave: (newConfig: string) => void;
  onToggle: (addon: AddonConfig) => void;
  onEdit: (addon: AddonConfig) => void;
  onDelete?: (addon: AddonConfig) => void;
}> = ({ addon, onSave, onToggle, onEdit, onDelete }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedConfig, setEditedConfig] = useState(addon.config || '');
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const toast = useToast();

  // Update editedConfig when addon.config changes
  useEffect(() => {
    setEditedConfig(addon.config || '');
  }, [addon.config]);

  const handleSave = async () => {
    try {
      // Validate YAML format
      YAML.parse(editedConfig || '');
      setError(null);
      
      const response = await fetch(`/api/admin/addons/${addon.name}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ config: editedConfig }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save configuration');
      }

      onSave(editedConfig);
      setIsEditing(false);
      toast({
        title: 'Success',
        description: 'Configuration saved successfully',
        status: 'success',
        duration: 3000,
      });
    } catch (err: any) {
      setError(err.message || 'Invalid YAML format');
      toast({
        title: 'Error',
        description: err.message || 'Invalid YAML format',
        status: 'error',
        duration: 5000,
      });
    }
  };

  return (
    <Card
      key={addon.name}
      bg={ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color ? `linear-gradient(135deg, ${ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS].gradient.from}, ${ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS].gradient.to})` : 'gray.900'}
      backdropFilter="blur(10px)"
      borderWidth={2}
      borderColor={ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color ? `${ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS].color}.400` : 'gray.600'}
      rounded="xl"
      overflow="hidden"
      transition="all 0.2s"
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color ? `0 4px 20px ${ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS].gradient.from}` : 'none',
      }}
    >
      <CardBody>
        {isEditing ? (
          <VStack spacing={4} align="stretch">
            <YamlEditor
              value={editedConfig}
              onChange={(value) => {
                setEditedConfig(value);
                setError(null); // Clear error when user makes changes
              }}
            />
            {error && (
              <Text color="red.400" fontSize="sm">
                {error}
              </Text>
            )}
            <HStack justify="flex-end" spacing={2}>
              <Button
                variant="ghost"
                onClick={() => {
                  setIsEditing(false);
                  setEditedConfig(addon.config || '');
                  setError(null);
                }}
              >
                Cancel
              </Button>
              <Button
                colorScheme="purple"
                onClick={handleSave}
                isDisabled={!!error}
              >
                Save Changes
              </Button>
            </HStack>
          </VStack>
        ) : (
          <VStack align="stretch" spacing={4}>
            <HStack justify="space-between">
              <HStack>
                <Icon 
                  as={FiPackage}
                  color={`${ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color || 'gray'}.400`}
                  boxSize={5}
                />
                <Heading size="md" color="white">
                  {addon.name}
                </Heading>
              </HStack>
              <Badge colorScheme={addon.enabled ? ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color || 'gray' : 'gray'}>
                {addon.enabled ? 'Active' : 'Inactive'}
              </Badge>
            </HStack>

            <Text color="gray.300" fontSize="sm" noOfLines={2}>
              {addon.description}
            </Text>

            <HStack>
              <Text color="gray.400" fontSize="xs">
                v{addon.version}
              </Text>
              <Text color="gray.400" fontSize="xs">
                by {addon.author}
              </Text>
            </HStack>

            <Divider borderColor="whiteAlpha.200" />

            <HStack justify="space-between">
              <Switch
                isChecked={addon.enabled}
                onChange={() => onToggle(addon)}
                colorScheme={ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color || 'gray'}
              />
              <HStack spacing={2}>
                <Button
                  size="sm"
                  leftIcon={<Icon as={FiCode} />}
                  variant="ghost"
                  colorScheme={ADDON_COLORS[addon.name as keyof typeof ADDON_COLORS]?.color || 'gray'}
                  onClick={() => onEdit(addon)}
                >
                  Edit Config
                </Button>
                {addon.isCustomAddon && onDelete && (
                  <>
                    <Button
                      size="sm"
                      leftIcon={<Icon as={FiTrash2} />}
                      variant="ghost"
                      colorScheme="red"
                      onClick={() => setShowDeleteConfirm(true)}
                    >
                      Delete
                    </Button>
                    
                    <Modal isOpen={showDeleteConfirm} onClose={() => setShowDeleteConfirm(false)}>
                      <ModalOverlay />
                      <ModalContent>
                        <ModalHeader>Delete Custom Addon</ModalHeader>
                        <ModalCloseButton />
                        <ModalBody>
                          <Text>
                            Are you sure you want to delete <strong>{addon.name}</strong>? 
                            This action cannot be undone and will permanently remove all files for this custom addon.
                          </Text>
                        </ModalBody>
                        <ModalFooter>
                          <Button 
                            variant="ghost" 
                            mr={3} 
                            onClick={() => setShowDeleteConfirm(false)}
                          >
                            Cancel
                          </Button>
                          <Button 
                            colorScheme="red" 
                            onClick={() => {
                              onDelete(addon);
                              setShowDeleteConfirm(false);
                            }}
                          >
                            Delete
                          </Button>
                        </ModalFooter>
                      </ModalContent>
                    </Modal>
                  </>
                )}
              </HStack>
            </HStack>
          </VStack>
        )}
      </CardBody>
    </Card>
  );
};

export default function AddonsPage() {
  const [builtInAddons, setBuiltInAddons] = useState<AddonConfig[]>([]);
  const [customAddons, setCustomAddons] = useState<AddonConfig[]>([]);
  const [selectedAddon, setSelectedAddon] = useState<AddonConfig | null>(null);
  const [configYaml, setConfigYaml] = useState('');
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();

  // Add cleanup effect when modal closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedAddon(null);
      setConfigYaml('');
    }
  }, [isOpen]);

  const fetchAddons = async () => {
    try {
      const response = await fetch('/api/admin/addons', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      if (!response.ok) {
        const errorData = await response.json();
        
        // If unauthorized, redirect to sign in
        if (response.status === 401 || response.status === 403) {
          window.location.href = '/signin';
          return;
        }
        
        throw new Error(errorData.message || 'Failed to fetch addons');
      }
      const data = await response.json();
      
      // Use the new API structure if available, fallback to legacy
      if (data.builtInAddons && data.customAddons) {
        setBuiltInAddons(data.builtInAddons);
        setCustomAddons(data.customAddons);
      } else {
        // Legacy fallback - categorize addons client-side
        const builtIn = data.addons?.filter((addon: AddonConfig) => !addon.isCustomAddon) || [];
        const custom = data.addons?.filter((addon: AddonConfig) => addon.isCustomAddon) || [];
        setBuiltInAddons(builtIn);
        setCustomAddons(custom);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to fetch addons',
        status: 'error',
        duration: 5000,
      });
    }
  };

  useEffect(() => {
    fetchAddons();
  }, []);

  const handleToggleAddon = async (addon: AddonConfig) => {
    try {
      const response = await fetch(`/api/admin/addons/${addon.name}`, {
        method: 'PATCH',
        credentials: 'include',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enabled: !addon.enabled }),
      });

      if (!response.ok) throw new Error('Failed to toggle addon');

      // Trigger live reload of addons (bot-side)
      try {
        await fetch('/api/admin/addons/reload', { 
          method: 'POST',
          credentials: 'include'
        });
      } catch { /* ignore */ }

      // Update the appropriate list
      const updateAddon = (prev: AddonConfig[]) => 
        prev.map((a) => a.name === addon.name ? { ...a, enabled: !a.enabled } : a);
      
      if (addon.isCustomAddon) {
        setCustomAddons(updateAddon);
      } else {
        setBuiltInAddons(updateAddon);
      }

      toast({
        title: 'Success',
        description: `Addon ${addon.enabled ? 'disabled' : 'enabled'} successfully`,
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to toggle addon',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const handleUpdateConfig = (addon: AddonConfig, newConfig: string) => {
    const updateAddon = (prev: AddonConfig[]) => 
      prev.map((a) => a.name === addon.name ? { ...a, config: newConfig } : a);
    
    if (addon.isCustomAddon) {
      setCustomAddons(updateAddon);
    } else {
      setBuiltInAddons(updateAddon);
    }
  };

  const handleConfigureAddon = async (addon: AddonConfig) => {
    try {
      const response = await fetch(`/api/admin/addons/${addon.name}`, {
        credentials: 'include'
      });
      if (!response.ok) throw new Error('Failed to fetch addon config');
      const data = await response.json();
      setSelectedAddon(addon);
      setConfigYaml(data.configYaml || '');
      onOpen();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to fetch addon configuration',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const handleEditConfig = async (addon: AddonConfig) => {
    try {
      console.log(`Fetching config for addon: ${addon.name}`);
      const response = await fetch(`/api/admin/addons/${addon.name}/config`, {
        credentials: 'include'
      });
      
      if (!response.ok) {
        const error = await response.json();
        console.error('Config fetch error:', error);
        throw new Error(error.error || `Failed to fetch addon config: ${response.status}`);
      }
      
      const data = await response.json();
      console.log('Config data received:', data);
      
      // Set the selected addon first
      setSelectedAddon(addon);
      
      // Then set the config with a small delay to ensure the modal is ready
      setTimeout(() => {
        setConfigYaml(data.config || '');
        onOpen();
      }, 100);
    } catch (error: any) {
      console.error('Error fetching config:', error);
      toast({
        title: 'Error Fetching Config',
        description: error.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleSaveConfig = async () => {
    if (!selectedAddon) return;

    try {
      // Validate YAML format
      const parsedConfig = YAML.parse(configYaml);

      const response = await fetch(`/api/admin/addons/${selectedAddon.name}/config`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ config: configYaml }),
      });

      if (!response.ok) throw new Error('Failed to save configuration');

      toast({
        title: 'Success',
        description: 'Configuration saved successfully',
        status: 'success',
        duration: 3000,
      });

      onClose();
      fetchAddons(); // Refresh the addons list
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to save configuration',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const handleReloadAddons = async () => {
    try {
      await fetch('/api/admin/addons/reload', { 
        method: 'POST',
        credentials: 'include'
      });
      
      toast({
        title: 'Success',
        description: 'Addons reloaded successfully',
        status: 'success',
        duration: 3000,
      });
      
      // Refresh the addons list
      fetchAddons();
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to reload addons',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const handleRefreshCommands = async () => {
    try {
      await fetch('/api/admin/commands/refresh', {
        method: 'POST',
        credentials: 'include'
      });
      
      toast({
        title: 'Success',
        description: 'Discord commands refreshed successfully',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to refresh commands',
        status: 'error',
        duration: 5000,
      });
    }
  };

  const handleDeleteAddon = async (addon: AddonConfig) => {
    try {
      const response = await fetch(`/api/admin/addons/${addon.name}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || errorData.error || 'Failed to delete addon');
      }

      // Trigger live reload of addons (bot-side)
      try {
        await fetch('/api/admin/addons/reload', { 
          method: 'POST',
          credentials: 'include'
        });
      } catch { /* ignore */ }

      setCustomAddons((prev) => prev.filter((a) => a.name !== addon.name));

      toast({
        title: 'Success',
        description: 'Custom addon deleted successfully',
        status: 'success',
        duration: 3000,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete addon',
        status: 'error',
        duration: 5000,
      });
    }
  };

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header Section */}
          <Box
            bg="rgba(255,255,255,0.08)"
            p={8}
            rounded="2xl"
            backdropFilter="blur(10px)"
            border="2px solid"
            borderColor="purple.400"
            boxShadow="0 0 15px rgba(159, 122, 234, 0.4)"
          >
            <HStack justify="space-between" align="center">
              <VStack align="start" spacing={2}>
                <Heading
                  size="xl"
                  bgGradient="linear(to-r, purple.300, pink.400)"
                  bgClip="text"
                >
                  Bot Addons Management
                </Heading>
                <Text color="gray.300">
                  Manage built-in addons and custom addons created with the addon builder
                </Text>
                <Text color="gray.500" fontSize="sm">
                  Use "Reload Addons" to refresh the bot's addon system. Use "Refresh Commands" to update Discord's command list.
                </Text>
              </VStack>
              <HStack spacing={2}>
                <Button
                  leftIcon={<Icon as={FiRefreshCw} />}
                  colorScheme="purple"
                  variant="outline"
                  onClick={handleReloadAddons}
                >
                  Reload Addons
                </Button>
                <Button
                  leftIcon={<Icon as={FiZap} />}
                  colorScheme="orange"
                  variant="outline"
                  onClick={handleRefreshCommands}
                >
                  Refresh Commands
                </Button>
              </HStack>
            </HStack>
          </Box>

          {/* Built-in Addons Section */}
          <Box>
            <Heading size="lg" mb={4} color="blue.300">
              🛠️ Built-in Addons ({builtInAddons.length})
            </Heading>
            <Text color="gray.400" mb={6} fontSize="sm">
              Core bot functionality - these addons cannot be deleted
            </Text>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {builtInAddons.map((addon) => (
                <AddonCard
                  key={addon.name}
                  addon={addon}
                  onSave={(newConfig) => handleUpdateConfig(addon, newConfig)}
                  onToggle={handleToggleAddon}
                  onEdit={handleEditConfig}
                  // No onDelete prop for built-in addons
                />
              ))}
            </SimpleGrid>
            {builtInAddons.length === 0 && (
              <Box
                bg="gray.800"
                p={6}
                rounded="lg"
                textAlign="center"
                borderColor="gray.600"
                borderWidth={1}
              >
                <Text color="gray.400">No built-in addons found</Text>
              </Box>
            )}
          </Box>

          {/* Custom Addons Section */}
          <Box>
            <Heading size="lg" mb={4} color="green.300">
              ⚗️ Custom Addons ({customAddons.length})
            </Heading>
            <Text color="gray.400" mb={6} fontSize="sm">
              Addons created with the addon builder - these can be edited or deleted
            </Text>
            <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={6}>
              {customAddons.map((addon) => (
                <AddonCard
                  key={addon.name}
                  addon={addon}
                  onSave={(newConfig) => handleUpdateConfig(addon, newConfig)}
                  onToggle={handleToggleAddon}
                  onEdit={handleEditConfig}
                  onDelete={handleDeleteAddon} // Only custom addons get delete functionality
                />
              ))}
            </SimpleGrid>
            {customAddons.length === 0 && (
              <Box
                bg="gray.800"
                p={6}
                rounded="lg"
                textAlign="center"
                borderColor="gray.600"
                borderWidth={1}
              >
                <Text color="gray.400">No custom addons created yet</Text>
                <Text color="gray.500" fontSize="sm" mt={2}>
                  Use the Addon Builder to create your first custom addon!
                </Text>
              </Box>
            )}
          </Box>
        </VStack>
      </Container>

      {/* Configuration Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="6xl">
        <ModalOverlay backdropFilter="blur(10px)" />
        <ModalContent
          bg="gray.800"
          border="1px solid"
          borderColor={`${ADDON_COLORS[selectedAddon?.name as keyof typeof ADDON_COLORS]?.color || 'gray'}.500`}
          maxW="1200px"
        >
          <ModalHeader>
            <HStack>
              <Icon as={FiSettings} />
              <Text>Configure {selectedAddon?.name}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody maxH="70vh" overflowY="auto">
            <VStack spacing={4} align="stretch">
              <Text color="gray.400" fontSize="sm">
                Edit the configuration in YAML format. Changes will be saved to config.yml
              </Text>
              <YamlEditor
                value={configYaml}
                onChange={setConfigYaml}
                height="60vh"
              />
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme={ADDON_COLORS[selectedAddon?.name as keyof typeof ADDON_COLORS]?.color || 'gray'}
              leftIcon={<Icon as={FiSave} />}
              onClick={handleSaveConfig}
            >
              Save Changes
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Layout>
  );
}
