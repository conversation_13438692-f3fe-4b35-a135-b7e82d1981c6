// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { DatabaseManager } from '@/core/DatabaseManager';
import { ConfigManager } from '@/core/ConfigManager';
import { dashboardConfig } from '../../../core/config';

const config = ConfigManager.getConfig();
const dbManager = new DatabaseManager(config);

const defaultSettings = {
  welcome: {
    enabled: false,
    channelId: null,
    message: 'Welcome {user} to {guild}! You are the {memberCount}th member.',
    autoRoles: [],
    embedColor: '#00FF00',
  },
  goodbye: {
    enabled: false,
    channelId: null,
    message: 'Goodbye {user}! We will miss you.',
    embedColor: '#FF0000',
  },
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Get guild ID from dashboard config instead of session
  const guildId = dashboardConfig.bot.guildId;
  if (!guildId) {
    return res.status(400).json({ error: 'Guild ID not found in configuration.' });
  }

  try {
    await dbManager.connect();

    if (req.method === 'GET') {
      const guildConfig = await dbManager.findOne('guild_configs', { guildId });
      const settings = {
        welcome: { ...defaultSettings.welcome, ...guildConfig?.welcome },
        goodbye: { ...defaultSettings.goodbye, ...guildConfig?.goodbye },
      };
      return res.status(200).json(settings);
    }

    if (req.method === 'POST') {
      const { welcome, goodbye } = req.body;

      // Basic validation
      if (typeof welcome?.enabled !== 'boolean' || typeof goodbye?.enabled !== 'boolean') {
        return res.status(400).json({ error: 'Invalid data format.' });
      }

      const updateData = {
        guildId,
        welcome: {
          enabled: welcome.enabled,
          channelId: welcome.channelId || null,
          message: welcome.message || defaultSettings.welcome.message,
          autoRoles: welcome.autoRoles || [],
          embedColor: welcome.embedColor || defaultSettings.welcome.embedColor,
        },
        goodbye: {
          enabled: goodbye.enabled,
          channelId: goodbye.channelId || null,
          message: goodbye.message || defaultSettings.goodbye.message,
          embedColor: goodbye.embedColor || defaultSettings.goodbye.embedColor,
        },
      };

      await dbManager.updateOne(
        'guild_configs',
        { guildId },
        { $set: updateData },
        { upsert: true }
      );

      return res.status(200).json({ message: 'Settings updated successfully.' });
    }

    res.setHeader('Allow', ['GET', 'POST']);
    return res.status(405).end(`Method ${req.method} Not Allowed`);
  } catch (error) {
    console.error(`[API/automation/welcome] Error:`, error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
}
