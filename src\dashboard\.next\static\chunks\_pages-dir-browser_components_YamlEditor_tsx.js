"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_YamlEditor_tsx"],{

/***/ "(pages-dir-browser)/./components/YamlEditor.tsx":
/*!***********************************!*\
  !*** ./components/YamlEditor.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_4601d62a7852177fac9c88e9f353f581/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-browser)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\n// Ace editor is not SSR-friendly, so we load it dynamically.\nconst AceEditor = next_dynamic__WEBPACK_IMPORTED_MODULE_2___default()(async ()=>{\n    const ace = await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_pnpm_react-ace_11_0_1_react-dom_19_1_0_react_19_1_0__react_19-f0b51d\").then(__webpack_require__.bind(__webpack_require__, /*! react-ace */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-ace@11.0.1_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/react-ace/lib/index.js\"));\n    // Import required modes, themes, and extensions\n    await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-5fc50a\").then(__webpack_require__.t.bind(__webpack_require__, /*! ace-builds/src-noconflict/mode-yaml */ \"(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/mode-yaml.js\", 23));\n    await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-0a6f04\").then(__webpack_require__.t.bind(__webpack_require__, /*! ace-builds/src-noconflict/theme-twilight */ \"(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/theme-twilight.js\", 23));\n    await __webpack_require__.e(/*! import() */ \"_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-749589\").then(__webpack_require__.t.bind(__webpack_require__, /*! ace-builds/src-noconflict/ext-language_tools */ \"(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/ext-language_tools.js\", 23)); // For autocompletion\n    return ace;\n}, {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\YamlEditor.tsx -> \" + \"ace-builds/src-noconflict/ext-language_tools\"\n        ]\n    },\n    ssr: false\n});\n_c = AceEditor;\nconst YamlEditor = (param)=>{\n    let { value, onChange, height = '60vh' } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n        borderWidth: \"1px\",\n        borderColor: \"purple.600\",\n        borderRadius: \"md\",\n        overflow: \"hidden\",\n        height: height,\n        sx: {\n            '.ace_editor': {\n                fontFamily: \"'Fira Code', 'Consolas', 'Monaco', 'monospace' !important\",\n                fontSize: '14px !important',\n                lineHeight: '1.6 !important'\n            },\n            '.ace_gutter': {\n                background: '#232323'\n            },\n            '.ace_scroller': {\n                backgroundColor: '#1e1e1e'\n            }\n        },\n        children:  true && AceEditor && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AceEditor, {\n            mode: \"yaml\",\n            theme: \"twilight\",\n            onChange: onChange,\n            value: value,\n            name: \"YAML_EDITOR\",\n            editorProps: {\n                $blockScrolling: true\n            },\n            width: \"100%\",\n            height: \"100%\",\n            setOptions: {\n                enableBasicAutocompletion: true,\n                enableLiveAutocompletion: true,\n                enableSnippets: true,\n                showLineNumbers: true,\n                tabSize: 2,\n                useWorker: false,\n                showPrintMargin: false\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\YamlEditor.tsx\",\n            lineNumber: 47,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\YamlEditor.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, undefined);\n};\n_c1 = YamlEditor;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (YamlEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"AceEditor\");\n$RefreshReg$(_c1, \"YamlEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS8uL2NvbXBvbmVudHMvWWFtbEVkaXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQTBCO0FBQ2E7QUFDSjtBQUVuQyw2REFBNkQ7QUFDN0QsTUFBTUcsWUFBWUQsbURBQU9BLENBQ3ZCO0lBQ0UsTUFBTUUsTUFBTSxNQUFNLG1XQUFtQjtJQUNyQyxnREFBZ0Q7SUFDaEQsTUFBTSx3V0FBNkM7SUFDbkQsTUFBTSxrWEFBa0Q7SUFDeEQsTUFBTSwwWEFBc0QsRUFBRSxxQkFBcUI7SUFDbkYsT0FBT0E7QUFDVDs7Ozs7O0lBQ0VDLEtBQUs7O0tBVEhGO0FBa0JOLE1BQU1HLGFBQXdDO1FBQUMsRUFBRUMsS0FBSyxFQUFFQyxRQUFRLEVBQUVDLFNBQVMsTUFBTSxFQUFFO0lBQ2pGLHFCQUNFLDhEQUFDUixpREFBR0E7UUFDRlMsYUFBWTtRQUNaQyxhQUFZO1FBQ1pDLGNBQWE7UUFDYkMsVUFBUztRQUNUSixRQUFRQTtRQUNSSyxJQUFJO1lBQ0YsZUFBZTtnQkFDYkMsWUFBWTtnQkFDWkMsVUFBVTtnQkFDVkMsWUFBWTtZQUNkO1lBQ0EsZUFBZTtnQkFDYkMsWUFBWTtZQUNkO1lBQ0EsaUJBQWlCO2dCQUNmQyxpQkFBaUI7WUFDbkI7UUFDRjtrQkFFQyxLQUE2QixJQUFJaEIsMkJBQ2hDLDhEQUFDQTtZQUNDaUIsTUFBSztZQUNMQyxPQUFNO1lBQ05iLFVBQVVBO1lBQ1ZELE9BQU9BO1lBQ1BlLE1BQUs7WUFDTEMsYUFBYTtnQkFBRUMsaUJBQWlCO1lBQUs7WUFDckNDLE9BQU07WUFDTmhCLFFBQU87WUFDUGlCLFlBQVk7Z0JBQ1ZDLDJCQUEyQjtnQkFDM0JDLDBCQUEwQjtnQkFDMUJDLGdCQUFnQjtnQkFDaEJDLGlCQUFpQjtnQkFDakJDLFNBQVM7Z0JBQ1RDLFdBQVc7Z0JBQ1hDLGlCQUFpQjtZQUNuQjs7Ozs7Ozs7Ozs7QUFLVjtNQTdDTTNCO0FBK0NOLGlFQUFlQSxVQUFVQSxFQUFDIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxzcmNcXGRhc2hib2FyZFxcY29tcG9uZW50c1xcWWFtbEVkaXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgQm94IH0gZnJvbSAnQGNoYWtyYS11aS9yZWFjdCc7XHJcbmltcG9ydCBkeW5hbWljIGZyb20gJ25leHQvZHluYW1pYyc7XHJcblxyXG4vLyBBY2UgZWRpdG9yIGlzIG5vdCBTU1ItZnJpZW5kbHksIHNvIHdlIGxvYWQgaXQgZHluYW1pY2FsbHkuXHJcbmNvbnN0IEFjZUVkaXRvciA9IGR5bmFtaWMoXHJcbiAgYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc3QgYWNlID0gYXdhaXQgaW1wb3J0KCdyZWFjdC1hY2UnKTtcclxuICAgIC8vIEltcG9ydCByZXF1aXJlZCBtb2RlcywgdGhlbWVzLCBhbmQgZXh0ZW5zaW9uc1xyXG4gICAgYXdhaXQgaW1wb3J0KCdhY2UtYnVpbGRzL3NyYy1ub2NvbmZsaWN0L21vZGUteWFtbCcpO1xyXG4gICAgYXdhaXQgaW1wb3J0KCdhY2UtYnVpbGRzL3NyYy1ub2NvbmZsaWN0L3RoZW1lLXR3aWxpZ2h0Jyk7XHJcbiAgICBhd2FpdCBpbXBvcnQoJ2FjZS1idWlsZHMvc3JjLW5vY29uZmxpY3QvZXh0LWxhbmd1YWdlX3Rvb2xzJyk7IC8vIEZvciBhdXRvY29tcGxldGlvblxyXG4gICAgcmV0dXJuIGFjZTtcclxuICB9LFxyXG4gIHsgc3NyOiBmYWxzZSB9XHJcbik7XHJcblxyXG5pbnRlcmZhY2UgWWFtbEVkaXRvclByb3BzIHtcclxuICB2YWx1ZTogc3RyaW5nO1xyXG4gIG9uQ2hhbmdlOiAodmFsdWU6IHN0cmluZykgPT4gdm9pZDsgLy8gQWNlIG9uQ2hhbmdlIHByb3ZpZGVzIHN0cmluZyBkaXJlY3RseVxyXG4gIGhlaWdodD86IHN0cmluZyB8IG51bWJlcjtcclxufVxyXG5cclxuY29uc3QgWWFtbEVkaXRvcjogUmVhY3QuRkM8WWFtbEVkaXRvclByb3BzPiA9ICh7IHZhbHVlLCBvbkNoYW5nZSwgaGVpZ2h0ID0gJzYwdmgnIH0pID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPEJveFxyXG4gICAgICBib3JkZXJXaWR0aD1cIjFweFwiXHJcbiAgICAgIGJvcmRlckNvbG9yPVwicHVycGxlLjYwMFwiXHJcbiAgICAgIGJvcmRlclJhZGl1cz1cIm1kXCJcclxuICAgICAgb3ZlcmZsb3c9XCJoaWRkZW5cIlxyXG4gICAgICBoZWlnaHQ9e2hlaWdodH1cclxuICAgICAgc3g9e3tcclxuICAgICAgICAnLmFjZV9lZGl0b3InOiB7XHJcbiAgICAgICAgICBmb250RmFtaWx5OiBcIidGaXJhIENvZGUnLCAnQ29uc29sYXMnLCAnTW9uYWNvJywgJ21vbm9zcGFjZScgIWltcG9ydGFudFwiLFxyXG4gICAgICAgICAgZm9udFNpemU6ICcxNHB4ICFpbXBvcnRhbnQnLFxyXG4gICAgICAgICAgbGluZUhlaWdodDogJzEuNiAhaW1wb3J0YW50JyxcclxuICAgICAgICB9LFxyXG4gICAgICAgICcuYWNlX2d1dHRlcic6IHtcclxuICAgICAgICAgIGJhY2tncm91bmQ6ICcjMjMyMzIzJywgLy8gTWF0Y2ggdHdpbGlnaHQgdGhlbWUgYmFja2dyb3VuZFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgJy5hY2Vfc2Nyb2xsZXInOiB7XHJcbiAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6ICcjMWUxZTFlJywgLy8gTWF0Y2ggdHdpbGlnaHQgdGhlbWUgYmFja2dyb3VuZFxyXG4gICAgICAgIH0sXHJcbiAgICAgIH19XHJcbiAgICA+XHJcbiAgICAgIHt0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiBBY2VFZGl0b3IgJiYgKFxyXG4gICAgICAgIDxBY2VFZGl0b3JcclxuICAgICAgICAgIG1vZGU9XCJ5YW1sXCJcclxuICAgICAgICAgIHRoZW1lPVwidHdpbGlnaHRcIlxyXG4gICAgICAgICAgb25DaGFuZ2U9e29uQ2hhbmdlfVxyXG4gICAgICAgICAgdmFsdWU9e3ZhbHVlfVxyXG4gICAgICAgICAgbmFtZT1cIllBTUxfRURJVE9SXCJcclxuICAgICAgICAgIGVkaXRvclByb3BzPXt7ICRibG9ja1Njcm9sbGluZzogdHJ1ZSB9fVxyXG4gICAgICAgICAgd2lkdGg9XCIxMDAlXCJcclxuICAgICAgICAgIGhlaWdodD1cIjEwMCVcIlxyXG4gICAgICAgICAgc2V0T3B0aW9ucz17e1xyXG4gICAgICAgICAgICBlbmFibGVCYXNpY0F1dG9jb21wbGV0aW9uOiB0cnVlLFxyXG4gICAgICAgICAgICBlbmFibGVMaXZlQXV0b2NvbXBsZXRpb246IHRydWUsXHJcbiAgICAgICAgICAgIGVuYWJsZVNuaXBwZXRzOiB0cnVlLFxyXG4gICAgICAgICAgICBzaG93TGluZU51bWJlcnM6IHRydWUsXHJcbiAgICAgICAgICAgIHRhYlNpemU6IDIsXHJcbiAgICAgICAgICAgIHVzZVdvcmtlcjogZmFsc2UsIC8vIERpc2FibGUgd29ya2VyIHRvIGF2b2lkIGlzc3VlcyBpbiBzb21lIGVudmlyb25tZW50c1xyXG4gICAgICAgICAgICBzaG93UHJpbnRNYXJnaW46IGZhbHNlLFxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG4gICAgPC9Cb3g+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IFlhbWxFZGl0b3I7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkJveCIsImR5bmFtaWMiLCJBY2VFZGl0b3IiLCJhY2UiLCJzc3IiLCJZYW1sRWRpdG9yIiwidmFsdWUiLCJvbkNoYW5nZSIsImhlaWdodCIsImJvcmRlcldpZHRoIiwiYm9yZGVyQ29sb3IiLCJib3JkZXJSYWRpdXMiLCJvdmVyZmxvdyIsInN4IiwiZm9udEZhbWlseSIsImZvbnRTaXplIiwibGluZUhlaWdodCIsImJhY2tncm91bmQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJtb2RlIiwidGhlbWUiLCJuYW1lIiwiZWRpdG9yUHJvcHMiLCIkYmxvY2tTY3JvbGxpbmciLCJ3aWR0aCIsInNldE9wdGlvbnMiLCJlbmFibGVCYXNpY0F1dG9jb21wbGV0aW9uIiwiZW5hYmxlTGl2ZUF1dG9jb21wbGV0aW9uIiwiZW5hYmxlU25pcHBldHMiLCJzaG93TGluZU51bWJlcnMiLCJ0YWJTaXplIiwidXNlV29ya2VyIiwic2hvd1ByaW50TWFyZ2luIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/YamlEditor.tsx\n"));

/***/ })

}]);