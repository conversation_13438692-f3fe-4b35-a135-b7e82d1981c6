// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';
import { TIMEZONES } from '../../../config/timezones';

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

// Default application config
const defaultConfig = {
  isOpen: true,
  type: 'applications',
  questions: [
    {
      id: 'discord_username',
      type: 'text',
      label: 'What is your Discord username?',
      required: true,
    },
    {
      id: 'age',
      type: 'number',
      label: 'How old are you?',
      required: true,
    },
    {
      id: 'timezone',
      type: 'select',
      label: 'What timezone are you in?',
      required: true,
      options: TIMEZONES.map(tz => tz.label),
    },
    {
      id: 'hours_per_week',
      type: 'text',
      label: 'How many hours per week can you dedicate to moderation?',
      required: true,
    },
    {
      id: 'why_moderator',
      type: 'textarea',
      label: 'Why do you want to be a moderator?',
      required: true,
    },
    {
      id: 'moderation_experience',
      type: 'textarea',
      label: 'Do you have any previous moderation experience?',
      required: false,
    },
    {
      id: 'heated_argument',
      type: 'textarea',
      label: 'How would you handle a heated argument between two members?',
      required: true,
    },
  ],
  scenarios: [],
  open: true // Whether applications are currently being accepted
};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (req.method === 'GET') {
    let client;
    try {
      client = await connectToDatabase();
      const db = client.db(dbName);
      let config = await db.collection('config').findOne({ type: 'applications' });
      
      // If no config exists, use default
      if (!config) {
        config = defaultConfig;
      } else {
        // Merge with default config to ensure all fields exist
        config = {
          ...defaultConfig,
          ...config,
          // Ensure scenarios and questions exist
          scenarios: defaultConfig.scenarios,
          questions: defaultConfig.questions
        };

        // Ensure both isOpen and open fields are present and synchronized
        if (typeof config.isOpen === 'boolean' && config.open !== config.isOpen) {
          await db.collection('config').updateOne(
            { type: 'applications' },
            { $set: { open: config.isOpen } }
          );
          config.open = config.isOpen;
        } else if (typeof config.open === 'boolean' && config.isOpen !== config.open) {
          await db.collection('config').updateOne(
            { type: 'applications' },
            { $set: { isOpen: config.open } }
          );
          config.isOpen = config.open;
        }
      }

      return res.status(200).json(config);
    } catch (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  if (req.method === 'POST') {
    const { isOpen } = req.body;

    if (typeof isOpen !== 'boolean') {
      return res.status(400).json({ error: 'Missing or invalid isOpen field' });
    }

    let client;
    try {
      client = await connectToDatabase();
      const db = client.db(dbName);

      // Get existing config or use default
      let existingConfig = await db.collection('config').findOne({ type: 'applications' }) || defaultConfig;
      
      // Update both isOpen and open fields while preserving other config
      // Ensure scenarios and questions are always from defaultConfig
      await db.collection('config').updateOne(
        { type: 'applications' },
        { 
          $set: { 
            ...existingConfig,
            type: 'applications',
            isOpen,
            open: isOpen,
            scenarios: [],
            questions: defaultConfig.questions
          }
        },
        { upsert: true }
      );

      const updatedConfig = await db.collection('config').findOne({ type: 'applications' });
      return res.status(200).json(updatedConfig);
    } catch (error) {
      console.error('Database error:', error);
      return res.status(500).json({ error: 'Internal server error' });
    }
  }

  res.setHeader('Allow', ['GET', 'POST']);
  return res.status(405).json({ error: 'Method not allowed' });
} 