import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ModalBody,
  ModalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  NumberInput,
  NumberInputField,
  useToast,
  VStack,
  Text,
  Box,
  List,
  ListItem,
  Flex,
  Spinner,
  Switch,
  HStack
} from '@chakra-ui/react';

interface GameServerDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (server: GameServer) => Promise<void>;
  server?: GameServer;
}

interface GameServer {
  _id?: string;
  name: string;
  description?: string;
  host: string;
  port: number;
  type: string;
  hasPassword?: boolean;
  password?: string;
}

interface Game {
  id: string;
  name: string;
}

export function GameServerDialog({ isOpen, onClose, onSave, server }: GameServerDialogProps) {
  const [name, setName] = useState(server?.name || '');
  const [description, setDescription] = useState(server?.description || '');
  const [host, setHost] = useState(server?.host || '');
  const [port, setPort] = useState(server?.port || 25565);
  const [type, setType] = useState(server?.type || '');
  const [hasPassword, setHasPassword] = useState(server?.hasPassword || false);
  const [password, setPassword] = useState(server?.password || '');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Game[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const toast = useToast();

  useEffect(() => {
    if (server) {
      setName(server.name || '');
      setDescription(server.description || '');
      setHost(server.host);
      setPort(server.port);
      setType(server.type);
      setHasPassword(server.hasPassword || false);
      setPassword(server.password || '');
      setSearchQuery('');
    }
  }, [server]);

  useEffect(() => {
    if (searchQuery) {
      searchGames();
    } else {
      setSearchResults([]);
      setShowResults(false);
    }
  }, [searchQuery]);

  const searchGames = async () => {
    try {
      setIsSearching(true);
      const response = await fetch(`/api/gameservers/games?search=${encodeURIComponent(searchQuery)}`);
      if (!response.ok) {
        throw new Error('Failed to search games');
      }
      const results = await response.json();
      setSearchResults(results || []);
      setShowResults(true);
    } catch (error) {
      console.error('Error searching games:', error);
      // Set searchResults to empty array to prevent map() error
      setSearchResults([]);
      setShowResults(false);
      toast({
        title: 'Error',
        description: 'Failed to search games',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleSubmit = async () => {
    if (!name || !host || !port || !type) {
      toast({
        title: 'Error',
        description: 'Please fill in all required fields',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    if (hasPassword && !password) {
      toast({
        title: 'Error',
        description: 'Password is required when password protection is enabled',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
      return;
    }

    try {
      // Create the server data object
      const serverData = {
        _id: server?._id,
        name,
        description,
        host,
        port: Number(port),
        type,
        hasPassword,
        password: hasPassword ? password : undefined
      };

      // Save the server first
      await onSave(serverData);
      onClose();
    } catch (error) {
      console.error('Error saving server:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save server',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const selectGame = (game: Game) => {
    setType(game.id);
    setSearchQuery(game.name);
    setShowResults(false);
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="lg">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent bg="gray.800" border="1px" borderColor="whiteAlpha.200">
        <ModalHeader color="white">{server ? 'Edit Server' : 'Add Server'}</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={6}>
            <FormControl isRequired>
              <FormLabel color="gray.200">Server Name</FormLabel>
              <Input
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="My Game Server"
                bg="gray.700"
                border="1px"
                borderColor="whiteAlpha.300"
                _hover={{ borderColor: "whiteAlpha.400" }}
                _focus={{ borderColor: "blue.300", boxShadow: "0 0 0 1px var(--chakra-colors-blue-300)" }}
              />
            </FormControl>

            <FormControl>
              <FormLabel color="gray.200">Description</FormLabel>
              <Input
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Optional description"
                bg="gray.700"
                border="1px"
                borderColor="whiteAlpha.300"
                _hover={{ borderColor: "whiteAlpha.400" }}
                _focus={{ borderColor: "blue.300", boxShadow: "0 0 0 1px var(--chakra-colors-blue-300)" }}
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel color="gray.200">Game Type</FormLabel>
              <Input
                value={searchQuery}
                onChange={(e) => {
                  setSearchQuery(e.target.value);
                  setType('');
                }}
                placeholder="Search for a game..."
                bg="gray.700"
                border="1px"
                borderColor="whiteAlpha.300"
                _hover={{ borderColor: "whiteAlpha.400" }}
                _focus={{ borderColor: "blue.300", boxShadow: "0 0 0 1px var(--chakra-colors-blue-300)" }}
                onFocus={() => setShowResults(true)}
              />
              {isSearching && (
                <Flex justify="center" mt={2}>
                  <Spinner size="sm" color="blue.300" />
                </Flex>
              )}
              {showResults && searchResults.length > 0 && (
                <Box
                  mt={2}
                  border="1px"
                  borderColor="whiteAlpha.200"
                  borderRadius="md"
                  maxH="200px"
                  overflowY="auto"
                  bg="gray.700"
                >
                  <List spacing={0}>
                    {(searchResults || []).map((game) => (
                      <ListItem
                        key={game.id}
                        p={2}
                        cursor="pointer"
                        _hover={{ bg: "whiteAlpha.100" }}
                        onClick={() => selectGame(game)}
                        color="gray.200"
                      >
                        <Text>{game.name}</Text>
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
              {type && (
                <Text mt={1} fontSize="sm" color="blue.300">
                  Selected: {searchResults.find(g => g.id === type)?.name || type}
                </Text>
              )}
            </FormControl>

            <FormControl isRequired>
              <FormLabel color="gray.200">Host</FormLabel>
              <Input
                value={host}
                onChange={(e) => setHost(e.target.value)}
                placeholder="localhost or IP address"
                bg="gray.700"
                border="1px"
                borderColor="whiteAlpha.300"
                _hover={{ borderColor: "whiteAlpha.400" }}
                _focus={{ borderColor: "blue.300", boxShadow: "0 0 0 1px var(--chakra-colors-blue-300)" }}
              />
            </FormControl>

            <FormControl isRequired>
              <FormLabel color="gray.200">Port</FormLabel>
              <NumberInput value={port} min={1} max={65535}>
                <NumberInputField
                  onChange={(e) => setPort(parseInt(e.target.value) || 25565)}
                  bg="gray.700"
                  border="1px"
                  borderColor="whiteAlpha.300"
                  _hover={{ borderColor: "whiteAlpha.400" }}
                  _focus={{ borderColor: "blue.300", boxShadow: "0 0 0 1px var(--chakra-colors-blue-300)" }}
                />
              </NumberInput>
            </FormControl>

            <FormControl>
              <HStack>
                <FormLabel color="gray.200" mb="0">Password Protected</FormLabel>
                <Switch
                  colorScheme="blue"
                  isChecked={hasPassword}
                  onChange={(e) => setHasPassword(e.target.checked)}
                />
              </HStack>
            </FormControl>

            {hasPassword && (
              <FormControl isRequired>
                <FormLabel color="gray.200">Server Password</FormLabel>
                <Input
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Enter server password"
                  bg="gray.700"
                  border="1px"
                  borderColor="whiteAlpha.300"
                  _hover={{ borderColor: "whiteAlpha.400" }}
                  _focus={{ borderColor: "blue.300", boxShadow: "0 0 0 1px var(--chakra-colors-blue-300)" }}
                />
              </FormControl>
            )}
          </VStack>
        </ModalBody>

        <ModalFooter gap={3}>
          <Button variant="ghost" onClick={onClose} color="gray.300" _hover={{ bg: "whiteAlpha.100" }}>
            Cancel
          </Button>
          <Button 
            colorScheme="blue"
            onClick={handleSubmit}
            _hover={{ bg: "blue.500" }}
            _active={{ bg: "blue.600" }}
          >
            {server ? 'Save Changes' : 'Add Server'}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 