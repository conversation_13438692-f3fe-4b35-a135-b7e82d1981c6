"use strict";(()=>{var e={};e.id=8696,e.ids=[8696],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},1331:(e,t,o)=>{o.r(t),o.d(t,{config:()=>p,default:()=>l,routeModule:()=>u});var a={};o.r(a),o.d(a,{default:()=>m});var i=o(3433),n=o(264),s=o(584),d=o(5806),r=o(8525);let c=[{id:"ping-command",name:"Ping Command",description:"Simple ping command that responds with latency",category:"Basic",thumbnail:"\uD83C\uDFD3",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"command-1",type:"command",position:{x:300,y:180},data:{label:"Ping Command",commandName:"ping",description:"Shows bot latency and status"}},{id:"action-1",type:"action",position:{x:300,y:310},data:{label:"Send Response",actionType:"sendEmbed",message:"Pong! \uD83C\uDFD3\nLatency: {ping}ms"}}],edges:[{id:"e1-2",source:"trigger-1",target:"command-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"command-1",target:"action-1",type:"smoothstep",animated:!0}]},{id:"welcome-system",name:"Welcome System",description:"Welcomes new members with a message",category:"Events",thumbnail:"\uD83D\uDC4B",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"event-1",type:"event",position:{x:300,y:180},data:{label:"Member Joined",eventType:"guildMemberAdd"}},{id:"action-1",type:"action",position:{x:300,y:310},data:{label:"Welcome Message",actionType:"sendMessage",message:"Welcome to the server, {user}! \uD83C\uDF89",channel:"welcome"}}],edges:[{id:"e1-2",source:"trigger-1",target:"event-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"event-1",target:"action-1",type:"smoothstep",animated:!0}]},{id:"moderation-kick",name:"Moderation Kick",description:"Kick command with permission check",category:"Moderation",thumbnail:"\uD83E\uDD7E",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"command-1",type:"command",position:{x:300,y:180},data:{label:"Kick Command",commandName:"kick",description:"Kick a member from the server"}},{id:"condition-1",type:"condition",position:{x:300,y:310},data:{label:"Permission Check",conditionType:"userPermission",operator:"equals",value:"KICK_MEMBERS"}},{id:"action-1",type:"action",position:{x:150,y:450},data:{label:"Kick User",actionType:"kickUser",message:"User {user} has been kicked from the server."}},{id:"action-2",type:"action",position:{x:450,y:450},data:{label:"No Permission",actionType:"sendMessage",message:"You do not have permission to kick members."}}],edges:[{id:"e1-2",source:"trigger-1",target:"command-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"command-1",target:"condition-1",type:"smoothstep",animated:!0},{id:"e3-4",source:"condition-1",target:"action-1",sourceHandle:"true",type:"smoothstep",animated:!0},{id:"e3-5",source:"condition-1",target:"action-2",sourceHandle:"false",type:"smoothstep",animated:!0}]},{id:"reaction-roles",name:"Reaction Roles",description:"Give roles based on message reactions",category:"Utility",thumbnail:"\uD83C\uDFAD",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"event-1",type:"event",position:{x:300,y:180},data:{label:"Reaction Added",eventType:"messageReactionAdd"}},{id:"condition-1",type:"condition",position:{x:300,y:310},data:{label:"Check Emoji",conditionType:"reactionEmoji",operator:"equals",value:"✅"}},{id:"action-1",type:"action",position:{x:150,y:450},data:{label:"Add Role",actionType:"addRole",role:"Member",message:"Added {role} role to {user}!"}},{id:"action-2",type:"action",position:{x:450,y:450},data:{label:"Wrong Reaction",actionType:"sendMessage",message:"That emoji is not configured for reaction roles."}}],edges:[{id:"e1-2",source:"trigger-1",target:"event-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"event-1",target:"condition-1",type:"smoothstep",animated:!0},{id:"e3-4",source:"condition-1",target:"action-1",sourceHandle:"true",type:"smoothstep",animated:!0},{id:"e3-5",source:"condition-1",target:"action-2",sourceHandle:"false",type:"smoothstep",animated:!0}]},{id:"auto-moderation",name:"Auto Moderation",description:"Automatically delete messages with bad words",category:"Moderation",thumbnail:"\uD83D\uDEE1️",nodes:[{id:"trigger-1",type:"trigger",position:{x:300,y:50},data:{label:"Start"}},{id:"event-1",type:"event",position:{x:300,y:180},data:{label:"Message Created",eventType:"messageCreate"}},{id:"condition-1",type:"condition",position:{x:300,y:310},data:{label:"Contains Bad Word",conditionType:"messageContains",operator:"contains",value:"badword"}},{id:"action-1",type:"action",position:{x:150,y:450},data:{label:"Delete Message",actionType:"deleteMessage",message:"Message deleted for containing inappropriate content."}},{id:"action-2",type:"action",position:{x:450,y:450},data:{label:"Allow Message",actionType:"doNothing",message:"Message is clean - no action needed"}}],edges:[{id:"e1-2",source:"trigger-1",target:"event-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"event-1",target:"condition-1",type:"smoothstep",animated:!0},{id:"e3-4",source:"condition-1",target:"action-1",sourceHandle:"true",type:"smoothstep",animated:!0},{id:"e3-5",source:"condition-1",target:"action-2",sourceHandle:"false",type:"smoothstep",animated:!0}]},{id:"custom-embed",name:"Custom Embed Command",description:"Create beautiful embed messages",category:"Utility",thumbnail:"\uD83D\uDCCB",nodes:[{id:"trigger-1",type:"trigger",position:{x:100,y:100},data:{label:"Start"}},{id:"command-1",type:"command",position:{x:100,y:200},data:{label:"Embed Command",commandName:"embed",description:"Create a custom embed message"}},{id:"action-1",type:"action",position:{x:100,y:300},data:{label:"Send Embed",actionType:"sendEmbed",message:"This is a custom embed!\n\nYou can customize:\n• Title\n• Description\n• Color\n• Fields\n• And more!"}}],edges:[{id:"e1-2",source:"trigger-1",target:"command-1",type:"smoothstep",animated:!0},{id:"e2-3",source:"command-1",target:"action-1",type:"smoothstep",animated:!0}]}];async function m(e,t){if("GET"!==e.method)return t.status(405).json({message:"Method not allowed"});try{let o=await (0,d.getServerSession)(e,t,r.authOptions);if(!o||o.user?.id!=="933023999770918932")return t.status(403).json({message:"Unauthorized"});let{category:a}=e.query,i=c;return a&&"string"==typeof a&&(i=c.filter(e=>e.category.toLowerCase()===a.toLowerCase())),t.status(200).json({templates:i,categories:Array.from(new Set(c.map(e=>e.category)))})}catch(e){return t.status(500).json({message:"Internal server error"})}}let l=(0,s.M)(a,"default"),p=(0,s.M)(a,"config"),u=new i.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/experimental/addon-builder/templates",pathname:"/api/admin/experimental/addon-builder/templates",bundlePath:"",filename:""},userland:a})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>r,default:()=>c});var a=o(5542),i=o.n(a);let n=require("next-auth/providers/discord");var s=o.n(n),d=o(8580);let r={providers:[s()({clientId:d.dashboardConfig.bot.clientId,clientSecret:d.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let i=!1;if(o)if((d.dashboardConfig.dashboard.admins||[]).includes(o))i=!0;else{let e=d.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&d.dashboardConfig.bot.token&&d.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${d.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${d.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();i=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:d.dashboardConfig.dashboard.session.secret||d.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},c=i()(r)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>r,default:()=>c});var a=o(9021),i=o(2115),n=o.n(i),s=o(3873);let d={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>s.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=s.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");d=n().parse(t)}catch(e){process.exit(1)}let r={bot:{token:d.bot.token,clientId:d.bot.clientId,clientSecret:d.bot.clientSecret,guildId:d.bot.guildId,ticketCategoryId:d.bot.ticketCategoryId||null,ticketLogChannelId:d.bot.ticketLogChannelId||null,prefix:d.bot.prefix},dashboard:{admins:d.dashboard?.admins||[],adminRoleIds:d.dashboard?.adminRoleIds||[],session:{secret:d.dashboard?.session?.secret||d.bot.clientSecret}},database:{url:d.database.url,name:d.database.name,options:{maxPoolSize:d.database.options?.maxPoolSize||10,minPoolSize:d.database.options?.minPoolSize||1,maxIdleTimeMS:d.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:d.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:d.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:d.database.options?.connectTimeoutMS||1e4,retryWrites:d.database.options?.retryWrites!==!1,retryReads:d.database.options?.retryReads!==!1}}};r.bot.token||process.exit(1),r.bot.clientId&&r.bot.clientSecret||process.exit(1),r.bot.guildId||process.exit(1),r.database.url&&r.database.name||process.exit(1);let c=r},9021:e=>{e.exports=require("fs")}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=1331);module.exports=o})();