import { ButtonInteraction, ActionRowBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, ChannelType, MessageFlags } from 'discord.js';
import { ChannelUtils } from '../utils/channelUtils.js';
import { Logger } from '../../../core/Logger.js';

export async function handleVCButtonInteraction(interaction: ButtonInteraction) {
  const logger = Logger.createAddonLogger('tempvc');
  
  // Special handling for password entry button (from waiting room)
  if (interaction.customId.startsWith('vc_enter_password:')) {
    await handleEnterPasswordButton(interaction);
    return;
  }
  
  // Check if user is in a voice channel
  const member = interaction.guild?.members.cache.get(interaction.user.id);
  const voiceChannel = member?.voice.channel;

  if (!voiceChannel || voiceChannel.type !== ChannelType.GuildVoice) {
    await interaction.reply({
      content: '❌ You must be in a voice channel to use this!',
      ephemeral: true
    });
    return;
  }

  // Check if user is the channel owner
  if (!ChannelUtils.isChannelOwner(voiceChannel.id, interaction.user.id)) {
    await interaction.reply({
      content: '❌ Only the channel owner can use these controls!',
      ephemeral: true
    });
    return;
  }

  const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
  if (!tempChannelData) {
    await interaction.reply({
      content: '❌ This is not a temporary voice channel!',
      ephemeral: true
    });
    return;
  }

  try {
    switch (interaction.customId) {
      case 'vc_lock':
        await handleLockToggle(interaction, voiceChannel);
        break;
      case 'vc_hide':
        await handleHideToggle(interaction, voiceChannel);
        break;
      case 'vc_password':
        await handlePasswordModal(interaction);
        break;
      case 'vc_settings':
        await handleSettingsModal(interaction, voiceChannel);
        break;
      case 'vc_access':
        await handleAccessModal(interaction, voiceChannel);
        break;
      case 'vc_block_user':
        await handleBlockUserButton(interaction);
        break;
      case 'vc_unblock_user':
        await handleUnblockUserButton(interaction, voiceChannel);
        break;
      case 'vc_kick_user':
        await handleKickUserButton(interaction, voiceChannel);
        break;
      case 'vc_regions':
        await handleRegionsButton(interaction, voiceChannel);
        break;
      default:
        await interaction.reply({
          content: '❌ Unknown button interaction!',
          ephemeral: true
        });
    }
  } catch (error) {
    logger.error('Button interaction error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      customId: interaction.customId,
      user: interaction.user.tag
    });
    
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: '❌ An error occurred while processing your request!',
        ephemeral: true
      });
    }
  }
}

async function handleLockToggle(interaction: ButtonInteraction, voiceChannel: any) {
  const success = await ChannelUtils.toggleLock(voiceChannel);

  if (success) {
    const updatedData = ChannelUtils.getTempChannel(voiceChannel.id);
    
    const confirmationContainer = {
      type: 17, // Container
      accent_color: updatedData?.settings.locked ? 0xFF0000 : 0x00FF00, // Red for locked, Green for unlocked
      components: [
        {
          type: 10, // Text
          content: `🔒 **Channel ${updatedData?.settings.locked ? 'Locked' : 'Unlocked'} Successfully!**\n${updatedData?.settings.locked ? 'Only you can invite people now' : 'Anyone can join your channel now'}`
        }
      ]
    };
    
    await interaction.reply({
      components: [confirmationContainer],
      flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
    });
    
    // Update the main control panel
    const { updateControlPanel } = await import('../index.js');
    await updateControlPanel(voiceChannel, interaction.user);
    
    // Auto-delete after 3 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        // Message may already be deleted, ignore error
      }
    }, 3000);
  } else {
    await interaction.reply({
      content: '❌ Failed to toggle channel lock!',
      ephemeral: true
    });
  }
}

async function handleHideToggle(interaction: ButtonInteraction, voiceChannel: any) {
  const success = await ChannelUtils.toggleHide(voiceChannel);

  if (success) {
    const updatedData = ChannelUtils.getTempChannel(voiceChannel.id);
    
    const confirmationContainer = {
      type: 17, // Container
      accent_color: updatedData?.settings.hidden ? 0x6A0DAD : 0x00BFFF, // Purple for hidden, Blue for visible
      components: [
        {
          type: 10, // Text
          content: `👁️ **Channel ${updatedData?.settings.hidden ? 'Hidden' : 'Visible'} Successfully!**\n${updatedData?.settings.hidden ? 'Your channel is now hidden from the channel list' : 'Your channel is now visible to everyone'}`
        }
      ]
    };
    
    await interaction.reply({
      components: [confirmationContainer],
      flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
    });
    
    // Update the main control panel
    const { updateControlPanel } = await import('../index.js');
    await updateControlPanel(voiceChannel, interaction.user);
    
    // Auto-delete after 3 seconds
    setTimeout(async () => {
      try {
        await interaction.deleteReply();
      } catch (error) {
        // Message may already be deleted, ignore error
      }
    }, 3000);
  } else {
    await interaction.reply({
      content: '❌ Failed to toggle channel visibility!',
      ephemeral: true
    });
  }
}

async function handlePasswordModal(interaction: ButtonInteraction) {
  const modal = new ModalBuilder()
    .setCustomId('vc_password_modal')
    .setTitle('Set Channel Password');

  const passwordInput = new TextInputBuilder()
    .setCustomId('password_input')
    .setLabel('Enter password (leave empty to remove)')
    .setStyle(TextInputStyle.Short)
    .setMaxLength(50)
    .setRequired(false);

  const firstActionRow = new ActionRowBuilder<TextInputBuilder>().addComponents(passwordInput);
  modal.addComponents(firstActionRow);

  await interaction.showModal(modal);
}

async function handleSettingsModal(interaction: ButtonInteraction, voiceChannel: any) {
  const modal = new ModalBuilder()
    .setCustomId('vc_settings_modal')
    .setTitle('Channel Settings');

  const nameInput = new TextInputBuilder()
    .setCustomId('name_input')
    .setLabel('Channel Name')
    .setStyle(TextInputStyle.Short)
    .setValue(voiceChannel.name)
    .setMaxLength(100)
    .setRequired(true);

  const limitInput = new TextInputBuilder()
    .setCustomId('limit_input')
    .setLabel('User Limit (0 for no limit)')
    .setStyle(TextInputStyle.Short)
    .setValue(voiceChannel.userLimit.toString())
    .setMaxLength(2)
    .setRequired(true);

  const bitrateInput = new TextInputBuilder()
    .setCustomId('bitrate_input')
    .setLabel('Bitrate (8-384 kbps)')
    .setStyle(TextInputStyle.Short)
    .setValue((voiceChannel.bitrate / 1000).toString())
    .setMaxLength(3)
    .setRequired(true);

  modal.addComponents(
    new ActionRowBuilder<TextInputBuilder>().addComponents(nameInput),
    new ActionRowBuilder<TextInputBuilder>().addComponents(limitInput),
    new ActionRowBuilder<TextInputBuilder>().addComponents(bitrateInput)
  );

  await interaction.showModal(modal);
}

async function handleAccessModal(interaction: ButtonInteraction, voiceChannel: any) {
  const blockedUsers = ChannelUtils.getBlockedUsers(voiceChannel.id);
  const totalBlockedUsers = new Set([...blockedUsers.join, ...blockedUsers.view]).size;
  
  const accessContainer = {
    type: 17, // Container
    accent_color: 0xFF6B35, // Orange theme for user access
    components: [
      {
        type: 10, // Text
        content: `# 👥 User Access Control\nManage who can access **${voiceChannel.name}**`
      },
      {
        type: 10, // Text
        content: `🚫 **Currently Blocked:** ${totalBlockedUsers} users\n💡 Block users to prevent them from joining or seeing your channel`
      },
      {
        type: 1, // ActionRow for buttons
        components: [
          {
            type: 2, // Button
            custom_id: 'vc_block_user',
            label: 'Block Users',
            style: 4, // Danger
            emoji: { name: '🚫' }
          },
          {
            type: 2, // Button
            custom_id: 'vc_unblock_user',
            label: 'Unblock Users',
            style: 3, // Success
            emoji: { name: '✅' }
          },
          {
            type: 2, // Button
            custom_id: 'vc_kick_user',
            label: 'Kick Users',
            style: 4, // Danger
            emoji: { name: '🦵' }
          }
        ]
      }
    ]
  };

  await interaction.reply({ 
    components: [accessContainer],
    flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
  });
}

async function handleBlockUserButton(interaction: ButtonInteraction) {
  const member = interaction.guild?.members.cache.get(interaction.user.id);
  const voiceChannel = member?.voice.channel;
  
  if (!voiceChannel) {
    await interaction.reply({
      content: '❌ You must be in a voice channel to use this!',
      ephemeral: true
    });
    return;
  }

  const blockedUsers = ChannelUtils.getBlockedUsers(voiceChannel.id);
  const allBlockedUsers = new Set([...blockedUsers.view, ...blockedUsers.join]);
  
  const blockContainer = {
    type: 17, // Container
    accent_color: 0xFF0000, // Red theme for blocking
    components: [
      {
        type: 10, // Text
        content: `# 🚫 Block Users\nSelect users to block from **${voiceChannel.name}**`
      },
      {
        type: 10, // Text
        content: allBlockedUsers.size > 0 
          ? `📊 **Currently Blocked:** ${allBlockedUsers.size} users\n💡 *Channel owner cannot be blocked*`
          : `📊 **Currently Blocked:** 0 users\n💡 *Channel owner cannot be blocked*`
      },
      {
        type: 1, // ActionRow for user select
        components: [
          {
            type: 5, // UserSelectMenu
            custom_id: 'vc_block_user_select',
            placeholder: 'Select users to block...',
            min_values: 1,
            max_values: 10
          }
        ]
      }
    ]
  };

  await interaction.reply({
    components: [blockContainer],
    flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
  });
}

async function handleUnblockUserButton(interaction: ButtonInteraction, voiceChannel: any) {
  const blockedUsers = ChannelUtils.getBlockedUsers(voiceChannel.id);
  const allBlockedUsers = new Set([...blockedUsers.view, ...blockedUsers.join]);
  
  if (allBlockedUsers.size === 0) {
    const noUsersContainer = {
      type: 17, // Container
      accent_color: 0x808080, // Gray theme
      components: [
        {
          type: 10, // Text
          content: `# ✅ No Blocked Users\nThere are currently no blocked users in **${voiceChannel.name}**`
        }
      ]
    };
    
    await interaction.reply({
      components: [noUsersContainer],
      flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
    });
    return;
  }

  // Create options for blocked users
  const options = [];
  
  for (const userId of allBlockedUsers) {
    try {
      const user = await interaction.guild!.members.fetch(userId);
      options.push({
        label: user.displayName,
        description: `@${user.user.username}`,
        value: userId,
        emoji: { name: '✅' }
      });
    } catch (error) {
      // User not found, add anyway with ID
      options.push({
        label: `Unknown User`,
        description: `ID: ${userId}`,
        value: userId,
        emoji: { name: '❓' }
      });
    }
  }

  if (options.length === 0) {
    const noValidUsersContainer = {
      type: 17, // Container
      accent_color: 0x808080, // Gray theme
      components: [
        {
          type: 10, // Text
          content: `# ❌ No Valid Users\nNo valid blocked users found to unblock`
        }
      ]
    };
    
    await interaction.reply({
      components: [noValidUsersContainer],
      flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
    });
    return;
  }

  const unblockContainer = {
    type: 17, // Container
    accent_color: 0x00FF00, // Green theme for unblocking
    components: [
      {
        type: 10, // Text
        content: `# ✅ Unblock Users\nSelect users to unblock from **${voiceChannel.name}**`
      },
      {
        type: 10, // Text
        content: `📊 **Currently Blocked:** ${allBlockedUsers.size} users\n💡 *Unblocked users will regain access based on channel settings*`
      },
      {
        type: 1, // ActionRow for select menu
        components: [
          {
            type: 3, // StringSelectMenu
            custom_id: 'vc_unblock_user_select',
            placeholder: 'Select users to unblock...',
            min_values: 1,
            max_values: Math.min(options.length, 25), // Discord limit is 25
            options: options
          }
        ]
      }
    ]
  };

  await interaction.reply({
    components: [unblockContainer],
    flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
  });
}

async function handleEnterPasswordButton(interaction: ButtonInteraction) {
  const [, waitingRoomId, mainChannelId] = interaction.customId.split(':');
  
  const modal = new ModalBuilder()
    .setCustomId(`vc_submit_password:${waitingRoomId}:${mainChannelId}`)
    .setTitle('Enter Channel Password');

  const passwordInput = new TextInputBuilder()
    .setCustomId('password')
    .setLabel("What's the secret password?")
    .setStyle(TextInputStyle.Short)
    .setRequired(true);

  const firstActionRow = new ActionRowBuilder<TextInputBuilder>().addComponents(passwordInput);
  modal.addComponents(firstActionRow);

  await interaction.showModal(modal);
}

async function handleKickUserButton(interaction: ButtonInteraction, voiceChannel: any) {
  // Get users currently in the voice channel (excluding the owner)
  const usersInChannel = voiceChannel.members.filter((member: any) => 
    member.id !== interaction.user.id // Exclude the owner
  );
  
  if (usersInChannel.size === 0) {
    const noUsersContainer = {
      type: 17, // Container
      accent_color: 0x808080, // Gray theme
      components: [
        {
          type: 10, // Text
          content: `# 🦵 No Users to Kick\nThere are currently no other users in **${voiceChannel.name}** to kick.`
        }
      ]
    };
    
    await interaction.reply({
      components: [noUsersContainer],
      flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
    });
    return;
  }

  // Create options for users in the voice channel
  const options = [];
  
  for (const [userId, member] of usersInChannel) {
    options.push({
      label: member.displayName,
      description: `@${member.user.username} - Currently in voice`,
      value: userId,
      emoji: { name: '🔊' }
    });
  }

  const kickContainer = {
    type: 17, // Container
    accent_color: 0xFF4500, // Orange-red theme for kicking
    components: [
      {
        type: 10, // Text
        content: `# 🦵 Kick Users\nSelect users to kick from **${voiceChannel.name}**`
      },
      {
        type: 10, // Text
        content: `👥 **Users in Voice:** ${usersInChannel.size}\n⚠️ **Warning:** Kicked users will be locked out for 60 seconds\n💡 *This gives you time to change the password if needed*`
      },
      {
        type: 1, // ActionRow for string select menu
        components: [
          {
            type: 3, // StringSelectMenu
            custom_id: 'vc_kick_user_select',
            placeholder: 'Select users to kick...',
            min_values: 1,
            max_values: Math.min(options.length, 25), // Discord limit is 25
            options: options
          }
        ]
      }
    ]
  };

  await interaction.reply({
    components: [kickContainer],
    flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
  });
}

async function handleRegionsButton(interaction: ButtonInteraction, voiceChannel: any) {
  // Discord voice regions (only valid ones according to API)
  const regions = [
    { name: 'Automatic', value: null, emoji: '🤖', description: 'Let Discord choose the best region' },
    { name: 'US West', value: 'us-west', emoji: '🇺🇸', description: 'United States West Coast' },
    { name: 'US East', value: 'us-east', emoji: '🇺🇸', description: 'United States East Coast' },
    { name: 'US Central', value: 'us-central', emoji: '🇺🇸', description: 'United States Central' },
    { name: 'US South', value: 'us-south', emoji: '🇺🇸', description: 'United States South' },
    { name: 'Rotterdam', value: 'rotterdam', emoji: '🇳🇱', description: 'Netherlands (Europe)' },
    { name: 'Singapore', value: 'singapore', emoji: '🇸🇬', description: 'Singapore (Asia)' },
    { name: 'Sydney', value: 'sydney', emoji: '🇦🇺', description: 'Australia (Oceania)' },
    { name: 'Japan', value: 'japan', emoji: '🇯🇵', description: 'Japan (Asia)' },
    { name: 'Hong Kong', value: 'hongkong', emoji: '🇭🇰', description: 'Hong Kong (Asia)' },
    { name: 'South Korea', value: 'south-korea', emoji: '🇰🇷', description: 'South Korea (Asia)' },
    { name: 'India', value: 'india', emoji: '🇮🇳', description: 'India (Asia)' },
    { name: 'Brazil', value: 'brazil', emoji: '🇧🇷', description: 'Brazil (South America)' },
    { name: 'South Africa', value: 'southafrica', emoji: '🇿🇦', description: 'South Africa' }
  ];

  // Get current region
  const currentRegionDisplay = regions.find(r => r.value === voiceChannel.rtcRegion)?.name || 'Automatic';

  // Create options for the select menu
  const options = regions.map(region => ({
    label: region.name,
    description: region.description,
    value: region.value || 'auto',
    emoji: { name: region.emoji },
    default: (region.value === voiceChannel.rtcRegion) || (region.value === null && !voiceChannel.rtcRegion)
  }));

  const regionsContainer = {
    type: 17, // Container
    accent_color: 0x00CED1, // Dark turquoise theme for regions
    components: [
      {
        type: 10, // Text
        content: `# 🌍 Voice Regions\nSelect a voice region for **${voiceChannel.name}**`
      },
      {
        type: 10, // Text
        content: `📍 **Current Region:** ${currentRegionDisplay}\n💡 *Choosing a region closer to users reduces latency*`
      },
      {
        type: 1, // ActionRow for select menu
        components: [
          {
            type: 3, // StringSelectMenu
            custom_id: 'vc_region_select',
            placeholder: 'Choose a voice region...',
            min_values: 1,
            max_values: 1,
            options: options.slice(0, 25) // Discord limit is 25 options
          }
        ]
      }
    ]
  };

  await interaction.reply({
    components: [regionsContainer],
    flags: MessageFlags.Ephemeral | MessageFlags.IsComponentsV2
  });
}


 