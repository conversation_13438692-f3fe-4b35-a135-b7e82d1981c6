"use strict";(()=>{var e={};e.id=3781,e.ids=[3781],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},795:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>b,routeModule:()=>p});var a={};r.r(a),r.d(a,{createNotification:()=>f,default:()=>c});var o=r(3433),i=r(264),s=r(584),n=r(5806),d=r(8525),l=r(2290),u=r(2518);async function c(e,t){let r=await (0,n.getServerSession)(e,t,d.authOptions);if(!r?.user?.id)return t.status(401).json({error:"Unauthorized"});let a=(await (0,l.L)()).collection("notifications");if("GET"===e.method)try{let e=await a.find({userId:r.user.id}).sort({timestamp:-1}).limit(50).toArray();return t.status(200).json({notifications:e})}catch(e){return t.status(500).json({error:"Failed to fetch notifications"})}if("POST"===e.method)try{let{type:r,title:o,message:i,targetUserId:s,actionUrl:n,relatedId:d}=e.body;if(!r||!o||!i||!s)return t.status(400).json({error:"Missing required fields"});let l={userId:s,type:r,title:o,message:i,timestamp:new Date,read:!1,actionUrl:n,relatedId:d},u=await a.insertOne(l);return t.status(200).json({id:u.insertedId,...l})}catch(e){return t.status(500).json({error:"Failed to create notification"})}if("PATCH"===e.method)try{let{notificationIds:o,markAsRead:i}=e.body;if(!Array.isArray(o))return t.status(400).json({error:"notificationIds must be an array"});return await a.updateMany({_id:{$in:o.map(e=>new u.ObjectId(e))},userId:r.user.id},{$set:{read:!0===i}}),t.status(200).json({success:!0})}catch(e){return t.status(500).json({error:"Failed to update notifications"})}if("DELETE"===e.method)try{let{notificationIds:o}=e.body;if(!Array.isArray(o))return t.status(400).json({error:"notificationIds must be an array"});return await a.deleteMany({_id:{$in:o.map(e=>new u.ObjectId(e))},userId:r.user.id}),t.status(200).json({success:!0})}catch(e){return t.status(500).json({error:"Failed to delete notifications"})}return t.status(405).json({error:"Method not allowed"})}async function f(e,t,r,a,o,i){try{let s=(await (0,l.L)()).collection("notifications"),n={userId:e,type:t,title:r,message:a,timestamp:new Date,read:!1,actionUrl:o,relatedId:i};return{id:(await s.insertOne(n)).insertedId,...n}}catch(e){throw e}}let b=(0,s.M)(a,"default"),m=(0,s.M)(a,"config"),p=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/notifications",pathname:"/api/notifications",bundlePath:"",filename:""},userland:a})},2115:e=>{e.exports=require("yaml")},2290:(e,t,r)=>{r.d(t,{L:()=>n});var a=r(8580),o=r(2518);let i=null,s=null;async function n(){if(s)return s;let e=a.dashboardConfig.database?.url||"mongodb://localhost:27017",t=a.dashboardConfig.database?.name||"discord_bot";return i||(i=await o.MongoClient.connect(e,{...a.dashboardConfig.database?.options||{}})),s=i.db(t)}},2518:e=>{e.exports=require("mongodb")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var a=r(5542),o=r.n(a);let i=require("next-auth/providers/discord");var s=r.n(i),n=r(8580);let d={providers:[s()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,a=t.accessToken||null;e.user.id=r,e.user.accessToken=a;let o=!1;if(r)if((n.dashboardConfig.dashboard.admins||[]).includes(r))o=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();o=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),a=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=o()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var a=r(9021),o=r(2115),i=r.n(o),s=r(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>s.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=s.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");n=i().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../webpack-api-runtime.js");t.C(e);var r=t(t.s=795);module.exports=r})();