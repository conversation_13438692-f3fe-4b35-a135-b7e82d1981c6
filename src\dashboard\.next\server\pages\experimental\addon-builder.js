"use strict";(()=>{var e={};e.id=8963,e.ids=[8963],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},918:(e,t,n)=>{n.a(e,async(e,s)=>{try{n.r(t),n.d(t,{config:()=>p,default:()=>u,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>m,reportWebVitals:()=>g,routeModule:()=>f,unstable_getServerProps:()=>C,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>j});var r=n(1292),o=n(8834),a=n(786),i=n(3567),l=n(8077),c=n(2380),d=e([l,c]);[l,c]=d.then?(await d)():d;let u=(0,a.M)(c,"default"),m=(0,a.M)(c,"getStaticProps"),h=(0,a.M)(c,"getStaticPaths"),x=(0,a.M)(c,"getServerSideProps"),p=(0,a.M)(c,"config"),g=(0,a.M)(c,"reportWebVitals"),j=(0,a.M)(c,"unstable_getStaticProps"),b=(0,a.M)(c,"unstable_getStaticPaths"),v=(0,a.M)(c,"unstable_getStaticParams"),C=(0,a.M)(c,"unstable_getServerProps"),S=(0,a.M)(c,"unstable_getServerSideProps"),f=new r.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/experimental/addon-builder",pathname:"/experimental/addon-builder",bundlePath:"",filename:""},components:{App:l.default,Document:i.default},userland:c});s()}catch(e){s(e)}})},1908:(e,t,n)=>{n.a(e,async(e,s)=>{try{n.d(t,{A:()=>m});var r=n(8732),o=n(2015),a=n.n(o),i=n(9733),l=n(2695),c=n(4722),d=n(8358),u=e([i]);i=(u.then?(await u)():u)[0];let h={name:"",description:"",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:`// Your command code here
await interaction.reply({
  content: 'Hello from your new command!',
  ephemeral: true
});`},x={name:"ready",once:!0,code:`// Your event code here
console.log('Bot is ready!');`},p=["ready","messageCreate","interactionCreate","guildMemberAdd","guildMemberRemove","voiceStateUpdate","messageReactionAdd","messageReactionRemove","channelCreate","channelDelete"];function m(){let{data:e}=(0,c.useSession)(),t=(0,d.useRouter)(),n=(0,i.useToast)(),{isOpen:s,onOpen:u,onClose:m}=(0,i.useDisclosure)(),{isOpen:g,onOpen:j,onClose:b}=(0,i.useDisclosure)(),{isOpen:v,onOpen:C,onClose:S}=(0,i.useDisclosure)(),[f,y]=(0,o.useState)(""),[k,A]=(0,o.useState)(!1),[w,I]=(0,o.useState)([]),[T,F]=(0,o.useState)([]),[M,B]=(0,o.useState)(!1),z=a().useRef(null),[P,$]=(0,o.useState)({name:"",version:"1.0.0",description:"",author:e?.user?.name||"",commands:[],events:[],settings:{embedColor:"#0099FF"}}),D=(0,i.useColorModeValue)("white","gray.800"),q=(0,i.useColorModeValue)("gray.200","gray.600"),E=(0,o.useCallback)((e,t)=>{$(n=>({...n,[e]:t}))},[]),H=(0,o.useCallback)((e,t)=>{$(n=>({...n,settings:{...n.settings,[e]:t}}))},[]),R=(0,o.useCallback)(()=>{$(e=>({...e,commands:[...e.commands,{...h}]}))},[]),L=(0,o.useCallback)((e,t,n)=>{$(s=>({...s,commands:s.commands.map((s,r)=>r===e?{...s,[t]:n}:s)}))},[]),V=(0,o.useCallback)(e=>{$(t=>({...t,commands:t.commands.filter((t,n)=>n!==e)}))},[]),N=(0,o.useCallback)(()=>{$(e=>({...e,events:[...e.events,{...x}]}))},[]),O=(0,o.useCallback)((e,t,n)=>{$(s=>({...s,events:s.events.map((s,r)=>r===e?{...s,[t]:n}:s)}))},[]),_=(0,o.useCallback)(e=>{$(t=>({...t,events:t.events.filter((t,n)=>n!==e)}))},[]),W=(0,o.useCallback)(()=>{let e=[];return(!P.name||P.name.length<2)&&e.push("Addon name must be at least 2 characters long"),/^[a-z0-9-]+$/.test(P.name)||e.push("Addon name must contain only lowercase letters, numbers, and hyphens"),P.version&&/^\d+\.\d+\.\d+$/.test(P.version)||e.push("Version must be in semver format (e.g., 1.0.0)"),(!P.description||P.description.length<10)&&e.push("Description must be at least 10 characters long"),(!P.author||P.author.length<2)&&e.push("Author name must be at least 2 characters long"),P.settings?.embedColor&&/^#[0-9a-fA-F]{6}$/.test(P.settings.embedColor)||e.push("Embed color must be a valid hex color (e.g., #0099FF)"),P.commands.forEach((t,n)=>{t.name&&/^[a-z0-9-]+$/.test(t.name)||e.push(`Command ${n+1}: Invalid name format`),(!t.description||t.description.length<1)&&e.push(`Command ${n+1}: Description is required`),(!t.code||t.code.trim().length<10)&&e.push(`Command ${n+1}: Code implementation is required`)}),P.events.forEach((t,n)=>{t.name||e.push(`Event ${n+1}: Name is required`),(!t.code||t.code.trim().length<5)&&e.push(`Event ${n+1}: Code implementation is required`)}),e},[P]),G=(0,o.useCallback)(()=>{let e=W();if(e.length>0)return void I(e);let t=`// ${P.name} - Generated Addon Preview
// This is a preview of your addon's main structure

export default {
  info: {
    name: "${P.name}",
    version: "${P.version}",
    description: "${P.description}",
    author: "${P.author}"
  },

  commands: ${P.commands.length} command${1!==P.commands.length?"s":""},
  events: ${P.events.length} event${1!==P.events.length?"s":""},
  
  settings: {
    embedColor: "${P.settings.embedColor}"
  }
};

// Commands: ${P.commands.map(e=>e.name).join(", ")||"None"}
// Events: ${P.events.map(e=>e.name).join(", ")||"None"}
`;y(t),u()},[P,W,u]),Y=(0,o.useCallback)(async()=>{B(!0);try{let e=await fetch("/api/experimental/addon-builder/templates"),t=await e.json();if(e.ok)F(t.templates);else throw Error(t.error||"Failed to load templates")}catch(e){n({title:"Error Loading Templates",description:e instanceof Error?e.message:"Failed to load templates",status:"error",duration:3e3,isClosable:!0})}finally{B(!1)}},[n]),U=(0,o.useCallback)(t=>{let s={...t.config};s.author=e?.user?.name||s.author,$(s),b(),n({title:"Template Loaded!",description:`${t.name} template has been applied.`,status:"success",duration:3e3,isClosable:!0})},[e,b,n]),J=(0,o.useCallback)(async()=>{let t=W();if(t.length>0)return void I(t);A(!0),I([]);try{let t=await fetch("/api/experimental/addon-builder/create",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(P)}),s=await t.json();if(!t.ok)throw Error(s.error||"Failed to create addon");n({title:"Addon Created Successfully!",description:`${P.name} has been created with ${s.files.length} files.`,status:"success",duration:5e3,isClosable:!0}),$({name:"",version:"1.0.0",description:"",author:e?.user?.name||"",commands:[],events:[],settings:{embedColor:"#0099FF"}})}catch(e){n({title:"Error Creating Addon",description:e instanceof Error?e.message:"An unknown error occurred",status:"error",duration:5e3,isClosable:!0})}finally{A(!1)}},[P,W,n,e]),Q=(0,o.useCallback)(()=>{""!==P.name.trim()||""!==P.description.trim()||P.commands.length>0||P.events.length>0||P.author!==(e?.user?.name||"")?C():t.push("/admin/addons")},[P,e,C,t]),X=(0,o.useCallback)(()=>{S(),t.push("/admin/addons")},[S,t]);return(0,r.jsxs)(i.Box,{maxW:"4xl",mx:"auto",p:6,children:[(0,r.jsxs)(i.VStack,{spacing:6,align:"stretch",children:[(0,r.jsxs)(i.Box,{textAlign:"center",children:[(0,r.jsx)(i.Heading,{size:"lg",mb:2,children:"\uD83D\uDEE0️ Addon Builder"}),(0,r.jsx)(i.Text,{color:"gray.500",mb:4,children:"Create custom addons for your Discord bot with a visual interface"}),(0,r.jsxs)(i.HStack,{spacing:3,justify:"center",children:[(0,r.jsx)(i.Button,{leftIcon:(0,r.jsx)(l.QVr,{}),onClick:Q,colorScheme:"gray",variant:"outline",size:"sm",children:"Go Back"}),(0,r.jsx)(i.Button,{leftIcon:(0,r.jsx)(l.FSj,{}),onClick:()=>{Y(),j()},colorScheme:"purple",variant:"outline",size:"sm",children:"Start from Template"})]})]}),w.length>0&&(0,r.jsxs)(i.Alert,{status:"error",children:[(0,r.jsx)(i.AlertIcon,{}),(0,r.jsxs)(i.Box,{children:[(0,r.jsx)(i.AlertTitle,{children:"Validation Errors:"}),(0,r.jsx)(i.AlertDescription,{children:(0,r.jsx)(i.VStack,{align:"start",spacing:1,children:w.map((e,t)=>(0,r.jsxs)(i.Text,{fontSize:"sm",children:["• ",e]},t))})})]})]}),(0,r.jsxs)(i.Tabs,{colorScheme:"blue",variant:"enclosed",children:[(0,r.jsxs)(i.TabList,{children:[(0,r.jsx)(i.Tab,{children:"Basic Info"}),(0,r.jsxs)(i.Tab,{children:["Commands (",P.commands.length,")"]}),(0,r.jsxs)(i.Tab,{children:["Events (",P.events.length,")"]}),(0,r.jsx)(i.Tab,{children:"Settings"})]}),(0,r.jsxs)(i.TabPanels,{children:[(0,r.jsx)(i.TabPanel,{children:(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Addon Name"}),(0,r.jsx)(i.Input,{value:P.name,onChange:e=>E("name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"my-awesome-addon"}),(0,r.jsx)(i.Text,{fontSize:"sm",color:"gray.500",children:"Only lowercase letters, numbers, and hyphens allowed"})]}),(0,r.jsxs)(i.HStack,{spacing:4,children:[(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Version"}),(0,r.jsx)(i.Input,{value:P.version,onChange:e=>E("version",e.target.value),placeholder:"1.0.0"})]}),(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Author"}),(0,r.jsx)(i.Input,{value:P.author,onChange:e=>E("author",e.target.value),placeholder:"Your Name"})]})]}),(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Description"}),(0,r.jsx)(i.Textarea,{value:P.description,onChange:e=>E("description",e.target.value),placeholder:"A brief description of what your addon does...",minH:"100px"})]})]})}),(0,r.jsx)(i.TabPanel,{children:(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(i.HStack,{justify:"space-between",children:[(0,r.jsx)(i.Heading,{size:"md",children:"Commands"}),(0,r.jsx)(i.Button,{leftIcon:(0,r.jsx)(l.OiG,{}),onClick:R,colorScheme:"blue",children:"Add Command"})]}),0===P.commands.length?(0,r.jsxs)(i.Alert,{status:"info",children:[(0,r.jsx)(i.AlertIcon,{}),(0,r.jsx)(i.AlertDescription,{children:"No commands yet. Add your first command to get started!"})]}):(0,r.jsx)(i.Accordion,{allowMultiple:!0,children:P.commands.map((e,t)=>(0,r.jsxs)(i.AccordionItem,{children:[(0,r.jsxs)(i.AccordionButton,{children:[(0,r.jsxs)(i.Box,{flex:"1",textAlign:"left",children:[(0,r.jsxs)(i.HStack,{children:[(0,r.jsx)(i.Text,{fontWeight:"bold",children:e.name||`Command ${t+1}`}),(0,r.jsx)(i.Badge,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Enabled":"Disabled"})]}),(0,r.jsx)(i.Text,{fontSize:"sm",color:"gray.500",children:e.description||"No description"})]}),(0,r.jsx)(i.AccordionIcon,{})]}),(0,r.jsx)(i.AccordionPanel,{pb:4,children:(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(i.HStack,{children:[(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Command Name"}),(0,r.jsx)(i.Input,{value:e.name,onChange:e=>L(t,"name",e.target.value.toLowerCase().replace(/[^a-z0-9-]/g,"")),placeholder:"ping"})]}),(0,r.jsxs)(i.FormControl,{children:[(0,r.jsx)(i.FormLabel,{children:"Cooldown (ms)"}),(0,r.jsxs)(i.NumberInput,{value:e.cooldown,onChange:(e,n)=>L(t,"cooldown",n),min:1e3,max:3e5,children:[(0,r.jsx)(i.NumberInputField,{}),(0,r.jsxs)(i.NumberInputStepper,{children:[(0,r.jsx)(i.NumberIncrementStepper,{}),(0,r.jsx)(i.NumberDecrementStepper,{})]})]})]})]}),(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Description"}),(0,r.jsx)(i.Input,{value:e.description,onChange:e=>L(t,"description",e.target.value),placeholder:"Shows bot ping"})]}),(0,r.jsxs)(i.FormControl,{children:[(0,r.jsx)(i.FormLabel,{children:"Command Code"}),(0,r.jsx)(i.Textarea,{value:e.code,onChange:e=>L(t,"code",e.target.value),placeholder:"// Your command implementation here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,r.jsxs)(i.HStack,{children:[(0,r.jsx)(i.Checkbox,{isChecked:e.enabled,onChange:e=>L(t,"enabled",e.target.checked),children:"Enabled"}),(0,r.jsx)(i.Button,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,r.jsx)(l.qbC,{}),onClick:()=>V(t),children:"Remove"})]})]})})]},t))})]})}),(0,r.jsx)(i.TabPanel,{children:(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(i.HStack,{justify:"space-between",children:[(0,r.jsx)(i.Heading,{size:"md",children:"Events"}),(0,r.jsx)(i.Button,{leftIcon:(0,r.jsx)(l.OiG,{}),onClick:N,colorScheme:"blue",children:"Add Event"})]}),0===P.events.length?(0,r.jsxs)(i.Alert,{status:"info",children:[(0,r.jsx)(i.AlertIcon,{}),(0,r.jsx)(i.AlertDescription,{children:"No events yet. Add event handlers to respond to Discord events!"})]}):(0,r.jsx)(i.Accordion,{allowMultiple:!0,children:P.events.map((e,t)=>(0,r.jsxs)(i.AccordionItem,{children:[(0,r.jsxs)(i.AccordionButton,{children:[(0,r.jsx)(i.Box,{flex:"1",textAlign:"left",children:(0,r.jsxs)(i.HStack,{children:[(0,r.jsx)(i.Text,{fontWeight:"bold",children:e.name||`Event ${t+1}`}),(0,r.jsx)(i.Badge,{colorScheme:e.once?"blue":"green",children:e.once?"Once":"Recurring"})]})}),(0,r.jsx)(i.AccordionIcon,{})]}),(0,r.jsx)(i.AccordionPanel,{pb:4,children:(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsxs)(i.HStack,{children:[(0,r.jsxs)(i.FormControl,{isRequired:!0,children:[(0,r.jsx)(i.FormLabel,{children:"Event Name"}),(0,r.jsxs)(i.Select,{value:e.name,onChange:e=>O(t,"name",e.target.value),children:[(0,r.jsx)("option",{value:"",children:"Select an event"}),p.map(e=>(0,r.jsx)("option",{value:e,children:e},e))]})]}),(0,r.jsxs)(i.FormControl,{children:[(0,r.jsx)(i.FormLabel,{children:"Trigger Type"}),(0,r.jsxs)(i.Select,{value:e.once?"once":"recurring",onChange:e=>O(t,"once","once"===e.target.value),children:[(0,r.jsx)("option",{value:"once",children:"Once"}),(0,r.jsx)("option",{value:"recurring",children:"Recurring"})]})]})]}),(0,r.jsxs)(i.FormControl,{children:[(0,r.jsx)(i.FormLabel,{children:"Event Code"}),(0,r.jsx)(i.Textarea,{value:e.code,onChange:e=>O(t,"code",e.target.value),placeholder:"// Your event handler code here",minH:"200px",fontFamily:"mono",fontSize:"sm"})]}),(0,r.jsx)(i.Button,{size:"sm",variant:"outline",colorScheme:"red",leftIcon:(0,r.jsx)(l.qbC,{}),onClick:()=>_(t),alignSelf:"flex-start",children:"Remove Event"})]})})]},t))})]})}),(0,r.jsx)(i.TabPanel,{children:(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(i.Heading,{size:"md",children:"Settings"}),(0,r.jsxs)(i.FormControl,{children:[(0,r.jsx)(i.FormLabel,{children:"Embed Color"}),(0,r.jsxs)(i.HStack,{children:[(0,r.jsx)(i.Input,{type:"color",value:P.settings.embedColor,onChange:e=>H("embedColor",e.target.value),w:"80px"}),(0,r.jsx)(i.Input,{value:P.settings.embedColor,onChange:e=>H("embedColor",e.target.value),placeholder:"#0099FF"})]})]}),(0,r.jsxs)(i.Alert,{status:"info",children:[(0,r.jsx)(i.AlertIcon,{}),(0,r.jsx)(i.AlertDescription,{children:"More settings will be added in future updates!"})]})]})})]})]}),(0,r.jsx)(i.Divider,{}),(0,r.jsxs)(i.HStack,{spacing:4,justify:"center",children:[(0,r.jsx)(i.Button,{leftIcon:(0,r.jsx)(l.Ny1,{}),onClick:G,variant:"outline",colorScheme:"blue",children:"Preview"}),(0,r.jsx)(i.Button,{leftIcon:(0,r.jsx)(l.uoG,{}),onClick:J,colorScheme:"blue",size:"lg",isLoading:k,loadingText:"Creating...",children:"Create Addon"})]})]}),(0,r.jsxs)(i.Modal,{isOpen:s,onClose:m,size:"xl",children:[(0,r.jsx)(i.ModalOverlay,{}),(0,r.jsxs)(i.ModalContent,{children:[(0,r.jsx)(i.ModalHeader,{children:"Addon Preview"}),(0,r.jsx)(i.ModalCloseButton,{}),(0,r.jsx)(i.ModalBody,{children:(0,r.jsx)(i.Code,{as:"pre",p:4,fontSize:"sm",overflow:"auto",maxH:"400px",children:f})}),(0,r.jsx)(i.ModalFooter,{children:(0,r.jsx)(i.Button,{onClick:m,children:"Close"})})]})]}),(0,r.jsxs)(i.Modal,{isOpen:g,onClose:b,size:"4xl",children:[(0,r.jsx)(i.ModalOverlay,{}),(0,r.jsxs)(i.ModalContent,{children:[(0,r.jsx)(i.ModalHeader,{children:"Choose a Template"}),(0,r.jsx)(i.ModalCloseButton,{}),(0,r.jsx)(i.ModalBody,{children:M?(0,r.jsx)(i.Center,{py:8,children:(0,r.jsxs)(i.VStack,{spacing:4,children:[(0,r.jsx)(i.Spinner,{size:"lg"}),(0,r.jsx)(i.Text,{children:"Loading templates..."})]})}):(0,r.jsxs)(i.VStack,{spacing:4,align:"stretch",children:[(0,r.jsx)(i.Text,{color:"gray.500",children:"Start with a pre-built template to save time and learn best practices."}),(0,r.jsx)(i.SimpleGrid,{columns:{base:1,md:2},spacing:4,children:T.map(e=>(0,r.jsx)(i.Box,{bg:D,border:"1px",borderColor:q,borderRadius:"md",p:4,cursor:"pointer",transition:"all 0.2s",_hover:{borderColor:"blue.500",transform:"translateY(-2px)",boxShadow:"md"},onClick:()=>U(e),children:(0,r.jsxs)(i.VStack,{align:"start",spacing:3,children:[(0,r.jsxs)(i.HStack,{justify:"space-between",w:"full",children:[(0,r.jsx)(i.Heading,{size:"sm",children:e.name}),(0,r.jsx)(i.Badge,{colorScheme:"blue",size:"sm",children:e.category})]}),(0,r.jsx)(i.Text,{fontSize:"sm",color:"gray.500",children:e.description}),(0,r.jsxs)(i.HStack,{spacing:2,children:[(0,r.jsxs)(i.Badge,{variant:"outline",size:"xs",children:[e.config.commands.length," commands"]}),(0,r.jsxs)(i.Badge,{variant:"outline",size:"xs",children:[e.config.events.length," events"]})]})]})},e.id))})]})}),(0,r.jsx)(i.ModalFooter,{children:(0,r.jsx)(i.Button,{onClick:b,children:"Cancel"})})]})]}),(0,r.jsx)(i.AlertDialog,{isOpen:v,leastDestructiveRef:z,onClose:S,isCentered:!0,children:(0,r.jsx)(i.AlertDialogOverlay,{children:(0,r.jsxs)(i.AlertDialogContent,{children:[(0,r.jsx)(i.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",children:"Leave Addon Builder?"}),(0,r.jsx)(i.AlertDialogBody,{children:"You have unsaved changes to your addon. Are you sure you want to go back? All your work will be lost."}),(0,r.jsxs)(i.AlertDialogFooter,{children:[(0,r.jsx)(i.Button,{ref:z,onClick:S,children:"Cancel"}),(0,r.jsx)(i.Button,{colorScheme:"red",onClick:X,ml:3,children:"Yes, Go Back"})]})]})})})]})}s()}catch(e){s(e)}})},2015:e=>{e.exports=require("react")},2115:e=>{e.exports=require("yaml")},2326:e=>{e.exports=require("react-dom")},2380:(e,t,n)=>{n.a(e,async(e,s)=>{try{n.r(t),n.d(t,{default:()=>u,getServerSideProps:()=>m});var r=n(8732),o=n(5806),a=n(3762),i=n(9733),l=n(1011),c=n(1908),d=e([i,l,c]);function u(){return(0,r.jsx)(l.A,{children:(0,r.jsxs)(i.VStack,{spacing:8,p:8,align:"stretch",maxW:"100%",overflow:"hidden",children:[(0,r.jsxs)(i.Box,{textAlign:"center",children:[(0,r.jsx)(i.Heading,{size:"xl",mb:2,children:"⚗️ Addon Builder"}),(0,r.jsxs)(i.Alert,{status:"info",mb:4,children:[(0,r.jsx)(i.AlertIcon,{}),(0,r.jsxs)(i.AlertDescription,{children:[(0,r.jsx)("strong",{children:"Experimental Feature:"})," This is a beta feature for creating custom Discord bot addons. Use with caution and report any issues you encounter."]})]})]}),(0,r.jsx)(c.A,{})]})})}[i,l,c]=d.then?(await d)():d;let m=async e=>await (0,o.getServerSession)(e.req,e.res,a.N)?{props:{}}:{redirect:{destination:"/api/auth/signin?callbackUrl=%2Fexperimental%2Faddon-builder",permanent:!1}};s()}catch(e){s(e)}})},3762:(e,t,n)=>{n.d(t,{N:()=>h});var s=n(5542),r=n.n(s);let o=require("next-auth/providers/discord");var a=n.n(o),i=n(9021),l=n(2115),c=n.n(l),d=n(3873);let u={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d.resolve(process.cwd(),e)).find(e=>i.existsSync(e));if(!e){let t=d.resolve(__dirname,"../../../config.yml");i.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=i.readFileSync(e,"utf8");u=c().parse(t)}catch(e){process.exit(1)}let m={bot:{token:u.bot.token,clientId:u.bot.clientId,clientSecret:u.bot.clientSecret,guildId:u.bot.guildId,ticketCategoryId:u.bot.ticketCategoryId||null,ticketLogChannelId:u.bot.ticketLogChannelId||null,prefix:u.bot.prefix},dashboard:{admins:u.dashboard?.admins||[],adminRoleIds:u.dashboard?.adminRoleIds||[],session:{secret:u.dashboard?.session?.secret||u.bot.clientSecret}},database:{url:u.database.url,name:u.database.name,options:{maxPoolSize:u.database.options?.maxPoolSize||10,minPoolSize:u.database.options?.minPoolSize||1,maxIdleTimeMS:u.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:u.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:u.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:u.database.options?.connectTimeoutMS||1e4,retryWrites:u.database.options?.retryWrites!==!1,retryReads:u.database.options?.retryReads!==!1}}};m.bot.token||process.exit(1),m.bot.clientId&&m.bot.clientSecret||process.exit(1),m.bot.guildId||process.exit(1),m.database.url&&m.database.name||process.exit(1);let h={providers:[a()({clientId:m.bot.clientId,clientSecret:m.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:n})=>(t&&n&&(e.accessToken=t.access_token||null,e.id=n.id||null),e),async session({session:e,token:t}){if(e?.user){let n=t.id||null,s=t.accessToken||null;e.user.id=n,e.user.accessToken=s;let r=!1;if(n)if((m.dashboard.admins||[]).includes(n))r=!0;else{let e=m.dashboard.adminRoleIds||[];if(e.length&&m.bot.token&&m.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${m.bot.guildId}/members/${n}`,{headers:{Authorization:`Bot ${m.bot.token}`}});if(t.ok){let n=await t.json();r=e.some(e=>n.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let n=new URL(t),s=`${n.protocol}//localhost${n.port?`:${n.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:m.dashboard.session.secret||m.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};r()(h)},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},5542:e=>{e.exports=require("next-auth")},5806:e=>{e.exports=require("next-auth/next")},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),s=t.X(0,[8270,4874,752,6281,2695,5333],()=>n(918));module.exports=s})();