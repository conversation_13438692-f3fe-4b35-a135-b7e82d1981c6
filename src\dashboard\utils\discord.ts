import { Session } from 'next-auth';
import { Client, Guild } from 'discord.js';

let client: Client | null = null;

export async function getGuildFromSession(session: Session): Promise<Guild | null> {
  if (!client) {
    client = new Client({
      intents: [] // No intents needed for just fetching
    });
    await client.login(process.env.DISCORD_BOT_TOKEN);
  }

  try {
    // Get guild ID from session or environment
    const guildId = process.env.DISCORD_GUILD_ID;
    if (!guildId) {
      throw new Error('DISCORD_GUILD_ID not configured');
    }

    // Fetch guild
    const guild = await client.guilds.fetch(guildId);
    return guild;
  } catch (error) {
    console.error('Error fetching guild:', error);
    return null;
  }
} 