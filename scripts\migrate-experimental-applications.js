// Migration script: experimental_requests -> applications (type: "experimental")
// and experimental_settings -> settings collection.
// Run with: node scripts/migrate-experimental-applications.js

import { MongoClient } from 'mongodb';
import fs from 'fs';
import path from 'path';
import YAML from 'yaml';

// Load root config.yml (supports several relative search paths like other config loaders)
function loadRootConfig() {
  const attempts = [
    'config.yml',
    '../config.yml',
    '../../config.yml',
  ].map(rel => path.resolve(process.cwd(), rel));

  const found = attempts.find(p => fs.existsSync(p));
  if (!found) throw new Error('config.yml not found');
  return YAML.parse(fs.readFileSync(found, 'utf8'));
}

const rootConfig = loadRootConfig();
const mongoUrl = rootConfig.database?.url || 'mongodb://localhost:27017';
const dbName   = rootConfig.database?.name || 'discord_bot';
const options  = rootConfig.database?.options || {};

async function runMigration() {
  const client = await MongoClient.connect(mongoUrl, { ...(options || {}) });
  const db = client.db(dbName);

  try {
    // 1. Migrate experimental_requests → applications
    const experimentalRequestsCol = db.collection('experimental_requests');
    const applicationsCol = db.collection('applications');

    const requestsCursor = experimentalRequestsCol.find({});
    let migratedCount = 0;

    while (await requestsCursor.hasNext()) {
      const request = await requestsCursor.next();
      if (!request) break;

      // Build new application doc
      const newDoc = {
        userId: request.userId,
        username: request.username || null,
        feature: request.feature,
        reason: request.reason,
        timestamp: request.timestamp || new Date(),
        status: request.status || 'pending',
        type: 'experimental',
      };

      // Upsert (prevent duplicates if re-run)
      await applicationsCol.updateOne(
        { userId: newDoc.userId, type: 'experimental', feature: newDoc.feature },
        { $setOnInsert: newDoc },
        { upsert: true }
      );
      migratedCount++;
    }

    console.log(`✅ Migrated ${migratedCount} experimental request(s) into 'applications' collection.`);

    // 2. Migrate experimental_settings → settings collection
    const experimentalSettingsCol = db.collection('experimental_settings');
    const settingsCol = db.collection('settings');

    const expSettings = await experimentalSettingsCol.find({}).toArray();
    for (const setting of expSettings) {
      await settingsCol.updateOne(
        { key: setting.key },
        { $set: { value: setting.enabled, updatedAt: new Date() } },
        { upsert: true }
      );
    }
    console.log(`✅ Migrated ${expSettings.length} experimental setting(s) into 'settings' collection.`);

    // 3. Create indexes on applications
    await applicationsCol.createIndex({ type: 1 });
    await applicationsCol.createIndex({ status: 1 });
    await applicationsCol.createIndex({ userId: 1 });
    console.log('✅ Ensured indexes on applications collection.');

    console.log('🎉 Migration completed successfully.');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await client.close();
  }
}

runMigration(); 