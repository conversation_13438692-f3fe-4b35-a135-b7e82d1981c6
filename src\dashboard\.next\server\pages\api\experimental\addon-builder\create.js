"use strict";(()=>{var e={};e.id=3571,e.ids=[3571],e.modules={264:(e,o)=>{Object.defineProperty(o,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,o)=>{Object.defineProperty(o,"M",{enumerable:!0,get:function(){return function e(o,n){return n in o?o[n]:"then"in o&&"function"==typeof o.then?o.then(o=>e(o,n)):"function"==typeof o&&"default"===n?o:void 0}}})},1884:(e,o,n)=>{n.r(o),n.d(o,{config:()=>x,default:()=>y,routeModule:()=>S});var t={};n.r(t),n.d(t,{default:()=>p});var a=n(3433),r=n(264),s=n(584),i=n(5806),d=n(8525),c=n(2518),l=n(9021),m=n.n(l),u=n(3873),f=n.n(u);let g={url:process.env.DATABASE_URL||"mongodb://localhost:27017",name:process.env.DATABASE_NAME||"discord-bot"};async function p(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});let n=await (0,i.getServerSession)(e,o,d.authOptions);if(!n)return o.status(401).json({error:"Unauthorized"});try{let t=await c.MongoClient.connect(g.url),a=t.db(g.name),r={userId:n.user.id},[s,i]=await Promise.all([a.collection("experimental_testers").findOne(r),a.collection("experimental_flags").findOne(r)]);if(!(s||i||n.user.isAdmin))return await t.close(),o.status(403).json({error:"Experimental features access required"});let d=e.body,l=function(e){let o=[];return(!e.name||e.name.length<2)&&o.push("Addon name must be at least 2 characters long"),/^[a-z0-9-]+$/.test(e.name)||o.push("Addon name must contain only lowercase letters, numbers, and hyphens"),e.version&&/^\d+\.\d+\.\d+$/.test(e.version)||o.push("Version must be in semver format (e.g., 1.0.0)"),(!e.description||e.description.length<10)&&o.push("Description must be at least 10 characters long"),(!e.author||e.author.length<2)&&o.push("Author name must be at least 2 characters long"),e.settings?.embedColor&&/^#[0-9a-fA-F]{6}$/.test(e.settings.embedColor)||o.push("Embed color must be a valid hex color (e.g., #0099FF)"),{isValid:0===o.length,errors:o}}(d);if(!l.isValid)return await t.close(),o.status(400).json({error:"Invalid addon configuration",details:l.errors});let u=function(e){var o,n;let t={};return t["index.ts"]=(o=0,`import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  commands: await loadCommands(),

  events: [
    {
      name: 'ready',
      once: true,
      execute: async (client) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        logger.info(\`\${config.addon.name} addon loaded! Bot ready as \${client.user?.tag}\`);
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Loading \${config.addon.name} addon...\`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Unloading \${config.addon.name} addon...\`);
  }
};

export default addon;`),t["config.yml"]=function(e){let o={addon:{name:e.name,version:e.version,description:e.description,author:e.author,enabled:!0},commands:{},settings:{embedColor:parseInt(e.settings.embedColor.slice(1),16)}};return e.commands.forEach(e=>{o.commands[e.name]={enabled:e.enabled,cooldown:e.cooldown||3e3,description:e.description}}),`addon:
  name: "${e.name}"
  version: "${e.version}"
  description: "${e.description}"
  author: "${e.author}"
  enabled: true

settings:
  embedColor: 0x${e.settings.embedColor.slice(1)}

logging:
  enabled: true
  logCommands: true
  logLevel: "info"`}(e),t["README.md"]=(n=e,`# ${n.name}

${n.description}

**Author:** ${n.author}  
**Version:** ${n.version}

## Commands

${n.commands.map(e=>`- **/${e.name}** - ${e.description}`).join("\n")||"No commands available."}

## Settings

- **Embed Color:** ${n.settings.embedColor}

---

*This addon was generated using the 404 Bot Addon Builder.*`),e.commands.forEach(e=>{var o;t[`commands/${e.name.toLowerCase()}.ts`]=(o=e,`import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('${o.name}')
  .setDescription('${o.description}');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    ${o.code}
  } catch (error) {
    console.error('Error executing ${o.name} command:', error);
    
    const errorMessage = 'There was an error executing this command!';
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({ content: errorMessage, ephemeral: true });
    }
  }
}

export const cooldown = ${o.cooldown||3e3};`)}),t}(d),p=await h(d.name);await b(p,u),await a.collection("addon_builder_logs").insertOne({userId:n.user.id,userEmail:n.user.email,addonName:d.name,action:"created",timestamp:new Date,fileCount:Object.keys(u).length});let y=process.cwd().includes(f().join("src","dashboard"))?f().resolve(process.cwd(),"..",".."):process.cwd(),x=f().join(y,"addon-reload.signal");m().writeFileSync(x,JSON.stringify({requestedBy:n.user.email||"addon-builder",timestamp:Date.now(),action:"addon-created",addonName:d.name})),await t.close(),o.status(201).json({message:"Addon created successfully",addonName:d.name,files:Object.keys(u),path:p})}catch(e){o.status(500).json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"})}}async function h(e){let o=process.cwd().includes(f().join("src","dashboard"))?f().resolve(process.cwd(),"..",".."):process.cwd(),n=f().join(o,"src","addons"),t=f().join(n,e);if(m().existsSync(t))throw Error(`Addon '${e}' already exists`);return m().mkdirSync(t,{recursive:!0}),m().mkdirSync(f().join(t,"commands"),{recursive:!0}),t}async function b(e,o){for(let[n,t]of Object.entries(o)){let o=f().join(e,n),a=f().dirname(o);m().existsSync(a)||m().mkdirSync(a,{recursive:!0}),m().writeFileSync(o,t,"utf8")}}let y=(0,s.M)(t,"default"),x=(0,s.M)(t,"config"),S=new a.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/experimental/addon-builder/create",pathname:"/api/experimental/addon-builder/create",bundlePath:"",filename:""},userland:t})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,o,n)=>{e.exports=n(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,o,n)=>{n.r(o),n.d(o,{authOptions:()=>d,default:()=>c});var t=n(5542),a=n.n(t);let r=require("next-auth/providers/discord");var s=n.n(r),i=n(8580);let d={providers:[s()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:o,profile:n})=>(o&&n&&(e.accessToken=o.access_token||null,e.id=n.id||null),e),async session({session:e,token:o}){if(e?.user){let n=o.id||null,t=o.accessToken||null;e.user.id=n,e.user.accessToken=t;let a=!1;if(n)if((i.dashboardConfig.dashboard.admins||[]).includes(n))a=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let o=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${n}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(o.ok){let n=await o.json();a=e.some(e=>n.roles?.includes(e))||!1}else await o.text()}catch(e){}}e.user.isAdmin=a,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:o}){let n=new URL(o),t=`${n.protocol}//localhost${n.port?`:${n.port}`:""}`;return e.startsWith(o)||e.startsWith(t)?e:o}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,o)=>{},warn:e=>{},debug:(e,o)=>{}}},c=a()(d)},8580:(e,o,n)=>{n.r(o),n.d(o,{dashboardConfig:()=>d,default:()=>c});var t=n(9021),a=n(2115),r=n.n(a),s=n(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>s.resolve(process.cwd(),e)).find(e=>t.existsSync(e));if(!e){let o=s.resolve(__dirname,"../../../config.yml");t.existsSync(o)&&(e=o)}if(!e)throw Error("config.yml not found");let o=t.readFileSync(e,"utf8");i=r().parse(o)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let c=d},9021:e=>{e.exports=require("fs")}};var o=require("../../../../webpack-api-runtime.js");o.C(e);var n=o(o.s=1884);module.exports=n})();