"use strict";(()=>{var e={};e.id=2462,e.ids=[2462],e.modules={156:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>u,routeModule:()=>f});var o={};r.r(o),r.d(o,{default:()=>c});var a=r(3433),s=r(264),n=r(584),i=r(5542),d=r(8525);let l=()=>{let e=["Discord API","Database","Command Handler","Event Handler","Addon System","Authentication"],t=["error","warn","info"],r=["Failed to connect to Discord API","Database connection timeout","Command execution failed","Rate limit exceeded","Invalid permissions for user action","Addon failed to load","Memory usage high","Webhook delivery failed","Cache miss for guild data","Token refresh required","User not found in database","Channel permissions insufficient","Role assignment failed","Message send timeout","Voice channel connection lost"],o=[],a=Date.now();for(let s=0;s<50;s++){let n=t[Math.floor(Math.random()*t.length)],i=e[Math.floor(Math.random()*e.length)],d=r[Math.floor(Math.random()*r.length)],l=new Date(a-7*Math.random()*864e5).toISOString();o.push({id:`error_${s+1}`,timestamp:l,level:n,message:d,source:i,stack:"error"===n?`Error: ${d}
    at CommandHandler.execute (/app/src/commands/handler.js:123:45)
    at DiscordClient.onMessage (/app/src/client.js:67:12)
    at EventEmitter.emit (events.js:314:20)`:void 0,userId:Math.random()>.5?"933023999770918932":void 0,guildId:Math.random()>.3?"1234567890123456789":void 0,metadata:Math.random()>.7?{commandName:"ping",executionTime:Math.floor(1e3*Math.random()),memoryUsage:Math.floor(100*Math.random())}:void 0})}return o.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime())};async function c(e,t){try{let r=await (0,i.getServerSession)(e,t,d.authOptions);if(!r||!r.user?.isAdmin)return t.status(401).json({error:"Unauthorized"});if("GET"===e.method)try{let e=l();return t.status(200).json({success:!0,errors:e,total:e.length})}catch(e){return t.status(500).json({error:"Failed to fetch error logs",details:e instanceof Error?e.message:"Unknown error"})}if("DELETE"===e.method)try{return t.status(200).json({success:!0,message:"All error logs have been cleared"})}catch(e){return t.status(500).json({error:"Failed to clear error logs",details:e instanceof Error?e.message:"Unknown error"})}if("POST"===e.method){let{level:o,message:a,source:s,stack:n,metadata:i}=e.body;if(!o||!a||!s)return t.status(400).json({error:"Missing required fields: level, message, source"});let d={id:`error_${Date.now()}`,timestamp:new Date().toISOString(),level:o,message:a,source:s,stack:n,userId:r.user?.id,metadata:i};return t.status(201).json({success:!0,error:d,message:"Error log created successfully"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"})}}let u=(0,n.M)(o,"default"),m=(0,n.M)(o,"config"),f=new a.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/admin/errors",pathname:"/api/admin/errors",bundlePath:"",filename:""},userland:o})},264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var o=r(5542),a=r.n(o);let s=require("next-auth/providers/discord");var n=r.n(s),i=r(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,o=t.accessToken||null;e.user.id=r,e.user.accessToken=o;let a=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))a=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();a=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=a,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),o=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=a()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var o=r(9021),a=r(2115),s=r.n(a),n=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");i=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=156);module.exports=r})();