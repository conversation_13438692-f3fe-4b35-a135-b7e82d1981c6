// @ts-nocheck
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  <PERSON><PERSON>,
  HS<PERSON>ck,
  Icon,
  Spinner,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Text,
  Badge,
  useDisclosure,
  VStack,
} from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import Layout from '../components/Layout';
import CreateTicketDialog from '../components/CreateTicketDialog';
import { FiPlus, FiExternalLink } from 'react-icons/fi';
import { GetServerSideProps } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from './api/auth/[...nextauth]';

interface Ticket {
  _id: string;
  creatorId: string;
  creatorTag: string;
  channelId: string;
  status: 'open' | 'closed';
  reason?: string;
  createdAt: string;
  closedAt?: string;
  transcriptUrl?: string;
  discordLink?: string;
}

interface TicketsPageProps { isAdmin: boolean; }

export default function TicketsPage({ isAdmin }: TicketsPageProps) {
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { isOpen, onOpen, onClose } = useDisclosure();

  const fetchTickets = async () => {
    setIsLoading(true);
    try {
      const res = await fetch('/api/discord/tickets');
      if (!res.ok) throw new Error('Failed to fetch tickets');
      const data = await res.json();
      setTickets(data);
    } catch (err) {
      console.error(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTickets();
  }, []);

  return (
    <Layout>
      <Container maxW="container.xl" py={8}>
        <VStack spacing={8} align="stretch">
          {/* Header */}
          <Box
            bg="rgba(255,255,255,0.08)"
            p={8}
            rounded="2xl"
            backdropFilter="blur(10px)"
            border="2px solid"
            borderColor="blue.400"
            boxShadow="0 0 15px rgba(66, 153, 225, 0.4)"
            textAlign="center"
          >
            <Heading
              size="2xl"
              bgGradient="linear(to-r, blue.300, purple.400)"
              bgClip="text"
              mb={4}
            >
              Support Tickets
            </Heading>
            <Text color="gray.300" fontSize="lg">
              Open new tickets or review existing ones
            </Text>
          </Box>

          <HStack justify="flex-end">
            <Button leftIcon={<Icon as={FiPlus} />} colorScheme="blue" onClick={onOpen}>
              Create Ticket
            </Button>
          </HStack>

          {/* Ticket Table */}
          <Box bg="whiteAlpha.100" backdropFilter="blur(10px)" rounded="lg" p={4}>
            {isLoading ? (
              <HStack justify="center" py={10}>
                <Spinner size="lg" />
              </HStack>
            ) : tickets.length === 0 ? (
              <Text textAlign="center" py={10} color="gray.300">
                You have no tickets yet.
              </Text>
            ) : (
              <Table variant="simple">
                <Thead>
                  <Tr>
                    <Th>ID</Th>
                    <Th>Reason</Th>
                    <Th>Status</Th>
                    <Th>Created</Th>
                    <Th>Actions</Th>
                  </Tr>
                </Thead>
                <Tbody>
                  {tickets.map((ticket) => (
                    <Tr key={ticket._id}>
                      <Td>{ticket._id.toString().slice(-6)}</Td>
                      <Td maxW="300px">
                        <Text isTruncated title={ticket.reason}>{ticket.reason || 'No reason provided'}</Text>
                      </Td>
                      <Td>
                        <Badge colorScheme={ticket.status === 'open' ? 'green' : 'red'}>{ticket.status}</Badge>
                      </Td>
                      <Td>{new Date(ticket.createdAt).toLocaleString()}</Td>
                      <Td>
                        <HStack spacing={2}>
                          {ticket.discordLink && (
                            <Button
                              as="a"
                              href={ticket.discordLink}
                              target="_blank"
                              size="sm"
                              leftIcon={<Icon as={FiExternalLink} />}
                            >
                              Discord
                            </Button>
                          )}
                          {ticket.status === 'open' && (
                            <Button
                              size="sm"
                              colorScheme="yellow"
                              onClick={async () => {
                                if (!window.confirm('Close this ticket?')) return;
                                try {
                                  const res = await fetch(`/api/discord/tickets/${ticket._id}`, {
                                    method: 'PATCH',
                                  });
                                  if (!res.ok) throw new Error('Failed to close ticket');
                                  fetchTickets();
                                } catch (err) {
                                  console.error(err);
                                }
                              }}
                            >
                              Close
                            </Button>
                          )}
                          {isAdmin && ticket.status === 'closed' && (
                            <Button
                              size="sm"
                              colorScheme="red"
                              onClick={async () => {
                                if (!window.confirm('Delete this ticket? This is irreversible.')) return;
                                try {
                                  const res = await fetch(`/api/discord/tickets/${ticket._id}`, {
                                    method: 'DELETE',
                                  });
                                  if (!res.ok) throw new Error('Failed to delete ticket');
                                  fetchTickets();
                                } catch (err) {
                                  console.error(err);
                                }
                              }}
                            >
                              Delete
                            </Button>
                          )}
                          {ticket.status === 'closed' && (
                            <Button
                              as="a"
                              href={`/api/discord/tickets/${ticket._id}/transcript`}
                              size="sm"
                              colorScheme="green"
                              leftIcon={<Icon as={FiExternalLink} />}
                              target="_blank"
                            >
                              Transcript
                            </Button>
                          )}
                        </HStack>
                      </Td>
                    </Tr>
                  ))}
                </Tbody>
              </Table>
            )}
          </Box>
        </VStack>

        {/* Create Ticket Modal */}
        <CreateTicketDialog isOpen={isOpen} onClose={onClose} onSuccess={fetchTickets} />
      </Container>
    </Layout>
  );
}

// Server-side auth guard
export const getServerSideProps: GetServerSideProps = async (ctx) => {
  const session = await getServerSession(ctx.req, ctx.res, authOptions);
  if (!session) {
    return {
      redirect: {
        destination: '/signin',
        permanent: false,
      },
    };
  }
  return { props: { isAdmin: (session.user as any).isAdmin || false } };
}; 