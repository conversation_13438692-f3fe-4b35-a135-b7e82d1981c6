"use strict";(()=>{var e={};e.id=7783,e.ids=[7783],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},2720:(e,t,o)=>{o.r(t),o.d(t,{config:()=>g,default:()=>f,routeModule:()=>y});var a={};o.r(a),o.d(a,{default:()=>b});var i=o(3433),s=o(264),r=o(584),n=o(5806),d=o(8525),l=o(2518),c=o(8580);let{url:u,name:m}=c.dashboardConfig.database,p=null;async function h(){return p||(p=await l.MongoClient.connect(u))}async function b(e,t){let o,a=await (0,n.getServerSession)(e,t,d.authOptions);if(!a)return t.status(401).json({error:"Unauthorized"});let{token:i,guildId:s,ticketCategoryId:r,ticketLogChannelId:l}=c.dashboardConfig.bot;if(!i||!s)return t.status(500).json({error:"Bot configuration missing (token/guildId)"});try{o=await h()}catch(e){return t.status(500).json({error:"Database connection failed"})}let u=o.db(m).collection("tickets");switch(e.method){case"GET":try{let e=a.user.isAdmin?{}:{creatorId:a.user.id},o=(await u.find(e).sort({createdAt:-1}).toArray()).map(e=>({...e,discordLink:`https://discord.com/channels/${s}/${e.channelId}`}));return t.status(200).json(o)}catch(e){return t.status(500).json({error:"Failed to fetch tickets"})}case"POST":{let{reason:o="",category:n="support"}=e.body??{},d=Math.floor(9e3*Math.random()+1e3),m="18plus"===n?"18plus":n,p=`ticket-${m}-${d}`.slice(0,100),h=[{id:s,type:0,deny:"1024"},{id:a.user.id,type:1,allow:"68608"}];c.dashboardConfig.dashboard?.adminRoleIds?.length&&c.dashboardConfig.dashboard.adminRoleIds.forEach(e=>{h.push({id:e,type:0,allow:"68608"})});try{let e=await fetch(`https://discord.com/api/v10/guilds/${s}/channels`,{method:"POST",headers:{Authorization:`Bot ${i}`,"Content-Type":"application/json"},body:JSON.stringify({name:p,type:0,parent_id:r??void 0,permission_overwrites:h,topic:`Ticket created by ${a.user.name} | Reason: ${o}`.slice(0,1024)})});if(!e.ok){let o;try{o=await e.json()}catch{o=await e.text()}return t.status(e.status).json({error:"Failed to create Discord channel",details:o})}let d=await e.json(),c={creatorId:a.user.id,creatorTag:a.user.name,channelId:d.id,status:"open",reason:o,category:n,createdAt:new Date},m=await u.insertOne(c);try{let e=m.insertedId.toString();await fetch(`https://discord.com/api/v10/channels/${d.id}/messages`,{method:"POST",headers:{Authorization:`Bot ${i}`,"Content-Type":"application/json"},body:JSON.stringify({embeds:[{title:"\uD83C\uDFAB Support Ticket",description:`Hello <@${c.creatorId}>! A staff member will be with you shortly.

**Category:** ${n}
**Reason:** ${o||"No reason provided"}`,color:2845872,timestamp:new Date().toISOString(),footer:{text:`Ticket ID: ${e}`}}],components:[{type:1,components:[{type:2,style:1,label:"Claim",custom_id:`ticket_claim_${e}`},{type:2,style:4,label:"Close",custom_id:`ticket_close_${e}`}]}]})})}catch(e){}if(l)try{await fetch(`https://discord.com/api/v10/channels/${l}/messages`,{method:"POST",headers:{Authorization:`Bot ${i}`,"Content-Type":"application/json"},body:JSON.stringify({embeds:[{title:"\uD83C\uDFAB New Ticket Created",color:3900150,fields:[{name:"Ticket ID",value:m.insertedId.toString(),inline:!0},{name:"Category",value:n,inline:!0},{name:"User",value:`<@${c.creatorId}>`,inline:!1},{name:"Reason",value:o||"None",inline:!1}],timestamp:new Date().toISOString()}]})})}catch(e){}return t.status(201).json({...c,_id:m.insertedId,discordLink:`https://discord.com/channels/${s}/${c.channelId}`})}catch(e){return t.status(500).json({error:"Failed to create ticket"})}}default:return t.setHeader("Allow",["GET","POST"]),t.status(405).json({error:`Method ${e.method} not allowed`})}}let f=(0,r.M)(a,"default"),g=(0,r.M)(a,"config"),y=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/tickets",pathname:"/api/discord/tickets",bundlePath:"",filename:""},userland:a})},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var a=o(5542),i=o.n(a);let s=require("next-auth/providers/discord");var r=o.n(s),n=o(8580);let d={providers:[r()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let i=!1;if(o)if((n.dashboardConfig.dashboard.admins||[]).includes(o))i=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();i=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),i=o(2115),s=o.n(i),r=o(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");n=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=2720);module.exports=o})();