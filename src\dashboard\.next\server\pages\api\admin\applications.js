"use strict";(()=>{var e={};e.id=1298,e.ids=[1298],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2125:(e,t,o)=>{o.r(t),o.d(t,{config:()=>p,default:()=>c,routeModule:()=>f});var r={};o.r(r),o.d(r,{default:()=>u});var n=o(3433),a=o(264),i=o(584),s=o(5806),d=o(2290),l=o(2518);async function u(e,t){try{let o=await (0,s.getServerSession)(e,t,Object(function(){var e=Error("Cannot find module '../../auth/[...nextauth]'");throw e.code="MODULE_NOT_FOUND",e}()));if(!o?.user?.id)return t.status(401).json({error:"Unauthorized"});if(!o.user?.isAdmin)return t.status(403).json({error:"Forbidden"});let r=(await (0,d.L)()).collection("application_submissions");if("GET"===e.method){let e=await r.find({}).toArray(),o={total:e.length,pending:e.filter(e=>"pending"===e.status).length,approved:e.filter(e=>"approved"===e.status).length,rejected:e.filter(e=>"rejected"===e.status).length,recentIncrease:0};return t.status(200).json({applications:e,stats:o})}if("PATCH"===e.method){let{applicationSubmissionId:n,action:a}=e.body;if(!n||!a)return t.status(400).json({error:"Missing applicationSubmissionId or action"});let i={status:"approve"===a?"approved":"rejected",reviewedAt:new Date,reviewedBy:o.user.id};return await r.updateOne({_id:new l.ObjectId(n)},{$set:i}),t.status(200).json({success:!0})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}!function(){var e=Error("Cannot find module '../../auth/[...nextauth]'");throw e.code="MODULE_NOT_FOUND",e}();let c=(0,i.M)(r,"default"),p=(0,i.M)(r,"config"),f=new n.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/admin/applications",pathname:"/api/admin/applications",bundlePath:"",filename:""},userland:r})},2290:(e,t,o)=>{o.d(t,{L:()=>s});var r=o(8580),n=o(2518);let a=null,i=null;async function s(){if(i)return i;let e=r.dashboardConfig.database?.url||"mongodb://localhost:27017",t=r.dashboardConfig.database?.name||"discord_bot";return a||(a=await n.MongoClient.connect(e,{...r.dashboardConfig.database?.options||{}})),i=a.db(t)}},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var r=o(9021),n=o(2115),a=o.n(n),i=o(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");s=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=2125);module.exports=o})();