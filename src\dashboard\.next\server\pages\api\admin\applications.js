"use strict";(()=>{var e={};e.id=1298,e.ids=[1298],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2125:(e,t,o)=>{o.r(t),o.d(t,{config:()=>b,default:()=>p,routeModule:()=>f});var a={};o.r(a),o.d(a,{default:()=>c});var r=o(3433),s=o(264),n=o(584),i=o(5806),d=o(8525),l=o(2290),u=o(2518);async function c(e,t){try{let o=await (0,i.getServerSession)(e,t,d.authOptions);if(!o?.user?.id)return t.status(401).json({error:"Unauthorized"});if(!o.user?.isAdmin)return t.status(403).json({error:"Forbidden"});let a=(await (0,l.L)()).collection("application_submissions");if("GET"===e.method){let e=await a.find({}).toArray(),o={total:e.length,pending:e.filter(e=>"pending"===e.status).length,approved:e.filter(e=>"approved"===e.status).length,rejected:e.filter(e=>"rejected"===e.status).length,recentIncrease:0};return t.status(200).json({applications:e,stats:o})}if("PATCH"===e.method){let{applicationSubmissionId:r,action:s}=e.body;if(!r||!s)return t.status(400).json({error:"Missing applicationSubmissionId or action"});let n={status:"approve"===s?"approved":"rejected",reviewedAt:new Date,reviewedBy:o.user.id};return await a.updateOne({_id:new u.ObjectId(r)},{$set:n}),t.status(200).json({success:!0})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let p=(0,n.M)(a,"default"),b=(0,n.M)(a,"config"),f=new r.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/admin/applications",pathname:"/api/admin/applications",bundlePath:"",filename:""},userland:a})},2290:(e,t,o)=>{o.d(t,{L:()=>i});var a=o(8580),r=o(2518);let s=null,n=null;async function i(){if(n)return n;let e=a.dashboardConfig.database?.url||"mongodb://localhost:27017",t=a.dashboardConfig.database?.name||"discord_bot";return s||(s=await r.MongoClient.connect(e,{...a.dashboardConfig.database?.options||{}})),n=s.db(t)}},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var a=o(5542),r=o.n(a);let s=require("next-auth/providers/discord");var n=o.n(s),i=o(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let r=!1;if(o)if((i.dashboardConfig.dashboard.admins||[]).includes(o))r=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();r=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=r()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),r=o(2115),s=o.n(r),n=o(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");i=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=2125);module.exports=o})();