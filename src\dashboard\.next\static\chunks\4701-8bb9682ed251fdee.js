(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4701],{7746:(e,t,s)=>{"use strict";s.d(t,{E:()=>b});var n=s(94513),i=s(94285),r=s(13215),l=s(75387),o=s(22697),a=s(610),c=s(79364),u=s(33225),f=s(2923),h=s(56915);let d=(0,u.B)("div",{baseStyle:{boxShadow:"none",backgroundClip:"padding-box",cursor:"default",color:"transparent",pointerEvents:"none",userSelect:"none","&::before, &::after, *":{visibility:"hidden"}}}),p=(0,r.Vg)("skeleton-start-color"),m=(0,r.Vg)("skeleton-end-color"),y=(0,a.i7)({from:{opacity:0},to:{opacity:1}}),g=(0,a.i7)({from:{borderColor:p.reference,background:p.reference},to:{borderColor:m.reference,background:m.reference}}),b=(0,f.R)((e,t)=>{let s={...e,fadeDuration:"number"==typeof e.fadeDuration?e.fadeDuration:.4,speed:"number"==typeof e.speed?e.speed:.8},r=(0,h.V)("Skeleton",s),a=function(){let e=(0,i.useRef)(!0);return(0,i.useEffect)(()=>{e.current=!1},[]),e.current}(),{startColor:f="",endColor:b="",isLoaded:k,fadeDuration:w,speed:v,className:S,fitContent:N,animation:E,...O}=(0,l.M)(s),[_,x]=(0,c.rd)("colors",[f,b]),A=function(e){let t=(0,i.useRef)(void 0);return(0,i.useEffect)(()=>{t.current=e},[e]),t.current}(k),T=(0,o.cx)("chakra-skeleton",S),I={..._&&{[p.variable]:_},...x&&{[m.variable]:x}};if(k){let e=a||A?"none":`${y} ${w}s`;return(0,n.jsx)(u.B.div,{ref:t,className:T,__css:{animation:e},...O})}return(0,n.jsx)(d,{ref:t,className:T,...O,__css:{width:N?"fit-content":void 0,...r,...I,_dark:{...r._dark,...I},animation:E||`${v}s linear infinite alternate ${g}`}})});b.displayName="Skeleton"},14205:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var s in t)Object.defineProperty(e,s,{enumerable:!0,get:t[s]})}(t,{default:function(){return o},noSSR:function(){return l}});let n=s(34007);s(94513),s(94285);let i=n._(s(34138));function r(e){return{default:(null==e?void 0:e.default)||e}}function l(e,t){return delete t.webpack,delete t.modules,e(t)}function o(e,t){let s=i.default,n={loading:e=>{let{error:t,isLoading:s,pastDelay:n}=e;return null}};e instanceof Promise?n.loader=()=>e:"function"==typeof e?n.loader=e:"object"==typeof e&&(n={...n,...e});let o=(n={...n,...t}).loader;return(n.loadableGenerated&&(n={...n,...n.loadableGenerated},delete n.loadableGenerated),"boolean"!=typeof n.ssr||n.ssr)?s({...n,loader:()=>null!=o?o().then(r):Promise.resolve(r(()=>null))}):(delete n.webpack,delete n.modules,l(s,n))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25680:(e,t,s)=>{"use strict";s.d(t,{r:()=>u});var n=s(94513),i=s(58714),r=s(2923),l=s(33225);let o=(0,r.R)(function(e,t){let{templateAreas:s,gap:i,rowGap:r,columnGap:o,column:a,row:c,autoFlow:u,autoRows:f,templateRows:h,autoColumns:d,templateColumns:p,...m}=e;return(0,n.jsx)(l.B.div,{ref:t,__css:{display:"grid",gridTemplateAreas:s,gridGap:i,gridRowGap:r,gridColumnGap:o,gridAutoColumns:d,gridColumn:a,gridRow:c,gridAutoFlow:u,gridAutoRows:f,gridTemplateRows:h,gridTemplateColumns:p},...m})});o.displayName="Grid";var a=s(83745),c=s(79364);let u=(0,r.R)(function(e,t){var s,r,l;let{columns:u,spacingX:f,spacingY:h,spacing:d,minChildWidth:p,...m}=e,y=(0,a.D)(),g=p?(s=p,r=y,(0,i.bk)(s,e=>{let t=(0,c.gf)("sizes",e,"number"==typeof e?`${e}px`:e)(r);return null===e?null:`repeat(auto-fit, minmax(${t}, 1fr))`})):(l=u,(0,i.bk)(l,e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`));return(0,n.jsx)(o,{ref:t,gap:d,columnGap:f,rowGap:h,templateColumns:g,...m})});u.displayName="SimpleGrid"},28245:(e,t,s)=>{"use strict";s.d(t,{j:()=>c});var n=s(94513),i=s(55100),r=s(22697),l=s(9557),o=s(2923),a=s(33225);let c=(0,o.R)((e,t)=>{let{className:s,...o}=e,c=(0,r.cx)("chakra-modal__footer",s),u=(0,l.x5)(),f=(0,i.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...u.footer});return(0,n.jsx)(a.B.footer,{ref:t,...o,__css:f,className:c})});c.displayName="ModalFooter"},34138:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return h}});let n=s(34007)._(s(94285)),i=s(89532),r=[],l=[],o=!1;function a(e){let t=e(),s={loading:!0,loaded:null,error:null};return s.promise=t.then(e=>(s.loading=!1,s.loaded=e,e)).catch(e=>{throw s.loading=!1,s.error=e,e}),s}class c{promise(){return this._res.promise}retry(){this._clearTimeouts(),this._res=this._loadFn(this._opts.loader),this._state={pastDelay:!1,timedOut:!1};let{_res:e,_opts:t}=this;e.loading&&("number"==typeof t.delay&&(0===t.delay?this._state.pastDelay=!0:this._delay=setTimeout(()=>{this._update({pastDelay:!0})},t.delay)),"number"==typeof t.timeout&&(this._timeout=setTimeout(()=>{this._update({timedOut:!0})},t.timeout))),this._res.promise.then(()=>{this._update({}),this._clearTimeouts()}).catch(e=>{this._update({}),this._clearTimeouts()}),this._update({})}_update(e){this._state={...this._state,error:this._res.error,loaded:this._res.loaded,loading:this._res.loading,...e},this._callbacks.forEach(e=>e())}_clearTimeouts(){clearTimeout(this._delay),clearTimeout(this._timeout)}getCurrentValue(){return this._state}subscribe(e){return this._callbacks.add(e),()=>{this._callbacks.delete(e)}}constructor(e,t){this._loadFn=e,this._opts=t,this._callbacks=new Set,this._delay=null,this._timeout=null,this.retry()}}function u(e){return function(e,t){let s=Object.assign({loader:null,loading:null,delay:200,timeout:null,webpack:null,modules:null},t),r=null;function a(){if(!r){let t=new c(e,s);r={getCurrentValue:t.getCurrentValue.bind(t),subscribe:t.subscribe.bind(t),retry:t.retry.bind(t),promise:t.promise.bind(t)}}return r.promise()}if(!o){let e=s.webpack&&1?s.webpack():s.modules;e&&l.push(t=>{for(let s of e)if(t.includes(s))return a()})}function u(e,t){a();let l=n.default.useContext(i.LoadableContext);l&&Array.isArray(s.modules)&&s.modules.forEach(e=>{l(e)});let o=n.default.useSyncExternalStore(r.subscribe,r.getCurrentValue,r.getCurrentValue);return n.default.useImperativeHandle(t,()=>({retry:r.retry}),[]),n.default.useMemo(()=>{var t;return o.loading||o.error?n.default.createElement(s.loading,{isLoading:o.loading,pastDelay:o.pastDelay,timedOut:o.timedOut,error:o.error,retry:r.retry}):o.loaded?n.default.createElement((t=o.loaded)&&t.default?t.default:t,e):null},[e,o])}return u.preload=()=>a(),u.displayName="LoadableComponent",n.default.forwardRef(u)}(a,e)}function f(e,t){let s=[];for(;e.length;){let n=e.pop();s.push(n(t))}return Promise.all(s).then(()=>{if(e.length)return f(e,t)})}u.preloadAll=()=>new Promise((e,t)=>{f(r).then(e,t)}),u.preloadReady=e=>(void 0===e&&(e=[]),new Promise(t=>{let s=()=>(o=!0,t());f(l,e).then(s,s)})),window.__NEXT_PRELOADREADY=u.preloadReady;let h=u},51413:(e,t,s)=>{"use strict";s.d(t,{Q:()=>i,s:()=>n});let[n,i]=(0,s(1e3).Wh)("Card")},55631:(e,t,s)=>{"use strict";s.d(t,{d:()=>f});var n=s(94513),i=s(75387),r=s(22697),l=s(94285),o=s(96027),a=s(2923),c=s(56915),u=s(33225);let f=(0,a.R)(function(e,t){let s=(0,c.o)("Switch",e),{spacing:a="0.5rem",children:f,...h}=(0,i.M)(e),{getIndicatorProps:d,getInputProps:p,getCheckboxProps:m,getRootProps:y,getLabelProps:g}=(0,o.v)(h),b=(0,l.useMemo)(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...s.container}),[s.container]),k=(0,l.useMemo)(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...s.track}),[s.track]),w=(0,l.useMemo)(()=>({userSelect:"none",marginStart:a,...s.label}),[a,s.label]);return(0,n.jsxs)(u.B.label,{...y(),className:(0,r.cx)("chakra-switch",e.className),__css:b,children:[(0,n.jsx)("input",{className:"chakra-switch__input",...p({},t)}),(0,n.jsx)(u.B.span,{...m(),className:"chakra-switch__track",__css:k,children:(0,n.jsx)(u.B.span,{__css:s.thumb,className:"chakra-switch__thumb",...d()})}),f&&(0,n.jsx)(u.B.span,{className:"chakra-switch__label",...g(),__css:w,children:f})]})});f.displayName="Switch"},59365:(e,t,s)=>{"use strict";s.d(t,{s:()=>a});var n=s(94513),i=s(22697),r=s(50614),l=s(9557),o=s(33021);let a=(0,s(2923).R)((e,t)=>{let{onClick:s,className:a,...c}=e,{onClose:u}=(0,l.k3)(),f=(0,i.cx)("chakra-modal__close-btn",a),h=(0,l.x5)();return(0,n.jsx)(o.J,{ref:t,__css:h.closeButton,className:f,onClick:(0,r.H)(s,e=>{e.stopPropagation(),u()}),...c})});a.displayName="ModalCloseButton"},59818:(e,t,s)=>{"use strict";s.d(t,{b:()=>a});var n=s(94513),i=s(22697),r=s(51413),l=s(2923),o=s(33225);let a=(0,l.R)(function(e,t){let{className:s,...l}=e,a=(0,r.Q)();return(0,n.jsx)(o.B.div,{ref:t,className:(0,i.cx)("chakra-card__body",s),__css:a.body,...l})})},68443:(e,t,s)=>{"use strict";s.d(t,{Z:()=>u});var n=s(94513),i=s(75387),r=s(22697),l=s(51413),o=s(2923),a=s(56915),c=s(33225);let u=(0,o.R)(function(e,t){let{className:s,children:o,direction:u="column",justify:f,align:h,...d}=(0,i.M)(e),p=(0,a.o)("Card",e);return(0,n.jsx)(c.B.div,{ref:t,className:(0,r.cx)("chakra-card",s),__css:{display:"flex",flexDirection:u,justifyContent:f,alignItems:h,position:"relative",minWidth:0,wordWrap:"break-word",...p.container},...d,children:(0,n.jsx)(l.s,{value:p,children:o})})})},73366:(e,t,s)=>{"use strict";let n;s.d(t,{qg:()=>tY});let i=Symbol.for("yaml.alias"),r=Symbol.for("yaml.document"),l=Symbol.for("yaml.map"),o=Symbol.for("yaml.pair"),a=Symbol.for("yaml.scalar"),c=Symbol.for("yaml.seq"),u=Symbol.for("yaml.node.type"),f=e=>!!e&&"object"==typeof e&&e[u]===i,h=e=>!!e&&"object"==typeof e&&e[u]===r,d=e=>!!e&&"object"==typeof e&&e[u]===l,p=e=>!!e&&"object"==typeof e&&e[u]===o,m=e=>!!e&&"object"==typeof e&&e[u]===a,y=e=>!!e&&"object"==typeof e&&e[u]===c;function g(e){if(e&&"object"==typeof e)switch(e[u]){case l:case c:return!0}return!1}function b(e){if(e&&"object"==typeof e)switch(e[u]){case i:case l:case a:case c:return!0}return!1}let k=e=>(m(e)||g(e))&&!!e.anchor,w=Symbol("break visit"),v=Symbol("skip children"),S=Symbol("remove node");function N(e,t){let s=x(t);h(e)?E(null,e.contents,s,Object.freeze([e]))===S&&(e.contents=null):E(null,e,s,Object.freeze([]))}function E(e,t,s,n){let i=A(e,t,s,n);if(b(i)||p(i))return T(e,n,i),E(e,i,s,n);if("symbol"!=typeof i){if(g(t)){n=Object.freeze(n.concat(t));for(let e=0;e<t.items.length;++e){let i=E(e,t.items[e],s,n);if("number"==typeof i)e=i-1;else{if(i===w)return w;i===S&&(t.items.splice(e,1),e-=1)}}}else if(p(t)){n=Object.freeze(n.concat(t));let e=E("key",t.key,s,n);if(e===w)return w;e===S&&(t.key=null);let i=E("value",t.value,s,n);if(i===w)return w;i===S&&(t.value=null)}}return i}async function O(e,t){let s=x(t);h(e)?await _(null,e.contents,s,Object.freeze([e]))===S&&(e.contents=null):await _(null,e,s,Object.freeze([]))}async function _(e,t,s,n){let i=await A(e,t,s,n);if(b(i)||p(i))return T(e,n,i),_(e,i,s,n);if("symbol"!=typeof i){if(g(t)){n=Object.freeze(n.concat(t));for(let e=0;e<t.items.length;++e){let i=await _(e,t.items[e],s,n);if("number"==typeof i)e=i-1;else{if(i===w)return w;i===S&&(t.items.splice(e,1),e-=1)}}}else if(p(t)){n=Object.freeze(n.concat(t));let e=await _("key",t.key,s,n);if(e===w)return w;e===S&&(t.key=null);let i=await _("value",t.value,s,n);if(i===w)return w;i===S&&(t.value=null)}}return i}function x(e){return"object"==typeof e&&(e.Collection||e.Node||e.Value)?Object.assign({Alias:e.Node,Map:e.Node,Scalar:e.Node,Seq:e.Node},e.Value&&{Map:e.Value,Scalar:e.Value,Seq:e.Value},e.Collection&&{Map:e.Collection,Seq:e.Collection},e):e}function A(e,t,s,n){return"function"==typeof s?s(e,t,n):d(t)?s.Map?.(e,t,n):y(t)?s.Seq?.(e,t,n):p(t)?s.Pair?.(e,t,n):m(t)?s.Scalar?.(e,t,n):f(t)?s.Alias?.(e,t,n):void 0}function T(e,t,s){let n=t[t.length-1];if(g(n))n.items[e]=s;else if(p(n))"key"===e?n.key=s:n.value=s;else if(h(n))n.contents=s;else{let e=f(n)?"alias":"scalar";throw Error(`Cannot replace node with ${e} parent`)}}N.BREAK=w,N.SKIP=v,N.REMOVE=S,O.BREAK=w,O.SKIP=v,O.REMOVE=S;let I={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"},$=e=>e.replace(/[!,[\]{}]/g,e=>I[e]);class L{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},L.defaultYaml,e),this.tags=Object.assign({},L.defaultTags,t)}clone(){let e=new L(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){let e=new L(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:L.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},L.defaultTags)}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:L.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},L.defaultTags),this.atNextDocument=!1);let s=e.trim().split(/[ \t]+/),n=s.shift();switch(n){case"%TAG":{if(2!==s.length&&(t(0,"%TAG directive should contain exactly two parts"),s.length<2))return!1;let[e,n]=s;return this.tags[e]=n,!0}case"%YAML":{if(this.yaml.explicit=!0,1!==s.length)return t(0,"%YAML directive should contain exactly one part"),!1;let[e]=s;if("1.1"===e||"1.2"===e)return this.yaml.version=e,!0;{let s=/^\d+\.\d+$/.test(e);return t(6,`Unsupported YAML version ${e}`,s),!1}}default:return t(0,`Unknown directive ${n}`,!0),!1}}tagName(e,t){if("!"===e)return"!";if("!"!==e[0])return t(`Not a valid tag: ${e}`),null;if("<"===e[1]){let s=e.slice(2,-1);return"!"===s||"!!"===s?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(">"!==e[e.length-1]&&t("Verbatim tags must end with a >"),s)}let[,s,n]=e.match(/^(.*!)([^!]*)$/s);n||t(`The ${e} tag has no suffix`);let i=this.tags[s];if(i)try{return i+decodeURIComponent(n)}catch(e){return t(String(e)),null}return"!"===s?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(let[t,s]of Object.entries(this.tags))if(e.startsWith(s))return t+$(e.substring(s.length));return"!"===e[0]?e:`!<${e}>`}toString(e){let t,s=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],n=Object.entries(this.tags);if(e&&n.length>0&&b(e.contents)){let s={};N(e.contents,(e,t)=>{b(t)&&t.tag&&(s[t.tag]=!0)}),t=Object.keys(s)}else t=[];for(let[i,r]of n)("!!"!==i||"tag:yaml.org,2002:"!==r)&&(!e||t.some(e=>e.startsWith(r)))&&s.push(`%TAG ${i} ${r}`);return s.join("\n")}}function C(e){if(/[\x00-\x19\s,[\]{}]/.test(e)){let t=JSON.stringify(e);throw Error(`Anchor must not contain whitespace or control characters: ${t}`)}return!0}function j(e){let t=new Set;return N(e,{Value(e,s){s.anchor&&t.add(s.anchor)}}),t}function B(e,t){for(let s=1;;++s){let n=`${e}${s}`;if(!t.has(n))return n}}function M(e,t,s,n){if(n&&"object"==typeof n)if(Array.isArray(n))for(let t=0,s=n.length;t<s;++t){let s=n[t],i=M(e,n,String(t),s);void 0===i?delete n[t]:i!==s&&(n[t]=i)}else if(n instanceof Map)for(let t of Array.from(n.keys())){let s=n.get(t),i=M(e,n,t,s);void 0===i?n.delete(t):i!==s&&n.set(t,i)}else if(n instanceof Set)for(let t of Array.from(n)){let s=M(e,n,t,t);void 0===s?n.delete(t):s!==t&&(n.delete(t),n.add(s))}else for(let[t,s]of Object.entries(n)){let i=M(e,n,t,s);void 0===i?delete n[t]:i!==s&&(n[t]=i)}return e.call(t,s,n)}function K(e,t,s){if(Array.isArray(e))return e.map((e,t)=>K(e,String(t),s));if(e&&"function"==typeof e.toJSON){if(!s||!k(e))return e.toJSON(t,s);let n={aliasCount:0,count:1,res:void 0};s.anchors.set(e,n),s.onCreate=e=>{n.res=e,delete s.onCreate};let i=e.toJSON(t,s);return s.onCreate&&s.onCreate(i),i}return"bigint"!=typeof e||s?.keep?e:Number(e)}L.defaultYaml={explicit:!1,version:"1.2"},L.defaultTags={"!!":"tag:yaml.org,2002:"};class D{constructor(e){Object.defineProperty(this,u,{value:e})}clone(){let e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:t,maxAliasCount:s,onAnchor:n,reviver:i}={}){if(!h(e))throw TypeError("A document argument is required");let r={anchors:new Map,doc:e,keep:!0,mapAsMap:!0===t,mapKeyWarned:!1,maxAliasCount:"number"==typeof s?s:100},l=K(this,"",r);if("function"==typeof n)for(let{count:e,res:t}of r.anchors.values())n(t,e);return"function"==typeof i?M(i,{"":l},"",l):l}}class P extends D{constructor(e){super(i),this.source=e,Object.defineProperty(this,"tag",{set(){throw Error("Alias nodes cannot have tags")}})}resolve(e,t){let s,n;for(let i of(t?.aliasResolveCache?s=t.aliasResolveCache:(s=[],N(e,{Node:(e,t)=>{(f(t)||k(t))&&s.push(t)}}),t&&(t.aliasResolveCache=s)),s)){if(i===this)break;i.anchor===this.source&&(n=i)}return n}toJSON(e,t){if(!t)return{source:this.source};let{anchors:s,doc:n,maxAliasCount:i}=t,r=this.resolve(n,t);if(!r)throw ReferenceError(`Unresolved alias (the anchor must be set before the alias): ${this.source}`);let l=s.get(r);if(l||(K(r,null,t),l=s.get(r)),!l||void 0===l.res)throw ReferenceError("This should not happen: Alias anchor was not resolved?");if(i>=0&&(l.count+=1,0===l.aliasCount&&(l.aliasCount=function e(t,s,n){if(f(s)){let e=s.resolve(t),i=n&&e&&n.get(e);return i?i.count*i.aliasCount:0}if(g(s)){let i=0;for(let r of s.items){let s=e(t,r,n);s>i&&(i=s)}return i}return p(s)?Math.max(e(t,s.key,n),e(t,s.value,n)):1}(n,r,s)),l.count*l.aliasCount>i))throw ReferenceError("Excessive alias count indicates a resource exhaustion attack");return l.res}toString(e,t,s){let n=`*${this.source}`;if(e){if(C(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source))throw Error(`Unresolved alias (the anchor must be set before the alias): ${this.source}`);if(e.implicitKey)return`${n} `}return n}}let R=e=>!e||"function"!=typeof e&&"object"!=typeof e;class q extends D{constructor(e){super(a),this.value=e}toJSON(e,t){return t?.keep?this.value:K(this.value,e,t)}toString(){return String(this.value)}}function U(e,t,s){let n;if(h(e)&&(e=e.contents),b(e))return e;if(p(e)){let t=s.schema[l].createNode?.(s.schema,null,s);return t.items.push(e),t}(e instanceof String||e instanceof Number||e instanceof Boolean||"undefined"!=typeof BigInt&&e instanceof BigInt)&&(e=e.valueOf());let{aliasDuplicateObjects:i,onAnchor:r,onTagObj:o,schema:a,sourceObjects:u}=s;if(i&&e&&"object"==typeof e){if(n=u.get(e))return n.anchor??(n.anchor=r(e)),new P(n.anchor);n={anchor:null,node:null},u.set(e,n)}t?.startsWith("!!")&&(t="tag:yaml.org,2002:"+t.slice(2));let f=function(e,t,s){if(t){let e=s.filter(e=>e.tag===t),n=e.find(e=>!e.format)??e[0];if(!n)throw Error(`Tag ${t} not found`);return n}return s.find(t=>t.identify?.(e)&&!t.format)}(e,t,a.tags);if(!f){if(e&&"function"==typeof e.toJSON&&(e=e.toJSON()),!e||"object"!=typeof e){let t=new q(e);return n&&(n.node=t),t}f=e instanceof Map?a[l]:Symbol.iterator in Object(e)?a[c]:a[l]}o&&(o(f),delete s.onTagObj);let d=f?.createNode?f.createNode(s.schema,e,s):"function"==typeof f?.nodeClass?.from?f.nodeClass.from(s.schema,e,s):new q(e);return t?d.tag=t:f.default||(d.tag=f.tag),n&&(n.node=d),d}function F(e,t,s){let n=s;for(let e=t.length-1;e>=0;--e){let s=t[e];if("number"==typeof s&&Number.isInteger(s)&&s>=0){let e=[];e[s]=n,n=e}else n=new Map([[s,n]])}return U(n,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw Error("This should not happen, please report a bug.")},schema:e,sourceObjects:new Map})}q.BLOCK_FOLDED="BLOCK_FOLDED",q.BLOCK_LITERAL="BLOCK_LITERAL",q.PLAIN="PLAIN",q.QUOTE_DOUBLE="QUOTE_DOUBLE",q.QUOTE_SINGLE="QUOTE_SINGLE";let V=e=>null==e||"object"==typeof e&&!!e[Symbol.iterator]().next().done;class G extends D{constructor(e,t){super(e),Object.defineProperty(this,"schema",{value:t,configurable:!0,enumerable:!1,writable:!0})}clone(e){let t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(t.schema=e),t.items=t.items.map(t=>b(t)||p(t)?t.clone(e):t),this.range&&(t.range=this.range.slice()),t}addIn(e,t){if(V(e))this.add(t);else{let[s,...n]=e,i=this.get(s,!0);if(g(i))i.addIn(n,t);else if(void 0===i&&this.schema)this.set(s,F(this.schema,n,t));else throw Error(`Expected YAML collection at ${s}. Remaining path: ${n}`)}}deleteIn(e){let[t,...s]=e;if(0===s.length)return this.delete(t);let n=this.get(t,!0);if(g(n))return n.deleteIn(s);throw Error(`Expected YAML collection at ${t}. Remaining path: ${s}`)}getIn(e,t){let[s,...n]=e,i=this.get(s,!0);return 0===n.length?!t&&m(i)?i.value:i:g(i)?i.getIn(n,t):void 0}hasAllNullValues(e){return this.items.every(t=>{if(!p(t))return!1;let s=t.value;return null==s||e&&m(s)&&null==s.value&&!s.commentBefore&&!s.comment&&!s.tag})}hasIn(e){let[t,...s]=e;if(0===s.length)return this.has(t);let n=this.get(t,!0);return!!g(n)&&n.hasIn(s)}setIn(e,t){let[s,...n]=e;if(0===n.length)this.set(s,t);else{let e=this.get(s,!0);if(g(e))e.setIn(n,t);else if(void 0===e&&this.schema)this.set(s,F(this.schema,n,t));else throw Error(`Expected YAML collection at ${s}. Remaining path: ${n}`)}}}let J=e=>e.replace(/^(?!$)(?: $)?/gm,"#");function Y(e,t){return/^\n+$/.test(e)?e.substring(1):t?e.replace(/^(?! *$)/gm,t):e}let W=(e,t,s)=>e.endsWith("\n")?Y(s,t):s.includes("\n")?"\n"+Y(s,t):(e.endsWith(" ")?"":" ")+s,H="flow",Q="block",X="quoted";function z(e,t,s="flow",{indentAtStart:n,lineWidth:i=80,minContentWidth:r=20,onFold:l,onOverflow:o}={}){let a,c,u;if(!i||i<0)return e;i<r&&(r=0);let f=Math.max(1+r,1+i-t.length);if(e.length<=f)return e;let h=[],d={},p=i-t.length;"number"==typeof n&&(n>i-Math.max(2,r)?h.push(0):p=i-n);let m=!1,y=-1,g=-1,b=-1;for(s===Q&&-1!==(y=Z(e,y,t.length))&&(p=y+f);u=e[y+=1];){if(s===X&&"\\"===u){switch(g=y,e[y+1]){case"x":y+=3;break;case"u":y+=5;break;case"U":y+=9;break;default:y+=1}b=y}if("\n"===u)s===Q&&(y=Z(e,y,t.length)),p=y+t.length+f,a=void 0;else{if(" "===u&&c&&" "!==c&&"\n"!==c&&"	"!==c){let t=e[y+1];t&&" "!==t&&"\n"!==t&&"	"!==t&&(a=y)}if(y>=p)if(a)h.push(a),p=a+f,a=void 0;else if(s===X){for(;" "===c||"	"===c;)c=u,u=e[y+=1],m=!0;let t=y>b+1?y-2:g-1;if(d[t])return e;h.push(t),d[t]=!0,p=t+f,a=void 0}else m=!0}c=u}if(m&&o&&o(),0===h.length)return e;l&&l();let k=e.slice(0,h[0]);for(let n=0;n<h.length;++n){let i=h[n],r=h[n+1]||e.length;0===i?k=`
${t}${e.slice(0,r)}`:(s===X&&d[i]&&(k+=`${e[i]}\\`),k+=`
${t}${e.slice(i+1,r)}`)}return k}function Z(e,t,s){let n=t,i=t+1,r=e[i];for(;" "===r||"	"===r;)if(t<i+s)r=e[++t];else{do r=e[++t];while(r&&"\n"!==r);n=t,r=e[i=t+1]}return n}let ee=(e,t)=>({indentAtStart:t?e.indent.length:e.indentAtStart,lineWidth:e.options.lineWidth,minContentWidth:e.options.minContentWidth}),et=e=>/^(%|---|\.\.\.)/m.test(e);function es(e,t){let s=JSON.stringify(e);if(t.options.doubleQuotedAsJSON)return s;let{implicitKey:n}=t,i=t.options.doubleQuotedMinMultiLineLength,r=t.indent||(et(e)?"  ":""),l="",o=0;for(let e=0,t=s[e];t;t=s[++e])if(" "===t&&"\\"===s[e+1]&&"n"===s[e+2]&&(l+=s.slice(o,e)+"\\ ",e+=1,o=e,t="\\"),"\\"===t)switch(s[e+1]){case"u":{l+=s.slice(o,e);let t=s.substr(e+2,4);switch(t){case"0000":l+="\\0";break;case"0007":l+="\\a";break;case"000b":l+="\\v";break;case"001b":l+="\\e";break;case"0085":l+="\\N";break;case"00a0":l+="\\_";break;case"2028":l+="\\L";break;case"2029":l+="\\P";break;default:"00"===t.substr(0,2)?l+="\\x"+t.substr(2):l+=s.substr(e,6)}e+=5,o=e+1}break;case"n":if(n||'"'===s[e+2]||s.length<i)e+=1;else{for(l+=s.slice(o,e)+"\n\n";"\\"===s[e+2]&&"n"===s[e+3]&&'"'!==s[e+4];)l+="\n",e+=2;l+=r," "===s[e+2]&&(l+="\\"),e+=1,o=e+1}break;default:e+=1}return l=o?l+s.slice(o):s,n?l:z(l,r,X,ee(t,!1))}function en(e,t){if(!1===t.options.singleQuote||t.implicitKey&&e.includes("\n")||/[ \t]\n|\n[ \t]/.test(e))return es(e,t);let s=t.indent||(et(e)?"  ":""),n="'"+e.replace(/'/g,"''").replace(/\n+/g,`$&
${s}`)+"'";return t.implicitKey?n:z(n,s,H,ee(t,!1))}function ei(e,t){let s,{singleQuote:n}=t.options;if(!1===n)s=es;else{let t=e.includes('"'),i=e.includes("'");s=t&&!i?en:i&&!t?es:n?en:es}return s(e,t)}try{n=RegExp("(^|(?<!\n))\n+(?!\n|$)","g")}catch{n=/\n+(?!\n|$)/g}function er({comment:e,type:t,value:s},i,r,l){let o,a,c,{blockQuote:u,commentString:f,lineWidth:h}=i.options;if(!u||/\n[\t ]+$/.test(s)||/^\s*$/.test(s))return ei(s,i);let d=i.indent||(i.forceBlockIndent||et(s)?"  ":""),p="literal"===u||"folded"!==u&&t!==q.BLOCK_FOLDED&&(t===q.BLOCK_LITERAL||!function(e,t,s){if(!t||t<0)return!1;let n=t-s,i=e.length;if(i<=n)return!1;for(let t=0,s=0;t<i;++t)if("\n"===e[t]){if(t-s>n)return!0;if(i-(s=t+1)<=n)return!1}return!0}(s,h,d.length));if(!s)return p?"|\n":">\n";for(a=s.length;a>0;--a){let e=s[a-1];if("\n"!==e&&"	"!==e&&" "!==e)break}let m=s.substring(a),y=m.indexOf("\n");-1===y?o="-":s===m||y!==m.length-1?(o="+",l&&l()):o="",m&&(s=s.slice(0,-m.length),"\n"===m[m.length-1]&&(m=m.slice(0,-1)),m=m.replace(n,`$&${d}`));let g=!1,b=-1;for(c=0;c<s.length;++c){let e=s[c];if(" "===e)g=!0;else if("\n"===e)b=c;else break}let k=s.substring(0,b<c?b+1:c);k&&(s=s.substring(k.length),k=k.replace(/\n+/g,`$&${d}`));let w=d?"2":"1",v=(g?w:"")+o;if(e&&(v+=" "+f(e.replace(/ ?[\r\n]+/g," ")),r&&r()),!p){let e=s.replace(/\n+/g,"\n$&").replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${d}`),n=!1,r=ee(i,!0);"folded"!==u&&t!==q.BLOCK_FOLDED&&(r.onOverflow=()=>{n=!0});let l=z(`${k}${e}${m}`,d,Q,r);if(!n)return`>${v}
${d}${l}`}return s=s.replace(/\n+/g,`$&${d}`),`|${v}
${d}${k}${s}${m}`}function el(e,t,s,n){let{implicitKey:i,inFlow:r}=t,l="string"==typeof e.value?e:Object.assign({},e,{value:String(e.value)}),{type:o}=e;o!==q.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(l.value)&&(o=q.QUOTE_DOUBLE);let a=e=>{switch(e){case q.BLOCK_FOLDED:case q.BLOCK_LITERAL:return i||r?ei(l.value,t):er(l,t,s,n);case q.QUOTE_DOUBLE:return es(l.value,t);case q.QUOTE_SINGLE:return en(l.value,t);case q.PLAIN:return function(e,t,s,n){let{type:i,value:r}=e,{actualString:l,implicitKey:o,indent:a,indentStep:c,inFlow:u}=t;if(o&&r.includes("\n")||u&&/[[\]{},]/.test(r))return ei(r,t);if(/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(r))return o||u||!r.includes("\n")?ei(r,t):er(e,t,s,n);if(!o&&!u&&i!==q.PLAIN&&r.includes("\n"))return er(e,t,s,n);if(et(r)){if(""===a)return t.forceBlockIndent=!0,er(e,t,s,n);else if(o&&a===c)return ei(r,t)}let f=r.replace(/\n+/g,`$&
${a}`);if(l){let e=e=>e.default&&"tag:yaml.org,2002:str"!==e.tag&&e.test?.test(f),{compat:s,tags:n}=t.doc.schema;if(n.some(e)||s?.some(e))return ei(r,t)}return o?f:z(f,a,H,ee(t,!1))}(l,t,s,n);default:return null}},c=a(o);if(null===c){let{defaultKeyType:e,defaultStringType:s}=t.options,n=i&&e||s;if(null===(c=a(n)))throw Error(`Unsupported default string type ${n}`)}return c}function eo(e,t){let s,n=Object.assign({blockQuote:!0,commentString:J,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},e.schema.toStringOptions,t);switch(n.collectionStyle){case"block":s=!1;break;case"flow":s=!0;break;default:s=null}return{anchors:new Set,doc:e,flowCollectionPadding:n.flowCollectionPadding?" ":"",indent:"",indentStep:"number"==typeof n.indent?" ".repeat(n.indent):"  ",inFlow:s,options:n}}function ea(e,t,s,n){let i;if(p(e))return e.toString(t,s,n);if(f(e)){if(t.doc.directives)return e.toString(t);if(t.resolvedAliases?.has(e))throw TypeError("Cannot stringify circular structure without alias nodes");t.resolvedAliases?t.resolvedAliases.add(e):t.resolvedAliases=new Set([e]),e=e.resolve(t.doc)}let r=b(e)?e:t.doc.createNode(e,{onTagObj:e=>i=e});i??(i=function(e,t){let s,n;if(t.tag){let s=e.filter(e=>e.tag===t.tag);if(s.length>0)return s.find(e=>e.format===t.format)??s[0]}if(m(t)){s=t.value;let i=e.filter(e=>e.identify?.(s));if(i.length>1){let e=i.filter(e=>e.test);e.length>0&&(i=e)}n=i.find(e=>e.format===t.format)??i.find(e=>!e.format)}else s=t,n=e.find(e=>e.nodeClass&&s instanceof e.nodeClass);if(!n){let e=s?.constructor?.name??(null===s?"null":typeof s);throw Error(`Tag not resolved for ${e} value`)}return n}(t.doc.schema.tags,r));let l=function(e,t,{anchors:s,doc:n}){if(!n.directives)return"";let i=[],r=(m(e)||g(e))&&e.anchor;r&&C(r)&&(s.add(r),i.push(`&${r}`));let l=e.tag??(t.default?null:t.tag);return l&&i.push(n.directives.tagString(l)),i.join(" ")}(r,i,t);l.length>0&&(t.indentAtStart=(t.indentAtStart??0)+l.length+1);let o="function"==typeof i.stringify?i.stringify(r,t,s,n):m(r)?el(r,t,s,n):r.toString(t,s,n);return l?m(r)||"{"===o[0]||"["===o[0]?`${l} ${o}`:`${l}
${t.indent}${o}`:o}function ec(e,t){("debug"===e||"warn"===e)&&console.warn(t)}let eu={identify:e=>"<<"===e||"symbol"==typeof e&&"<<"===e.description,default:"key",tag:"tag:yaml.org,2002:merge",test:/^<<$/,resolve:()=>Object.assign(new q(Symbol("<<")),{addToJSMap:eh}),stringify:()=>"<<"},ef=(e,t)=>(eu.identify(t)||m(t)&&(!t.type||t.type===q.PLAIN)&&eu.identify(t.value))&&e?.doc.schema.tags.some(e=>e.tag===eu.tag&&e.default);function eh(e,t,s){if(y(s=e&&f(s)?s.resolve(e.doc):s))for(let n of s.items)ed(e,t,n);else if(Array.isArray(s))for(let n of s)ed(e,t,n);else ed(e,t,s)}function ed(e,t,s){let n=e&&f(s)?s.resolve(e.doc):s;if(!d(n))throw Error("Merge sources must be maps or map aliases");for(let[s,i]of n.toJSON(null,e,Map))t instanceof Map?t.has(s)||t.set(s,i):t instanceof Set?t.add(s):Object.prototype.hasOwnProperty.call(t,s)||Object.defineProperty(t,s,{value:i,writable:!0,enumerable:!0,configurable:!0});return t}function ep(e,t,{key:s,value:n}){if(b(s)&&s.addToJSMap)s.addToJSMap(e,t,n);else if(ef(e,s))eh(e,t,n);else{let i=K(s,"",e);if(t instanceof Map)t.set(i,K(n,i,e));else if(t instanceof Set)t.add(i);else{let r=function(e,t,s){if(null===t)return"";if("object"!=typeof t)return String(t);if(b(e)&&s?.doc){let t=eo(s.doc,{});for(let e of(t.anchors=new Set,s.anchors.keys()))t.anchors.add(e.anchor);t.inFlow=!0,t.inStringifyKey=!0;let n=e.toString(t);if(!s.mapKeyWarned){let e=JSON.stringify(n);e.length>40&&(e=e.substring(0,36)+'..."'),ec(s.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${e}. Set mapAsMap: true to use object keys.`),s.mapKeyWarned=!0}return n}return JSON.stringify(t)}(s,i,e),l=K(n,r,e);r in t?Object.defineProperty(t,r,{value:l,writable:!0,enumerable:!0,configurable:!0}):t[r]=l}}return t}function em(e,t,s){return new ey(U(e,void 0,s),U(t,void 0,s))}class ey{constructor(e,t=null){Object.defineProperty(this,u,{value:o}),this.key=e,this.value=t}clone(e){let{key:t,value:s}=this;return b(t)&&(t=t.clone(e)),b(s)&&(s=s.clone(e)),new ey(t,s)}toJSON(e,t){let s=t?.mapAsMap?new Map:{};return ep(t,s,this)}toString(e,t,s){return e?.doc?function({key:e,value:t},s,n,i){let r,l,o,{allNullValues:a,doc:c,indent:u,indentStep:f,options:{commentString:h,indentSeq:d,simpleKeys:p}}=s,k=b(e)&&e.comment||null;if(p){if(k)throw Error("With simple keys, key nodes cannot have comments");if(g(e)||!b(e)&&"object"==typeof e)throw Error("With simple keys, collection cannot be used as a key value")}let w=!p&&(!e||k&&null==t&&!s.inFlow||g(e)||(m(e)?e.type===q.BLOCK_FOLDED||e.type===q.BLOCK_LITERAL:"object"==typeof e));s=Object.assign({},s,{allNullValues:!1,implicitKey:!w&&(p||!a),indent:u+f});let v=!1,S=!1,N=ea(e,s,()=>v=!0,()=>S=!0);if(!w&&!s.inFlow&&N.length>1024){if(p)throw Error("With simple keys, single line scalar must not span more than 1024 characters");w=!0}if(s.inFlow){if(a||null==t)return v&&n&&n(),""===N?"?":w?`? ${N}`:N}else if(a&&!p||null==t&&w)return N=`? ${N}`,k&&!v?N+=W(N,s.indent,h(k)):S&&i&&i(),N;v&&(k=null),w?(k&&(N+=W(N,s.indent,h(k))),N=`? ${N}
${u}:`):(N=`${N}:`,k&&(N+=W(N,s.indent,h(k)))),b(t)?(r=!!t.spaceBefore,l=t.commentBefore,o=t.comment):(r=!1,l=null,o=null,t&&"object"==typeof t&&(t=c.createNode(t))),s.implicitKey=!1,!w&&!k&&m(t)&&(s.indentAtStart=N.length+1),S=!1,!(!d&&f.length>=2&&!s.inFlow&&!w&&y(t))||t.flow||t.tag||t.anchor||(s.indent=s.indent.substring(2));let E=!1,O=ea(t,s,()=>E=!0,()=>S=!0),_=" ";if(k||r||l){if(_=r?"\n":"",l){let e=h(l);_+=`
${Y(e,s.indent)}`}""!==O||s.inFlow?_+=`
${s.indent}`:"\n"===_&&(_="\n\n")}else if(!w&&g(t)){let e=O[0],n=O.indexOf("\n"),i=-1!==n,r=s.inFlow??t.flow??0===t.items.length;if(i||!r){let t=!1;if(i&&("&"===e||"!"===e)){let s=O.indexOf(" ");"&"===e&&-1!==s&&s<n&&"!"===O[s+1]&&(s=O.indexOf(" ",s+1)),(-1===s||n<s)&&(t=!0)}t||(_=`
${s.indent}`)}}else(""===O||"\n"===O[0])&&(_="");return N+=_+O,s.inFlow?E&&n&&n():o&&!E?N+=W(N,s.indent,h(o)):S&&i&&i(),N}(this,e,t,s):JSON.stringify(this)}}function eg(e,t,s){return(t.inFlow??e.flow?function({items:e},t,{flowChars:s,itemIndent:n}){let{indent:i,indentStep:r,flowCollectionPadding:l,options:{commentString:o}}=t,a=Object.assign({},t,{indent:n+=r,inFlow:!0,type:null}),c=!1,u=0,f=[];for(let s=0;s<e.length;++s){let i=e[s],r=null;if(b(i))i.spaceBefore&&f.push(""),eb(t,f,i.commentBefore,!1),i.comment&&(r=i.comment);else if(p(i)){let e=b(i.key)?i.key:null;e&&(e.spaceBefore&&f.push(""),eb(t,f,e.commentBefore,!1),e.comment&&(c=!0));let s=b(i.value)?i.value:null;s?(s.comment&&(r=s.comment),s.commentBefore&&(c=!0)):null==i.value&&e?.comment&&(r=e.comment)}r&&(c=!0);let l=ea(i,a,()=>r=null);s<e.length-1&&(l+=","),r&&(l+=W(l,n,o(r))),!c&&(f.length>u||l.includes("\n"))&&(c=!0),f.push(l),u=f.length}let{start:h,end:d}=s;if(0===f.length)return h+d;if(!c){let e=f.reduce((e,t)=>e+t.length+2,2);c=t.options.lineWidth>0&&e>t.options.lineWidth}if(!c)return`${h}${l}${f.join(" ")}${l}${d}`;{let e=h;for(let t of f)e+=t?`
${r}${i}${t}`:"\n";return`${e}
${i}${d}`}}:function({comment:e,items:t},s,{blockItemPrefix:n,flowChars:i,itemIndent:r,onChompKeep:l,onComment:o}){let a,{indent:c,options:{commentString:u}}=s,f=Object.assign({},s,{indent:r,type:null}),h=!1,d=[];for(let e=0;e<t.length;++e){let i=t[e],l=null;if(b(i))!h&&i.spaceBefore&&d.push(""),eb(s,d,i.commentBefore,h),i.comment&&(l=i.comment);else if(p(i)){let e=b(i.key)?i.key:null;e&&(!h&&e.spaceBefore&&d.push(""),eb(s,d,e.commentBefore,h))}h=!1;let o=ea(i,f,()=>l=null,()=>h=!0);l&&(o+=W(o,r,u(l))),h&&l&&(h=!1),d.push(n+o)}if(0===d.length)a=i.start+i.end;else{a=d[0];for(let e=1;e<d.length;++e){let t=d[e];a+=t?`
${c}${t}`:"\n"}}return e?(a+="\n"+Y(u(e),c),o&&o()):h&&l&&l(),a})(e,t,s)}function eb({indent:e,options:{commentString:t}},s,n,i){if(n&&i&&(n=n.replace(/^\n+/,"")),n){let i=Y(t(n),e);s.push(i.trimStart())}}function ek(e,t){let s=m(t)?t.value:t;for(let n of e)if(p(n)&&(n.key===t||n.key===s||m(n.key)&&n.key.value===s))return n}class ew extends G{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(l,e),this.items=[]}static from(e,t,s){let{keepUndefined:n,replacer:i}=s,r=new this(e),l=(e,l)=>{if("function"==typeof i)l=i.call(t,e,l);else if(Array.isArray(i)&&!i.includes(e))return;(void 0!==l||n)&&r.items.push(em(e,l,s))};if(t instanceof Map)for(let[e,s]of t)l(e,s);else if(t&&"object"==typeof t)for(let e of Object.keys(t))l(e,t[e]);return"function"==typeof e.sortMapEntries&&r.items.sort(e.sortMapEntries),r}add(e,t){let s;s=p(e)?e:e&&"object"==typeof e&&"key"in e?new ey(e.key,e.value):new ey(e,e?.value);let n=ek(this.items,s.key),i=this.schema?.sortMapEntries;if(n){if(!t)throw Error(`Key ${s.key} already set`);m(n.value)&&R(s.value)?n.value.value=s.value:n.value=s.value}else if(i){let e=this.items.findIndex(e=>0>i(s,e));-1===e?this.items.push(s):this.items.splice(e,0,s)}else this.items.push(s)}delete(e){let t=ek(this.items,e);return!!t&&this.items.splice(this.items.indexOf(t),1).length>0}get(e,t){let s=ek(this.items,e),n=s?.value;return(!t&&m(n)?n.value:n)??void 0}has(e){return!!ek(this.items,e)}set(e,t){this.add(new ey(e,t),!0)}toJSON(e,t,s){let n=s?new s:t?.mapAsMap?new Map:{};for(let e of(t?.onCreate&&t.onCreate(n),this.items))ep(t,n,e);return n}toString(e,t,s){if(!e)return JSON.stringify(this);for(let e of this.items)if(!p(e))throw Error(`Map items must all be pairs; found ${JSON.stringify(e)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),eg(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:s,onComment:t})}}let ev={collection:"map",default:!0,nodeClass:ew,tag:"tag:yaml.org,2002:map",resolve:(e,t)=>(d(e)||t("Expected a mapping for this tag"),e),createNode:(e,t,s)=>ew.from(e,t,s)};class eS extends G{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(c,e),this.items=[]}add(e){this.items.push(e)}delete(e){let t=eN(e);return"number"==typeof t&&this.items.splice(t,1).length>0}get(e,t){let s=eN(e);if("number"!=typeof s)return;let n=this.items[s];return!t&&m(n)?n.value:n}has(e){let t=eN(e);return"number"==typeof t&&t<this.items.length}set(e,t){let s=eN(e);if("number"!=typeof s)throw Error(`Expected a valid index, not ${e}.`);let n=this.items[s];m(n)&&R(t)?n.value=t:this.items[s]=t}toJSON(e,t){let s=[];t?.onCreate&&t.onCreate(s);let n=0;for(let e of this.items)s.push(K(e,String(n++),t));return s}toString(e,t,s){return e?eg(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:s,onComment:t}):JSON.stringify(this)}static from(e,t,s){let{replacer:n}=s,i=new this(e);if(t&&Symbol.iterator in Object(t)){let e=0;for(let r of t){if("function"==typeof n){let s=t instanceof Set?r:String(e++);r=n.call(t,s,r)}i.items.push(U(r,void 0,s))}}return i}}function eN(e){let t=m(e)?e.value:e;return t&&"string"==typeof t&&(t=Number(t)),"number"==typeof t&&Number.isInteger(t)&&t>=0?t:null}let eE={collection:"seq",default:!0,nodeClass:eS,tag:"tag:yaml.org,2002:seq",resolve:(e,t)=>(y(e)||t("Expected a sequence for this tag"),e),createNode:(e,t,s)=>eS.from(e,t,s)},eO={identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:(e,t,s,n)=>el(e,t=Object.assign({actualString:!0},t),s,n)},e_={identify:e=>null==e,createNode:()=>new q(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new q(null),stringify:({source:e},t)=>"string"==typeof e&&e_.test.test(e)?e:t.options.nullStr},ex={identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:e=>new q("t"===e[0]||"T"===e[0]),stringify:({source:e,value:t},s)=>e&&ex.test.test(e)&&t===("t"===e[0]||"T"===e[0])?e:t?s.options.trueStr:s.options.falseStr};function eA({format:e,minFractionDigits:t,tag:s,value:n}){if("bigint"==typeof n)return String(n);let i="number"==typeof n?n:Number(n);if(!isFinite(i))return isNaN(i)?".nan":i<0?"-.inf":".inf";let r=JSON.stringify(n);if(!e&&t&&(!s||"tag:yaml.org,2002:float"===s)&&/^\d/.test(r)){let e=r.indexOf(".");e<0&&(e=r.length,r+=".");let s=t-(r.length-e-1);for(;s-- >0;)r+="0"}return r}let eT={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:eA},eI={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e),stringify(e){let t=Number(e.value);return isFinite(t)?t.toExponential():eA(e)}},e$={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(e){let t=new q(parseFloat(e)),s=e.indexOf(".");return -1!==s&&"0"===e[e.length-1]&&(t.minFractionDigits=e.length-s-1),t},stringify:eA},eL=e=>"bigint"==typeof e||Number.isInteger(e),eC=(e,t,s,{intAsBigInt:n})=>n?BigInt(e):parseInt(e.substring(t),s);function ej(e,t,s){let{value:n}=e;return eL(n)&&n>=0?s+n.toString(t):eA(e)}let eB={identify:e=>eL(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(e,t,s)=>eC(e,2,8,s),stringify:e=>ej(e,8,"0o")},eM={identify:eL,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(e,t,s)=>eC(e,0,10,s),stringify:eA},eK={identify:e=>eL(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(e,t,s)=>eC(e,2,16,s),stringify:e=>ej(e,16,"0x")},eD=[ev,eE,eO,e_,ex,eB,eM,eK,eT,eI,e$];function eP(e){return"bigint"==typeof e||Number.isInteger(e)}let eR=({value:e})=>JSON.stringify(e),eq=[ev,eE].concat([{identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:eR},{identify:e=>null==e,createNode:()=>new q(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:eR},{identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^true$|^false$/,resolve:e=>"true"===e,stringify:eR},{identify:eP,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(e,t,{intAsBigInt:s})=>s?BigInt(e):parseInt(e,10),stringify:({value:e})=>eP(e)?e.toString():JSON.stringify(e)},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:e=>parseFloat(e),stringify:eR}],{default:!0,tag:"",test:/^/,resolve:(e,t)=>(t(`Unresolved plain scalar ${JSON.stringify(e)}`),e)}),eU={identify:e=>e instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(e,t){if("function"!=typeof atob)return t("This environment does not support reading binary tags; either Buffer or atob is required"),e;{let t=atob(e.replace(/[\n\r]/g,"")),s=new Uint8Array(t.length);for(let e=0;e<t.length;++e)s[e]=t.charCodeAt(e);return s}},stringify({comment:e,type:t,value:s},n,i,r){let l;if(!s)return"";if("function"==typeof btoa){let e="";for(let t=0;t<s.length;++t)e+=String.fromCharCode(s[t]);l=btoa(e)}else throw Error("This environment does not support writing binary tags; either Buffer or btoa is required");if(t??(t=q.BLOCK_LITERAL),t!==q.QUOTE_DOUBLE){let e=Math.max(n.options.lineWidth-n.indent.length,n.options.minContentWidth),s=Math.ceil(l.length/e),i=Array(s);for(let t=0,n=0;t<s;++t,n+=e)i[t]=l.substr(n,e);l=i.join(t===q.BLOCK_LITERAL?"\n":" ")}return el({comment:e,type:t,value:l},n,i,r)}};function eF(e,t){if(y(e))for(let s=0;s<e.items.length;++s){let n=e.items[s];if(!p(n)){if(d(n)){n.items.length>1&&t("Each pair must have its own sequence indicator");let e=n.items[0]||new ey(new q(null));if(n.commentBefore&&(e.key.commentBefore=e.key.commentBefore?`${n.commentBefore}
${e.key.commentBefore}`:n.commentBefore),n.comment){let t=e.value??e.key;t.comment=t.comment?`${n.comment}
${t.comment}`:n.comment}n=e}e.items[s]=p(n)?n:new ey(n)}}else t("Expected a sequence for this tag");return e}function eV(e,t,s){let{replacer:n}=s,i=new eS(e);i.tag="tag:yaml.org,2002:pairs";let r=0;if(t&&Symbol.iterator in Object(t))for(let e of t){let l,o;if("function"==typeof n&&(e=n.call(t,String(r++),e)),Array.isArray(e))if(2===e.length)l=e[0],o=e[1];else throw TypeError(`Expected [key, value] tuple: ${e}`);else if(e&&e instanceof Object){let t=Object.keys(e);if(1===t.length)o=e[l=t[0]];else throw TypeError(`Expected tuple with one key, not ${t.length} keys`)}else l=e;i.items.push(em(l,o,s))}return i}let eG={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:eF,createNode:eV};class eJ extends eS{constructor(){super(),this.add=ew.prototype.add.bind(this),this.delete=ew.prototype.delete.bind(this),this.get=ew.prototype.get.bind(this),this.has=ew.prototype.has.bind(this),this.set=ew.prototype.set.bind(this),this.tag=eJ.tag}toJSON(e,t){if(!t)return super.toJSON(e);let s=new Map;for(let e of(t?.onCreate&&t.onCreate(s),this.items)){let n,i;if(p(e)?(n=K(e.key,"",t),i=K(e.value,n,t)):n=K(e,"",t),s.has(n))throw Error("Ordered maps must not include duplicate keys");s.set(n,i)}return s}static from(e,t,s){let n=eV(e,t,s),i=new this;return i.items=n.items,i}}eJ.tag="tag:yaml.org,2002:omap";let eY={collection:"seq",identify:e=>e instanceof Map,nodeClass:eJ,default:!1,tag:"tag:yaml.org,2002:omap",resolve(e,t){let s=eF(e,t),n=[];for(let{key:e}of s.items)m(e)&&(n.includes(e.value)?t(`Ordered maps must not include duplicate keys: ${e.value}`):n.push(e.value));return Object.assign(new eJ,s)},createNode:(e,t,s)=>eJ.from(e,t,s)};function eW({value:e,source:t},s){return t&&(e?eH:eQ).test.test(t)?t:e?s.options.trueStr:s.options.falseStr}let eH={identify:e=>!0===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new q(!0),stringify:eW},eQ={identify:e=>!1===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new q(!1),stringify:eW},eX=e=>"bigint"==typeof e||Number.isInteger(e);function ez(e,t,s,{intAsBigInt:n}){let i=e[0];if(("-"===i||"+"===i)&&(t+=1),e=e.substring(t).replace(/_/g,""),n){switch(s){case 2:e=`0b${e}`;break;case 8:e=`0o${e}`;break;case 16:e=`0x${e}`}let t=BigInt(e);return"-"===i?BigInt(-1)*t:t}let r=parseInt(e,s);return"-"===i?-1*r:r}function eZ(e,t,s){let{value:n}=e;if(eX(n)){let e=n.toString(t);return n<0?"-"+s+e.substr(1):s+e}return eA(e)}class e0 extends ew{constructor(e){super(e),this.tag=e0.tag}add(e){let t;t=p(e)?e:e&&"object"==typeof e&&"key"in e&&"value"in e&&null===e.value?new ey(e.key,null):new ey(e,null),ek(this.items,t.key)||this.items.push(t)}get(e,t){let s=ek(this.items,e);return!t&&p(s)?m(s.key)?s.key.value:s.key:s}set(e,t){if("boolean"!=typeof t)throw Error(`Expected boolean value for set(key, value) in a YAML set, not ${typeof t}`);let s=ek(this.items,e);s&&!t?this.items.splice(this.items.indexOf(s),1):!s&&t&&this.items.push(new ey(e))}toJSON(e,t){return super.toJSON(e,t,Set)}toString(e,t,s){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),t,s);throw Error("Set items must all have null values")}static from(e,t,s){let{replacer:n}=s,i=new this(e);if(t&&Symbol.iterator in Object(t))for(let e of t)"function"==typeof n&&(e=n.call(t,e,e)),i.items.push(em(e,null,s));return i}}e0.tag="tag:yaml.org,2002:set";let e1={collection:"map",identify:e=>e instanceof Set,nodeClass:e0,default:!1,tag:"tag:yaml.org,2002:set",createNode:(e,t,s)=>e0.from(e,t,s),resolve(e,t){if(d(e))if(e.hasAllNullValues(!0))return Object.assign(new e0,e);else t("Set items must all have null values");else t("Expected a mapping for this tag");return e}};function e2(e,t){let s=e[0],n="-"===s||"+"===s?e.substring(1):e,i=e=>t?BigInt(e):Number(e),r=n.replace(/_/g,"").split(":").reduce((e,t)=>e*i(60)+i(t),i(0));return"-"===s?i(-1)*r:r}function e9(e){let{value:t}=e,s=e=>e;if("bigint"==typeof t)s=e=>BigInt(e);else if(isNaN(t)||!isFinite(t))return eA(e);let n="";t<0&&(n="-",t*=s(-1));let i=s(60),r=[t%i];return t<60?r.unshift(0):(t=(t-r[0])/i,r.unshift(t%i),t>=60&&(t=(t-r[0])/i,r.unshift(t))),n+r.map(e=>String(e).padStart(2,"0")).join(":").replace(/000000\d*$/,"")}let e3={identify:e=>"bigint"==typeof e||Number.isInteger(e),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(e,t,{intAsBigInt:s})=>e2(e,s),stringify:e9},e5={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:e=>e2(e,!1),stringify:e9},e4={identify:e=>e instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(e){let t=e.match(e4.test);if(!t)throw Error("!!timestamp expects a date, starting with yyyy-mm-dd");let[,s,n,i,r,l,o]=t.map(Number),a=Date.UTC(s,n-1,i,r||0,l||0,o||0,t[7]?Number((t[7]+"00").substr(1,3)):0),c=t[8];if(c&&"Z"!==c){let e=e2(c,!1);30>Math.abs(e)&&(e*=60),a-=6e4*e}return new Date(a)},stringify:({value:e})=>e?.toISOString().replace(/(T00:00:00)?\.000Z$/,"")??""},e7=[ev,eE,eO,e_,eH,eQ,{identify:eX,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(e,t,s)=>ez(e,2,2,s),stringify:e=>eZ(e,2,"0b")},{identify:eX,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(e,t,s)=>ez(e,1,8,s),stringify:e=>eZ(e,8,"0")},{identify:eX,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(e,t,s)=>ez(e,0,10,s),stringify:eA},{identify:eX,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(e,t,s)=>ez(e,2,16,s),stringify:e=>eZ(e,16,"0x")},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:eA},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e.replace(/_/g,"")),stringify(e){let t=Number(e.value);return isFinite(t)?t.toExponential():eA(e)}},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(e){let t=new q(parseFloat(e.replace(/_/g,""))),s=e.indexOf(".");if(-1!==s){let n=e.substring(s+1).replace(/_/g,"");"0"===n[n.length-1]&&(t.minFractionDigits=n.length)}return t},stringify:eA},eU,eu,eY,eG,e1,e3,e5,e4],e8=new Map([["core",eD],["failsafe",[ev,eE,eO]],["json",eq],["yaml11",e7],["yaml-1.1",e7]]),e6={binary:eU,bool:ex,float:e$,floatExp:eI,floatNaN:eT,floatTime:e5,int:eM,intHex:eK,intOct:eB,intTime:e3,map:ev,merge:eu,null:e_,omap:eY,pairs:eG,seq:eE,set:e1,timestamp:e4},te={"tag:yaml.org,2002:binary":eU,"tag:yaml.org,2002:merge":eu,"tag:yaml.org,2002:omap":eY,"tag:yaml.org,2002:pairs":eG,"tag:yaml.org,2002:set":e1,"tag:yaml.org,2002:timestamp":e4};function tt(e,t,s){let n=e8.get(t);if(n&&!e)return s&&!n.includes(eu)?n.concat(eu):n.slice();let i=n;if(!i)if(Array.isArray(e))i=[];else{let e=Array.from(e8.keys()).filter(e=>"yaml11"!==e).map(e=>JSON.stringify(e)).join(", ");throw Error(`Unknown schema "${t}"; use one of ${e} or define customTags array`)}if(Array.isArray(e))for(let t of e)i=i.concat(t);else"function"==typeof e&&(i=e(i.slice()));return s&&(i=i.concat(eu)),i.reduce((e,t)=>{let s="string"==typeof t?e6[t]:t;if(!s){let e=JSON.stringify(t),s=Object.keys(e6).map(e=>JSON.stringify(e)).join(", ");throw Error(`Unknown custom tag ${e}; use one of ${s}`)}return e.includes(s)||e.push(s),e},[])}let ts=(e,t)=>e.key<t.key?-1:+(e.key>t.key);class tn{constructor({compat:e,customTags:t,merge:s,resolveKnownTags:n,schema:i,sortMapEntries:r,toStringDefaults:o}){this.compat=Array.isArray(e)?tt(e,"compat"):e?tt(null,e):null,this.name="string"==typeof i&&i||"core",this.knownTags=n?te:{},this.tags=tt(t,this.name,s),this.toStringOptions=o??null,Object.defineProperty(this,l,{value:ev}),Object.defineProperty(this,a,{value:eO}),Object.defineProperty(this,c,{value:eE}),this.sortMapEntries="function"==typeof r?r:!0===r?ts:null}clone(){let e=Object.create(tn.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}}class ti{constructor(e,t,s){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,u,{value:r});let n=null;"function"==typeof t||Array.isArray(t)?n=t:void 0===s&&t&&(s=t,t=void 0);let i=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,stringKeys:!1,uniqueKeys:!0,version:"1.2"},s);this.options=i;let{version:l}=i;s?._directives?(this.directives=s._directives.atDocument(),this.directives.yaml.explicit&&(l=this.directives.yaml.version)):this.directives=new L({version:l}),this.setSchema(l,s),this.contents=void 0===e?null:this.createNode(e,n,s)}clone(){let e=Object.create(ti.prototype,{[u]:{value:r}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=b(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){tr(this.contents)&&this.contents.add(e)}addIn(e,t){tr(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){let s=j(this);e.anchor=!t||s.has(t)?B(t||"a",s):t}return new P(e.anchor)}createNode(e,t,s){let n;if("function"==typeof t)e=t.call({"":e},"",e),n=t;else if(Array.isArray(t)){let e=t.filter(e=>"number"==typeof e||e instanceof String||e instanceof Number).map(String);e.length>0&&(t=t.concat(e)),n=t}else void 0===s&&t&&(s=t,t=void 0);let{aliasDuplicateObjects:i,anchorPrefix:r,flow:l,keepUndefined:o,onTagObj:a,tag:c}=s??{},{onAnchor:u,setAnchors:f,sourceObjects:h}=function(e,t){let s=[],n=new Map,i=null;return{onAnchor:n=>{s.push(n),i??(i=j(e));let r=B(t,i);return i.add(r),r},setAnchors:()=>{for(let e of s){let t=n.get(e);if("object"==typeof t&&t.anchor&&(m(t.node)||g(t.node)))t.node.anchor=t.anchor;else{let t=Error("Failed to resolve repeated object (this should not happen)");throw t.source=e,t}}},sourceObjects:n}}(this,r||"a"),d={aliasDuplicateObjects:i??!0,keepUndefined:o??!1,onAnchor:u,onTagObj:a,replacer:n,schema:this.schema,sourceObjects:h},p=U(e,c,d);return l&&g(p)&&(p.flow=!0),f(),p}createPair(e,t,s={}){return new ey(this.createNode(e,null,s),this.createNode(t,null,s))}delete(e){return!!tr(this.contents)&&this.contents.delete(e)}deleteIn(e){return V(e)?null!=this.contents&&(this.contents=null,!0):!!tr(this.contents)&&this.contents.deleteIn(e)}get(e,t){return g(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return V(e)?!t&&m(this.contents)?this.contents.value:this.contents:g(this.contents)?this.contents.getIn(e,t):void 0}has(e){return!!g(this.contents)&&this.contents.has(e)}hasIn(e){return V(e)?void 0!==this.contents:!!g(this.contents)&&this.contents.hasIn(e)}set(e,t){null==this.contents?this.contents=F(this.schema,[e],t):tr(this.contents)&&this.contents.set(e,t)}setIn(e,t){V(e)?this.contents=t:null==this.contents?this.contents=F(this.schema,Array.from(e),t):tr(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){let s;switch("number"==typeof e&&(e=String(e)),e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new L({version:"1.1"}),s={resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new L({version:e}),s={resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,s=null;break;default:{let t=JSON.stringify(e);throw Error(`Expected '1.1', '1.2' or null as first argument, but found: ${t}`)}}if(t.schema instanceof Object)this.schema=t.schema;else if(s)this.schema=new tn(Object.assign(s,t));else throw Error("With a null YAML version, the { schema: Schema } option is required")}toJS({json:e,jsonArg:t,mapAsMap:s,maxAliasCount:n,onAnchor:i,reviver:r}={}){let l={anchors:new Map,doc:this,keep:!e,mapAsMap:!0===s,mapKeyWarned:!1,maxAliasCount:"number"==typeof n?n:100},o=K(this.contents,t??"",l);if("function"==typeof i)for(let{count:e,res:t}of l.anchors.values())i(t,e);return"function"==typeof r?M(r,{"":o},"",o):o}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||0>=Number(e.indent))){let t=JSON.stringify(e.indent);throw Error(`"indent" option must be a positive integer, not ${t}`)}return function(e,t){let s=[],n=!0===t.directives;if(!1!==t.directives&&e.directives){let t=e.directives.toString(e);t?(s.push(t),n=!0):e.directives.docStart&&(n=!0)}n&&s.push("---");let i=eo(e,t),{commentString:r}=i.options;if(e.commentBefore){1!==s.length&&s.unshift("");let t=r(e.commentBefore);s.unshift(Y(t,""))}let l=!1,o=null;if(e.contents){if(b(e.contents)){if(e.contents.spaceBefore&&n&&s.push(""),e.contents.commentBefore){let t=r(e.contents.commentBefore);s.push(Y(t,""))}i.forceBlockIndent=!!e.comment,o=e.contents.comment}let t=o?void 0:()=>l=!0,a=ea(e.contents,i,()=>o=null,t);o&&(a+=W(a,"",r(o))),("|"===a[0]||">"===a[0])&&"---"===s[s.length-1]?s[s.length-1]=`--- ${a}`:s.push(a)}else s.push(ea(e.contents,i));if(e.directives?.docEnd)if(e.comment){let t=r(e.comment);t.includes("\n")?(s.push("..."),s.push(Y(t,""))):s.push(`... ${t}`)}else s.push("...");else{let t=e.comment;t&&l&&(t=t.replace(/^\n+/,"")),t&&((!l||o)&&""!==s[s.length-1]&&s.push(""),s.push(Y(r(t),"")))}return s.join("\n")+"\n"}(this,e)}}function tr(e){if(g(e))return!0;throw Error("Expected a YAML collection as document contents")}class tl extends Error{constructor(e,t,s,n){super(),this.name=e,this.code=s,this.message=n,this.pos=t}}class to extends tl{constructor(e,t,s){super("YAMLParseError",e,t,s)}}class ta extends tl{constructor(e,t,s){super("YAMLWarning",e,t,s)}}let tc=(e,t)=>s=>{if(-1===s.pos[0])return;s.linePos=s.pos.map(e=>t.linePos(e));let{line:n,col:i}=s.linePos[0];s.message+=` at line ${n}, column ${i}`;let r=i-1,l=e.substring(t.lineStarts[n-1],t.lineStarts[n]).replace(/[\n\r]+$/,"");if(r>=60&&l.length>80){let e=Math.min(r-39,l.length-79);l="…"+l.substring(e),r-=e-1}if(l.length>80&&(l=l.substring(0,79)+"…"),n>1&&/^ *$/.test(l.substring(0,r))){let s=e.substring(t.lineStarts[n-2],t.lineStarts[n-1]);s.length>80&&(s=s.substring(0,79)+"…\n"),l=s+l}if(/[^ ]/.test(l)){let e=1,t=s.linePos[1];t&&t.line===n&&t.col>i&&(e=Math.max(1,Math.min(t.col-i,80-r)));let o=" ".repeat(r)+"^".repeat(e);s.message+=`:

${l}
${o}
`}};function tu(e,{flow:t,indicator:s,next:n,offset:i,onError:r,parentIndent:l,startOnNewline:o}){let a=!1,c=o,u=o,f="",h="",d=!1,p=!1,m=null,y=null,g=null,b=null,k=null,w=null,v=null;for(let i of e)switch(p&&("space"!==i.type&&"newline"!==i.type&&"comma"!==i.type&&r(i.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),p=!1),m&&(c&&"comment"!==i.type&&"newline"!==i.type&&r(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),m=null),i.type){case"space":!t&&("doc-start"!==s||n?.type!=="flow-collection")&&i.source.includes("	")&&(m=i),u=!0;break;case"comment":{u||r(i,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let e=i.source.substring(1)||" ";f?f+=h+e:f=e,h="",c=!1;break}case"newline":c?f?f+=i.source:w&&"seq-item-ind"===s||(a=!0):h+=i.source,c=!0,d=!0,(y||g)&&(b=i),u=!0;break;case"anchor":y&&r(i,"MULTIPLE_ANCHORS","A node can have at most one anchor"),i.source.endsWith(":")&&r(i.offset+i.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),y=i,v??(v=i.offset),c=!1,u=!1,p=!0;break;case"tag":g&&r(i,"MULTIPLE_TAGS","A node can have at most one tag"),g=i,v??(v=i.offset),c=!1,u=!1,p=!0;break;case s:(y||g)&&r(i,"BAD_PROP_ORDER",`Anchors and tags must be after the ${i.source} indicator`),w&&r(i,"UNEXPECTED_TOKEN",`Unexpected ${i.source} in ${t??"collection"}`),w=i,c="seq-item-ind"===s||"explicit-key-ind"===s,u=!1;break;case"comma":if(t){k&&r(i,"UNEXPECTED_TOKEN",`Unexpected , in ${t}`),k=i,c=!1,u=!1;break}default:r(i,"UNEXPECTED_TOKEN",`Unexpected ${i.type} token`),c=!1,u=!1}let S=e[e.length-1],N=S?S.offset+S.source.length:i;return p&&n&&"space"!==n.type&&"newline"!==n.type&&"comma"!==n.type&&("scalar"!==n.type||""!==n.source)&&r(n.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),m&&(c&&m.indent<=l||n?.type==="block-map"||n?.type==="block-seq")&&r(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:k,found:w,spaceBefore:a,comment:f,hasNewline:d,anchor:y,tag:g,newlineAfterProp:b,end:N,start:v??N}}function tf(e){if(!e)return null;switch(e.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(e.source.includes("\n"))return!0;if(e.end){for(let t of e.end)if("newline"===t.type)return!0}return!1;case"flow-collection":for(let t of e.items){for(let e of t.start)if("newline"===e.type)return!0;if(t.sep){for(let e of t.sep)if("newline"===e.type)return!0}if(tf(t.key)||tf(t.value))return!0}return!1;default:return!0}}function th(e,t,s){if(t?.type==="flow-collection"){let n=t.end[0];n.indent===e&&("]"===n.source||"}"===n.source)&&tf(t)&&s(n,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}function td(e,t,s){let{uniqueKeys:n}=e.options;if(!1===n)return!1;let i="function"==typeof n?n:(e,t)=>e===t||m(e)&&m(t)&&e.value===t.value;return t.some(e=>i(e.key,s))}let tp="All mapping items must start at the same column";function tm(e,t,s,n){let i="";if(e){let r=!1,l="";for(let o of e){let{source:e,type:a}=o;switch(a){case"space":r=!0;break;case"comment":{s&&!r&&n(o,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");let t=e.substring(1)||" ";i?i+=l+t:i=t,l="";break}case"newline":i&&(l+=e),r=!0;break;default:n(o,"UNEXPECTED_TOKEN",`Unexpected ${a} at node end`)}t+=e.length}}return{comment:i,offset:t}}let ty="Block collections are not allowed within flow collections",tg=e=>e&&("block-map"===e.type||"block-seq"===e.type);function tb(e,t,s,n,i,r){let l="block-map"===s.type?function({composeNode:e,composeEmptyNode:t},s,n,i,r){let l=new(r?.nodeClass??ew)(s.schema);s.atRoot&&(s.atRoot=!1);let o=n.offset,a=null;for(let r of n.items){let{start:c,key:u,sep:f,value:h}=r,d=tu(c,{indicator:"explicit-key-ind",next:u??f?.[0],offset:o,onError:i,parentIndent:n.indent,startOnNewline:!0}),p=!d.found;if(p){if(u&&("block-seq"===u.type?i(o,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in u&&u.indent!==n.indent&&i(o,"BAD_INDENT",tp)),!d.anchor&&!d.tag&&!f){a=d.end,d.comment&&(l.comment?l.comment+="\n"+d.comment:l.comment=d.comment);continue}(d.newlineAfterProp||tf(u))&&i(u??c[c.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else d.found?.indent!==n.indent&&i(o,"BAD_INDENT",tp);s.atKey=!0;let m=d.end,y=u?e(s,u,d,i):t(s,m,c,null,d,i);s.schema.compat&&th(n.indent,u,i),s.atKey=!1,td(s,l.items,y)&&i(m,"DUPLICATE_KEY","Map keys must be unique");let g=tu(f??[],{indicator:"map-value-ind",next:h,offset:y.range[2],onError:i,parentIndent:n.indent,startOnNewline:!u||"block-scalar"===u.type});if(o=g.end,g.found){p&&(h?.type!=="block-map"||g.hasNewline||i(o,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),s.options.strict&&d.start<g.found.offset-1024&&i(y.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));let a=h?e(s,h,g,i):t(s,o,f,null,g,i);s.schema.compat&&th(n.indent,h,i),o=a.range[2];let c=new ey(y,a);s.options.keepSourceTokens&&(c.srcToken=r),l.items.push(c)}else{p&&i(y.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),g.comment&&(y.comment?y.comment+="\n"+g.comment:y.comment=g.comment);let e=new ey(y);s.options.keepSourceTokens&&(e.srcToken=r),l.items.push(e)}}return a&&a<o&&i(a,"IMPOSSIBLE","Map comment with trailing content"),l.range=[n.offset,o,a??o],l}(e,t,s,n,r):"block-seq"===s.type?function({composeNode:e,composeEmptyNode:t},s,n,i,r){let l=new(r?.nodeClass??eS)(s.schema);s.atRoot&&(s.atRoot=!1),s.atKey&&(s.atKey=!1);let o=n.offset,a=null;for(let{start:r,value:c}of n.items){let u=tu(r,{indicator:"seq-item-ind",next:c,offset:o,onError:i,parentIndent:n.indent,startOnNewline:!0});if(!u.found)if(u.anchor||u.tag||c)c&&"block-seq"===c.type?i(u.end,"BAD_INDENT","All sequence items must start at the same column"):i(o,"MISSING_CHAR","Sequence item without - indicator");else{a=u.end,u.comment&&(l.comment=u.comment);continue}let f=c?e(s,c,u,i):t(s,u.end,r,null,u,i);s.schema.compat&&th(n.indent,c,i),o=f.range[2],l.items.push(f)}return l.range=[n.offset,o,a??o],l}(e,t,s,n,r):function({composeNode:e,composeEmptyNode:t},s,n,i,r){let l="{"===n.start.source,o=l?"flow map":"flow sequence",a=new(r?.nodeClass??(l?ew:eS))(s.schema);a.flow=!0;let c=s.atRoot;c&&(s.atRoot=!1),s.atKey&&(s.atKey=!1);let u=n.offset+n.start.source.length;for(let r=0;r<n.items.length;++r){let c=n.items[r],{start:f,key:h,sep:d,value:m}=c,y=tu(f,{flow:o,indicator:"explicit-key-ind",next:h??d?.[0],offset:u,onError:i,parentIndent:n.indent,startOnNewline:!1});if(!y.found){if(!y.anchor&&!y.tag&&!d&&!m){0===r&&y.comma?i(y.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${o}`):r<n.items.length-1&&i(y.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${o}`),y.comment&&(a.comment?a.comment+="\n"+y.comment:a.comment=y.comment),u=y.end;continue}!l&&s.options.strict&&tf(h)&&i(h,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(0===r)y.comma&&i(y.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${o}`);else if(y.comma||i(y.start,"MISSING_CHAR",`Missing , between ${o} items`),y.comment){let e="";e:for(let t of f)switch(t.type){case"comma":case"space":break;case"comment":e=t.source.substring(1);break e;default:break e}if(e){let t=a.items[a.items.length-1];p(t)&&(t=t.value??t.key),t.comment?t.comment+="\n"+e:t.comment=e,y.comment=y.comment.substring(e.length+1)}}if(l||d||y.found){s.atKey=!0;let r=y.end,p=h?e(s,h,y,i):t(s,r,f,null,y,i);tg(h)&&i(p.range,"BLOCK_IN_FLOW",ty),s.atKey=!1;let g=tu(d??[],{flow:o,indicator:"map-value-ind",next:m,offset:p.range[2],onError:i,parentIndent:n.indent,startOnNewline:!1});if(g.found){if(!l&&!y.found&&s.options.strict){if(d)for(let e of d){if(e===g.found)break;if("newline"===e.type){i(e,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}y.start<g.found.offset-1024&&i(g.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else m&&("source"in m&&m.source&&":"===m.source[0]?i(m,"MISSING_CHAR",`Missing space after : in ${o}`):i(g.start,"MISSING_CHAR",`Missing , or : between ${o} items`));let b=m?e(s,m,g,i):g.found?t(s,g.end,d,null,g,i):null;b?tg(m)&&i(b.range,"BLOCK_IN_FLOW",ty):g.comment&&(p.comment?p.comment+="\n"+g.comment:p.comment=g.comment);let k=new ey(p,b);if(s.options.keepSourceTokens&&(k.srcToken=c),l)td(s,a.items,p)&&i(r,"DUPLICATE_KEY","Map keys must be unique"),a.items.push(k);else{let e=new ew(s.schema);e.flow=!0,e.items.push(k);let t=(b??p).range;e.range=[p.range[0],t[1],t[2]],a.items.push(e)}u=b?b.range[2]:g.end}else{let n=m?e(s,m,y,i):t(s,y.end,d,null,y,i);a.items.push(n),u=n.range[2],tg(m)&&i(n.range,"BLOCK_IN_FLOW",ty)}}let f=l?"}":"]",[h,...d]=n.end,m=u;if(h&&h.source===f)m=h.offset+h.source.length;else{let e=o[0].toUpperCase()+o.substring(1),t=c?`${e} must end with a ${f}`:`${e} in block collection must be sufficiently indented and end with a ${f}`;i(u,c?"MISSING_CHAR":"BAD_INDENT",t),h&&1!==h.source.length&&d.unshift(h)}if(d.length>0){let e=tm(d,m,s.options.strict,i);e.comment&&(a.comment?a.comment+="\n"+e.comment:a.comment=e.comment),a.range=[n.offset,m,e.offset]}else a.range=[n.offset,m,m];return a}(e,t,s,n,r),o=l.constructor;return"!"===i||i===o.tagName?l.tag=o.tagName:i&&(l.tag=i),l}function tk(e){let t,s;try{t=RegExp("(.*?)(?<![ 	])[ 	]*\r?\n","sy"),s=RegExp("[ 	]*(.*?)(?:(?<![ 	])[ 	]*)?\r?\n","sy")}catch{t=/(.*?)[ \t]*\r?\n/sy,s=/[ \t]*(.*?)[ \t]*\r?\n/sy}let n=t.exec(e);if(!n)return e;let i=n[1],r=" ",l=t.lastIndex;for(s.lastIndex=l;n=s.exec(e);)""===n[1]?"\n"===r?i+=r:r="\n":(i+=r+n[1],r=" "),l=s.lastIndex;let o=/[ \t]*(.*)/sy;return o.lastIndex=l,n=o.exec(e),i+r+(n?.[1]??"")}let tw={0:"\0",a:"\x07",b:"\b",e:"\x1b",f:"\f",n:"\n",r:"\r",t:"	",v:"\v",N:"\x85",_:"\xa0",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","	":"	"};function tv(e,t,s,n){let i,r,{value:l,type:o,comment:c,range:u}="block-scalar"===t.type?function(e,t,s){let n=t.offset,i=function({offset:e,props:t},s,n){if("block-scalar-header"!==t[0].type)return n(t[0],"IMPOSSIBLE","Block scalar header not found"),null;let{source:i}=t[0],r=i[0],l=0,o="",a=-1;for(let t=1;t<i.length;++t){let s=i[t];if(o||"-"!==s&&"+"!==s){let n=Number(s);!l&&n?l=n:-1===a&&(a=e+t)}else o=s}-1!==a&&n(a,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${i}`);let c=!1,u="",f=i.length;for(let e=1;e<t.length;++e){let i=t[e];switch(i.type){case"space":c=!0;case"newline":f+=i.source.length;break;case"comment":s&&!c&&n(i,"MISSING_CHAR","Comments must be separated from other tokens by white space characters"),f+=i.source.length,u=i.source.substring(1);break;case"error":n(i,"UNEXPECTED_TOKEN",i.message),f+=i.source.length;break;default:{let e=`Unexpected token in block scalar header: ${i.type}`;n(i,"UNEXPECTED_TOKEN",e);let t=i.source;t&&"string"==typeof t&&(f+=t.length)}}}return{mode:r,indent:l,chomp:o,comment:u,length:f}}(t,e.options.strict,s);if(!i)return{value:"",type:null,comment:"",range:[n,n,n]};let r=">"===i.mode?q.BLOCK_FOLDED:q.BLOCK_LITERAL,l=t.source?function(e){let t=e.split(/\n( *)/),s=t[0],n=s.match(/^( *)/),i=[n?.[1]?[n[1],s.slice(n[1].length)]:["",s]];for(let e=1;e<t.length;e+=2)i.push([t[e],t[e+1]]);return i}(t.source):[],o=l.length;for(let e=l.length-1;e>=0;--e){let t=l[e][1];if(""===t||"\r"===t)o=e;else break}if(0===o){let e="+"===i.chomp&&l.length>0?"\n".repeat(Math.max(1,l.length-1)):"",s=n+i.length;return t.source&&(s+=t.source.length),{value:e,type:r,comment:i.comment,range:[n,s,s]}}let a=t.indent+i.indent,c=t.offset+i.length,u=0;for(let t=0;t<o;++t){let[n,r]=l[t];if(""===r||"\r"===r)0===i.indent&&n.length>a&&(a=n.length);else{n.length<a&&s(c+n.length,"MISSING_CHAR","Block scalars with more-indented leading empty lines must use an explicit indentation indicator"),0===i.indent&&(a=n.length),u=t,0!==a||e.atRoot||s(c,"BAD_INDENT","Block scalar values in collections must be indented");break}c+=n.length+r.length+1}for(let e=l.length-1;e>=o;--e)l[e][0].length>a&&(o=e+1);let f="",h="",d=!1;for(let e=0;e<u;++e)f+=l[e][0].slice(a)+"\n";for(let e=u;e<o;++e){let[t,n]=l[e];c+=t.length+n.length+1;let o="\r"===n[n.length-1];if(o&&(n=n.slice(0,-1)),n&&t.length<a){let e=i.indent?"explicit indentation indicator":"first line",r=`Block scalar lines must not be less indented than their ${e}`;s(c-n.length-(o?2:1),"BAD_INDENT",r),t=""}r===q.BLOCK_LITERAL?(f+=h+t.slice(a)+n,h="\n"):t.length>a||"	"===n[0]?(" "===h?h="\n":d||"\n"!==h||(h="\n\n"),f+=h+t.slice(a)+n,h="\n",d=!0):""===n?"\n"===h?f+="\n":h="\n":(f+=h+n,h=" ",d=!1)}switch(i.chomp){case"-":break;case"+":for(let e=o;e<l.length;++e)f+="\n"+l[e][0].slice(a);"\n"!==f[f.length-1]&&(f+="\n");break;default:f+="\n"}let p=n+i.length+t.source.length;return{value:f,type:r,comment:i.comment,range:[n,p,p]}}(e,t,n):function(e,t,s){let n,i,{offset:r,type:l,source:o,end:a}=e,c=(e,t,n)=>s(r+e,t,n);switch(l){case"scalar":n=q.PLAIN,i=function(e,t){let s="";switch(e[0]){case"	":s="a tab character";break;case",":s="flow indicator character ,";break;case"%":s="directive indicator character %";break;case"|":case">":s=`block scalar indicator ${e[0]}`;break;case"@":case"`":s=`reserved character ${e[0]}`}return s&&t(0,"BAD_SCALAR_START",`Plain value cannot start with ${s}`),tk(e)}(o,c);break;case"single-quoted-scalar":var u,f;n=q.QUOTE_SINGLE,u=o,f=c,("'"!==u[u.length-1]||1===u.length)&&f(u.length,"MISSING_CHAR","Missing closing 'quote"),i=tk(u.slice(1,-1)).replace(/''/g,"'");break;case"double-quoted-scalar":n=q.QUOTE_DOUBLE,i=function(e,t){let s="";for(let n=1;n<e.length-1;++n){let i=e[n];if("\r"!==i||"\n"!==e[n+1])if("\n"===i){let{fold:t,offset:i}=function(e,t){let s="",n=e[t+1];for(;(" "===n||"	"===n||"\n"===n||"\r"===n)&&("\r"!==n||"\n"===e[t+2]);)"\n"===n&&(s+="\n"),t+=1,n=e[t+1];return s||(s=" "),{fold:s,offset:t}}(e,n);s+=t,n=i}else if("\\"===i){let i=e[++n],r=tw[i];if(r)s+=r;else if("\n"===i)for(i=e[n+1];" "===i||"	"===i;)i=e[++n+1];else if("\r"===i&&"\n"===e[n+1])for(i=e[++n+1];" "===i||"	"===i;)i=e[++n+1];else if("x"===i||"u"===i||"U"===i){let r={x:2,u:4,U:8}[i];s+=function(e,t,s,n){let i=e.substr(t,s),r=i.length===s&&/^[0-9a-fA-F]+$/.test(i)?parseInt(i,16):NaN;if(isNaN(r)){let i=e.substr(t-2,s+2);return n(t-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${i}`),i}return String.fromCodePoint(r)}(e,n+1,r,t),n+=r}else{let i=e.substr(n-1,2);t(n-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${i}`),s+=i}}else if(" "===i||"	"===i){let t=n,r=e[n+1];for(;" "===r||"	"===r;)r=e[++n+1];"\n"!==r&&("\r"!==r||"\n"!==e[n+2])&&(s+=n>t?e.slice(t,n+1):i)}else s+=i}return('"'!==e[e.length-1]||1===e.length)&&t(e.length,"MISSING_CHAR",'Missing closing "quote'),s}(o,c);break;default:return s(e,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${l}`),{value:"",type:null,comment:"",range:[r,r+o.length,r+o.length]}}let h=r+o.length,d=tm(a,h,t,s);return{value:i,type:n,comment:d.comment,range:[r,h,d.offset]}}(t,e.options.strict,n),f=s?e.directives.tagName(s.source,e=>n(s,"TAG_RESOLVE_FAILED",e)):null;i=e.options.stringKeys&&e.atKey?e.schema[a]:f?function(e,t,s,n,i){if("!"===s)return e[a];let r=[];for(let t of e.tags)if(!t.collection&&t.tag===s)if(!t.default||!t.test)return t;else r.push(t);for(let e of r)if(e.test?.test(t))return e;let l=e.knownTags[s];return l&&!l.collection?(e.tags.push(Object.assign({},l,{default:!1,test:void 0})),l):(i(n,"TAG_RESOLVE_FAILED",`Unresolved tag: ${s}`,"tag:yaml.org,2002:str"!==s),e[a])}(e.schema,l,f,s,n):"scalar"===t.type?function({atKey:e,directives:t,schema:s},n,i,r){let l=s.tags.find(t=>(!0===t.default||e&&"key"===t.default)&&t.test?.test(n))||s[a];if(s.compat){let e=s.compat.find(e=>e.default&&e.test?.test(n))??s[a];if(l.tag!==e.tag){let s=t.tagString(l.tag),n=t.tagString(e.tag);r(i,"TAG_RESOLVE_FAILED",`Value may be parsed as either ${s} or ${n}`,!0)}}return l}(e,l,t,n):e.schema[a];try{let o=i.resolve(l,e=>n(s??t,"TAG_RESOLVE_FAILED",e),e.options);r=m(o)?o:new q(o)}catch(e){n(s??t,"TAG_RESOLVE_FAILED",e instanceof Error?e.message:String(e)),r=new q(l)}return r.range=u,r.source=l,o&&(r.type=o),f&&(r.tag=f),i.format&&(r.format=i.format),c&&(r.comment=c),r}let tS={composeNode:tN,composeEmptyNode:tE};function tN(e,t,s,n){let i,r=e.atKey,{spaceBefore:l,comment:o,anchor:a,tag:c}=s,u=!0;switch(t.type){case"alias":i=function({options:e},{offset:t,source:s,end:n},i){let r=new P(s.substring(1));""===r.source&&i(t,"BAD_ALIAS","Alias cannot be an empty string"),r.source.endsWith(":")&&i(t+s.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);let l=t+s.length,o=tm(n,l,e.strict,i);return r.range=[t,l,o.offset],o.comment&&(r.comment=o.comment),r}(e,t,n),(a||c)&&n(t,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":i=tv(e,t,c,n),a&&(i.anchor=a.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":i=function(e,t,s,n,i){let r=n.tag,l=r?t.directives.tagName(r.source,e=>i(r,"TAG_RESOLVE_FAILED",e)):null;if("block-seq"===s.type){let{anchor:e,newlineAfterProp:t}=n,s=e&&r?e.offset>r.offset?e:r:e??r;s&&(!t||t.offset<s.offset)&&i(s,"MISSING_CHAR","Missing newline after block sequence props")}let o="block-map"===s.type?"map":"block-seq"===s.type?"seq":"{"===s.start.source?"map":"seq";if(!r||!l||"!"===l||l===ew.tagName&&"map"===o||l===eS.tagName&&"seq"===o)return tb(e,t,s,i,l);let a=t.schema.tags.find(e=>e.tag===l&&e.collection===o);if(!a){let n=t.schema.knownTags[l];if(!n||n.collection!==o)return n?i(r,"BAD_COLLECTION_TYPE",`${n.tag} used for ${o} collection, but expects ${n.collection??"scalar"}`,!0):i(r,"TAG_RESOLVE_FAILED",`Unresolved tag: ${l}`,!0),tb(e,t,s,i,l);t.schema.tags.push(Object.assign({},n,{default:!1})),a=n}let c=tb(e,t,s,i,l,a),u=a.resolve?.(c,e=>i(r,"TAG_RESOLVE_FAILED",e),t.options)??c,f=b(u)?u:new q(u);return f.range=c.range,f.tag=l,a?.format&&(f.format=a.format),f}(tS,e,t,s,n),a&&(i.anchor=a.source.substring(1));break;default:{let r="error"===t.type?t.message:`Unsupported token (type: ${t.type})`;n(t,"UNEXPECTED_TOKEN",r),i=tE(e,t.offset,void 0,null,s,n),u=!1}}return a&&""===i.anchor&&n(a,"BAD_ALIAS","Anchor cannot be an empty string"),r&&e.options.stringKeys&&(!m(i)||"string"!=typeof i.value||i.tag&&"tag:yaml.org,2002:str"!==i.tag)&&n(c??t,"NON_STRING_KEY","With stringKeys, all keys must be strings"),l&&(i.spaceBefore=!0),o&&("scalar"===t.type&&""===t.source?i.comment=o:i.commentBefore=o),e.options.keepSourceTokens&&u&&(i.srcToken=t),i}function tE(e,t,s,n,{spaceBefore:i,comment:r,anchor:l,tag:o,end:a},c){let u=tv(e,{type:"scalar",offset:function(e,t,s){if(t){s??(s=t.length);for(let n=s-1;n>=0;--n){let s=t[n];switch(s.type){case"space":case"comment":case"newline":e-=s.source.length;continue}for(s=t[++n];s?.type==="space";)e+=s.source.length,s=t[++n];break}}return e}(t,s,n),indent:-1,source:""},o,c);return l&&(u.anchor=l.source.substring(1),""===u.anchor&&c(l,"BAD_ALIAS","Anchor cannot be an empty string")),i&&(u.spaceBefore=!0),r&&(u.comment=r,u.range[2]=a),u}function tO(e){if("number"==typeof e)return[e,e+1];if(Array.isArray(e))return 2===e.length?e:[e[0],e[1]];let{offset:t,source:s}=e;return[t,t+("string"==typeof s?s.length:1)]}function t_(e){let t="",s=!1,n=!1;for(let i=0;i<e.length;++i){let r=e[i];switch(r[0]){case"#":t+=(""===t?"":n?"\n\n":"\n")+(r.substring(1)||" "),s=!0,n=!1;break;case"%":e[i+1]?.[0]!=="#"&&(i+=1),s=!1;break;default:s||(n=!0),s=!1}}return{comment:t,afterEmptyLine:n}}class tx{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(e,t,s,n)=>{let i=tO(e);n?this.warnings.push(new ta(i,t,s)):this.errors.push(new to(i,t,s))},this.directives=new L({version:e.version||"1.2"}),this.options=e}decorate(e,t){let{comment:s,afterEmptyLine:n}=t_(this.prelude);if(s){let i=e.contents;if(t)e.comment=e.comment?`${e.comment}
${s}`:s;else if(n||e.directives.docStart||!i)e.commentBefore=s;else if(g(i)&&!i.flow&&i.items.length>0){let e=i.items[0];p(e)&&(e=e.key);let t=e.commentBefore;e.commentBefore=t?`${s}
${t}`:s}else{let e=i.commentBefore;i.commentBefore=e?`${s}
${e}`:s}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:t_(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,s=-1){for(let t of e)yield*this.next(t);yield*this.end(t,s)}*next(e){switch(e.type){case"directive":this.directives.add(e.source,(t,s,n)=>{let i=tO(e);i[0]+=t,this.onError(i,"BAD_DIRECTIVE",s,n)}),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{let t=function(e,t,{offset:s,start:n,value:i,end:r},l){let o=new ti(void 0,Object.assign({_directives:t},e)),a={atKey:!1,atRoot:!0,directives:o.directives,options:o.options,schema:o.schema},c=tu(n,{indicator:"doc-start",next:i??r?.[0],offset:s,onError:l,parentIndent:0,startOnNewline:!0});c.found&&(o.directives.docStart=!0,i&&("block-map"===i.type||"block-seq"===i.type)&&!c.hasNewline&&l(c.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),o.contents=i?tN(a,i,c,l):tE(a,c.end,n,null,c,l);let u=o.contents.range[2],f=tm(r,u,!1,l);return f.comment&&(o.comment=f.comment),o.range=[s,u,f.offset],o}(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{let t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,s=new to(tO(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(s):this.doc.errors.push(s);break}case"doc-end":{if(!this.doc){this.errors.push(new to(tO(e),"UNEXPECTED_TOKEN","Unexpected doc-end without preceding document"));break}this.doc.directives.docEnd=!0;let t=tm(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){let e=this.doc.comment;this.doc.comment=e?`${e}
${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new to(tO(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){let e=new ti(void 0,Object.assign({_directives:this.directives},this.options));this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),e.range=[0,t,t],this.decorate(e,!1),yield e}}}let tA=Symbol("break visit"),tT=Symbol("skip children"),tI=Symbol("remove item");function t$(e,t){"type"in e&&"document"===e.type&&(e={start:e.start,value:e.value}),function e(t,s,n){let i=n(s,t);if("symbol"==typeof i)return i;for(let r of["key","value"]){let l=s[r];if(l&&"items"in l){for(let s=0;s<l.items.length;++s){let i=e(Object.freeze(t.concat([[r,s]])),l.items[s],n);if("number"==typeof i)s=i-1;else{if(i===tA)return tA;i===tI&&(l.items.splice(s,1),s-=1)}}"function"==typeof i&&"key"===r&&(i=i(s,t))}}return"function"==typeof i?i(s,t):i}(Object.freeze([]),e,t)}function tL(e){switch(e){case void 0:case" ":case"\n":case"\r":case"	":return!0;default:return!1}}t$.BREAK=tA,t$.SKIP=tT,t$.REMOVE=tI,t$.itemAtPath=(e,t)=>{let s=e;for(let[e,n]of t){let t=s?.[e];if(!t||!("items"in t))return;s=t.items[n]}return s},t$.parentCollection=(e,t)=>{let s=t$.itemAtPath(e,t.slice(0,-1)),n=t[t.length-1][0],i=s?.[n];if(i&&"items"in i)return i;throw Error("Parent collection not found")};let tC=new Set("0123456789ABCDEFabcdef"),tj=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),tB=new Set(",[]{}"),tM=new Set(" ,[]{}\n\r	"),tK=e=>!e||tM.has(e);class tD{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,t=!1){if(e){if("string"!=typeof e)throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!t;let s=this.next??"stream";for(;s&&(t||this.hasChars(1));)s=yield*this.parseNext(s)}atLineEnd(){let e=this.pos,t=this.buffer[e];for(;" "===t||"	"===t;)t=this.buffer[++e];return!t||"#"===t||"\n"===t||"\r"===t&&"\n"===this.buffer[e+1]}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let t=this.buffer[e];if(this.indentNext>0){let s=0;for(;" "===t;)t=this.buffer[++s+e];if("\r"===t){let t=this.buffer[s+e+1];if("\n"===t||!t&&!this.atEnd)return e+s+1}return"\n"!==t&&!(s>=this.indentNext)&&(t||this.atEnd)?-1:e+s}if("-"===t||"."===t){let t=this.buffer.substr(e,3);if(("---"===t||"..."===t)&&tL(this.buffer[e+3]))return -1}return e}getLine(){let e=this.lineEndPos;return(("number"!=typeof e||-1!==e&&e<this.pos)&&(e=this.buffer.indexOf("\n",this.pos),this.lineEndPos=e),-1===e)?this.atEnd?this.buffer.substring(this.pos):null:("\r"===this.buffer[e-1]&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(null===e)return this.setNext("stream");if("\uFEFF"===e[0]&&(yield*this.pushCount(1),e=e.substring(1)),"%"===e[0]){let t=e.length,s=e.indexOf("#");for(;-1!==s;){let n=e[s-1];if(" "===n||"	"===n){t=s-1;break}s=e.indexOf("#",s+1)}for(;;){let s=e[t-1];if(" "===s||"	"===s)t-=1;else break}let n=(yield*this.pushCount(t))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-n),this.pushNewline(),"stream"}if(this.atLineEnd()){let t=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-t),yield*this.pushNewline(),"stream"}return yield"\x02",yield*this.parseLineStart()}*parseLineStart(){let e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if("-"===e||"."===e){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");let e=this.peek(3);if(("---"===e||"..."===e)&&tL(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,"---"===e?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!tL(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){let[e,t]=this.peek(2);if(!t&&!this.atEnd)return this.setNext("block-start");if(("-"===e||"?"===e||":"===e)&&tL(t)){let e=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=e,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);let e=this.getLine();if(null===e)return this.setNext("doc");let t=yield*this.pushIndicators();switch(e[t]){case"#":yield*this.pushCount(e.length-t);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(tK),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return t+=yield*this.parseBlockScalarHeader(),t+=yield*this.pushSpaces(!0),yield*this.pushCount(e.length-t),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,t,s=-1;do(e=yield*this.pushNewline())>0?(t=yield*this.pushSpaces(!1),this.indentValue=s=t):t=0,t+=yield*this.pushSpaces(!0);while(e+t>0);let n=this.getLine();if(null===n)return this.setNext("flow");if((-1!==s&&s<this.indentNext&&"#"!==n[0]||0===s&&(n.startsWith("---")||n.startsWith("..."))&&tL(n[3]))&&(s!==this.indentNext-1||1!==this.flowLevel||"]"!==n[0]&&"}"!==n[0]))return this.flowLevel=0,yield"\x18",yield*this.parseLineStart();let i=0;for(;","===n[i];)i+=yield*this.pushCount(1),i+=yield*this.pushSpaces(!0),this.flowKey=!1;switch(n[i+=yield*this.pushIndicators()]){case void 0:return"flow";case"#":return yield*this.pushCount(n.length-i),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(tK),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{let e=this.charAt(1);if(this.flowKey||tL(e)||","===e)return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){let e=this.charAt(0),t=this.buffer.indexOf(e,this.pos+1);if("'"===e)for(;-1!==t&&"'"===this.buffer[t+1];)t=this.buffer.indexOf("'",t+2);else for(;-1!==t;){let e=0;for(;"\\"===this.buffer[t-1-e];)e+=1;if(e%2==0)break;t=this.buffer.indexOf('"',t+1)}let s=this.buffer.substring(0,t),n=s.indexOf("\n",this.pos);if(-1!==n){for(;-1!==n;){let e=this.continueScalar(n+1);if(-1===e)break;n=s.indexOf("\n",e)}-1!==n&&(t=n-("\r"===s[n-1]?2:1))}if(-1===t){if(!this.atEnd)return this.setNext("quoted-scalar");t=this.buffer.length}return yield*this.pushToIndex(t+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){let t=this.buffer[++e];if("+"===t)this.blockScalarKeep=!0;else if(t>"0"&&t<="9")this.blockScalarIndent=Number(t)-1;else if("-"!==t)break}return yield*this.pushUntil(e=>tL(e)||"#"===e)}*parseBlockScalar(){let e,t=this.pos-1,s=0;e:for(let n=this.pos;e=this.buffer[n];++n)switch(e){case" ":s+=1;break;case"\n":t=n,s=0;break;case"\r":{let e=this.buffer[n+1];if(!e&&!this.atEnd)return this.setNext("block-scalar");if("\n"===e)break}default:break e}if(!e&&!this.atEnd)return this.setNext("block-scalar");if(s>=this.indentNext){-1===this.blockScalarIndent?this.indentNext=s:this.indentNext=this.blockScalarIndent+(0===this.indentNext?1:this.indentNext);do{let e=this.continueScalar(t+1);if(-1===e)break;t=this.buffer.indexOf("\n",e)}while(-1!==t);if(-1===t){if(!this.atEnd)return this.setNext("block-scalar");t=this.buffer.length}}let n=t+1;for(e=this.buffer[n];" "===e;)e=this.buffer[++n];if("	"===e){for(;"	"===e||" "===e||"\r"===e||"\n"===e;)e=this.buffer[++n];t=n-1}else if(!this.blockScalarKeep)for(;;){let e=t-1,n=this.buffer[e];"\r"===n&&(n=this.buffer[--e]);let i=e;for(;" "===n;)n=this.buffer[--e];if("\n"===n&&e>=this.pos&&e+1+s>i)t=e;else break}return yield"\x1f",yield*this.pushToIndex(t+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){let e,t=this.flowLevel>0,s=this.pos-1,n=this.pos-1;for(;e=this.buffer[++n];)if(":"===e){let e=this.buffer[n+1];if(tL(e)||t&&tB.has(e))break;s=n}else if(tL(e)){let i=this.buffer[n+1];if("\r"===e&&("\n"===i?(n+=1,e="\n",i=this.buffer[n+1]):s=n),"#"===i||t&&tB.has(i))break;if("\n"===e){let e=this.continueScalar(n+1);if(-1===e)break;n=Math.max(n,e-2)}}else{if(t&&tB.has(e))break;s=n}return e||this.atEnd?(yield"\x1f",yield*this.pushToIndex(s+1,!0),t?"flow":"doc"):this.setNext("plain-scalar")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,t){let s=this.buffer.slice(this.pos,e);return s?(yield s,this.pos+=s.length,s.length):(t&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(tK))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{let e=this.flowLevel>0,t=this.charAt(1);if(tL(t)||e&&tB.has(t))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if("<"===this.charAt(1)){let e=this.pos+2,t=this.buffer[e];for(;!tL(t)&&">"!==t;)t=this.buffer[++e];return yield*this.pushToIndex(">"===t?e+1:e,!1)}{let e=this.pos+1,t=this.buffer[e];for(;t;)if(tj.has(t))t=this.buffer[++e];else if("%"===t&&tC.has(this.buffer[e+1])&&tC.has(this.buffer[e+2]))t=this.buffer[e+=3];else break;return yield*this.pushToIndex(e,!1)}}*pushNewline(){let e=this.buffer[this.pos];return"\n"===e?yield*this.pushCount(1):"\r"===e&&"\n"===this.charAt(1)?yield*this.pushCount(2):0}*pushSpaces(e){let t,s=this.pos-1;do t=this.buffer[++s];while(" "===t||e&&"	"===t);let n=s-this.pos;return n>0&&(yield this.buffer.substr(this.pos,n),this.pos=s),n}*pushUntil(e){let t=this.pos,s=this.buffer[t];for(;!e(s);)s=this.buffer[++t];return yield*this.pushToIndex(t,!1)}}function tP(e,t){for(let s=0;s<e.length;++s)if(e[s].type===t)return!0;return!1}function tR(e){for(let t=0;t<e.length;++t)switch(e[t].type){case"space":case"comment":case"newline":break;default:return t}return -1}function tq(e){switch(e?.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function tU(e){switch(e.type){case"document":return e.start;case"block-map":{let t=e.items[e.items.length-1];return t.sep??t.start}case"block-seq":return e.items[e.items.length-1].start;default:return[]}}function tF(e){if(0===e.length)return[];let t=e.length;e:for(;--t>=0;)switch(e[t].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;e[++t]?.type==="space";);return e.splice(t,e.length)}function tV(e){if("flow-seq-start"===e.start.type)for(let t of e.items)!t.sep||t.value||tP(t.start,"explicit-key-ind")||tP(t.sep,"map-value-ind")||(t.key&&(t.value=t.key),delete t.key,tq(t.value)?t.value.end?Array.prototype.push.apply(t.value.end,t.sep):t.value.end=t.sep:Array.prototype.push.apply(t.start,t.sep),delete t.sep)}class tG{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new tD,this.onNewLine=e}*parse(e,t=!1){for(let s of(this.onNewLine&&0===this.offset&&this.onNewLine(0),this.lexer.lex(e,t)))yield*this.next(s);t||(yield*this.end())}*next(e){if(this.source=e,this.atScalar){this.atScalar=!1,yield*this.step(),this.offset+=e.length;return}let t=function(e){switch(e){case"\uFEFF":return"byte-order-mark";case"\x02":return"doc-mode";case"\x18":return"flow-error-end";case"\x1f":return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case"\n":case"\r\n":return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(e[0]){case" ":case"	":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}(e);if(t)if("scalar"===t)this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&" "===e[0]&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{let t=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:t,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){let e=this.peek(1);if("doc-end"===this.type&&(!e||"doc-end"!==e.type)){for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source});return}if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}peek(e){return this.stack[this.stack.length-e]}*pop(e){let t=e??this.stack.pop();if(t)if(0===this.stack.length)yield t;else{let e=this.peek(1);switch("block-scalar"===t.type?t.indent="indent"in e?e.indent:0:"flow-collection"===t.type&&"document"===e.type&&(t.indent=0),"flow-collection"===t.type&&tV(t),e.type){case"document":e.value=t;break;case"block-scalar":e.props.push(t);break;case"block-map":{let s=e.items[e.items.length-1];if(s.value){e.items.push({start:[],key:t,sep:[]}),this.onKeyLine=!0;return}if(s.sep)s.value=t;else{Object.assign(s,{key:t,sep:[]}),this.onKeyLine=!s.explicitKey;return}break}case"block-seq":{let s=e.items[e.items.length-1];s.value?e.items.push({start:[],value:t}):s.value=t;break}case"flow-collection":{let s=e.items[e.items.length-1];!s||s.value?e.items.push({start:[],key:t,sep:[]}):s.sep?s.value=t:Object.assign(s,{key:t,sep:[]});return}default:yield*this.pop(),yield*this.pop(t)}if(("document"===e.type||"block-map"===e.type||"block-seq"===e.type)&&("block-map"===t.type||"block-seq"===t.type)){let s=t.items[t.items.length-1];s&&!s.sep&&!s.value&&s.start.length>0&&-1===tR(s.start)&&(0===t.indent||s.start.every(e=>"comment"!==e.type||e.indent<t.indent))&&("document"===e.type?e.end=s.start:e.items.push({start:s.start}),t.items.splice(-1,1))}}else yield{type:"error",offset:this.offset,source:"",message:"Tried to pop an empty stack"}}*stream(){switch(this.type){case"directive-line":yield{type:"directive",offset:this.offset,source:this.source};return;case"byte-order-mark":case"space":case"comment":case"newline":yield this.sourceToken;return;case"doc-mode":case"doc-start":{let e={type:"document",offset:this.offset,start:[]};"doc-start"===this.type&&e.start.push(this.sourceToken),this.stack.push(e);return}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":-1!==tR(e.start)?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken);return;case"anchor":case"tag":case"space":case"comment":case"newline":e.start.push(this.sourceToken);return}let t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if("map-value-ind"===this.type){let t,s=tF(tU(this.peek(2)));e.end?((t=e.end).push(this.sourceToken),delete e.end):t=[this.sourceToken];let n={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:s,key:e,sep:t}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=n}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":e.props.push(this.sourceToken);return;case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){let t=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,t.value){let s="end"in t.value?t.value.end:void 0,n=Array.isArray(s)?s[s.length-1]:void 0;n?.type==="comment"?s?.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else if(t.sep)t.sep.push(this.sourceToken);else{if(this.atIndentedComment(t.start,e.indent)){let s=e.items[e.items.length-2],n=s?.value?.end;if(Array.isArray(n)){Array.prototype.push.apply(n,t.start),n.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return}if(this.indent>=e.indent){let s=!this.onKeyLine&&this.indent===e.indent,n=s&&(t.sep||t.explicitKey)&&"seq-item-ind"!==this.type,i=[];if(n&&t.sep&&!t.value){let s=[];for(let n=0;n<t.sep.length;++n){let i=t.sep[n];switch(i.type){case"newline":s.push(n);break;case"space":break;case"comment":i.indent>e.indent&&(s.length=0);break;default:s.length=0}}s.length>=2&&(i=t.sep.splice(s[1]))}switch(this.type){case"anchor":case"tag":n||t.value?(i.push(this.sourceToken),e.items.push({start:i}),this.onKeyLine=!0):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"explicit-key-ind":t.sep||t.explicitKey?n||t.value?(i.push(this.sourceToken),e.items.push({start:i,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}):(t.start.push(this.sourceToken),t.explicitKey=!0),this.onKeyLine=!0;return;case"map-value-ind":if(t.explicitKey)if(t.sep)if(t.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(tP(t.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:i,key:null,sep:[this.sourceToken]}]});else if(tq(t.key)&&!tP(t.sep,"newline")){let e=tF(t.start),s=t.key,n=t.sep;n.push(this.sourceToken),delete t.key,delete t.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:s,sep:n}]})}else i.length>0?t.sep=t.sep.concat(i,this.sourceToken):t.sep.push(this.sourceToken);else if(tP(t.start,"newline"))Object.assign(t,{key:null,sep:[this.sourceToken]});else{let e=tF(t.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:null,sep:[this.sourceToken]}]})}else t.sep?t.value||n?e.items.push({start:i,key:null,sep:[this.sourceToken]}):tP(t.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});this.onKeyLine=!0;return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let s=this.flowScalar(this.type);n||t.value?(e.items.push({start:i,key:s,sep:[]}),this.onKeyLine=!0):t.sep?this.stack.push(s):(Object.assign(t,{key:s,sep:[]}),this.onKeyLine=!0);return}default:{let n=this.startBlockValue(e);if(n){if("block-seq"===n.type){if(!t.explicitKey&&t.sep&&!tP(t.sep,"newline"))return void(yield*this.pop({type:"error",offset:this.offset,message:"Unexpected block-seq-ind on same line with key",source:this.source}))}else s&&e.items.push({start:i});this.stack.push(n);return}}}}yield*this.pop(),yield*this.step()}*blockSequence(e){let t=e.items[e.items.length-1];switch(this.type){case"newline":if(t.value){let s="end"in t.value?t.value.end:void 0,n=Array.isArray(s)?s[s.length-1]:void 0;n?.type==="comment"?s?.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else t.start.push(this.sourceToken);return;case"space":case"comment":if(t.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(t.start,e.indent)){let s=e.items[e.items.length-2],n=s?.value?.end;if(Array.isArray(n)){Array.prototype.push.apply(n,t.start),n.push(this.sourceToken),e.items.pop();return}}t.start.push(this.sourceToken)}return;case"anchor":case"tag":if(t.value||this.indent<=e.indent)break;t.start.push(this.sourceToken);return;case"seq-item-ind":if(this.indent!==e.indent)break;t.value||tP(t.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return}if(this.indent>e.indent){let t=this.startBlockValue(e);if(t)return void this.stack.push(t)}yield*this.pop(),yield*this.step()}*flowCollection(e){let t=e.items[e.items.length-1];if("flow-error-end"===this.type){let e;do yield*this.pop(),e=this.peek(1);while(e&&"flow-collection"===e.type)}else if(0===e.end.length){switch(this.type){case"comma":case"explicit-key-ind":!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken);return;case"map-value-ind":!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]});return;case"space":case"comment":case"newline":case"anchor":case"tag":!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken);return;case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{let s=this.flowScalar(this.type);!t||t.value?e.items.push({start:[],key:s,sep:[]}):t.sep?this.stack.push(s):Object.assign(t,{key:s,sep:[]});return}case"flow-map-end":case"flow-seq-end":e.end.push(this.sourceToken);return}let s=this.startBlockValue(e);s?this.stack.push(s):(yield*this.pop(),yield*this.step())}else{let t=this.peek(2);if("block-map"!==t.type||("map-value-ind"!==this.type||t.indent!==e.indent)&&("newline"!==this.type||t.items[t.items.length-1].sep))if("map-value-ind"===this.type&&"flow-collection"!==t.type){let s=tF(tU(t));tV(e);let n=e.end.splice(1,e.end.length);n.push(this.sourceToken);let i={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:s,key:e,sep:n}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=i}else yield*this.lineEnd(e);else yield*this.pop(),yield*this.step()}}flowScalar(e){if(this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;let t=tF(tU(e));return t.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;let t=tF(tU(e));return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return"comment"===this.type&&!(this.indent<=t)&&e.every(e=>"newline"===e.type||"space"===e.type)}*documentEnd(e){"doc-mode"!==this.type&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop())}}}class tJ{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,s=this.lineStarts.length;for(;t<s;){let n=t+s>>1;this.lineStarts[n]<e?t=n+1:s=n}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(0===t)return{line:0,col:e};let n=this.lineStarts[t-1];return{line:t,col:e-n+1}}}}function tY(e,t,s){let n;"function"==typeof t?n=t:void 0===s&&t&&"object"==typeof t&&(s=t);let i=function(e,t={}){let{lineCounter:s,prettyErrors:n}=function(e){let t=!1!==e.prettyErrors;return{lineCounter:e.lineCounter||t&&new tJ||null,prettyErrors:t}}(t),i=new tG(s?.addNewLine),r=new tx(t),l=null;for(let t of r.compose(i.parse(e),!0,e.length))if(l){if("silent"!==l.options.logLevel){l.errors.push(new to(t.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}}else l=t;return n&&s&&(l.errors.forEach(tc(e,s)),l.warnings.forEach(tc(e,s))),l}(e,s);if(!i)return null;if(i.warnings.forEach(e=>ec(i.options.logLevel,e)),i.errors.length>0)if("silent"!==i.options.logLevel)throw i.errors[0];else i.errors=[];return i.toJS(Object.assign({reviver:n},s))}},77072:(e,t,s)=>{e.exports=s(14205)},89532:(e,t,s)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"LoadableContext",{enumerable:!0,get:function(){return n}});let n=s(34007)._(s(94285)).default.createContext(null)}}]);