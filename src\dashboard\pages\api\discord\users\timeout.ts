import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { REST } from '@discordjs/rest';
import { Routes } from 'discord-api-types/v10';
import { env } from '../../../../core/config/index';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req as any, res as any, authOptions);
    if (!session) {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized' });
      }
    }

    const { userId, duration, reason } = req.body;
    if (!userId || !duration) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Initialize Discord REST client
    const rest = new REST({ version: '10' }).setToken(env.DISCORD_BOT_TOKEN);
    const guildId = env.DISCORD_GUILD_ID;

    // Timeout the user
    await rest.patch(Routes.guildMember(guildId, userId), {
      body: {
        communication_disabled_until: new Date(Date.now() + duration).toISOString(),
        reason: reason || 'No reason provided'
      }
    });

    res.status(200).json({ success: true });
  } catch (error) {
    console.error('Error timing out user:', error);
    res.status(500).json({ error: 'Failed to timeout user' });
  }
} 