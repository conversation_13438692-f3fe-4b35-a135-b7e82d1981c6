// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient, ObjectId } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

// MongoDB connection
let cachedClient = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  try {
    const client = await MongoClient.connect(dashboardConfig.database.url, {
      ...dashboardConfig.database.options,
    });
    cachedClient = client;
    return client;
  } catch (error) {
    console.error('Failed to connect to MongoDB:', error);
    throw error;
  }
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Only admin users can read/update/delete applications. Submitting (POST) is allowed for any logged-in user.
  if (req.method !== 'POST' && !(session.user as any).isAdmin) {
    return res.status(403).json({ error: 'Forbidden - Admin access required' });
  }

  let client;
  try {
    client = await connectToDatabase();
    const db = client.db(dashboardConfig.database.name);
    const applications = db.collection('applications');

    if (req.method === 'POST') {
      const { userId, username, answers, quizAnswers, timezone, age, hoursPerWeek, extraInfo } = req.body;
      
      // Validate required fields
      if (!userId || !answers?.statement || !quizAnswers || !timezone || !age || !hoursPerWeek) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      try {
        // Check if user has already applied
        const existingApp = await applications.findOne({ userId });
        if (existingApp) {
          return res.status(400).json({ error: 'You have already submitted an application' });
        }

        const result = await applications.insertOne({
          userId,
          username,
          answers,
          quizAnswers,
          timezone,
          age,
          hoursPerWeek,
          extraInfo,
          date: new Date(),
          status: 'pending'
        });

        return res.status(200).json({ success: true, id: result.insertedId });
      } catch (error) {
        console.error('Failed to save application:', error);
        return res.status(500).json({ error: 'Failed to save application' });
      }
    }

    if (req.method === 'GET') {
      const allApplications = await applications.find().sort({ date: -1 }).toArray();
      return res.status(200).json(allApplications);
    }

    if (req.method === 'PUT') {
      const { applicationId } = req.query;
      const { status } = req.body;

      if (!applicationId || typeof applicationId !== 'string' || !status) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const result = await applications.updateOne(
        { _id: new ObjectId(applicationId) },
        { $set: { status } }
      );

      if (result.matchedCount === 0) {
        return res.status(404).json({ error: 'Application not found' });
      }

      return res.status(200).json({ message: 'Application updated successfully' });
    }

    if (req.method === 'DELETE') {
      const { applicationId } = req.query;

      if (!applicationId || typeof applicationId !== 'string') {
        return res.status(400).json({ error: 'Missing application ID' });
      }

      const result = await applications.deleteOne({ _id: new ObjectId(applicationId) });

      if (result.deletedCount === 0) {
        return res.status(404).json({ error: 'Application not found' });
      }

      return res.status(200).json({ message: 'Application deleted successfully' });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 