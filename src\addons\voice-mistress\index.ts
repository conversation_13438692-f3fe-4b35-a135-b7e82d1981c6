import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import { ChannelType, PermissionFlagsBits, VoiceState, ButtonInteraction, ModalSubmitInteraction, MessageFlags, VoiceChannel } from 'discord.js';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';
import { ChannelUtils } from './utils/channelUtils.js';
import { handleVCButtonInteraction } from './events/buttonHandler.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  events: [
    {
      name: 'voiceStateUpdate',
      once: false,
      execute: async (oldState: VoiceState, newState: VoiceState) => {
        const logger = Logger.createAddonLogger(config.addon.name);

        // Handle user joining the trigger channel
        if (newState.channelId === config.settings.triggerChannelId) {
          try {
            // Check bot permissions
            const botMember = newState.guild.members.cache.get(newState.client.user.id);
            if (!botMember?.permissions.has(PermissionFlagsBits.ManageChannels)) {
              logger.error('Missing required permissions', {
                permission: 'MANAGE_CHANNELS',
                user: newState.member?.user.tag
              });
              return;
            }

            // Create a new temporary voice channel
            const voiceChannel = await newState.guild.channels.create({
              name: `${config.settings.defaultChannelName}${newState.member?.displayName}`,
              type: ChannelType.GuildVoice,
              parent: config.settings.categoryId,
              userLimit: config.settings.defaultUserLimit,
              permissionOverwrites: [
                {
                  id: newState.member!.id,
                  allow: [PermissionFlagsBits.ManageChannels, PermissionFlagsBits.MoveMembers]
                }
              ]
            });

            // Move the user to the new voice channel
            await newState.setChannel(voiceChannel);

            // Add to our channel tracking system
            ChannelUtils.addTempChannel(voiceChannel.id, newState.member!.id, newState.guild.id);

            // Store the creation timestamp
            const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
            if (tempChannelData) {
              (tempChannelData as any).createdTimestamp = Math.floor(Date.now() / 1000);
            }

            // Temporary voice channel created successfully

            // Send the control panel directly to the voice channel's text chat
            await sendControlPanel(voiceChannel, newState.member!.user);

          } catch (error) {
            logger.error('Failed to create temporary channel', {
              error: error instanceof Error ? error.message : 'Unknown error',
              user: newState.member?.user.tag,
              stack: error instanceof Error ? error.stack : undefined
            });
          }
        }

        // Handle control panel updates when users join/leave and channel cleanup
        if (oldState.channel && ChannelUtils.isTempChannel(oldState.channelId!)) {
          // Update control panel when user count changes
          if (oldState.channel.type === ChannelType.GuildVoice) {
            try {
              const tempChannelData = ChannelUtils.getTempChannel(oldState.channelId!);
              if (tempChannelData) {
                const owner = await oldState.guild.members.fetch(tempChannelData.ownerId).catch(() => null);
                if (owner) {
                  await updateControlPanel(oldState.channel, owner.user);
                }

                // Normal leave - password cache remains intact for easy rejoin
              }
            } catch (error) {
              logger.error('Failed to update control panel on user leave', {
                error: error instanceof Error ? error.message : 'Unknown error',
                channelId: oldState.channelId
              });
            }

            // Check if the channel is empty for deletion
            if (oldState.channel?.members && oldState.channel.members.size === 0) {
            setTimeout(async () => {
              try {
                // Double check if still empty after delay
                const channel = await oldState.guild.channels.fetch(oldState.channelId!).catch(() => null);
                if (channel && channel.type === ChannelType.GuildVoice && channel.members.size === 0) {
                  try {
                    // Delete the voice channel (text chat is built-in, so it gets deleted automatically)
                    await channel.delete();
                  } catch (error) {
                    // If channel is already gone, just clean up our tracking
                    if (error instanceof Error && 'code' in error && error.code === 10003) { // Unknown Channel
                      logger.info('Channel already deleted, cleaning up tracking data', {
                        channelId: oldState.channelId
                      });
                    } else {
                      throw error; // Re-throw other errors
                    }
                  }
                  // Always clean up our tracking data
                  ChannelUtils.removeTempChannel(oldState.channelId!);
                }
              } catch (error) {
                logger.error('Failed to delete temporary channel', {
                  error: error instanceof Error ? error.message : 'Unknown error',
                  channelId: oldState.channelId,
                  stack: error instanceof Error ? error.stack : undefined
                });
              }
            }, config.settings.deleteDelay);
            }
          }
        }

        // Handle password protection and waiting rooms for temp channels
        if (newState.channel && ChannelUtils.isTempChannel(newState.channelId!)) {
          if (newState.channel.type === ChannelType.GuildVoice && newState.channelId !== config.settings.triggerChannelId) {
            try {
              const tempChannelData = ChannelUtils.getTempChannel(newState.channelId!);
              if (tempChannelData) {
                const isOwner = newState.member!.id === tempChannelData.ownerId;
                
                // Check for one-time authorization first
                if (ChannelUtils.isJoinAuthorized(newState.channelId!, newState.member!.id)) {
                                  // User joined with authorization
                  
                  // Update control panel for authorized join
                  const owner = await newState.guild.members.fetch(tempChannelData.ownerId).catch(() => null);
                  if (owner) {
                    await updateControlPanel(newState.channel, owner.user);
                  }
                  return;
                }
                
                // Check if channel has password and user is not owner
                if (ChannelUtils.hasPassword(newState.channelId!) && !isOwner) {
                  // Check for kick protection first
                  if (ChannelUtils.isKickProtected(newState.channelId!, newState.member!.id)) {
                    const timeLeft = ChannelUtils.getKickProtectionTimeLeft(newState.channelId!, newState.member!.id);
                    // User blocked by kick protection
                    
                    // Move them back out and send a message
                    await newState.setChannel(null);
                    
                    // Try to send them a DM about the kick protection
                    try {
                      await newState.member!.send(`🚫 **Kick Protection Active**\nYou were recently kicked from **${newState.channel.name}** and are temporarily blocked for ${timeLeft} seconds to allow password changes.`);
                    } catch (error) {
                      // DM failed, ignore
                    }
                    return;
                  }
                  
                  // Check if user has cached password
                  if (ChannelUtils.hasCachedPassword(newState.channelId!, newState.member!.id)) {
                    // User joining with cached password
                    
                    // Allow them to join, password is cached
                    const owner = await newState.guild.members.fetch(tempChannelData.ownerId).catch(() => null);
                    if (owner) {
                      await updateControlPanel(newState.channel, owner.user);
                    }
                    return;
                  }
                  
                  // User attempting to join password-protected channel
                  
                  // Create waiting room
                  await ChannelUtils.createWaitingRoom(newState.member!, newState.channel);
                  return;
                }
                
                // Normal join - update control panel
                const owner = await newState.guild.members.fetch(tempChannelData.ownerId).catch(() => null);
                if (owner) {
                  await updateControlPanel(newState.channel, owner.user);
                }
              }
            } catch (error) {
              logger.error('Failed to handle temp channel join', {
                error: error instanceof Error ? error.message : 'Unknown error',
                channelId: newState.channelId
              });
            }
          }
        }

        // Handle waiting room cleanup when users leave
        if (oldState.channel && ChannelUtils.isWaitingRoom(oldState.channelId!)) {
          if (oldState.channel?.members && oldState.channel.members.size === 0) {
            setTimeout(async () => {
              try {
                const channel = await oldState.guild.channels.fetch(oldState.channelId!).catch(() => null);
                if (channel && channel.type === ChannelType.GuildVoice && channel.members.size === 0) {
                  try {
                    await ChannelUtils.cleanupWaitingRoom(channel as VoiceChannel);
                  } catch (error) {
                    // If channel is already gone, just clean up our tracking
                    if (error instanceof Error && 'code' in error && error.code === 10003) { // Unknown Channel
                      logger.info('Waiting room already deleted, cleaning up tracking data', {
                        channelId: oldState.channelId
                      });
                      await ChannelUtils.cleanupWaitingRoom(channel as VoiceChannel);
                    } else {
                      logger.error('Failed to cleanup waiting room', {
                        error: error instanceof Error ? error.message : 'Unknown error',
                        channelId: oldState.channelId
                      });
                    }
                  }
                }
              } catch (error) {
                logger.error('Failed to cleanup waiting room', {
                  error: error instanceof Error ? error.message : 'Unknown error',
                  channelId: oldState.channelId
                });
              }
            }, 2000); // Shorter delay for waiting rooms
          }
        }
      }
    },

    {
      name: 'interactionCreate',
      once: false,
      execute: async (interaction) => {
        // Handle button interactions for voice channel controls
        if (interaction.isButton() && interaction.customId.startsWith('vc_')) {
          await handleVCButtonInteraction(interaction as ButtonInteraction);
        }
        
        // Handle modal submissions for voice channel settings and password entry
        if (interaction.isModalSubmit() && (interaction.customId.endsWith('_modal') || interaction.customId.startsWith('vc_submit_password'))) {
          await handleModalSubmission(interaction as ModalSubmitInteraction);
        }

        // Handle user select menus for voice channel access control
        if ((interaction.isUserSelectMenu() || interaction.isStringSelectMenu()) && interaction.customId.startsWith('vc_')) {
          await handleUserSelectMenu(interaction);
        }
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);

    // Initialize database collection if needed
    if (bot.database && config.database?.collections) {
      try {
        const collection = bot.database.db.collection('temp_channels');
        await collection.createIndex({ userId: 1, channelId: 1 });
        // Database collection initialized
      } catch (error) {
        logger.error('Failed to initialize database', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });
      }
    }

    // Auto-cleanup orphaned temp channels on startup
    setTimeout(async () => {
      await cleanupOrphanedChannels(bot, logger);
    }, 5000); // Wait 5 seconds for bot to be fully ready

    // Periodic cleanup every 30 minutes
    setInterval(async () => {
      await cleanupOrphanedChannels(bot, logger);
    }, 30 * 60 * 1000); // 30 minutes

    // Validate required settings
    if (!config.settings.categoryId || !config.settings.triggerChannelId) {
      logger.error('Missing required configuration', {
        categoryId: config.settings.categoryId || 'Not set',
        triggerChannelId: config.settings.triggerChannelId || 'Not set'
      });
      return;
    }

    // Validate channel existence when bot is ready
    setTimeout(async () => {
      try {
        const guild = bot.client.guilds.cache.first();
        if (guild) {
          const triggerChannel = await guild.channels.fetch(config.settings.triggerChannelId);
          const category = await guild.channels.fetch(config.settings.categoryId);
          
          if (!triggerChannel || !category) {
            logger.error('Channel validation failed', {
              triggerChannel: triggerChannel?.name ?? 'Not found',
              triggerChannelId: config.settings.triggerChannelId,
              category: category?.name ?? 'Not found',
              categoryId: config.settings.categoryId
            });
            return;
          }
          
          // Temporary voice channels initialized successfully
        }
      } catch (error) {
        logger.error('Channel validation error', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        });
      }
    }, 2000); // Wait 2 seconds for bot to be fully ready
  },

  onUnload: async (_bot: BotInstance) => {
    // Addon unloaded
  }
};

// Function to build the control panel container
function buildControlPanelContainer(voiceChannel: any, owner: any) {
  const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
  
  return {
    type: 17, // Container
    accent_color: 0x0099FF,
    components: [
      {
        type: 10, // Text - Header
        content: `# 🔊 Voice Channel Control Panel\n🎉 **${owner}**, your temporary voice channel is now **live!**`
      },
      {
        type: 10, // Text - Channel info
        content: `> 📢 **Channel:** \`${voiceChannel.name}\`\n> ⏰ **Created:** <t:${(tempChannelData as any)?.createdTimestamp || Math.floor(Date.now() / 1000)}:R>`
      },
            {
        type: 10, // Text - Status header
        content: `## 📊 Channel Status Dashboard`
      },
      {
        type: 10, // Text - Owner info
        content: `🏆 **Owner** → ${owner.tag}`
      },
      {
        type: 10, // Text - Security and visibility
        content: `${tempChannelData?.settings.locked ? '🔒 **Security** → \`LOCKED\` 🛡️' : '🔓 **Security** → \`OPEN\` ✅'}\n${tempChannelData?.settings.hidden ? '👁️‍🗨️ **Visibility** → \`HIDDEN\` 🫥' : '👁️ **Visibility** → \`PUBLIC\` 🌟'}`
      },
      {
        type: 10, // Text - Password and capacity
        content: `${ChannelUtils.hasPassword(voiceChannel.id) ? '🔑 **Password** → \`PASSWORD PROTECTED\` 🛡️' : '🔑 **Password** → \`NO PASSWORD\` 🚪'}\n👥 **Capacity** → \`${voiceChannel.members?.size || 0}/${voiceChannel.userLimit === 0 ? 'UNLIMITED' : voiceChannel.userLimit}\` 👨‍👩‍👧‍👦`
      },
      {
        type: 10, // Text - Audio quality
        content: `🎵 **Audio Quality** → \`${voiceChannel.bitrate / 1000}kbps\` ${voiceChannel.bitrate >= 128000 ? '🎧 HIGH' : voiceChannel.bitrate >= 64000 ? '🔊 GOOD' : '📻 BASIC'}`
      },
              {
          type: 10, // Text - Action prompt
          content: `## 🎮 Control Panel\n*Click the buttons below to manage your voice channel:*`
        },
      {
        type: 1, // ActionRow for primary buttons
        components: [
          {
            type: 2, // Button
            custom_id: 'vc_settings',
            label: 'Channel Settings',
            style: 1, // Primary
            emoji: { name: '⚙️' }
          },
                      {
              type: 2, // Button
              custom_id: 'vc_access',
              label: 'User Access',
              style: 2, // Secondary
              emoji: { name: '👥' }
            },
                      {
              type: 2, // Button
              custom_id: 'vc_password',
              label: 'Password',
              style: 2, // Secondary
              emoji: { name: '🔑' }
            },
                      {
              type: 2, // Button
              custom_id: 'vc_regions',
              label: 'Regions',
              style: 2, // Secondary
              emoji: { name: '🌍' }
            }
        ]
      },
      {
        type: 1, // ActionRow for additional buttons
        components: [
          {
            type: 2, // Button
            custom_id: 'vc_lock',
            label: tempChannelData?.settings.locked ? 'Unlock Channel' : 'Lock Channel',
            style: tempChannelData?.settings.locked ? 3 : 4, // Success or Danger
            emoji: { name: tempChannelData?.settings.locked ? '🔓' : '🔒' }
          },
          {
            type: 2, // Button
            custom_id: 'vc_hide',
            label: tempChannelData?.settings.hidden ? 'Show Channel' : 'Hide Channel',
            style: tempChannelData?.settings.hidden ? 3 : 2, // Success or Secondary
            emoji: { name: tempChannelData?.settings.hidden ? '👁️' : '🫥' }
          }
        ]
      },
              {
          type: 10, // Text - Footer
          content: `---\n💡 **Tip:** Your channel will be automatically deleted when empty\n🔧 **Need help?** Use the buttons above to customize your experience!`
        }
    ]
  };
}

// Function to update existing control panel
async function updateControlPanel(voiceChannel: any, owner: any) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
      try {
      const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
      const controlPanelMessageId = (tempChannelData as any)?.controlPanelMessageId;
      if (!controlPanelMessageId) return;
      
      const container = buildControlPanelContainer(voiceChannel, owner);
      
      const message = await voiceChannel.messages.fetch(controlPanelMessageId).catch(() => null);
      if (message) {
        await message.edit({
          components: [container],
          flags: MessageFlags.IsComponentsV2
        });
      }
  } catch (error) {
    logger.error('Failed to update control panel', {
      error: error instanceof Error ? error.message : 'Unknown error',
      voiceChannel: voiceChannel.name,
      owner: owner.tag
    });
  }
}

// Function to send control panel to voice channel's text chat
async function sendControlPanel(voiceChannel: any, owner: any) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  try {
    const container = buildControlPanelContainer(voiceChannel, owner);

    const message = await voiceChannel.send({
      components: [container],
      flags: MessageFlags.IsComponentsV2
    });

    // Store the control panel message ID for future updates
    const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
    if (tempChannelData) {
      (tempChannelData as any).controlPanelMessageId = message.id;
    }

    // Control panel sent to text channel

  } catch (error) {
    logger.error('Failed to send control panel', {
      error: error instanceof Error ? error.message : 'Unknown error',
      voiceChannel: voiceChannel.name,
      owner: owner.tag
    });
  }
}

// Handle modal submissions
async function handleModalSubmission(interaction: ModalSubmitInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  try {
    // Handle password submission for waiting rooms first (no owner check needed)
    if (interaction.customId.startsWith('vc_submit_password:')) {
      await handlePasswordSubmission(interaction);
      return;
    }
    
    const member = interaction.guild?.members.cache.get(interaction.user.id);
    const voiceChannel = member?.voice.channel;

    if (!voiceChannel || voiceChannel.type !== ChannelType.GuildVoice) {
      await interaction.reply({
        content: '❌ You must be in a voice channel to use this!',
        ephemeral: true
      });
      return;
    }

    if (!ChannelUtils.isChannelOwner(voiceChannel.id, interaction.user.id)) {
      await interaction.reply({
        content: '❌ Only the channel owner can modify settings!',
        ephemeral: true
      });
      return;
    }

    switch (interaction.customId) {
      case 'vc_password_modal':
        const password = interaction.fields.getTextInputValue('password_input').trim();
        if (password) {
          ChannelUtils.setPassword(voiceChannel.id, password);
          await interaction.reply({
            content: '🔑 Password set successfully!',
            ephemeral: true
          });
        } else {
          ChannelUtils.removePassword(voiceChannel.id);
          await interaction.reply({
            content: '🔓 Password removed successfully!',
            ephemeral: true
          });
        }
        
        // Update the control panel to reflect the changes
        await updateControlPanel(voiceChannel, interaction.user);
        break;

      case 'vc_settings_modal':
        const name = interaction.fields.getTextInputValue('name_input').trim();
        const limit = parseInt(interaction.fields.getTextInputValue('limit_input'));
        const bitrate = parseInt(interaction.fields.getTextInputValue('bitrate_input')) * 1000;

        if (name) await voiceChannel.setName(name);
        if (!isNaN(limit) && limit >= 0 && limit <= 99) {
          await ChannelUtils.setUserLimit(voiceChannel, limit);
        }
        if (!isNaN(bitrate) && bitrate >= 8000 && bitrate <= 384000) {
          await ChannelUtils.setBitrate(voiceChannel, bitrate);
        }

        await interaction.reply({
          content: '⚙️ Channel settings updated successfully!',
          ephemeral: true
        });
        
        // Update the control panel to reflect the changes
        await updateControlPanel(voiceChannel, interaction.user);
        break;

      default:
        await interaction.reply({
          content: '❌ Unknown modal submission!',
          ephemeral: true
        });
    }
  } catch (error) {
    logger.error('Modal submission error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      customId: interaction.customId,
      user: interaction.user.tag
    });

    if (!interaction.replied) {
      await interaction.reply({
        content: '❌ An error occurred while processing your request!',
        ephemeral: true
      });
    }
  }
}

// Handle password submission for waiting rooms
async function handlePasswordSubmission(interaction: ModalSubmitInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  try {
    await interaction.deferReply({ ephemeral: true });
    
    const parts = interaction.customId.split(':');
    if (parts.length < 3) {
      await interaction.editReply({
        content: '❌ Invalid password submission format.'
      });
      return;
    }
    
    const [, waitingRoomId, mainChannelId] = parts;
    const password = interaction.fields.getTextInputValue('password');
    
    if (!waitingRoomId || !mainChannelId) {
      await interaction.editReply({
        content: '❌ Missing channel information.'
      });
      return;
    }
    
    // Processing password submission
    
    const mainChannel = await interaction.guild!.channels.fetch(mainChannelId).catch((error) => {
      logger.error('Failed to fetch main channel', { mainChannelId, error: error.message });
      return null;
    });
    const waitingRoom = await interaction.guild!.channels.fetch(waitingRoomId).catch((error) => {
      logger.error('Failed to fetch waiting room', { waitingRoomId, error: error.message });
      return null;
    });
    
    if (!mainChannel || !waitingRoom) {
      await interaction.editReply({
        content: `❌ Could not find channels. Main: ${mainChannel ? 'Found' : 'Missing'}, Waiting: ${waitingRoom ? 'Found' : 'Missing'}`
      });
      return;
    }
    
    if (mainChannel.type !== ChannelType.GuildVoice || waitingRoom.type !== ChannelType.GuildVoice) {
      await interaction.editReply({
        content: '❌ One or both channels are not voice channels.'
      });
      return;
    }
    
    // Verify password and cache it if correct
    const isValid = ChannelUtils.verifyPasswordWithCache(mainChannelId, interaction.user.id, password);
    
    if (isValid) {
      // Authorize the user to join
      ChannelUtils.authorizeJoin(mainChannelId, interaction.user.id);
      
      const successContainer = {
        type: 17, // Container
        accent_color: 0x00FF00, // Green for success
        components: [
          {
            type: 10, // Text
            content: `✅ **Password Correct!**\nMoving you to **${mainChannel.name}**...`
          }
        ]
      };
      
      await interaction.editReply({
        components: [successContainer],
        flags: MessageFlags.IsComponentsV2
      });
      
      // Move user to main channel
      try {
        const member = interaction.guild!.members.cache.get(interaction.user.id);
        if (member && member.voice.channel) {
          await member.voice.setChannel(mainChannel);
          
          // Clean up waiting room
          setTimeout(async () => {
            await ChannelUtils.cleanupWaitingRoom(waitingRoom as VoiceChannel);
          }, 1000);
        }
        
      } catch (error) {
        logger.error('Failed to move user after password entry', {
          error: error instanceof Error ? error.message : 'Unknown error',
          user: interaction.user.tag
        });
      }
    } else {
      const errorContainer = {
        type: 17, // Container
        accent_color: 0xFF0000, // Red for error
        components: [
          {
            type: 10, // Text
            content: `❌ **Incorrect Password**\nPlease try again with the correct password.`
          }
        ]
      };
      
      await interaction.editReply({
        components: [errorContainer],
        flags: MessageFlags.IsComponentsV2
      });
    }
    
  } catch (error) {
    logger.error('Password submission error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      customId: interaction.customId,
      user: interaction.user.tag
    });
    
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({
        content: '❌ An error occurred while processing your password!',
        ephemeral: true
      });
    } else {
      await interaction.editReply({
        content: '❌ An error occurred while processing your password!'
      });
    }
  }
}

// Handle user select menu interactions for access control
async function handleUserSelectMenu(interaction: any) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  try {
    const member = interaction.guild?.members.cache.get(interaction.user.id);
    const voiceChannel = member?.voice.channel;

    if (!voiceChannel || voiceChannel.type !== ChannelType.GuildVoice) {
      await interaction.reply({
        content: '❌ You must be in a voice channel to use this!',
        ephemeral: true
      });
      return;
    }

    if (!ChannelUtils.isChannelOwner(voiceChannel.id, interaction.user.id)) {
      await interaction.reply({
        content: '❌ Only the channel owner can modify access settings!',
        ephemeral: true
      });
      return;
    }

    const selectedUsers = interaction.values;

    switch (interaction.customId) {
      case 'vc_block_user_select':
        // Check if trying to block themselves
        if (selectedUsers.includes(interaction.user.id)) {
          await interaction.reply({
            content: '❌ You cannot block yourself from your own channel!',
            ephemeral: true
          });
          return;
        }

        let blockedCount = 0;
        const currentlyBlocked = ChannelUtils.getBlockedUsers(voiceChannel.id);
        const alreadyBlockedSet = new Set([...currentlyBlocked.view, ...currentlyBlocked.join]);
        
        for (const userId of selectedUsers) {
          // Skip if user is already blocked
          if (alreadyBlockedSet.has(userId)) {
            continue;
          }
          const success = await ChannelUtils.blockUser(voiceChannel, userId, 'both');
          if (success) blockedCount++;
        }
        
        const skippedCount = selectedUsers.length - blockedCount;
        let message = `🚫 Successfully blocked ${blockedCount} users from the channel!`;
        if (skippedCount > 0) {
          message += `\n(${skippedCount} users were already blocked)`;
        }
        
        const blockResultContainer = {
          type: 17, // Container
          accent_color: 0x00FF00, // Green for success
          components: [
            {
              type: 10, // Text
              content: `🚫 **Users Blocked Successfully!**\n${blockedCount} users have been blocked from your channel${skippedCount > 0 ? `\n(${skippedCount} users were already blocked)` : ''}`
            }
          ]
        };
        
        await interaction.update({
          components: [blockResultContainer],
          flags: MessageFlags.IsComponentsV2
        });
        
        // Auto-delete after 3 seconds
        setTimeout(async () => {
          try {
            await interaction.deleteReply();
          } catch (error) {
            // Message may already be deleted, ignore error
          }
        }, 3000);
        break;

      case 'vc_unblock_user_select':
        let unblockedCount = 0;
        for (const userId of selectedUsers) {
          const success = await ChannelUtils.unblockUser(voiceChannel, userId, 'both');
          if (success) unblockedCount++;
        }
        const unblockResultContainer = {
          type: 17, // Container
          accent_color: 0x00FF00, // Green for success
          components: [
            {
              type: 10, // Text
              content: `✅ **Users Unblocked Successfully!**\nUnblocked ${unblockedCount} out of ${selectedUsers.length} selected users`
            }
          ]
        };
        
        await interaction.update({
          components: [unblockResultContainer],
          flags: MessageFlags.IsComponentsV2
        });
        
        // Auto-delete after 3 seconds
        setTimeout(async () => {
          try {
            await interaction.deleteReply();
          } catch (error) {
            // Message may already be deleted, ignore error
          }
        }, 3000);
        break;

      case 'vc_kick_user_select':
        let kickedCount = 0;
        for (const userId of selectedUsers) {
          try {
            const member = await interaction.guild!.members.fetch(userId);
            if (member && member.voice.channel?.id === voiceChannel.id) {
              // Kick the user from the voice channel
              await member.voice.setChannel(null);
              
              // Apply kick protection (60-second lockout)
              ChannelUtils.addKickProtection(voiceChannel.id, userId);
              
              // Try to send them a DM about being kicked
              try {
                await member.send(`🦵 **You were kicked from ${voiceChannel.name}**\nYou are temporarily locked out for 60 seconds to allow the owner to change the password if needed.`);
              } catch (error) {
                // DM failed, ignore
              }
              
              kickedCount++;
              
              // User kicked with protection applied
            }
          } catch (error) {
            logger.error('Failed to kick user', {
              userId,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }
        
        const kickResultContainer = {
          type: 17, // Container
          accent_color: 0xFF4500, // Orange-red for kick success
          components: [
            {
              type: 10, // Text
              content: `🦵 **Users Kicked Successfully!**\n${kickedCount} out of ${selectedUsers.length} users were kicked and locked out for 60 seconds`
            }
          ]
        };
        
        await interaction.update({
          components: [kickResultContainer],
          flags: MessageFlags.IsComponentsV2
        });
        
        // Update control panel to reflect user count change
        try {
          const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
          if (tempChannelData) {
            const owner = await interaction.guild!.members.fetch(tempChannelData.ownerId).catch(() => null);
            if (owner) {
              await updateControlPanel(voiceChannel, owner.user);
            }
          }
        } catch (error) {
          // Ignore control panel update errors
        }
        
        // Auto-delete after 5 seconds (longer for kick confirmation)
        setTimeout(async () => {
          try {
            await interaction.deleteReply();
          } catch (error) {
            // Message may already be deleted, ignore error
          }
        }, 5000);
        break;

      case 'vc_region_select':
        const selectedRegion = interaction.values[0];
        const regionValue = selectedRegion === 'auto' ? null : selectedRegion;
        
        try {
          await voiceChannel.setRTCRegion(regionValue);
          
          // Get region display name (only valid Discord regions)
          const regions = [
            { name: 'Automatic', value: null },
            { name: 'US West', value: 'us-west' },
            { name: 'US East', value: 'us-east' },
            { name: 'US Central', value: 'us-central' },
            { name: 'US South', value: 'us-south' },
            { name: 'Rotterdam', value: 'rotterdam' },
            { name: 'Singapore', value: 'singapore' },
            { name: 'Sydney', value: 'sydney' },
            { name: 'Japan', value: 'japan' },
            { name: 'Hong Kong', value: 'hongkong' },
            { name: 'South Korea', value: 'south-korea' },
            { name: 'India', value: 'india' },
            { name: 'Brazil', value: 'brazil' },
            { name: 'South Africa', value: 'southafrica' }
          ];
          
          const regionName = regions.find(r => r.value === regionValue)?.name || 'Automatic';
          
          const regionResultContainer = {
            type: 17, // Container
            accent_color: 0x00FF00, // Green for success
            components: [
              {
                type: 10, // Text
                content: `🌍 **Region Changed Successfully!**\nVoice region set to **${regionName}**`
              }
            ]
          };
          
          await interaction.update({
            components: [regionResultContainer],
            flags: MessageFlags.IsComponentsV2
          });
          
          // Voice region changed successfully
          
          // Auto-delete after 3 seconds
          setTimeout(async () => {
            try {
              await interaction.deleteReply();
            } catch (error) {
              // Message may already be deleted, ignore error
            }
          }, 3000);
          
        } catch (error) {
          logger.error('Failed to change voice region', {
            error: error instanceof Error ? error.message : 'Unknown error',
            selectedRegion,
            user: interaction.user.tag
          });
          
          const errorContainer = {
            type: 17, // Container
            accent_color: 0xFF0000, // Red for error
            components: [
              {
                type: 10, // Text
                content: `❌ **Failed to Change Region**\nCould not set voice region to **${selectedRegion}**`
              }
            ]
          };
          
          await interaction.update({
            components: [errorContainer],
            flags: MessageFlags.IsComponentsV2
          });
          
          // Auto-delete after 3 seconds
          setTimeout(async () => {
            try {
              await interaction.deleteReply();
            } catch (error) {
              // Message may already be deleted, ignore error
            }
          }, 3000);
        }
        break;

      default:
        await interaction.reply({
          content: '❌ Unknown user selection!',
          ephemeral: true
        });
    }
  } catch (error) {
    logger.error('User select menu error', {
      error: error instanceof Error ? error.message : 'Unknown error',
      customId: interaction.customId,
      user: interaction.user.tag
    });

    if (!interaction.replied) {
      await interaction.reply({
        content: '❌ An error occurred while processing your selection!',
        ephemeral: true
      });
    }
  }
}

// Cleanup orphaned temp channels on bot restart
async function cleanupOrphanedChannels(bot: BotInstance, logger: any) {
  let cleanedCount = 0;
  let errorCount = 0;
  
  try {
    // Get all guilds the bot is in
    for (const guild of bot.client.guilds.cache.values()) {
      try {
        // Find channels in the temp voice category (including waiting rooms)
        const tempChannels = guild.channels.cache.filter(channel => 
          channel.type === ChannelType.GuildVoice && 
          channel.parentId === config.settings.categoryId &&
          channel.id !== config.settings.triggerChannelId &&
          (
            // Only consider channels created by the addon: either start with the configured prefix or are a waiting room
            channel.name.startsWith(config.settings.defaultChannelName) ||
            channel.name.startsWith('🚪') // Waiting room channels begin with door emoji
          )
        );
        
        for (const channel of tempChannels.values()) {
          try {
            const voiceChannel = channel as VoiceChannel;
            
            // Check if channel is empty or has only bots
            const humanMembers = voiceChannel.members.filter(member => !member.user.bot);
            
            if (humanMembers.size === 0) {
              // Channel is empty, delete it
              const channelType = voiceChannel.name.includes('🚪 Waiting Room') ? 'waiting room' : 'temp channel';
              await voiceChannel.delete(`Cleanup: Empty ${channelType} found on startup`);
              cleanedCount++;
              
              // Cleaned up orphaned channel
            } else {
              // Channel has users
              if (voiceChannel.name.includes('🚪 Waiting Room')) {
                // This is a waiting room, don't track it as a temp channel
                // Found active waiting room (not tracking)
              } else {
                // This is a temp channel, add it to our tracking system
                const firstMember = humanMembers.first();
                if (firstMember) {
                  ChannelUtils.addTempChannel(voiceChannel.id, firstMember.id, guild.id);
                  
                  // Store creation timestamp (approximate)
                  const tempChannelData = ChannelUtils.getTempChannel(voiceChannel.id);
                  if (tempChannelData) {
                    (tempChannelData as any).createdTimestamp = Math.floor((Date.now() - 300000) / 1000); // Assume created 5 minutes ago
                  }
                  
                  // Re-tracked existing temp channel
                }
              }
            }
          } catch (channelError) {
            errorCount++;
            logger.error('Failed to process temp channel', {
              channelId: channel.id,
              channelName: channel.name,
              error: channelError instanceof Error ? channelError.message : 'Unknown error'
            });
          }
        }
      } catch (guildError) {
        errorCount++;
        logger.error('Failed to process guild for cleanup', {
          guildId: guild.id,
          guildName: guild.name,
          error: guildError instanceof Error ? guildError.message : 'Unknown error'
        });
      }
    }
    
    // Orphaned temp channel cleanup completed
    
  } catch (error) {
    logger.error('Failed to complete orphaned channel cleanup', {
      error: error instanceof Error ? error.message : 'Unknown error',
      cleanedChannels: cleanedCount,
      errors: errorCount
    });
  }
}

export default addon; 
export { updateControlPanel }; 