import {
    Box,
    Container,
    Heading,
    Text,
    VStack,
    HStack,
    Icon,
    useToast,
    Card,
    CardHeader,
    CardBody,
    Button,
    Table,
    Thead,
    Tbody,
    Tr,
    Th,
    Td,
    IconButton,
    Tooltip,
    Badge,
    useDisclosure,
    Modal,
    ModalOverlay,
    ModalContent,
    ModalHeader,
    ModalBody,
    ModalFooter,
    ModalCloseButton,
    FormControl,
    FormLabel,
    Input,
    Textarea,
    Select,
    Switch,
    Spinner,
    Link as ChakraLink,
    RadioGroup,
    Radio,
    Stack,
    NumberInput,
    NumberInputField,
    NumberInputStepper,
    NumberIncrementStepper,
    NumberDecrementStepper,
    Tabs,
    TabList,
    TabPanels,
    Tab,
    TabPanel,
    SimpleGrid,
  } from '@chakra-ui/react';
  import { useState, useEffect, useMemo } from 'react';
  import { GetServerSideProps } from 'next';
  import { getServerSession } from 'next-auth/next';
  import { authOptions } from '../api/auth/[...nextauth]';
  import { FiCalendar, FiPlus, FiEdit, FiTrash2, FiMessageSquare, FiStar } from 'react-icons/fi';
  import Layout from '../../components/Layout';
  import { useForm, Controller, useWatch } from 'react-hook-form';
  import { useTheme } from '../../contexts/ThemeContext';

  type EventType = 'TIMED_MESSAGE' | 'GUILD_EVENT';
  type IntervalUnit = 'seconds' | 'minutes' | 'hours' | 'days' | 'weeks' | 'months';

  interface ScheduledEventFormData {
    _id?: string;
    name: string;
    isEnabled: boolean;
    createdAt?: string;
    eventType: EventType;

    // Timed Message fields
    channelId?: string;
    message?: string;
    cron?: string; // Still exists for display, but not for input
    intervalValue?: number;
    intervalUnit?: IntervalUnit;

    // Embed fields (for TIMED_MESSAGE)
    embedEnabled?: boolean;
    embedTitle?: string;
    embedDescription?: string;
    embedColor?: string; // Hex color string
    embedAuthorName?: string;
    embedAuthorUrl?: string;
    embedAuthorIconUrl?: string;
    embedImageUrl?: string;
    embedThumbnailUrl?: string;
    embedFooterText?: string;
    embedFooterIconUrl?: string;
    embedTimestamp?: boolean; // To include current timestamp

    // Guild Event fields
    description?: string;
    startTime?: string;
    endTime?: string;
    locationType?: 'VOICE' | 'EXTERNAL';
    location?: string; // For external URL
    eventChannelId?: string; // For voice channel
  }

  interface Channel {
    id: string;
    name: string;
    raw_type: number;
    parent_id?: string;
    position: number;
    topic?: string;
    nsfw?: boolean;
  }

  const EventSchedulerPage = () => {
    const toast = useToast();
    const { isOpen, onOpen, onClose } = useDisclosure();
    const [events, setEvents] = useState<ScheduledEventFormData[]>([]);
    const [allChannels, setAllChannels] = useState<Channel[]>([]);
    const [loading, setLoading] = useState(true);
    const [selectedEvent, setSelectedEvent] = useState<ScheduledEventFormData | null>(null);
    const { currentScheme } = useTheme();

    const {
      handleSubmit,
      control,
      register,
      reset,
      formState: { errors, isSubmitting },
    } = useForm<ScheduledEventFormData>({
      defaultValues: {
        eventType: 'TIMED_MESSAGE',
        isEnabled: true,
        locationType: 'VOICE',
        intervalValue: 24,
        intervalUnit: 'hours',
      },
    });

    const eventType = useWatch({ control, name: 'eventType' });
    const locationType = useWatch({ control, name: 'locationType' });
    const embedEnabled = useWatch({ control, name: 'embedEnabled' });

    const fetchEvents = async () => {
      try {
        const response = await fetch('/api/scheduler');
        if (!response.ok) throw new Error('Failed to fetch events');
        const data = await response.json();
        setEvents(data);
      } catch (error) {
        toast({
          title: 'Error fetching events',
          description: (error as Error).message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      } finally {
        setLoading(false);
      }
    };

    const fetchChannels = async () => {
      try {
        const response = await fetch('/api/discord/channels');
        if (!response.ok) throw new Error('Failed to fetch channels');
        const data = await response.json();
        setAllChannels(data);
      } catch (error) {
        toast({
          title: 'Error fetching channels',
          description: (error as Error).message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    };

    useEffect(() => {
      fetchEvents();
      fetchChannels();
    }, []);

    const { textChannels, voiceChannels, categories } = useMemo(() => {
      const categories = allChannels.filter(c => c.raw_type === 4).sort((a, b) => a.position - b.position);
      const text = allChannels.filter(c => [0, 5, 10, 11, 12].includes(c.raw_type));
      const voice = allChannels.filter(c => [2, 13].includes(c.raw_type));
      return { textChannels: text, voiceChannels: voice, categories };
    }, [allChannels]);

    const handleOpenModal = (event: ScheduledEventFormData | null = null) => {
      setSelectedEvent(event);
      if (event) {
        const formattedEvent: ScheduledEventFormData = {
          ...event,
          startTime: event.startTime ? new Date(event.startTime).toISOString().slice(0, 16) : '',
          endTime: event.endTime ? new Date(event.endTime).toISOString().slice(0, 16) : '',
        };

        // Parse message content for embeds if applicable
        if (event.eventType === 'TIMED_MESSAGE' && typeof event.message === 'string') {
          try {
            const parsedMessage = JSON.parse(event.message);
            if (parsedMessage && typeof parsedMessage === 'object' && !Array.isArray(parsedMessage) && parsedMessage.embeds) {
              const embed = parsedMessage.embeds[0];
              formattedEvent.message = parsedMessage.content || '';
              formattedEvent.embedEnabled = true;
              formattedEvent.embedTitle = embed.title || '';
              formattedEvent.embedDescription = embed.description || '';
              formattedEvent.embedColor = embed.color ? '#' + embed.color.toString(16).padStart(6, '0') : '';
              formattedEvent.embedAuthorName = embed.author?.name || '';
              formattedEvent.embedAuthorUrl = embed.author?.url || '';
              formattedEvent.embedAuthorIconUrl = embed.author?.icon_url || '';
              formattedEvent.embedImageUrl = embed.image?.url || '';
              formattedEvent.embedThumbnailUrl = embed.thumbnail?.url || '';
              formattedEvent.embedFooterText = embed.footer?.text || '';
              formattedEvent.embedFooterIconUrl = embed.footer?.icon_url || '';
              formattedEvent.embedTimestamp = !!embed.timestamp;
            } else {
                formattedEvent.embedEnabled = false; // No embed found
            }
          } catch (e) {
            // Message is plain text, not JSON
            formattedEvent.embedEnabled = false;
          }
        }

        reset(formattedEvent);
      } else {
        reset({
          eventType: 'TIMED_MESSAGE',
          name: '',
          isEnabled: true,
          locationType: 'VOICE',
          message: '',
          description: '',
          channelId: '',
          eventChannelId: '',
          startTime: '',
          endTime: '',
          location: '',
          intervalValue: 24,
          intervalUnit: 'hours',
          // Reset embed fields as well
          embedEnabled: false,
          embedTitle: '',
          embedDescription: '',
          embedColor: '',
          embedAuthorName: '',
          embedAuthorUrl: '',
          embedAuthorIconUrl: '',
          embedImageUrl: '',
          embedThumbnailUrl: '',
          embedFooterText: '',
          embedFooterIconUrl: '',
          embedTimestamp: false,
        });
      }
      onOpen();
    };

    const onSubmit = async (data: ScheduledEventFormData) => {
      const url = selectedEvent ? `/api/scheduler?eventId=${selectedEvent._id}` : '/api/scheduler';
      const method = selectedEvent ? 'PUT' : 'POST';

      // Build a clean payload with only the relevant fields for the selected event type
      const { eventType, name, isEnabled } = data;
      let payload: any = { eventType, name, isEnabled };

      if (eventType === 'TIMED_MESSAGE') {
        payload = {
            ...payload,
            channelId: data.channelId,
            message: data.message,
            intervalValue: data.intervalValue,
            intervalUnit: data.intervalUnit,
        };

        // Handle embed data if enabled
        if (data.embedEnabled) {
            const embed: any = {};
            if (data.embedTitle) embed.title = data.embedTitle;
            if (data.embedDescription) embed.description = data.embedDescription;
            if (data.embedColor) embed.color = parseInt(data.embedColor.replace('#', ''), 16);

            if (data.embedAuthorName || data.embedAuthorUrl || data.embedAuthorIconUrl) {
                embed.author = {};
                if (data.embedAuthorName) embed.author.name = data.embedAuthorName;
                if (data.embedAuthorUrl) embed.author.url = data.embedAuthorUrl;
                if (data.embedAuthorIconUrl) embed.author.icon_url = data.embedAuthorIconUrl;
            }

            if (data.embedImageUrl) embed.image = { url: data.embedImageUrl };
            if (data.embedThumbnailUrl) embed.thumbnail = { url: data.embedThumbnailUrl };

            if (data.embedFooterText || data.embedFooterIconUrl) {
                embed.footer = {};
                if (data.embedFooterText) embed.footer.text = data.embedFooterText;
                if (data.embedFooterIconUrl) embed.footer.icon_url = data.embedFooterIconUrl;
            }

            if (data.embedTimestamp) embed.timestamp = new Date().toISOString();

            // Combine message content and embed into a single payload string
            payload.message = JSON.stringify({
                content: data.message || undefined,
                embeds: [embed]
            });
        }

      } else if (eventType === 'GUILD_EVENT') {
        payload = {
            ...payload,
            description: data.description,
            startTime: data.startTime ? new Date(data.startTime).toISOString() : undefined,
            endTime: data.endTime ? new Date(data.endTime).toISOString() : undefined,
            locationType: data.locationType,
        };
        if (data.locationType === 'VOICE') {
            payload.eventChannelId = data.eventChannelId;
        } else {
            payload.location = data.location;
        }
      }

      // Debug: log the payload before sending
      console.log('Submitting scheduled event payload:', payload);

      try {
        const response = await fetch(url, {
          method,
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        if (!response.ok) {
          let errorMsg = 'Failed to save event';
          try {
            const errorData = await response.json();
            errorMsg = errorData.error || errorMsg;
          } catch {}
          throw new Error(errorMsg);
        }

        toast({
          title: `Event ${selectedEvent ? 'updated' : 'created'}`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        });

        fetchEvents();
        onClose();
      } catch (error) {
        toast({
          title: 'Error saving event',
          description: (error as Error).message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    };

    const handleDelete = async (eventId: string) => {
      if (!window.confirm('Are you sure you want to delete this event?')) return;

      try {
        const response = await fetch(`/api/scheduler?eventId=${eventId}`, { method: 'DELETE' });
        if (!response.ok) throw new Error((await response.json()).error || 'Failed to delete');
        toast({ title: 'Event deleted', status: 'success', duration: 3000, isClosable: true });
        fetchEvents();
      } catch (error) {
        toast({ title: 'Error deleting event', description: (error as Error).message, status: 'error', duration: 5000, isClosable: true });
      }
    };

    const getChannelName = (channelId: string) => {
      const channel = allChannels.find(c => c.id === channelId);
      return channel ? `#${channel.name}` : 'Unknown Channel';
    };

    const renderChannelOptions = (channelsToRender: Channel[]) => {
      const channelsWithCategory = categories.map(cat => ({
        ...cat,
        children: channelsToRender.filter(c => c.parent_id === cat.id).sort((a, b) => a.position - b.position),
      })).filter(cat => cat.children.length > 0);

      const channelsWithoutCategory = channelsToRender.filter(c => !c.parent_id).sort((a, b) => a.position - b.position);

      return (
        <>
          {channelsWithCategory.map(cat => (
            <optgroup key={cat.id} label={cat.name}>
              {cat.children.map(channel => (
                <option key={channel.id} value={channel.id}>
                  #{channel.name}
                </option>
              ))}
            </optgroup>
          ))}
          {channelsWithoutCategory.length > 0 && (
            <optgroup label="No Category">
              {channelsWithoutCategory.map(channel => (
                <option key={channel.id} value={channel.id}>
                  #{channel.name}
                </option>
              ))}
            </optgroup>
          )}
        </>
      );
    };

    return (
      <Layout>
        {/* Main content of the Event Scheduler page */}
        <Container maxW="container.xl" py={8}>
          <VStack spacing={8} align="stretch">
            <HStack justify="space-between">
              <VStack align="start">
                <Heading size="lg"><Icon as={FiCalendar} mr={3} />Event Scheduler</Heading>
                <Text color="gray.500">Schedule timed messages and guild events for your server.</Text>
              </VStack>
              <Button leftIcon={<FiPlus />} colorScheme="blue" onClick={() => handleOpenModal()}>Create Event</Button>
            </HStack>

            <Card>
              <CardHeader><Heading size="md">Scheduled Events</Heading></CardHeader>
              <CardBody>
                {loading ? <Spinner /> : events.length === 0 ? (
                  <Text>No scheduled events found. Create one to get started!</Text>
                ) : (
                  <Box overflowX="auto">
                    <Table variant="simple">
                      <Thead>
                        <Tr>
                          <Th>Status</Th>
                          <Th>Type</Th>
                          <Th>Name</Th>
                          <Th>Target / Location</Th>
                          <Th>Schedule</Th>
                          <Th>Actions</Th>
                        </Tr>
                      </Thead>
                      <Tbody>
                        {events.map(event => (
                          <Tr key={event._id}>
                            <Td><Badge colorScheme={event.isEnabled ? 'green' : 'red'}>{event.isEnabled ? 'Enabled' : 'Disabled'}</Badge></Td>
                            <Td>
                              <HStack>
                                <Icon as={event.eventType === 'GUILD_EVENT' ? FiStar : FiMessageSquare} />
                                <Text>{event.eventType === 'GUILD_EVENT' ? 'Guild Event' : 'Message'}</Text>
                              </HStack>
                            </Td>
                            <Td>{event.name}</Td>
                            <Td>{event.eventType === 'TIMED_MESSAGE' ? getChannelName(event.channelId!) : (event.locationType === 'VOICE' ? getChannelName(event.eventChannelId!) : event.location)}</Td>
                            <Td>
                              {event.eventType === 'TIMED_MESSAGE' ? (
                                <Text fontFamily="mono" fontSize="sm">{event.cron || `${event.intervalValue} ${event.intervalUnit}`}</Text>
                              ) : (
                                <Text fontSize="sm">{new Date(event.startTime!).toLocaleString()}</Text>
                              )}
                            </Td>
                            <Td>
                              <HStack spacing={2}>
                                <Tooltip label="Edit Event"><IconButton aria-label="Edit" icon={<FiEdit />} size="sm" onClick={() => handleOpenModal(event)} /></Tooltip>
                                <Tooltip label="Delete Event"><IconButton aria-label="Delete" icon={<FiTrash2 />} size="sm" colorScheme="red" onClick={() => handleDelete(event._id!)} /></Tooltip>
                              </HStack>
                            </Td>
                          </Tr>
                        ))}
                      </Tbody>
                    </Table>
                  </Box>
                )}
              </CardBody>
            </Card>
          </VStack>
        </Container>

        <Modal isOpen={isOpen} onClose={onClose} size="6xl" closeOnOverlayClick={false}> {/* Changed size to 6xl */}
          <ModalOverlay bg="blackAlpha.600" />
          <ModalContent bg={currentScheme.colors.background} border="2px solid" borderColor="blue.400" maxW="1200px" as="form" onSubmit={handleSubmit(onSubmit)}>
            <ModalHeader color={currentScheme.colors.text}>{selectedEvent ? 'Edit' : 'Create'} Scheduled Event</ModalHeader>
            <ModalCloseButton />
            <ModalBody pb={6}>
              <VStack spacing={6} align="stretch">

                <FormControl>
                  <FormLabel color={currentScheme.colors.text}>Event Type</FormLabel>
                  <Controller
                    name="eventType"
                    control={control}
                    render={({ field }) => (
                      <RadioGroup {...field}>
                        <Stack direction="row" spacing={5}>
                          <Radio value="TIMED_MESSAGE">Timed Message</Radio>
                          <Radio value="GUILD_EVENT">Guild Event</Radio>
                        </Stack>
                      </RadioGroup>
                    )}
                  />
                </FormControl>

                <Tabs variant="enclosed" colorScheme="blue">
                  <TabList>
                    {eventType === 'TIMED_MESSAGE' ? (
                      <>
                        <Tab>Timed Message Settings</Tab>
                        <Tab>Message Builder</Tab>
                      </>
                    ) : (
                      <Tab>Guild Event Settings</Tab>
                    )}
                  </TabList>

                  <TabPanels>
                    {/* Timed Message Settings Tab */}
                    {eventType === 'TIMED_MESSAGE' && (
                      <TabPanel>
                        <VStack spacing={4} align="stretch">
                          <FormControl isInvalid={!!errors.name}>
                            <FormLabel color={currentScheme.colors.text}>Event Name</FormLabel>
                            <Input {...register('name', { required: 'Event name is required' })} placeholder="e.g., Daily Announcement" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                          </FormControl>
                          <FormControl display="flex" alignItems="center">
                            <FormLabel htmlFor="isEnabled" mb="0" color={currentScheme.colors.text}>Enable Event</FormLabel>
                            <Switch id="isEnabled" {...register('isEnabled')} />
                          </FormControl>
                          <FormControl isInvalid={!!errors.channelId}>
                            <FormLabel color={currentScheme.colors.text}>Channel</FormLabel>
                            <Select {...register('channelId', { required: 'Channel is required' })} placeholder="Select a text channel" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border}>
                              {renderChannelOptions(textChannels)}
                            </Select>
                          </FormControl>
                          <FormControl isInvalid={!!errors.intervalValue}>
                            <FormLabel color={currentScheme.colors.text}>Interval</FormLabel>
                            <HStack>
                              <Controller
                                name="intervalValue"
                                control={control}
                                rules={{ required: 'Interval is required', min: 1 }}
                                render={({ field }) => (
                                  <NumberInput min={1} {...field} onChange={val => field.onChange(Number(val))}>
                                    <NumberInputField placeholder="e.g., 5" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                    <NumberInputStepper>
                                      <NumberIncrementStepper />
                                      <NumberDecrementStepper />
                                    </NumberInputStepper>
                                  </NumberInput>
                                )}
                              />
                              <Select {...register('intervalUnit', { required: 'Unit is required' })} w="40%" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border}>
                                <option value="seconds">Seconds</option>
                                <option value="minutes">Minutes</option>
                                <option value="hours">Hours</option>
                                <option value="days">Days</option>
                                <option value="weeks">Weeks</option>
                                <option value="months">Months</option>
                              </Select>
                            </HStack>
                            <Text fontSize="xs" color="gray.500" mt={1}>
                              Example: Every 5 minutes, every 2 hours, every 1 day, etc.
                            </Text>
                          </FormControl>
                        </VStack>
                      </TabPanel>
                    )}

                    {/* Message Builder Tab (New) */}
                    {eventType === 'TIMED_MESSAGE' && (
                      <TabPanel>
                        <VStack spacing={4} align="stretch">
                          <FormControl isInvalid={!!errors.message}>
                            <FormLabel color={currentScheme.colors.text}>Message Content</FormLabel>
                            <Textarea {...register('message')} placeholder="Enter your message content. Optional if using an embed. Supports Discord markdown." rows={3} bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>This will be sent as plain text or alongside your embed.</Text>
                          </FormControl>
                          <FormControl display="flex" alignItems="center">
                            <FormLabel htmlFor="embedEnabled" mb="0" color={currentScheme.colors.text}>Enable Embed</FormLabel>
                            <Switch id="embedEnabled" {...register('embedEnabled')} />
                          </FormControl>

                          {embedEnabled && (
                            <VStack spacing={4} w="full" pl={4} borderLeft="2px solid" borderColor="blue.200">
                              <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>Embed Details</Text>

                              <FormControl isInvalid={!!errors.embedTitle}>
                                <FormLabel color={currentScheme.colors.text}>Title</FormLabel>
                                <Input {...register('embedTitle')} placeholder="e.g., Important Announcement" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                              </FormControl>

                              <FormControl isInvalid={!!errors.embedDescription}>
                                <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                                <Textarea {...register('embedDescription')} placeholder="Main content of the embed." rows={3} bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                              </FormControl>

                              <FormControl isInvalid={!!errors.embedColor}>
                                <FormLabel color={currentScheme.colors.text}>Color (Hex Code)</FormLabel>
                                <Input type="color" {...register('embedColor')} placeholder="e.g., #00FF00" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                <Text fontSize="xs" color="gray.500" mt={1}>Enter a hex color code (e.g., #RRGGBB).</Text>
                              </FormControl>

                              <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mt={4} mb={2}>Author Fields</Text>
                              <SimpleGrid columns={2} spacing={4}>
                                <FormControl isInvalid={!!errors.embedAuthorName}>
                                  <FormLabel color={currentScheme.colors.text}>Author Name</FormLabel>
                                  <Input {...register('embedAuthorName')} placeholder="e.g., 404 Bot" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                </FormControl>
                                <FormControl isInvalid={!!errors.embedAuthorUrl}>
                                  <FormLabel color={currentScheme.colors.text}>Author URL</FormLabel>
                                  <Input {...register('embedAuthorUrl')} placeholder="https://example.com" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                </FormControl>
                                <FormControl isInvalid={!!errors.embedAuthorIconUrl}>
                                  <FormLabel color={currentScheme.colors.text}>Author Icon URL</FormLabel>
                                  <Input {...register('embedAuthorIconUrl')} placeholder="https://example.com/icon.png" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                </FormControl>
                              </SimpleGrid>

                              <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mt={4} mb={2}>Image/Thumbnail Fields</Text>
                              <SimpleGrid columns={2} spacing={4}>
                                <FormControl isInvalid={!!errors.embedImageUrl}>
                                  <FormLabel color={currentScheme.colors.text}>Image URL</FormLabel>
                                  <Input {...register('embedImageUrl')} placeholder="https://example.com/image.png" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                  <Text fontSize="xs" color="gray.500" mt={1}>Large image displayed above the footer.</Text>
                                </FormControl>
                                <FormControl isInvalid={!!errors.embedThumbnailUrl}>
                                  <FormLabel color={currentScheme.colors.text}>Thumbnail URL</FormLabel>
                                  <Input {...register('embedThumbnailUrl')} placeholder="https://example.com/thumbnail.png" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                  <Text fontSize="xs" color="gray.500" mt={1}>Small image usually in the top right.</Text>
                                </FormControl>
                              </SimpleGrid>

                              <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mt={4} mb={2}>Footer Fields</Text>
                              <SimpleGrid columns={2} spacing={4}>
                                <FormControl isInvalid={!!errors.embedFooterText}>
                                  <FormLabel color={currentScheme.colors.text}>Footer Text</FormLabel>
                                  <Input {...register('embedFooterText')} placeholder="e.g., Powered by 404 Bot" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                </FormControl>
                                <FormControl isInvalid={!!errors.embedFooterIconUrl}>
                                  <FormLabel color={currentScheme.colors.text}>Footer Icon URL</FormLabel>
                                  <Input {...register('embedFooterIconUrl')} placeholder="https://example.com/footer-icon.png" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                                </FormControl>
                              </SimpleGrid>

                              <FormControl display="flex" alignItems="center">
                                <FormLabel htmlFor="embedTimestamp" mb="0" color={currentScheme.colors.text}>Show Timestamp</FormLabel>
                                <Switch id="embedTimestamp" {...register('embedTimestamp')} />
                                <Text fontSize="xs" color="gray.500" ml={2}>Adds current time to the embed footer.</Text>
                              </FormControl>
                            </VStack>
                          )}
                        </VStack>
                      </TabPanel>
                    )}

                    {/* Guild Event Settings Tab */}
                    {eventType === 'GUILD_EVENT' && (
                      <TabPanel>
                        <VStack spacing={4} align="stretch">
                          <FormControl isInvalid={!!errors.description}>
                            <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                            <Textarea {...register('description', { required: eventType === 'GUILD_EVENT' ? 'Description is required' : false })} placeholder="Describe the event." rows={3} bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                          </FormControl>
                          <SimpleGrid columns={2} spacing={4} w="full">
                            <FormControl isInvalid={!!errors.startTime}>
                              <FormLabel color={currentScheme.colors.text}>Start Time</FormLabel>
                              <Input type="datetime-local" {...register('startTime', { required: eventType === 'GUILD_EVENT' ? 'Start time is required' : false })} bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                            </FormControl>
                            <FormControl>
                              <FormLabel color={currentScheme.colors.text}>End Time (Optional)</FormLabel>
                              <Input type="datetime-local" {...register('endTime')} bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                            </FormControl>
                          </SimpleGrid>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Location Type</FormLabel>
                            <Controller name="locationType" control={control} render={({ field }) => (
                              <RadioGroup {...field}>
                                <Stack direction="row" spacing={5}>
                                  <Radio value="VOICE">Voice Channel</Radio>
                                  <Radio value="EXTERNAL">External URL</Radio>
                                </Stack>
                              </RadioGroup>
                            )} />
                          </FormControl>
                          {locationType === 'VOICE' ? (
                            <FormControl isInvalid={!!errors.eventChannelId}>
                              <FormLabel color={currentScheme.colors.text}>Voice Channel</FormLabel>
                              <Select {...register('eventChannelId', { required: eventType === 'GUILD_EVENT' && locationType === 'VOICE' ? 'Voice channel is required' : false })} placeholder="Select a voice channel" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border}>
                                {renderChannelOptions(voiceChannels)}
                              </Select>
                            </FormControl>
                          ) : (
                            <FormControl isInvalid={!!errors.location}>
                              <FormLabel color={currentScheme.colors.text}>External URL</FormLabel>
                              <Input {...register('location', { required: eventType === 'GUILD_EVENT' && locationType === 'EXTERNAL' ? 'URL is required' : false })} placeholder="https://your.event.link" bg={currentScheme.colors.background} color={currentScheme.colors.text} borderColor={currentScheme.colors.border} />
                            </FormControl>
                          )}
                        </VStack>
                      </TabPanel>
                    )}
                  </TabPanels>
                </Tabs>

              </VStack>
            </ModalBody>
            <ModalFooter>
              <Button variant="ghost" mr={3} onClick={onClose}>Cancel</Button>
              <Button colorScheme="blue" type="submit" isLoading={isSubmitting}>{selectedEvent ? 'Save Changes' : 'Create Event'}</Button>
            </ModalFooter>
          </ModalContent>
        </Modal>
      </Layout>
    );
  };

  export default EventSchedulerPage;

  export const getServerSideProps: GetServerSideProps = async (ctx) => {
    const session = await getServerSession(ctx.req, ctx.res, authOptions);
    if (!session || !(session.user as any).isAdmin) {
      return { redirect: { destination: '/api/auth/signin?callbackUrl=%2Fadmin%2Fscheduler', permanent: false } };
    }
    return { props: { session } };
  };
