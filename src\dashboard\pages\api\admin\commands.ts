import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { dashboardConfig } from '../../../core/config';
import { MongoClient } from 'mongodb';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) return res.status(401).json({ error: 'Unauthorized' });
  if (!(session.user as any).isAdmin) return res.status(403).json({ error: 'Forbidden' });

  const { bot, database } = dashboardConfig;
  if (!bot?.token || !bot?.clientId) return res.status(500).json({ error: 'Bot credentials missing' });

  const headers = { 
    Authorization: `Bot ${bot.token}`,
    'Content-Type': 'application/json'
  } as const;

  try {
    // Connect to MongoDB
    const client = await MongoClient.connect(database.url);
    const db = client.db(database.name);
    const commandStates = db.collection('command_states');

    if (req.method === 'GET') {
      // Fetch global commands
      const globalRes = await fetch(`https://discord.com/api/v10/applications/${bot.clientId}/commands`, { headers });
      const globalCmds = globalRes.ok ? await globalRes.json() : [];

      // Fetch guild commands if guildId present
      let guildCmds: any[] = [];
      if (bot.guildId) {
        const gRes = await fetch(`https://discord.com/api/v10/applications/${bot.clientId}/guilds/${bot.guildId}/commands`, { headers });
        guildCmds = gRes.ok ? await gRes.json() : [];
      }

      // Fetch command states from database
      const states = await commandStates.find({}).toArray();
      const stateMap = new Map(states.map(s => [s.commandId, s.enabled]));

      // Read addon command metadata from filesystem
      const addonCommandMap = new Map<string, { addon: string; category: string }>();
      try {
        const possibleDirs = [
          path.resolve(process.cwd(), '../addons'),          // src/addons relative to dashboard
          path.resolve(process.cwd(), 'src/addons'),        // when running from project root
          path.resolve(process.cwd(), '../../addons'),      // fallback two levels up
          path.resolve(process.cwd(), '../dist/addons'),    // compiled JS output
          path.resolve(process.cwd(), 'dist/addons')
        ].filter(p => fs.existsSync(p));

        for (const addonsDir of possibleDirs) {
          const addonDirs = fs.readdirSync(addonsDir, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name);

          for (const addonName of addonDirs) {
            const commandsDir = path.join(addonsDir, addonName, 'commands');
            if (fs.existsSync(commandsDir)) {
              const commandFiles = fs.readdirSync(commandsDir)
                .filter(file => file.endsWith('.ts') || file.endsWith('.js'))
                .map(file => path.basename(file, path.extname(file)));
              
              for (const cmdName of commandFiles) {
                addonCommandMap.set(cmdName, { 
                  addon: addonName, 
                  category: addonName.charAt(0).toUpperCase() + addonName.slice(1) 
                });
              }
            }
          }
        }
      } catch (err) {
        console.warn('Failed to read addon command metadata:', err);
      }

      const mapped = [
        ...globalCmds.map((c: any) => ({ 
          scope: 'GLOBAL', 
          ...c,
          enabled: stateMap.get(c.id) ?? true, // Default to enabled if no state found
          addon: addonCommandMap.get(c.name)?.addon || 'Unknown',
          category: addonCommandMap.get(c.name)?.category || 'Unknown'
        })),
        ...guildCmds.map((c: any) => ({ 
          scope: 'GUILD', 
          ...c,
          enabled: stateMap.get(c.id) ?? true, // Default to enabled if no state found
          addon: addonCommandMap.get(c.name)?.addon || 'Unknown',
          category: addonCommandMap.get(c.name)?.category || 'Unknown'
        }))
      ];

      return res.status(200).json(mapped);
    } 
    
    if (req.method === 'PUT') {
      const { commandId, enabled } = req.body;
      if (typeof commandId !== 'string' || typeof enabled !== 'boolean') {
        return res.status(400).json({ error: 'Invalid request body' });
      }

      // Update command state in database
      await commandStates.updateOne(
        { commandId },
        { $set: { commandId, enabled } },
        { upsert: true }
      );

      // Clear cache to ensure immediate reflection of changes
      // This could be expanded to use a proper cache invalidation mechanism
      // For now, we'll rely on the CommandHandler's TTL cache

      try {
        const baseDir = process.cwd().includes(path.join('src', 'dashboard'))
          ? path.resolve(process.cwd(), '..', '..')
          : process.cwd();
        const signalPath = path.join(baseDir, 'command-state.signal');
        fs.writeFileSync(signalPath, JSON.stringify({
          commandId,
          enabled,
          requestedBy: (session.user as any)?.email || 'unknown',
          timestamp: Date.now()
        }));
      } catch (signalErr) {
        console.warn('Failed to write command-state signal file:', signalErr);
      }

      return res.status(200).json({ commandId, enabled });
    }
    
    if (req.method === 'DELETE') {
      const { commandId, scope } = req.query;
      if (!commandId) return res.status(400).json({ error: 'Command ID required' });

      const endpoint = scope === 'GUILD' && bot.guildId
        ? `https://discord.com/api/v10/applications/${bot.clientId}/guilds/${bot.guildId}/commands/${commandId}`
        : `https://discord.com/api/v10/applications/${bot.clientId}/commands/${commandId}`;

      const deleteRes = await fetch(endpoint, { 
        method: 'DELETE',
        headers 
      });

      if (!deleteRes.ok) {
        const error = await deleteRes.json().catch(() => ({ message: 'Unknown error' }));
        return res.status(deleteRes.status).json(error);
      }

      // Remove command state from database
      await commandStates.deleteOne({ commandId });

      return res.status(204).end();
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (err: any) {
    console.error('Failed to handle command operation:', err);
    return res.status(500).json({ error: 'Failed to handle command operation', details: err.message });
  }
} 