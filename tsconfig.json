{"compilerOptions": {"target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "checkJs": false, "removeComments": true, "sourceMap": false, "declaration": false, "declarationMap": false, "incremental": true, "tsBuildInfoFile": "./dist/.tsbuildinfo", "baseUrl": "./src", "paths": {"@/*": ["*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "src/dashboard/**/*", "**/*.test.ts", "**/*.spec.ts"]}