(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6934],{469:(e,r,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/gameservers",function(){return o(20619)}])},5095:(e,r,o)=>{"use strict";o.d(r,{EO:()=>n,Lt:()=>l});var t=o(94513),a=o(9557),s=o(52922);function l(e){let{leastDestructiveRef:r,...o}=e;return(0,t.jsx)(a.aF,{...o,initialFocusRef:r})}let n=(0,o(2923).R)((e,r)=>(0,t.jsx)(s.$,{ref:r,role:"alertdialog",...e}))},12183:(e,r,o)=>{"use strict";o.d(r,{E:()=>h,r:()=>p});var t=o(94513),a=o(75387),s=o(29035),l=o(22697),n=o(2923),i=o(56915),c=o(33225);let[d,h]=(0,s.q)({name:"StatStylesContext",errorMessage:"useStatStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Stat />\" "}),p=(0,n.R)(function(e,r){let o=(0,i.o)("Stat",e),s={position:"relative",flex:"1 1 0%",...o.container},{className:n,children:h,...p}=(0,a.M)(e);return(0,t.jsx)(d,{value:o,children:(0,t.jsx)(c.B.div,{ref:r,...p,className:(0,l.cx)("chakra-stat",n),__css:s,children:(0,t.jsx)("dl",{children:h})})})});p.displayName="Stat"},13341:e=>{e.exports=function(){var e=document.getSelection();if(!e.rangeCount)return function(){};for(var r=document.activeElement,o=[],t=0;t<e.rangeCount;t++)o.push(e.getRangeAt(t));switch(r.tagName.toUpperCase()){case"INPUT":case"TEXTAREA":r.blur();break;default:r=null}return e.removeAllRanges(),function(){"Caret"===e.type&&e.removeAllRanges(),e.rangeCount||o.forEach(function(r){e.addRange(r)}),r&&r.focus()}}},20619:(e,r,o)=>{"use strict";o.r(r),o.d(r,{__N_SSP:()=>W,default:()=>X});var t=o(94513),a=o(94285),s=o(95845),l=o(22907),n=o(51961),i=o(31678),c=o(41611),d=o(78902),h=o(62690),p=o(79156),u=o(57561),g=o(49217),x=o(25680),m=o(73011),b=o(5095),f=o(7680),v=o(47847),j=o(85104),y=o(28245),w=o(17842),C=o(60341),S=o(37211),_=o(44327),k=o(24792),E=o(71601),A=o(12183),N=o(56966),R=o(86153),z=o(83901),P=o(71185),D=o(79961);let T={minecraft:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},minecraftbe:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},csgo:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accent:"#ED8936"},valheim:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"},accent:"#F56565"},rust:{color:"brown",gradient:{from:"rgba(193, 105, 79, 0.4)",to:"rgba(193, 105, 79, 0.1)"},accent:"#C1694F"},arkse:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accent:"#9F7AEA"},sdtd:{color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accent:"#ECC94B"},default:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"},accent:"#4299E1"}},F=e=>{var r;let{server:o}=e,[s,l]=(0,a.useState)(!1),{hasCopied:i,onCopy:u}=function(e,r={}){let[o,t]=(0,a.useState)(!1),[s,l]=(0,a.useState)(e);(0,a.useEffect)(()=>l(e),[e]);let{timeout:n=1500,...i}="number"==typeof r?{timeout:r}:r,c=(0,a.useCallback)(e=>{let r="string"==typeof e?e:s;"clipboard"in navigator?navigator.clipboard.writeText(r).then(()=>t(!0)).catch(()=>t(S(r,i))):t(S(r,i))},[s,i]);return(0,a.useEffect)(()=>{let e=null;return o&&(e=window.setTimeout(()=>{t(!1)},n)),()=>{e&&window.clearTimeout(e)}},[n,o]),{value:s,setValue:l,onCopy:c,hasCopied:o}}(o.hasPassword?"Server: ".concat(o.host,":").concat(o.port,"\nPassword: ").concat(o.password):"".concat(o.host,":").concat(o.port)),x=T[o.type.toLowerCase()]||T.default;(0,_.dU)("gray.800","gray.800");let m=o.online?"".concat(x.color,".400"):"red.400",b=o.online?x.color:"red",f=(null==(r=o.players)?void 0:r.length)||0;return(0,t.jsx)(n.a,{p:5,bg:"linear-gradient(135deg, ".concat(x.gradient.from,", ").concat(x.gradient.to,")"),borderRadius:"xl",border:"2px",borderColor:m,width:"100%",position:"relative",overflow:"hidden",zIndex:1,transition:"all 0.2s",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%)",zIndex:-1},_hover:{transform:"translateY(-2px)",boxShadow:"0 8px 20px ".concat(x.gradient.from),borderColor:o.online?"".concat(x.color,".300"):"red.300"},children:(0,t.jsxs)(p.T,{align:"stretch",spacing:4,children:[(0,t.jsx)(d.z,{justify:"space-between",children:(0,t.jsxs)(p.T,{align:"start",spacing:1,children:[(0,t.jsxs)(d.z,{children:[(0,t.jsx)(c.E,{fontSize:"xl",fontWeight:"bold",color:"white",children:o.name||"".concat(o.host,":").concat(o.port)}),o.hasPassword&&(0,t.jsx)(k.m,{label:"Password Protected",children:(0,t.jsx)("span",{children:(0,t.jsx)(g.I,{as:w.JhU,color:"".concat(x.color,".200")})})})]}),(0,t.jsxs)(d.z,{spacing:2,children:[(0,t.jsx)(E.E,{colorScheme:b,fontSize:"sm",children:o.online?"Online":"Offline"}),(0,t.jsx)(E.E,{colorScheme:x.color,fontSize:"sm",children:o.type.toUpperCase()})]})]})}),o.description&&(0,t.jsx)(c.E,{color:"gray.300",fontSize:"sm",children:o.description}),o.online?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(d.z,{spacing:8,justify:"space-around",children:[(0,t.jsxs)(A.r,{children:[(0,t.jsx)(N.v,{color:"gray.400",children:(0,t.jsxs)(d.z,{children:[(0,t.jsx)(w.x$1,{}),(0,t.jsx)(c.E,{children:"Players"})]})}),(0,t.jsxs)(R.k,{color:"white",children:[f,"/",o.maxPlayers||"?"]})]}),o.map&&(0,t.jsxs)(A.r,{children:[(0,t.jsx)(N.v,{color:"gray.400",children:(0,t.jsxs)(d.z,{children:[(0,t.jsx)(w.pBr,{}),(0,t.jsx)(c.E,{children:"Map"})]})}),(0,t.jsx)(R.k,{color:"white",fontSize:"lg",children:o.map})]}),o.ping&&(0,t.jsxs)(A.r,{children:[(0,t.jsx)(N.v,{color:"gray.400",children:"Ping"}),(0,t.jsxs)(R.k,{color:o.ping<100?"".concat(x.color,".400"):o.ping<200?"yellow.400":"red.400",children:[o.ping,"ms"]})]})]}),(0,t.jsx)(h.$,{size:"sm",variant:"ghost",colorScheme:x.color,onClick:()=>l(!s),leftIcon:(0,t.jsx)(w.__w,{}),children:s?"Hide Details":"Show Details"}),(0,t.jsx)(z.S,{in:s,children:(0,t.jsxs)(p.T,{align:"stretch",spacing:3,pt:2,children:[(0,t.jsx)(P.c,{borderColor:"whiteAlpha.200"}),(0,t.jsxs)(n.a,{children:[(0,t.jsx)(c.E,{color:"gray.400",mb:2,fontWeight:"bold",children:"Connection Information"}),(0,t.jsx)(p.T,{align:"stretch",spacing:2,children:(0,t.jsxs)(d.z,{children:[(0,t.jsxs)(D.C,{p:2,borderRadius:"md",bg:"gray.700",color:"".concat(x.color,".300"),children:[o.host,":",o.port]}),o.hasPassword&&(0,t.jsxs)(D.C,{p:2,borderRadius:"md",bg:"gray.700",color:"".concat(x.color,".300"),children:["Password: ",o.password]}),(0,t.jsx)(k.m,{label:i?"Copied!":"Copy All",children:(0,t.jsx)(h.$,{size:"sm",variant:"ghost",colorScheme:i?"green":x.color,onClick:u,children:(0,t.jsx)(g.I,{as:i?w.CMH:w.paH})})})]})})]}),(0,t.jsxs)(n.a,{children:[(0,t.jsx)(c.E,{color:"gray.400",mb:2,fontWeight:"bold",children:"How to Connect"}),(0,t.jsx)(D.C,{display:"block",whiteSpace:"pre",p:3,borderRadius:"md",bg:"gray.700",color:"".concat(x.color,".300"),children:(()=>{switch(o.type.toLowerCase()){case"minecraft":case"minecraftbe":return'1. Open Minecraft\n2. Click "Multiplayer"\n3. Click "Add Server"\n4. Enter server address: '.concat(o.host,":").concat(o.port).concat(o.hasPassword?"\n5. Enter Password: ".concat(o.password):"");case"sdtd":return'1. Open 7 Days to Die\n2. Click "Join Game"\n3. Click "Server Browser"\n4. Search for "'.concat(o.name,'"\n').concat(o.hasPassword?"5. Enter Password: ".concat(o.password):"");default:return"Connect using: ".concat(o.host,":").concat(o.port).concat(o.hasPassword?"\nPassword: ".concat(o.password):"")}})()})]})]})})]}):(0,t.jsx)(c.E,{color:"red.400",children:o.error||"Server is offline"}),(0,t.jsxs)(d.z,{fontSize:"sm",color:"gray.500",spacing:2,children:[(0,t.jsx)(w.w_X,{}),(0,t.jsxs)(c.E,{children:["Last updated: ",new Date(o.lastUpdated).toLocaleTimeString()]})]})]})})};var I=o(9557),M=o(52922),O=o(59365),L=o(40443),B=o(63730),U=o(64057),J=o(19521),$=o(50691),q=o(61481),H=o(55631);function Y(e){var r;let{isOpen:o,onClose:s,onSave:i,server:g}=e,[x,m]=(0,a.useState)((null==g?void 0:g.name)||""),[b,w]=(0,a.useState)((null==g?void 0:g.description)||""),[C,S]=(0,a.useState)((null==g?void 0:g.host)||""),[_,k]=(0,a.useState)((null==g?void 0:g.port)||25565),[E,A]=(0,a.useState)((null==g?void 0:g.type)||""),[N,R]=(0,a.useState)((null==g?void 0:g.hasPassword)||!1),[z,P]=(0,a.useState)((null==g?void 0:g.password)||""),[D,T]=(0,a.useState)(""),[F,Y]=(0,a.useState)([]),[G,W]=(0,a.useState)(!1),[X,V]=(0,a.useState)(!1),K=(0,l.d)();(0,a.useEffect)(()=>{g&&(m(g.name||""),w(g.description||""),S(g.host),k(g.port),A(g.type),R(g.hasPassword||!1),P(g.password||""),T(""))},[g]),(0,a.useEffect)(()=>{D?Q():(Y([]),V(!1))},[D]);let Q=async()=>{try{W(!0);let e=await fetch("/api/gameservers/games?search=".concat(encodeURIComponent(D)));if(!e.ok)throw Error("Failed to search games");let r=await e.json();Y(r||[]),V(!0)}catch(e){Y([]),V(!1),K({title:"Error",description:"Failed to search games",status:"error",duration:5e3,isClosable:!0})}finally{W(!1)}},Z=async()=>{if(!x||!C||!_||!E)return void K({title:"Error",description:"Please fill in all required fields",status:"error",duration:5e3,isClosable:!0});if(N&&!z)return void K({title:"Error",description:"Password is required when password protection is enabled",status:"error",duration:5e3,isClosable:!0});try{let e={_id:null==g?void 0:g._id,name:x,description:b,host:C,port:Number(_),type:E,hasPassword:N,password:N?z:void 0};await i(e),s()}catch(e){K({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0})}},ee=e=>{A(e.id),T(e.name),V(!1)};return(0,t.jsxs)(I.aF,{isOpen:o,onClose:s,size:"lg",children:[(0,t.jsx)(f.m,{backdropFilter:"blur(10px)"}),(0,t.jsxs)(M.$,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,t.jsx)(v.r,{color:"white",children:g?"Edit Server":"Add Server"}),(0,t.jsx)(O.s,{}),(0,t.jsx)(j.c,{children:(0,t.jsxs)(p.T,{spacing:6,children:[(0,t.jsxs)(L.MJ,{isRequired:!0,children:[(0,t.jsx)(B.l,{color:"gray.200",children:"Server Name"}),(0,t.jsx)(U.p,{value:x,onChange:e=>m(e.target.value),placeholder:"My Game Server",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,t.jsxs)(L.MJ,{children:[(0,t.jsx)(B.l,{color:"gray.200",children:"Description"}),(0,t.jsx)(U.p,{value:b,onChange:e=>w(e.target.value),placeholder:"Optional description",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,t.jsxs)(L.MJ,{isRequired:!0,children:[(0,t.jsx)(B.l,{color:"gray.200",children:"Game Type"}),(0,t.jsx)(U.p,{value:D,onChange:e=>{T(e.target.value),A("")},placeholder:"Search for a game...",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"},onFocus:()=>V(!0)}),G&&(0,t.jsx)(J.s,{justify:"center",mt:2,children:(0,t.jsx)(u.y,{size:"sm",color:"blue.300"})}),X&&F.length>0&&(0,t.jsx)(n.a,{mt:2,border:"1px",borderColor:"whiteAlpha.200",borderRadius:"md",maxH:"200px",overflowY:"auto",bg:"gray.700",children:(0,t.jsx)($.B8,{spacing:0,children:(F||[]).map(e=>(0,t.jsx)($.ck,{p:2,cursor:"pointer",_hover:{bg:"whiteAlpha.100"},onClick:()=>ee(e),color:"gray.200",children:(0,t.jsx)(c.E,{children:e.name})},e.id))})}),E&&(0,t.jsxs)(c.E,{mt:1,fontSize:"sm",color:"blue.300",children:["Selected: ",(null==(r=F.find(e=>e.id===E))?void 0:r.name)||E]})]}),(0,t.jsxs)(L.MJ,{isRequired:!0,children:[(0,t.jsx)(B.l,{color:"gray.200",children:"Host"}),(0,t.jsx)(U.p,{value:C,onChange:e=>S(e.target.value),placeholder:"localhost or IP address",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,t.jsxs)(L.MJ,{isRequired:!0,children:[(0,t.jsx)(B.l,{color:"gray.200",children:"Port"}),(0,t.jsx)(q.Q7,{value:_,min:1,max:65535,children:(0,t.jsx)(q.OO,{onChange:e=>k(parseInt(e.target.value)||25565),bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})})]}),(0,t.jsx)(L.MJ,{children:(0,t.jsxs)(d.z,{children:[(0,t.jsx)(B.l,{color:"gray.200",mb:"0",children:"Password Protected"}),(0,t.jsx)(H.d,{colorScheme:"blue",isChecked:N,onChange:e=>R(e.target.checked)})]})}),N&&(0,t.jsxs)(L.MJ,{isRequired:!0,children:[(0,t.jsx)(B.l,{color:"gray.200",children:"Server Password"}),(0,t.jsx)(U.p,{value:z,onChange:e=>P(e.target.value),placeholder:"Enter server password",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]})]})}),(0,t.jsxs)(y.j,{gap:3,children:[(0,t.jsx)(h.$,{variant:"ghost",onClick:s,color:"gray.300",_hover:{bg:"whiteAlpha.100"},children:"Cancel"}),(0,t.jsx)(h.$,{colorScheme:"blue",onClick:Z,_hover:{bg:"blue.500"},_active:{bg:"blue.600"},children:g?"Save Changes":"Add Server"})]})]})]})}var G=o(53424),W=!0;function X(){var e;let{data:r}=(0,G.useSession)(),o=(null==r||null==(e=r.user)?void 0:e.isAdmin)===!0,[S,_]=(0,a.useState)([]),[k,E]=(0,a.useState)(!0),[A,N]=(0,a.useState)(!1),[R,z]=(0,a.useState)(),[P,D]=(0,a.useState)(),{isOpen:T,onOpen:I,onClose:M}=(0,s.j)(),{isOpen:O,onOpen:L,onClose:B}=(0,s.j)(),U=a.useRef(null),J=(0,l.d)(),$=async()=>{try{N(!0);let e=await fetch("/api/gameservers/query");if(!e.ok)throw Error("Failed to fetch server status");let r=await e.json(),o=Array.isArray(r)?r:[];_(o)}catch(e){_([]),J({title:"Error",description:e instanceof Error?e.message:"Failed to fetch server status",status:"error",duration:5e3,isClosable:!0})}finally{E(!1),N(!1)}};(0,a.useEffect)(()=>{$();let e=setInterval($,3e4);return()=>clearInterval(e)},[]);let q=async e=>{try{let r,o=await fetch("/api/gameservers/games?type=".concat(encodeURIComponent(e.type)));if(!o.ok)throw Error("Invalid game type");let{type:t}=await o.json();e.type=t;let a=e._id?"PUT":"POST";if("PUT"===a){let{_id:o,...t}=e;r={id:o,...t}}else{let{_id:o,...t}=e;r=t}let s=await fetch("/api/gameservers/manage",{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),l=await s.json();if(!s.ok)throw Error(l.error||"Failed to save server");$(),J({title:e._id?"Server Updated":"Server Added",description:e._id?"The server has been updated successfully":"The server has been added successfully",status:"success",duration:3e3,isClosable:!0})}catch(e){throw J({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0}),e}},H=e=>{z({_id:e._id,name:e.name,type:e.type,host:e.host,port:e.port,description:e.description,hasPassword:e.hasPassword,password:e.password}),I()},W=e=>{z(e),L()},X=async()=>{if(R)try{let e={id:R._id},r=await fetch("/api/gameservers/manage",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),o=await r.json();if(!r.ok)throw Error(o.error||"Failed to delete server");_(e=>e.filter(e=>e._id!==R._id)),z(void 0),B(),J({title:"Success",description:"Server deleted successfully",status:"success",duration:5e3,isClosable:!0})}catch(e){J({title:"Error",description:e.message||"Failed to delete server",status:"error",duration:5e3,isClosable:!0})}};return(0,t.jsx)(C.A,{children:(0,t.jsxs)(n.a,{w:"full",p:4,children:[(0,t.jsxs)(n.a,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"green.400",boxShadow:"0 0 15px rgba(72, 187, 120, 0.4)",textAlign:"center",children:[(0,t.jsx)(i.D,{size:"2xl",bgGradient:"linear(to-r, green.300, teal.400)",bgClip:"text",mb:4,children:"Game Servers"}),(0,t.jsx)(c.E,{color:"gray.300",fontSize:"lg",mb:6,children:"Monitor and manage your game servers in real-time"}),(0,t.jsxs)(d.z,{spacing:4,justify:"center",children:[o&&(0,t.jsx)(h.$,{leftIcon:(0,t.jsx)(w.OiG,{}),colorScheme:"green",onClick:()=>{z(void 0),I()},size:"md",variant:"solid",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Server"}),(0,t.jsx)(h.$,{leftIcon:(0,t.jsx)(w.DIg,{}),onClick:$,isLoading:A,loadingText:"Refreshing",size:"md",variant:"outline",colorScheme:"green",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Refresh Status"})]})]}),(0,t.jsx)(n.a,{maxW:"7xl",mx:"auto",children:k?(0,t.jsxs)(p.T,{py:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:[(0,t.jsx)(u.y,{size:"xl",color:"green.400"}),(0,t.jsx)(c.E,{color:"gray.400",children:"Loading servers..."})]}):S&&0!==S.length?(0,t.jsx)(x.r,{columns:{base:1,md:2,lg:3},spacing:6,children:(S||[]).map((e,r)=>(0,t.jsxs)(n.a,{position:"relative",transition:"all 0.2s",_hover:o?{transform:"translateY(-4px)","& > .server-actions":{opacity:1,transform:"translateY(0)"}}:void 0,children:[(0,t.jsx)(F,{server:e}),o&&(0,t.jsxs)(d.z,{className:"server-actions",position:"absolute",top:2,right:2,spacing:1,bg:"blackAlpha.800",p:1,borderRadius:"md",opacity:0,transform:"translateY(-4px)",transition:"all 0.2s",zIndex:2,backdropFilter:"blur(8px)",children:[(0,t.jsx)(m.K,{"aria-label":"Edit server",icon:(0,t.jsx)(w.uO9,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:()=>H(e),_hover:{bg:"green.700"}}),(0,t.jsx)(m.K,{"aria-label":"Delete server",icon:(0,t.jsx)(w.qbC,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>W(e),_hover:{bg:"red.700"}})]})]},"".concat(e._id||"".concat(e.host,":").concat(e.port,"-").concat(r))))}):(0,t.jsxs)(p.T,{spacing:4,p:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",textAlign:"center",children:[(0,t.jsx)(g.I,{as:w.pBr,boxSize:12,color:"green.400"}),(0,t.jsx)(c.E,{color:"gray.300",fontSize:"lg",children:"No game servers found"}),(0,t.jsx)(c.E,{fontSize:"md",color:"gray.500",children:"Add your first game server to start monitoring"}),o&&(0,t.jsx)(h.$,{leftIcon:(0,t.jsx)(w.OiG,{}),colorScheme:"green",onClick:()=>{z(void 0),I()},size:"md",variant:"outline",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Your First Server"})]})}),o&&(0,t.jsx)(Y,{isOpen:T,onClose:M,server:R,onSave:q}),(0,t.jsx)(b.Lt,{isOpen:O,leastDestructiveRef:U,onClose:B,isCentered:!0,children:(0,t.jsx)(f.m,{backdropFilter:"blur(10px)",children:(0,t.jsxs)(b.EO,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,t.jsx)(v.r,{fontSize:"lg",fontWeight:"bold",color:"white",children:"Delete Server"}),(0,t.jsxs)(j.c,{color:"gray.300",children:["Are you sure you want to delete ",(null==R?void 0:R.name)||"".concat(null==R?void 0:R.host,":").concat(null==R?void 0:R.port),"? This action cannot be undone."]}),(0,t.jsxs)(y.j,{gap:3,children:[(0,t.jsx)(h.$,{ref:U,onClick:B,variant:"ghost",color:"gray.300",children:"Cancel"}),(0,t.jsx)(h.$,{colorScheme:"red",onClick:X,_hover:{bg:"red.600"},_active:{bg:"red.700"},children:"Delete"})]})]})})})]})})}},28245:(e,r,o)=>{"use strict";o.d(r,{j:()=>c});var t=o(94513),a=o(55100),s=o(22697),l=o(9557),n=o(2923),i=o(33225);let c=(0,n.R)((e,r)=>{let{className:o,...n}=e,c=(0,s.cx)("chakra-modal__footer",o),d=(0,l.x5)(),h=(0,a.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...d.footer});return(0,t.jsx)(i.B.footer,{ref:r,...n,__css:h,className:c})});c.displayName="ModalFooter"},37211:(e,r,o)=>{"use strict";var t=o(13341),a={"text/plain":"Text","text/html":"Url",default:"Text"};e.exports=function(e,r){var o,s,l,n,i,c,d,h,p=!1;r||(r={}),l=r.debug||!1;try{if(i=t(),c=document.createRange(),d=document.getSelection(),(h=document.createElement("span")).textContent=e,h.ariaHidden="true",h.style.all="unset",h.style.position="fixed",h.style.top=0,h.style.clip="rect(0, 0, 0, 0)",h.style.whiteSpace="pre",h.style.webkitUserSelect="text",h.style.MozUserSelect="text",h.style.msUserSelect="text",h.style.userSelect="text",h.addEventListener("copy",function(o){if(o.stopPropagation(),r.format)if(o.preventDefault(),void 0===o.clipboardData){l&&console.warn("unable to use e.clipboardData"),l&&console.warn("trying IE specific stuff"),window.clipboardData.clearData();var t=a[r.format]||a.default;window.clipboardData.setData(t,e)}else o.clipboardData.clearData(),o.clipboardData.setData(r.format,e);r.onCopy&&(o.preventDefault(),r.onCopy(o.clipboardData))}),document.body.appendChild(h),c.selectNodeContents(h),d.addRange(c),!document.execCommand("copy"))throw Error("copy command was unsuccessful");p=!0}catch(t){l&&console.error("unable to copy using execCommand: ",t),l&&console.warn("trying IE specific stuff");try{window.clipboardData.setData(r.format||"text",e),r.onCopy&&r.onCopy(window.clipboardData),p=!0}catch(t){l&&console.error("unable to copy using clipboardData: ",t),l&&console.error("falling back to prompt"),o="message"in r?r.message:"Copy to clipboard: #{key}, Enter",s=(/mac os x/i.test(navigator.userAgent)?"⌘":"Ctrl")+"+C",n=o.replace(/#{\s*key\s*}/g,s),window.prompt(n,e)}}finally{d&&("function"==typeof d.removeRange?d.removeRange(c):d.removeAllRanges()),h&&document.body.removeChild(h),i()}return p}},50691:(e,r,o)=>{"use strict";o.d(r,{B8:()=>u,ck:()=>g});var t=o(94513),a=o(75387),s=o(29035),l=o(47133),n=o(49217),i=o(2923),c=o(56915),d=o(33225);let[h,p]=(0,s.q)({name:"ListStylesContext",errorMessage:"useListStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<List />\" "}),u=(0,i.R)(function(e,r){let o=(0,c.o)("List",e),{children:s,styleType:n="none",stylePosition:i,spacing:p,...u}=(0,a.M)(e),g=(0,l.a)(s);return(0,t.jsx)(h,{value:o,children:(0,t.jsx)(d.B.ul,{ref:r,listStyleType:n,listStylePosition:i,role:"list",__css:{...o.container,...p?{"& > *:not(style) ~ *:not(style)":{mt:p}}:{}},...u,children:g})})});u.displayName="List",(0,i.R)((e,r)=>{let{as:o,...a}=e;return(0,t.jsx)(u,{ref:r,as:"ol",styleType:"decimal",marginStart:"1em",...a})}).displayName="OrderedList",(0,i.R)(function(e,r){let{as:o,...a}=e;return(0,t.jsx)(u,{ref:r,as:"ul",styleType:"initial",marginStart:"1em",...a})}).displayName="UnorderedList";let g=(0,i.R)(function(e,r){let o=p();return(0,t.jsx)(d.B.li,{ref:r,...e,__css:o.item})});g.displayName="ListItem",(0,i.R)(function(e,r){let o=p();return(0,t.jsx)(n.I,{ref:r,role:"presentation",...e,__css:o.icon})}).displayName="ListIcon"},55631:(e,r,o)=>{"use strict";o.d(r,{d:()=>h});var t=o(94513),a=o(75387),s=o(22697),l=o(94285),n=o(96027),i=o(2923),c=o(56915),d=o(33225);let h=(0,i.R)(function(e,r){let o=(0,c.o)("Switch",e),{spacing:i="0.5rem",children:h,...p}=(0,a.M)(e),{getIndicatorProps:u,getInputProps:g,getCheckboxProps:x,getRootProps:m,getLabelProps:b}=(0,n.v)(p),f=(0,l.useMemo)(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...o.container}),[o.container]),v=(0,l.useMemo)(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...o.track}),[o.track]),j=(0,l.useMemo)(()=>({userSelect:"none",marginStart:i,...o.label}),[i,o.label]);return(0,t.jsxs)(d.B.label,{...m(),className:(0,s.cx)("chakra-switch",e.className),__css:f,children:[(0,t.jsx)("input",{className:"chakra-switch__input",...g({},r)}),(0,t.jsx)(d.B.span,{...x(),className:"chakra-switch__track",__css:v,children:(0,t.jsx)(d.B.span,{__css:o.thumb,className:"chakra-switch__thumb",...u()})}),h&&(0,t.jsx)(d.B.span,{className:"chakra-switch__label",...b(),__css:j,children:h})]})});h.displayName="Switch"},56966:(e,r,o)=>{"use strict";o.d(r,{v:()=>i});var t=o(94513),a=o(22697),s=o(12183),l=o(2923),n=o(33225);let i=(0,l.R)(function(e,r){let o=(0,s.E)();return(0,t.jsx)(n.B.dt,{ref:r,...e,className:(0,a.cx)("chakra-stat__label",e.className),__css:o.label})});i.displayName="StatLabel"},59365:(e,r,o)=>{"use strict";o.d(r,{s:()=>i});var t=o(94513),a=o(22697),s=o(50614),l=o(9557),n=o(33021);let i=(0,o(2923).R)((e,r)=>{let{onClick:o,className:i,...c}=e,{onClose:d}=(0,l.k3)(),h=(0,a.cx)("chakra-modal__close-btn",i),p=(0,l.x5)();return(0,t.jsx)(n.J,{ref:r,__css:p.closeButton,className:h,onClick:(0,s.H)(o,e=>{e.stopPropagation(),d()}),...c})});i.displayName="ModalCloseButton"},79961:(e,r,o)=>{"use strict";o.d(r,{C:()=>c});var t=o(94513),a=o(75387),s=o(22697),l=o(2923),n=o(56915),i=o(33225);let c=(0,l.R)(function(e,r){let o=(0,n.V)("Code",e),{className:l,...c}=(0,a.M)(e);return(0,t.jsx)(i.B.code,{ref:r,className:(0,s.cx)("chakra-code",e.className),...c,__css:{display:"inline-block",...o}})});c.displayName="Code"},86153:(e,r,o)=>{"use strict";o.d(r,{k:()=>i});var t=o(94513),a=o(22697),s=o(12183),l=o(2923),n=o(33225);let i=(0,l.R)(function(e,r){let o=(0,s.E)();return(0,t.jsx)(n.B.dd,{ref:r,...e,className:(0,a.cx)("chakra-stat__number",e.className),__css:{...o.number,fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums"}})});i.displayName="StatNumber"}},e=>{var r=r=>e(e.s=r);e.O(0,[4108,3256,9998,4976,217,2965,3177,3035,341,636,6593,8792],()=>r(469)),_N_E=e.O()}]);