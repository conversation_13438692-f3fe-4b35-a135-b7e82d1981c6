// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { dashboardConfig } from '../../../core/config';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = await getServerSession(req, res, authOptions);

    if (!session?.user) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const { guildId, token } = dashboardConfig.bot;
    if (!token || !guildId) {
      console.error('Missing bot configuration');
      return res.status(500).json({ error: 'Bot configuration missing' });
    }

    if (req.method === 'GET') {
      try {
        // Fetch guild data from Discord API
        const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}`, {
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        if (!response.ok) {
          const errorData = await response.text();
          console.error('Discord API error fetching guild:', errorData);
          throw new Error(`Failed to fetch guild data: ${response.status}`);
        }

        const guildData = await response.json();

        // Fetch bot's nickname in the guild
        const botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/${dashboardConfig.bot.clientId}`, {
          headers: {
            Authorization: `Bot ${token}`,
          },
        });

        let botName = '';
        if (botResponse.ok) {
          const botData = await botResponse.json();
          botName = botData.nick || botData.user?.username || '';
        } else {
          console.warn('Could not fetch bot nickname:', await botResponse.text());
        }

        return res.status(200).json({
          id: guildData.id,
          name: guildData.name,
          icon: guildData.icon,
          botName: botName,
          features: guildData.features,
          description: guildData.description,
          preferred_locale: guildData.preferred_locale,
        });
      } catch (error) {
        console.error('Error fetching guild data:', error);
        return res.status(500).json({ 
          error: 'Failed to fetch guild data',
          details: error.message 
        });
      }
    }

    if (req.method === 'PATCH') {
      // Check if user has admin permissions
      if (!(session.user as any).isAdmin) {
        return res.status(403).json({ error: 'Forbidden - Admin access required' });
      }

      const { guildName, botName, icon } = req.body;
      console.log('Updating guild settings:', { guildName, botName, hasIcon: Boolean(icon) });

      try {
        const updates = [];
        let guildUpdateError = null;
        let botUpdateError = null;

        // Update guild name or icon
        if (guildName || icon) {
          try {
            const payload: any = {};
            if (guildName) payload.name = guildName;
            if (icon) payload.icon = icon; // data URI string e.g., data:image/png;base64,...

            const guildResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}`, {
              method: 'PATCH',
              headers: {
                Authorization: `Bot ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(payload),
            });

            if (!guildResponse.ok) {
              const errorData = await guildResponse.text();
              console.error('Discord API error updating guild settings:', errorData);
              guildUpdateError = `Failed to update guild settings: ${guildResponse.status}`;
            } else {
              updates.push(icon ? 'guild icon' : 'guild name');
            }
          } catch (error) {
            console.error('Error updating guild name/icon:', error);
            guildUpdateError = error.message;
          }
        }

        // Update bot nickname if provided
        if (botName) {
          try {
            // Prefer the @me endpoint which is recommended for bots
            let botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/@me`, {
              method: 'PATCH',
              headers: {
                Authorization: `Bot ${token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ nick: botName }),
            });

            // Some older API behaviours require the /@me/nick endpoint
            if (botResponse.status === 404 || botResponse.status === 405) {
              botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/@me/nick`, {
                method: 'PATCH',
                headers: {
                  Authorization: `Bot ${token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ nick: botName }),
              });
            }

            // Fallback to explicit user-id endpoint if still not ok
            if (!botResponse.ok) {
              botResponse = await fetch(`https://discord.com/api/v10/guilds/${guildId}/members/${dashboardConfig.bot.clientId}`, {
                method: 'PATCH',
                headers: {
                  Authorization: `Bot ${token}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({ nick: botName }),
              });
            }

            if (!botResponse.ok) {
              const errorData = await botResponse.text();
              console.error('Discord API error updating bot nickname:', errorData);
              botUpdateError = `Failed to update bot nickname: ${botResponse.status}`;
            } else {
              updates.push('bot nickname');
            }
          } catch (error) {
            console.error('Error updating bot nickname:', error);
            botUpdateError = error.message;
          }
        }

        // Handle errors and responses
        if (guildUpdateError || botUpdateError) {
          const errors = [];
          if (guildUpdateError) errors.push(guildUpdateError);
          if (botUpdateError) errors.push(botUpdateError);
          
          const statusCode = updates.length > 0 ? 207 : 500; // 207 = Multi-Status (partial success)

          return res.status(statusCode).json({
            error: updates.length > 0 ? 'Partial success' : 'Failed to update settings',
            details: errors.join('; '),
            partialSuccess: updates.length > 0,
            succeeded: updates
          });
        }

        if (updates.length === 0) {
          return res.status(400).json({ error: 'No valid updates provided' });
        }

        return res.status(200).json({
          message: `Successfully updated ${updates.join(' and ')}`,
          updated: updates
        });
      } catch (error) {
        console.error('Error updating guild:', error);
        return res.status(500).json({ 
          error: 'Failed to update guild settings',
          details: error.message
        });
      }
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (error) {
    console.error('Error in guild handler:', error);
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message
    });
  }
} 