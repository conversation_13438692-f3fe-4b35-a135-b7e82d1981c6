import React, { useState, memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Box,
  Text,
  VStack,
  HStack,
  Select,
  Input,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Badge,
  Alert,
  AlertIcon,
  AlertDescription,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Code,
  Collapse,
  SimpleGrid,
  Switch,
  Textarea,
  Divider,
} from '@chakra-ui/react';
import { FiSettings, FiHelpCircle, FiEye, FiEyeOff, FiCopy } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';

interface ConditionNodeData {
  label: string;
  conditionType?: string;
  value?: string;
  comparison?: string;
  operator?: string;
  logicalOperator?: 'AND' | 'OR';
  conditions?: Array<{
    type: string;
    operator: string;
    value: string;
  }>;
  caseSensitive?: boolean;
  description?: string;
}

// Available variables for condition context
const conditionVariables = {
  user: [
    { name: '{user.id}', description: 'User ID', icon: '🆔' },
    { name: '{user.username}', description: 'Username', icon: '👤' },
    { name: '{user.displayName}', description: 'Display Name', icon: '📝' },
    { name: '{user.roles}', description: 'User Roles (array)', icon: '🎭' },
    { name: '{user.permissions}', description: 'User Permissions (array)', icon: '🔐' },
    { name: '{user.isBot}', description: 'Is Bot (true/false)', icon: '🤖' },
    { name: '{user.createdAt}', description: 'Account Creation Date', icon: '📅' },
    { name: '{user.joinedAt}', description: 'Server Join Date', icon: '🚪' },
  ],
  channel: [
    { name: '{channel.id}', description: 'Channel ID', icon: '🆔' },
    { name: '{channel.name}', description: 'Channel Name', icon: '📺' },
    { name: '{channel.type}', description: 'Channel Type', icon: '📋' },
    { name: '{channel.nsfw}', description: 'Is NSFW (true/false)', icon: '🔞' },
    { name: '{channel.memberCount}', description: 'Member Count (number)', icon: '👥' },
  ],
  server: [
    { name: '{server.id}', description: 'Server ID', icon: '🆔' },
    { name: '{server.name}', description: 'Server Name', icon: '🏠' },
    { name: '{server.memberCount}', description: 'Total Members (number)', icon: '👥' },
    { name: '{server.boostLevel}', description: 'Boost Level (number)', icon: '🚀' },
    { name: '{server.owner}', description: 'Server Owner ID', icon: '👑' },
  ],
  message: [
    { name: '{message.content}', description: 'Message Content', icon: '💬' },
    { name: '{message.length}', description: 'Message Length (number)', icon: '📏' },
    { name: '{message.mentions}', description: 'Message Mentions (array)', icon: '📢' },
    { name: '{message.attachments}', description: 'Attachments Count (number)', icon: '📎' },
    { name: '{message.embeds}', description: 'Embeds Count (number)', icon: '📋' },
  ],
  api: [
    { name: '{response.status}', description: 'HTTP Status Code (number)', icon: '🔢' },
    { name: '{response.data}', description: 'Response Data', icon: '📊' },
    { name: '{response.error}', description: 'Error Message', icon: '❌' },
    { name: '{response.length}', description: 'Response Array Length', icon: '📏' },
  ],
  time: [
    { name: '{time.hour}', description: 'Current Hour (0-23)', icon: '🕐' },
    { name: '{time.day}', description: 'Day of Week (0-6)', icon: '📅' },
    { name: '{time.date}', description: 'Current Date', icon: '📆' },
    { name: '{time.timestamp}', description: 'Unix Timestamp', icon: '⏰' },
  ],
  random: [
    { name: '{random.number}', description: 'Random Number (1-100)', icon: '🎲' },
    { name: '{random.boolean}', description: 'Random True/False', icon: '🎯' },
  ],
};

const conditionTypes = [
  // User Conditions
  { value: 'userHasRole', label: '🎭 User Has Role', category: 'User', description: 'Check if user has a specific role' },
  { value: 'userIsAdmin', label: '👑 User Is Admin', category: 'User', description: 'Check if user is server admin' },
  { value: 'userHasPermission', label: '🔐 User Has Permission', category: 'User', description: 'Check if user has specific permission' },
  { value: 'userIsBot', label: '🤖 User Is Bot', category: 'User', description: 'Check if user is a bot' },
  { value: 'userJoinedRecently', label: '🚪 User Joined Recently', category: 'User', description: 'Check if user joined within timeframe' },
  
  // Message Conditions
  { value: 'messageContains', label: '💬 Message Contains', category: 'Message', description: 'Check if message contains text' },
  { value: 'messageLength', label: '📏 Message Length', category: 'Message', description: 'Check message character count' },
  { value: 'messageHasMentions', label: '📢 Message Has Mentions', category: 'Message', description: 'Check if message mentions users/roles' },
  { value: 'messageHasAttachments', label: '📎 Message Has Attachments', category: 'Message', description: 'Check if message has files' },
  { value: 'messageHasEmbeds', label: '📋 Message Has Embeds', category: 'Message', description: 'Check if message has embeds' },
  
  // Channel Conditions
  { value: 'channelType', label: '📺 Channel Type', category: 'Channel', description: 'Check channel type (text, voice, etc.)' },
  { value: 'channelIsNSFW', label: '🔞 Channel Is NSFW', category: 'Channel', description: 'Check if channel is NSFW' },
  { value: 'channelMemberCount', label: '👥 Channel Member Count', category: 'Channel', description: 'Check voice channel member count' },
  
  // Server Conditions
  { value: 'serverMemberCount', label: '👥 Server Member Count', category: 'Server', description: 'Check total server members' },
  { value: 'serverBoostLevel', label: '🚀 Server Boost Level', category: 'Server', description: 'Check server boost level' },
  { value: 'serverName', label: '🏠 Server Name', category: 'Server', description: 'Check server name' },
  
  // Time Conditions
  { value: 'timeOfDay', label: '🕐 Time of Day', category: 'Time', description: 'Check current hour of day' },
  { value: 'dayOfWeek', label: '📅 Day of Week', category: 'Time', description: 'Check day of the week' },
  
  // API Conditions
  { value: 'apiResponseStatus', label: '🔢 API Response Status', category: 'API', description: 'Check HTTP status code' },
  { value: 'apiResponseData', label: '📊 API Response Data', category: 'API', description: 'Check API response content' },
  
  // Custom Conditions
  { value: 'customVariable', label: '⚙️ Custom Variable', category: 'Custom', description: 'Check custom variable value' },
  { value: 'randomChance', label: '🎲 Random Chance', category: 'Custom', description: 'Random percentage chance' },
];

const operators = [
  { value: 'equals', label: '= Equals', description: 'Exact match' },
  { value: 'notEquals', label: '≠ Not Equals', description: 'Does not match' },
  { value: 'contains', label: '🔍 Contains', description: 'Contains substring' },
  { value: 'notContains', label: '🚫 Not Contains', description: 'Does not contain substring' },
  { value: 'startsWith', label: '▶️ Starts With', description: 'Begins with text' },
  { value: 'endsWith', label: '◀️ Ends With', description: 'Ends with text' },
  { value: 'greaterThan', label: '> Greater Than', description: 'Numeric greater than' },
  { value: 'lessThan', label: '< Less Than', description: 'Numeric less than' },
  { value: 'greaterEqual', label: '≥ Greater or Equal', description: 'Numeric greater than or equal' },
  { value: 'lessEqual', label: '≤ Less or Equal', description: 'Numeric less than or equal' },
  { value: 'regex', label: '🔍 Regex Match', description: 'Regular expression pattern' },
  { value: 'inArray', label: '📋 In Array', description: 'Value exists in array' },
  { value: 'hasLength', label: '📏 Has Length', description: 'Array/string has specific length' },
];

const ConditionNode = memo(({ data, selected, id, updateNodeData: updateParentNodeData }: NodeProps<ConditionNodeData> & { updateNodeData?: (nodeId: string, newData: any) => void }) => {
  const { currentScheme } = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [nodeData, setNodeData] = useState<ConditionNodeData>(() => ({
    operator: 'equals',
    logicalOperator: 'AND',
    caseSensitive: false,
    conditions: [],
    ...data
  }));
  const [showVariables, setShowVariables] = useState(false);

  const updateNodeData = (updates: Partial<ConditionNodeData>) => {
    setNodeData(prev => ({ ...prev, ...updates }));
  };

  const handleModalClose = () => {
    // Update parent nodes array when modal closes
    if (updateParentNodeData && id) {
      updateParentNodeData(id, nodeData);
    }
    onClose();
  };

  const getConditionLabel = (conditionType: string) => {
    const condition = conditionTypes.find(c => c.value === conditionType);
    return condition ? condition.label.split(' ').slice(1).join(' ') : conditionType;
  };

  const getConditionIcon = (conditionType: string) => {
    const condition = conditionTypes.find(c => c.value === conditionType);
    return condition ? condition.label.split(' ')[0] : '❓';
  };

  const getOperatorLabel = (operator: string) => {
    return operators.find(o => o.value === operator)?.label || operator;
  };

  const copyVariable = (variable: string) => {
    navigator.clipboard.writeText(variable);
  };

  const renderVariablesList = () => (
    <Collapse in={showVariables} animateOpacity>
      <Box
        bg={currentScheme.colors.surface}
        border="1px solid"
        borderColor={currentScheme.colors.border}
        borderRadius="md"
        p={4}
        mt={3}
        maxH="400px"
        overflowY="auto"
      >
        <Accordion allowMultiple>
          {Object.entries(conditionVariables).map(([category, variables]) => (
            <AccordionItem key={category} border="none">
              <AccordionButton px={0} py={2}>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} textTransform="capitalize">
                    {category} Variables
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px={0} py={2}>
                <VStack spacing={2} align="stretch">
                  {variables.map((variable) => (
                    <HStack
                      key={variable.name}
                      spacing={2}
                      p={2}
                      bg={currentScheme.colors.background}
                      borderRadius="md"
                      cursor="pointer"
                      _hover={{ bg: currentScheme.colors.surface }}
                      onClick={() => copyVariable(variable.name)}
                    >
                      <Text fontSize="sm">{variable.icon}</Text>
                      <Code fontSize="xs" colorScheme="orange">
                        {variable.name}
                      </Code>
                      <Text fontSize="xs" color={currentScheme.colors.textSecondary} flex="1">
                        {variable.description}
                      </Text>
                      <IconButton
                        icon={<FiCopy />}
                        size="xs"
                        variant="ghost"
                        aria-label="Copy variable"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyVariable(variable.name);
                        }}
                      />
                    </HStack>
                  ))}
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </Box>
    </Collapse>
  );

  return (
    <>
      <Box
        bg={currentScheme.colors.surface}
        border={`2px solid ${selected ? '#f59e0b' : currentScheme.colors.border}`}
        borderRadius="md"
        p={2}
        minW="140px"
        maxW="180px"
        boxShadow="sm"
        position="relative"
        _hover={{
          boxShadow: 'md',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <Handle
          type="target"
          position={Position.Top}
          style={{
            background: '#f59e0b',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            top: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
        
        <VStack spacing={1} align="stretch">
          <HStack justify="space-between" align="center">
            <HStack spacing={1}>
              <Box
                bg="orange.500"
                color="white"
                borderRadius="full"
                p={0.5}
                fontSize="xs"
              >
                <FiHelpCircle />
              </Box>
              <Text fontSize="xs" fontWeight="bold" color={currentScheme.colors.text}>
                Condition
              </Text>
            </HStack>
            <IconButton
              icon={<FiSettings />}
              size="xs"
              variant="ghost"
              onClick={onOpen}
              aria-label="Configure condition"
            />
          </HStack>
          
          <Box>
            <HStack spacing={1}>
              {nodeData.conditionType && (
                <Text fontSize="xs">{getConditionIcon(nodeData.conditionType)}</Text>
              )}
            <Text fontSize="xs" color={currentScheme.colors.text} noOfLines={1}>
              {nodeData.conditionType ? getConditionLabel(nodeData.conditionType) : 'Select Condition'}
            </Text>
            </HStack>
          </Box>
          
          {nodeData.operator && nodeData.value && (
            <Box>
              <Text fontSize="xs" color={currentScheme.colors.textSecondary} noOfLines={1}>
                {getOperatorLabel(nodeData.operator).split(' ').slice(1).join(' ')} "{nodeData.value.length > 15 ? nodeData.value.substring(0, 15) + '...' : nodeData.value}"
              </Text>
            </Box>
          )}
          
          {/* Show TRUE/FALSE paths */}
          <HStack spacing={1} justify="space-between">
            <Badge size="xs" colorScheme="green">
              TRUE
            </Badge>
            <Badge size="xs" colorScheme="red">
              FALSE
            </Badge>
          </HStack>
        </VStack>
        
        {/* TRUE path (left) */}
        <Handle
          type="source"
          position={Position.Bottom}
          id="true"
          style={{
            background: '#38a169',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            bottom: '-6px',
            left: '25%',
            transform: 'translateX(-50%)',
          }}
        />
        
        {/* FALSE path (right) */}
        <Handle
          type="source"
          position={Position.Bottom}
          id="false"
          style={{
            background: '#e53e3e',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            bottom: '-6px',
            left: '75%',
            transform: 'translateX(-50%)',
          }}
        />
      </Box>

      {/* Enhanced Configuration Modal */}
      <Modal isOpen={isOpen} onClose={handleModalClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent bg={currentScheme.colors.background} border="2px solid" borderColor="orange.400" maxW="1200px">
          <ModalHeader color={currentScheme.colors.text}>
            ❓ Configure Condition
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={6} align="stretch">
              {/* Variables Helper */}
              <Box>
                <HStack justify="space-between" align="center" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    Available Variables
                  </Text>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={showVariables ? <FiEyeOff /> : <FiEye />}
                    onClick={() => setShowVariables(!showVariables)}
                  >
                    {showVariables ? 'Hide' : 'Show'} Variables
                  </Button>
                </HStack>
                <Alert status="info" borderRadius="md" mb={2}>
                  <AlertIcon />
                  <AlertDescription fontSize="sm">
                    💡 Use variables in your conditions! Click any variable below to copy it. Conditions determine which path (TRUE or FALSE) the flow takes.
                  </AlertDescription>
                </Alert>
                {renderVariablesList()}
              </Box>

              <Divider />

              {/* Tabbed Configuration */}
              <Tabs variant="enclosed" colorScheme="orange">
                <TabList>
                  <Tab>Basic Condition</Tab>
                  <Tab>Advanced</Tab>
                </TabList>
                
                <TabPanels>
                  {/* Basic Condition Tab */}
                  <TabPanel>
            <VStack spacing={4} align="stretch">
                      <FormControl isRequired>
                <FormLabel color={currentScheme.colors.text}>Condition Type</FormLabel>
                <Select
                  value={nodeData.conditionType || ''}
                  onChange={(e) => updateNodeData({ conditionType: e.target.value })}
                  placeholder="Select a condition type"
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                >
                          {Object.entries(
                            conditionTypes.reduce((acc, condition) => {
                              if (!acc[condition.category]) acc[condition.category] = [];
                              acc[condition.category].push(condition);
                              return acc;
                            }, {} as Record<string, typeof conditionTypes>)
                          ).map(([category, conditions]) => (
                            <optgroup key={category} label={category}>
                              {conditions.map((condition) => (
                    <option key={condition.value} value={condition.value}>
                      {condition.label}
                    </option>
                              ))}
                            </optgroup>
                  ))}
                </Select>
              </FormControl>
              
                      {nodeData.conditionType && (
                        <Alert status="info" borderRadius="md">
                          <AlertIcon />
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={1}>
                              {conditionTypes.find(c => c.value === nodeData.conditionType)?.label}
                            </Text>
                            <Text fontSize="sm">
                              {conditionTypes.find(c => c.value === nodeData.conditionType)?.description}
                            </Text>
                          </Box>
                        </Alert>
                      )}
                      
                      <SimpleGrid columns={2} spacing={4}>
              <FormControl>
                <FormLabel color={currentScheme.colors.text}>Operator</FormLabel>
                <Select
                            value={nodeData.operator || 'equals'}
                  onChange={(e) => updateNodeData({ operator: e.target.value })}
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                >
                  {operators.map((operator) => (
                    <option key={operator.value} value={operator.value}>
                      {operator.label}
                    </option>
                  ))}
                </Select>
              </FormControl>
              
              <FormControl>
                          <FormLabel color={currentScheme.colors.text}>Compare Value</FormLabel>
                <Input
                  value={nodeData.value || ''}
                  onChange={(e) => updateNodeData({ value: e.target.value })}
                            placeholder={
                              nodeData.conditionType === 'userHasRole' ? 'Member or {user.roles}' :
                              nodeData.conditionType === 'messageContains' ? 'hello or {message.content}' :
                              nodeData.conditionType === 'serverMemberCount' ? '100 or {server.memberCount}' :
                              nodeData.conditionType === 'timeOfDay' ? '14 (for 2 PM)' :
                              'Value to compare against'
                            }
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                />
              </FormControl>
                      </SimpleGrid>
                      
                      <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                        <Textarea
                          value={nodeData.description || ''}
                          onChange={(e) => updateNodeData({ description: e.target.value })}
                          placeholder="Describe what this condition checks for"
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                          minH="80px"
                        />
                      </FormControl>
                      
                      <Alert status="warning" borderRadius="md">
                        <AlertIcon />
                        <Box>
                          <Text fontSize="sm" fontWeight="bold" mb={1}>
                            Understanding TRUE/FALSE Paths
                          </Text>
                          <Text fontSize="sm">
                            • **TRUE (Left)**: Actions that run when the condition passes
                            • **FALSE (Right)**: Actions that run when the condition fails
                            • You can connect different actions to each path
                          </Text>
                        </Box>
                      </Alert>
                    </VStack>
                  </TabPanel>
                  
                  {/* Advanced Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                        Advanced Settings
                      </Text>
                      
                      <VStack spacing={4} align="stretch">
                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.caseSensitive}
                            onChange={(e) => updateNodeData({ caseSensitive: e.target.checked })}
                            colorScheme="orange"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Case Sensitive
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Match exact capitalization for text comparisons
                            </Text>
                          </VStack>
                        </HStack>
                      </VStack>
                      
                      <Alert status="info" borderRadius="md">
                        <AlertIcon />
                        <Box>
                          <Text fontSize="sm" fontWeight="bold" mb={2}>
                            💡 Condition Examples:
                          </Text>
                          <VStack align="start" spacing={1} fontSize="sm">
                            <Text>• Check if user has "Admin" role: **User Has Role** equals "Admin"</Text>
                            <Text>• Check if message contains swear word: **Message Contains** contains "badword"</Text>
                            <Text>• Check if server has many members: **Server Member Count** greater than 1000</Text>
                            <Text>• Check if it's nighttime: **Time of Day** greater than 22</Text>
                            <Text>• Check API status: **API Response Status** equals 200</Text>
                          </VStack>
                        </Box>
                      </Alert>
                      
                      <Alert status="warning" borderRadius="md">
                        <AlertIcon />
                        <Box>
                          <Text fontSize="sm" fontWeight="bold" mb={2}>
                            ⚠️ Important Notes:
                          </Text>
                          <VStack align="start" spacing={1} fontSize="sm">
                            <Text>• Use variables like {`{user.roles}`} to check dynamic values</Text>
                            <Text>• Number comparisons work with Greater/Less Than operators</Text>
                            <Text>• Text comparisons work with Contains, Starts With, etc.</Text>
                            <Text>• Always connect both TRUE and FALSE paths for complete logic</Text>
                          </VStack>
                        </Box>
                      </Alert>
                    </VStack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
              
              <Button
                colorScheme="orange"
                                  onClick={() => {
                    // Save the configuration
                    data.conditionType = nodeData.conditionType;
                    data.operator = nodeData.operator;
                    data.value = nodeData.value;
                  data.caseSensitive = nodeData.caseSensitive;
                  data.description = nodeData.description;
                  data.logicalOperator = nodeData.logicalOperator;
                  data.label = nodeData.conditionType ? getConditionLabel(nodeData.conditionType) : 'Condition';
                    onClose();
                  }}
                size="lg"
                width="full"
              >
                Save Configuration
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
});

ConditionNode.displayName = 'ConditionNode';

export default ConditionNode; 