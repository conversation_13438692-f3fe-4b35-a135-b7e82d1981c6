import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  devIndicators: false,
  // Transpile these ESM-only packages
  transpilePackages: ['jsonc-parser'],

  // Configure experimental features
  experimental: {
    serverActions: true,
    // Moving serverComponentsExternalPackages to the new location
    serverExternalPackages: []
  },

  // silence the "require function is used ..." warnings coming from jsonc-parser
  webpack(config) {
    // Add our custom alias to resolve paths from the project root
    config.resolve.alias['@'] = path.resolve(__dirname, 'src');

    const ignored = /jsonc-parser[\\/].+Critical dependency: require function/;
    config.ignoreWarnings = [
      ...(config.ignoreWarnings || []),
      (warn) =>
        typeof warn.message === 'string' &&
        ignored.test(warn.message),
    ];

    // Workers expect globalThis to be 'self'
    config.output = {
      ...config.output,
      globalObject: 'self',
    };

    return config;
  },

  // Configure hostname and port for Pterodactyl
  hostname: '0.0.0.0',
  port: process.env.SERVER_PORT || 3000
};

export default nextConfig;
