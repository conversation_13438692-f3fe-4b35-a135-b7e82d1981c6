import { FiShield, FiHelpCircle, FiCode, FiMusic, FiStar } from 'react-icons/fi';
import { ApplicationType } from '../types/applications';

export const DEFAULT_APPLICATION_TYPES: ApplicationType[] = [
  {
    id: 'moderator',
    title: 'Moderator Application',
    description: 'Join our moderation team to help maintain a safe and friendly community environment.',
    icon: 'FiShield',
    color: 'purple',
    gradient: {
      from: 'rgba(159, 122, 234, 0.4)',
      to: 'rgba(159, 122, 234, 0.1)'
    },
    isOpen: true,
    requiresApproval: true,
    questions: [
      {
        id: 'motivation',
        text: 'Why do you want to be a moderator?',
        type: 'text',
        required: true
      },
      {
        id: 'experience',
        text: 'What experience do you have with community moderation?',
        type: 'text',
        required: true
      },
      {
        id: 'scenario1',
        text: 'How would you handle a difficult situation with a user?',
        type: 'radio',
        required: true,
        options: [
          'Immediately ban them without warning',
          'Give them a warning and explain why their behavior is inappropriate',
          'Ignore it and let other moderators handle it',
          'Timeout the user and delete the messages'
        ],
        correctAnswer: 1
      }
    ],
    requirements: {
      minAge: 18,
      minAccountAge: 30, // days
      timezone: true,
      availability: true
    },
    metadata: {
      totalApplications: 0,
      acceptanceRate: 0,
      averageResponseTime: '48 hours'
    },
    quiz: [
      {
        question: "A user is spamming offensive content in multiple channels. What's your first action?",
        options: [
          "Immediately ban the user",
          "Warn them and explain why their behavior is inappropriate",
          "Temporarily mute them and remove the offensive content",
          "Ignore it and let other moderators handle it"
        ],
        correctAnswer: 2
      },
      {
        question: "Two users are having a heated argument that's disrupting chat. How do you handle it?",
        options: [
          "Mute both users immediately",
          "Take the discussion to a private channel and mediate",
          "Tell them to stop or face consequences",
          "Move their conversation to an appropriate channel and remind them of the rules"
        ],
        correctAnswer: 3
      },
      {
        question: "You notice a user sharing what might be personal information. What do you do?",
        options: [
          "Delete the message and send them a warning",
          "Temporarily mute them without explanation",
          "Remove the message and privately explain why sharing personal info is dangerous",
          "Publicly call them out to set an example"
        ],
        correctAnswer: 2
      },
      {
        question: "A user reports a bug in the bot. What's your response?",
        options: [
          "Tell them to check if it's already reported",
          "Document the issue and escalate to the development team",
          "Ignore it since you're not a developer",
          "Tell them to fix it themselves"
        ],
        correctAnswer: 1
      },
      {
        question: "You discover another moderator abusing their powers. What do you do?",
        options: [
          "Confront them publicly",
          "Remove their permissions immediately",
          "Document the abuse and report it to senior staff privately",
          "Ignore it to avoid conflict"
        ],
        correctAnswer: 2
      },
      {
        question: "A user is repeatedly asking for moderator roles. How do you respond?",
        options: [
          "Ban them for being annoying",
          "Direct them to the application process and explain the requirements",
          "Give them a trial moderator role",
          "Tell them to stop asking"
        ],
        correctAnswer: 1
      },
      {
        question: "You notice suspicious bot activity in the server. What's your first step?",
        options: [
          "Shut down all bots immediately",
          "Alert senior staff and monitor the situation",
          "Ignore it since it's probably nothing",
          "Delete all bot messages"
        ],
        correctAnswer: 1
      },
      {
        question: "A user claims they were wrongly banned. How do you proceed?",
        options: [
          "Unban them immediately",
          "Review the ban logs and discuss with the mod team",
          "Tell them bans are final",
          "Direct them to create a new account"
        ],
        correctAnswer: 1
      },
      {
        question: "Multiple users are using excessive caps and emojis. What do you do?",
        options: [
          "Mute everyone involved",
          "Delete all their messages",
          "Send a friendly reminder about chat etiquette",
          "Add a slowmode to the channel temporarily"
        ],
        correctAnswer: 2
      },
      {
        question: "You notice a potential raid beginning. What's your immediate action?",
        options: [
          "Ban all new accounts immediately",
          "Enable server lockdown and alert other moderators",
          "Warn users in chat about the raid",
          "Try to talk to the raiders"
        ],
        correctAnswer: 1
      }
    ]
  },
  {
    id: 'support',
    title: 'Support Team Application',
    description: 'Help other members with technical issues and general support inquiries.',
    icon: 'FiHelpCircle',
    color: 'blue',
    gradient: {
      from: 'rgba(66, 153, 225, 0.4)',
      to: 'rgba(66, 153, 225, 0.1)'
    },
    isOpen: true,
    requiresApproval: true,
    questions: [
      {
        id: 'tech_experience',
        text: 'What is your experience with technical support?',
        type: 'text',
        required: true
      },
      {
        id: 'availability',
        text: 'How many hours per week can you dedicate to support?',
        type: 'select',
        required: true,
        options: ['5-10 hours', '10-20 hours', '20+ hours']
      }
    ],
    requirements: {
      minAge: 16,
      timezone: true,
      availability: true
    },
    metadata: {
      totalApplications: 0,
      acceptanceRate: 0,
      averageResponseTime: '24 hours'
    }
  },
  {
    id: 'developer',
    title: 'Developer Team Application',
    description: 'Join our development team to help improve and maintain our bot and systems.',
    icon: 'FiCode',
    color: 'green',
    gradient: {
      from: 'rgba(72, 187, 120, 0.4)',
      to: 'rgba(72, 187, 120, 0.1)'
    },
    isOpen: false,
    requiresApproval: true,
    questions: [
      {
        id: 'programming_exp',
        text: 'What programming languages are you proficient in?',
        type: 'checkbox',
        required: true,
        options: ['JavaScript/TypeScript', 'Python', 'Java', 'C++', 'Other']
      },
      {
        id: 'github',
        text: 'Please provide your GitHub profile URL',
        type: 'text',
        required: true
      }
    ],
    requirements: {
      minAccountAge: 60, // days
      availability: true
    },
    metadata: {
      totalApplications: 0,
      acceptanceRate: 0,
      averageResponseTime: '72 hours',
      nextReviewDate: '2024-04-01'
    }
  }
]; 