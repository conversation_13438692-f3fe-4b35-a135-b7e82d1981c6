import { ButtonInteraction, ChannelType, Colors, PermissionsBitField, MessageFlags } from 'discord.js';
import { MongoClient, ObjectId } from 'mongodb';
import type { Addon, BotInstance } from '../../types/index.js';
import { dashboardConfig } from '../../dashboard/core/config.js';
import { Logger } from '../../core/Logger.js';

// Simple database helper – reuse dashboardConfig
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;
async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl);
  }
  return cachedClient.db(dbName).collection('tickets');
}

const logger = Logger.createAddonLogger('tickets');

const addon: Addon = {
  info: {
    name: 'tickets',
    version: '1.0.0',
    description: 'Support ticket buttons',
    author: 'auto'
  },
  commands: [],
  events: [
    {
      name: 'interactionCreate',
      once: false,
      execute: async (interaction: any) => {
        if (!interaction.isButton()) return;
        const custom = interaction.customId as string;
        if (!custom.startsWith('ticket_')) return;

        const [_, action, ticketId] = custom.split('_');
        const ticketsCol = await getDb();
        const ticket = await ticketsCol.findOne({ _id: new ObjectId(ticketId) });
        if (!ticket) {
          await interaction.reply({ content: 'Ticket not found in database.', ephemeral: true });
          return;
        }

        // Permissions check
        const member = interaction.member as any;
        const isStaff = (member.permissions as PermissionsBitField).has(PermissionsBitField.Flags.ManageGuild) || (dashboardConfig.dashboard.adminRoleIds || []).some((id: string)=> member.roles?.cache?.has(id));
        const isCreator = ticket.creatorId === interaction.user.id;

        switch (action) {
          case 'claim': {
            if (!isStaff) {
              return interaction.reply({ content: 'Only staff can claim tickets.', ephemeral: true });
            }

            // Toggle claim / unclaim
            if (ticket.claimedBy && ticket.claimedBy !== interaction.user.id) {
              return interaction.reply({ content: 'This ticket is already claimed by another staff member.', ephemeral: true });
            }

            const newClaimedBy = ticket.claimedBy === interaction.user.id ? null : interaction.user.id;
            await ticketsCol.updateOne({ _id: ticket._id }, { $set: { claimedBy: newClaimedBy } });

            // Build new components
            const components = [
              {
                type: 1,
                components: [
                  {
                    type: 2,
                    style: newClaimedBy ? 2 : 1, // secondary when unclaim, primary when claim
                    label: newClaimedBy ? 'Unclaim' : 'Claim',
                    custom_id: `ticket_claim_${ticket._id.toString()}`,
                    disabled: false,
                  },
                  {
                    type: 2,
                    style: 4,
                    label: 'Close',
                    custom_id: `ticket_close_${ticket._id.toString()}`,
                  },
                ],
              },
            ];

            await interaction.message.edit({ components });

            if (newClaimedBy) {
              await interaction.reply({ content: `${interaction.user} has claimed this ticket.`, ephemeral: false });
              if (dashboardConfig.bot.ticketLogChannelId) {
                await interaction.client.rest.post(`/channels/${dashboardConfig.bot.ticketLogChannelId}/messages`, {
                  body: {
                    embeds: [{
                      title: 'Ticket Claimed',
                      color: 0x3b82f6,
                      fields: [
                        { name: 'Ticket', value: ticket._id.toString(), inline: true },
                        { name: 'Staff', value: `<@${interaction.user.id}>`, inline: true },
                      ],
                      timestamp: new Date().toISOString(),
                    }]
                  }
                }).catch(()=>{});
              }
            } else {
              await interaction.reply({ content: `${interaction.user} has unclaimed this ticket.`, ephemeral: false });
              if (dashboardConfig.bot.ticketLogChannelId) {
                await interaction.client.rest.post(`/channels/${dashboardConfig.bot.ticketLogChannelId}/messages`, {
                  body: {
                    embeds: [{
                      title: 'Ticket Unclaimed',
                      color: 0xffc107,
                      fields: [
                        { name: 'Ticket', value: ticket._id.toString(), inline: true },
                        { name: 'Staff', value: `<@${interaction.user.id}>`, inline: true },
                      ],
                      timestamp: new Date().toISOString(),
                    }]
                  }
                }).catch(()=>{});
              }
            }
            break;
          }
          case 'close': {
            // Defer reply immediately to prevent timeout during long operations
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });

            if (!isCreator && !isStaff) {
              return interaction.editReply({ content: 'You are not allowed to close this ticket.' });
            }
            if (ticket.status === 'closed') {
              return interaction.editReply({ content: 'Ticket already closed.' });
            }
            // lock channel
            try {
              await (interaction.channel as any).permissionOverwrites.edit(ticket.creatorId, { SendMessages: false });
              await (interaction.channel as any).setName(`closed-${interaction.channel?.id.substring(0,4)}`);
            } catch (err) {
              logger.warn('Failed to update channel perms', err);
            }
            await ticketsCol.updateOne({ _id: ticket._id }, { $set: { status: 'closed', closedAt: new Date() } });

            // Generate transcript HTML with proper styling
            const fetchMessages = async () => {
              const messages: any[] = [];
              let before;
              while (true) {
                const batch: any = await (interaction.channel as any).messages.fetch({ limit: 100, before });
                messages.push(...batch.toJSON());
                if (batch.size < 100) break;
                before = batch.last()?.id;
              }
              return messages.sort((a,b)=>a.createdTimestamp-b.createdTimestamp);
            };

            const msgs = await fetchMessages();

            // Build participants list with more accurate roles
            const participants = await Promise.all(
              [...new Set(msgs.map(m => m.author.id))].map(async id => {
                const member = await interaction.guild?.members.fetch(id).catch(() => null);
                const username = member?.user?.username || msgs.find(m => m.author.id === id)?.author.username || 'Unknown User';
                const avatar = member?.user?.avatar
                  ? `https://cdn.discordapp.com/avatars/${id}/${member.user.avatar}.png?size=32`
                  : `https://cdn.discordapp.com/embed/avatars/${parseInt(member?.user?.discriminator || '0') % 5}.png`;

                const isStaffMember = member
                  ? (member.permissions as PermissionsBitField).has(PermissionsBitField.Flags.ManageGuild) ||
                    (dashboardConfig.dashboard.adminRoleIds || []).some((rid: string) => member.roles.cache.has(rid))
                  : false;

                const role = id === ticket.creatorId ? 'Creator' : isStaffMember ? 'Staff' : 'User';

                return { id, username, avatar, role };
              })
            );

            // Generate the participants HTML
            const participantsHtml = participants.map(p => `
              <div class="participant">
                <img src="${p.avatar}" alt="${p.username}'s avatar"/>
                <div>
                  <div class="participant-name">${p.username}</div>
                  <div class="participant-role">${p.role}</div>
                </div>
              </div>
            `).join('');

            // Helper functions for base64 conversion
            const fetchToDataUri = async (url: string, mimeGuess: string): Promise<string> => {
              try {
                const res = await fetch(url);
                const buf = await res.arrayBuffer();
                if (buf.byteLength > 1024*1024) return url; // too big
                const base64 = Buffer.from(buf).toString('base64');
                return `data:${mimeGuess};base64,${base64}`;
              } catch { return url; }
            };

            const replaceAsync = async (str: string, regex: RegExp, asyncFn: (...args:any[])=>Promise<string>): Promise<string> => {
              const matches = Array.from(str.matchAll(regex));
              if (matches.length === 0) return str;
              
              const results = await Promise.all(matches.map(match => asyncFn(...match)));
              
              let result = str;
              for (let i = matches.length - 1; i >= 0; i--) {
                const match = matches[i];
                const replacement = results[i];
                result = result.substring(0, match.index!) + replacement + result.substring(match.index! + match[0].length);
              }
              return result;
            };

            const attachmentToImg = async (att: any) => {
              if (!att.contentType?.startsWith('image/')) return '';

              // Always keep GIF/large images remote to preserve animation/avoid bloat
              if (att.contentType === 'image/gif' || att.size > 1024 * 1024) {
                return `<img class="attach" src="${att.url}" />`;
              }

              // Inline small static images
              try {
                const buf = await (await fetch(att.url)).arrayBuffer();
                const base64 = Buffer.from(buf).toString('base64');
                return `<img class="attach" src="data:${att.contentType};base64,${base64}" />`;
              } catch {
                return `<img class="attach" src="${att.url}" />`;
              }
            };

            let styleContent = `
/* Reset and base styles */
*{box-sizing:border-box;margin:0;padding:0}
:root {
  --bg-primary: #0f172a;
  --bg-secondary: rgba(255,255,255,0.08);
  --text-primary: #e2e8f0;
  --text-secondary: #94a3b8;
  --accent-blue: #3b82f6;
  --accent-purple: #a855f7;
  --accent-red: #ef4444;
}

/* Main layout */
body{
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: var(--bg-primary);
  color: var(--text-primary);
  min-height: 100vh;
  display: grid;
  grid-template-columns: 280px 1fr;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(29,78,216,.15), transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(60,46,123,.15), transparent 50%);
}

/* Sidebar */
.sidebar {
  background: var(--bg-secondary);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(255,255,255,0.1);
  padding: 24px;
  position: sticky;
  top: 0;
  height: 100vh;
  overflow-y: auto;
}

/* Main content */
.main {
  padding: 40px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

/* Messages */
.messages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.msg {
  display: flex;
  gap: 16px;
  animation: fadeIn 0.3s ease;
  padding: 4px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.msg:hover {
  background: rgba(255,255,255,0.03);
}

.avatar {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.msg:hover .avatar {
  transform: scale(1.05);
}

.bubble {
  background: rgba(255,255,255,0.04);
  padding: 16px;
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.06);
  max-width: calc(100% - 58px);
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.msg:hover .bubble {
  border-color: rgba(255,255,255,0.1);
  background: rgba(255,255,255,0.05);
}

.meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.name {
  font-size: 15px;
  font-weight: 500;
  color: #60a5fa;
}

.time {
  font-size: 12px;
  color: var(--text-secondary);
}

.content {
  font-size: 15px;
  line-height: 1.5;
  color: var(--text-primary);
  word-break: break-word;
}

/* Attachments */
.attach {
  display: block;
  margin-top: 12px;
  max-width: 400px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  transition: transform 0.2s ease;
}

.attach:hover {
  transform: scale(1.02);
}

.emoji {
  width: 32px;
  height: 32px;
  vertical-align: middle;
  margin: 0 2px;
  transition: transform 0.15s ease;
  cursor: pointer;
}

.emoji:hover {
  transform: scale(1.2);
}

/* Support details */
.support-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
  padding: 24px;
  background: var(--bg-secondary);
  border-radius: 16px;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255,255,255,0.1);
}

.detail-item {
  text-align: center;
  padding: 12px;
  background: rgba(255,255,255,0.02);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.detail-item:hover {
  background: rgba(255,255,255,0.04);
  transform: translateY(-2px);
}

.detail-label {
  font-size: 13px;
  color: var(--text-secondary);
  margin-bottom: 6px;
  font-weight: 500;
}

.detail-value {
  font-size: 15px;
  color: var(--text-primary);
  font-weight: 600;
}

/* Ticket embed */
.ticket-embed {
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(255,255,255,0.1);
  border-radius: 16px;
  padding: 24px;
  margin-top: 12px;
  transition: all 0.2s ease;
}

.ticket-embed:hover {
  background: rgba(255,255,255,0.04);
  border-color: rgba(255,255,255,0.15);
  transform: translateY(-2px);
}

.ticket-embed-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.ticket-embed-icon {
  font-size: 24px;
}

.ticket-embed-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--accent-blue);
  background: linear-gradient(135deg, #60a5fa 0%, #818cf8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Participants */
.participant {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.participant:hover {
  background: rgba(255,255,255,0.1);
  transform: translateX(4px);
}

.participant img {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.participant:hover img {
  transform: scale(1.1);
}

.participant-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.participant-role {
  font-size: 12px;
  color: var(--text-secondary);
}

.sidebar-section {
  margin-bottom: 32px;
}
`;

            const rows = await Promise.all(msgs.map(async m => {
              const time = new Date(m.createdTimestamp).toLocaleString();
              const avatar = m.author.avatar
                ? `https://cdn.discordapp.com/avatars/${m.author.id}/${m.author.avatar}.png?size=32`
                : `https://cdn.discordapp.com/embed/avatars/${parseInt(m.author.discriminator || '0') % 5}.png`;

              let content = m.content || '';

              // HTML escape the original content to prevent XSS
              content = content.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#39;');

              // Convert Tenor URLs in the content
              if (content.includes('tenor.com')) {
                const tenorUrlMatch = content.match(/https?:\/\/(?:www\.)?tenor\.com\/view\/[^"\s]+/);
                if (tenorUrlMatch) {
                  const idMatch = tenorUrlMatch[0].match(/-(\d+)(?:\?.*)?$/);
                  if (idMatch) {
                    const tenorId = idMatch[1];
                    try {
                      const apiResp = await fetch(`https://g.tenor.com/v1/gifs?ids=${tenorId}&key=LIVDSRZULELA`);
                      const apiJson: any = await apiResp.json();
                      if (apiJson?.results?.length) {
                        const media = apiJson.results[0].media_formats || apiJson.results[0].media?.[0];
                        const preferredOrder = ['gif','mediumgif','tinygif','nanogif'];
                        for (const key of preferredOrder) {
                          if (media?.[key]?.url) {
                            content = `<img class="attach" src="${media[key].url}" />`;
                            break;
                          }
                        }
                      }
                    } catch {
                      // Keep original URL if API fails
                      content = tenorUrlMatch[0];
                    }
                  }
                }
              }

              // Convert user mentions to usernames
              const mentionRegex = /&lt;@!?(\d+)&gt;/g;
              content = content.replace(mentionRegex, (match: string, id: string) => {
                const user = interaction.guild?.members.cache.get(id);
                return user ? `@${user.user.username}` : match;
              });

              // Handle embeds with images/GIFs (Discord often wraps media links in embeds)
              if (m !== msgs[0] && m.embeds?.length > 0) {
                for (const embed of m.embeds) {
                  // Skip embed handling since we already handled the URL in content
                  continue;
                }
              }

              return `<div class="msg">
                <img class="avatar" src="${avatar}"/>
                <div class="bubble">
                  <div class="meta">
                    <span class="name">${m.author.username}</span>
                    <span class="time">${time}</span>
                  </div>
                  <div class="content">${content}</div>
                </div>
              </div>`;
            }));

            const badgeColor = (category: string) => ({support:'#3b82f6', '18plus':'#ef4444', other:'#a855f7'}[category] || '#6b7280');

            const transcriptHtml = `<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${ticket._id.toString()} Transcript</title>
<style>${styleContent}</style></head>
<body>
  <div class="sidebar">
    <div class="sidebar-section">
      <h3>Participants</h3>
      ${participantsHtml}
    </div>
    <div class="sidebar-section">
      <h3>Support Info</h3>
      <div class="detail-item">
        <div class="detail-label">Status</div>
        <div class="detail-value">Closed</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Category</div>
        <div class="detail-value">${ticket.category || 'Support'}</div>
      </div>
    </div>
  </div>

  <div class="main">
    <div class="header" style="text-align: center;">
      <h1 style="color: ${badgeColor(ticket.category || 'support')};">Ticket Transcript</h1>
      <div class="category-label" style="color: ${badgeColor(ticket.category || 'support')}; margin-bottom: 20px; font-size: 1.1em;">${ticket.category || 'support'}</div>
    </div>

    <div class="support-details">
      <div class="detail-item">
        <div class="detail-label">Ticket ID</div>
        <div class="detail-value">${ticket._id.toString()}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Created</div>
        <div class="detail-value">${new Date(ticket.createdAt).toLocaleString()}</div>
      </div>
      <div class="detail-item">
        <div class="detail-label">Closed</div>
        <div class="detail-value">${new Date().toLocaleString()}</div>
      </div>
    </div>

    <div class="messages">
      ${rows.join('\n')}
    </div>
  </div>
</body></html>`;

            await ticketsCol.updateOne({ _id: ticket._id }, { $set: { transcriptHtml } });

            await interaction.message.edit({ components: [] });
            await interaction.editReply('Ticket closed. Channel will auto-delete in 15 s');

            // Send embed log
            if (dashboardConfig.bot.ticketLogChannelId) {
              await interaction.client.rest.post(`/channels/${dashboardConfig.bot.ticketLogChannelId}/messages`, {
                body: {
                  embeds: [{
                    title: 'Ticket Closed',
                    color: 0xffa500,
                    fields: [
                      { name: 'Ticket', value: ticket._id.toString(), inline: true },
                      { name: 'Closed by', value: `<@${interaction.user.id}>`, inline: true },
                    ],
                    timestamp: new Date().toISOString(),
                  }]
                }
              });
            }

            // Delete channel after 15s
            setTimeout(()=>{
              interaction.channel?.delete().catch(()=>{});
            },15000);
            break;
          }
          default:
            break;
        }
      }
    }
  ]
};

export default addon; 