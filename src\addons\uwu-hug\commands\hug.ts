import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

// Helper function to interpolate variables in strings
function interpolateVariables(text: string, context: any): string {
  return text.replace(/\{([^}]+)\}/g, (match, variable) => {
    try {
      const value = variable.split('.').reduce((obj: any, key: string) => obj?.[key], context);
      return value !== undefined ? String(value) : match;
    } catch {
      return match;
    }
  });
}

export const data = new SlashCommandBuilder()
  .setName('hug')
  .setDescription('uwu fuckl');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    // Create context with Discord data and API responses
    const context = {
      user: {
        id: interaction.user.id,
        username: interaction.user.username,
        displayName: interaction.user.displayName,
        tag: interaction.user.tag,
      },
      channel: {
        id: interaction.channel?.id,
        name: interaction.channel?.type === 0 ? interaction.channel.name : 'dm',
      },
      guild: {
        id: interaction.guild?.id,
        name: interaction.guild?.name,
      },
      // API response data will be added here by API request blocks
    };

    // API Request: GET https://nekos.best/api/v2/hug
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000);
      
      const responseResponse = await fetch(interpolateVariables('https://nekos.best/api/v2/hug', context), {
        method: 'GET',


        signal: controller.signal
      });
      
      clearTimeout(timeoutId);
      
      if (!responseResponse.ok) {
        throw new Error(`HTTP error! status: ${responseResponse.status}`);
      }
      
      const responseData = await responseResponse.json();
      console.log('API Response:', responseData);
      
      // Store response in context for use in other blocks
      (context as any).response = responseData;
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.error('API request timed out after 5000ms');
      } else {
        console.error('API request failed:', error);
      }
    }

    // Action: Send Embed
    try {
      const response = (context as any).response;
      
      if (response && response.results && response.results.length > 0) {
        const item = response.results[0];
        
        const embed = new EmbedBuilder()
          .setTitle('🤗 Hug!')
          .setDescription(`${interaction.user.username} sends a warm hug! 💕`)
          .setColor('#FF69B4');
        
        // Add image if available
        if (item.url) {
          embed.setImage(item.url);
        }
        
        // Add footer with source info
        if (item.anime_name) {
          embed.setFooter({ text: `From: ${item.anime_name}` });
        }
        
        await interaction.reply({ embeds: [embed] });
      } else {
        // Fallback if no API data available
        const fallbackMessage = interpolateVariables('uwu', context);
        
        const embed = new EmbedBuilder()
          .setTitle('Message')
          .setDescription(fallbackMessage)
          .setColor(0x00ff00);
        
        await interaction.reply({ embeds: [embed] });
      }
    } catch (error) {
      console.error('Error creating embed:', error);
      await interaction.reply({ content: '🤗 *sends a virtual hug* 💕', ephemeral: true });
    }
  } catch (error) {
    console.error('Error executing hug command:', error);
    await interaction.reply({ content: 'An error occurred while executing this command.', ephemeral: true });
  }
}

export const cooldown = 0;