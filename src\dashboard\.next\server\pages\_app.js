(()=>{var e={};e.id=636,e.ids=[636],e.modules={361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1292:(e,r,t)=>{"use strict";e.exports=t(361)},2015:e=>{"use strict";e.exports=require("react")},2403:(e,r)=>{"use strict";r._=function(e){return e&&e.__esModule?e:{default:e}}},2742:(e,r)=>{"use strict";function t(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap,a=new WeakMap;return(t=function(e){return e?a:r})(e)}r._=function(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var a=t(r);if(a&&a.has(e))return a.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var c=o?Object.getOwnPropertyDescriptor(e,s):null;c&&(c.get||c.set)?Object.defineProperty(n,s,c):n[s]=e[s]}return n.default=e,a&&a.set(e,n),n}},3001:(e,r,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(r,{DP:()=>f,NP:()=>u,nk:()=>d});var n=t(8732),o=t(2015),s=t(9733),c=t(6390),i=e([s,c]);[s,c]=i.then?(await i)():i;let d=[{id:"default",name:"Default Purple",description:"Classic purple and blue gradient theme",colors:{primary:"#8b5cf6",primaryLight:"#a78bfa",primaryDark:"#7c3aed",secondary:"#5865F2",accent:"#06b6d4",background:"#1a202c",surface:"rgba(255,255,255,0.03)",text:"#f7fafc",textSecondary:"#a0aec0",border:"rgba(255,255,255,0.2)",success:"#68d391",warning:"#fbb6ce",error:"#fc8181",info:"#63b3ed"}},{id:"ocean",name:"Ocean Blue",description:"Deep blue ocean-inspired theme",colors:{primary:"#0ea5e9",primaryLight:"#38bdf8",primaryDark:"#0284c7",secondary:"#06b6d4",accent:"#8b5cf6",background:"#0f172a",surface:"rgba(59, 130, 246, 0.05)",text:"#f1f5f9",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}},{id:"forest",name:"Forest Green",description:"Nature-inspired green theme",colors:{primary:"#059669",primaryLight:"#10b981",primaryDark:"#047857",secondary:"#065f46",accent:"#8b5cf6",background:"#0f1419",surface:"rgba(16, 185, 129, 0.05)",text:"#f0fdf4",textSecondary:"#86efac",border:"rgba(16, 185, 129, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"sunset",name:"Sunset Orange",description:"Warm sunset-inspired theme",colors:{primary:"#ea580c",primaryLight:"#fb923c",primaryDark:"#c2410c",secondary:"#dc2626",accent:"#8b5cf6",background:"#1c1917",surface:"rgba(251, 146, 60, 0.05)",text:"#fef7ed",textSecondary:"#fdba74",border:"rgba(251, 146, 60, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"rose",name:"Rose Pink",description:"Elegant rose and pink theme",colors:{primary:"#e11d48",primaryLight:"#f43f5e",primaryDark:"#be123c",secondary:"#ec4899",accent:"#8b5cf6",background:"#1f1720",surface:"rgba(244, 63, 94, 0.05)",text:"#fdf2f8",textSecondary:"#fda4af",border:"rgba(244, 63, 94, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"midnight",name:"Midnight Blue",description:"Dark midnight blue theme",colors:{primary:"#1e40af",primaryLight:"#3b82f6",primaryDark:"#1e3a8a",secondary:"#4338ca",accent:"#06b6d4",background:"#0c0a1f",surface:"rgba(59, 130, 246, 0.05)",text:"#f8fafc",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}}],l=(0,o.createContext)(void 0),f=()=>{let e=(0,o.useContext)(l);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},u=({children:e})=>{let[r,t]=(0,o.useState)(d[0]),[a,i]=(0,o.useState)([]);(0,o.useEffect)(()=>{let e=localStorage.getItem("dashboard-color-scheme"),r=localStorage.getItem("dashboard-custom-schemes");if(r)try{let e=JSON.parse(r);i(e)}catch(e){}if(e){let r=d.find(r=>r.id===e);if(r)t(r);else{let r=localStorage.getItem("dashboard-custom-schemes");if(r)try{let a=JSON.parse(r).find(r=>r.id===e);a&&t(a)}catch(e){}}}},[]),(0,o.useEffect)(()=>{localStorage.setItem("dashboard-color-scheme",r.id)},[r]),(0,o.useEffect)(()=>{localStorage.setItem("dashboard-custom-schemes",JSON.stringify(a))},[a]);let f=[...d,...a],u=(0,s.extendTheme)({...c.A,colors:{...c.A.colors,brand:{50:r.colors.primaryLight+"20",100:r.colors.primaryLight+"40",200:r.colors.primaryLight+"60",300:r.colors.primaryLight+"80",400:r.colors.primaryLight,500:r.colors.primary,600:r.colors.primaryDark,700:r.colors.primaryDark+"CC",800:r.colors.primaryDark+"AA",900:r.colors.primaryDark+"88"},custom:{primary:r.colors.primary,primaryLight:r.colors.primaryLight,primaryDark:r.colors.primaryDark,secondary:r.colors.secondary,accent:r.colors.accent,background:r.colors.background,surface:r.colors.surface,text:r.colors.text,textSecondary:r.colors.textSecondary,border:r.colors.border,success:r.colors.success,warning:r.colors.warning,error:r.colors.error,info:r.colors.info}},styles:{global:{body:{bg:r.colors.background,color:r.colors.text}}}});return(0,n.jsx)(l.Provider,{value:{currentScheme:r,setColorScheme:e=>{let r=d.find(r=>r.id===e);if(r)return void t(r);let n=a.find(r=>r.id===e);n&&t(n)},colorSchemes:f,customSchemes:a,addCustomScheme:e=>{i(r=>[...r.filter(r=>r.id!==e.id),e]),t(e)},deleteCustomScheme:e=>{i(r=>r.filter(r=>r.id!==e)),r.id===e&&t(d[0])},resetToDefault:()=>{t(d[0])}},children:(0,n.jsx)(s.ChakraProvider,{theme:u,children:e})})};a()}catch(e){a(e)}})},3118:(e,r,t)=>{"use strict";e.exports=t(1292).vendored.contexts.AmpContext},4722:e=>{"use strict";e.exports=require("next-auth/react")},4959:(e,r,t)=>{e.exports=t(8193)},5100:(e,r,t)=>{"use strict";e.exports=t(1292).vendored.contexts.HeadManagerContext},5416:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return s}});let a=t(2015),n=()=>{},o=()=>{};function s(e){var r;let{headManager:t,reduceComponentsToState:s}=e;function c(){if(t&&t.mountedInstances){let r=a.Children.toArray(Array.from(t.mountedInstances).filter(Boolean));t.updateHead(s(r,e))}}return null==t||null==(r=t.mountedInstances)||r.add(e.children),c(),n(()=>{var r;return null==t||null==(r=t.mountedInstances)||r.add(e.children),()=>{var r;null==t||null==(r=t.mountedInstances)||r.delete(e.children)}}),n(()=>(t&&(t._pendingUpdate=c),()=>{t&&(t._pendingUpdate=c)})),o(()=>(t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null),()=>{t&&t._pendingUpdate&&(t._pendingUpdate(),t._pendingUpdate=null)})),null}},5817:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return t}});let t=e=>{}},6390:(e,r,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(r,{A:()=>s});var n=t(9733),o=e([n]);n=(o.then?(await o)():o)[0];let s=(0,n.extendTheme)({config:{initialColorMode:"dark",useSystemColorMode:!1},fonts:{heading:"'Inter', sans-serif",body:"'Inter', sans-serif"},colors:{brand:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},discord:{50:"#e8e9fd",100:"#d1d3fc",200:"#b9bcfa",300:"#a2a5f9",400:"#8b8ef7",500:"#5865F2",600:"#4752c4",700:"#363f97",800:"#242c69",900:"#12193c"}},styles:{global:{body:{bg:"gray.900",color:"gray.100"}}},components:{Button:{defaultProps:{colorScheme:"brand"},variants:{solid:e=>({bg:`${e.colorScheme}.500`,color:"white",_hover:{bg:`${e.colorScheme}.600`,transform:"translateY(-2px)",boxShadow:"lg"},_active:{bg:`${e.colorScheme}.700`,transform:"translateY(0)"},transition:"all 0.2s ease"})}},Link:{baseStyle:{_hover:{textDecoration:"none"}}},Box:{baseStyle:{transition:"all 0.2s ease"}}}});a()}catch(e){a(e)}})},6665:(e,r)=>{"use strict";function t(e){let{ampFirst:r=!1,hybrid:t=!1,hasQuery:a=!1}=void 0===e?{}:e;return r||t&&a}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"isInAmpMode",{enumerable:!0,get:function(){return t}})},8077:(e,r,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(r),t.d(r,{default:()=>f});var n=t(8732),o=t(4722),s=t(4959),c=t.n(s),i=t(3001),d=e([i]);function l({Component:e,pageProps:r}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(c(),{children:[(0,n.jsx)("title",{children:"404 Bot Dashboard"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,n.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,n.jsx)(e,{...r})]})}function f({Component:e,pageProps:{session:r,...t}}){return(0,n.jsx)(o.SessionProvider,{session:r,children:(0,n.jsx)(i.NP,{children:(0,n.jsx)(l,{Component:e,pageProps:t})})})}i=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},8193:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{default:function(){return b},defaultHead:function(){return f}});let a=t(2403),n=t(2742),o=t(8732),s=n._(t(2015)),c=a._(t(5416)),i=t(3118),d=t(5100),l=t(6665);function f(e){void 0===e&&(e=!1);let r=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||r.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),r}function u(e,r){return"string"==typeof r||"number"==typeof r?e:r.type===s.default.Fragment?e.concat(s.default.Children.toArray(r.props.children).reduce((e,r)=>"string"==typeof r||"number"==typeof r?e:e.concat(r),[])):e.concat(r)}t(5817);let p=["name","httpEquiv","charSet","itemProp"];function m(e,r){let{inAmpMode:t}=r;return e.reduce(u,[]).reverse().concat(f(t).reverse()).filter(function(){let e=new Set,r=new Set,t=new Set,a={};return n=>{let o=!0,s=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){s=!0;let r=n.key.slice(n.key.indexOf("$")+1);e.has(r)?o=!1:e.add(r)}switch(n.type){case"title":case"base":r.has(n.type)?o=!1:r.add(n.type);break;case"meta":for(let e=0,r=p.length;e<r;e++){let r=p[e];if(n.props.hasOwnProperty(r))if("charSet"===r)t.has(r)?o=!1:t.add(r);else{let e=n.props[r],t=a[r]||new Set;("name"!==r||!s)&&t.has(e)?o=!1:(t.add(e),a[r]=t)}}}return o}}()).reverse().map((e,r)=>{let a=e.key||r;if(process.env.__NEXT_OPTIMIZE_FONTS&&!t&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(r=>e.props.href.startsWith(r))){let r={...e.props||{}};return r["data-href"]=r.href,r.href=void 0,r["data-optimized-fonts"]=!0,s.default.cloneElement(e,r)}return s.default.cloneElement(e,{key:a})})}let b=function(e){let{children:r}=e,t=(0,s.useContext)(i.AmpStateContext),a=(0,s.useContext)(d.HeadManagerContext);return(0,o.jsx)(c.default,{reduceComponentsToState:m,headManager:a,inAmpMode:(0,l.isInAmpMode)(t),children:r})};("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")}};var r=require("../webpack-runtime.js");r.C(e);var t=r(r.s=8077);module.exports=t})();