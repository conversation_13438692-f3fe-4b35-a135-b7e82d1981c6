import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Icon,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  StatHelpText,
  StatArrow,
  LinkBox,
  SimpleGrid,
} from '@chakra-ui/react';
import NextLink from 'next/link';
import { AdminCardConfig } from '../config/adminCards';
import { useSession } from 'next-auth/react';
import { useTheme } from '../contexts/ThemeContext';

interface AdminCardProps {
  card: AdminCardConfig;
  userPermissions?: string[];
}

export const AdminCard: React.FC<AdminCardProps> = ({
  card,
  userPermissions = [],
}) => {
  const { data: session } = useSession();
  const { currentScheme } = useTheme();
  
  // Developer-only check - hardcoded for security
  const DEVELOPER_ID = '933023999770918932';
  const isDeveloper = session?.user?.id === DEVELOPER_ID;
  
  const hasPermission = !card.requiresPermission || 
    card.requiresPermission.some(permission => userPermissions.includes(permission));

  // Hide developer-only cards from non-developers
  if (card.developerOnly && !isDeveloper) {
    return null;
  }

  if (!hasPermission) return null;

  return (
    <LinkBox
      as={NextLink}
      href={card.href}
      p={6}
      bg={card.gradient ? `linear-gradient(135deg, ${card.gradient.from}, ${card.gradient.to})` : currentScheme.colors.surface}
      borderWidth={1}
      borderColor={currentScheme.colors.border}
      rounded="xl"
      boxShadow={`0 4px 20px ${currentScheme.colors.background}80`}
      backdropFilter="blur(10px)"
      _hover={{
        transform: 'translateY(-4px)',
        boxShadow: `0 6px 25px ${currentScheme.colors.primary}60`,
        borderColor: currentScheme.colors.primary,
      }}
      transition="all 0.3s"
      position="relative"
      overflow="hidden"
    >
      <VStack spacing={4} align="stretch">
        <HStack justify="space-between">
          <HStack spacing={3}>
            <Icon
              as={card.icon}
              boxSize={6}
              color={`${card.color}.300`}
            />
            <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
              {card.title}
            </Text>
          </HStack>
          {card.badge && (
            <Badge
              colorScheme={card.badge.color}
              px={2}
              py={1}
              borderRadius="full"
              fontSize="sm"
            >
              {card.badge.text}
            </Badge>
          )}
        </HStack>

        <Text color={currentScheme.colors.textSecondary} fontSize="sm" noOfLines={2}>
          {card.description}
        </Text>

        {card.stats && (
          <SimpleGrid columns={card.stats.length} spacing={4} pt={2}>
            {card.stats.map((stat, index) => (
              <Stat key={index}>
                <StatLabel color={currentScheme.colors.textSecondary} fontSize="xs">
                  {stat.label}
                </StatLabel>
                <StatNumber fontSize="xl" color={`${card.color}.300`}>
                  {stat.value}
                </StatNumber>
                {stat.change && (
                  <StatHelpText fontSize="xs">
                    <StatArrow
                      type={stat.change.isIncrease ? 'increase' : 'decrease'}
                    />
                    {stat.change.value}
                  </StatHelpText>
                )}
              </Stat>
            ))}
          </SimpleGrid>
        )}
      </VStack>

      {/* Background Pattern */}
      <Box
        position="absolute"
        top={0}
        right={0}
        bottom={0}
        left={0}
        bg={`repeating-linear-gradient(
          45deg,
          transparent,
          transparent 10px,
          ${card.gradient?.from || `var(--chakra-colors-${card.color}-500)`}10 10px,
          ${card.gradient?.from || `var(--chakra-colors-${card.color}-500)`}10 20px
        )`}
        opacity={0.05}
        pointerEvents="none"
      />
    </LinkBox>
  );
}; 