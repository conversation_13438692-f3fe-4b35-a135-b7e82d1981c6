import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from 'next-auth/react';
import { REST } from '@discordjs/rest';
import { Routes } from 'discord-api-types/v10';
import { env } from '../../../../core/config/index';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getSession({ req });
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Initialize Discord REST client
    const rest = new REST({ version: '10' }).setToken(env.DISCORD_BOT_TOKEN);
    const guildId = env.DISCORD_GUILD_ID;

    // Fetch guild members with their roles (limit 1000)
    const members = await rest.get(`${Routes.guildMembers(guildId)}?limit=1000`) as any[];
    const roles = await rest.get(Routes.guildRoles(guildId)) as any[];

    // Transform the data
    const formattedMembers = members.map(member => ({
      id: member.user.id,
      tag: `${member.user.username}${member.user.discriminator !== '0' ? `#${member.user.discriminator}` : ''}`,
      username: member.user.username,
      discriminator: member.user.discriminator,
      avatar: member.user.avatar 
        ? `https://cdn.discordapp.com/avatars/${member.user.id}/${member.user.avatar}.${member.user.avatar.startsWith('a_') ? 'gif' : 'png'}`
        : null,
      roles: member.roles.map((roleId: string) => {
        const role = roles.find(r => r.id === roleId);
        return role ? {
          id: role.id,
          name: role.name,
          color: role.color ? `#${role.color.toString(16).padStart(6, '0')}` : null,
          position: role.position
        } : null;
      }).filter(Boolean),
      joinedAt: member.joined_at,
      timeoutEnd: member.communication_disabled_until ? new Date(member.communication_disabled_until).getTime() : null,
      isTimedOut: member.communication_disabled_until && new Date(member.communication_disabled_until).getTime() > Date.now(),
      isAdmin: member.roles.some((roleId: string) => {
        const role = roles.find(r => r.id === roleId);
        return role && (BigInt(role.permissions) & BigInt(0x8)) === BigInt(0x8); // ADMINISTRATOR permission
      }),
      isModerator: member.roles.some((roleId: string) => {
        const role = roles.find(r => r.id === roleId);
        return role && (BigInt(role.permissions) & BigInt(0x2)) === BigInt(0x2); // KICK_MEMBERS permission
      })
    }));

    res.status(200).json(formattedMembers);
  } catch (error) {
    console.error('Error fetching users:', error);
    res.status(500).json({ error: 'Failed to fetch users', details: error instanceof Error ? error.message : 'Unknown error' });
  }
} 