import { <PERSON>lash<PERSON><PERSON>mand<PERSON><PERSON>er, ChatInputCommandInteraction, PermissionFlagsBits, ChannelType, TextChannel } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('slowmode')
  .setDescription('Set slowmode (rate limit) for a channel')
  .addIntegerOption(option =>
    option.setName('seconds')
      .setDescription('Slowmode duration in seconds (0-21600, 0 to disable)')
      .setRequired(true)
      .setMinValue(0)
      .setMaxValue(21600))
  .addChannelOption(option =>
    option.setName('channel')
      .setDescription('Channel to set slowmode for (defaults to current)')
      .addChannelTypes(ChannelType.GuildText)
      .setRequired(false))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for setting slowmode')
      .setRequired(false))
  .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const seconds = interaction.options.getInteger('seconds', true);
  const channelOption = interaction.options.getChannel('channel');
  const channel = (channelOption ?? interaction.channel) as TextChannel | null;
  const reason = interaction.options.getString('reason') ?? 'No reason provided';

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ManageChannels)) {
    await interaction.reply({ content: '❌ You do not have permission to manage channels.', ephemeral: true });
    return;
  }

  if (!channel || channel.type !== ChannelType.GuildText) {
    await interaction.reply({ content: '❌ Invalid channel specified.', ephemeral: true });
    return;
  }

  try {
    await channel.setRateLimitPerUser(seconds, reason);

    if (seconds === 0) {
      await interaction.reply({ content: `✅ Slowmode disabled for ${channel} | Reason: ${reason}` });
    } else {
      const timeString = seconds >= 60 
        ? `${Math.floor(seconds / 60)}m ${seconds % 60}s`
        : `${seconds}s`;
      
      await interaction.reply({ 
        content: `🐌 Slowmode set to **${timeString}** for ${channel} | Reason: ${reason}` 
      });
    }
  } catch (error) {
    console.error('Error setting slowmode:', error);
    await interaction.reply({ content: '❌ Failed to set slowmode. Please try again.', ephemeral: true });
  }
}

export const cooldown = 3000; 