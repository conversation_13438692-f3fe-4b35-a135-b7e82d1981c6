import React, { useState, memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Box,
  Text,
  VStack,
  HStack,
  Input,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Textarea,
  Switch,
  useDisclosure,
  IconButton,
  Badge,
  Select,
  Divider,
  SimpleGrid,
  Checkbox,
  CheckboxGroup,
  Stack,
  Alert,
  AlertIcon,
  InputGroup,
  InputLeftAddon,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Code,
  Collapse,
  Tooltip,
  AlertDescription,
} from '@chakra-ui/react';
import { FiSettings, FiZap, FiPlus, FiMinus, <PERSON><PERSON>ye, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ser, <PERSON>MessageSquare, FiHash, FiGlobe, FiClock, FiShuffle, FiTrash2 } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';

interface CommandOption {
  name: string;
  description: string;
  type: 'string' | 'integer' | 'boolean' | 'user' | 'channel' | 'role' | 'mentionable' | 'number' | 'attachment';
  required: boolean;
  choices?: Array<{
    name: string;
    value: string;
  }>;
}

interface CommandNodeData {
  label: string;
  commandName?: string;
  description?: string;
  options?: CommandOption[];
  permissions?: string[];
  cooldown?: number;
  guildOnly?: boolean;
  adminOnly?: boolean;
  allowDMs?: boolean;
  category?: string;
  examples?: string[];
  ephemeral?: boolean;
  deferReply?: boolean;
}

// Available variables for command context
const commandVariables = {
  command: [
    { name: '{command.name}', description: 'Command name that was executed', icon: '⚡' },
    { name: '{command.user}', description: 'User who executed the command', icon: '👤' },
    { name: '{command.channel}', description: 'Channel where command was executed', icon: '📺' },
    { name: '{command.server}', description: 'Server where command was executed', icon: '🏠' },
    { name: '{command.timestamp}', description: 'When the command was executed', icon: '⏰' },
  ],
  options: [
    { name: '{option.name}', description: 'Value of a specific option', icon: '🔧' },
    { name: '{option.user}', description: 'User option value', icon: '👤' },
    { name: '{option.channel}', description: 'Channel option value', icon: '📺' },
    { name: '{option.role}', description: 'Role option value', icon: '🎭' },
    { name: '{option.string}', description: 'String option value', icon: '💬' },
    { name: '{option.number}', description: 'Number option value', icon: '🔢' },
    { name: '{option.boolean}', description: 'Boolean option value', icon: '✅' },
  ],
  user: [
    { name: '{user.id}', description: 'User ID', icon: '🆔' },
    { name: '{user.username}', description: 'Username', icon: '👤' },
    { name: '{user.displayName}', description: 'Display Name', icon: '📝' },
    { name: '{user.tag}', description: 'User Tag (username#0000)', icon: '🏷️' },
    { name: '{user.mention}', description: 'User Mention (<@id>)', icon: '📢' },
    { name: '{user.avatar}', description: 'Avatar URL', icon: '🖼️' },
    { name: '{user.roles}', description: 'User Roles', icon: '🎭' },
    { name: '{user.permissions}', description: 'User Permissions', icon: '🔐' },
    { name: '{user.joinedAt}', description: 'Server Join Date', icon: '🚪' },
  ],
  channel: [
    { name: '{channel.id}', description: 'Channel ID', icon: '🆔' },
    { name: '{channel.name}', description: 'Channel Name', icon: '📺' },
    { name: '{channel.mention}', description: 'Channel Mention (<#id>)', icon: '📢' },
    { name: '{channel.type}', description: 'Channel Type', icon: '📋' },
    { name: '{channel.topic}', description: 'Channel Topic', icon: '💬' },
    { name: '{channel.memberCount}', description: 'Member Count', icon: '👥' },
  ],
  server: [
    { name: '{server.id}', description: 'Server ID', icon: '🆔' },
    { name: '{server.name}', description: 'Server Name', icon: '🏠' },
    { name: '{server.icon}', description: 'Server Icon URL', icon: '🖼️' },
    { name: '{server.memberCount}', description: 'Member Count', icon: '👥' },
    { name: '{server.owner}', description: 'Server Owner', icon: '👑' },
    { name: '{server.boostLevel}', description: 'Server Boost Level', icon: '🚀' },
  ],
};

const permissionsList = [
  'ADMINISTRATOR',
  'MANAGE_GUILD',
  'MANAGE_ROLES',
  'MANAGE_CHANNELS',
  'KICK_MEMBERS',
  'BAN_MEMBERS',
  'MANAGE_MESSAGES',
  'EMBED_LINKS',
  'ATTACH_FILES',
  'READ_MESSAGE_HISTORY',
  'MENTION_EVERYONE',
  'USE_EXTERNAL_EMOJIS',
  'CONNECT',
  'SPEAK',
  'MUTE_MEMBERS',
  'DEAFEN_MEMBERS',
  'MOVE_MEMBERS',
  'USE_VAD',
  'CHANGE_NICKNAME',
  'MANAGE_NICKNAMES',
  'MANAGE_WEBHOOKS',
  'MANAGE_EMOJIS',
  'MODERATE_MEMBERS',
  'VIEW_AUDIT_LOG',
  'MANAGE_EVENTS',
  'MANAGE_THREADS',
  'CREATE_PUBLIC_THREADS',
  'CREATE_PRIVATE_THREADS',
  'USE_EXTERNAL_STICKERS',
  'SEND_MESSAGES_IN_THREADS',
  'START_EMBEDDED_ACTIVITIES',
];

const optionTypes = [
  { value: 'string', label: '📝 String - Text input' },
  { value: 'integer', label: '🔢 Integer - Whole number' },
  { value: 'number', label: '🔢 Number - Decimal number' },
  { value: 'boolean', label: '✅ Boolean - True/False' },
  { value: 'user', label: '👤 User - Discord user' },
  { value: 'channel', label: '📺 Channel - Discord channel' },
  { value: 'role', label: '🎭 Role - Discord role' },
  { value: 'mentionable', label: '📢 Mentionable - User or role' },
  { value: 'attachment', label: '📎 Attachment - File upload' },
];

const CommandNode = memo(({ data, selected, id, updateNodeData: updateParentNodeData }: NodeProps<CommandNodeData> & { updateNodeData?: (nodeId: string, newData: any) => void }) => {
  const { currentScheme } = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [nodeData, setNodeData] = useState<CommandNodeData>(() => ({
    guildOnly: false,
    adminOnly: false,
    allowDMs: false,
    cooldown: 0,
    options: [],
    category: 'general',
    examples: [],
    permissions: [],
    ephemeral: false,
    deferReply: false,
    ...data
  }));
  const [showVariables, setShowVariables] = useState(false);

  const updateNodeData = (updates: Partial<CommandNodeData>) => {
    setNodeData(prev => ({ ...prev, ...updates }));
  };

  const handleModalClose = () => {
    // Update parent nodes array when modal closes
    if (updateParentNodeData && id) {
      updateParentNodeData(id, nodeData);
    }
    onClose();
  };

  const addOption = () => {
    const newOption: CommandOption = {
      name: '',
      description: '',
      type: 'string',
      required: false,
      choices: []
    };
    updateNodeData({
      options: [...(nodeData.options || []), newOption]
    });
  };

  const updateOption = (index: number, updates: Partial<CommandOption>) => {
    const newOptions = [...(nodeData.options || [])];
    newOptions[index] = { ...newOptions[index], ...updates };
    updateNodeData({ options: newOptions });
  };

  const removeOption = (index: number) => {
    const newOptions = (nodeData.options || []).filter((_, i) => i !== index);
    updateNodeData({ options: newOptions });
  };

  const addChoice = (optionIndex: number) => {
    const newOptions = [...(nodeData.options || [])];
    if (!newOptions[optionIndex].choices) {
      newOptions[optionIndex].choices = [];
    }
    newOptions[optionIndex].choices!.push({ name: '', value: '' });
    updateNodeData({ options: newOptions });
  };

  const updateChoice = (optionIndex: number, choiceIndex: number, field: 'name' | 'value', value: string) => {
    const newOptions = [...(nodeData.options || [])];
    if (newOptions[optionIndex].choices) {
      newOptions[optionIndex].choices![choiceIndex][field] = value;
      updateNodeData({ options: newOptions });
    }
  };

  const removeChoice = (optionIndex: number, choiceIndex: number) => {
    const newOptions = [...(nodeData.options || [])];
    if (newOptions[optionIndex].choices) {
      newOptions[optionIndex].choices = newOptions[optionIndex].choices!.filter((_, i) => i !== choiceIndex);
      updateNodeData({ options: newOptions });
    }
  };

  const copyVariable = (variable: string) => {
    navigator.clipboard.writeText(variable);
  };

  const renderVariablesList = () => (
    <Collapse in={showVariables} animateOpacity>
      <Box
        bg={currentScheme.colors.surface}
        border="1px solid"
        borderColor={currentScheme.colors.border}
        borderRadius="md"
        p={4}
        mt={3}
        maxH="400px"
        overflowY="auto"
      >
        <Accordion allowMultiple>
          {Object.entries(commandVariables).map(([category, variables]) => (
            <AccordionItem key={category} border="none">
              <AccordionButton px={0} py={2}>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} textTransform="capitalize">
                    {category} Variables
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px={0} py={2}>
                <VStack spacing={2} align="stretch">
                  {variables.map((variable) => (
                    <HStack
                      key={variable.name}
                      spacing={2}
                      p={2}
                      bg={currentScheme.colors.background}
                      borderRadius="md"
                      cursor="pointer"
                      _hover={{ bg: currentScheme.colors.surface }}
                      onClick={() => copyVariable(variable.name)}
                    >
                      <Text fontSize="sm">{variable.icon}</Text>
                      <Code fontSize="xs" colorScheme="blue">
                        {variable.name}
                      </Code>
                      <Text fontSize="xs" color={currentScheme.colors.textSecondary} flex="1">
                        {variable.description}
                      </Text>
                      <IconButton
                        icon={<FiCopy />}
                        size="xs"
                        variant="ghost"
                        aria-label="Copy variable"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyVariable(variable.name);
                        }}
                      />
                    </HStack>
                  ))}
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </Box>
    </Collapse>
  );

  return (
    <>
      <Box
        bg={currentScheme.colors.surface}
        border={`2px solid ${selected ? '#3b82f6' : currentScheme.colors.border}`}
        borderRadius="md"
        p={2}
        minW="140px"
        maxW="180px"
        boxShadow="sm"
        position="relative"
        _hover={{
          boxShadow: 'md',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <Handle
          type="target"
          position={Position.Top}
          style={{
            background: '#3b82f6',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            top: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
        
        <VStack spacing={1} align="stretch">
          <HStack justify="space-between" align="center">
            <HStack spacing={1}>
              <Box
                bg="blue.500"
                color="white"
                borderRadius="full"
                p={0.5}
                fontSize="xs"
              >
                <FiZap />
              </Box>
              <Text fontSize="xs" fontWeight="bold" color={currentScheme.colors.text}>
                Command
              </Text>
            </HStack>
            <IconButton
              icon={<FiSettings />}
              size="xs"
              variant="ghost"
              onClick={onOpen}
              aria-label="Configure command"
            />
          </HStack>
          
          <Box>
            <Text fontSize="xs" color={currentScheme.colors.text} noOfLines={1}>
              /{nodeData.commandName || 'unnamed'}
            </Text>
          </Box>
          
          {nodeData.description && (
            <Box>
              <Text fontSize="xs" color={currentScheme.colors.textSecondary} noOfLines={1}>
                {nodeData.description.length > 25 ? nodeData.description.substring(0, 25) + '...' : nodeData.description}
              </Text>
            </Box>
          )}
          
          <HStack spacing={1} flexWrap="wrap">
            {(nodeData.options?.length ?? 0) > 0 && (
              <Badge size="xs" colorScheme="blue">
                {nodeData.options?.length} option{(nodeData.options?.length ?? 0) !== 1 ? 's' : ''}
              </Badge>
            )}
            {nodeData.adminOnly && (
              <Badge size="xs" colorScheme="red">
                Admin
              </Badge>
            )}
            {nodeData.cooldown && nodeData.cooldown > 0 && (
              <Badge size="xs" colorScheme="orange">
                {nodeData.cooldown}s
              </Badge>
            )}
          </HStack>
        </VStack>
        
        <Handle
          type="source"
          position={Position.Bottom}
          style={{
            background: '#3b82f6',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            bottom: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
      </Box>

      {/* Enhanced Configuration Modal */}
      <Modal isOpen={isOpen} onClose={handleModalClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent bg={currentScheme.colors.background} border="2px solid" borderColor="blue.400" maxW="1200px">
          <ModalHeader color={currentScheme.colors.text}>
            ⚡ Configure Command
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={6} align="stretch">
              {/* Variables Helper */}
              <Box>
                <HStack justify="space-between" align="center" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    Available Variables
                </Text>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={showVariables ? <FiEyeOff /> : <FiEye />}
                    onClick={() => setShowVariables(!showVariables)}
                  >
                    {showVariables ? 'Hide' : 'Show'} Variables
                  </Button>
                </HStack>
                <Alert status="info" borderRadius="md" mb={2}>
                  <AlertIcon />
                  <AlertDescription fontSize="sm">
                    💡 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs.
                  </AlertDescription>
                </Alert>
                {renderVariablesList()}
              </Box>

              <Divider />

              {/* Tabbed Configuration */}
              <Tabs variant="enclosed" colorScheme="blue">
                <TabList>
                  <Tab>Basic Info</Tab>
                  <Tab>Options</Tab>
                  <Tab>Permissions</Tab>
                  <Tab>Advanced</Tab>
                </TabList>
                
                <TabPanels>
                  {/* Basic Information Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <SimpleGrid columns={2} spacing={4}>
                        <FormControl isRequired>
                    <FormLabel color={currentScheme.colors.text}>Command Name</FormLabel>
                    <InputGroup>
                            <InputLeftAddon bg={currentScheme.colors.surface} color={currentScheme.colors.text}>
                              /
                            </InputLeftAddon>
                      <Input
                        value={nodeData.commandName || ''}
                              onChange={(e) => updateNodeData({ commandName: e.target.value })}
                              placeholder="ping"
                        bg={currentScheme.colors.background}
                        color={currentScheme.colors.text}
                        borderColor={currentScheme.colors.border}
                      />
                    </InputGroup>
                  </FormControl>
                  
                  <FormControl>
                          <FormLabel color={currentScheme.colors.text}>Category</FormLabel>
                          <Select
                            value={nodeData.category || 'general'}
                            onChange={(e) => updateNodeData({ category: e.target.value })}
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                            borderColor={currentScheme.colors.border}
                          >
                            <option value="general">General</option>
                            <option value="moderation">Moderation</option>
                            <option value="fun">Fun</option>
                            <option value="utility">Utility</option>
                            <option value="admin">Admin</option>
                            <option value="info">Info</option>
                            <option value="music">Music</option>
                            <option value="games">Games</option>
                          </Select>
                        </FormControl>
                      </SimpleGrid>
                      
                      <FormControl isRequired>
                    <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                    <Textarea
                      value={nodeData.description || ''}
                      onChange={(e) => updateNodeData({ description: e.target.value })}
                      placeholder="What does this command do?"
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                          minH="80px"
                    />
                  </FormControl>
                  
                  <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Usage Examples</FormLabel>
                        <Textarea
                          value={nodeData.examples?.join('\n') || ''}
                          onChange={(e) => updateNodeData({ examples: e.target.value.split('\n').filter(ex => ex.trim()) })}
                          placeholder={`/ping\n/ping server\n/ping {user.mention}`}
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                          minH="80px"
                        />
                        <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                          One example per line
                        </Text>
                  </FormControl>
                </VStack>
                  </TabPanel>
                  
                  {/* Options Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                          Command Options
                        </Text>
                        <Button
                          leftIcon={<FiPlus />}
                          onClick={addOption}
                          colorScheme="blue"
                          size="sm"
                        >
                          Add Option
                        </Button>
                      </HStack>
                      
                      <Alert status="info" borderRadius="md">
                        <AlertIcon />
                        <AlertDescription fontSize="sm">
                          Options are parameters users can provide with your command. They appear as autocomplete fields in Discord.
                        </AlertDescription>
                      </Alert>
                      
                      <VStack spacing={4} align="stretch">
                        {nodeData.options?.map((option, index) => (
                          <Box
                            key={index}
                            p={4}
                            bg={currentScheme.colors.surface}
                            borderRadius="md"
                            border="1px solid"
                            borderColor={currentScheme.colors.border}
                          >
                            <HStack justify="space-between" align="center" mb={3}>
                              <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text}>
                                Option {index + 1}
                </Text>
                        <IconButton
                                icon={<FiTrash2 />}
                                size="sm"
                          colorScheme="red"
                                variant="ghost"
                                onClick={() => removeOption(index)}
                          aria-label="Remove option"
                        />
                      </HStack>
                            
                            <VStack spacing={3} align="stretch">
                              <SimpleGrid columns={2} spacing={3}>
                                <FormControl isRequired>
                                  <FormLabel fontSize="sm" color={currentScheme.colors.text}>Option Name</FormLabel>
                                  <Input
                                    value={option.name}
                                    onChange={(e) => updateOption(index, { name: e.target.value })}
                                    placeholder="user"
                                    bg={currentScheme.colors.background}
                                    color={currentScheme.colors.text}
                                    borderColor={currentScheme.colors.border}
                                    size="sm"
                                  />
                                </FormControl>
                                
                                <FormControl isRequired>
                                  <FormLabel fontSize="sm" color={currentScheme.colors.text}>Option Type</FormLabel>
                          <Select
                            value={option.type}
                                    onChange={(e) => updateOption(index, { type: e.target.value as any })}
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                                    borderColor={currentScheme.colors.border}
                                    size="sm"
                                  >
                                    {optionTypes.map((type) => (
                                      <option key={type.value} value={type.value}>
                                        {type.label}
                                      </option>
                                    ))}
                          </Select>
                        </FormControl>
                              </SimpleGrid>
                              
                        <FormControl>
                        <FormLabel fontSize="sm" color={currentScheme.colors.text}>Description</FormLabel>
                        <Input
                          value={option.description}
                                  onChange={(e) => updateOption(index, { description: e.target.value })}
                                  placeholder="The user to ping"
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                                  size="sm"
                        />
                      </FormControl>
                              
                              <HStack>
                                <Switch
                                  isChecked={option.required}
                                  onChange={(e) => updateOption(index, { required: e.target.checked })}
                                  colorScheme="blue"
                                />
                                <Text fontSize="sm" color={currentScheme.colors.text}>
                                  Required option
                                </Text>
                              </HStack>
                              
                              {(option.type === 'string' || option.type === 'integer' || option.type === 'number') && (
                                <Box>
                                  <HStack justify="space-between" align="center" mb={2}>
                                    <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                                      Predefined Choices (Optional)
                                    </Text>
                  <Button
                                      size="xs"
                    leftIcon={<FiPlus />}
                                      onClick={() => addChoice(index)}
                    colorScheme="blue"
                                      variant="ghost"
                  >
                                      Add Choice
                  </Button>
                                  </HStack>
                                  
                                  <VStack spacing={2} align="stretch">
                                    {option.choices?.map((choice, choiceIndex) => (
                                      <HStack key={choiceIndex} spacing={2}>
                                        <Input
                                          value={choice.name}
                                          onChange={(e) => updateChoice(index, choiceIndex, 'name', e.target.value)}
                                          placeholder="Choice name"
                                          bg={currentScheme.colors.background}
                                          color={currentScheme.colors.text}
                                          borderColor={currentScheme.colors.border}
                                          size="sm"
                                        />
                                        <Input
                                          value={choice.value}
                                          onChange={(e) => updateChoice(index, choiceIndex, 'value', e.target.value)}
                                          placeholder="Choice value"
                                          bg={currentScheme.colors.background}
                                          color={currentScheme.colors.text}
                                          borderColor={currentScheme.colors.border}
                                          size="sm"
                                        />
                                        <IconButton
                                          icon={<FiMinus />}
                                          size="sm"
                                          colorScheme="red"
                                          variant="ghost"
                                          onClick={() => removeChoice(index, choiceIndex)}
                                          aria-label="Remove choice"
                                        />
                                      </HStack>
                                    ))}
                                  </VStack>
                                </Box>
                              )}
                </VStack>
              </Box>
                        ))}
                        
                        {(!nodeData.options || nodeData.options.length === 0) && (
                          <Alert status="info" borderRadius="md">
                            <AlertIcon />
                            <AlertDescription>
                              No options configured. Your command will work without any parameters.
                            </AlertDescription>
                          </Alert>
                        )}
                      </VStack>
                    </VStack>
                  </TabPanel>
                  
                  {/* Permissions Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                        Command Permissions
                </Text>
                      
                      <Alert status="warning" borderRadius="md">
                        <AlertIcon />
                        <AlertDescription fontSize="sm">
                          Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command.
                        </AlertDescription>
                      </Alert>
                      
                      <VStack spacing={4} align="stretch">
                        <HStack spacing={4}>
                      <Switch
                            isChecked={nodeData.adminOnly}
                            onChange={(e) => updateNodeData({ adminOnly: e.target.checked })}
                            colorScheme="red"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Admin Only
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Only server administrators can use this command
                            </Text>
                          </VStack>
                        </HStack>
                        
                        <HStack spacing={4}>
                      <Switch
                            isChecked={nodeData.guildOnly}
                            onChange={(e) => updateNodeData({ guildOnly: e.target.checked })}
                            colorScheme="blue"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Server Only
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Command can only be used in servers, not DMs
                            </Text>
                          </VStack>
                        </HStack>
                        
                        <HStack spacing={4}>
                      <Switch
                            isChecked={nodeData.allowDMs}
                        onChange={(e) => updateNodeData({ allowDMs: e.target.checked })}
                            colorScheme="green"
                      />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Allow DMs
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Command can be used in direct messages
                            </Text>
                          </VStack>
                        </HStack>
                      </VStack>
                      
                      <Divider />
                      
                      <Box>
                        <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mb={3}>
                          Required Permissions
                        </Text>
                        <Text fontSize="sm" color={currentScheme.colors.textSecondary} mb={3}>
                          Select the Discord permissions users need to use this command
                        </Text>
                        <CheckboxGroup
                          value={nodeData.permissions || []}
                          onChange={(value) => updateNodeData({ permissions: value as string[] })}
                        >
                          <SimpleGrid columns={3} spacing={2}>
                            {permissionsList.map((permission) => (
                              <Checkbox
                                key={permission}
                                value={permission}
                                colorScheme="blue"
                                size="sm"
                              >
                                <Text fontSize="xs" color={currentScheme.colors.text}>
                                  {permission.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                                </Text>
                              </Checkbox>
                            ))}
                          </SimpleGrid>
                        </CheckboxGroup>
                      </Box>
                    </VStack>
                  </TabPanel>
                  
                  {/* Advanced Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                        Advanced Settings
                      </Text>
                    
                    <FormControl>
                      <FormLabel color={currentScheme.colors.text}>Cooldown (seconds)</FormLabel>
                        <NumberInput
                        value={nodeData.cooldown || 0}
                          onChange={(valueString) => updateNodeData({ cooldown: parseInt(valueString) || 0 })}
                          min={0}
                          max={3600}
                        >
                          <NumberInputField
                        bg={currentScheme.colors.background}
                        color={currentScheme.colors.text}
                        borderColor={currentScheme.colors.border}
                      />
                          <NumberInputStepper>
                            <NumberIncrementStepper />
                            <NumberDecrementStepper />
                          </NumberInputStepper>
                        </NumberInput>
                        <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                          How long users must wait between uses (0 = no cooldown)
                        </Text>
                    </FormControl>
                      
                      <VStack spacing={4} align="stretch">
                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.ephemeral}
                            onChange={(e) => updateNodeData({ ephemeral: e.target.checked })}
                            colorScheme="blue"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Ephemeral Response
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Command response is only visible to the user who ran it
                            </Text>
                </VStack>
                        </HStack>

                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.deferReply}
                            onChange={(e) => updateNodeData({ deferReply: e.target.checked })}
                            colorScheme="orange"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Defer Reply
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Show "thinking..." message while processing (for slow commands)
                </Text>
                          </VStack>
                        </HStack>
                      </VStack>
                    </VStack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
              
              <Button
                colorScheme="blue"
                                  onClick={() => {
                    // Save the configuration
                    data.commandName = nodeData.commandName;
                    data.description = nodeData.description;
                  data.options = nodeData.options;
                  data.permissions = nodeData.permissions;
                    data.cooldown = nodeData.cooldown;
                    data.guildOnly = nodeData.guildOnly;
                    data.adminOnly = nodeData.adminOnly;
                    data.allowDMs = nodeData.allowDMs;
                    data.category = nodeData.category;
                    data.examples = nodeData.examples;
                  data.ephemeral = nodeData.ephemeral;
                  data.deferReply = nodeData.deferReply;
                    data.label = nodeData.commandName ? `/${nodeData.commandName}` : 'Command';
                    onClose();
                  }}
                size="lg"
                width="full"
              >
                Save Configuration
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
});

CommandNode.displayName = 'CommandNode';

export default CommandNode; 