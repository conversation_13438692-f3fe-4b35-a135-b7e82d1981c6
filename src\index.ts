import { Client, GatewayIntentBits, ActivityType, EmbedBuilder, TextChannel, GuildMember, PartialGuildMember, AuditLogEvent, PermissionsBitField } from 'discord.js';
import { spawn } from 'child_process';
import { Logger } from './core/Logger.js';
import { ConfigManager } from './core/ConfigManager.js';
import { DatabaseManager } from './core/DatabaseManager.js';
import { AddonManager } from './core/AddonManager.js';
import { CommandHandler } from './core/CommandHandler.js';
import { BotInstance } from './types/index.js';
import os from 'os';

process.env.NODE_ENV = process.env.NODE_ENV || 'production';

// Helper function to replace placeholders
const replacePlaceholders = (text: string, member: GuildMember | PartialGuildMember): string => {
  if (!text) return '';
  const user = member.user;
  const guild = member.guild;
  const memberCount = guild.memberCount;
  const now = new Date();

  return text
    .replace(/\{user\}/g, user.toString())
    .replace(/\{username\}/g, user.username)
    .replace(/\{userTag\}/g, user.tag)
    .replace(/\{userId\}/g, user.id)
    .replace(/\{guild\}/g, guild.name)
    .replace(/\{memberCount\}/g, memberCount.toString());
};

class Bot {
  private client: Client;
  private config: any;
  private logger: any;
  private database: DatabaseManager;
  private addonManager: AddonManager;
  private commandHandler: CommandHandler;
  private botInstance: BotInstance;
  private databaseConnected = false;
  private dashboardUrl: string;
  private dashboardProcess: any = null;
  private activityRotationHandle: NodeJS.Timeout | null = null;
  private currentActivityIndex: number = 0;

  constructor() {
    // Initialize Discord client
    this.client = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildModeration,
        GatewayIntentBits.GuildEmojisAndStickers,
        GatewayIntentBits.GuildIntegrations,
        GatewayIntentBits.GuildWebhooks,
        GatewayIntentBits.GuildInvites,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildPresences,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMessageTyping,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.DirectMessageReactions,
        GatewayIntentBits.DirectMessageTyping,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildScheduledEvents
      ]
    });

    // Load configuration
    this.config = ConfigManager.getConfig();
    this.logger = Logger.createLogger();

    // Initialize dashboard URL with proper environment variable support
    // Priority: explicit env var -> YAML url -> localhost with server port
    const envDashboardUrl = process.env.NEXTAUTH_URL || process.env.DASHBOARD_URL || null;
    const serverPort = process.env.SERVER_PORT || '3000';
    
    if (envDashboardUrl) {
      this.dashboardUrl = envDashboardUrl;
    } else if (this.config.dashboard?.url) {
      this.dashboardUrl = this.config.dashboard.url;
    } else {
      // Try to determine appropriate host based on environment
      const localIp = this.getLocalExternalIp();
      if (localIp && localIp.startsWith('192.168.')) {
        // Local network environment
        this.dashboardUrl = `http://${localIp}:${serverPort}`;
      } else {
        // Fallback to localhost
        this.dashboardUrl = `http://localhost:${serverPort}`;
      }
    }

    // Initialize managers
    this.database = new DatabaseManager(this.config);

    // Create bot instance
    this.botInstance = {
      client: this.client,
      config: this.config,
      logger: this.logger,
      database: this.database,
      addons: new Map(),
      commands: new Map(),
      contextMenus: new Map(),
      cooldowns: new Map()
    };

    // Initialize managers that need botInstance
    this.addonManager = new AddonManager(this.botInstance);
    this.commandHandler = new CommandHandler(this.botInstance);
  }

  public getClient(): Client {
    return this.client;
  }

  public getConfig(): any {
    return this.config;
  }

  public getLogger(): any {
    return this.logger;
  }

  public getDatabase(): DatabaseManager {
    return this.database;
  }

  private getLocalExternalIp(): string | null {
    const nets = os.networkInterfaces();
    for (const name of Object.keys(nets)) {
      const netArr = nets[name];
      if (!netArr) continue;
      for (const net of netArr) {
        if (net.family === 'IPv4' && !net.internal) {
          return net.address;
        }
      }
    }
    return null;
  }

  private async initializeDashboard() {
    try {
      // Use dev mode only if explicitly requested.
      const dev = process.env.DASHBOARD_DEV === '1' || process.env.DASHBOARD_DEV === 'true';

      // If running in production, ensure the dashboard is built once
      if (!dev) {
        const fs = await import('fs');
        const { execSync } = await import('child_process');
        // Check if a production build exists – verify the BUILD_ID file inside .next
        const buildIdPath = './src/dashboard/.next/BUILD_ID';
        if (!fs.existsSync(buildIdPath)) {
          this.logger.info('[Dashboard] First run – building dashboard…');
          execSync('pnpm build:dashboard', { stdio: 'inherit' });
        }
      }

      let command: string;
      let args: string[];

      if (dev) {
        // Use the dev script (no port expansion issues).
        command = 'pnpm';
        args = ['dev:dashboard'];
      } else {
        // Production: run Next directly to avoid shell variable expansion problems.
        const port = process.env.SERVER_PORT || '3000';
        command = process.platform === 'win32' ? 'npx.cmd' : 'npx';
        // Use --yes to auto-install if npx prompts (rare in containers).
        args = ['--yes', 'next', 'start', 'src/dashboard', '-H', '0.0.0.0', '-p', port];
      }

      this.dashboardProcess = spawn(command, args, {
        stdio: 'pipe',
        shell: true,
        cwd: process.cwd(),
        env: {
          ...process.env,
          NEXTAUTH_URL: this.dashboardUrl,
          DASHBOARD_URL: this.dashboardUrl,
        },
      });

      this.dashboardProcess.stdout.on('data', (data: Buffer) => {
        const output = data.toString().trim();
        if (output) this.logger.info(`[Dashboard] ${output}`);
      });

      this.dashboardProcess.stderr.on('data', (data: Buffer) => {
        const output = data.toString().trim();
        if (output) this.logger.error(`[Dashboard] ${output}`);
      });

      this.dashboardProcess.on('error', (error: Error) => {
        this.logger.error('[Dashboard] Failed to start process:', error);
        return false;
      });

      // Wait a moment for the dashboard to start
      await new Promise(resolve => setTimeout(resolve, 2000));

      return true;
    } catch (error) {
      this.logger.error('Failed to start dashboard:', error);
      return false;
    }
  }

  public async start() {
    try {
      this.logger.info('Starting bot...');
      this.logger.info(`Node.js version: ${process.version}`);
      this.logger.info(`Discord.js version: 14.21.0`);

      // Connect to database
      await this.database.connect();
      this.databaseConnected = true;
      this.logger.info('Connected to database');
      
      // Warm up database connection pool for better performance
      await this.database.warmupConnections();
      this.logger.info('Database connection pool warmed up');

      // Load addons
      await this.addonManager.loadAllAddons();
      this.logger.info('Loaded addons');

      // Start dashboard
      const dashboardStarted = await this.initializeDashboard();

      // Login to Discord
      await this.client.login(this.config.bot.token);

      // Log bot startup time for uptime calculation
      try {
        await this.database.updateOne(
          'bot_status',
          { key: 'start_time' },
          { $set: { timestamp: new Date(), key: 'start_time' } },
          { upsert: true }
        );
        this.logger.info('Bot startup time logged');
      } catch (err) {
        this.logger.warn('Failed to log startup time:', err);
      }

      // Load activities from DB and start rotation (now that client is ready)
      await this.initializePresenceActivities();

      // Watch for config changes and update presence dynamically
      ConfigManager.watch((newConfig) => {
        this.applyPresence(newConfig);
      });

      // Register member join/leave listeners
      this.initializeMemberListeners();


      // Log final status
      this.logger.info(`🚀 Bot is ready! Logged in as ${this.client.user?.tag}`);
      this.logger.info(`Bot ID: ${this.client.user?.id}`);
      this.logger.info(`Guilds: ${this.client.guilds.cache.size}`);
      
      // Print final status block with environment detection
      console.log('\n🔗 Connection Status');
      console.log('  Discord: ✓ Connected');
      console.log('  MongoDB: ✓ Connected');
      if (dashboardStarted) {
        const serverPort = process.env.SERVER_PORT || '3000';
        const envDashboardUrl = process.env.NEXTAUTH_URL || process.env.DASHBOARD_URL;
        const localIp = this.getLocalExternalIp();
        
        console.log(`  Dashboard: ✓ Running at ${this.dashboardUrl}`);
        
        // Provide additional connection information
        if (envDashboardUrl) {
          console.log(`    Public URL: ${envDashboardUrl}`);
        } else {
          console.log(`    Port: ${serverPort}`);
          if (localIp) {
            // Show only typical private LAN ranges (192.168.x.x or 10.x.x.x)
            if (localIp.startsWith('192.168.') || localIp.startsWith('10.')) {
              console.log(`    Local Network: http://${localIp}:${serverPort}`);
            }
          }
          console.log(`    Localhost: http://localhost:${serverPort}`);
          console.log(`    Hosting: Set NEXTAUTH_URL env var for public access`);
        }
      }

      // Handle process termination
      process.on('SIGINT', () => this.shutdown());
      process.on('SIGTERM', () => this.shutdown());

    } catch (error) {
      this.logger.error('Failed to start bot:', error);
      process.exit(1);
    }
  }

  private initializeMemberListeners() {
    this.client.on('guildMemberAdd', async (member) => {
      if (!this.databaseConnected) return;
      
      // Log member join for analytics
      try {
        await this.database.insertOne('member_logs', {
          userId: member.id,
          guildId: member.guild.id,
          timestamp: new Date(),
          action: 'join',
        });
      } catch (err) {
        this.logger.warn('Failed to log member join:', err);
      }

      // Handle built-in welcome system
      try {
        const config = await this.database.findOne('guild_configs', { guildId: member.guild.id });
        if (!config?.welcome?.enabled || !config.welcome.channelId) return;

        const channel = await member.guild.channels.fetch(config.welcome.channelId).catch(() => null);
        if (!channel || !(channel instanceof TextChannel)) return;

        const message = replacePlaceholders(config.welcome.message, member);
        const embed = new EmbedBuilder()
          .setColor(config.welcome.embedColor || '#00FF00')
          .setDescription(message)
          .setThumbnail(member.user.displayAvatarURL())
          .setTimestamp();
        
        await channel.send({ embeds: [embed] });

        // Handle auto-roles
        if (config.welcome.autoRoles && config.welcome.autoRoles.length > 0) {
          for (const roleId of config.welcome.autoRoles) {
            const role = member.guild.roles.cache.get(roleId);
            if (role) {
              await member.roles.add(role).catch(err => this.logger.warn(`Failed to add auto-role ${role.name}:`, err));
            }
          }
        }
      } catch (err) {
        this.logger.error('Error in built-in welcome system:', err);
      }
    });

    this.client.on('guildMemberRemove', async (member) => {
      if (!this.databaseConnected) return;

      // Log member leave for analytics
      try {
        let actionType: 'leave' | 'kick' | 'ban' = 'leave';
        // Check audit logs for kick/ban
        const me = member.guild.members.me;
        if (me?.permissions.has(PermissionsBitField.Flags.ViewAuditLog)) {
          const fetchedLogs = await member.guild.fetchAuditLogs({ limit: 1, type: AuditLogEvent.MemberKick });
          const kickLog = fetchedLogs.entries.first();
          if (kickLog && kickLog.target?.id === member.id && (Date.now() - kickLog.createdTimestamp) < 5000) {
            actionType = 'kick';
          } else {
            const banLogs = await member.guild.fetchAuditLogs({ limit: 1, type: AuditLogEvent.MemberBanAdd });
            const banLog = banLogs.entries.first();
            if (banLog && banLog.target?.id === member.id && (Date.now() - banLog.createdTimestamp) < 5000) {
              actionType = 'ban';
            }
          }
        }

        await this.database.insertOne('member_logs', {
          userId: member.id,
          guildId: member.guild.id,
          timestamp: new Date(),
          action: actionType,
        });
      } catch (err) {
        this.logger.warn('Failed to log member leave:', err);
      }

      // Handle built-in goodbye system
      try {
        const config = await this.database.findOne('guild_configs', { guildId: member.guild.id });
        if (!config?.goodbye?.enabled || !config.goodbye.channelId) return;

        const channel = await member.guild.channels.fetch(config.goodbye.channelId).catch(() => null);
        if (!channel || !(channel instanceof TextChannel)) return;

        const message = replacePlaceholders(config.goodbye.message, member);
        const embed = new EmbedBuilder()
          .setColor(config.goodbye.embedColor || '#FF0000')
          .setDescription(message)
          .setThumbnail(member.user.displayAvatarURL())
          .setTimestamp();
        
        await channel.send({ embeds: [embed] });
      } catch (err) {
        this.logger.error('Error in built-in goodbye system:', err);
      }
    });
  }

  private async shutdown() {
    this.logger.info('Shutting down...');

    // Kill dashboard process if running
    if (this.dashboardProcess) {
      this.dashboardProcess.kill();
      this.logger.info('Dashboard process terminated');
    }

    // Disconnect from database
    await this.database.disconnect();
    this.logger.info('Database disconnected');

    // Destroy Discord client
    this.client.destroy();
    this.logger.info('Discord client destroyed');

    process.exit(0);
  }

  /**
   * Apply the presence configuration (status + activity) to the Discord client.
   */
  private applyPresence(config: any) {
    if (!this.client.user) return;

    const presenceCfg = config?.bot?.presence || {};
    const status = (presenceCfg.status || 'online') as 'online' | 'idle' | 'dnd' | 'invisible';

    const activityCfg = presenceCfg.activity || {};
    const activityName = activityCfg.name || undefined;
    const typeStrRaw = (activityCfg.type || 'PLAYING') as string;

    const activityTypeKeyMap: Record<string, keyof typeof ActivityType> = {
      PLAYING: 'Playing',
      STREAMING: 'Streaming',
      LISTENING: 'Listening',
      WATCHING: 'Watching',
      COMPETING: 'Competing',
    };

    const activityTypeKey = activityTypeKeyMap[typeStrRaw.toUpperCase()] || 'Playing';
    const activityType = ActivityType[activityTypeKey];

    try {
      this.client.user.setPresence({
        status,
        activities: activityName ? [{ name: activityName, type: activityType }] : [],
      });
      this.logger.info(`Presence updated: status=${status}, activity=${activityTypeKey} ${activityName ?? ''}`);
    } catch (err) {
      this.logger.warn('Failed to update presence:', err);
    }
  }

  private async initializePresenceActivities() {
    try {
      const activitiesDoc = await this.database.db.collection('presence').findOne({ key: 'activities' });
      const activities = activitiesDoc?.activities || [];
      const rotationInterval = activitiesDoc?.activityRotationInterval || 60; // Default to 60 seconds

      // Only use database activities if there are actually activities defined
      if (activities.length > 0) {
        this.logger.info(`Loading ${activities.length} presence activities from database:`);
        activities.forEach((act: { type: string; name: string }, idx: number) => {
          this.logger.info(`  ${idx + 1}. ${act.type} ${act.name}`);
        });
        
        // Start with a random activity index
        this.currentActivityIndex = Math.floor(Math.random() * activities.length);
        this.updatePresenceFromActivity(activities[this.currentActivityIndex]);

        // Always start rotation for any number of activities (even single activity for consistency)
        if (this.activityRotationHandle) clearInterval(this.activityRotationHandle);
        
        this.activityRotationHandle = setInterval(() => {
          // Move to next activity in sequence
          this.currentActivityIndex = (this.currentActivityIndex + 1) % activities.length;
          const act = activities[this.currentActivityIndex];
          this.updatePresenceFromActivity(act);
        }, rotationInterval * 1000);
        this.logger.info(`Activity rotation enabled (${rotationInterval} second intervals) for ${activities.length} activities - sequential cycling`);
      } else {
        // No activities in database or empty array; fallback to config presence
        this.logger.info('No activities found in database, using config presence');
        this.applyPresence(this.config);
      }
    } catch (err) {
      this.logger.warn('Failed to initialize presence activities:', err);
      this.applyPresence(this.config);
    }
  }

  private updatePresenceFromActivity(activity: { type: string; name: string }) {
    if (!this.client.user) return;
    const typeKeyMap: Record<string, keyof typeof ActivityType> = {
      PLAYING: 'Playing',
      STREAMING: 'Streaming',
      LISTENING: 'Listening',
      WATCHING: 'Watching',
      COMPETING: 'Competing',
    };
    const actKey = typeKeyMap[activity.type.toUpperCase()] || 'Playing';
    const actType = ActivityType[actKey];
    
    try {
      this.client.user.setPresence({
        status: 'online',
        activities: [{ name: activity.name, type: actType }],
      });
    } catch (err) {
      this.logger.warn('Failed to update activity:', err);
    }
  }
}

// Start the bot
const bot = new Bot();
bot.start();
