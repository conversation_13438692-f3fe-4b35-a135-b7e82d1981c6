import winston from 'winston';
import path from 'node:path';
import fs from 'node:fs';
import chalk from 'chalk';
import type { BotConfig } from '@/types/index.js';
import { ColorManager } from '@/core/ColorManager.js';
import 'winston-daily-rotate-file';

// Custom console format with colors and emojis
const consoleFormat = winston.format.combine(
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    // Level styling with emojis
    let levelIcon = '';
    let levelColor = chalk.white;
    
    switch (level.toLowerCase()) {
      case 'error':
        levelIcon = '❌';
        levelColor = chalk.red.bold;
        break;
      case 'warn':
        levelIcon = '⚠️ ';
        levelColor = chalk.yellow.bold;
        break;
      case 'info':
        levelIcon = 'ℹ️ ';
        levelColor = chalk.blue.bold;
        break;
      case 'debug':
        levelIcon = '🔍';
        levelColor = chalk.gray;
        break;
      default:
        levelIcon = '📝';
        levelColor = chalk.white;
    }

    // Format timestamp
    const timeFormatted = chalk.gray(`[${timestamp}]`);
    
    // Format level
    const levelFormatted = levelColor(`${levelIcon} ${level.toUpperCase().padEnd(5)}`);
    
    // Format message with type safety
    const messageStr = String(message);
    let messageFormatted = messageStr;
    
    // Add colors to specific message types
    if (messageStr.includes('Bot is ready!')) {
      messageFormatted = chalk.green.bold('🚀 ' + messageStr);
    } else if (messageStr.includes('Connected to MongoDB')) {
      messageFormatted = chalk.green('🗃️  ' + messageStr);
    } else if (messageStr.includes('Failed to connect to MongoDB')) {
      messageFormatted = chalk.red('🗃️  ' + messageStr);
    } else if (messageStr.includes('Starting bot')) {
      messageFormatted = chalk.blue.bold('🤖 ' + messageStr);
    } else if (messageStr.includes('[Dashboard]')) {
      messageFormatted = chalk.magenta('🌐 ' + messageStr);
    }

    let log = `${timeFormatted} ${levelFormatted} ${messageFormatted}`;
    
    // Add metadata if present
    if (Object.keys(meta).length > 0) {
      log += chalk.gray(` ${JSON.stringify(meta)}`);
    }
    
    // Add stack trace for errors
    if (stack) {
      log += '\n' + chalk.red(String(stack));
    }
    
    return log;
  })
);

// File format (without colors)
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }: winston.Logform.TransformableInfo) => {
    let log = `[${timestamp}] ${level.toUpperCase().padEnd(5)} ${String(message)}`;
    
    if (Object.keys(meta).length > 0) {
      log += ` ${JSON.stringify(meta)}`;
    }
    
    if (stack) {
      log += `\n${String(stack)}`;
    }
    
    return log;
  })
);

export class Logger {
  private static instance: winston.Logger | null = null;

  public static createLogger(): winston.Logger {
    if (!this.instance) {
      this.instance = winston.createLogger({
        level: 'info',
        format: winston.format.combine(
          winston.format.timestamp({ format: 'HH:mm:ss' }),
          winston.format.errors({ stack: true })
        ),
        transports: [
          new winston.transports.Console({
            format: consoleFormat
          }),
          new winston.transports.DailyRotateFile({
            filename: 'logs/bot-%DATE%.log',
            datePattern: 'YYYY-MM-DD',
            maxSize: '10m',
            maxFiles: '7d',
            format: winston.format.combine(
              winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
              winston.format.json()
            )
          })
        ]
      });
    }
    return this.instance;
  }

  public static createAddonLogger(addonName: string): winston.Logger {
    return winston.createLogger({
      level: 'info',
      format: winston.format.combine(
        winston.format.timestamp({ format: 'HH:mm:ss' }),
        winston.format.errors({ stack: true })
      ),
      transports: [
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.timestamp({ format: 'HH:mm:ss' }),
            winston.format.printf(({ timestamp, level, message, stack }) => {
              const timeFormatted = chalk.gray(`[${timestamp}]`);
              const addonFormatted = chalk.cyan(`[${addonName}]`);
              const levelFormatted = level === 'error' ? chalk.red(level.toUpperCase()) :
                                   level === 'warn' ? chalk.yellow(level.toUpperCase()) :
                                   chalk.blue(level.toUpperCase());
              
              let log = `${timeFormatted} ${levelFormatted} ${addonFormatted} ${message}`;
              if (stack) {
                log += '\n' + chalk.red(String(stack));
              }
              return log;
            })
          )
        }),
        new winston.transports.DailyRotateFile({
          filename: `logs/${addonName}-%DATE%.log`,
          datePattern: 'YYYY-MM-DD',
          maxSize: '10m',
          maxFiles: '7d',
          format: winston.format.combine(
            winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
            winston.format.json()
          )
        })
      ]
    });
  }

  public static create(config: BotConfig): winston.Logger {
    if (Logger.instance) {
      return Logger.instance;
    }

    const transports: winston.transport[] = [];

    // Console transport with pretty formatting
    if (config.logging.console) {
      transports.push(
        new winston.transports.Console({
          level: config.logging.level,
          format: consoleFormat
        })
      );
    }

    // File transport with standard formatting
    if (config.logging.file.enabled) {
      const logDir = path.resolve(config.logging.file.path);
      
      // Ensure log directory exists
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }

      transports.push(
        new winston.transports.File({
          level: config.logging.level,
          filename: path.join(logDir, 'bot.log'),
          maxsize: this.parseSize(config.logging.file.maxSize),
          maxFiles: config.logging.file.maxFiles,
          format: fileFormat
        })
      );

      // Separate error log
      transports.push(
        new winston.transports.File({
          level: 'error',
          filename: path.join(logDir, 'error.log'),
          maxsize: this.parseSize(config.logging.file.maxSize),
          maxFiles: config.logging.file.maxFiles,
          format: fileFormat
        })
      );
    }

    Logger.instance = winston.createLogger({
      level: config.logging.level,
      transports,
      exitOnError: false
    });

    return Logger.instance;
  }

  public static get(): winston.Logger {
    if (!Logger.instance) {
      throw new Error('Logger not initialized. Call Logger.create() first.');
    }
    return Logger.instance;
  }

  private static parseSize(size: string): number {
    const units: Record<string, number> = {
      'b': 1,
      'k': 1024,
      'm': 1024 * 1024,
      'g': 1024 * 1024 * 1024
    } as const;

    const match = size.toLowerCase().match(/^(\d+)([bkmg]?)$/);
    if (!match) {
      return 10 * 1024 * 1024; // Default 10MB
    }

    const numStr = match[1];
    const unit = match[2] || 'b';
    
    if (!numStr) {
      return 10 * 1024 * 1024; // Default fallback
    }
    
    return parseInt(numStr, 10) * (units[unit] ?? 1);
  }

  // Simple startup message instead of box
  public static showStartupBanner(): void {
    console.log(chalk.cyan.bold('\n🤖 404 Bot') + chalk.gray(' - Discord Bot with Addon System &amp; MongoDB'));
    console.log(chalk.green('✓') + ' TypeScript + Discord.js + ES2024');
    console.log(chalk.green('✓') + ' MongoDB Integration');
    console.log(chalk.green('✓') + ' Hot-Reloadable Addons');
    console.log(chalk.green('✓') + ' Comprehensive Logging\n');
  }

  // System info display with modern features
  public static showSystemInfo(): void {
    const memoryUsage = process.memoryUsage();
    const heapUsed = Math.round(memoryUsage.heapUsed / 1024 / 1024);
    const heapTotal = Math.round(memoryUsage.heapTotal / 1024 / 1024);
    
    const info = `
${chalk.blue.bold('🔧 System Information')}
  ${chalk.gray('Node.js:')} ${chalk.white(process.version)}
  ${chalk.gray('Platform:')} ${chalk.white(process.platform)} ${chalk.white(process.arch)}
  ${chalk.gray('Memory:')} ${chalk.white(`${heapUsed}MB`)} / ${chalk.white(`${heapTotal}MB`)}
  ${chalk.gray('ES Version:')} ${chalk.white('ES2024')}
`;
    console.log(info);
  }

  // Connection status display
  public static showConnectionStatus(database: boolean, discord: boolean): void {
    const status = `
${chalk.blue.bold('🔗 Connection Status')}
  ${chalk.gray('Discord:')} ${discord ? chalk.green('✓ Connected') : chalk.red('✗ Disconnected')}
  ${chalk.gray('MongoDB:')} ${database ? chalk.green('✓ Connected') : chalk.red('✗ Disconnected')}
`;
    console.log(status);
  }
}
