"use strict";(()=>{var e={};e.id=8255,e.ids=[8255],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return i}});var i=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,i){return i in t?t[i]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,i)):"function"==typeof t&&"default"===i?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2290:(e,t,i)=>{i.d(t,{L:()=>s});var a=i(8580),o=i(2518);let r=null,n=null;async function s(){if(n)return n;let e=a.dashboardConfig.database?.url||"mongodb://localhost:27017",t=a.dashboardConfig.database?.name||"discord_bot";return r||(r=await o.MongoClient.connect(e,{...a.dashboardConfig.database?.options||{}})),n=r.db(t)}},2518:e=>{e.exports=require("mongodb")},3433:(e,t,i)=>{e.exports=i(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8351:(e,t,i)=>{i.r(t),i.d(t,{config:()=>p,default:()=>c,routeModule:()=>b});var a={};i.r(a),i.d(a,{default:()=>u});var o=i(3433),r=i(264),n=i(584),s=i(5806),d=i(8525),l=i(2290);async function u(e,t){let i=await (0,s.getServerSession)(e,t,d.authOptions);if(!i?.user?.id)return t.status(401).json({error:"Unauthorized"});if(!i.user?.isAdmin)return t.status(403).json({error:"Admin access required"});let a=(await (0,l.L)()).collection("custom_applications");if("GET"===e.method)try{let e=(await a.find({}).toArray()).map(({_id:e,...t})=>t);return t.status(200).json({applications:e})}catch(e){return t.status(500).json({error:"Failed to fetch applications"})}if("POST"===e.method)try{let o=e.body;if(!o.title||!o.description)return t.status(400).json({error:"Title and description are required"});if(!o.id)return t.status(400).json({error:"Application ID is required"});if(await a.findOne({id:o.id})){let e={id:o.id,title:o.title,description:o.description,color:o.color,icon:o.icon,enabled:o.enabled,questions:o.questions,settings:o.settings,updatedAt:new Date};return await a.updateOne({id:o.id},{$set:e}),t.status(200).json({application:e})}{let e={...o,createdAt:new Date,updatedAt:new Date,createdBy:i.user.id};return await a.insertOne(e),t.status(201).json({application:e})}}catch(e){return t.status(500).json({error:"Failed to save application"})}return t.status(405).json({error:"Method not allowed"})}let c=(0,n.M)(a,"default"),p=(0,n.M)(a,"config"),b=new o.PagesAPIRouteModule({definition:{kind:r.A.PAGES_API,page:"/api/admin/applications-builder",pathname:"/api/admin/applications-builder",bundlePath:"",filename:""},userland:a})},8525:(e,t,i)=>{i.r(t),i.d(t,{authOptions:()=>d,default:()=>l});var a=i(5542),o=i.n(a);let r=require("next-auth/providers/discord");var n=i.n(r),s=i(8580);let d={providers:[n()({clientId:s.dashboardConfig.bot.clientId,clientSecret:s.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:i})=>(t&&i&&(e.accessToken=t.access_token||null,e.id=i.id||null),e),async session({session:e,token:t}){if(e?.user){let i=t.id||null,a=t.accessToken||null;e.user.id=i,e.user.accessToken=a;let o=!1;if(i)if((s.dashboardConfig.dashboard.admins||[]).includes(i))o=!0;else{let e=s.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&s.dashboardConfig.bot.token&&s.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${s.dashboardConfig.bot.guildId}/members/${i}`,{headers:{Authorization:`Bot ${s.dashboardConfig.bot.token}`}});if(t.ok){let i=await t.json();o=e.some(e=>i.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let i=new URL(t),a=`${i.protocol}//localhost${i.port?`:${i.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:s.dashboardConfig.dashboard.session.secret||s.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=o()(d)},8580:(e,t,i)=>{i.r(t),i.d(t,{dashboardConfig:()=>d,default:()=>l});var a=i(9021),o=i(2115),r=i.n(o),n=i(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");s=r().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var i=t(t.s=8351);module.exports=i})();