import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, EmbedBuilder } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('warnings')
  .setDescription('View warning history for a user')
  .addUserOption(option =>
    option.setName('user')
      .setDescription('The user to check warnings for')
      .setRequired(true))
  .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers);

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  const targetUser = interaction.options.getUser('user', true);

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ModerateMembers)) {
    await interaction.reply({ content: '❌ You do not have permission to view warnings.', ephemeral: true });
    return;
  }

  try {
    // Get user's warnings from database
    const warnings = await bot.database.db
      .collection('warnings')
      .find({ userId: targetUser.id, guildId: interaction.guild?.id })
      .sort({ timestamp: -1 })
      .limit(10)
      .toArray();

    if (warnings.length === 0) {
      await interaction.reply({ 
        content: `✅ **${targetUser.tag}** has no warnings on record.`,
        ephemeral: true 
      });
      return;
    }

    const embed = new EmbedBuilder()
      .setTitle(`⚠️ Warning History for ${targetUser.tag}`)
      .setColor(0xFFAA00)
      .setThumbnail(targetUser.displayAvatarURL())
      .setDescription(`Total warnings: **${warnings.length}**`)
      .setTimestamp();

    // Add warning fields (limit to 10 most recent)
    for (let i = 0; i < Math.min(warnings.length, 10); i++) {
      const warning = warnings[i];
      const date = new Date(warning.timestamp).toLocaleDateString();
      const moderator = await interaction.client.users.fetch(warning.moderatorId).catch(() => null);
      
      embed.addFields({
        name: `Warning #${warnings.length - i}`,
        value: `**Date:** ${date}\n**Moderator:** ${moderator?.tag || 'Unknown'}\n**Reason:** ${warning.reason}`,
        inline: false
      });
    }

    if (warnings.length > 10) {
      embed.setFooter({ text: `Showing 10 most recent warnings out of ${warnings.length} total` });
    }

    await interaction.reply({ embeds: [embed], ephemeral: true });

  } catch (error) {
    console.error('Error fetching warnings:', error);
    await interaction.reply({ content: '❌ Failed to fetch warnings. Please try again.', ephemeral: true });
  }
}

export const cooldown = 3000; 