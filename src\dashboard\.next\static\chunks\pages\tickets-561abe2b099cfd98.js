(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8111],{5357:(e,t,r)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/tickets",function(){return r(44991)}])},44991:(e,t,r)=>{"use strict";r.r(t),r.d(t,{__N_SSP:()=>L,default:()=>P});var s=r(94513),i=r(95845),c=r(59001),n=r(79156),l=r(51961),o=r(31678),a=r(41611),d=r(78902),h=r(62690),x=r(49217),p=r(57561),j=r(15373),u=r(51927),k=r(8595),g=r(1341),f=r(35981),b=r(95497),w=r(71601),T=r(94285),y=r(60341),m=r(22907),C=r(9557),S=r(7680),_=r(52922),E=r(47847),v=r(59365),z=r(85104),D=r(40443),F=r(63730),N=r(25964),I=r(7627),O=r(28245);function $(e){let{isOpen:t,onClose:r,onSuccess:i}=e,c=(0,m.d)(),[n,l]=(0,T.useState)(""),[o,a]=(0,T.useState)("support"),[d,x]=(0,T.useState)(!1),p=async()=>{x(!0);try{let e=await fetch("/api/discord/tickets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({reason:n,category:o})});if(!e.ok){let t=await e.json();throw Error(t.error||"Failed to create ticket")}c({title:"Ticket Created",description:"Your support ticket has been opened.",status:"success",duration:3e3}),l(""),a("support"),i(),r()}catch(e){c({title:"Error",description:e.message||"Failed to create ticket",status:"error",duration:5e3})}finally{x(!1)}};return(0,s.jsxs)(C.aF,{isOpen:t,onClose:r,size:"lg",scrollBehavior:"inside",children:[(0,s.jsx)(S.m,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(_.$,{bg:"gray.800",children:[(0,s.jsx)(E.r,{children:"Create Support Ticket"}),(0,s.jsx)(v.s,{}),(0,s.jsxs)(z.c,{children:[(0,s.jsxs)(D.MJ,{mb:4,children:[(0,s.jsx)(F.l,{children:"Category"}),(0,s.jsxs)(N.l,{value:o,onChange:e=>a(e.target.value),children:[(0,s.jsx)("option",{value:"support",children:"Support"}),(0,s.jsx)("option",{value:"18plus",children:"18+"}),(0,s.jsx)("option",{value:"other",children:"Other"})]})]}),(0,s.jsxs)(D.MJ,{children:[(0,s.jsx)(F.l,{children:"Describe your issue"}),(0,s.jsx)(I.T,{placeholder:"I need help with...",value:n,onChange:e=>l(e.target.value),rows:5})]})]}),(0,s.jsxs)(O.j,{children:[(0,s.jsx)(h.$,{mr:3,variant:"ghost",onClick:r,children:"Cancel"}),(0,s.jsx)(h.$,{colorScheme:"blue",onClick:p,isLoading:d,children:"Create Ticket"})]})]})]})}var A=r(97146),L=!0;function P(e){let{isAdmin:t}=e,[r,m]=(0,T.useState)([]),[C,S]=(0,T.useState)(!0),{isOpen:_,onOpen:E,onClose:v}=(0,i.j)(),z=async()=>{S(!0);try{let e=await fetch("/api/discord/tickets");if(!e.ok)throw Error("Failed to fetch tickets");let t=await e.json();m(t)}catch(e){}finally{S(!1)}};return(0,T.useEffect)(()=>{z()},[]),(0,s.jsx)(y.A,{children:(0,s.jsxs)(c.m,{maxW:"container.xl",py:8,children:[(0,s.jsxs)(n.T,{spacing:8,align:"stretch",children:[(0,s.jsxs)(l.a,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",textAlign:"center",children:[(0,s.jsx)(o.D,{size:"2xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",mb:4,children:"Support Tickets"}),(0,s.jsx)(a.E,{color:"gray.300",fontSize:"lg",children:"Open new tickets or review existing ones"})]}),(0,s.jsx)(d.z,{justify:"flex-end",children:(0,s.jsx)(h.$,{leftIcon:(0,s.jsx)(x.I,{as:A.GGD}),colorScheme:"blue",onClick:E,children:"Create Ticket"})}),(0,s.jsx)(l.a,{bg:"whiteAlpha.100",backdropFilter:"blur(10px)",rounded:"lg",p:4,children:C?(0,s.jsx)(d.z,{justify:"center",py:10,children:(0,s.jsx)(p.y,{size:"lg"})}):0===r.length?(0,s.jsx)(a.E,{textAlign:"center",py:10,color:"gray.300",children:"You have no tickets yet."}):(0,s.jsxs)(j.X,{variant:"simple",children:[(0,s.jsx)(u.d,{children:(0,s.jsxs)(k.Tr,{children:[(0,s.jsx)(g.Th,{children:"ID"}),(0,s.jsx)(g.Th,{children:"Reason"}),(0,s.jsx)(g.Th,{children:"Status"}),(0,s.jsx)(g.Th,{children:"Created"}),(0,s.jsx)(g.Th,{children:"Actions"})]})}),(0,s.jsx)(f.N,{children:r.map(e=>(0,s.jsxs)(k.Tr,{children:[(0,s.jsx)(b.Td,{children:e._id.toString().slice(-6)}),(0,s.jsx)(b.Td,{maxW:"300px",children:(0,s.jsx)(a.E,{isTruncated:!0,title:e.reason,children:e.reason||"No reason provided"})}),(0,s.jsx)(b.Td,{children:(0,s.jsx)(w.E,{colorScheme:"open"===e.status?"green":"red",children:e.status})}),(0,s.jsx)(b.Td,{children:new Date(e.createdAt).toLocaleString()}),(0,s.jsx)(b.Td,{children:(0,s.jsxs)(d.z,{spacing:2,children:[e.discordLink&&(0,s.jsx)(h.$,{as:"a",href:e.discordLink,target:"_blank",size:"sm",leftIcon:(0,s.jsx)(x.I,{as:A.HaR}),children:"Discord"}),"open"===e.status&&(0,s.jsx)(h.$,{size:"sm",colorScheme:"yellow",onClick:async()=>{if(window.confirm("Close this ticket?"))try{if(!(await fetch("/api/discord/tickets/".concat(e._id),{method:"PATCH"})).ok)throw Error("Failed to close ticket");z()}catch(e){}},children:"Close"}),t&&"closed"===e.status&&(0,s.jsx)(h.$,{size:"sm",colorScheme:"red",onClick:async()=>{if(window.confirm("Delete this ticket? This is irreversible."))try{if(!(await fetch("/api/discord/tickets/".concat(e._id),{method:"DELETE"})).ok)throw Error("Failed to delete ticket");z()}catch(e){}},children:"Delete"}),"closed"===e.status&&(0,s.jsx)(h.$,{as:"a",href:"/api/discord/tickets/".concat(e._id,"/transcript"),size:"sm",colorScheme:"green",leftIcon:(0,s.jsx)(x.I,{as:A.HaR}),target:"_blank",children:"Transcript"})]})})]},e._id))})]})})]}),(0,s.jsx)($,{isOpen:_,onClose:v,onSuccess:z})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4108,9998,4976,217,2965,7937,341,636,6593,8792],()=>t(5357)),_N_E=e.O()}]);