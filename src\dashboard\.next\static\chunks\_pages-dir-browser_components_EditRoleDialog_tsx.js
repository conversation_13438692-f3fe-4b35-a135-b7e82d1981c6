"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_components_EditRoleDialog_tsx"],{

/***/ "(pages-dir-browser)/./components/EditRoleDialog.tsx":
/*!***************************************!*\
  !*** ./components/EditRoleDialog.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditRoleDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @chakra-ui/react */ \"(pages-dir-browser)/../../node_modules/.pnpm/@chakra-ui+react@2.10.9_@em_4601d62a7852177fac9c88e9f353f581/node_modules/@chakra-ui/react/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(pages-dir-browser)/../../node_modules/.pnpm/react@19.1.0/node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!react-icons/fi */ \"(pages-dir-browser)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n// @ts-nocheck\n\nvar _s = $RefreshSig$();\n\n\n\nconst PERMISSION_GROUPS = {\n    General: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiShield,\n        permissions: [\n            'ADMINISTRATOR',\n            'VIEW_AUDIT_LOG',\n            'MANAGE_GUILD',\n            'MANAGE_ROLES',\n            'MANAGE_CHANNELS',\n            'MANAGE_EMOJIS_AND_STICKERS',\n            'MANAGE_WEBHOOKS',\n            'VIEW_CHANNEL'\n        ]\n    },\n    Text: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiMessageSquare,\n        permissions: [\n            'SEND_MESSAGES',\n            'EMBED_LINKS',\n            'ATTACH_FILES',\n            'ADD_REACTIONS',\n            'USE_EXTERNAL_EMOJIS',\n            'MENTION_EVERYONE',\n            'MANAGE_MESSAGES',\n            'READ_MESSAGE_HISTORY'\n        ]\n    },\n    Voice: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiVolume2,\n        permissions: [\n            'CONNECT',\n            'SPEAK',\n            'STREAM',\n            'USE_VAD',\n            'PRIORITY_SPEAKER',\n            'MUTE_MEMBERS',\n            'DEAFEN_MEMBERS',\n            'MOVE_MEMBERS'\n        ]\n    },\n    Members: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_2__.FiUsers,\n        permissions: [\n            'KICK_MEMBERS',\n            'BAN_MEMBERS',\n            'CHANGE_NICKNAME',\n            'MANAGE_NICKNAMES',\n            'CREATE_INSTANT_INVITE'\n        ]\n    }\n};\nfunction EditRoleDialog(param) {\n    let { isOpen, onClose, onSuccess, role } = param;\n    _s();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        color: '#99AAB5',\n        permissions: [],\n        hoist: false,\n        mentionable: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditRoleDialog.useEffect\": ()=>{\n            if (role) {\n                setFormData({\n                    name: role.name || '',\n                    color: role.color || '#99AAB5',\n                    permissions: role.permissions || [],\n                    hoist: role.hoist || false,\n                    mentionable: role.mentionable || false\n                });\n            }\n        }\n    }[\"EditRoleDialog.useEffect\"], [\n        role\n    ]);\n    const handleSubmit = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch(\"/api/discord/roles/\".concat(role.id), {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.message || 'Failed to update role');\n            }\n            toast({\n                title: 'Success',\n                description: 'Role updated successfully',\n                status: 'success',\n                duration: 3000\n            });\n            onSuccess();\n            onClose();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to update role',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePermissionChange = (permission)=>{\n        setFormData((prev)=>({\n                ...prev,\n                permissions: prev.permissions.includes(permission) ? prev.permissions.filter((p)=>p !== permission) : [\n                    ...prev.permissions,\n                    permission\n                ]\n            }));\n    };\n    const handleChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"xl\",\n        scrollBehavior: \"inside\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalOverlay, {\n                backdropFilter: \"blur(10px)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalContent, {\n                bg: \"gray.800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalHeader, {\n                        children: \"Edit Role\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.VStack, {\n                            spacing: 6,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                            children: \"Role Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            placeholder: \"Enter role name\",\n                                            value: formData.name,\n                                            onChange: (e)=>handleChange('name', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormLabel, {\n                                            children: \"Role Color\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                            type: \"color\",\n                                            value: formData.color,\n                                            onChange: (e)=>handleChange('color', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.FormControl, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                        spacing: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                isChecked: formData.hoist,\n                                                onChange: (e)=>handleChange('hoist', e.target.checked),\n                                                children: \"Display role separately\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                isChecked: formData.mentionable,\n                                                onChange: (e)=>handleChange('mentionable', e.target.checked),\n                                                children: \"Allow anyone to @mention\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    alignSelf: \"flex-start\",\n                                    children: \"Permissions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                Object.entries(PERMISSION_GROUPS).map((param)=>{\n                                    let [groupName, group] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Box, {\n                                        w: \"full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.HStack, {\n                                                mb: 2,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Icon, {\n                                                        as: group.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Text, {\n                                                        fontWeight: \"semibold\",\n                                                        children: groupName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.SimpleGrid, {\n                                                columns: 2,\n                                                spacing: 2,\n                                                children: group.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Checkbox, {\n                                                        isChecked: formData.permissions.includes(permission),\n                                                        onChange: ()=>handlePermissionChange(permission),\n                                                        children: permission.split('_').map((word)=>word.charAt(0) + word.slice(1).toLowerCase()).join(' ')\n                                                    }, permission, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, groupName, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this);\n                                })\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.ModalFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                mr: 3,\n                                onClick: onClose,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                colorScheme: \"blue\",\n                                onClick: handleSubmit,\n                                isLoading: isLoading,\n                                children: \"Save Changes\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(EditRoleDialog, \"FTsbvQc1QslR8HcAtAS219dbVwA=\", false, function() {\n    return [\n        _chakra_ui_react__WEBPACK_IMPORTED_MODULE_3__.useToast\n    ];\n});\n_c = EditRoleDialog;\nvar _c;\n$RefreshReg$(_c, \"EditRoleDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./components/EditRoleDialog.tsx\n"));

/***/ }),

/***/ "(pages-dir-browser)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!****************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \****************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-browser)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1icm93c2VyKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpTWVzc2FnZVNxdWFyZSxGaVNoaWVsZCxGaVVzZXJzLEZpVm9sdW1lMiE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-browser)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n"));

/***/ })

}]);