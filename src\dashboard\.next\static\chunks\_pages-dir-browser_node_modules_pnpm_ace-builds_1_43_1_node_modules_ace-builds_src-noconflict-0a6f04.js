/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-0a6f04"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/theme-twilight.js":
/*!***********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/theme-twilight.js ***!
  \***********************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* module decorator */ module = __webpack_require__.nmd(module);\nace.define(\"ace/theme/twilight-css\",[\"require\",\"exports\",\"module\"], function(require, exports, module){module.exports = \".ace-twilight .ace_gutter {\\n  background: #232323;\\n  color: #E2E2E2\\n}\\n\\n.ace-twilight .ace_print-margin {\\n  width: 1px;\\n  background: #232323\\n}\\n\\n.ace-twilight {\\n  background-color: #141414;\\n  color: #F8F8F8\\n}\\n\\n.ace-twilight .ace_cursor {\\n  color: #A7A7A7\\n}\\n\\n.ace-twilight .ace_marker-layer .ace_selection {\\n  background: rgba(221, 240, 255, 0.20)\\n}\\n\\n.ace-twilight.ace_multiselect .ace_selection.ace_start {\\n  box-shadow: 0 0 3px 0px #141414;\\n}\\n\\n.ace-twilight .ace_marker-layer .ace_step {\\n  background: rgb(102, 82, 0)\\n}\\n\\n.ace-twilight .ace_marker-layer .ace_bracket {\\n  margin: -1px 0 0 -1px;\\n  border: 1px solid rgba(255, 255, 255, 0.25)\\n}\\n\\n.ace-twilight .ace_marker-layer .ace_active-line {\\n  background: rgba(255, 255, 255, 0.031)\\n}\\n\\n.ace-twilight .ace_gutter-active-line {\\n  background-color: rgba(255, 255, 255, 0.031)\\n}\\n\\n.ace-twilight .ace_marker-layer .ace_selected-word {\\n  border: 1px solid rgba(221, 240, 255, 0.20)\\n}\\n\\n.ace-twilight .ace_invisible {\\n  color: rgba(255, 255, 255, 0.25)\\n}\\n\\n.ace-twilight .ace_keyword,\\n.ace-twilight .ace_meta {\\n  color: #CDA869\\n}\\n\\n.ace-twilight .ace_constant,\\n.ace-twilight .ace_constant.ace_character,\\n.ace-twilight .ace_constant.ace_character.ace_escape,\\n.ace-twilight .ace_constant.ace_other,\\n.ace-twilight .ace_heading,\\n.ace-twilight .ace_markup.ace_heading,\\n.ace-twilight .ace_support.ace_constant {\\n  color: #CF6A4C\\n}\\n\\n.ace-twilight .ace_invalid.ace_illegal {\\n  color: #F8F8F8;\\n  background-color: rgba(86, 45, 86, 0.75)\\n}\\n\\n.ace-twilight .ace_invalid.ace_deprecated {\\n  text-decoration: underline;\\n  font-style: italic;\\n  color: #D2A8A1\\n}\\n\\n.ace-twilight .ace_support {\\n  color: #9B859D\\n}\\n\\n.ace-twilight .ace_fold {\\n  background-color: #AC885B;\\n  border-color: #F8F8F8\\n}\\n\\n.ace-twilight .ace_support.ace_function {\\n  color: #DAD085\\n}\\n\\n.ace-twilight .ace_list,\\n.ace-twilight .ace_markup.ace_list,\\n.ace-twilight .ace_storage {\\n  color: #F9EE98\\n}\\n\\n.ace-twilight .ace_entity.ace_name.ace_function,\\n.ace-twilight .ace_meta.ace_tag {\\n  color: #AC885B\\n}\\n\\n.ace-twilight .ace_string {\\n  color: #8F9D6A\\n}\\n\\n.ace-twilight .ace_string.ace_regexp {\\n  color: #E9C062\\n}\\n\\n.ace-twilight .ace_comment {\\n  font-style: italic;\\n  color: #5F5A60\\n}\\n\\n.ace-twilight .ace_variable {\\n  color: #7587A6\\n}\\n\\n.ace-twilight .ace_xml-pe {\\n  color: #494949\\n}\\n\\n.ace-twilight .ace_indent-guide {\\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAEklEQVQImWMQERFpYLC1tf0PAAgOAnPnhxyiAAAAAElFTkSuQmCC) right repeat-y\\n}\\n\\n.ace-twilight .ace_indent-guide-active {\\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAEklEQVQIW2PQ1dX9zzBz5sz/ABCcBFFentLlAAAAAElFTkSuQmCC) right repeat-y;\\n}\\n\";\n\n});\n\nace.define(\"ace/theme/twilight\",[\"require\",\"exports\",\"module\",\"ace/theme/twilight-css\",\"ace/lib/dom\"], function(require, exports, module){exports.isDark = true;\nexports.cssClass = \"ace-twilight\";\nexports.cssText = require(\"./twilight-css\");\nvar dom = require(\"../lib/dom\");\ndom.importCssString(exports.cssText, exports.cssClass, false);\n\n});                (function() {\n                    ace.require([\"ace/theme/twilight\"], function(m) {\n                        if ( true && module) {\n                            module.exports = m;\n                        }\n                    });\n                })();\n            //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/theme-twilight.js\n"));

/***/ })

}]);