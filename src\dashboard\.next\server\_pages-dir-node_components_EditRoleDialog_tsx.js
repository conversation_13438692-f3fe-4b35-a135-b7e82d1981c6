"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_pages-dir-node_components_EditRoleDialog_tsx";
exports.ids = ["_pages-dir-node_components_EditRoleDialog_tsx"];
exports.modules = {

/***/ "(pages-dir-node)/./components/EditRoleDialog.tsx":
/*!***************************************!*\
  !*** ./components/EditRoleDialog.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditRoleDialog)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// @ts-nocheck\n\n\n\n\nconst PERMISSION_GROUPS = {\n    General: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiShield,\n        permissions: [\n            'ADMINISTRATOR',\n            'VIEW_AUDIT_LOG',\n            'MANAGE_GUILD',\n            'MANAGE_ROLES',\n            'MANAGE_CHANNELS',\n            'MANAGE_EMOJIS_AND_STICKERS',\n            'MANAGE_WEBHOOKS',\n            'VIEW_CHANNEL'\n        ]\n    },\n    Text: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiMessageSquare,\n        permissions: [\n            'SEND_MESSAGES',\n            'EMBED_LINKS',\n            'ATTACH_FILES',\n            'ADD_REACTIONS',\n            'USE_EXTERNAL_EMOJIS',\n            'MENTION_EVERYONE',\n            'MANAGE_MESSAGES',\n            'READ_MESSAGE_HISTORY'\n        ]\n    },\n    Voice: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiVolume2,\n        permissions: [\n            'CONNECT',\n            'SPEAK',\n            'STREAM',\n            'USE_VAD',\n            'PRIORITY_SPEAKER',\n            'MUTE_MEMBERS',\n            'DEAFEN_MEMBERS',\n            'MOVE_MEMBERS'\n        ]\n    },\n    Members: {\n        icon: _barrel_optimize_names_FiMessageSquare_FiShield_FiUsers_FiVolume2_react_icons_fi__WEBPACK_IMPORTED_MODULE_3__.FiUsers,\n        permissions: [\n            'KICK_MEMBERS',\n            'BAN_MEMBERS',\n            'CHANGE_NICKNAME',\n            'MANAGE_NICKNAMES',\n            'CREATE_INSTANT_INVITE'\n        ]\n    }\n};\nfunction EditRoleDialog({ isOpen, onClose, onSuccess, role }) {\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.useToast)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: '',\n        color: '#99AAB5',\n        permissions: [],\n        hoist: false,\n        mentionable: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"EditRoleDialog.useEffect\": ()=>{\n            if (role) {\n                setFormData({\n                    name: role.name || '',\n                    color: role.color || '#99AAB5',\n                    permissions: role.permissions || [],\n                    hoist: role.hoist || false,\n                    mentionable: role.mentionable || false\n                });\n            }\n        }\n    }[\"EditRoleDialog.useEffect\"], [\n        role\n    ]);\n    const handleSubmit = async ()=>{\n        setIsLoading(true);\n        try {\n            const response = await fetch(`/api/discord/roles/${role.id}`, {\n                method: 'PATCH',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                throw new Error(error.message || 'Failed to update role');\n            }\n            toast({\n                title: 'Success',\n                description: 'Role updated successfully',\n                status: 'success',\n                duration: 3000\n            });\n            onSuccess();\n            onClose();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to update role',\n                status: 'error',\n                duration: 5000\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handlePermissionChange = (permission)=>{\n        setFormData((prev)=>({\n                ...prev,\n                permissions: prev.permissions.includes(permission) ? prev.permissions.filter((p)=>p !== permission) : [\n                    ...prev.permissions,\n                    permission\n                ]\n            }));\n    };\n    const handleChange = (field, value)=>{\n        setFormData((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Modal, {\n        isOpen: isOpen,\n        onClose: onClose,\n        size: \"xl\",\n        scrollBehavior: \"inside\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalOverlay, {\n                backdropFilter: \"blur(10px)\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalContent, {\n                bg: \"gray.800\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalHeader, {\n                        children: \"Edit Role\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalCloseButton, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                            spacing: 6,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                            children: \"Role Name\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                            placeholder: \"Enter role name\",\n                                            value: formData.name,\n                                            onChange: (e)=>handleChange('name', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormLabel, {\n                                            children: \"Role Color\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Input, {\n                                            type: \"color\",\n                                            value: formData.color,\n                                            onChange: (e)=>handleChange('color', e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.FormControl, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                        spacing: 4,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Checkbox, {\n                                                isChecked: formData.hoist,\n                                                onChange: (e)=>handleChange('hoist', e.target.checked),\n                                                children: \"Display role separately\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Checkbox, {\n                                                isChecked: formData.mentionable,\n                                                onChange: (e)=>handleChange('mentionable', e.target.checked),\n                                                children: \"Allow anyone to @mention\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Divider, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    fontSize: \"lg\",\n                                    fontWeight: \"bold\",\n                                    alignSelf: \"flex-start\",\n                                    children: \"Permissions\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 13\n                                }, this),\n                                Object.entries(PERMISSION_GROUPS).map(([groupName, group])=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                        w: \"full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                mb: 2,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                        as: group.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                        lineNumber: 219,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                        fontWeight: \"semibold\",\n                                                        children: groupName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.SimpleGrid, {\n                                                columns: 2,\n                                                spacing: 2,\n                                                children: group.permissions.map((permission)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Checkbox, {\n                                                        isChecked: formData.permissions.includes(permission),\n                                                        onChange: ()=>handlePermissionChange(permission),\n                                                        children: permission.split('_').map((word)=>word.charAt(0) + word.slice(1).toLowerCase()).join(' ')\n                                                    }, permission, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, groupName, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 15\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.ModalFooter, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                mr: 3,\n                                onClick: onClose,\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                colorScheme: \"blue\",\n                                onClick: handleSubmit,\n                                isLoading: isLoading,\n                                children: \"Save Changes\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                        lineNumber: 239,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\EditRoleDialog.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/EditRoleDialog.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!****************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \****************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpTWVzc2FnZVNxdWFyZSxGaVNoaWVsZCxGaVVzZXJzLEZpVm9sdW1lMiE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiMessageSquare,FiShield,FiUsers,FiVolume2!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ })

};
;