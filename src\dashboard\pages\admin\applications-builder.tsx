import { useState, useEffect } from 'react';
import {
  <PERSON>, VStack, HStack, Heading, Text, Button, Card, CardBody, Badge, useToast,
  Tabs, TabList, TabPanels, Tab, TabPanel, SimpleGrid, Icon, Divider,
  Modal, ModalOverlay, ModalContent, ModalHeader, ModalCloseButton, ModalBody, ModalFooter,
  useDisclosure, Input, Textarea, Select, Switch, FormControl, FormLabel,
  Alert, AlertIcon, Flex, Spacer, IconButton, Menu, MenuButton, MenuList, MenuItem
} from '@chakra-ui/react';
import { 
  FaPlus, FaEdit, FaTrash, FaCog, FaEye, FaClipboardList, FaPalette, 
  FaChevronDown, FaWrench, FaLayerGroup, FaRocket
} from 'react-icons/fa';
import Layout from '../../components/Layout';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

// Types for application builder
interface ApplicationType {
  id: string;
  title: string;
  description: string;
  color: string;
  icon: string;
  enabled: boolean;
  questions: Question[];
  settings: ApplicationSettings;
}

interface Question {
  id: string;
  type: 'text' | 'textarea' | 'select' | 'radio' | 'checkbox' | 'number' | 'email';
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[]; // For select, radio, checkbox
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

interface ApplicationSettings {
  allowMultipleSubmissions: boolean;
  requireApproval: boolean;
  autoResponse: boolean;
  notificationChannels: string[];
  openingSchedule?: {
    enabled: boolean;
    startDate: string;
    endDate: string;
  };
}

const QUESTION_TYPES = [
  { value: 'text', label: 'Short Text', icon: FaEdit },
  { value: 'textarea', label: 'Long Text', icon: FaClipboardList },
  { value: 'select', label: 'Dropdown', icon: FaChevronDown },
  { value: 'radio', label: 'Multiple Choice', icon: FaLayerGroup },
  { value: 'checkbox', label: 'Checkboxes', icon: FaLayerGroup },
  { value: 'number', label: 'Number', icon: FaWrench },
  { value: 'email', label: 'Email', icon: FaRocket },
];

const COLOR_SCHEMES = [
  'blue', 'green', 'red', 'purple', 'orange', 'pink', 'teal', 'cyan', 'yellow'
];

export default function ApplicationsBuilder() {
  const [applications, setApplications] = useState<ApplicationType[]>([]);
  const [selectedApp, setSelectedApp] = useState<ApplicationType | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState(0);
  
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isPreviewOpen, onOpen: onPreviewOpen, onClose: onPreviewClose } = useDisclosure();
  
  const toast = useToast();
  const { data: session } = useSession();
  const router = useRouter();

  useEffect(() => {
    fetchApplications();
  }, []);

  const fetchApplications = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/admin/applications-builder');
      if (response.ok) {
        const data = await response.json();
        setApplications(data.applications || []);
      }
    } catch (error) {
      console.error('Error fetching applications:', error);
      toast({
        title: 'Error',
        description: 'Failed to load applications',
        status: 'error',
        duration: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createNewApplication = () => {
    const newApp: ApplicationType = {
      id: `app-${Date.now()}`,
      title: 'New Application',
      description: 'Description for new application',
      color: 'blue',
      icon: 'FaClipboardList',
      enabled: false,
      questions: [],
      settings: {
        allowMultipleSubmissions: false,
        requireApproval: true,
        autoResponse: false,
        notificationChannels: [],
      }
    };
    console.log('Creating new application:', newApp);
    setSelectedApp(newApp);
    onOpen();
  };

  const createFromTemplate = (template: ApplicationType) => {
    const newApp: ApplicationType = {
      ...template,
      id: `app-${Date.now()}`,
      enabled: false, // Start as disabled
    };
    setSelectedApp(newApp);
    onOpen();
  };

  const editApplication = (app: ApplicationType) => {
    setSelectedApp(app);
    onOpen();
  };

  const deleteApplication = async (appId: string) => {
    try {
      const response = await fetch(`/api/admin/applications-builder/${appId}`, {
        method: 'DELETE',
      });
      
      if (response.ok) {
        setApplications(prev => prev.filter(app => app.id !== appId));
        toast({
          title: 'Success',
          description: 'Application deleted successfully',
          status: 'success',
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('Error deleting application:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete application',
        status: 'error',
        duration: 3000,
      });
    }
  };

  const saveApplication = async (application: ApplicationType) => {
    setIsSaving(true);
    try {
      console.log('Saving application:', application);
      
      const response = await fetch('/api/admin/applications-builder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(application),
      });
      
      console.log('Save response status:', response.status);
      
      if (response.ok) {
        const result = await response.json();
        console.log('Save result:', result);
        await fetchApplications();
        onClose();
        toast({
          title: 'Success',
          description: 'Application saved successfully',
          status: 'success',
          duration: 3000,
        });
      } else {
        const errorData = await response.json();
        console.error('Save error response:', errorData);
        throw new Error(errorData.error || 'Failed to save application');
      }
    } catch (error) {
      console.error('Error saving application:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to save application',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsSaving(false);
    }
  };

  const toggleApplicationStatus = async (appId: string, enabled: boolean) => {
    try {
      const app = applications.find(a => a.id === appId);
      if (!app) {
        console.error('Application not found:', appId);
        return;
      }

      console.log('Toggling application status:', appId, 'to', enabled);
      const updatedApp = { ...app, enabled };
      
      const response = await fetch('/api/admin/applications-builder', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedApp),
      });
      
      console.log('Toggle response status:', response.status);
      
      if (response.ok) {
        await fetchApplications();
        toast({
          title: 'Success',
          description: `Application ${enabled ? 'activated' : 'deactivated'} successfully`,
          status: 'success',
          duration: 3000,
        });
      } else {
        const errorData = await response.json();
        console.error('Toggle error response:', errorData);
        throw new Error(errorData.error || 'Failed to update application status');
      }
    } catch (error) {
      console.error('Error toggling application status:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update application status',
        status: 'error',
        duration: 3000,
      });
    }
  };

  return (
    <Layout>
      <Box p={8}>
        <VStack align="stretch" spacing={6}>
          <HStack justify="space-between">
            <VStack align="start" spacing={1}>
              <Heading size="lg">Applications Builder</Heading>
              <Text color="gray.600" _dark={{ color: 'gray.300' }}>
                Create and manage custom application forms for your server
              </Text>
            </VStack>
            <Button
              colorScheme="blue"
              leftIcon={<FaPlus />}
              onClick={createNewApplication}
            >
              Create Application
            </Button>
          </HStack>

          <Tabs index={activeTab} onChange={setActiveTab} variant="enclosed">
            <TabList>
              <Tab>Applications</Tab>
              <Tab>Templates</Tab>
              <Tab>Settings</Tab>
            </TabList>

            <TabPanels>
              {/* Applications Tab */}
              <TabPanel>
                <VStack align="stretch" spacing={4}>
                  <Alert status="info">
                    <AlertIcon />
                    <Text>
                      Build custom application forms with drag-and-drop ease. Create different types of applications for your server members.
                    </Text>
                  </Alert>
                  
                  <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                    {applications.map(app => (
                      <Card key={app.id} borderTop="4px solid" borderTopColor={`${app.color}.500`}>
                        <CardBody>
                          <VStack align="stretch" spacing={3}>
                            <HStack justify="space-between">
                              <VStack align="start" spacing={1}>
                                <Heading size="md">{app.title}</Heading>
                                <Text fontSize="sm" color="gray.600" _dark={{ color: 'gray.400' }}>
                                  {app.description}
                                </Text>
                              </VStack>
                              <Badge colorScheme={app.enabled ? 'green' : 'gray'}>
                                {app.enabled ? 'Active' : 'Inactive'}
                              </Badge>
                            </HStack>
                            
                            <Text fontSize="sm" color="gray.500">
                              {app.questions.length} questions configured
                            </Text>
                            
                            <HStack spacing={2}>
                              <Button
                                size="sm"
                                colorScheme="blue"
                                leftIcon={<FaEdit />}
                                onClick={() => editApplication(app)}
                              >
                                Edit
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                leftIcon={<FaEye />}
                                onClick={() => {
                                  setSelectedApp(app);
                                  onPreviewOpen();
                                }}
                              >
                                Preview
                              </Button>
                              <Menu>
                                <MenuButton as={IconButton} icon={<FaCog />} size="sm" />
                                <MenuList>
                                  <MenuItem
                                    icon={<FaCog />}
                                    onClick={() => toggleApplicationStatus(app.id, !app.enabled)}
                                  >
                                    {app.enabled ? 'Deactivate' : 'Activate'}
                                  </MenuItem>
                                  <MenuItem
                                    icon={<FaTrash />}
                                    color="red.500"
                                    onClick={() => deleteApplication(app.id)}
                                  >
                                    Delete
                                  </MenuItem>
                                </MenuList>
                              </Menu>
                            </HStack>
                          </VStack>
                        </CardBody>
                      </Card>
                    ))}
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              {/* Templates Tab */}
              <TabPanel>
                <VStack align="stretch" spacing={4}>
                  <Alert status="info">
                    <AlertIcon />
                    <Text>
                      Pre-built templates to get you started quickly. Choose from common application types.
                    </Text>
                  </Alert>
                  
                  <SimpleGrid columns={{ base: 1, md: 2, lg: 3 }} spacing={4}>
                    <TemplateGallery onSelectTemplate={createFromTemplate} />
                  </SimpleGrid>
                </VStack>
              </TabPanel>

              {/* Settings Tab */}
              <TabPanel>
                <VStack align="stretch" spacing={4}>
                  <Alert status="info">
                    <AlertIcon />
                    <Text>
                      Global settings for all applications. Configure defaults and system-wide preferences.
                    </Text>
                  </Alert>
                  
                  <Card>
                    <CardBody>
                      <VStack align="stretch" spacing={4}>
                        <Heading size="md">Global Settings</Heading>
                        <FormControl>
                          <FormLabel>Default Auto-Response</FormLabel>
                          <Switch />
                        </FormControl>
                        <FormControl>
                          <FormLabel>Require Email Verification</FormLabel>
                          <Switch />
                        </FormControl>
                        <FormControl>
                          <FormLabel>Application Cooldown (days)</FormLabel>
                          <Input type="number" defaultValue={30} />
                        </FormControl>
                      </VStack>
                    </CardBody>
                  </Card>
                </VStack>
              </TabPanel>
            </TabPanels>
          </Tabs>
        </VStack>
      </Box>

      {/* Application Editor Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="6xl" scrollBehavior="inside">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <Icon as={FaPalette} color="blue.500" />
              <Text>{selectedApp?.id.startsWith('app-') ? 'Create' : 'Edit'} Application</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedApp && (
              <ApplicationEditor
                application={selectedApp}
                onSave={saveApplication}
                onCancel={onClose}
                isSaving={isSaving}
              />
            )}
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Preview Modal */}
      <Modal isOpen={isPreviewOpen} onClose={onPreviewClose} size="4xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            <HStack>
              <Icon as={FaEye} color="green.500" />
              <Text>Preview: {selectedApp?.title}</Text>
            </HStack>
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {selectedApp && (
              <ApplicationPreview application={selectedApp} />
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onPreviewClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Layout>
  );
}

// Application Editor Component
function ApplicationEditor({ 
  application, 
  onSave, 
  onCancel,
  isSaving = false
}: { 
  application: ApplicationType; 
  onSave: (app: ApplicationType) => void; 
  onCancel: () => void; 
  isSaving?: boolean;
}) {
  const [app, setApp] = useState<ApplicationType>(application);
  const [activeEditorTab, setActiveEditorTab] = useState(0);

  const updateApp = (updates: Partial<ApplicationType>) => {
    setApp(prev => ({ ...prev, ...updates }));
  };

  const addQuestion = () => {
    const newQuestion: Question = {
      id: `q-${Date.now()}`,
      type: 'text',
      label: 'New Question',
      required: false,
    };
    setApp(prev => ({
      ...prev,
      questions: [...prev.questions, newQuestion]
    }));
  };

  const updateQuestion = (questionId: string, updates: Partial<Question>) => {
    setApp(prev => ({
      ...prev,
      questions: prev.questions.map(q => 
        q.id === questionId ? { ...q, ...updates } : q
      )
    }));
  };

  const deleteQuestion = (questionId: string) => {
    setApp(prev => ({
      ...prev,
      questions: prev.questions.filter(q => q.id !== questionId)
    }));
  };

  return (
    <VStack align="stretch" spacing={4}>
      <Tabs index={activeEditorTab} onChange={setActiveEditorTab}>
        <TabList>
          <Tab>Basic Info</Tab>
          <Tab>Questions</Tab>
          <Tab>Settings</Tab>
        </TabList>

        <TabPanels>
          {/* Basic Info Tab */}
          <TabPanel>
            <VStack align="stretch" spacing={4}>
              <FormControl>
                <FormLabel>Application Title</FormLabel>
                <Input
                  value={app.title}
                  onChange={(e) => updateApp({ title: e.target.value })}
                  placeholder="Enter application title"
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>Description</FormLabel>
                <Textarea
                  value={app.description}
                  onChange={(e) => updateApp({ description: e.target.value })}
                  placeholder="Enter application description"
                />
              </FormControl>
              
              <FormControl>
                <FormLabel>Color Scheme</FormLabel>
                <HStack spacing={2}>
                  {COLOR_SCHEMES.map(color => (
                    <Box
                      key={color}
                      w={8}
                      h={8}
                      bg={`${color}.500`}
                      rounded="md"
                      cursor="pointer"
                      border={app.color === color ? "3px solid" : "1px solid"}
                      borderColor={app.color === color ? "white" : "gray.300"}
                      onClick={() => updateApp({ color })}
                    />
                  ))}
                </HStack>
              </FormControl>
              
              <FormControl>
                <FormLabel>Status</FormLabel>
                <HStack>
                  <Switch
                    isChecked={app.enabled}
                    onChange={(e) => updateApp({ enabled: e.target.checked })}
                  />
                  <Text>{app.enabled ? 'Active' : 'Inactive'}</Text>
                </HStack>
              </FormControl>
            </VStack>
          </TabPanel>

          {/* Questions Tab */}
          <TabPanel>
            <VStack align="stretch" spacing={4}>
              <HStack justify="space-between">
                <Text fontWeight="bold">Questions ({app.questions.length})</Text>
                <Button
                  size="sm"
                  colorScheme="blue"
                  leftIcon={<FaPlus />}
                  onClick={addQuestion}
                >
                  Add Question
                </Button>
              </HStack>
              
              {app.questions.map(question => (
                <Card key={question.id} borderLeft="4px solid" borderLeftColor="blue.500">
                  <CardBody>
                    <VStack align="stretch" spacing={3}>
                      <HStack justify="space-between">
                        <FormControl flex={1}>
                          <FormLabel>Question Label</FormLabel>
                          <Input
                            value={question.label}
                            onChange={(e) => updateQuestion(question.id, { label: e.target.value })}
                            placeholder="Enter question label"
                          />
                        </FormControl>
                        <FormControl w="200px">
                          <FormLabel>Type</FormLabel>
                          <Select
                            value={question.type}
                            onChange={(e) => updateQuestion(question.id, { type: e.target.value as Question['type'] })}
                          >
                            {QUESTION_TYPES.map(type => (
                              <option key={type.value} value={type.value}>
                                {type.label}
                              </option>
                            ))}
                          </Select>
                        </FormControl>
                        <IconButton
                          aria-label="Delete question"
                          icon={<FaTrash />}
                          colorScheme="red"
                          size="sm"
                          onClick={() => deleteQuestion(question.id)}
                        />
                      </HStack>
                      
                      <FormControl>
                        <FormLabel>Placeholder</FormLabel>
                        <Input
                          value={question.placeholder || ''}
                          onChange={(e) => updateQuestion(question.id, { placeholder: e.target.value })}
                          placeholder="Enter placeholder text"
                        />
                      </FormControl>
                      
                      <HStack>
                        <FormControl>
                          <HStack>
                            <Switch
                              isChecked={question.required}
                              onChange={(e) => updateQuestion(question.id, { required: e.target.checked })}
                            />
                            <Text>Required</Text>
                          </HStack>
                        </FormControl>
                      </HStack>
                    </VStack>
                  </CardBody>
                </Card>
              ))}
            </VStack>
          </TabPanel>

          {/* Settings Tab */}
          <TabPanel>
            <VStack align="stretch" spacing={4}>
              <FormControl>
                <HStack>
                  <Switch
                    isChecked={app.settings.allowMultipleSubmissions}
                    onChange={(e) => updateApp({ 
                      settings: { ...app.settings, allowMultipleSubmissions: e.target.checked }
                    })}
                  />
                  <Text>Allow Multiple Submissions</Text>
                </HStack>
              </FormControl>
              
              <FormControl>
                <HStack>
                  <Switch
                    isChecked={app.settings.requireApproval}
                    onChange={(e) => updateApp({ 
                      settings: { ...app.settings, requireApproval: e.target.checked }
                    })}
                  />
                  <Text>Require Manual Approval</Text>
                </HStack>
              </FormControl>
              
              <FormControl>
                <HStack>
                  <Switch
                    isChecked={app.settings.autoResponse}
                    onChange={(e) => updateApp({ 
                      settings: { ...app.settings, autoResponse: e.target.checked }
                    })}
                  />
                  <Text>Send Auto-Response</Text>
                </HStack>
              </FormControl>
            </VStack>
          </TabPanel>
        </TabPanels>
      </Tabs>
      
      <HStack justify="flex-end" spacing={4}>
        <Button onClick={onCancel} isDisabled={isSaving}>
          Cancel
        </Button>
        <Button 
          colorScheme="blue" 
          onClick={() => onSave(app)}
          isLoading={isSaving}
          loadingText="Saving..."
        >
          Save Application
        </Button>
      </HStack>
    </VStack>
  );
}

// Template Gallery Component
function TemplateGallery({ onSelectTemplate }: { onSelectTemplate: (template: ApplicationType) => void }) {
  const templates: ApplicationType[] = [
    {
      id: 'template-moderator',
      title: 'Moderator Application',
      description: 'Standard moderator application with experience and scenario questions',
      color: 'blue',
      icon: 'FaUserShield',
      enabled: false,
      questions: [
        { id: 'q1', type: 'text', label: 'What is your Discord username?', required: true },
        { id: 'q2', type: 'number', label: 'How old are you?', required: true },
        { id: 'q3', type: 'text', label: 'What timezone are you in?', required: true },
        { id: 'q4', type: 'number', label: 'How many hours per week can you dedicate to moderation?', required: true },
        { id: 'q5', type: 'textarea', label: 'Why do you want to be a moderator?', required: true },
        { id: 'q6', type: 'textarea', label: 'Do you have any previous moderation experience?', required: false },
        { id: 'q7', type: 'textarea', label: 'How would you handle a heated argument between two members?', required: true },
      ],
      settings: { allowMultipleSubmissions: false, requireApproval: true, autoResponse: true, notificationChannels: [] }
    },
    {
      id: 'template-developer',
      title: 'Developer Application',
      description: 'Technical application for developer positions',
      color: 'green',
      icon: 'FaCode',
      enabled: false,
      questions: [
        { id: 'q1', type: 'text', label: 'Full Name', required: true },
        { id: 'q2', type: 'email', label: 'Email Address', required: true },
        { id: 'q3', type: 'textarea', label: 'Tell us about your programming experience', required: true },
        { id: 'q4', type: 'select', label: 'Primary Programming Language', required: true, options: ['JavaScript', 'Python', 'Java', 'C#', 'Go', 'Other'] },
        { id: 'q5', type: 'textarea', label: 'Describe a challenging project you worked on', required: true },
        { id: 'q6', type: 'text', label: 'GitHub/Portfolio URL', required: false },
        { id: 'q7', type: 'radio', label: 'Are you available for full-time work?', required: true, options: ['Yes', 'No', 'Part-time only'] },
      ],
      settings: { allowMultipleSubmissions: true, requireApproval: true, autoResponse: true, notificationChannels: [] }
    },
    {
      id: 'template-event-host',
      title: 'Event Host Application',
      description: 'Application for community event organizers',
      color: 'purple',
      icon: 'FaCalendar',
      enabled: false,
      questions: [
        { id: 'q1', type: 'text', label: 'Discord Username', required: true },
        { id: 'q2', type: 'text', label: 'Preferred Name', required: true },
        { id: 'q3', type: 'textarea', label: 'What types of events would you like to host?', required: true },
        { id: 'q4', type: 'textarea', label: 'Do you have experience organizing events?', required: false },
        { id: 'q5', type: 'select', label: 'How often would you like to host events?', required: true, options: ['Weekly', 'Bi-weekly', 'Monthly', 'As needed'] },
        { id: 'q6', type: 'textarea', label: 'Describe an event idea you have', required: true },
      ],
      settings: { allowMultipleSubmissions: false, requireApproval: true, autoResponse: true, notificationChannels: [] }
    },
    {
      id: 'template-support',
      title: 'Support Team Application',
      description: 'Customer support and help desk application',
      color: 'orange',
      icon: 'FaHeadset',
      enabled: false,
      questions: [
        { id: 'q1', type: 'text', label: 'Discord Username', required: true },
        { id: 'q2', type: 'text', label: 'Age', required: true },
        { id: 'q3', type: 'text', label: 'Timezone', required: true },
        { id: 'q4', type: 'textarea', label: 'Why do you want to join the support team?', required: true },
        { id: 'q5', type: 'checkbox', label: 'Which areas can you help with?', required: true, options: ['Technical Issues', 'Account Problems', 'General Questions', 'Bug Reports', 'Feature Requests'] },
        { id: 'q6', type: 'textarea', label: 'How would you help a frustrated user?', required: true },
      ],
      settings: { allowMultipleSubmissions: false, requireApproval: true, autoResponse: true, notificationChannels: [] }
    },
    {
      id: 'template-content',
      title: 'Content Creator Application',
      description: 'Application for content creators and influencers',
      color: 'pink',
      icon: 'FaVideo',
      enabled: false,
      questions: [
        { id: 'q1', type: 'text', label: 'Creator Name/Handle', required: true },
        { id: 'q2', type: 'email', label: 'Contact Email', required: true },
        { id: 'q3', type: 'select', label: 'Primary Content Platform', required: true, options: ['YouTube', 'Twitch', 'TikTok', 'Instagram', 'Twitter', 'Other'] },
        { id: 'q4', type: 'text', label: 'Channel/Profile URL', required: true },
        { id: 'q5', type: 'textarea', label: 'What type of content do you create?', required: true },
        { id: 'q6', type: 'number', label: 'How many followers/subscribers do you have?', required: false },
        { id: 'q7', type: 'textarea', label: 'How would you promote our community?', required: true },
      ],
      settings: { allowMultipleSubmissions: true, requireApproval: true, autoResponse: true, notificationChannels: [] }
    },
    {
      id: 'template-beta',
      title: 'Beta Tester Application',
      description: 'Application for beta testing programs',
      color: 'teal',
      icon: 'FaFlask',
      enabled: false,
      questions: [
        { id: 'q1', type: 'text', label: 'Discord Username', required: true },
        { id: 'q2', type: 'textarea', label: 'What interests you about beta testing?', required: true },
        { id: 'q3', type: 'textarea', label: 'Do you have experience finding and reporting bugs?', required: false },
        { id: 'q4', type: 'select', label: 'How much time can you dedicate to testing?', required: true, options: ['1-2 hours/week', '3-5 hours/week', '6-10 hours/week', '10+ hours/week'] },
        { id: 'q5', type: 'checkbox', label: 'Which platforms do you have access to?', required: true, options: ['Windows', 'Mac', 'Linux', 'iOS', 'Android', 'Web Browser'] },
      ],
      settings: { allowMultipleSubmissions: false, requireApproval: true, autoResponse: true, notificationChannels: [] }
    }
  ];

  return (
    <>
      {templates.map(template => (
        <Card key={template.id} borderTop="4px solid" borderTopColor={`${template.color}.500`}>
          <CardBody>
            <VStack align="stretch" spacing={3}>
              <VStack align="start" spacing={1}>
                <Heading size="md">{template.title}</Heading>
                <Text fontSize="sm" color="gray.600" _dark={{ color: 'gray.400' }}>
                  {template.description}
                </Text>
              </VStack>
              
              <Text fontSize="sm" color="gray.500">
                {template.questions.length} pre-configured questions
              </Text>
              
              <Button
                size="sm"
                colorScheme={template.color}
                onClick={() => onSelectTemplate(template)}
                leftIcon={<FaRocket />}
              >
                Use Template
              </Button>
            </VStack>
          </CardBody>
        </Card>
      ))}
    </>
  );
}

// Application Preview Component
function ApplicationPreview({ application }: { application: ApplicationType }) {
  return (
    <VStack align="stretch" spacing={4}>
      <VStack align="center" spacing={2}>
        <Heading size="lg">{application.title}</Heading>
        <Text color="gray.600" _dark={{ color: 'gray.400' }}>
          {application.description}
        </Text>
      </VStack>
      
      <Divider />
      
      <VStack align="stretch" spacing={4}>
        {application.questions.map(question => (
          <FormControl key={question.id} isRequired={question.required}>
            <FormLabel>{question.label}</FormLabel>
            {question.type === 'text' && (
              <Input placeholder={question.placeholder} />
            )}
            {question.type === 'textarea' && (
              <Textarea placeholder={question.placeholder} />
            )}
            {question.type === 'select' && (
              <Select placeholder={question.placeholder}>
                {question.options?.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </Select>
            )}
            {question.type === 'number' && (
              <Input type="number" placeholder={question.placeholder} />
            )}
            {question.type === 'email' && (
              <Input type="email" placeholder={question.placeholder} />
            )}
          </FormControl>
        ))}
      </VStack>
      
      <Button colorScheme={application.color} size="lg" isDisabled>
        Submit Application (Preview)
      </Button>
    </VStack>
  );
} 