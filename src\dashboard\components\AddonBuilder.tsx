import React, { useState, useCallback } from 'react';
import {
  Box,
  Button,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  VStack,
  HStack,
  Heading,
  Text,
  useToast,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Divider,
  IconButton,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Badge,
  Select,
  Checkbox,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  useColorModeValue,
  Spinner,
  Code,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Flex,
  useDisclosure,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalFooter,
  ModalBody,
  ModalCloseButton,
  Center,
  SimpleGrid,
  AlertDialog,
  AlertDialogBody,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogContent,
  AlertDialogOverlay,
} from '@chakra-ui/react';
import { FaPlus, FaTrash, FaCode, FaRocket, FaDownload, FaEye, FaArrowLeft } from 'react-icons/fa';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';

interface AddonConfig {
  name: string;
  version: string;
  description: string;
  author: string;
  commands: CommandConfig[];
  events: EventConfig[];
  settings: {
    embedColor: string;
    [key: string]: any;
  };
  database?: {
    collections: string[];
  };
}

interface CommandConfig {
  name: string;
  description: string;
  type: 'slash' | 'context-menu';
  options?: SlashCommandOption[];
  permissions?: string[];
  cooldown?: number;
  enabled: boolean;
  code: string;
}

interface SlashCommandOption {
  name: string;
  description: string;
  type: 'string' | 'integer' | 'boolean' | 'user' | 'channel' | 'role' | 'mentionable' | 'number' | 'attachment';
  required: boolean;
  choices?: { name: string; value: string | number }[];
}

interface EventConfig {
  name: string;
  once: boolean;
  code: string;
}

const defaultCommand: CommandConfig = {
  name: '',
  description: '',
  type: 'slash',
  permissions: [],
  cooldown: 3000,
  enabled: true,
  code: `// Your command code here
await interaction.reply({
  content: 'Hello from your new command!',
  ephemeral: true
});`
};

const defaultEvent: EventConfig = {
  name: 'ready',
  once: true,
  code: `// Your event code here
console.log('Bot is ready!');`
};

const eventTypes = [
  'ready', 'messageCreate', 'interactionCreate', 'guildMemberAdd', 
  'guildMemberRemove', 'voiceStateUpdate', 'messageReactionAdd', 
  'messageReactionRemove', 'channelCreate', 'channelDelete'
];

const optionTypes = [
  'string', 'integer', 'boolean', 'user', 'channel', 'role', 
  'mentionable', 'number', 'attachment'
];

export default function AddonBuilder() {
  const { data: session } = useSession();
  const router = useRouter();
  const toast = useToast();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const { isOpen: isTemplateOpen, onOpen: onTemplateOpen, onClose: onTemplateClose } = useDisclosure();
  const { isOpen: isGoBackOpen, onOpen: onGoBackOpen, onClose: onGoBackClose } = useDisclosure();
  const [previewCode, setPreviewCode] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [templates, setTemplates] = useState<any[]>([]);
  const [isLoadingTemplates, setIsLoadingTemplates] = useState(false);
  const cancelRef = React.useRef<HTMLButtonElement>(null);

  const [config, setConfig] = useState<AddonConfig>({
    name: '',
    version: '1.0.0',
    description: '',
    author: (session?.user as any)?.name || '',
    commands: [],
    events: [],
    settings: {
      embedColor: '#0099FF'
    }
  });

  const bgColor = useColorModeValue('white', 'gray.800');
  const borderColor = useColorModeValue('gray.200', 'gray.600');

  const updateConfig = useCallback((field: keyof AddonConfig, value: any) => {
    setConfig(prev => ({ ...prev, [field]: value }));
  }, []);

  const updateSettings = useCallback((field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      settings: { ...prev.settings, [field]: value }
    }));
  }, []);

  const addCommand = useCallback(() => {
    setConfig(prev => ({
      ...prev,
      commands: [...prev.commands, { ...defaultCommand }]
    }));
  }, []);

  const updateCommand = useCallback((index: number, field: keyof CommandConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      commands: prev.commands.map((cmd, i) => 
        i === index ? { ...cmd, [field]: value } : cmd
      )
    }));
  }, []);

  const removeCommand = useCallback((index: number) => {
    setConfig(prev => ({
      ...prev,
      commands: prev.commands.filter((_, i) => i !== index)
    }));
  }, []);

  const addEvent = useCallback(() => {
    setConfig(prev => ({
      ...prev,
      events: [...prev.events, { ...defaultEvent }]
    }));
  }, []);

  const updateEvent = useCallback((index: number, field: keyof EventConfig, value: any) => {
    setConfig(prev => ({
      ...prev,
      events: prev.events.map((event, i) => 
        i === index ? { ...event, [field]: value } : event
      )
    }));
  }, []);

  const removeEvent = useCallback((index: number) => {
    setConfig(prev => ({
      ...prev,
      events: prev.events.filter((_, i) => i !== index)
    }));
  }, []);

  const validateConfig = useCallback((): string[] => {
    const errors: string[] = [];

    if (!config.name || config.name.length < 2) {
      errors.push('Addon name must be at least 2 characters long');
    }

    if (!/^[a-z0-9-]+$/.test(config.name)) {
      errors.push('Addon name must contain only lowercase letters, numbers, and hyphens');
    }

    if (!config.version || !/^\d+\.\d+\.\d+$/.test(config.version)) {
      errors.push('Version must be in semver format (e.g., 1.0.0)');
    }

    if (!config.description || config.description.length < 10) {
      errors.push('Description must be at least 10 characters long');
    }

    if (!config.author || config.author.length < 2) {
      errors.push('Author name must be at least 2 characters long');
    }

    if (!config.settings?.embedColor || !/^#[0-9a-fA-F]{6}$/.test(config.settings.embedColor)) {
      errors.push('Embed color must be a valid hex color (e.g., #0099FF)');
    }

    config.commands.forEach((cmd, index) => {
      if (!cmd.name || !/^[a-z0-9-]+$/.test(cmd.name)) {
        errors.push(`Command ${index + 1}: Invalid name format`);
      }

      if (!cmd.description || cmd.description.length < 1) {
        errors.push(`Command ${index + 1}: Description is required`);
      }

      if (!cmd.code || cmd.code.trim().length < 10) {
        errors.push(`Command ${index + 1}: Code implementation is required`);
      }
    });

    config.events.forEach((event, index) => {
      if (!event.name) {
        errors.push(`Event ${index + 1}: Name is required`);
      }

      if (!event.code || event.code.trim().length < 5) {
        errors.push(`Event ${index + 1}: Code implementation is required`);
      }
    });

    return errors;
  }, [config]);

  const handlePreview = useCallback(() => {
    const errors = validateConfig();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    const preview = `// ${config.name} - Generated Addon Preview
// This is a preview of your addon's main structure

export default {
  info: {
    name: "${config.name}",
    version: "${config.version}",
    description: "${config.description}",
    author: "${config.author}"
  },

  commands: ${config.commands.length} command${config.commands.length !== 1 ? 's' : ''},
  events: ${config.events.length} event${config.events.length !== 1 ? 's' : ''},
  
  settings: {
    embedColor: "${config.settings.embedColor}"
  }
};

// Commands: ${config.commands.map(cmd => cmd.name).join(', ') || 'None'}
// Events: ${config.events.map(event => event.name).join(', ') || 'None'}
`;

    setPreviewCode(preview);
    onOpen();
  }, [config, validateConfig, onOpen]);

  const loadTemplates = useCallback(async () => {
    setIsLoadingTemplates(true);
    try {
      const response = await fetch('/api/experimental/addon-builder/templates');
      const data = await response.json();
      
      if (response.ok) {
        setTemplates(data.templates);
      } else {
        throw new Error(data.error || 'Failed to load templates');
      }
    } catch (error) {
      console.error('Error loading templates:', error);
      toast({
        title: 'Error Loading Templates',
        description: error instanceof Error ? error.message : 'Failed to load templates',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    } finally {
      setIsLoadingTemplates(false);
    }
  }, [toast]);

  const handleTemplateSelect = useCallback((template: any) => {
    const templateConfig = { ...template.config };
    templateConfig.author = (session?.user as any)?.name || templateConfig.author;
    
    setConfig(templateConfig);
    onTemplateClose();
    
    toast({
      title: 'Template Loaded!',
      description: `${template.name} template has been applied.`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  }, [session, onTemplateClose, toast]);

  const handleCreate = useCallback(async () => {
    const errors = validateConfig();
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }

    setIsCreating(true);
    setValidationErrors([]);

    try {
      const response = await fetch('/api/experimental/addon-builder/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create addon');
      }

      toast({
        title: 'Addon Created Successfully!',
        description: `${config.name} has been created with ${data.files.length} files.`,
        status: 'success',
        duration: 5000,
        isClosable: true,
      });

      // Reset form
      setConfig({
        name: '',
        version: '1.0.0',
        description: '',
        author: (session?.user as any)?.name || '',
        commands: [],
        events: [],
        settings: {
          embedColor: '#0099FF'
        }
      });

    } catch (error) {
      console.error('Error creating addon:', error);
      toast({
        title: 'Error Creating Addon',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsCreating(false);
    }
  }, [config, validateConfig, toast, session]);

  const handleGoBack = useCallback(() => {
    // Check if user has made any changes
    const hasChanges = config.name.trim() !== '' || 
                      config.description.trim() !== '' || 
                      config.commands.length > 0 || 
                      config.events.length > 0 ||
                      config.author !== ((session?.user as any)?.name || '');

    if (hasChanges) {
      onGoBackOpen();
    } else {
      router.push('/admin/addons');
    }
  }, [config, session, onGoBackOpen, router]);

  const confirmGoBack = useCallback(() => {
    onGoBackClose();
    router.push('/admin/addons');
  }, [onGoBackClose, router]);

  return (
    <Box maxW="4xl" mx="auto" p={6}>
      <VStack spacing={6} align="stretch">
        <Box textAlign="center">
          <Heading size="lg" mb={2}>
            🛠️ Addon Builder
          </Heading>
          <Text color="gray.500" mb={4}>
            Create custom addons for your Discord bot with a visual interface
          </Text>
          <HStack spacing={3} justify="center">
            <Button 
              leftIcon={<FaArrowLeft />} 
              onClick={handleGoBack}
              colorScheme="gray"
              variant="outline"
              size="sm"
            >
              Go Back
            </Button>
            <Button 
              leftIcon={<FaCode />} 
              onClick={() => { loadTemplates(); onTemplateOpen(); }}
              colorScheme="purple"
              variant="outline"
              size="sm"
            >
              Start from Template
            </Button>
          </HStack>
        </Box>

        {validationErrors.length > 0 && (
          <Alert status="error">
            <AlertIcon />
            <Box>
              <AlertTitle>Validation Errors:</AlertTitle>
              <AlertDescription>
                <VStack align="start" spacing={1}>
                  {validationErrors.map((error, index) => (
                    <Text key={index} fontSize="sm">• {error}</Text>
                  ))}
                </VStack>
              </AlertDescription>
            </Box>
          </Alert>
        )}

        <Tabs colorScheme="blue" variant="enclosed">
          <TabList>
            <Tab>Basic Info</Tab>
            <Tab>Commands ({config.commands.length})</Tab>
            <Tab>Events ({config.events.length})</Tab>
            <Tab>Settings</Tab>
          </TabList>

          <TabPanels>
            {/* Basic Info Tab */}
            <TabPanel>
              <VStack spacing={4} align="stretch">
                <FormControl isRequired>
                  <FormLabel>Addon Name</FormLabel>
                  <Input
                    value={config.name}
                    onChange={(e) => updateConfig('name', e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                    placeholder="my-awesome-addon"
                  />
                  <Text fontSize="sm" color="gray.500">
                    Only lowercase letters, numbers, and hyphens allowed
                  </Text>
                </FormControl>

                <HStack spacing={4}>
                  <FormControl isRequired>
                    <FormLabel>Version</FormLabel>
                    <Input
                      value={config.version}
                      onChange={(e) => updateConfig('version', e.target.value)}
                      placeholder="1.0.0"
                    />
                  </FormControl>

                  <FormControl isRequired>
                    <FormLabel>Author</FormLabel>
                    <Input
                      value={config.author}
                      onChange={(e) => updateConfig('author', e.target.value)}
                      placeholder="Your Name"
                    />
                  </FormControl>
                </HStack>

                <FormControl isRequired>
                  <FormLabel>Description</FormLabel>
                  <Textarea
                    value={config.description}
                    onChange={(e) => updateConfig('description', e.target.value)}
                    placeholder="A brief description of what your addon does..."
                    minH="100px"
                  />
                </FormControl>
              </VStack>
            </TabPanel>

            {/* Commands Tab */}
            <TabPanel>
              <VStack spacing={4} align="stretch">
                <HStack justify="space-between">
                  <Heading size="md">Commands</Heading>
                  <Button leftIcon={<FaPlus />} onClick={addCommand} colorScheme="blue">
                    Add Command
                  </Button>
                </HStack>

                {config.commands.length === 0 ? (
                  <Alert status="info">
                    <AlertIcon />
                    <AlertDescription>
                      No commands yet. Add your first command to get started!
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Accordion allowMultiple>
                    {config.commands.map((command, index) => (
                      <AccordionItem key={index}>
                        <AccordionButton>
                          <Box flex="1" textAlign="left">
                            <HStack>
                              <Text fontWeight="bold">
                                {command.name || `Command ${index + 1}`}
                              </Text>
                              <Badge colorScheme={command.enabled ? 'green' : 'gray'}>
                                {command.enabled ? 'Enabled' : 'Disabled'}
                              </Badge>
                            </HStack>
                            <Text fontSize="sm" color="gray.500">
                              {command.description || 'No description'}
                            </Text>
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={4}>
                          <VStack spacing={4} align="stretch">
                            <HStack>
                              <FormControl isRequired>
                                <FormLabel>Command Name</FormLabel>
                                <Input
                                  value={command.name}
                                  onChange={(e) => updateCommand(index, 'name', e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, ''))}
                                  placeholder="ping"
                                />
                              </FormControl>
                              <FormControl>
                                <FormLabel>Cooldown (ms)</FormLabel>
                                <NumberInput
                                  value={command.cooldown}
                                  onChange={(_, value) => updateCommand(index, 'cooldown', value)}
                                  min={1000}
                                  max={300000}
                                >
                                  <NumberInputField />
                                  <NumberInputStepper>
                                    <NumberIncrementStepper />
                                    <NumberDecrementStepper />
                                  </NumberInputStepper>
                                </NumberInput>
                              </FormControl>
                            </HStack>

                            <FormControl isRequired>
                              <FormLabel>Description</FormLabel>
                              <Input
                                value={command.description}
                                onChange={(e) => updateCommand(index, 'description', e.target.value)}
                                placeholder="Shows bot ping"
                              />
                            </FormControl>

                            <FormControl>
                              <FormLabel>Command Code</FormLabel>
                              <Textarea
                                value={command.code}
                                onChange={(e) => updateCommand(index, 'code', e.target.value)}
                                placeholder="// Your command implementation here"
                                minH="200px"
                                fontFamily="mono"
                                fontSize="sm"
                              />
                            </FormControl>

                            <HStack>
                              <Checkbox
                                isChecked={command.enabled}
                                onChange={(e) => updateCommand(index, 'enabled', e.target.checked)}
                              >
                                Enabled
                              </Checkbox>
                              <Button
                                size="sm"
                                variant="outline"
                                colorScheme="red"
                                leftIcon={<FaTrash />}
                                onClick={() => removeCommand(index)}
                              >
                                Remove
                              </Button>
                            </HStack>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>
                    ))}
                  </Accordion>
                )}
              </VStack>
            </TabPanel>

            {/* Events Tab */}
            <TabPanel>
              <VStack spacing={4} align="stretch">
                <HStack justify="space-between">
                  <Heading size="md">Events</Heading>
                  <Button leftIcon={<FaPlus />} onClick={addEvent} colorScheme="blue">
                    Add Event
                  </Button>
                </HStack>

                {config.events.length === 0 ? (
                  <Alert status="info">
                    <AlertIcon />
                    <AlertDescription>
                      No events yet. Add event handlers to respond to Discord events!
                    </AlertDescription>
                  </Alert>
                ) : (
                  <Accordion allowMultiple>
                    {config.events.map((event, index) => (
                      <AccordionItem key={index}>
                        <AccordionButton>
                          <Box flex="1" textAlign="left">
                            <HStack>
                              <Text fontWeight="bold">
                                {event.name || `Event ${index + 1}`}
                              </Text>
                              <Badge colorScheme={event.once ? 'blue' : 'green'}>
                                {event.once ? 'Once' : 'Recurring'}
                              </Badge>
                            </HStack>
                          </Box>
                          <AccordionIcon />
                        </AccordionButton>
                        <AccordionPanel pb={4}>
                          <VStack spacing={4} align="stretch">
                            <HStack>
                              <FormControl isRequired>
                                <FormLabel>Event Name</FormLabel>
                                <Select
                                  value={event.name}
                                  onChange={(e) => updateEvent(index, 'name', e.target.value)}
                                >
                                  <option value="">Select an event</option>
                                  {eventTypes.map(type => (
                                    <option key={type} value={type}>{type}</option>
                                  ))}
                                </Select>
                              </FormControl>
                              <FormControl>
                                <FormLabel>Trigger Type</FormLabel>
                                <Select
                                  value={event.once ? 'once' : 'recurring'}
                                  onChange={(e) => updateEvent(index, 'once', e.target.value === 'once')}
                                >
                                  <option value="once">Once</option>
                                  <option value="recurring">Recurring</option>
                                </Select>
                              </FormControl>
                            </HStack>

                            <FormControl>
                              <FormLabel>Event Code</FormLabel>
                              <Textarea
                                value={event.code}
                                onChange={(e) => updateEvent(index, 'code', e.target.value)}
                                placeholder="// Your event handler code here"
                                minH="200px"
                                fontFamily="mono"
                                fontSize="sm"
                              />
                            </FormControl>

                            <Button
                              size="sm"
                              variant="outline"
                              colorScheme="red"
                              leftIcon={<FaTrash />}
                              onClick={() => removeEvent(index)}
                              alignSelf="flex-start"
                            >
                              Remove Event
                            </Button>
                          </VStack>
                        </AccordionPanel>
                      </AccordionItem>
                    ))}
                  </Accordion>
                )}
              </VStack>
            </TabPanel>

            {/* Settings Tab */}
            <TabPanel>
              <VStack spacing={4} align="stretch">
                <Heading size="md">Settings</Heading>
                
                <FormControl>
                  <FormLabel>Embed Color</FormLabel>
                  <HStack>
                    <Input
                      type="color"
                      value={config.settings.embedColor}
                      onChange={(e) => updateSettings('embedColor', e.target.value)}
                      w="80px"
                    />
                    <Input
                      value={config.settings.embedColor}
                      onChange={(e) => updateSettings('embedColor', e.target.value)}
                      placeholder="#0099FF"
                    />
                  </HStack>
                </FormControl>

                <Alert status="info">
                  <AlertIcon />
                  <AlertDescription>
                    More settings will be added in future updates!
                  </AlertDescription>
                </Alert>
              </VStack>
            </TabPanel>
          </TabPanels>
        </Tabs>

        <Divider />

        <HStack spacing={4} justify="center">
          <Button
            leftIcon={<FaEye />}
            onClick={handlePreview}
            variant="outline"
            colorScheme="blue"
          >
            Preview
          </Button>
          <Button
            leftIcon={<FaRocket />}
            onClick={handleCreate}
            colorScheme="blue"
            size="lg"
            isLoading={isCreating}
            loadingText="Creating..."
          >
            Create Addon
          </Button>
        </HStack>
      </VStack>

      {/* Preview Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Addon Preview</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Code as="pre" p={4} fontSize="sm" overflow="auto" maxH="400px">
              {previewCode}
            </Code>
          </ModalBody>
          <ModalFooter>
            <Button onClick={onClose}>Close</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Template Selection Modal */}
      <Modal isOpen={isTemplateOpen} onClose={onTemplateClose} size="4xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Choose a Template</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            {isLoadingTemplates ? (
              <Center py={8}>
                <VStack spacing={4}>
                  <Spinner size="lg" />
                  <Text>Loading templates...</Text>
                </VStack>
              </Center>
            ) : (
              <VStack spacing={4} align="stretch">
                <Text color="gray.500">
                  Start with a pre-built template to save time and learn best practices.
                </Text>
                <SimpleGrid columns={{ base: 1, md: 2 }} spacing={4}>
                  {templates.map((template) => (
                    <Box
                      key={template.id}
                      bg={bgColor}
                      border="1px"
                      borderColor={borderColor}
                      borderRadius="md"
                      p={4}
                      cursor="pointer"
                      transition="all 0.2s"
                      _hover={{
                        borderColor: 'blue.500',
                        transform: 'translateY(-2px)',
                        boxShadow: 'md'
                      }}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <VStack align="start" spacing={3}>
                        <HStack justify="space-between" w="full">
                          <Heading size="sm">{template.name}</Heading>
                          <Badge colorScheme="blue" size="sm">
                            {template.category}
                          </Badge>
                        </HStack>
                        <Text fontSize="sm" color="gray.500">
                          {template.description}
                        </Text>
                        <HStack spacing={2}>
                          <Badge variant="outline" size="xs">
                            {template.config.commands.length} commands
                          </Badge>
                          <Badge variant="outline" size="xs">
                            {template.config.events.length} events
                          </Badge>
                        </HStack>
                      </VStack>
                    </Box>
                  ))}
                </SimpleGrid>
              </VStack>
            )}
          </ModalBody>
          <ModalFooter>
            <Button onClick={onTemplateClose}>Cancel</Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Go Back Confirmation Dialog */}
      <AlertDialog
        isOpen={isGoBackOpen}
        leastDestructiveRef={cancelRef}
        onClose={onGoBackClose}
        isCentered
      >
        <AlertDialogOverlay>
          <AlertDialogContent>
            <AlertDialogHeader fontSize="lg" fontWeight="bold">
              Leave Addon Builder?
            </AlertDialogHeader>

            <AlertDialogBody>
              You have unsaved changes to your addon. Are you sure you want to go back? 
              All your work will be lost.
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={onGoBackClose}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={confirmGoBack} ml={3}>
                Yes, Go Back
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
} 