import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';

const DEVELOPER_ID = '933023999770918932';

interface FlowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  thumbnail: string;
  nodes: any[];
  edges: any[];
}

const templates: FlowTemplate[] = [
  {
    id: 'ping-command',
    name: 'Ping Command',
    description: 'Simple ping command that responds with latency',
    category: 'Basic',
    thumbnail: '🏓',
    nodes: [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 300, y: 50 },
        data: { label: 'Start' }
      },
      {
        id: 'command-1',
        type: 'command',
        position: { x: 300, y: 180 },
        data: { 
          label: 'Ping Command',
          commandName: 'ping',
          description: 'Shows bot latency and status'
        }
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 300, y: 310 },
        data: {
          label: 'Send Response',
          actionType: 'sendEmbed',
          message: 'Pong! 🏓\nLatency: {ping}ms'
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'trigger-1',
        target: 'command-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e2-3',
        source: 'command-1',
        target: 'action-1',
        type: 'smoothstep',
        animated: true
      }
    ]
  },
  {
    id: 'welcome-system',
    name: 'Welcome System',
    description: 'Welcomes new members with a message',
    category: 'Events',
    thumbnail: '👋',
    nodes: [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 300, y: 50 },
        data: { label: 'Start' }
      },
      {
        id: 'event-1',
        type: 'event',
        position: { x: 300, y: 180 },
        data: {
          label: 'Member Joined',
          eventType: 'guildMemberAdd'
        }
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 300, y: 310 },
        data: {
          label: 'Welcome Message',
          actionType: 'sendMessage',
          message: 'Welcome to the server, {user}! 🎉',
          channel: 'welcome'
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'trigger-1',
        target: 'event-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e2-3',
        source: 'event-1',
        target: 'action-1',
        type: 'smoothstep',
        animated: true
      }
    ]
  },
  {
    id: 'moderation-kick',
    name: 'Moderation Kick',
    description: 'Kick command with permission check',
    category: 'Moderation',
    thumbnail: '🥾',
    nodes: [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 300, y: 50 },
        data: { label: 'Start' }
      },
      {
        id: 'command-1',
        type: 'command',
        position: { x: 300, y: 180 },
        data: {
          label: 'Kick Command',
          commandName: 'kick',
          description: 'Kick a member from the server'
        }
      },
      {
        id: 'condition-1',
        type: 'condition',
        position: { x: 300, y: 310 },
        data: {
          label: 'Permission Check',
          conditionType: 'userPermission',
          operator: 'equals',
          value: 'KICK_MEMBERS'
        }
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 150, y: 450 },
        data: {
          label: 'Kick User',
          actionType: 'kickUser',
          message: 'User {user} has been kicked from the server.'
        }
      },
      {
        id: 'action-2',
        type: 'action',
        position: { x: 450, y: 450 },
        data: {
          label: 'No Permission',
          actionType: 'sendMessage',
          message: 'You do not have permission to kick members.'
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'trigger-1',
        target: 'command-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e2-3',
        source: 'command-1',
        target: 'condition-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e3-4',
        source: 'condition-1',
        target: 'action-1',
        sourceHandle: 'true',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e3-5',
        source: 'condition-1',
        target: 'action-2',
        sourceHandle: 'false',
        type: 'smoothstep',
        animated: true
      }
    ]
  },
  {
    id: 'reaction-roles',
    name: 'Reaction Roles',
    description: 'Give roles based on message reactions',
    category: 'Utility',
    thumbnail: '🎭',
    nodes: [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 300, y: 50 },
        data: { label: 'Start' }
      },
      {
        id: 'event-1',
        type: 'event',
        position: { x: 300, y: 180 },
        data: {
          label: 'Reaction Added',
          eventType: 'messageReactionAdd'
        }
      },
      {
        id: 'condition-1',
        type: 'condition',
        position: { x: 300, y: 310 },
        data: {
          label: 'Check Emoji',
          conditionType: 'reactionEmoji',
          operator: 'equals',
          value: '✅'
        }
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 150, y: 450 },
        data: {
          label: 'Add Role',
          actionType: 'addRole',
          role: 'Member',
          message: 'Added {role} role to {user}!'
        }
      },
      {
        id: 'action-2',
        type: 'action',
        position: { x: 450, y: 450 },
        data: {
          label: 'Wrong Reaction',
          actionType: 'sendMessage',
          message: 'That emoji is not configured for reaction roles.'
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'trigger-1',
        target: 'event-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e2-3',
        source: 'event-1',
        target: 'condition-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e3-4',
        source: 'condition-1',
        target: 'action-1',
        sourceHandle: 'true',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e3-5',
        source: 'condition-1',
        target: 'action-2',
        sourceHandle: 'false',
        type: 'smoothstep',
        animated: true
      }
    ]
  },
  {
    id: 'auto-moderation',
    name: 'Auto Moderation',
    description: 'Automatically delete messages with bad words',
    category: 'Moderation',
    thumbnail: '🛡️',
    nodes: [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 300, y: 50 },
        data: { label: 'Start' }
      },
      {
        id: 'event-1',
        type: 'event',
        position: { x: 300, y: 180 },
        data: {
          label: 'Message Created',
          eventType: 'messageCreate'
        }
      },
      {
        id: 'condition-1',
        type: 'condition',
        position: { x: 300, y: 310 },
        data: {
          label: 'Contains Bad Word',
          conditionType: 'messageContains',
          operator: 'contains',
          value: 'badword'
        }
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 150, y: 450 },
        data: {
          label: 'Delete Message',
          actionType: 'deleteMessage',
          message: 'Message deleted for containing inappropriate content.'
        }
      },
      {
        id: 'action-2',
        type: 'action',
        position: { x: 450, y: 450 },
        data: {
          label: 'Allow Message',
          actionType: 'doNothing',
          message: 'Message is clean - no action needed'
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'trigger-1',
        target: 'event-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e2-3',
        source: 'event-1',
        target: 'condition-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e3-4',
        source: 'condition-1',
        target: 'action-1',
        sourceHandle: 'true',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e3-5',
        source: 'condition-1',
        target: 'action-2',
        sourceHandle: 'false',
        type: 'smoothstep',
        animated: true
      }
    ]
  },
  {
    id: 'custom-embed',
    name: 'Custom Embed Command',
    description: 'Create beautiful embed messages',
    category: 'Utility',
    thumbnail: '📋',
    nodes: [
      {
        id: 'trigger-1',
        type: 'trigger',
        position: { x: 100, y: 100 },
        data: { label: 'Start' }
      },
      {
        id: 'command-1',
        type: 'command',
        position: { x: 100, y: 200 },
        data: {
          label: 'Embed Command',
          commandName: 'embed',
          description: 'Create a custom embed message'
        }
      },
      {
        id: 'action-1',
        type: 'action',
        position: { x: 100, y: 300 },
        data: {
          label: 'Send Embed',
          actionType: 'sendEmbed',
          message: 'This is a custom embed!\n\nYou can customize:\n• Title\n• Description\n• Color\n• Fields\n• And more!'
        }
      }
    ],
    edges: [
      {
        id: 'e1-2',
        source: 'trigger-1',
        target: 'command-1',
        type: 'smoothstep',
        animated: true
      },
      {
        id: 'e2-3',
        source: 'command-1',
        target: 'action-1',
        type: 'smoothstep',
        animated: true
      }
    ]
  }
];

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session || (session.user as any)?.id !== DEVELOPER_ID) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    const { category } = req.query;

    let filteredTemplates = templates;
    
    if (category && typeof category === 'string') {
      filteredTemplates = templates.filter(template => 
        template.category.toLowerCase() === category.toLowerCase()
      );
    }

    return res.status(200).json({
      templates: filteredTemplates,
      categories: Array.from(new Set(templates.map(t => t.category)))
    });
    
  } catch (error) {
    console.error('Error fetching templates:', error);
    return res.status(500).json({ message: 'Internal server error' });
  }
} 