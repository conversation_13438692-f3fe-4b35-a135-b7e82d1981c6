import { FiSettings, FiPackage, FiHelpCircle, FiMonitor, FiServer, FiLock, FiUsers, FiActivity } from 'react-icons/fi';
import { OverviewCardProps } from '../components/OverviewCard';

export interface CardConfig extends Omit<OverviewCardProps, 'icon'> {
  id: string;
  icon: any;
  requiredRole?: 'user' | 'admin' | 'moderator';
}

export const CARD_CONFIGS: CardConfig[] = [
  {
    id: 'overview',
    title: 'Overview',
    description: 'View server statistics and general information.',
    icon: FiActivity,
    href: '/overview',
    color: 'blue',
    gradient: {
      from: 'rgba(49, 130, 206, 0.4)',
      to: 'rgba(49, 130, 206, 0.1)'
    },
    accentColor: '#63B3ED'
  },
  {
    id: 'gameservers',
    title: 'Game Servers',
    description: 'Manage and monitor your game servers. View status, add or edit server configurations.',
    icon: FiMonitor,
    href: '/gameservers',
    color: 'green',
    gradient: {
      from: 'rgba(72, 187, 120, 0.4)',
      to: 'rgba(72, 187, 120, 0.1)'
    },
    accentColor: '#68D391'
  },
  {
    id: 'applications',
    title: 'Applications',
    description: 'Review and manage guild applications. Process new members and handle requests.',
    icon: FiPackage,
    href: '/applications',
    color: 'purple',
    gradient: {
      from: 'rgba(159, 122, 234, 0.4)',
      to: 'rgba(159, 122, 234, 0.1)'
    },
    accentColor: '#B794F4'
  },
  {
    id: 'tickets',
    title: 'Support Tickets',
    description: 'Track and manage support tickets. Respond to user inquiries and resolve issues.',
    icon: FiHelpCircle,
    href: '/tickets',
    color: 'orange',
    gradient: {
      from: 'rgba(237, 137, 54, 0.4)',
      to: 'rgba(237, 137, 54, 0.1)'
    },
    accentColor: '#F6AD55'
  },

  {
    id: 'moderation',
    title: 'Moderation',
    description: 'Tools and features for server moderators.',
    icon: FiLock,
    href: '/moderation',
    color: 'teal',
    gradient: {
      from: 'rgba(49, 151, 149, 0.4)',
      to: 'rgba(49, 151, 149, 0.1)'
    },
    accentColor: '#4FD1C5',
    requiredRole: 'moderator'
  },
  {
    id: 'experimental',
    title: 'Experimental Features',
    description: 'Try out new features that are still in development. These may not work as expected.',
    icon: FiSettings,
    href: '#',
    color: 'yellow',
    gradient: {
      from: 'rgba(236, 201, 75, 0.4)',
      to: 'rgba(236, 201, 75, 0.1)'
    },
    accentColor: '#F6E05E',
    experimental: true,
    disabled: true
  }
]; 