import chalk from 'chalk';

export class ColorManager {
  private static colors = ['cyan', 'magenta', 'yellow', 'green', 'blue', 'red'] as const;
  private static colorMap = new Map<string, number>();
  private static nextColorIndex = 0;

  // Initialize specific colors for addons
  static {
    // Pre-assign colors for specific addons
    this.colorMap.set('example', this.colors.indexOf('blue'));
    this.colorMap.set('voice-mistress', this.colors.indexOf('magenta'));
  }

  public static colorize(text: string): string {
    if (!this.colorMap.has(text)) {
      this.colorMap.set(text, this.nextColorIndex++ % this.colors.length);
    }
    
    const colorIndex = this.colorMap.get(text)!;
    const color = this.colors[colorIndex];
    
    switch (color) {
      case 'cyan': return chalk.cyan(text);
      case 'magenta': return chalk.magenta(text);
      case 'yellow': return chalk.yellow(text);
      case 'green': return chalk.green(text);
      case 'blue': return chalk.blue(text);
      case 'red': return chalk.red(text);
      default: return text; // This should never happen due to const array
    }
  }
} 