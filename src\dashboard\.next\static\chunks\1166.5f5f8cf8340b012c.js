"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1166],{11166:(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var o=a(94513);a(94285);var n=a(51961),i=a(77072);let r=a.n(i)()(async()=>{let e=await Promise.all([a.e(392),a.e(8042)]).then(a.bind(a,68042));return await a.e(7991).then(a.t.bind(a,97991,23)),await a.e(490).then(a.t.bind(a,80490,23)),await a.e(7858).then(a.t.bind(a,7858,23)),e},{loadableGenerated:{webpack:()=>[7858]},ssr:!1}),l=e=>{let{value:t,onChange:a,height:i="60vh"}=e;return(0,o.jsx)(n.a,{borderWidth:"1px",borderColor:"purple.600",borderRadius:"md",overflow:"hidden",height:i,sx:{".ace_editor":{fontFamily:"'Fira Code', 'Consolas', 'Monaco', 'monospace' !important",fontSize:"14px !important",lineHeight:"1.6 !important"},".ace_gutter":{background:"#232323"},".ace_scroller":{backgroundColor:"#1e1e1e"}},children:r&&(0,o.jsx)(r,{mode:"yaml",theme:"twilight",onChange:a,value:t,name:"YAML_EDITOR",editorProps:{$blockScrolling:!0},width:"100%",height:"100%",setOptions:{enableBasicAutocompletion:!0,enableLiveAutocompletion:!0,enableSnippets:!0,showLineNumbers:!0,tabSize:2,useWorker:!1,showPrintMargin:!1}})})}}}]);