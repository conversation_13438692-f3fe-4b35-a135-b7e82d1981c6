import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import {
  GuildMember,
  PartialGuildMember,
  EmbedBuilder,
  ColorResolvable,
  AuditLogEvent,
  PermissionsBitField,
  User,
  EmbedFooterData
} from 'discord.js';
import { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';
import { toOrdinal } from './utils/numberUtils.js';
import { NekosResponse, MessageTemplate } from './types.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Add this helper function at the top level, after the interfaces
async function assignRole(
  member: GuildMember,
  roleId: string,
  logger: any,
  attempt: number = 1
): Promise<boolean> {
  try {
    const role = member.guild.roles.cache.get(roleId);
    if (!role) {
      logger.warn(`Auto-role not found: ${roleId}`);
      return false;
    }

    // Check if bot can assign this role
    const me = member.guild.members.me;
    if (!me || me.roles.highest.position <= role.position) {
      logger.warn(`Bot role position too low to assign role: ${role.name}`);
      return false;
    }

    await member.roles.add(role);
    logger.info(`Successfully assigned role "${role.name}" to ${member.user.tag}`);
    return true;
  } catch (error) {
    logger.error(`Error assigning role ${roleId} to ${member.user.tag} (Attempt ${attempt}):`, error instanceof Error ? error.message : 'Unknown error');
    return false;
  }
}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  events: [
    {
      name: 'ready',
      once: true,
      execute: async (client) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        logger.info(`${config.addon.name} addon loaded! Bot ready as ${client.user?.tag}`);
      }
    },

    {
      name: 'guildMemberAdd',
      once: false,
      execute: async (member: GuildMember) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        try {
          const guildId = member.guild.id;
          
          if (!config || !config.welcome?.enabled || !config.welcome?.channelId) {
            return;
          }

          // Get welcome messages from the config
          const welcomeMessages = config.welcome.messages;
          if (!welcomeMessages || !Array.isArray(welcomeMessages) || welcomeMessages.length === 0) {
            return;
          }

          // Get the welcome channel
          const channel = await member.guild.channels.fetch(config.welcome.channelId).catch(() => null);
          if (!channel || !channel.isTextBased()) {
            return;
          }

          // Cross-check guild membership
          if (channel.guild.id !== guildId) {
            return;
          }

          // Select a random message template
          const messageTemplate = welcomeMessages[Math.floor(Math.random() * welcomeMessages.length)] as MessageTemplate;
          if (!messageTemplate || typeof messageTemplate !== 'object') {
            logger.error(`Invalid message template selected`);
            return;
          }
          
          // Get user information with full profile data
          let userTag: string, userName: string, userId: string, userAvatarURL: string, userMention: string, userBannerURL: string | null;
          let user: User | null = null;
          
          try {
            // Try to fetch full user data
            const fetchedUser = await member.client.users.fetch(member.id, { force: true });
            if (!fetchedUser) {
              throw new Error('Failed to fetch user data');
            }
            user = fetchedUser;
            userTag = user.tag;
            userName = user.displayName || user.globalName || user.username || 'Unknown User';
            userId = user.id;
            userAvatarURL = user.displayAvatarURL({ size: 512 });
            userMention = user.toString();
            userBannerURL = user.bannerURL({ size: 1024 }) ?? null;
          } catch (fetchError) {
            // Fallback to member.user if fetch fails
            logger.warn(`Could not fetch full user data: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
            if (member.user) {
              user = member.user;
              userTag = user.tag;
              userName = user.displayName || user.globalName || user.username || 'Unknown User';
              userId = user.id;
              userAvatarURL = user.displayAvatarURL({ size: 512 });
              userMention = user.toString();
              userBannerURL = user.bannerURL({ size: 1024 }) ?? null;
            } else {
              // Last resort fallback if we have no user data at all
              userTag = 'Unknown User#0000';
              userName = 'Unknown User';
              userId = member.id;
              userAvatarURL = 'https://cdn.discordapp.com/embed/avatars/0.png';
              userMention = 'Unknown User';
              userBannerURL = null;
            }
          }

          // Replace placeholders in the message template
          const replacePlaceholders = (text: string): string => {
            if (!text) return '';
            const memberCount = member.guild.memberCount;
            const memberCountOrdinal = toOrdinal(memberCount);
            const now = new Date();
            
            return text
              .replace(/\{user\}/g, userMention)
              .replace(/\{username\}/g, userName)
              .replace(/\{userName\}/g, userName)
              .replace(/\{userTag\}/g, userTag)
              .replace(/\{user\.tag\}/g, userTag)
              .replace(/\{user\.mention\}/g, userMention)
              .replace(/\{userId\}/g, userId)
              .replace(/\{user\.id\}/g, userId)
              .replace(/\{guild\}/g, member.guild.name)
              .replace(/\{guild\.name\}/g, member.guild.name)
              .replace(/\{server\}/g, member.guild.name)
              .replace(/\{memberCount\}/g, memberCountOrdinal)
              .replace(/\{member\.count\}/g, memberCountOrdinal)
              .replace(/\{memberCountNumeric\}/g, memberCount.toString())
              .replace(/\{guildName\}/g, member.guild.name)
              .replace(/\{guildIcon\}/g, member.guild.iconURL() || '')
              .replace(/\{userBanner\}/g, userBannerURL || 'No Banner')
              .replace(/\{UserCreation\}/g, user ? `<t:${Math.floor(user.createdTimestamp / 1000)}:R>` : 'N/A')
              .replace(/\{user-createdAt\}/g, user ? `<t:${Math.floor(user.createdTimestamp / 1000)}:F>` : 'N/A')
              .replace(/\{longTime\}/g, `<t:${Math.floor(now.getTime() / 1000)}:F>`) // Full date and time
              .replace(/\{shortTime\}/g, `<t:${Math.floor(now.getTime() / 1000)}:T>`) // Time only
              .replace(/\{joinDate\}/g, `<t:${Math.floor(member.joinedTimestamp ?? now.getTime() / 1000)}:D>`) // Short date
              .replace(/\{joinTime\}/g, `<t:${Math.floor(member.joinedTimestamp ?? now.getTime() / 1000)}:T>`) // Time only
              .replace(/\{user-joinedAt\}/g, `<t:${Math.floor(member.joinedTimestamp ?? now.getTime() / 1000)}:F>`);
          };

          // Create an embed for the welcome message
          const embed = new EmbedBuilder()
            .setColor((messageTemplate.color as ColorResolvable) || '#4CAF50')
            .setTitle(replacePlaceholders(messageTemplate.title || '🎉 Welcome!'))
            .setDescription(replacePlaceholders(messageTemplate.description || ''))
            .setThumbnail(userAvatarURL);

          // Set the main image - try banner first, then avatar, then nekos.best
          if (userBannerURL) {
            // Use user's banner if available
            embed.setImage(userBannerURL);
          } else {
            try {
              // Fetch a random greeting GIF from nekos.best
              const response = await fetch('https://nekos.best/api/v2/wave');
              const data = await response.json() as NekosResponse;
              if (data?.results?.[0]?.url) {
                embed.setImage(data.results[0].url);
              }
            } catch (error) {
              logger.warn(`Failed to fetch nekos.best GIF: ${error instanceof Error ? error.message : 'Unknown error'}`);
              // If nekos.best fails, use the avatar as the main image
              embed.setImage(userAvatarURL);
            }
          }

          if (messageTemplate.footer) {
            const iconURL = member.guild.iconURL();
            const footerData = {
              text: replacePlaceholders(messageTemplate.footer),
              ...(iconURL ? { iconURL } : {})
            } as EmbedFooterData;
            embed.setFooter(footerData);
          }

          embed.setTimestamp();

          // Send the welcome message
          await channel.send({ embeds: [embed] });
          logger.info(`Sent welcome message for ${userName}`);

          // Handle auto-roles if configured
          if (config.welcome?.autoRole?.enabled && config.welcome.autoRole.roleIds && config.welcome.autoRole.roleIds.length > 0) {
            // Wait for the configured delay if specified
            if (config.welcome.autoRole.delay) {
              await new Promise(resolve => setTimeout(resolve, config.welcome.autoRole.delay));
            }

            let successCount = 0;
            let failureCount = 0;
            const maxAttempts = config.welcome.autoRole.retry?.maxAttempts || 1;
            const delayBetweenAttempts = config.welcome.autoRole.retry?.delayBetweenAttempts || 5000;

            for (const roleId of config.welcome.autoRole.roleIds) {
              if (!roleId) continue; // Skip empty role IDs

              let success = false;
              let attempts = 0;

              while (!success && attempts < maxAttempts) {
                attempts++;
                
                // If this is a retry attempt, wait before trying again
                if (attempts > 1) {
                  await new Promise(resolve => setTimeout(resolve, delayBetweenAttempts));
                  logger.info(`Retrying role assignment for ${roleId} (Attempt ${attempts}/${maxAttempts})`);
                }

                success = await assignRole(member, roleId, logger, attempts);
              }

              if (success) {
                successCount++;
              } else {
                failureCount++;
                logger.warn(`Failed to assign role ${roleId} after ${maxAttempts} attempts`);
              }
            }

            if (successCount > 0) {
              logger.info(`Successfully assigned ${successCount} auto-role${successCount !== 1 ? 's' : ''} to ${member.user.tag}`);
            }
            if (failureCount > 0) {
              logger.warn(`Failed to assign ${failureCount} auto-role${failureCount !== 1 ? 's' : ''} to ${member.user.tag}`);
            }
          }

        } catch (error) {
          logger.error(`Error in guildMemberAdd:`, error instanceof Error ? error.message : 'Unknown error');
        }
      }
    },

    {
      name: 'guildMemberRemove',
      once: false,
      execute: async (member: GuildMember | PartialGuildMember) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        try {
          // Check if the member is partial (not fully cached)
          if (member.partial) {
            try {
              // Try to fetch the complete member data
              member = await member.fetch();
            } catch (fetchError) {
              // If we can't fetch the member, log it but continue with what we have
              logger.warn(`Could not fetch partial member: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
            }
          }

          const guildId = member.guild.id;
          
          if (!config || !config.goodbye?.enabled || !config.goodbye?.channelId) {
            return;
          }

          // --- Audit Log Check for Kick/Ban Status ---
          let kickStatus = 'Left'; // Default status
          let kickReason = ''; // Store the reason if available
          const me = member.guild.members.me;

          if (me?.permissions.has(PermissionsBitField.Flags.ViewAuditLog)) {
            try {
              // Wait a moment for the audit log to be written
              await new Promise(resolve => setTimeout(resolve, 1500));

              const fetchedLogs = await member.guild.fetchAuditLogs({
                limit: 5, // Check the last 5 events to be safe
                type: AuditLogEvent.MemberKick // First check for kicks
              });

              const now = Date.now();
              const kickLog = fetchedLogs.entries.find(
                (entry) =>
                  entry.targetId === member.id &&
                  now - entry.createdTimestamp < 10000 // Within the last 10 seconds
              );

              if (kickLog) {
                kickStatus = 'Kicked';
                if (kickLog.reason) kickReason = kickLog.reason;
              } else {
                // If no kick log found, check for bans
                const banLogs = await member.guild.fetchAuditLogs({
                  limit: 5,
                  type: AuditLogEvent.MemberBanAdd
                });

                const banLog = banLogs.entries.find(
                  (entry) =>
                    entry.targetId === member.id &&
                    now - entry.createdTimestamp < 10000
                );

                if (banLog) {
                  kickStatus = 'Banned';
                  if (banLog.reason) kickReason = banLog.reason;
                }
              }
            } catch (error) {
              logger.warn(`Error checking audit logs for kick/ban: ${error instanceof Error ? error.message : 'Unknown error'}`);
              // Silently fail and default to 'Left'
            }
          } else {
            logger.warn(`Missing 'View Audit Log' permission. Cannot determine kick/ban status.`);
          }
          // --- End Audit Log Check ---

          // Get goodbye messages from the config
          const goodbyeMessages = config.goodbye.messages;
          if (!goodbyeMessages || !Array.isArray(goodbyeMessages) || goodbyeMessages.length === 0) {
            return;
          }

          // Get the goodbye channel
          const channel = await member.guild.channels.fetch(config.goodbye.channelId).catch(() => null);
          if (!channel || !channel.isTextBased()) {
            return;
          }

          // Cross-check guild membership
          if (channel.guild.id !== guildId) {
            return;
          }

          // Select a random message template
          const messageTemplate = goodbyeMessages[Math.floor(Math.random() * goodbyeMessages.length)] as MessageTemplate;
          if (!messageTemplate || typeof messageTemplate !== 'object') {
            logger.error(`Invalid message template selected`);
            return;
          }
          
          // Get user information with full profile data
          let userTag: string, userName: string, userId: string, userAvatarURL: string, userMention: string, userBannerURL: string | null;
          let user: User | null = null;

          try {
            // Try to fetch full user data
            const fetchedUser = await member.client.users.fetch(member.id, { force: true });
            if (!fetchedUser) {
              throw new Error('Failed to fetch user data');
            }
            user = fetchedUser;
            userTag = user.tag;
            userName = user.displayName || user.globalName || user.username || 'Unknown User';
            userId = user.id;
            userAvatarURL = user.displayAvatarURL({ size: 512 });
            userMention = user.toString();
            userBannerURL = user.bannerURL({ size: 1024 }) ?? null;
          } catch (fetchError) {
            // Fallback to member.user if fetch fails
            logger.warn(`Could not fetch full user data: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
            if (member.user) {
              user = member.user;
              userTag = user.tag;
              userName = user.displayName || user.globalName || user.username || 'Unknown User';
              userId = user.id;
              userAvatarURL = user.displayAvatarURL({ size: 512 });
              userMention = user.toString();
              userBannerURL = user.bannerURL({ size: 1024 }) ?? null;
            } else {
              // Last resort fallback if we have no user data at all
              userTag = 'Unknown User#0000';
              userName = 'Unknown User';
              userId = member.id;
              userAvatarURL = 'https://cdn.discordapp.com/embed/avatars/0.png';
              userMention = 'Unknown User';
              userBannerURL = null;
            }
          }

          // Replace placeholders in the message template
          const replacePlaceholders = (text: string): string => {
            if (!text) return '';
            const memberCount = member.guild.memberCount;
            const memberCountOrdinal = toOrdinal(memberCount);
            const now = new Date();
            
            // Conditionally create the status string with reason if available
            const statusText = (kickStatus === 'Kicked' || kickStatus === 'Banned') 
              ? `**Status:** ${kickStatus}${kickReason ? `\n**Reason:** ${kickReason}` : ''}` 
              : '';

            return text
              .replace(/\{user\}/g, userMention)
              .replace(/\{username\}/g, userName)
              .replace(/\{userName\}/g, userName)
              .replace(/\{userTag\}/g, userTag)
              .replace(/\{user\.tag\}/g, userTag)
              .replace(/\{user\.mention\}/g, userMention)
              .replace(/\{userId\}/g, userId)
              .replace(/\{user\.id\}/g, userId)
              .replace(/\{guild\}/g, member.guild.name)
              .replace(/\{guild\.name\}/g, member.guild.name)
              .replace(/\{server\}/g, member.guild.name)
              .replace(/\{memberCount\}/g, memberCountOrdinal)
              .replace(/\{member\.count\}/g, memberCountOrdinal)
              .replace(/\{memberCountNumeric\}/g, memberCount.toString())
              .replace(/\{guildName\}/g, member.guild.name)
              .replace(/\{guildIcon\}/g, member.guild.iconURL() || '')
              .replace(/\{userBanner\}/g, userBannerURL || 'No Banner')
              .replace(/\{UserCreation\}/g, user ? `<t:${Math.floor(user.createdTimestamp / 1000)}:R>` : 'N/A')
              .replace(/\{user-createdAt\}/g, user ? `<t:${Math.floor(user.createdTimestamp / 1000)}:F>` : 'N/A')
              .replace(/\{longTime\}/g, `<t:${Math.floor(now.getTime() / 1000)}:F>`) // Full date and time
              .replace(/\{shortTime\}/g, `<t:${Math.floor(now.getTime() / 1000)}:T>`) // Time only
              .replace(/\{leaveDate\}/g, `<t:${Math.floor(now.getTime() / 1000)}:D>`) // Short date
              .replace(/\{leaveTime\}/g, `<t:${Math.floor(now.getTime() / 1000)}:T>`) // Time only
              .replace(/\{user-joinedAt\}/g, member.joinedTimestamp ? `<t:${Math.floor(member.joinedTimestamp / 1000)}:F>` : 'N/A')
              .replace(/\{kickStatus\}/g, statusText);
          };

          // Create an embed for the goodbye message
          const embed = new EmbedBuilder()
            .setColor((messageTemplate.color as ColorResolvable) || '#FF6B6B')
            .setTitle(replacePlaceholders(messageTemplate.title || '👋 Goodbye!'))
            .setDescription(replacePlaceholders(messageTemplate.description || ''))
            .setThumbnail(userAvatarURL);

          // Set the main image - try banner first, then avatar, then nekos.best
          if (userBannerURL) {
            // Use user's banner if available
            embed.setImage(userBannerURL);
          } else {
            try {
              // Fetch a random goodbye GIF from nekos.best
              const response = await fetch('https://nekos.best/api/v2/cry');
              const data = await response.json() as NekosResponse;
              if (data?.results?.[0]?.url) {
                embed.setImage(data.results[0].url);
              }
            } catch (error) {
              logger.warn(`Failed to fetch nekos.best GIF: ${error instanceof Error ? error.message : 'Unknown error'}`);
              // If nekos.best fails, use the avatar as the main image
              embed.setImage(userAvatarURL);
            }
          }

          if (messageTemplate.footer) {
            const iconURL = member.guild.iconURL();
            const footerData = {
              text: replacePlaceholders(messageTemplate.footer),
              ...(iconURL ? { iconURL } : {})
            } as EmbedFooterData;
            embed.setFooter(footerData);
          }

          embed.setTimestamp();

          // Send the goodbye message
          await channel.send({ embeds: [embed] });
          logger.info(`Sent goodbye message for ${userName} (Status: ${kickStatus}${kickReason ? `, Reason: ${kickReason}` : ''})`);

        } catch (error) {
          logger.error(`Error in guildMemberRemove:`, error instanceof Error ? error.message : 'Unknown error');
        }
      }
    }
  ],

  onLoad: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(`Loading ${config.addon.name} addon...`);
    
    // Log configuration
    if (config.logging?.enabled) {
      logger.info(`Addon configuration loaded - Welcome Channel: ${config.welcome?.channelId}, Goodbye Channel: ${config.goodbye?.channelId}`);
    }
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(`Unloading ${config.addon.name} addon...`);
  }
};

export default addon; 