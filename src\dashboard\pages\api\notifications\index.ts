import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { getDb } from '../../../apiHelpers/db';
import { ObjectId } from 'mongodb';

const DEVELOPER_ID = '933023999770918932';

interface Notification {
  id: string;
  userId: string; // Who the notification is for
  type: 'experimental-application' | 'application-status' | 'system' | 'admin';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  actionUrl?: string;
  relatedId?: string; // ID of related object (like application ID)
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user?.id) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const db = await getDb();
  const collection = db.collection('notifications');

  if (req.method === 'GET') {
    try {
      // Get notifications for the current user
      const notifications = await collection
        .find({ userId: session.user.id })
        .sort({ timestamp: -1 })
        .limit(50)
        .toArray();

      return res.status(200).json({ notifications });
    } catch (error) {
      console.error('Error fetching notifications:', error);
      return res.status(500).json({ error: 'Failed to fetch notifications' });
    }
  }

  if (req.method === 'POST') {
    try {
      const { type, title, message, targetUserId, actionUrl, relatedId } = req.body;
      
      if (!type || !title || !message || !targetUserId) {
        return res.status(400).json({ error: 'Missing required fields' });
      }

      const notification: Omit<Notification, 'id'> = {
        userId: targetUserId,
        type,
        title,
        message,
        timestamp: new Date(),
        read: false,
        actionUrl,
        relatedId,
      };

      const result = await collection.insertOne(notification);
      return res.status(200).json({ id: result.insertedId, ...notification });
    } catch (error) {
      console.error('Error creating notification:', error);
      return res.status(500).json({ error: 'Failed to create notification' });
    }
  }

  if (req.method === 'PATCH') {
    try {
      const { notificationIds, markAsRead } = req.body;
      
      if (!Array.isArray(notificationIds)) {
        return res.status(400).json({ error: 'notificationIds must be an array' });
      }

      // Only allow users to mark their own notifications
      await collection.updateMany(
        { 
          _id: { $in: notificationIds.map(id => new ObjectId(id)) },
          userId: session.user.id 
        },
        { $set: { read: markAsRead === true } }
      );

      return res.status(200).json({ success: true });
    } catch (error) {
      console.error('Error updating notifications:', error);
      return res.status(500).json({ error: 'Failed to update notifications' });
    }
  }

  if (req.method === 'DELETE') {
    try {
      const { notificationIds } = req.body;
      
      if (!Array.isArray(notificationIds)) {
        return res.status(400).json({ error: 'notificationIds must be an array' });
      }

      // Only allow users to delete their own notifications
      await collection.deleteMany(
        { 
          _id: { $in: notificationIds.map(id => new ObjectId(id)) },
          userId: session.user.id 
        }
      );

      return res.status(200).json({ success: true });
    } catch (error) {
      console.error('Error deleting notifications:', error);
      return res.status(500).json({ error: 'Failed to delete notifications' });
    }
  }

  return res.status(405).json({ error: 'Method not allowed' });
}

// Helper function to create notifications (can be called from other API endpoints)
export async function createNotification(
  targetUserId: string,
  type: Notification['type'],
  title: string,
  message: string,
  actionUrl?: string,
  relatedId?: string
) {
  try {
    const db = await getDb();
    const collection = db.collection('notifications');

    const notification: Omit<Notification, 'id'> = {
      userId: targetUserId,
      type,
      title,
      message,
      timestamp: new Date(),
      read: false,
      actionUrl,
      relatedId,
    };

    const result = await collection.insertOne(notification);
    return { id: result.insertedId, ...notification };
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
} 