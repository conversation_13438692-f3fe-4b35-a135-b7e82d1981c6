export interface Timezone {
  value: string;
  label: string;
}

export const TIMEZONES: Timezone[] = [
  { value: 'GMT-12:00', label: '(GMT-12:00) International Date Line West' },
  { value: 'GMT-11:00', label: '(GMT-11:00) Midway Island, Samoa' },
  { value: 'GMT-10:00', label: '(GMT-10:00) Hawaii' },
  { value: 'GMT-09:00', label: '(GMT-09:00) Alaska' },
  { value: 'GMT-08:00', label: '(GMT-08:00) Pacific Time (US & Canada)' },
  { value: 'GMT-07:00', label: '(GMT-07:00) Mountain Time (US & Canada)' },
  { value: 'GMT-06:00', label: '(GMT-06:00) Central Time (US & Canada)' },
  { value: 'GMT-05:00', label: '(GMT-05:00) Eastern Time (US & Canada)' },
  { value: 'GMT-04:00', label: '(GMT-04:00) Atlantic Time (Canada)' },
  { value: 'GMT-03:00', label: '(GMT-03:00) Buenos Aires, Georgetown' },
  { value: 'GMT-02:00', label: '(GMT-02:00) Mid-Atlantic' },
  { value: 'GMT-01:00', label: '(GMT-01:00) Azores, Cape Verde Islands' },
  { value: 'GMT+00:00', label: '(GMT+00:00) London, Dublin, Edinburgh' },
  { value: 'GMT+01:00', label: '(GMT+01:00) Paris, Amsterdam, Berlin' },
  { value: 'GMT+02:00', label: '(GMT+02:00) Athens, Istanbul, Helsinki' },
  { value: 'GMT+03:00', label: '(GMT+03:00) Moscow, Baghdad, Kuwait' },
  { value: 'GMT+04:00', label: '(GMT+04:00) Abu Dhabi, Dubai, Baku' },
  { value: 'GMT+05:00', label: '(GMT+05:00) Karachi, Tashkent' },
  { value: 'GMT+06:00', label: '(GMT+06:00) Dhaka, Almaty' },
  { value: 'GMT+07:00', label: '(GMT+07:00) Bangkok, Jakarta' },
  { value: 'GMT+08:00', label: '(GMT+08:00) Beijing, Singapore, Hong Kong' },
  { value: 'GMT+09:00', label: '(GMT+09:00) Tokyo, Seoul, Osaka' },
  { value: 'GMT+10:00', label: '(GMT+10:00) Sydney, Melbourne, Brisbane' },
  { value: 'GMT+11:00', label: '(GMT+11:00) Solomon Islands' },
  { value: 'GMT+12:00', label: '(GMT+12:00) Auckland, Wellington' }
]; 