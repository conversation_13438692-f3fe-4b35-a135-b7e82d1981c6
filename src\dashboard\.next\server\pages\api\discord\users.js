"use strict";(()=>{var e={};e.id=8616,e.ids=[8616],e.modules={224:e=>{e.exports=import("@discordjs/rest")},264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},381:(e,t,r)=>{r.d(t,{_:()=>f});var i=r(2115),n=r.n(i),o=r(9021),s=r.n(o),a=r(3873),d=r.n(a);let u=function(){let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d().resolve(process.cwd(),e)).find(e=>s().existsSync(e));if(!e){let t=d().resolve(__dirname,"../../../config.yml");s().existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");return e}(),l=n().parse(s().readFileSync(u,"utf8")),c="http://***********:3000",m=new URL(l.dashboard?.url||"http://localhost:3000"),p=`${m.protocol}//localhost:${m.port||"3000"}`,f={DISCORD_BOT_TOKEN:l.bot.token,DISCORD_CLIENT_ID:l.bot.clientId,DISCORD_CLIENT_SECRET:l.bot.clientSecret,DISCORD_GUILD_ID:l.bot.guildId,NEXTAUTH_URL:c||l.dashboard?.nextAuthUrl||l.dashboard?.url||p,NEXTAUTH_LOCALHOST_URL:p};l.bot.token,l.bot.clientId,l.bot.clientSecret,l.bot.guildId,l.bot.presence,c||l.dashboard?.url,c||l.dashboard?.nextAuthUrl||l.dashboard?.url,l.dashboard?.admins,l.dashboard?.adminRoleIds,l.dashboard?.session?.secret||l.bot.clientSecret,Object.entries(f).forEach(([e,t])=>{if(!t)throw Error(`Missing required environment variable: ${e}`)})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},3882:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>u});var n=r(4722),o=r(224),s=r(3915),a=r(381),d=e([o,s]);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,n.getSession)({req:e}))return t.status(401).json({error:"Unauthorized"});let r=new o.REST({version:"10"}).setToken(a._.DISCORD_BOT_TOKEN),i=a._.DISCORD_GUILD_ID,d=await r.get(`${s.Routes.guildMembers(i)}?limit=1000`),u=await r.get(s.Routes.guildRoles(i)),l=d.map(e=>({id:e.user.id,tag:`${e.user.username}${"0"!==e.user.discriminator?`#${e.user.discriminator}`:""}`,username:e.user.username,discriminator:e.user.discriminator,avatar:e.user.avatar?`https://cdn.discordapp.com/avatars/${e.user.id}/${e.user.avatar}.${e.user.avatar.startsWith("a_")?"gif":"png"}`:null,roles:e.roles.map(e=>{let t=u.find(t=>t.id===e);return t?{id:t.id,name:t.name,color:t.color?`#${t.color.toString(16).padStart(6,"0")}`:null,position:t.position}:null}).filter(Boolean),joinedAt:e.joined_at,timeoutEnd:e.communication_disabled_until?new Date(e.communication_disabled_until).getTime():null,isTimedOut:e.communication_disabled_until&&new Date(e.communication_disabled_until).getTime()>Date.now(),isAdmin:e.roles.some(e=>{let t=u.find(t=>t.id===e);return t&&(BigInt(t.permissions)&BigInt(8))===BigInt(8)}),isModerator:e.roles.some(e=>{let t=u.find(t=>t.id===e);return t&&(BigInt(t.permissions)&BigInt(2))===BigInt(2)})}));t.status(200).json(l)}catch(e){t.status(500).json({error:"Failed to fetch users",details:e instanceof Error?e.message:"Unknown error"})}}[o,s]=d.then?(await d)():d,i()}catch(e){i(e)}})},3915:e=>{e.exports=import("discord-api-types/v10")},4722:e=>{e.exports=require("next-auth/react")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6688:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>l,default:()=>u,routeModule:()=>c});var n=r(3433),o=r(264),s=r(584),a=r(3882),d=e([a]);a=(d.then?(await d)():d)[0];let u=(0,s.M)(a,"default"),l=(0,s.M)(a,"config"),c=new n.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/discord/users",pathname:"/api/discord/users",bundlePath:"",filename:""},userland:a});i()}catch(e){i(e)}})},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=6688);module.exports=r})();