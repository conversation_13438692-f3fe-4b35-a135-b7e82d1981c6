"use strict";(()=>{var e={};e.id=4822,e.ids=[4822],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},1937:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var o=r(5806),s=r(8525),n=r(9524),i=r(2518),d=r(8580),l=e([n]);n=(l.then?(await l)():l)[0];let{url:p,name:m}=d.dashboardConfig.database,b=null;async function c(){if(b)return b;let e=await i.MongoClient.connect(p);return b=e,e}let g=new n.GameDig;async function u(e,t){if(!await (0,o.getServerSession)(e,t,s.authOptions))return t.status(401).json({error:"Unauthorized"});if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{let e=(await c()).db(m),r=(await e.collection("gameservers").find({}).toArray()).map(e=>({_id:e._id?.toString(),type:e.type,host:e.host,port:e.port,name:e.name,description:e.description,hasPassword:e.hasPassword,password:e.password})),a=await Promise.all(r.map(async e=>{try{let t=await g.query({type:e.type,host:e.host,port:e.port,maxAttempts:1,socketTimeout:2e3,..."sdtd"===e.type?{port:e.port,queryPort:e.port+1}:{}});return{...e,online:!0,players:t.players,maxPlayers:t.maxplayers,map:t.map,name:t.name||e.name,ping:t.ping,lastUpdated:new Date().toISOString()}}catch(r){let t=r instanceof Error?r.message:"Unknown error";return{...e,online:!1,error:t,lastUpdated:new Date().toISOString()}}}));return t.status(200).json(a)}catch(e){return t.status(500).json({error:"Internal server error"})}}a()}catch(e){a(e)}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5634:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>c,default:()=>l,routeModule:()=>u});var o=r(3433),s=r(264),n=r(584),i=r(1937),d=e([i]);i=(d.then?(await d)():d)[0];let l=(0,n.M)(i,"default"),c=(0,n.M)(i,"config"),u=new o.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/gameservers/query",pathname:"/api/gameservers/query",bundlePath:"",filename:""},userland:i});a()}catch(e){a(e)}})},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var a=r(5542),o=r.n(a);let s=require("next-auth/providers/discord");var n=r.n(s),i=r(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,a=t.accessToken||null;e.user.id=r,e.user.accessToken=a;let o=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))o=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();o=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),a=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=o()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var a=r(9021),o=r(2115),s=r.n(o),n=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");i=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")},9524:e=>{e.exports=import("gamedig")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=5634);module.exports=r})();