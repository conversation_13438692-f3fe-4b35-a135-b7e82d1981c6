import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';

// Helper function to locate project root
function locateProjectRoot(): string {
  const attempts = [
    '404-bot',
    '.',
    '..',
    '../..',
    '../../..',
    '../../../..',
  ].map(rel => path.resolve(process.cwd(), rel));
  
  let found = attempts.find(p => {
    try {
      return fs.existsSync(path.join(p, 'addon-reload.signal')) || 
             fs.existsSync(path.join(p, 'config.yml'));
    } catch {
      return false;
    }
  });
  
  if (!found) {
    const dirBased = path.resolve(__dirname, '../../../../../../..');
    if (fs.existsSync(path.join(dirBased, 'config.yml'))) found = dirBased;
  }
  
  if (!found) throw new Error('Project root not found');
  return found;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check admin permission
  const isAdmin = (session.user as any).isAdmin;
  if (!isAdmin) {
    return res.status(403).json({ error: 'Forbidden - Admin access required' });
  }

  try {
    // Find project root and create signal file path
    const projectRoot = locateProjectRoot();
    const reloadSignalPath = path.join(projectRoot, 'addon-reload.signal');
    
    console.log('Writing reload signal to:', reloadSignalPath);
    
    // Create a signal file with timestamp
    const signal = {
      timestamp: new Date().toISOString(),
      requestedBy: session.user?.email || 'dashboard'
    };
    
    // Use synchronous write to ensure file is written before response
    fs.writeFileSync(reloadSignalPath, JSON.stringify(signal, null, 2));
    
    return res.status(200).json({ 
      success: true, 
      message: 'Addon reload signal sent',
      timestamp: signal.timestamp,
      path: reloadSignalPath
    });
  } catch (error: any) {
    console.error('Error reloading addons:', error);
    return res.status(500).json({ 
      error: 'Internal server error', 
      details: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
} 