{"version": 1, "files": ["../../../../webpack-api-runtime.js", "../../../../../../../../package.json", "../../../../../../../../config.yml", "../../../../../../addon-reload.signal", "../../../../../../../addons/example/commands/ping.ts", "../../../../../../../addons/example/config.yml", "../../../../../../../addons/example/index.ts", "../../../../../../../addons/moderation/commands/lockdown.ts", "../../../../../../../addons/moderation/commands/ban.ts", "../../../../../../../addons/moderation/commands/purge.ts", "../../../../../../../addons/moderation/commands/kick.ts", "../../../../../../../addons/moderation/commands/timeout.ts", "../../../../../../../addons/moderation/commands/warn.ts", "../../../../../../../addons/moderation/commands/warnings.ts", "../../../../../../../addons/moderation/commands/slowmode.ts", "../../../../../../../addons/moderation/commands/untimeout.ts", "../../../../../../../addons/moderation/config.yml", "../../../../../../../addons/server-hub/index.ts", "../../../../../../../addons/moderation/index.ts", "../../../../../../../addons/tickets/config.yml", "../../../../../../../addons/server-hub/config.yml", "../../../../../../../addons/tickets/index.ts", "../../../../../../../addons/uwu-hug/config.yml", "../../../../../../../addons/tickets/index WORKING.ts", "../../../../../../../addons/uwu-hug/flow.json", "../../../../../../../addons/uwu-hug/commands/hug.ts", "../../../../../../../addons/voice-mistress/config.yml", "../../../../../../../addons/voice-mistress/index.ts", "../../../../../../../addons/uwu-hug/index.ts", "../../../../../../../addons/voice-mistress/utils/channelUtils.ts", "../../../../../../../addons/voice-mistress/events/buttonHandler.ts", "../../../../../../../addons/welcome-goodbye/index.ts", "../../../../../../../addons/welcome-goodbye/utils/numberUtils.ts", "../../../../../../../addons/welcome-goodbye/config.yml", "../../../../../../../addons/welcome-goodbye/types.ts"]}