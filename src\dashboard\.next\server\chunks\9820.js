"use strict";exports.id=9820,exports.ids=[9820],exports.modules={9820:(e,r,t)=>{t.a(e,async(e,n)=>{try{t.r(r),t.d(r,{default:()=>o});var s=t(8732),a=t(9733),i=t(2015),l=e([a]);a=(l.then?(await l)():l)[0];let c={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function o({isOpen:e,onClose:r,onSuccess:t,channel:n,categories:l}){let o=(0,a.useToast)(),[d,p]=(0,i.useState)(!1),[m,u]=(0,i.useState)({name:"",type:0,topic:"",nsfw:!1,bitrate:64e3,userLimit:0,parent:"",rateLimitPerUser:0}),h=async()=>{p(!0);try{let e=await fetch(`/api/discord/channels/${n.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:m.name,topic:m.topic,nsfw:m.nsfw,bitrate:m.type===c.GUILD_VOICE?m.bitrate:void 0,user_limit:m.type===c.GUILD_VOICE?m.userLimit:void 0,parent_id:m.parent||null,rate_limit_per_user:m.type===c.GUILD_TEXT?m.rateLimitPerUser:void 0})});if(!e.ok){let r=await e.json();throw Error(r.message||"Failed to update channel")}o({title:"Success",description:"Channel updated successfully",status:"success",duration:3e3}),t(),r()}catch(e){o({title:"Error",description:e.message||"Failed to update channel",status:"error",duration:5e3})}finally{p(!1)}},x=(e,r)=>{u(t=>({...t,[e]:r}))};return(0,s.jsxs)(a.Modal,{isOpen:e,onClose:r,size:"xl",children:[(0,s.jsx)(a.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(a.ModalContent,{bg:"gray.800",children:[(0,s.jsx)(a.ModalHeader,{children:"Edit Channel"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{children:(0,s.jsxs)(a.VStack,{spacing:4,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{children:"Channel Name"}),(0,s.jsx)(a.Input,{placeholder:"Enter channel name",value:m.name,onChange:e=>x("name",e.target.value)})]}),m.type===c.GUILD_TEXT&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{children:"Channel Topic"}),(0,s.jsx)(a.Input,{placeholder:"Enter channel topic",value:m.topic,onChange:e=>x("topic",e.target.value)})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{children:"Slowmode (seconds)"}),(0,s.jsxs)(a.NumberInput,{min:0,max:21600,value:m.rateLimitPerUser,onChange:e=>x("rateLimitPerUser",parseInt(e)),children:[(0,s.jsx)(a.NumberInputField,{}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]})]}),(0,s.jsxs)(a.FormControl,{display:"flex",alignItems:"center",children:[(0,s.jsx)(a.FormLabel,{htmlFor:"nsfw",mb:"0",children:"Age-Restricted (NSFW)"}),(0,s.jsx)(a.Switch,{id:"nsfw",isChecked:m.nsfw,onChange:e=>x("nsfw",e.target.checked)})]})]}),m.type===c.GUILD_VOICE&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{children:"Bitrate (kbps)"}),(0,s.jsxs)(a.NumberInput,{min:8,max:96,value:m.bitrate/1e3,onChange:e=>x("bitrate",1e3*parseInt(e)),children:[(0,s.jsx)(a.NumberInputField,{}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{children:"User Limit"}),(0,s.jsxs)(a.NumberInput,{min:0,max:99,value:m.userLimit,onChange:e=>x("userLimit",parseInt(e)),children:[(0,s.jsx)(a.NumberInputField,{}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]})]})]}),m.type!==c.GUILD_CATEGORY&&(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{children:"Parent Category"}),(0,s.jsxs)(a.Select,{value:m.parent,onChange:e=>x("parent",e.target.value),children:[(0,s.jsx)("option",{value:"",children:"None"}),(l||[]).map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]})}),(0,s.jsxs)(a.ModalFooter,{children:[(0,s.jsx)(a.Button,{variant:"ghost",mr:3,onClick:r,children:"Cancel"}),(0,s.jsx)(a.Button,{colorScheme:"blue",onClick:h,isLoading:d,children:"Save Changes"})]})]})]})}n()}catch(e){n(e)}})}};