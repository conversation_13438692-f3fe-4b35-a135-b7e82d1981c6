import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from 'next-auth/react';

interface ChannelData {
  id: string;
  name: string;
  type: string;
  position: number;
}

interface RoleData {
  id: string;
  name: string;
  color: string;
  position: number;
  permissions: string[];
  managed: boolean;
}

interface UserData {
  id: string;
  username: string;
  displayName?: string;
  avatar?: string;
  roles?: string[];
  joinedAt?: string;
  bot: boolean;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getSession({ req });
    if (!session?.user || (session.user as any).id !== '933023999770918932') {
      return res.status(403).json({ error: 'Unauthorized' });
    }

    // Use the existing Discord API endpoints (same pattern as guilds.tsx)
    const baseUrl = `${req.headers['x-forwarded-proto'] || 'http'}://${req.headers.host}`;
    
    const [rolesRes, channelsRes, guildRes, usersRes] = await Promise.all([
      fetch(`${baseUrl}/api/discord/roles`, {
        headers: { Cookie: req.headers.cookie || '' }
      }),
      fetch(`${baseUrl}/api/discord/channels`, {
        headers: { Cookie: req.headers.cookie || '' }
      }),
      fetch(`${baseUrl}/api/discord/guild`, {
        headers: { Cookie: req.headers.cookie || '' }
      }),
      fetch(`${baseUrl}/api/discord/users`, {
        headers: { Cookie: req.headers.cookie || '' }
      }).catch(() => null) // Users endpoint might not exist, so catch the error
    ]);

    if (!rolesRes.ok || !channelsRes.ok || !guildRes.ok) {
      throw new Error('Failed to fetch data from Discord API endpoints');
    }

    const [rolesData, channelsData, guildData] = await Promise.all([
      rolesRes.json(),
      channelsRes.json(),
      guildRes.json(),
    ]);

    // Get users data if endpoint exists
    let usersData = [];
    if (usersRes && usersRes.ok) {
      try {
        usersData = await usersRes.json();
      } catch (error) {
        console.warn('Could not fetch users data:', error);
      }
    }

    // Transform channels data to match expected format
    const channelData = (channelsData as ChannelData[])
      .filter((channel: ChannelData) => channel.type === 'GUILD_TEXT' || channel.type === 'GUILD_VOICE' || channel.type === 'GUILD_CATEGORY')
      .map((channel: ChannelData) => ({
        id: channel.id,
        name: channel.name,
        type: channel.type === 'GUILD_TEXT' ? 'text' : 
              channel.type === 'GUILD_VOICE' ? 'voice' : 'category',
        position: channel.position || 0,
      }))
      .sort((a: any, b: any) => a.position - b.position);

    // Transform roles data to match expected format
    const roleData = (rolesData as RoleData[])
      .filter((role: RoleData) => !role.managed && role.name !== '@everyone')
      .map((role: RoleData) => ({
        id: role.id,
        name: role.name,
        color: role.color,
        position: role.position,
        permissions: role.permissions || [],
      }))
      .sort((a: any, b: any) => b.position - a.position);

    // Transform users data to match expected format (if available)
    const memberData = Array.isArray(usersData) ? (usersData as UserData[])
      .filter((user: UserData) => !user.bot)
      .map((user: UserData) => ({
        id: user.id,
        username: user.username,
        displayName: user.displayName || user.username,
        avatar: user.avatar,
        roles: user.roles || [],
        joinedAt: user.joinedAt,
      }))
      .slice(0, 20) : []; // Limit to 20 for dropdown

    res.status(200).json({
      guild: {
        id: guildData.id,
        name: guildData.name,
        icon: guildData.icon,
        memberCount: guildData.memberCount,
        ownerId: guildData.ownerId,
        createdAt: guildData.createdAt,
      },
      channels: channelData,
      roles: roleData,
      members: memberData,
    });

  } catch (error) {
    console.error('Error fetching guild data:', error);
    res.status(500).json({ 
      error: 'Failed to fetch guild data',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 