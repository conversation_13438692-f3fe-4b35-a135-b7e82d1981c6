import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { dashboardConfig } from '../../../core/config';
import { MongoClient } from 'mongodb';

// Reuse same connection pattern used in other API routes
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

/*
 * GET  -> returns list of activities
 * POST -> replace activities array
 */
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user) return res.status(401).json({ error: 'Unauthorized' });

  try {
    const db = await getDb();
    const collection = db.collection('presence');

    if (req.method === 'GET') {
      const doc = await collection.findOne({ key: 'activities' });
      return res.status(200).json({ activities: doc?.activities || [] });
    }

    if (req.method === 'POST') {
      if (!(session.user as any).isAdmin) {
        return res.status(403).json({ error: 'Forbidden - Admin access required' });
      }
      const { activities, activityRotationInterval } = req.body;
      if (!Array.isArray(activities)) {
        return res.status(400).json({ error: 'Invalid activities' });
      }

      // Validate rotation interval
      const interval = activityRotationInterval || 60;
      if (typeof interval !== 'number' || interval < 10 || interval > 3600) {
        return res.status(400).json({ error: 'Invalid rotation interval (must be 10-3600 seconds)' });
      }

      // basic validation of each activity
      const validTypes = ['PLAYING', 'STREAMING', 'LISTENING', 'WATCHING', 'COMPETING'];
      for (const act of activities) {
        if (typeof act !== 'object' || typeof act.name !== 'string' || !validTypes.includes(act.type)) {
          return res.status(400).json({ error: 'Invalid activity entry' });
        }
      }

      await collection.updateOne(
        { key: 'activities' },
        { $set: { activities, activityRotationInterval: interval, key: 'activities' } },
        { upsert: true }
      );
      return res.status(200).json({ message: 'Activities updated', activities, activityRotationInterval: interval });
    }

    return res.status(405).json({ error: 'Method not allowed' });
  } catch (err) {
    console.error('Presence API error:', err);
    return res.status(500).json({ error: 'Server error', details: (err as Error).message });
  }
}
