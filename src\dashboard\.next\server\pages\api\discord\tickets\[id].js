"use strict";(()=>{var e={};e.id=4159,e.ids=[4159],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,a)=>{e.exports=a(5600)},3873:e=>{e.exports=require("path")},4179:(e,t,a)=>{a.r(t),a.d(t,{config:()=>b,default:()=>f,routeModule:()=>y});var r={};a.r(r),a.d(r,{default:()=>m});var i=a(3433),s=a(264),n=a(584),o=a(5806),d=a(8525),c=a(2518),l=a(8580);let{url:p,name:u}=l.dashboardConfig.database,g=null;async function h(){return g||(g=await c.MongoClient.connect(p))}async function m(e,t){let a,r,i=await (0,o.getServerSession)(e,t,d.authOptions);if(!i)return t.status(401).json({error:"Unauthorized"});let{id:s}=e.query;if(!s||Array.isArray(s))return t.status(400).json({error:"Invalid ticket id"});let{token:n,guildId:p,ticketLogChannelId:g}=l.dashboardConfig.bot;if(!n||!p)return t.status(500).json({error:"Bot configuration missing"});try{a=await h()}catch(e){return t.status(500).json({error:"Database connection failed"})}let m=a.db(u).collection("tickets");try{if(!(r=await m.findOne({_id:new c.ObjectId(s)})))return t.status(404).json({error:"Ticket not found"})}catch(e){return t.status(500).json({error:"Database error"})}let f=i.user.isAdmin,b=r.creatorId===i.user.id;if(!f&&!b)return t.status(403).json({error:"Forbidden"});switch(e.method){case"PATCH":{if("closed"===r.status)return t.status(400).json({error:"Ticket already closed"});try{await fetch(`https://discord.com/api/v10/channels/${r.channelId}`,{method:"PATCH",headers:{Authorization:`Bot ${n}`,"Content-Type":"application/json"},body:JSON.stringify({name:`closed-${r.channelId}`.slice(0,100),permission_overwrites:[{id:p,type:0,deny:"2048"},{id:r.creatorId,type:1,deny:"2048"}]})})}catch(e){}let e=async()=>{let e,t=[];for(;;){let a=`https://discord.com/api/v10/channels/${r.channelId}/messages?limit=100${e?`&before=${e}`:""}`,i=await fetch(a,{headers:{Authorization:`Bot ${n}`}});if(!i.ok)break;let s=await i.json();if(t.push(...s),s.length<100)break;e=s[s.length-1].id}return t.reverse(),t},a=await e(),o=async(e,t)=>{try{let a=await fetch(e),r=await a.arrayBuffer();if(r.byteLength>1048576)return e;let i=Buffer.from(r).toString("base64");return`data:${t};base64,${i}`}catch{return e}},d=async(e,t,a)=>{let r=Array.from(e.matchAll(t));if(0===r.length)return e;let i=await Promise.all(r.map(e=>a(...e))),s=e;for(let e=r.length-1;e>=0;e--){let t=r[e],a=i[e];s=s.substring(0,t.index)+a+s.substring(t.index+t[0].length)}return s},l=async e=>{if(!e.content_type?.startsWith("image/")||e.size>1048576)return e.content_type?.startsWith("image/")?"":`<a href="${e.url}" target="_blank">${e.filename}</a>`;try{let t=await (await fetch(e.url)).arrayBuffer(),a=Buffer.from(t).toString("base64");return`<img class="attach" src="data:${e.content_type};base64,${a}" />`}catch{return`<img class="attach" src="${e.url}" />`}},u=a.map(async e=>{let t=new Date(e.timestamp).toLocaleString(),a=e.author.avatar?`https://cdn.discordapp.com/avatars/${e.author.id}/${e.author.avatar}.png?size=32`:`https://cdn.discordapp.com/embed/avatars/${parseInt(e.author.discriminator)%5}.png`,r=e.content||"",i=r.match(/<a?:[\w]+:\d+>/g);if(i)for(let e of i){let t=e.startsWith("<a:"),a=e.match(/:(\d+)>/);if(a){let i=a[1],s=t?"gif":"png",n=`https://cdn.discordapp.com/emojis/${i}.${s}`;try{let t=await o(n,`image/${s}`);r=r.replace(e,`<img class="emoji" src="${t}" />`)}catch(e){}}}let s=(r=await d(r,/(https?:\/\/tenor\.com\/view\/[^\s]+)/g,async e=>{let t=e.match(/-(\d+)(?:\?.*)?$/);if(!t)return`<a href="${e}" target="_blank">🎬 Tenor GIF</a>`;let a=t[1];try{let e,t=await fetch(`https://g.tenor.com/v1/gifs?ids=${a}&key=LIVDSRZULELA`),r=await t.json();if(!r?.results?.length)throw 0;let i=r.results[0].media_formats||r.results[0].media?.[0];for(let t of["mediumgif","gif","tinygif","nanogif"])if(i?.[t]?.url){e=i[t].url;break}if(!e)throw 0;let s=await o(e,"image/gif");if(s.startsWith("data:"))return`<img class="attach" src="${s}" />`;return`<img class="attach" src="${e}" />`}catch{return`<a href="${e}" target="_blank">🎬 Tenor GIF</a>`}})).match(/https?:\/\/[^\s]+\.(png|jpe?g|gif|webp)/gi);if(s)for(let e of s)try{let t=e.endsWith(".gif")?"image/gif":e.endsWith(".webp")?"image/webp":e.endsWith(".png")?"image/png":"image/jpeg",a=await o(e,t);r=r.replace(e,`<img class="attach" src="${a}" />`)}catch(e){}r=r.replace(/</g,"|||LT|||").replace(/>/g,"|||GT|||").replace(/|||LT|||img/g,"<img").replace(/src="[^"]*"[^>]*|||GT|||/g,e=>e.replace(/|||GT|||/g,">")).replace(/\/|||GT|||/g,"/>").replace(/|||LT|||\/img|||GT|||/g,"</img>").replace(/|||LT|||a href/g,"<a href").replace(/|||LT|||\/a|||GT|||/g,"</a>").replace(/target="_blank"[^>]*|||GT|||/g,e=>e.replace(/|||GT|||/g,">")).replace(/|||LT|||/g,"&lt;").replace(/|||GT|||/g,"&gt;").replace(/\n/g,"<br/>");let n="";return e.attachments?.length>0&&(n=(await Promise.all(e.attachments.map(l))).join("")),`<div class="msg"><img class="avatar" src="${a}"/><div class="bubble"><div class="meta"><span class="name">${e.author.username}${e.author.discriminator?`#${e.author.discriminator}`:""}</span><span class="time">${t}</span></div><div class="content">${r}${n}</div></div></div>`}),h=(await Promise.all(u)).join("\n"),f=`<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${s} Transcript</title>
<style>
*{box-sizing:border-box;margin:0;padding:0}
body{font-family:Segoe UI,Arial,sans-serif;background:#0f172a;color:#e2e8f0;min-height:100vh;display:flex;flex-direction:column;align-items:center;padding:40px 12px;background-image:radial-gradient(circle at 25% 25%,rgba(29,78,216,.4),transparent 60%),radial-gradient(circle at 75% 75%,rgba(60,46,123,.35),transparent 60%)}
h2{color:#60a5fa;margin-bottom:12px;font-weight:600;letter-spacing:.3px;text-shadow:0 1px 2px rgba(0,0,0,.4)}
#wrapper{width:100%;max-width:860px}
.msg{display:flex;gap:12px;margin-bottom:18px;animation:fadeIn .4s ease}
.avatar{width:42px;height:42px;border-radius:50%;flex-shrink:0;box-shadow:0 0 0 2px rgba(255,255,255,.07)}
.bubble{background:rgba(255,255,255,0.06);padding:10px 14px;border-radius:10px;backdrop-filter:blur(14px) saturate(130%);width:fit-content;max-width:90%}
.meta{font-size:12px;color:#94a3b8;margin-bottom:6px;display:flex;flex-wrap:wrap;gap:6px 10px}
.name{color:#82b1ff;font-weight:600}
.content{font-size:14px;line-height:1.5;word-break:break-word}
.emoji{width:20px;height:20px;vertical-align:-4px}
.attach{margin-top:8px;max-width:260px;border-radius:8px;box-shadow:0 2px 6px rgba(0,0,0,.4)}
@keyframes fadeIn{from{opacity:0;transform:translateY(6px)}to{opacity:1;transform:translateY(0)}}
.badge{display:inline-block;padding:4px 10px;border-radius:999px;font-size:12px;font-weight:600;margin-bottom:28px;background:var(--badge-bg);color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4)}
</style></head><body>
<h2>Ticket ${s} Transcript</h2>
<span class="badge" style="--badge-bg:${{support:"#3b82f6","18plus":"#ef4444",other:"#a855f7"}[r.category||"other"]||"#6b7280"}">${r.category||"Support"}</span>
<div id="wrapper">
${h}
</div>
</body></html>`;if(await m.updateOne({_id:new c.ObjectId(s)},{$set:{status:"closed",closedAt:new Date,transcriptHtml:f}}),g)try{await fetch(`https://discord.com/api/v10/channels/${g}/messages`,{method:"POST",headers:{Authorization:`Bot ${n}`,"Content-Type":"application/json"},body:JSON.stringify({content:`✅ Ticket <#${r.channelId}> closed by <@${i.user.id}>`})})}catch{}return t.status(200).json({message:"Ticket closed"})}case"DELETE":if(!f)return t.status(403).json({error:"Admin only"});try{await fetch(`https://discord.com/api/v10/channels/${r.channelId}`,{method:"DELETE",headers:{Authorization:`Bot ${n}`}})}catch(e){}if(await m.deleteOne({_id:new c.ObjectId(s)}),g)try{await fetch(`https://discord.com/api/v10/channels/${g}/messages`,{method:"POST",headers:{Authorization:`Bot ${n}`,"Content-Type":"application/json"},body:JSON.stringify({content:`🗑️ Ticket ${s} deleted by <@${i.user.id}>`})})}catch{}return t.status(200).json({message:"Ticket deleted"});default:return t.setHeader("Allow",["PATCH","DELETE"]),t.status(405).json({error:`Method ${e.method} not allowed`})}}let f=(0,n.M)(r,"default"),b=(0,n.M)(r,"config"),y=new i.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/tickets/[id]",pathname:"/api/discord/tickets/[id]",bundlePath:"",filename:""},userland:r})},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,a)=>{a.r(t),a.d(t,{authOptions:()=>d,default:()=>c});var r=a(5542),i=a.n(r);let s=require("next-auth/providers/discord");var n=a.n(s),o=a(8580);let d={providers:[n()({clientId:o.dashboardConfig.bot.clientId,clientSecret:o.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:a})=>(t&&a&&(e.accessToken=t.access_token||null,e.id=a.id||null),e),async session({session:e,token:t}){if(e?.user){let a=t.id||null,r=t.accessToken||null;e.user.id=a,e.user.accessToken=r;let i=!1;if(a)if((o.dashboardConfig.dashboard.admins||[]).includes(a))i=!0;else{let e=o.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&o.dashboardConfig.bot.token&&o.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${o.dashboardConfig.bot.guildId}/members/${a}`,{headers:{Authorization:`Bot ${o.dashboardConfig.bot.token}`}});if(t.ok){let a=await t.json();i=e.some(e=>a.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let a=new URL(t),r=`${a.protocol}//localhost${a.port?`:${a.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:o.dashboardConfig.dashboard.session.secret||o.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},c=i()(d)},8580:(e,t,a)=>{a.r(t),a.d(t,{dashboardConfig:()=>d,default:()=>c});var r=a(9021),i=a(2115),s=a.n(i),n=a(3873);let o={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");o=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:o.bot.token,clientId:o.bot.clientId,clientSecret:o.bot.clientSecret,guildId:o.bot.guildId,ticketCategoryId:o.bot.ticketCategoryId||null,ticketLogChannelId:o.bot.ticketLogChannelId||null,prefix:o.bot.prefix},dashboard:{admins:o.dashboard?.admins||[],adminRoleIds:o.dashboard?.adminRoleIds||[],session:{secret:o.dashboard?.session?.secret||o.bot.clientSecret}},database:{url:o.database.url,name:o.database.name,options:{maxPoolSize:o.database.options?.maxPoolSize||10,minPoolSize:o.database.options?.minPoolSize||1,maxIdleTimeMS:o.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:o.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:o.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:o.database.options?.connectTimeoutMS||1e4,retryWrites:o.database.options?.retryWrites!==!1,retryReads:o.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let c=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var a=t(t.s=4179);module.exports=a})();