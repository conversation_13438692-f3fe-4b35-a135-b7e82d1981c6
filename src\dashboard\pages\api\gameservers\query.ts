import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { QueryOptions, QueryResult } from 'gamedig';
import { GameDig } from 'gamedig';
import { MongoClient, Document, WithId } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

interface GameServer {
  type: string;
  host: string;
  port: number;
  name?: string;
  description?: string;
  hasPassword?: boolean;
  password?: string;
}

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

// Initialize GameDig instance
const gamedig = new GameDig();

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  let client;
  try {
    client = await connectToDatabase();
    const db = client.db(dbName);
    
    // Get all registered game servers from the database
    const servers = await db.collection('gameservers').find({}).toArray();
    const gameServers = servers.map(doc => ({
      _id: doc._id?.toString(),
      type: doc.type as string,
      host: doc.host as string,
      port: doc.port as number,
      name: doc.name as string | undefined,
      description: doc.description as string | undefined,
      hasPassword: doc.hasPassword as boolean | undefined,
      password: doc.password as string | undefined
    }));
    
    // Query each server for its status
    const serverStatuses = await Promise.all(
      gameServers.map(async (server: GameServer) => {
        try {
          const state = await gamedig.query({
            type: server.type,
            host: server.host,
            port: server.port,
            maxAttempts: 1,
            socketTimeout: 2000,
            ...(server.type === 'sdtd' ? {
              port: server.port,
              queryPort: server.port + 1, // Query port is typically game port + 1
            } : {}),
          } as QueryOptions);

          return {
            ...server,
            online: true,
            players: state.players,
            maxPlayers: state.maxplayers,
            map: state.map,
            name: state.name || server.name,
            ping: state.ping,
            lastUpdated: new Date().toISOString(),
          };
        } catch (error: unknown) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          return {
            ...server,
            online: false,
            error: errorMessage,
            lastUpdated: new Date().toISOString(),
          };
        }
      })
    );

    return res.status(200).json(serverStatuses);
  } catch (error: unknown) {
    console.error('Server query error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 