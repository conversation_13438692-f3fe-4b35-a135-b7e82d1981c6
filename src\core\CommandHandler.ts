import { Client, CommandInteraction, MessageFlags } from 'discord.js';
import { Logger } from './Logger.js';
import { BotInstance } from '../types/index.js';
import * as chokidar from 'chokidar';
import fs from 'fs';
import path from 'path';

export class CommandHandler {
  private bot: BotInstance;
  private logger: any;
  private cooldowns: Map<string, Map<string, number>>;
  private commandStatesCache: Map<string, { enabled: boolean; lastChecked: number }>;
  private commandStateWatcher?: chokidar.FSWatcher;

  constructor(bot: BotInstance) {
    this.bot = bot;
    this.logger = Logger.createLogger();
    
    // Simple cooldown tracking
    this.cooldowns = new Map();
    
    // Cache for command states to reduce database calls
    this.commandStatesCache = new Map();

    // Set up interaction handler
    this.bot.client.on('interactionCreate', async (interaction) => {
      if (!interaction.isCommand()) return;

      const startTime = Date.now();

      const command = this.bot.commands.get(interaction.commandName);
      if (!command) {
        this.logCommand(interaction.commandName, interaction.user.id, Date.now() - startTime, false);
        return;
      }

      try {
        // Check if command is enabled FIRST (before any other checks)
        const isEnabled = await this.isCommandEnabled(interaction.commandId);
        if (!isEnabled) {
          await this.safeReply(interaction, { 
            content: 'This command is currently disabled.', 
            flags: MessageFlags.Ephemeral
          });
          this.logCommand(interaction.commandName, interaction.user.id, Date.now() - startTime, false);
          return;
        }

        // Simple cooldown check
        const cooldownAmount = (command.cooldown || this.bot.config.features.commandCooldown || 3000);
        
        if (!this.cooldowns.has(interaction.commandName)) {
          this.cooldowns.set(interaction.commandName, new Map());
        }

        const now = Date.now();
        const timestamps = this.cooldowns.get(interaction.commandName)!;
        
        if (timestamps.has(interaction.user.id)) {
          const expirationTime = timestamps.get(interaction.user.id)! + cooldownAmount;
          
          if (now < expirationTime) {
            const timeLeft = (expirationTime - now) / 1000;
            await this.safeReply(interaction, {
              content: `⏱️ Please wait ${timeLeft.toFixed(1)} more second(s) before using this command again.`,
              flags: MessageFlags.Ephemeral
            });
            this.logCommand(interaction.commandName, interaction.user.id, Date.now() - startTime, false);
            return;
          }
        }

        // Set cooldown
        timestamps.set(interaction.user.id, now);
        setTimeout(() => timestamps.delete(interaction.user.id), cooldownAmount);

        // Execute command
        await command.execute(interaction, this.bot);
        this.logCommand(interaction.commandName, interaction.user.id, Date.now() - startTime, true);
      } catch (error) {
        this.logger.error(`Error executing command ${interaction.commandName}:`, error);
        this.logCommand(interaction.commandName, interaction.user.id, Date.now() - startTime, false, error);
        
        // Only try to respond if the interaction hasn't been handled
        await this.safeErrorReply(interaction, 'There was an error executing this command!');
      }
    });

    // Clean up old cooldowns and cache every 5 minutes
    setInterval(() => {
      this.cleanupCooldowns();
      this.cleanupCommandStatesCache();
    }, 5 * 60 * 1000);

    // Watch for external signal to clear command cache
    this.setupCommandStateWatcher();
  }

  private setupCommandStateWatcher(): void {
    const signalPath = path.resolve(process.cwd(), 'command-state.signal');
    const dir = path.dirname(signalPath);
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });

    this.logger.info(`Watching for command state signals at: ${signalPath}`);

    this.commandStateWatcher = chokidar.watch(signalPath, {
      persistent: true,
      ignoreInitial: true,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 100,
      },
    });

    const handleSignal = () => {
      try {
        const content = fs.readFileSync(signalPath, 'utf-8');
        const data = JSON.parse(content);
        this.logger.info(`Received command state change signal for ${data.commandId}`);
        this.clearCommandStateCache(data.commandId);

        // Remove the signal file after processing so future updates re-trigger
        if (fs.existsSync(signalPath)) fs.unlinkSync(signalPath);
      } catch (err) {
        this.logger.error('Failed to process command state signal:', err);
      }
    };

    this.commandStateWatcher.on('add', handleSignal);
    this.commandStateWatcher.on('change', handleSignal);
  }

  private async safeReply(interaction: any, options: any): Promise<void> {
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply(options);
      }
    } catch (error) {
      this.logger.error(`Failed to reply to interaction:`, error);
    }
  }

  private async safeErrorReply(interaction: any, content: string): Promise<void> {
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content,
          flags: MessageFlags.Ephemeral
        });
      } else if (interaction.deferred) {
        await interaction.editReply({
          content,
        });
      } else {
        await interaction.followUp({
          content,
          flags: MessageFlags.Ephemeral
        });
      }
    } catch (replyError) {
      this.logger.error(`Failed to send error response:`, replyError);
    }
  }

  private async isCommandEnabled(commandId: string): Promise<boolean> {
    const now = Date.now();
    const cached = this.commandStatesCache.get(commandId);
    
    // Use cached result if it's less than 30 seconds old (reduced for faster response to changes)
    if (cached && (now - cached.lastChecked) < 30 * 1000) {
      return cached.enabled;
    }

    try {
      // Use optimized database method with shorter timeout
      const commandState = await Promise.race([
        this.bot.database.findOne('command_states', { commandId }),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Database timeout')), 500))
      ]);

      const enabled = !commandState || commandState.enabled !== false;
      this.commandStatesCache.set(commandId, { enabled, lastChecked: now });
      return enabled;
    } catch (error) {
      // On error, assume enabled and cache for shorter time
      this.commandStatesCache.set(commandId, { enabled: true, lastChecked: now });
      return true;
    }
  }

  private cleanupCooldowns(): void {
    // Clean up empty cooldown maps
    for (const [commandName, timestamps] of this.cooldowns.entries()) {
      if (timestamps.size === 0) {
        this.cooldowns.delete(commandName);
      }
    }
  }

  private cleanupCommandStatesCache(): void {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10 minutes
    
    for (const [commandId, cache] of this.commandStatesCache.entries()) {
      if (now - cache.lastChecked > maxAge) {
        this.commandStatesCache.delete(commandId);
      }
    }
  }

  public clearCommandStateCache(commandId?: string): void {
    if (commandId) {
      this.commandStatesCache.delete(commandId);
    } else {
      this.commandStatesCache.clear();
    }
  }

  private logCommand(commandName: string, userId: string, responseTime: number, success: boolean, error?: any): void {
    // Use completely non-blocking logging with optimized database method
    setImmediate(async () => {
      try {
        await Promise.race([
          this.bot.database.insertOne('command_logs', {
            commandName,
            userId,
            timestamp: new Date(),
            responseTime,
            success,
            error: error ? error.message : null,
          }),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Log timeout')), 1000))
        ]);
      } catch (err) {
        // Silently fail logging to avoid affecting command performance
      }

      // Also log errors separately
      if (!success && error) {
        try {
          await Promise.race([
            this.bot.database.insertOne('error_logs', {
              type: 'command_error',
              commandName,
              userId,
              timestamp: new Date(),
              error: error.message,
              stack: error.stack,
            }),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Error log timeout')), 1000))
          ]);
        } catch (err) {
          // Silently fail error logging
        }
      }
    });
  }
} 