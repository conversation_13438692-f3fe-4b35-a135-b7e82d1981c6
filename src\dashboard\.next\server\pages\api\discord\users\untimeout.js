"use strict";(()=>{var e={};e.id=9511,e.ids=[9511],e.modules={224:e=>{e.exports=import("@discordjs/rest")},264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},381:(e,t,o)=>{o.d(t,{_:()=>f});var r=o(2115),s=o.n(r),i=o(9021),a=o.n(i),n=o(3873),d=o.n(n);let l=function(){let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d().resolve(process.cwd(),e)).find(e=>a().existsSync(e));if(!e){let t=d().resolve(__dirname,"../../../config.yml");a().existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");return e}(),c=s().parse(a().readFileSync(l,"utf8")),u="http://***********:3000",b=new URL(c.dashboard?.url||"http://localhost:3000"),m=`${b.protocol}//localhost:${b.port||"3000"}`,f={DISCORD_BOT_TOKEN:c.bot.token,DISCORD_CLIENT_ID:c.bot.clientId,DISCORD_CLIENT_SECRET:c.bot.clientSecret,DISCORD_GUILD_ID:c.bot.guildId,NEXTAUTH_URL:u||c.dashboard?.nextAuthUrl||c.dashboard?.url||m,NEXTAUTH_LOCALHOST_URL:m};c.bot.token,c.bot.clientId,c.bot.clientSecret,c.bot.guildId,c.bot.presence,u||c.dashboard?.url,u||c.dashboard?.nextAuthUrl||c.dashboard?.url,c.dashboard?.admins,c.dashboard?.adminRoleIds,c.dashboard?.session?.secret||c.bot.clientSecret,Object.entries(f).forEach(([e,t])=>{if(!t)throw Error(`Missing required environment variable: ${e}`)})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3687:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.r(t),o.d(t,{config:()=>c,default:()=>l,routeModule:()=>u});var s=o(3433),i=o(264),a=o(584),n=o(6952),d=e([n]);n=(d.then?(await d)():d)[0];let l=(0,a.M)(n,"default"),c=(0,a.M)(n,"config"),u=new s.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/users/untimeout",pathname:"/api/discord/users/untimeout",bundlePath:"",filename:""},userland:n});r()}catch(e){r(e)}})},3873:e=>{e.exports=require("path")},3915:e=>{e.exports=import("discord-api-types/v10")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},6952:(e,t,o)=>{o.a(e,async(e,r)=>{try{o.r(t),o.d(t,{default:()=>c});var s=o(5806),i=o(8525),a=o(224),n=o(3915),d=o(381),l=e([a,n]);async function c(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,s.getServerSession)(e,t,i.authOptions)){let o=e.headers.authorization;if(!o||!o.startsWith("Bearer "))return t.status(401).json({error:"Unauthorized"})}let{userId:o}=e.body;if(!o)return t.status(400).json({error:"Missing userId"});let r=new a.REST({version:"10"}).setToken(d._.DISCORD_BOT_TOKEN),l=d._.DISCORD_GUILD_ID;await r.patch(n.Routes.guildMember(l,o),{body:{communication_disabled_until:null}}),t.status(200).json({success:!0})}catch(e){t.status(500).json({error:"Failed to remove timeout"})}}[a,n]=l.then?(await l)():l,r()}catch(e){r(e)}})},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var r=o(5542),s=o.n(r);let i=require("next-auth/providers/discord");var a=o.n(i),n=o(8580);let d={providers:[a()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,r=t.accessToken||null;e.user.id=o,e.user.accessToken=r;let s=!1;if(o)if((n.dashboardConfig.dashboard.admins||[]).includes(o))s=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();s=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),r=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var r=o(9021),s=o(2115),i=o.n(s),a=o(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");n=i().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=3687);module.exports=o})();