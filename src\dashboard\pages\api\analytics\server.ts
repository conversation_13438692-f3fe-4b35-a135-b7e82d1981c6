import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { dashboardConfig } from '../../../core/config';
import { REST } from '@discordjs/rest';
import { Routes } from 'discord-api-types/v10';
import { MongoClient } from 'mongodb';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Initialize Discord REST client
    const rest = new REST({ version: '10' }).setToken(dashboardConfig.bot.token);
    const guildId = dashboardConfig.bot.guildId;

    // Fetch guild data
    const [guild, channels, roles, members] = await Promise.all([
      rest.get(Routes.guild(guildId)) as Promise<any>,
      rest.get(Routes.guildChannels(guildId)) as Promise<any[]>,
      rest.get(Routes.guildRoles(guildId)) as Promise<any[]>,
      rest.get(Routes.guildMembers(guildId), { query: new URLSearchParams({ limit: '1000' }) }) as Promise<any[]>
    ]);

    // Calculate online members (check presence if available, else use a better approximation)
    let onlineMembers = 0;
    if (members.some(m => m.presence)) {
      // If presence data is available (requires presence intent)
      onlineMembers = members.filter(member => 
        member.user && !member.user.bot && member.presence?.status !== 'offline'
      ).length;
    } else {
      // Fallback: Assume 50% of non-bot members are online (better than 0.3)
      onlineMembers = Math.ceil(members.filter(member => member.user && !member.user.bot).length * 0.5);
    }

    // Filter channels by type
    const textChannels = channels.filter(c => c.type === 0).length; // GUILD_TEXT
    const voiceChannels = channels.filter(c => c.type === 2).length; // GUILD_VOICE
    const categories = channels.filter(c => c.type === 4).length; // GUILD_CATEGORY

    // Filter roles (exclude @everyone and managed roles)
    const customRoles = roles.filter(role => 
      !role.managed && role.name !== '@everyone'
    ).length;

    const serverStats: any = {
      totalMembers: guild.member_count || members.length,
      onlineMembers: onlineMembers,
      totalChannels: channels.length,
      textChannels,
      voiceChannels,
      categories,
      totalRoles: customRoles,
      serverBoosts: guild.premium_subscription_count || 0,
      boostLevel: guild.premium_tier || 0,
    };

    // Additional metrics: new members joined and left today
    const db = await getDb();

    const startOfDay = new Date();
    startOfDay.setHours(0,0,0,0);
    const endOfDay = new Date(startOfDay.getTime() + 24*60*60*1000);

    const newMembersToday = await db.collection('member_logs')
      .countDocuments({ action: 'join', timestamp: { $gte: startOfDay, $lt: endOfDay } })
      .catch(() => 0);

    const leftMembersToday = await db.collection('member_logs')
      .countDocuments({ action: 'leave', timestamp: { $gte: startOfDay, $lt: endOfDay } })
      .catch(() => 0);

    // Weekly joins/leaves
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);

    const logsWeek = await db.collection('member_logs')
      .find({ timestamp: { $gte: sevenDaysAgo } })
      .toArray()
      .catch(() => []);

    const dayLabels = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];
    const daysMap: Record<string,{ joins:number; leaves:number }> = {
      Mon:{joins:0,leaves:0},Tue:{joins:0,leaves:0},Wed:{joins:0,leaves:0},Thu:{joins:0,leaves:0},Fri:{joins:0,leaves:0},Sat:{joins:0,leaves:0},Sun:{joins:0,leaves:0},
    };

    for(const log of logsWeek){
      const d=new Date(log.timestamp);
      const label=dayLabels[d.getDay()];
      if(!daysMap[label]) continue;
      if(log.action==='join') daysMap[label].joins +=1;
      else if(log.action==='leave') daysMap[label].leaves +=1;
    }
    const orderedDays=['Mon','Tue','Wed','Thu','Fri','Sat','Sun'];
    const weeklyMembers=orderedDays.map(day=>({day,joins:daysMap[day].joins,leaves:daysMap[day].leaves}));

    serverStats.newMembersToday = newMembersToday;
    serverStats.leftMembersToday = leftMembersToday;
    serverStats.weeklyMembers = weeklyMembers;

    res.status(200).json({ serverStats });
  } catch (error) {
    console.error('Error fetching server analytics:', error);
    res.status(500).json({ 
      error: 'Failed to fetch server analytics', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}

// Reuse connection pattern like other analytics
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
} 