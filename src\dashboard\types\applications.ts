export interface ApplicationQuestion {
  id: string;
  text: string;
  type: 'text' | 'radio' | 'checkbox' | 'select';
  required: boolean;
  options?: string[];
  correctAnswer?: number;
}

export interface QuizQuestion {
  question: string;
  options: string[];
  correctAnswer: number;
}

export interface ApplicationType {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  gradient: {
    from: string;
    to: string;
  };
  isOpen: boolean;
  requiresApproval: boolean;
  questions: ApplicationQuestion[];
  quiz?: QuizQuestion[];
  requirements?: {
    minAge?: number;
    minAccountAge?: number;
    timezone?: boolean;
    availability?: boolean;
  };
  metadata: {
    totalApplications: number;
    acceptanceRate: number;
    averageResponseTime: string;
    nextReviewDate?: string;
  };
}

export interface ApplicationSubmission {
  id: string;
  applicationTypeId: string;
  userId: string;
  username: string;
  submittedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  answers: Record<string, string | string[]>;
  reviewedBy?: string;
  reviewedAt?: string;
  feedback?: string;
}

export interface ApplicationsState {
  types: ApplicationType[];
  userSubmissions: ApplicationSubmission[];
  loading: boolean;
  error: string | null;
}

export interface ModScenario {
  id: number;
  scenario: string;
  context: string;
  displayId?: number;
} 