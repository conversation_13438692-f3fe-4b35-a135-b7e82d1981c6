"use strict";(()=>{var e={};e.id=5088,e.ids=[5088],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2875:(e,t,r)=>{r.r(t),r.d(t,{config:()=>f,default:()=>h,routeModule:()=>m});var s={};r.r(s),r.d(s,{default:()=>p});var o=r(3433),a=r(264),n=r(584),i=r(5806),d=r(8525),u=r(8580);let l={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4,GUILD_ANNOUNCEMENT:5,GUILD_ANNOUNCEMENT_THREAD:10,GUILD_PUBLIC_THREAD:11,GUILD_PRIVATE_THREAD:12,GUILD_STAGE_VOICE:13,GUILD_FORUM:15},c={0:"GUILD_TEXT",2:"GUILD_VOICE",4:"GUILD_CATEGORY",5:"GUILD_ANNOUNCEMENT",10:"GUILD_ANNOUNCEMENT_THREAD",11:"GUILD_PUBLIC_THREAD",12:"GUILD_PRIVATE_THREAD",13:"GUILD_STAGE_VOICE",15:"GUILD_FORUM"};async function p(e,t){try{let r=await (0,i.getServerSession)(e,t,d.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});let s=r.user.isAdmin,{guildId:o,token:a}=u.dashboardConfig.bot;if(!a||!o)return t.status(500).json({error:"Bot configuration missing"});if("GET"===e.method)try{let e=await fetch(`https://discord.com/api/v10/guilds/${o}/channels`,{headers:{Authorization:`Bot ${a}`}});if(!e.ok)throw Error("Failed to fetch channels");let r=(await e.json()).map(e=>{let t={id:e.id,name:e.name,type:c[e.type]||"unknown",position:e.position};return e.parent_id&&(t.parent_id=e.parent_id),t.raw_type=e.type,e.type===l.GUILD_TEXT?(t.topic=e.topic,t.nsfw=e.nsfw,t.rate_limit_per_user=e.rate_limit_per_user):e.type===l.GUILD_VOICE&&(t.bitrate=e.bitrate,t.user_limit=e.user_limit),t});return t.status(200).json(r)}catch(e){return t.status(500).json({error:"Failed to fetch channels"})}if("POST"===e.method){if(!s)return t.status(403).json({error:"Forbidden - Admin access required"});try{let{name:r,type:s,topic:n,nsfw:i,bitrate:d,userLimit:u,parent:c,position:p,rateLimitPerUser:h}=e.body;if(!r||"number"!=typeof s&&"string"!=typeof s)return t.status(400).json({error:"Name and type are required"});let f="number"==typeof s?s:l[s];if("number"!=typeof f)return t.status(400).json({error:"Invalid channel type"});let m={name:r,type:f,position:p||0};f===l.GUILD_TEXT?(n&&(m.topic=n),m.nsfw=!!i,"number"!=typeof h||isNaN(h)||(m.rate_limit_per_user=h)):f===l.GUILD_VOICE&&("number"!=typeof d||isNaN(d)||(m.bitrate=d),"number"!=typeof u||isNaN(u)||(m.user_limit=u)),c&&f!==l.GUILD_CATEGORY&&(m.parent_id=c);let b=await fetch(`https://discord.com/api/v10/guilds/${o}/channels`,{method:"POST",headers:{Authorization:`Bot ${a}`,"Content-Type":"application/json"},body:JSON.stringify(m)});if(!b.ok){let e;try{e=await b.json()}catch{e=await b.text()}return t.status(b.status).json(e)}let I=await b.json();return t.status(201).json(I)}catch(e){return t.status(500).json({error:"Failed to create channel"})}}if("PATCH"===e.method){if(!s)return t.status(403).json({error:"Forbidden - Admin access required"});try{let r=e.query.channelId;if(!r)return t.status(400).json({error:"Channel ID is required"});let s=await fetch(`https://discord.com/api/v10/channels/${r}`,{method:"PATCH",headers:{Authorization:`Bot ${a}`,"Content-Type":"application/json"},body:JSON.stringify(e.body)});if(!s.ok){let e;try{e=await s.json()}catch{e=await s.text()}return t.status(s.status).json(e)}let o=await s.json();return t.status(200).json(o)}catch(e){return t.status(500).json({error:"Failed to update channel"})}}if("DELETE"===e.method){if(!s)return t.status(403).json({error:"Forbidden - Admin access required"});try{let r=e.query.channelId;if(!r)return t.status(400).json({error:"Channel ID is required"});let s=await fetch(`https://discord.com/api/v10/channels/${r}`,{method:"DELETE",headers:{Authorization:`Bot ${a}`}});if(!s.ok){let e;try{e=await s.json()}catch{e=await s.text()}return t.status(s.status).json(e)}return t.status(200).json({message:"Channel deleted successfully"})}catch(e){return t.status(500).json({error:"Failed to delete channel"})}}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let h=(0,n.M)(s,"default"),f=(0,n.M)(s,"config"),m=new o.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/channels",pathname:"/api/discord/channels",bundlePath:"",filename:""},userland:s})},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>u});var s=r(5542),o=r.n(s);let a=require("next-auth/providers/discord");var n=r.n(a),i=r(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,s=t.accessToken||null;e.user.id=r,e.user.accessToken=s;let o=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))o=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();o=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),s=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},u=o()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>u});var s=r(9021),o=r(2115),a=r.n(o),n=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");i=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let u=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=2875);module.exports=r})();