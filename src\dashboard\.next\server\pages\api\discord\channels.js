"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/discord/channels";
exports.ids = ["pages/api/discord/channels"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\discord\\channels.ts */ \"(api-node)/./pages/api/discord/channels.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/discord/channels\",\n        pathname: \"/api/discord/channels\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_discord_channels_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./core/config.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/discord/channels.ts":
/*!***************************************!*\
  !*** ./pages/api/discord/channels.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\n// Discord channel type mapping\nconst CHANNEL_TYPES = {\n    GUILD_TEXT: 0,\n    GUILD_VOICE: 2,\n    GUILD_CATEGORY: 4,\n    GUILD_ANNOUNCEMENT: 5,\n    GUILD_ANNOUNCEMENT_THREAD: 10,\n    GUILD_PUBLIC_THREAD: 11,\n    GUILD_PRIVATE_THREAD: 12,\n    GUILD_STAGE_VOICE: 13,\n    GUILD_FORUM: 15\n};\n// Reverse mapping for type conversion\nconst CHANNEL_TYPE_NAMES = {\n    0: 'GUILD_TEXT',\n    2: 'GUILD_VOICE',\n    4: 'GUILD_CATEGORY',\n    5: 'GUILD_ANNOUNCEMENT',\n    10: 'GUILD_ANNOUNCEMENT_THREAD',\n    11: 'GUILD_PUBLIC_THREAD',\n    12: 'GUILD_PRIVATE_THREAD',\n    13: 'GUILD_STAGE_VOICE',\n    15: 'GUILD_FORUM'\n};\nasync function handler(req, res) {\n    try {\n        const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n        if (!session?.user) {\n            return res.status(401).json({\n                error: 'Unauthorized'\n            });\n        }\n        // For sensitive operations like creating channels we still require admin.\n        const isAdmin = session.user.isAdmin;\n        const { guildId, token } = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot;\n        if (!token || !guildId) {\n            console.error('Missing bot configuration');\n            return res.status(500).json({\n                error: 'Bot configuration missing'\n            });\n        }\n        if (req.method === 'GET') {\n            try {\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    throw new Error('Failed to fetch channels');\n                }\n                const channels = await response.json();\n                // Format channels based on the request source\n                const formattedChannels = channels.map((channel)=>{\n                    // Base channel format\n                    const formattedChannel = {\n                        id: channel.id,\n                        name: channel.name,\n                        type: CHANNEL_TYPE_NAMES[channel.type] || 'unknown',\n                        position: channel.position\n                    };\n                    // Add parent_id if it exists\n                    if (channel.parent_id) {\n                        formattedChannel.parent_id = channel.parent_id;\n                    }\n                    // Add raw type for the channel management page\n                    formattedChannel.raw_type = channel.type;\n                    // Add additional properties based on channel type\n                    if (channel.type === CHANNEL_TYPES.GUILD_TEXT) {\n                        formattedChannel.topic = channel.topic;\n                        formattedChannel.nsfw = channel.nsfw;\n                        formattedChannel.rate_limit_per_user = channel.rate_limit_per_user;\n                    } else if (channel.type === CHANNEL_TYPES.GUILD_VOICE) {\n                        formattedChannel.bitrate = channel.bitrate;\n                        formattedChannel.user_limit = channel.user_limit;\n                    }\n                    return formattedChannel;\n                });\n                return res.status(200).json(formattedChannels);\n            } catch (error) {\n                console.error('Error fetching channels:', error);\n                return res.status(500).json({\n                    error: 'Failed to fetch channels'\n                });\n            }\n        }\n        if (req.method === 'POST') {\n            // Creating channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const { name, type, topic, nsfw, bitrate, userLimit, parent, position, rateLimitPerUser } = req.body;\n                console.log('Received channel creation request:', req.body);\n                // Validate required fields\n                if (!name || typeof type !== 'number' && typeof type !== 'string') {\n                    console.log('Validation failed:', {\n                        name,\n                        type\n                    });\n                    return res.status(400).json({\n                        error: 'Name and type are required'\n                    });\n                }\n                // Convert string type to numeric if needed\n                let numericType = typeof type === 'number' ? type : CHANNEL_TYPES[type];\n                console.log('Channel type conversion:', {\n                    original: type,\n                    converted: numericType\n                });\n                if (typeof numericType !== 'number') {\n                    return res.status(400).json({\n                        error: 'Invalid channel type'\n                    });\n                }\n                // Prepare channel data based on type\n                const channelData = {\n                    name,\n                    type: numericType,\n                    position: position || 0\n                };\n                // Add type-specific properties\n                if (numericType === CHANNEL_TYPES.GUILD_TEXT) {\n                    if (topic) channelData.topic = topic;\n                    channelData.nsfw = Boolean(nsfw);\n                    if (typeof rateLimitPerUser === 'number' && !isNaN(rateLimitPerUser)) {\n                        channelData.rate_limit_per_user = rateLimitPerUser;\n                    }\n                } else if (numericType === CHANNEL_TYPES.GUILD_VOICE) {\n                    if (typeof bitrate === 'number' && !isNaN(bitrate)) {\n                        channelData.bitrate = bitrate;\n                    }\n                    if (typeof userLimit === 'number' && !isNaN(userLimit)) {\n                        channelData.user_limit = userLimit;\n                    }\n                }\n                // Add parent category if specified\n                if (parent && numericType !== CHANNEL_TYPES.GUILD_CATEGORY) {\n                    channelData.parent_id = parent;\n                }\n                console.log('Creating channel with data:', channelData);\n                const response = await fetch(`https://discord.com/api/v10/guilds/${guildId}/channels`, {\n                    method: 'POST',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(channelData)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const newChannel = await response.json();\n                return res.status(201).json(newChannel);\n            } catch (error) {\n                console.error('Error creating channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to create channel'\n                });\n            }\n        }\n        if (req.method === 'PATCH') {\n            // Editing channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const channelId = req.query.channelId;\n                if (!channelId) {\n                    return res.status(400).json({\n                        error: 'Channel ID is required'\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {\n                    method: 'PATCH',\n                    headers: {\n                        Authorization: `Bot ${token}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify(req.body)\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                const updatedChannel = await response.json();\n                return res.status(200).json(updatedChannel);\n            } catch (error) {\n                console.error('Error updating channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to update channel'\n                });\n            }\n        }\n        if (req.method === 'DELETE') {\n            // Deleting channels requires admin permissions\n            if (!isAdmin) {\n                return res.status(403).json({\n                    error: 'Forbidden - Admin access required'\n                });\n            }\n            try {\n                const channelId = req.query.channelId;\n                if (!channelId) {\n                    return res.status(400).json({\n                        error: 'Channel ID is required'\n                    });\n                }\n                const response = await fetch(`https://discord.com/api/v10/channels/${channelId}`, {\n                    method: 'DELETE',\n                    headers: {\n                        Authorization: `Bot ${token}`\n                    }\n                });\n                if (!response.ok) {\n                    let error;\n                    try {\n                        error = await response.json();\n                    } catch  {\n                        error = await response.text();\n                    }\n                    console.error('Discord API error:', error);\n                    return res.status(response.status).json(error);\n                }\n                return res.status(200).json({\n                    message: 'Channel deleted successfully'\n                });\n            } catch (error) {\n                console.error('Error deleting channel:', error);\n                return res.status(500).json({\n                    error: 'Failed to delete channel'\n                });\n            }\n        }\n        return res.status(405).json({\n            error: 'Method not allowed'\n        });\n    } catch (error) {\n        console.error('Error in channel handler:', error);\n        return res.status(500).json({\n            error: 'Internal server error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/discord/channels.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fdiscord%2Fchannels&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cdiscord%5Cchannels.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();