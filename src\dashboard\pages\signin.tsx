// @ts-nocheck
import { 
  Box, 
  Button, 
  <PERSON>lex, 
  <PERSON>ing, 
  Text, 
  VStack, 
  Icon, 
  useDisclosure,
} from '@chakra-ui/react';
import { keyframes } from '@emotion/react';
import { signIn, signOut, useSession } from 'next-auth/react';
import { FaDiscord, FaCode, FaRobot, FaServer, FaCog, FaBolt } from 'react-icons/fa';
import { useEffect, useState } from 'react';

// Quotes for random selection
const quotes = [
  "Welcome to 404 Chill Not Found - Where errors take a break!",
  "Join our community of chill developers and gamers",
  "Relax, you're in the right place",
  "404 Error? More like 404 Fun Found!",
  "Where coding meets community"
];

// Define floating animation keyframes
const floatKeyframes = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(5deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`;

const pulseKeyframes = keyframes`
  0% { transform: scale(1); opacity: 0.8; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 0.8; }
`;

// Convert keyframes to Chakra UI animation
const float = `${floatKeyframes} 3s ease-in-out infinite`;
const pulse = `${pulseKeyframes} 3s ease-in-out infinite`;

export default function SignIn() {
  const { isOpen, onOpen } = useDisclosure();
  const [quote, setQuote] = useState(quotes[0]);
  const { data: session } = useSession();

  useEffect(() => {
    // Only update the quote after the component has mounted on the client
    const randomQuote = quotes[Math.floor(Math.random() * quotes.length)];
    setQuote(randomQuote);
    // Trigger entrance animation
    onOpen();
  }, [onOpen]);

  return (
    <Flex
      minH="100vh"
      align="center"
      justify="center"
      bg="gray.900"
      position="relative"
      overflow="hidden"
    >
      {/* Background elements */}
      <Box
        position="absolute"
        top="10%"
        left="5%"
        animation={float}
        opacity={0.1}
      >
        <Icon as={FaCode} boxSize={20} />
      </Box>
      <Box
        position="absolute"
        bottom="15%"
        right="10%"
        animation={float}
        opacity={0.1}
      >
        <Icon as={FaCog} boxSize={16} />
      </Box>
      <Box
        position="absolute"
        top="20%"
        right="15%"
        animation={float}
        opacity={0.1}
      >
        <Icon as={FaBolt} boxSize={14} />
      </Box>
      <Box
        position="absolute"
        bottom="20%"
        left="15%"
        animation={float}
        opacity={0.1}
      >
        <Icon as={FaServer} boxSize={18} />
      </Box>

      {/* Main content */}
      <VStack
        spacing={8}
        p={8}
        bg="gray.800"
        borderRadius="xl"
        boxShadow="2xl"
        maxW="md"
        w="full"
        position="relative"
        zIndex={1}
      >
        <Box
          animation={pulse}
        >
          <Icon 
            as={FaRobot} 
            boxSize={20} 
            color="discord.500"
          />
        </Box>
        
        <Heading 
          size="xl" 
          textAlign="center"
          bgGradient="linear(to-r, discord.500, purple.500)"
          bgClip="text"
        >
          404 Bot Dashboard
        </Heading>
        
        <Text 
          fontSize="lg" 
          color="gray.300" 
          textAlign="center"
          opacity={isOpen ? 1 : 0}
          transform={isOpen ? "translateY(0)" : "translateY(20px)"}
          transition="all 0.5s ease"
        >
          {quote}
        </Text>

        {session ? (
          <Button
            leftIcon={<Icon as={FaDiscord} boxSize={5} />}
            size="lg"
            w="full"
            bg="red.500"
            color="white"
            onClick={() => signOut({ callbackUrl: '/signin' })}
            _hover={{
              bg: "red.600",
              transform: "translateY(-2px)",
              boxShadow: "0 0 20px rgba(229,62,62,0.6)",
            }}
            _active={{
              transform: "translateY(0)",
              boxShadow: "0 0 10px rgba(229,62,62,0.4)",
            }}
            transition="all 0.2s ease"
          >
            Sign Out
          </Button>
        ) : (
          <Button
            leftIcon={<Icon as={FaDiscord} boxSize={5} />}
            size="lg"
            w="full"
            bg="discord.500"
            color="white"
            onClick={() => signIn('discord', { callbackUrl: '/overview' })}
            _hover={{
              bg: "discord.600",
              transform: "translateY(-2px)",
              boxShadow: "0 0 20px rgba(88,101,242,0.6)",
            }}
            _active={{
              transform: "translateY(0)",
              boxShadow: "0 0 10px rgba(88,101,242,0.4)",
            }}
            transition="all 0.2s ease"
          >
            Sign in with Discord
          </Button>
        )}
      </VStack>
    </Flex>
  );
}
