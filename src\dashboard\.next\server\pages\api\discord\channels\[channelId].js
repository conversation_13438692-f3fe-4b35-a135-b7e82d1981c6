"use strict";(()=>{var e={};e.id=9685,e.ids=[9685],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5645:(e,t,r)=>{r.r(t),r.d(t,{config:()=>h,default:()=>u,routeModule:()=>f});var o={};r.r(o),r.d(o,{default:()=>c});var s=r(3433),a=r(264),n=r(584),i=r(5806),d=r(8525),l=r(8580);async function c(e,t){try{let r=await (0,i.getServerSession)(e,t,d.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:o,token:s}=l.dashboardConfig.bot;if(!s||!o)return t.status(500).json({error:"Bot configuration missing"});let{channelId:a}=e.query;if(!a||"string"!=typeof a)return t.status(400).json({error:"Channel ID is required"});if("PATCH"===e.method)try{let r=await fetch(`https://discord.com/api/v10/channels/${a}`,{method:"PATCH",headers:{Authorization:`Bot ${s}`,"Content-Type":"application/json"},body:JSON.stringify(e.body)});if(!r.ok){let e;try{e=await r.json()}catch{e=await r.text()}return t.status(r.status).json(e)}let o=await r.json();return t.status(200).json(o)}catch(e){return t.status(500).json({error:"Failed to update channel"})}if("DELETE"===e.method)try{let e=await fetch(`https://discord.com/api/v10/channels/${a}`,{method:"DELETE",headers:{Authorization:`Bot ${s}`}});if(!e.ok){let r;try{r=await e.json()}catch{r=await e.text()}return t.status(e.status).json(r)}return t.status(200).json({message:"Channel deleted successfully"})}catch(e){return t.status(500).json({error:"Failed to delete channel"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let u=(0,n.M)(o,"default"),h=(0,n.M)(o,"config"),f=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/channels/[channelId]",pathname:"/api/discord/channels/[channelId]",bundlePath:"",filename:""},userland:o})},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var o=r(5542),s=r.n(o);let a=require("next-auth/providers/discord");var n=r.n(a),i=r(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,o=t.accessToken||null;e.user.id=r,e.user.accessToken=o;let s=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))s=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();s=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),o=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var o=r(9021),s=r(2115),a=r.n(s),n=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");i=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=5645);module.exports=r})();