import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('timeout')
  .setDescription('Temporarily timeout (mute) a member')
  .addUserOption(option =>
    option.setName('user')
      .setDescription('The user to timeout')
      .setRequired(true))
  .addIntegerOption(option =>
    option.setName('minutes')
      .setDescription('Duration of the timeout in minutes (1-10080)')
      .setRequired(true)
      .setMinValue(1)
      .setMaxValue(10080))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for the timeout')
      .setRequired(false))
  .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const targetUser = interaction.options.getUser('user', true);
  const minutes = interaction.options.getInteger('minutes', true);
  const reason = interaction.options.getString('reason') ?? 'No reason provided';

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ModerateMembers)) {
    await interaction.reply({ content: '❌ You do not have permission to timeout members.', ephemeral: true });
    return;
  }

  const member = await interaction.guild?.members.fetch(targetUser.id).catch(() => null);
  if (!member) {
    await interaction.reply({ content: '❌ Could not find that member in this guild.', ephemeral: true });
    return;
  }

  if (!member.moderatable) {
    await interaction.reply({ content: '❌ I cannot timeout this user. They may have higher permissions or roles than me.', ephemeral: true });
    return;
  }

  const durationMs = minutes * 60_000;
  await member.timeout(durationMs, reason);
  await interaction.reply({ content: `🔇 Timed out **${targetUser.tag}** for ${minutes} minute(s) | Reason: ${reason}` });
}

export const cooldown = 5000; 