import React, { useState, memo } from 'react';
import { <PERSON><PERSON>, <PERSON>sition, NodeProps } from 'reactflow';
import {
  Box,
  Text,
  VStack,
  HStack,
  Select,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Badge,
  Input,
  Textarea,
  Switch,
  Divider,
  SimpleGrid,
  Alert,
  AlertIcon,
  AlertDescription,
  Checkbox,
  CheckboxGroup,
  Stack,
  Tag,
  TagLabel,
  TagCloseButton,
  Wrap,
  WrapItem,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Code,
  Collapse,
  Tooltip,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
} from '@chakra-ui/react';
import { FiSettings, FiRadio, FiPlus, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, FiTrash2 } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';

interface EventFilter {
  type: 'channel' | 'role' | 'user' | 'regex' | 'content' | 'cooldown' | 'custom';
  value: string;
  operator?: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'regex';
}

interface EventNodeData {
  label: string;
  eventType?: string;
  eventName?: string;
  filters?: EventFilter[];
  conditions?: string[];
  ignoreBot?: boolean;
  ignoreSystem?: boolean;
  rateLimited?: boolean;
  rateLimit?: number;
  channelRestrictions?: string[];
  roleRestrictions?: string[];
  description?: string;
  priority?: number;
  async?: boolean;
  retryOnError?: boolean;
  maxRetries?: number;
}

// Available variables for event context
const eventVariables = {
  event: [
    { name: '{event.type}', description: 'Type of event that triggered', icon: '📡' },
    { name: '{event.timestamp}', description: 'When the event occurred', icon: '⏰' },
    { name: '{event.guild}', description: 'Server where event occurred', icon: '🏠' },
    { name: '{event.channel}', description: 'Channel where event occurred', icon: '📺' },
    { name: '{event.user}', description: 'User who triggered the event', icon: '👤' },
  ],
  message: [
    { name: '{message.id}', description: 'Message ID', icon: '🆔' },
    { name: '{message.content}', description: 'Message content', icon: '💬' },
    { name: '{message.author}', description: 'Message author', icon: '👤' },
    { name: '{message.channel}', description: 'Message channel', icon: '📺' },
    { name: '{message.createdAt}', description: 'Message creation time', icon: '📅' },
    { name: '{message.editedAt}', description: 'Message edit time', icon: '✏️' },
    { name: '{message.attachments}', description: 'Message attachments', icon: '📎' },
    { name: '{message.embeds}', description: 'Message embeds', icon: '📋' },
    { name: '{message.reactions}', description: 'Message reactions', icon: '👍' },
    { name: '{message.mentions}', description: 'Message mentions', icon: '📢' },
  ],
  member: [
    { name: '{member.id}', description: 'Member ID', icon: '🆔' },
    { name: '{member.username}', description: 'Member username', icon: '👤' },
    { name: '{member.displayName}', description: 'Member display name', icon: '📝' },
    { name: '{member.tag}', description: 'Member tag (username#0000)', icon: '🏷️' },
    { name: '{member.mention}', description: 'Member mention (<@id>)', icon: '📢' },
    { name: '{member.avatar}', description: 'Member avatar URL', icon: '🖼️' },
    { name: '{member.joinedAt}', description: 'Server join date', icon: '🚪' },
    { name: '{member.roles}', description: 'Member roles', icon: '🎭' },
    { name: '{member.permissions}', description: 'Member permissions', icon: '🔐' },
    { name: '{member.isBot}', description: 'Is member a bot', icon: '🤖' },
  ],
  channel: [
    { name: '{channel.id}', description: 'Channel ID', icon: '🆔' },
    { name: '{channel.name}', description: 'Channel name', icon: '📺' },
    { name: '{channel.mention}', description: 'Channel mention (<#id>)', icon: '📢' },
    { name: '{channel.type}', description: 'Channel type', icon: '📋' },
    { name: '{channel.topic}', description: 'Channel topic', icon: '💬' },
    { name: '{channel.memberCount}', description: 'Member count', icon: '👥' },
    { name: '{channel.position}', description: 'Channel position', icon: '📍' },
    { name: '{channel.nsfw}', description: 'Is NSFW channel', icon: '🔞' },
  ],
  server: [
    { name: '{server.id}', description: 'Server ID', icon: '🆔' },
    { name: '{server.name}', description: 'Server name', icon: '🏠' },
    { name: '{server.icon}', description: 'Server icon URL', icon: '🖼️' },
    { name: '{server.memberCount}', description: 'Total member count', icon: '👥' },
    { name: '{server.owner}', description: 'Server owner', icon: '👑' },
    { name: '{server.boostLevel}', description: 'Server boost level', icon: '🚀' },
    { name: '{server.boostCount}', description: 'Server boost count', icon: '💎' },
    { name: '{server.createdAt}', description: 'Server creation date', icon: '📅' },
  ],
  reaction: [
    { name: '{reaction.emoji}', description: 'Reaction emoji', icon: '😀' },
    { name: '{reaction.count}', description: 'Reaction count', icon: '🔢' },
    { name: '{reaction.users}', description: 'Users who reacted', icon: '👥' },
    { name: '{reaction.me}', description: 'Bot reacted', icon: '🤖' },
  ],
  voice: [
    { name: '{voice.channelId}', description: 'Voice channel ID', icon: '🔊' },
    { name: '{voice.channelName}', description: 'Voice channel name', icon: '🔊' },
    { name: '{voice.memberCount}', description: 'Voice channel member count', icon: '👥' },
    { name: '{voice.muted}', description: 'Is member muted', icon: '🔇' },
    { name: '{voice.deafened}', description: 'Is member deafened', icon: '🔇' },
    { name: '{voice.streaming}', description: 'Is member streaming', icon: '📺' },
    { name: '{voice.camera}', description: 'Is member using camera', icon: '📹' },
  ],
  role: [
    { name: '{role.id}', description: 'Role ID', icon: '🆔' },
    { name: '{role.name}', description: 'Role name', icon: '🎭' },
    { name: '{role.mention}', description: 'Role mention (<@&id>)', icon: '📢' },
    { name: '{role.color}', description: 'Role color', icon: '🎨' },
    { name: '{role.position}', description: 'Role position', icon: '📍' },
    { name: '{role.permissions}', description: 'Role permissions', icon: '🔐' },
    { name: '{role.mentionable}', description: 'Is role mentionable', icon: '📢' },
    { name: '{role.hoisted}', description: 'Is role hoisted', icon: '📌' },
  ],
};

const eventTypes = [
  { value: 'messageCreate', label: '💬 Message Created', category: 'Messages', description: 'When a new message is sent' },
  { value: 'messageUpdate', label: '✏️ Message Edited', category: 'Messages', description: 'When a message is edited' },
  { value: 'messageDelete', label: '🗑️ Message Deleted', category: 'Messages', description: 'When a message is deleted' },
  { value: 'messageReactionAdd', label: '👍 Reaction Added', category: 'Messages', description: 'When a reaction is added to a message' },
  { value: 'messageReactionRemove', label: '👎 Reaction Removed', category: 'Messages', description: 'When a reaction is removed from a message' },
  { value: 'messageReactionRemoveAll', label: '🧹 All Reactions Removed', category: 'Messages', description: 'When all reactions are removed from a message' },
  
  { value: 'guildMemberAdd', label: '🚪 Member Joined', category: 'Members', description: 'When a new member joins the server' },
  { value: 'guildMemberRemove', label: '👋 Member Left', category: 'Members', description: 'When a member leaves the server' },
  { value: 'guildMemberUpdate', label: '👤 Member Updated', category: 'Members', description: 'When member info changes (roles, nickname, etc.)' },
  { value: 'userUpdate', label: '📝 User Updated', category: 'Members', description: 'When user profile changes (avatar, username, etc.)' },
  { value: 'presenceUpdate', label: '🟢 Presence Updated', category: 'Members', description: 'When member status/activity changes' },
  
  { value: 'guildBanAdd', label: '🔨 Member Banned', category: 'Moderation', description: 'When a member is banned' },
  { value: 'guildBanRemove', label: '🔓 Member Unbanned', category: 'Moderation', description: 'When a member is unbanned' },
  { value: 'messageDeleteBulk', label: '🧹 Bulk Message Delete', category: 'Moderation', description: 'When multiple messages are deleted at once' },
  
  { value: 'voiceStateUpdate', label: '🔊 Voice State Changed', category: 'Voice', description: 'When member joins/leaves/mutes in voice' },
  
  { value: 'channelCreate', label: '📺 Channel Created', category: 'Channels', description: 'When a new channel is created' },
  { value: 'channelDelete', label: '🗑️ Channel Deleted', category: 'Channels', description: 'When a channel is deleted' },
  { value: 'channelUpdate', label: '⚙️ Channel Updated', category: 'Channels', description: 'When channel settings change' },
  { value: 'channelPinsUpdate', label: '📌 Channel Pins Updated', category: 'Channels', description: 'When pinned messages change' },
  
  { value: 'roleCreate', label: '🎭 Role Created', category: 'Roles', description: 'When a new role is created' },
  { value: 'roleDelete', label: '🗑️ Role Deleted', category: 'Roles', description: 'When a role is deleted' },
  { value: 'roleUpdate', label: '⚙️ Role Updated', category: 'Roles', description: 'When role settings change' },
  
  { value: 'threadCreate', label: '🧵 Thread Created', category: 'Threads', description: 'When a thread is created' },
  { value: 'threadDelete', label: '🗑️ Thread Deleted', category: 'Threads', description: 'When a thread is deleted' },
  { value: 'threadUpdate', label: '⚙️ Thread Updated', category: 'Threads', description: 'When thread settings change' },
  { value: 'threadMemberUpdate', label: '👤 Thread Member Update', category: 'Threads', description: 'When someone joins/leaves a thread' },
  
  { value: 'interactionCreate', label: '🎛️ Interaction Created', category: 'Interactions', description: 'When buttons/selects are used' },
  { value: 'applicationCommandPermissionsUpdate', label: '🔐 Command Permissions Updated', category: 'Interactions', description: 'When command permissions change' },
  
  { value: 'guildUpdate', label: '🏠 Server Updated', category: 'Server', description: 'When server settings change' },
  { value: 'guildUnavailable', label: '⚠️ Server Unavailable', category: 'Server', description: 'When server becomes unavailable' },
  { value: 'guildIntegrationsUpdate', label: '🔗 Integrations Updated', category: 'Server', description: 'When server integrations change' },
  
  { value: 'inviteCreate', label: '🔗 Invite Created', category: 'Server', description: 'When an invite is created' },
  { value: 'inviteDelete', label: '🗑️ Invite Deleted', category: 'Server', description: 'When an invite is deleted' },
  
  { value: 'emojiCreate', label: '😀 Emoji Created', category: 'Server', description: 'When a custom emoji is added' },
  { value: 'emojiDelete', label: '🗑️ Emoji Deleted', category: 'Server', description: 'When a custom emoji is removed' },
  { value: 'emojiUpdate', label: '⚙️ Emoji Updated', category: 'Server', description: 'When a custom emoji is modified' },
  
  { value: 'stickerCreate', label: '🏷️ Sticker Created', category: 'Server', description: 'When a custom sticker is added' },
  { value: 'stickerDelete', label: '🗑️ Sticker Deleted', category: 'Server', description: 'When a custom sticker is removed' },
  { value: 'stickerUpdate', label: '⚙️ Sticker Updated', category: 'Server', description: 'When a custom sticker is modified' },
];

const filterTypes = [
  { value: 'channel', label: '📺 Channel Filter', description: 'Filter by specific channels' },
  { value: 'role', label: '🎭 Role Filter', description: 'Filter by user roles' },
  { value: 'user', label: '👤 User Filter', description: 'Filter by specific users' },
  { value: 'regex', label: '🔍 Regex Pattern', description: 'Filter using regular expressions' },
  { value: 'cooldown', label: '⏰ Cooldown', description: 'Rate limit event triggers' },
  { value: 'permission', label: '🔐 Permission', description: 'Filter by user permissions' },
  { value: 'content', label: '💬 Content Filter', description: 'Filter by message content' },
  { value: 'custom', label: '⚙️ Custom', description: 'Custom filter condition' },
];

const EventNode = memo(({ data, selected, id, updateNodeData: updateParentNodeData }: NodeProps<EventNodeData> & { updateNodeData?: (nodeId: string, newData: any) => void }) => {
  const { currentScheme } = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [nodeData, setNodeData] = useState<EventNodeData>(() => ({
    ignoreBot: true,
    ignoreSystem: true,
    rateLimited: false,
    rateLimit: 1000,
    priority: 1,
    async: false,
    retryOnError: false,
    maxRetries: 3,
    filters: [],
    channelRestrictions: [],
    roleRestrictions: [],
    ...data
  }));
  const [showVariables, setShowVariables] = useState(false);

  const updateNodeData = (updates: Partial<EventNodeData>) => {
    setNodeData(prev => ({ ...prev, ...updates }));
  };

  const handleModalClose = () => {
    // Update parent nodes array when modal closes
    if (updateParentNodeData && id) {
      updateParentNodeData(id, nodeData);
    }
    onClose();
  };

  const getEventLabel = (eventType: string) => {
    const event = eventTypes.find(e => e.value === eventType);
    return event ? event.label.split(' ').slice(1).join(' ') : eventType;
  };

  const getEventIcon = (eventType: string) => {
    const event = eventTypes.find(e => e.value === eventType);
    return event ? event.label.split(' ')[0] : '📡';
  };

  const addFilter = () => {
    const newFilter: EventFilter = {
      type: 'channel',
      value: '',
      operator: 'equals'
    };
    updateNodeData({
      filters: [...(nodeData.filters || []), newFilter]
    });
  };

  const updateFilter = (index: number, updates: Partial<EventFilter>) => {
    const newFilters = [...(nodeData.filters || [])];
    newFilters[index] = { ...newFilters[index], ...updates };
    updateNodeData({ filters: newFilters });
  };

  const removeFilter = (index: number) => {
    const newFilters = (nodeData.filters || []).filter((_, i) => i !== index);
    updateNodeData({ filters: newFilters });
  };

  const copyVariable = (variable: string) => {
    navigator.clipboard.writeText(variable);
  };

  const renderVariablesList = () => (
    <Collapse in={showVariables} animateOpacity>
      <Box
        bg={currentScheme.colors.surface}
        border="1px solid"
        borderColor={currentScheme.colors.border}
        borderRadius="md"
        p={4}
        mt={3}
        maxH="400px"
        overflowY="auto"
      >
        <Accordion allowMultiple>
          {Object.entries(eventVariables).map(([category, variables]) => (
            <AccordionItem key={category} border="none">
              <AccordionButton px={0} py={2}>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} textTransform="capitalize">
                    {category} Variables
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px={0} py={2}>
                <VStack spacing={2} align="stretch">
                  {variables.map((variable) => (
                    <HStack
                      key={variable.name}
                      spacing={2}
                      p={2}
                      bg={currentScheme.colors.background}
                      borderRadius="md"
                      cursor="pointer"
                      _hover={{ bg: currentScheme.colors.surface }}
                      onClick={() => copyVariable(variable.name)}
                    >
                      <Text fontSize="sm">{variable.icon}</Text>
                      <Code fontSize="xs" colorScheme="green">
                        {variable.name}
                      </Code>
                      <Text fontSize="xs" color={currentScheme.colors.textSecondary} flex="1">
                        {variable.description}
                      </Text>
                      <IconButton
                        icon={<FiCopy />}
                        size="xs"
                        variant="ghost"
                        aria-label="Copy variable"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyVariable(variable.name);
                        }}
                      />
                    </HStack>
                  ))}
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </Box>
    </Collapse>
  );

  return (
    <>
      <Box
        bg={currentScheme.colors.surface}
        border={`2px solid ${selected ? '#10b981' : currentScheme.colors.border}`}
        borderRadius="md"
        p={2}
        minW="140px"
        maxW="180px"
        boxShadow="sm"
        position="relative"
        _hover={{
          boxShadow: 'md',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <Handle
          type="target"
          position={Position.Top}
          style={{
            background: '#10b981',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            top: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
        
        <VStack spacing={1} align="stretch">
          <HStack justify="space-between" align="center">
            <HStack spacing={1}>
              <Box
                bg="green.500"
                color="white"
                borderRadius="full"
                p={0.5}
                fontSize="xs"
              >
                <FiRadio />
              </Box>
              <Text fontSize="xs" fontWeight="bold" color={currentScheme.colors.text}>
                Event
              </Text>
            </HStack>
            <IconButton
              icon={<FiSettings />}
              size="xs"
              variant="ghost"
              onClick={onOpen}
              aria-label="Configure event"
            />
          </HStack>
          
          <Box>
            <HStack spacing={1}>
              {nodeData.eventType && (
                <Text fontSize="xs">{getEventIcon(nodeData.eventType)}</Text>
              )}
            <Text fontSize="xs" color={currentScheme.colors.text} noOfLines={1}>
              {nodeData.eventType ? getEventLabel(nodeData.eventType) : 'Select Event'}
            </Text>
            </HStack>
          </Box>
          
          {nodeData.description && (
            <Box>
              <Text fontSize="xs" color={currentScheme.colors.textSecondary} noOfLines={1}>
                {nodeData.description.length > 25 ? nodeData.description.substring(0, 25) + '...' : nodeData.description}
              </Text>
            </Box>
          )}
          
            <HStack spacing={1} flexWrap="wrap">
            {(nodeData.filters?.length ?? 0) > 0 && (
              <Badge size="xs" colorScheme="green">
                {nodeData.filters?.length} filter{(nodeData.filters?.length ?? 0) !== 1 ? 's' : ''}
                </Badge>
            )}
            {nodeData.ignoreBot && (
              <Badge size="xs" colorScheme="orange">
                No Bots
              </Badge>
            )}
            {nodeData.rateLimited && (
              <Badge size="xs" colorScheme="yellow">
                Rate Limited
              </Badge>
            )}
            </HStack>
        </VStack>
        
        <Handle
          type="source"
          position={Position.Bottom}
          style={{
            background: '#10b981',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            bottom: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
      </Box>

      {/* Enhanced Configuration Modal */}
      <Modal isOpen={isOpen} onClose={handleModalClose} size="4xl">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent bg={currentScheme.colors.background} border="2px solid" borderColor="green.400" maxW="1200px">
          <ModalHeader color={currentScheme.colors.text}>
            📡 Configure Event
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={6} align="stretch">
              {/* Variables Helper */}
              <Box>
                <HStack justify="space-between" align="center" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    Available Variables
                </Text>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={showVariables ? <FiEyeOff /> : <FiEye />}
                    onClick={() => setShowVariables(!showVariables)}
                  >
                    {showVariables ? 'Hide' : 'Show'} Variables
                  </Button>
                </HStack>
                <Alert status="info" borderRadius="md" mb={2}>
                  <AlertIcon />
                  <AlertDescription fontSize="sm">
                    💡 Use variables in your event responses! Click any variable below to copy it. Variables are replaced with actual values when your event triggers.
                  </AlertDescription>
                </Alert>
                {renderVariablesList()}
              </Box>

              <Divider />

              {/* Tabbed Configuration */}
              <Tabs variant="enclosed" colorScheme="green">
                <TabList>
                  <Tab>Event Type</Tab>
                  <Tab>Filters</Tab>
                  <Tab>Settings</Tab>
                  <Tab>Advanced</Tab>
                </TabList>
                
                <TabPanels>
                  {/* Event Type Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <FormControl isRequired>
                    <FormLabel color={currentScheme.colors.text}>Event Type</FormLabel>
                    <Select
                      value={nodeData.eventType || ''}
                      onChange={(e) => updateNodeData({ eventType: e.target.value })}
                      placeholder="Select an event type"
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                    >
                      {Object.entries(
                        eventTypes.reduce((acc, event) => {
                          if (!acc[event.category]) acc[event.category] = [];
                          acc[event.category].push(event);
                          return acc;
                        }, {} as Record<string, typeof eventTypes>)
                      ).map(([category, events]) => (
                        <optgroup key={category} label={category}>
                          {events.map((event) => (
                            <option key={event.value} value={event.value}>
                              {event.label}
                            </option>
                          ))}
                        </optgroup>
                      ))}
                    </Select>
                  </FormControl>
                      
                      {nodeData.eventType && (
                        <Alert status="info" borderRadius="md">
                          <AlertIcon />
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={1}>
                              {eventTypes.find(e => e.value === nodeData.eventType)?.label}
                            </Text>
                            <Text fontSize="sm">
                              {eventTypes.find(e => e.value === nodeData.eventType)?.description}
                            </Text>
                          </Box>
                        </Alert>
                      )}
                  
                  <FormControl>
                    <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                    <Textarea
                      value={nodeData.description || ''}
                      onChange={(e) => updateNodeData({ description: e.target.value })}
                      placeholder="Describe when this event should trigger"
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                          minH="80px"
                    />
                  </FormControl>
                </VStack>
                  </TabPanel>
                  
                  {/* Filters Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                          Event Filters
                        </Text>
                        <Button
                          leftIcon={<FiPlus />}
                          onClick={addFilter}
                          colorScheme="green"
                          size="sm"
                        >
                          Add Filter
                        </Button>
                      </HStack>
                      
                      <Alert status="info" borderRadius="md">
                        <AlertIcon />
                        <AlertDescription fontSize="sm">
                          Filters determine when this event should trigger. Only events that pass all filters will execute the connected actions.
                        </AlertDescription>
                      </Alert>
                      
                      <VStack spacing={4} align="stretch">
                        {nodeData.filters?.map((filter, index) => (
                          <Box
                            key={index}
                            p={4}
                            bg={currentScheme.colors.surface}
                            borderRadius="md"
                            border="1px solid"
                            borderColor={currentScheme.colors.border}
                          >
                            <HStack justify="space-between" align="center" mb={3}>
                              <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text}>
                                Filter {index + 1}
                </Text>
                        <IconButton
                                icon={<FiTrash2 />}
                                size="sm"
                          colorScheme="red"
                                variant="ghost"
                                onClick={() => removeFilter(index)}
                          aria-label="Remove filter"
                        />
                      </HStack>
                            
                            <VStack spacing={3} align="stretch">
                              <SimpleGrid columns={2} spacing={3}>
                        <FormControl>
                          <FormLabel fontSize="sm" color={currentScheme.colors.text}>Filter Type</FormLabel>
                          <Select
                            value={filter.type}
                                    onChange={(e) => updateFilter(index, { type: e.target.value as EventFilter['type'] })}
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                                    borderColor={currentScheme.colors.border}
                                    size="sm"
                                  >
                                    {filterTypes.map((type) => (
                                      <option key={type.value} value={type.value}>
                                        {type.label}
                                      </option>
                                    ))}
                          </Select>
                        </FormControl>
                                
                        <FormControl>
                          <FormLabel fontSize="sm" color={currentScheme.colors.text}>Operator</FormLabel>
                          <Select
                            value={filter.operator || 'equals'}
                                    onChange={(e) => updateFilter(index, { operator: e.target.value as EventFilter['operator'] })}
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                                    borderColor={currentScheme.colors.border}
                                    size="sm"
                          >
                            <option value="equals">Equals</option>
                            <option value="contains">Contains</option>
                            <option value="startsWith">Starts With</option>
                            <option value="endsWith">Ends With</option>
                            <option value="regex">Regex</option>
                          </Select>
                        </FormControl>
                      </SimpleGrid>
                              
                              <FormControl>
                                <FormLabel fontSize="sm" color={currentScheme.colors.text}>Filter Value</FormLabel>
                        <Input
                          value={filter.value}
                                  onChange={(e) => updateFilter(index, { value: e.target.value })}
                                  placeholder={
                                    filter.type === 'channel' ? 'general or {channel.name}' :
                                    filter.type === 'role' ? 'Member or {role.name}' :
                                    filter.type === 'user' ? 'username or {user.id}' :
                                    filter.type === 'regex' ? '^Hello.*' :
                                    filter.type === 'content' ? 'hello world' :
                                    'Filter value'
                                  }
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                                  size="sm"
                        />
                      </FormControl>
                              
                              <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                                {filterTypes.find(t => t.value === filter.type)?.description}
                              </Text>
                            </VStack>
                    </Box>
                  ))}
                        
                        {(!nodeData.filters || nodeData.filters.length === 0) && (
                          <Alert status="info" borderRadius="md">
                            <AlertIcon />
                            <AlertDescription>
                              No filters configured. This event will trigger for all occurrences of the selected event type.
                            </AlertDescription>
                          </Alert>
                        )}
                      </VStack>
                </VStack>
                  </TabPanel>

                  {/* Settings Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                        Event Settings
                </Text>
                      
                      <VStack spacing={4} align="stretch">
                        <HStack spacing={4}>
                      <Switch
                            isChecked={nodeData.ignoreBot}
                        onChange={(e) => updateNodeData({ ignoreBot: e.target.checked })}
                            colorScheme="green"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Ignore Bot Messages
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Don't trigger on messages from bots (recommended)
                            </Text>
                          </VStack>
                        </HStack>
                        
                        <HStack spacing={4}>
                      <Switch
                            isChecked={nodeData.ignoreSystem}
                        onChange={(e) => updateNodeData({ ignoreSystem: e.target.checked })}
                            colorScheme="green"
                      />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Ignore System Messages
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Don't trigger on Discord system messages
                            </Text>
                          </VStack>
                        </HStack>
                        
                        <HStack spacing={4}>
                      <Switch
                            isChecked={nodeData.rateLimited}
                        onChange={(e) => updateNodeData({ rateLimited: e.target.checked })}
                            colorScheme="orange"
                      />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Rate Limited
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Limit how often this event can trigger
                            </Text>
                          </VStack>
                        </HStack>
                    
                    {nodeData.rateLimited && (
                      <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Rate Limit (milliseconds)</FormLabel>
                            <NumberInput
                              value={nodeData.rateLimit || 1000}
                              onChange={(valueString) => updateNodeData({ rateLimit: parseInt(valueString) || 1000 })}
                              min={100}
                              max={60000}
                            >
                              <NumberInputField
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                        />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                              Minimum time between triggers (1000ms = 1 second)
                            </Text>
                      </FormControl>
                    )}
                      </VStack>
                </VStack>
                  </TabPanel>

                  {/* Advanced Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                        Advanced Settings
                </Text>
                      
                  <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Event Priority</FormLabel>
                        <NumberInput
                          value={nodeData.priority || 1}
                          onChange={(valueString) => updateNodeData({ priority: parseInt(valueString) || 1 })}
                          min={1}
                          max={10}
                        >
                          <NumberInputField
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                    />
                          <NumberInputStepper>
                            <NumberIncrementStepper />
                            <NumberDecrementStepper />
                          </NumberInputStepper>
                        </NumberInput>
                        <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                          Higher priority events execute first (1 = highest, 10 = lowest)
                        </Text>
                  </FormControl>
                  
                      <VStack spacing={4} align="stretch">
                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.async}
                            onChange={(e) => updateNodeData({ async: e.target.checked })}
                            colorScheme="green"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Async Processing
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Don't wait for this event to complete before processing others
                            </Text>
                          </VStack>
                        </HStack>
                        
                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.retryOnError}
                            onChange={(e) => updateNodeData({ retryOnError: e.target.checked })}
                            colorScheme="red"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Retry on Error
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Automatically retry if event processing fails
                            </Text>
                          </VStack>
                        </HStack>
                        
                        {nodeData.retryOnError && (
                  <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Max Retries</FormLabel>
                            <NumberInput
                              value={nodeData.maxRetries || 3}
                              onChange={(valueString) => updateNodeData({ maxRetries: parseInt(valueString) || 3 })}
                              min={1}
                              max={10}
                            >
                              <NumberInputField
                      bg={currentScheme.colors.background}
                      color={currentScheme.colors.text}
                      borderColor={currentScheme.colors.border}
                    />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                              Maximum number of retry attempts
                            </Text>
                  </FormControl>
                        )}
                      </VStack>
                </VStack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
              
              <Button
                colorScheme="green"
                                  onClick={() => {
                    // Save the configuration
                    data.eventType = nodeData.eventType;
                  data.description = nodeData.description;
                    data.filters = nodeData.filters;
                    data.ignoreBot = nodeData.ignoreBot;
                    data.ignoreSystem = nodeData.ignoreSystem;
                    data.rateLimited = nodeData.rateLimited;
                    data.rateLimit = nodeData.rateLimit;
                  data.priority = nodeData.priority;
                  data.async = nodeData.async;
                  data.retryOnError = nodeData.retryOnError;
                  data.maxRetries = nodeData.maxRetries;
                    data.channelRestrictions = nodeData.channelRestrictions;
                    data.roleRestrictions = nodeData.roleRestrictions;
                    data.label = nodeData.eventType ? getEventLabel(nodeData.eventType) : 'Event';
                    onClose();
                  }}
                size="lg"
                width="full"
              >
                Save Configuration
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
});

EventNode.displayName = 'EventNode';

export default EventNode; 