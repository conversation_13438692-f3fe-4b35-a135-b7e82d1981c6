(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9640],{33233:(e,s,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/guilds",function(){return i(63618)}])},63618:(e,s,i)=>{"use strict";i.r(s),i.d(s,{__N_SSP:()=>et,default:()=>eo});var n=i(94513),l=i(57561),r=i(22907),t=i(95845),o=i(59001),c=i(79156),a=i(7746),d=i(51961),h=i(78902),x=i(31678),j=i(49217),m=i(41611),u=i(62690),g=i(91047),p=i(83881),b=i(47402),f=i(99820),y=i(72671),S=i(68443),v=i(20429),E=i(59818),C=i(25680),T=i(40443),z=i(63730),k=i(64057),w=i(15373),I=i(51927),A=i(8595),N=i(1341),D=i(22237),_=i(35981),M=i(95497),R=i(71601),O=i(24792),P=i(73011),G=i(25964),B=i(71185),F=i(61481),$=i(60341),L=i(52826),K=i(94285),X=i(97146),W=i(17842),J=i(77072),V=i.n(J),Z=i(12772);let Y=V()(()=>Promise.all([i.e(9998),i.e(1677)]).then(i.bind(i,11677)),{loadableGenerated:{webpack:()=>[11677]},loading:()=>(0,n.jsx)(l.y,{size:"md"}),ssr:!1}),Q=V()(()=>Promise.all([i.e(9998),i.e(2213)]).then(i.bind(i,2213)),{loadableGenerated:{webpack:()=>[2213]},loading:()=>(0,n.jsx)(l.y,{size:"md"}),ssr:!1}),H=V()(()=>Promise.all([i.e(9998),i.e(5872)]).then(i.bind(i,35872)),{loadableGenerated:{webpack:()=>[35872]},loading:()=>(0,n.jsx)(l.y,{size:"md"}),ssr:!1}),U=V()(()=>Promise.all([i.e(9998),i.e(7090)]).then(i.bind(i,67090)),{loadableGenerated:{webpack:()=>[67090]},loading:()=>(0,n.jsx)(l.y,{size:"md"}),ssr:!1}),q=V()(()=>Promise.all([i.e(9998),i.e(152)]).then(i.bind(i,10152)),{loadableGenerated:{webpack:()=>[10152]},loading:()=>(0,n.jsx)(l.y,{size:"md"}),ssr:!1}),ee=V()(()=>Promise.all([i.e(9998),i.e(627)]).then(i.bind(i,60627)),{loadableGenerated:{webpack:()=>[60627]},loading:()=>(0,n.jsx)(l.y,{size:"md"}),ssr:!1});function es(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:2e3,[s,i]=(0,K.useState)(!1),n=(0,K.useRef)(null),l=(0,K.useCallback)(()=>{n.current&&clearTimeout(n.current),i(!0),n.current=setTimeout(()=>{i(!1)},e)},[e]);return(0,K.useEffect)(()=>()=>{n.current&&clearTimeout(n.current)},[]),[s,l]}let ei={0:{icon:X.mEP,color:"blue",label:"Text"},2:{icon:X.o77,color:"green",label:"Voice"},4:{icon:X.GsE,color:"purple",label:"Category"},5:{icon:X.DQs,color:"orange",label:"Announcement"},11:{icon:X.X6_,color:"cyan",label:"Public Thread"},12:{icon:X.F5$,color:"pink",label:"Private Thread"},13:{icon:X.Qz2,color:"teal",label:"Stage Voice"},15:{icon:X.Qz2,color:"gray",label:"Forum"}},en={ADMINISTRATOR:{color:"red",label:"Admin"},MANAGE_GUILD:{color:"orange",label:"Manage Server"},MANAGE_ROLES:{color:"yellow",label:"Manage Roles"},MANAGE_CHANNELS:{color:"green",label:"Manage Channels"},KICK_MEMBERS:{color:"purple",label:"Kick"},BAN_MEMBERS:{color:"pink",label:"Ban"},MANAGE_MESSAGES:{color:"blue",label:"Manage Messages"},MENTION_EVERYONE:{color:"cyan",label:"Mention @everyone"}},el={ADMINISTRATOR:1n<<3n,MANAGE_GUILD:1n<<5n,MANAGE_ROLES:1n<<28n,MANAGE_CHANNELS:1n<<4n,KICK_MEMBERS:1n<<1n,BAN_MEMBERS:1n<<2n,MANAGE_MESSAGES:1n<<13n,MENTION_EVERYONE:1n<<17n};function er(e){if(!e)return[];if(Array.isArray(e))return e;try{let s=[],i=BigInt(e);for(let[e,n]of Object.entries(el))(i&n)===n&&s.push(e);return s}catch(e){return[]}}var et=!0;function eo(){let e=(0,r.d)(),{displayName:s}=(0,L.A)(),[i,J]=es(),[V,el]=es(5e3),{currentScheme:et,setColorScheme:eo,customSchemes:ec,deleteCustomScheme:ea}=(0,Z.DP)(),[ed,eh]=(0,K.useState)({prefix:"!",botName:"Bot",guildName:"",guildId:"",guildIcon:null,activities:[{type:"PLAYING",name:"with Discord.js"}],activityRotationInterval:60}),[ex,ej]=(0,K.useState)([]),[em,eu]=(0,K.useState)([]),[eg,ep]=(0,K.useState)(!0),[eb,ef]=(0,K.useState)(!0),[ey,eS]=(0,K.useState)(!1),[ev,eE]=(0,K.useState)(!1),{isOpen:eC,onOpen:eT,onClose:ez}=(0,t.j)(),{isOpen:ek,onOpen:ew,onClose:eI}=(0,t.j)(),{isOpen:eA,onOpen:eN,onClose:eD}=(0,t.j)(),{isOpen:e_,onOpen:eM,onClose:eR}=(0,t.j)(),{isOpen:eO,onOpen:eP,onClose:eG}=(0,t.j)(),{isOpen:eB,onOpen:eF,onClose:e$}=(0,t.j)(),[eL,eK]=(0,K.useState)(null),[eX,eW]=(0,K.useState)(null),[eJ,eV]=(0,K.useState)([]),[eZ,eY]=(0,K.useState)([]),[eQ,eH]=(0,K.useState)(!1),[eU,eq]=(0,K.useState)(null),[e0,e1]=(0,K.useState)(null);(0,K.useRef)(null);let e3=async()=>{try{let[e,s,i]=await Promise.all([fetch("/api/discord/guild"),fetch("/api/discord/roles"),fetch("/api/discord/presence")]);if(e.ok){let s=await e.json();eh(e=>({...e,guildName:s.name,guildId:s.id,guildIcon:s.icon,botName:s.botName||e.botName}))}if(s.ok){let e=await s.json(),i=Array.isArray(e)?e:e.roles||[];ej(i.sort((e,s)=>s.position-e.position))}if(i.ok){let e=await i.json();eh(s=>({...s,activities:e.activities||[],activityRotationInterval:e.activityRotationInterval||60}))}}catch(s){e({title:"Error",description:"Failed to fetch guild data",status:"error",duration:3e3})}finally{ep(!1)}},e2=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let s=(await e.json()||[]).sort((e,s)=>4===e.type&&4!==s.type?-1:4!==e.type&&4===s.type?1:e.position-s.position);eu(s)}catch(s){e({title:"Error",description:"Failed to fetch channels",status:"error",duration:5e3})}finally{ef(!1)}};(0,K.useEffect)(()=>{e3(),e2()},[]);let e5=(e,s)=>{eh(i=>({...i,[e]:s}))},e6=async()=>{if(!ey&&!i){eS(!0),J();try{if((await fetch("/api/discord/settings",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(ed)})).ok)e({title:"Success",description:"Settings saved successfully",status:"success",duration:3e3});else throw Error("Failed to save settings")}catch(s){e({title:"Error",description:"Failed to save settings",status:"error",duration:3e3})}finally{eS(!1)}}},e4=e=>{eK(e),eM()},e7=e=>{eW(e),ew()},e9=async s=>{if(!i)try{J(),(await fetch("/api/discord/channels/".concat(s),{method:"DELETE"})).ok&&(await e2(),e({title:"Success",description:"Channel deleted successfully",status:"success",duration:3e3}))}catch(s){e({title:"Error",description:"Failed to delete channel",status:"error",duration:3e3})}},e8=e=>{if(!e||!em)return"-";let s=em.find(s=>s.id===e);return s?s.name:"-"},se=async()=>{if(0!==eJ.length&&!eQ&&!V){eH(!0),el();try{let s=await fetch("/api/discord/roles/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({roleIds:eJ})}),i=await s.json();if(s.ok)e({title:"Success",description:i.message,status:"success",duration:5e3}),eV([]),e3();else throw Error(i.error||"Failed to delete roles")}catch(s){e({title:"Error",description:s.message||"Failed to delete roles",status:"error",duration:5e3})}finally{eH(!1)}}},ss=async()=>{if(0!==eZ.length&&!eQ&&!V){eH(!0),el();try{let s=await fetch("/api/discord/channels/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({channelIds:eZ})}),i=await s.json();if(s.ok)e({title:"Success",description:i.message,status:"success",duration:5e3}),eY([]),e2();else throw Error(i.error||"Failed to delete channels")}catch(s){e({title:"Error",description:s.message||"Failed to delete channels",status:"error",duration:5e3})}finally{eH(!1)}}},si=e=>{eV(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},sn=e=>{eY(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},sl=(e,s,i)=>{let n=[...ed.activities];n[e]={...n[e],[s]:i},eh(e=>({...e,activities:n}))},sr=e=>{let s=ed.activities.filter((s,i)=>i!==e);eh(e=>({...e,activities:s}))},st=async()=>{if(!ev&&!i){eE(!0),J();try{let s=await fetch("/api/discord/presence",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({activities:ed.activities,activityRotationInterval:ed.activityRotationInterval})});if(s.ok)e({title:"Success",description:"Presence settings saved successfully",status:"success",duration:3e3});else{let e=await s.json();throw Error(e.error||"Failed to save presence settings")}}catch(s){e({title:"Error",description:s.message,status:"error",duration:5e3})}finally{eE(!1)}}};return eg?(0,n.jsx)($.A,{children:(0,n.jsx)(o.m,{maxW:"container.xl",py:8,children:(0,n.jsxs)(c.T,{spacing:6,children:[(0,n.jsx)(a.E,{height:"60px"}),(0,n.jsx)(a.E,{height:"400px"})]})})}):(0,n.jsx)($.A,{children:(0,n.jsxs)(o.m,{maxW:"container.xl",py:8,children:[(0,n.jsxs)(c.T,{spacing:8,align:"stretch",children:[(0,n.jsx)(d.a,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"blue.400",boxShadow:"0 0 15px rgba(66, 153, 225, 0.4)",children:(0,n.jsxs)(h.z,{justify:"space-between",align:"center",children:[(0,n.jsxs)(d.a,{children:[(0,n.jsxs)(x.D,{size:"xl",bgGradient:"linear(to-r, blue.300, purple.400)",bgClip:"text",children:[(0,n.jsx)(j.I,{as:X.LIi,mr:3}),"Server Management"]}),(0,n.jsxs)(m.E,{color:"gray.300",mt:2,children:["Comprehensive management for ",s||ed.guildName]})]}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.Bc_,{}),colorScheme:"blue",onClick:e6,isLoading:ey,isDisabled:i,size:"lg",children:"Save Settings"})]})}),(0,n.jsxs)(g.t,{colorScheme:"blue",isLazy:!0,children:[(0,n.jsxs)(p.w,{children:[(0,n.jsxs)(b.o,{children:[(0,n.jsx)(j.I,{as:X.VSk,mr:2}),"General Settings"]}),(0,n.jsxs)(b.o,{children:[(0,n.jsx)(j.I,{as:W.lV_,mr:2}),"Theme Builder"]}),(0,n.jsxs)(b.o,{children:[(0,n.jsx)(j.I,{as:X.i5c,mr:2}),"Builders"]}),(0,n.jsxs)(b.o,{children:[(0,n.jsx)(j.I,{as:X.FrA,mr:2}),"Automation"]})]}),(0,n.jsxs)(f.T,{children:[(0,n.jsx)(y.K,{children:(0,n.jsxs)(c.T,{spacing:8,align:"stretch",children:[(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(x.D,{size:"md",children:"Basic Settings"})}),(0,n.jsx)(E.b,{children:(0,n.jsxs)(C.r,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{children:"Bot Name"}),(0,n.jsx)(k.p,{value:ed.botName,onChange:e=>e5("botName",e.target.value),placeholder:"Enter bot name"})]}),(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{children:"Command Prefix"}),(0,n.jsx)(k.p,{value:ed.prefix,onChange:e=>e5("prefix",e.target.value),placeholder:"Enter command prefix"})]})]}),(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{children:"Server Name"}),(0,n.jsx)(k.p,{value:ed.guildName||"",isReadOnly:!0,bg:"gray.50",_dark:{bg:"gray.700"}})]}),(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{children:"Server ID"}),(0,n.jsx)(k.p,{value:ed.guildId||"",isReadOnly:!0,bg:"gray.50",_dark:{bg:"gray.700"},fontFamily:"mono",fontSize:"sm"})]})]})]})})]}),(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(h.z,{justify:"space-between",children:[(0,n.jsxs)(x.D,{size:"md",children:[(0,n.jsx)(j.I,{as:X.cfS,mr:2}),"Roles (",ex.length,")"]}),(0,n.jsx)(h.z,{spacing:2,children:(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.GGD,{}),colorScheme:"green",onClick:()=>{eN()},isDisabled:i,size:"sm",children:"Create Role"})})]}),eJ.length>0&&(0,n.jsxs)(h.z,{justify:"space-between",p:3,bg:"blue.50",_dark:{bg:"blue.900"},borderRadius:"md",children:[(0,n.jsxs)(m.E,{fontSize:"sm",fontWeight:"medium",children:[eJ.length," role(s) selected"]}),(0,n.jsxs)(h.z,{spacing:2,children:[(0,n.jsx)(u.$,{size:"sm",variant:"outline",onClick:()=>eV([]),children:"Clear Selection"}),(0,n.jsx)(u.$,{size:"sm",colorScheme:"red",leftIcon:(0,n.jsx)(X.IXo,{}),onClick:se,isLoading:eQ,isDisabled:V,children:"Delete Selected"})]})]})]})}),(0,n.jsx)(E.b,{children:eg?(0,n.jsx)(c.T,{spacing:3,children:[void 0,void 0,void 0].map((e,s)=>(0,n.jsx)(a.E,{height:"60px"},s))}):(0,n.jsx)(d.a,{overflowX:"auto",children:(0,n.jsxs)(w.X,{variant:"simple",size:"sm",children:[(0,n.jsx)(I.d,{children:(0,n.jsxs)(A.Tr,{children:[(0,n.jsx)(N.Th,{children:(0,n.jsx)(D.S,{isChecked:eJ.length===ex.filter(e=>"@everyone"!==e.name).length&&ex.length>1,isIndeterminate:eJ.length>0&&eJ.length<ex.filter(e=>"@everyone"!==e.name).length,onChange:()=>{let e=ex.filter(e=>"@everyone"!==e.name).map(e=>e.id);eV(eJ.length===e.length?[]:e)}})}),(0,n.jsx)(N.Th,{children:"Role"}),(0,n.jsx)(N.Th,{children:"Members"}),(0,n.jsx)(N.Th,{children:"Permissions"}),(0,n.jsx)(N.Th,{children:"Actions"})]})}),(0,n.jsx)(_.N,{children:(ex||[]).map(e=>(0,n.jsxs)(A.Tr,{children:[(0,n.jsx)(M.Td,{children:(0,n.jsx)(D.S,{isChecked:eJ.includes(e.id),onChange:()=>si(e.id),isDisabled:"@everyone"===e.name})}),(0,n.jsx)(M.Td,{children:(0,n.jsxs)(h.z,{children:[(0,n.jsx)(d.a,{w:4,h:4,rounded:"full",bg:e.color?"#".concat(e.color.toString(16).padStart(6,"0")):"gray.500"}),(0,n.jsx)(m.E,{children:e.name})]})}),(0,n.jsx)(M.Td,{children:(0,n.jsx)(R.E,{colorScheme:"blue",children:"0"})}),(0,n.jsx)(M.Td,{children:(0,n.jsxs)(h.z,{wrap:"wrap",spacing:1,children:[(er(e.permissions)||[]).slice(0,3).map(e=>{var s,i;return(0,n.jsx)(R.E,{colorScheme:(null==(s=en[e])?void 0:s.color)||"gray",size:"sm",children:(null==(i=en[e])?void 0:i.label)||e},e)}),er(e.permissions).length>3&&(0,n.jsxs)(R.E,{colorScheme:"gray",size:"sm",children:["+",er(e.permissions).length-3]})]})}),(0,n.jsx)(M.Td,{children:(0,n.jsxs)(h.z,{spacing:2,children:[(0,n.jsx)(O.m,{label:"Edit Role",children:(0,n.jsx)(P.K,{"aria-label":"Edit role",icon:(0,n.jsx)(X.WXf,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>e4(e),isDisabled:i})}),(0,n.jsx)(O.m,{label:"Delete Role",children:(0,n.jsx)(P.K,{"aria-label":"Delete role",icon:(0,n.jsx)(X.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",isDisabled:i})})]})})]},e.id))})]})})})]}),(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsxs)(h.z,{justify:"space-between",children:[(0,n.jsxs)(x.D,{size:"md",children:[(0,n.jsx)(j.I,{as:X.Qz2,mr:2}),"Channels (",em.length,")"]}),(0,n.jsx)(h.z,{spacing:2,children:(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.GGD,{}),colorScheme:"blue",onClick:eT,size:"sm",children:"Create Channel"})})]}),eZ.length>0&&(0,n.jsxs)(h.z,{justify:"space-between",p:3,bg:"blue.50",_dark:{bg:"blue.900"},borderRadius:"md",children:[(0,n.jsxs)(m.E,{fontSize:"sm",fontWeight:"medium",children:[eZ.length," channel(s) selected"]}),(0,n.jsxs)(h.z,{spacing:2,children:[(0,n.jsx)(u.$,{size:"sm",variant:"outline",onClick:()=>eY([]),children:"Clear Selection"}),(0,n.jsx)(u.$,{size:"sm",colorScheme:"red",leftIcon:(0,n.jsx)(X.IXo,{}),onClick:ss,isLoading:eQ,isDisabled:V,children:"Delete Selected"})]})]})]})}),(0,n.jsx)(E.b,{children:eb?(0,n.jsx)(c.T,{spacing:3,children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,n.jsx)(a.E,{height:"50px"},s))}):0===em.length?(0,n.jsx)(m.E,{color:"gray.500",textAlign:"center",py:8,children:"No channels found"}):(0,n.jsx)(d.a,{overflowX:"auto",children:(0,n.jsxs)(w.X,{variant:"simple",size:"sm",children:[(0,n.jsx)(I.d,{children:(0,n.jsxs)(A.Tr,{children:[(0,n.jsx)(N.Th,{children:(0,n.jsx)(D.S,{isChecked:eZ.length===em.length&&em.length>0,isIndeterminate:eZ.length>0&&eZ.length<em.length,onChange:()=>{let e=em.map(e=>e.id);eY(eZ.length===e.length?[]:e)}})}),(0,n.jsx)(N.Th,{children:"Name"}),(0,n.jsx)(N.Th,{children:"Type"}),(0,n.jsx)(N.Th,{children:"Category"}),(0,n.jsx)(N.Th,{children:"Position"}),(0,n.jsx)(N.Th,{children:"Actions"})]})}),(0,n.jsx)(_.N,{children:(em||[]).map(e=>{let s=ei[e.type]||{icon:X.mEP,color:"gray",label:"Other"};return(0,n.jsxs)(A.Tr,{children:[(0,n.jsx)(M.Td,{children:(0,n.jsx)(D.S,{isChecked:eZ.includes(e.id),onChange:()=>sn(e.id)})}),(0,n.jsx)(M.Td,{children:(0,n.jsxs)(h.z,{children:[(0,n.jsx)(j.I,{as:s.icon,color:"".concat(s.color,".400")}),(0,n.jsx)(m.E,{children:e.name})]})}),(0,n.jsx)(M.Td,{children:(0,n.jsx)(R.E,{colorScheme:s.color,children:s.label})}),(0,n.jsx)(M.Td,{children:(0,n.jsx)(m.E,{color:"gray.500",children:e8(e.parent_id)})}),(0,n.jsx)(M.Td,{children:(0,n.jsx)(m.E,{color:"gray.500",children:e.position})}),(0,n.jsx)(M.Td,{children:(0,n.jsxs)(h.z,{spacing:2,children:[(0,n.jsx)(O.m,{label:"Edit Channel",children:(0,n.jsx)(P.K,{"aria-label":"Edit channel",icon:(0,n.jsx)(X.WXf,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>e7(e),isDisabled:i})}),(0,n.jsx)(O.m,{label:"Delete Channel",children:(0,n.jsx)(P.K,{"aria-label":"Delete channel",icon:(0,n.jsx)(X.IXo,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>e9(e.id),isDisabled:i})})]})})]},e.id)})})]})})})]})]})}),(0,n.jsx)(y.K,{children:(0,n.jsxs)(c.T,{spacing:6,align:"stretch",children:[(0,n.jsxs)(d.a,{children:[(0,n.jsx)(x.D,{size:"md",mb:4,children:"\uD83C\uDFA8 Theme Builder"}),(0,n.jsx)(m.E,{color:"gray.600",_dark:{color:"gray.300"},mb:6,children:"Create and customize your own themes with the advanced color builder"})]}),(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(h.z,{justify:"space-between",align:"center",children:[(0,n.jsx)(x.D,{size:"sm",children:"Theme Builder & Presets"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(W.lV_,{}),colorScheme:"purple",onClick:eP,size:"sm",children:"Create Custom Theme"})]})}),(0,n.jsx)(E.b,{children:(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsx)(m.E,{fontSize:"sm",color:"gray.500",mb:2,children:"Choose from pre-built themes or create your own custom theme"}),(0,n.jsxs)(c.T,{spacing:3,align:"stretch",children:[(0,n.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:"gray.400",textTransform:"uppercase",children:"Built-in Themes"}),(0,n.jsx)(C.r,{columns:{base:1,md:2,lg:3},spacing:3,children:Z.nk.map(e=>(0,n.jsx)(d.a,{p:3,border:"1px",borderColor:et.id===e.id?e.colors.primary:"gray.200",borderRadius:"md",cursor:"pointer",transition:"all 0.2s",bg:et.id===e.id?"".concat(e.colors.primary,"10"):"transparent",_hover:{borderColor:e.colors.primary,transform:"translateY(-1px)"},onClick:()=>eo(e.id),children:(0,n.jsxs)(c.T,{spacing:2,align:"stretch",children:[(0,n.jsxs)(h.z,{justify:"space-between",children:[(0,n.jsx)(m.E,{fontSize:"sm",fontWeight:"medium",children:e.name}),et.id===e.id&&(0,n.jsx)(R.E,{colorScheme:"green",size:"sm",children:"Active"})]}),(0,n.jsx)(m.E,{fontSize:"xs",color:"gray.500",lineHeight:"1.3",children:e.description}),(0,n.jsxs)(h.z,{spacing:1,children:[(0,n.jsx)(d.a,{w:3,h:3,bg:e.colors.primary,borderRadius:"full"}),(0,n.jsx)(d.a,{w:3,h:3,bg:e.colors.secondary,borderRadius:"full"}),(0,n.jsx)(d.a,{w:3,h:3,bg:e.colors.accent,borderRadius:"full"}),(0,n.jsx)(d.a,{w:3,h:3,bg:e.colors.success,borderRadius:"full"})]})]})},e.id))})]}),ec.length>0&&(0,n.jsxs)(c.T,{spacing:3,align:"stretch",children:[(0,n.jsx)(m.E,{fontSize:"xs",fontWeight:"bold",color:"gray.400",textTransform:"uppercase",children:"Custom Themes"}),(0,n.jsx)(C.r,{columns:{base:1,md:2,lg:3},spacing:3,children:ec.map(s=>(0,n.jsx)(d.a,{p:3,border:"1px",borderColor:et.id===s.id?s.colors.primary:"gray.200",borderRadius:"md",position:"relative",transition:"all 0.2s",bg:et.id===s.id?"".concat(s.colors.primary,"10"):"transparent",_hover:{borderColor:s.colors.primary,transform:"translateY(-1px)"},children:(0,n.jsxs)(c.T,{spacing:2,align:"stretch",children:[(0,n.jsxs)(h.z,{justify:"space-between",children:[(0,n.jsx)(m.E,{fontSize:"sm",fontWeight:"medium",cursor:"pointer",onClick:()=>eo(s.id),flex:"1",children:s.name}),(0,n.jsxs)(h.z,{spacing:1,children:[et.id===s.id&&(0,n.jsx)(R.E,{colorScheme:"green",size:"sm",children:"Active"}),(0,n.jsx)(R.E,{colorScheme:"purple",size:"sm",children:"Custom"}),(0,n.jsx)(O.m,{label:"Delete Custom Theme",children:(0,n.jsx)(P.K,{"aria-label":"Delete theme",icon:(0,n.jsx)(X.IXo,{}),size:"xs",variant:"ghost",colorScheme:"red",onClick:i=>{i.stopPropagation(),ea(s.id),e({title:"Theme Deleted",description:"".concat(s.name," has been deleted"),status:"success",duration:3e3})}})})]})]}),(0,n.jsx)(m.E,{fontSize:"xs",color:"gray.500",lineHeight:"1.3",cursor:"pointer",onClick:()=>eo(s.id),children:s.description}),(0,n.jsxs)(h.z,{spacing:1,cursor:"pointer",onClick:()=>eo(s.id),children:[(0,n.jsx)(d.a,{w:3,h:3,bg:s.colors.primary,borderRadius:"full"}),(0,n.jsx)(d.a,{w:3,h:3,bg:s.colors.secondary,borderRadius:"full"}),(0,n.jsx)(d.a,{w:3,h:3,bg:s.colors.accent,borderRadius:"full"}),(0,n.jsx)(d.a,{w:3,h:3,bg:s.colors.success,borderRadius:"full"})]})]})},s.id))})]})]})})]})]})}),(0,n.jsx)(y.K,{children:(0,n.jsxs)(c.T,{spacing:6,align:"stretch",children:[(0,n.jsxs)(d.a,{children:[(0,n.jsx)(x.D,{size:"md",mb:4,children:"\uD83D\uDEE0️ Builders & Tools"}),(0,n.jsx)(m.E,{color:"gray.600",_dark:{color:"gray.300"},mb:6,children:"Create custom content and manage server features with powerful builders"})]}),(0,n.jsxs)(C.r,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(x.D,{size:"sm",children:"Content Builders"})}),(0,n.jsx)(E.b,{children:(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.FrA,{}),colorScheme:"green",onClick:()=>window.open("/admin/experimental/addon-builder","_blank"),children:"Addon Builder"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.mEP,{}),colorScheme:"blue",onClick:()=>window.open("/admin/applications-builder","_blank"),children:"Applications Builder"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.mEP,{}),colorScheme:"purple",onClick:()=>window.open("/admin/embed-builder","_blank"),isDisabled:!0,children:"Message Builder (Coming Soon)"})]})})]}),(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(x.D,{size:"sm",children:"Management Tools"})}),(0,n.jsx)(E.b,{children:(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.VSk,{}),colorScheme:"orange",onClick:()=>window.open("/admin/addons","_blank"),children:"Manage Addons"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.VSk,{}),colorScheme:"teal",onClick:()=>window.open("/admin/commands","_blank"),children:"Command Manager"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.VSk,{}),colorScheme:"cyan",onClick:()=>window.open("/admin/applications","_blank"),children:"Application Manager"})]})})]})]})]})}),(0,n.jsx)(y.K,{children:(0,n.jsxs)(c.T,{spacing:6,align:"stretch",children:[(0,n.jsxs)(d.a,{children:[(0,n.jsx)(x.D,{size:"md",mb:4,children:"⚡ Automation & Activities"}),(0,n.jsx)(m.E,{color:"gray.600",_dark:{color:"gray.300"},mb:6,children:"Set up automated features and server activities"})]}),(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsxs)(h.z,{justify:"space-between",children:[(0,n.jsx)(x.D,{size:"md",children:"Bot Presence"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.Bc_,{}),colorScheme:"blue",onClick:st,isLoading:ev,isDisabled:i,children:"Save Presence"})]})}),(0,n.jsx)(E.b,{children:(0,n.jsxs)(c.T,{spacing:6,align:"stretch",children:[ed.activities.map((e,s)=>(0,n.jsxs)(h.z,{spacing:4,align:"flex-end",children:[(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{fontSize:"sm",children:"Activity Type"}),(0,n.jsxs)(G.l,{value:e.type,onChange:e=>sl(s,"type",e.target.value),children:[(0,n.jsx)("option",{value:"PLAYING",children:"Playing"}),(0,n.jsx)("option",{value:"STREAMING",children:"Streaming"}),(0,n.jsx)("option",{value:"LISTENING",children:"Listening"}),(0,n.jsx)("option",{value:"WATCHING",children:"Watching"}),(0,n.jsx)("option",{value:"COMPETING",children:"Competing"})]})]}),(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{fontSize:"sm",children:"Activity Name"}),(0,n.jsx)(k.p,{value:e.name,onChange:e=>sl(s,"name",e.target.value),placeholder:"e.g., with discord.js"})]}),(0,n.jsx)(P.K,{"aria-label":"Remove activity",icon:(0,n.jsx)(X.IXo,{}),colorScheme:"red",variant:"ghost",onClick:()=>sr(s)})]},s)),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.GGD,{}),onClick:()=>{eh(e=>({...e,activities:[...e.activities,{type:"PLAYING",name:""}]}))},size:"sm",alignSelf:"flex-start",children:"Add Activity"}),(0,n.jsx)(B.c,{}),(0,n.jsxs)(T.MJ,{children:[(0,n.jsx)(z.l,{children:"Activity Rotation Interval (seconds)"}),(0,n.jsxs)(F.Q7,{value:ed.activityRotationInterval,onChange:e=>e5("activityRotationInterval",parseInt(e)||60),min:10,max:3600,children:[(0,n.jsx)(F.OO,{}),(0,n.jsxs)(F.lw,{children:[(0,n.jsx)(F.Q0,{}),(0,n.jsx)(F.Sh,{})]})]}),(0,n.jsx)(m.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Time between rotating activities (min 10s, max 1hr)."})]})]})})]}),(0,n.jsxs)(C.r,{columns:{base:1,lg:2},spacing:6,children:[(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(x.D,{size:"sm",children:"Activity Templates"})}),(0,n.jsxs)(E.b,{children:[(0,n.jsx)(m.E,{color:"gray.500",fontSize:"sm",mb:4,children:"Pre-built activity templates to get you started quickly:"}),(0,n.jsxs)(c.T,{spacing:2,align:"stretch",children:[(0,n.jsx)(m.E,{fontSize:"sm",children:"• Event Management System"}),(0,n.jsx)(m.E,{fontSize:"sm",children:"• Welcome & Onboarding Flow"}),(0,n.jsx)(m.E,{fontSize:"sm",children:"• Moderation Automation"}),(0,n.jsx)(m.E,{fontSize:"sm",children:"• Custom Commands"}),(0,n.jsx)(m.E,{fontSize:"sm",children:"• Auto-Role Assignment"}),(0,n.jsx)(m.E,{fontSize:"sm",children:"• Scheduled Messages"})]})]})]}),(0,n.jsxs)(S.Z,{children:[(0,n.jsx)(v.a,{children:(0,n.jsx)(x.D,{size:"sm",children:"Automation Settings"})}),(0,n.jsx)(E.b,{children:(0,n.jsxs)(c.T,{spacing:4,align:"stretch",children:[(0,n.jsx)(m.E,{fontSize:"sm",color:"gray.500",children:"Configure automated server features"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.FrA,{}),colorScheme:"yellow",variant:"outline",isDisabled:!0,children:"Auto-Moderation (Coming Soon)"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.FrA,{}),colorScheme:"green",variant:"outline",onClick:eF,children:"Welcome System"}),(0,n.jsx)(u.$,{leftIcon:(0,n.jsx)(X.FrA,{}),colorScheme:"blue",variant:"outline",isDisabled:!0,children:"Event Scheduler (Coming Soon)"})]})})]})]})]})})]})]})]}),(0,n.jsx)(K.Suspense,{fallback:(0,n.jsx)(l.y,{}),children:(0,n.jsx)(Y,{isOpen:eC,onClose:ez,onSuccess:e2})}),(0,n.jsx)(K.Suspense,{fallback:(0,n.jsx)(l.y,{}),children:(0,n.jsx)(Q,{isOpen:ek,onClose:eI,channel:eX,categories:em.filter(e=>4===e.type),onSuccess:e2})}),(0,n.jsx)(K.Suspense,{fallback:(0,n.jsx)(l.y,{}),children:(0,n.jsx)(q,{isOpen:eA,onClose:eD,onSuccess:e3})}),(0,n.jsx)(K.Suspense,{fallback:(0,n.jsx)(l.y,{}),children:(0,n.jsx)(H,{isOpen:e_,onClose:eR,role:eL,onSuccess:e3})}),(0,n.jsx)(K.Suspense,{fallback:(0,n.jsx)(l.y,{}),children:(0,n.jsx)(U,{isOpen:eO,onClose:eG})}),(0,n.jsx)(K.Suspense,{fallback:(0,n.jsx)(l.y,{}),children:(0,n.jsx)(ee,{isOpen:eB,onClose:e$,channels:em,roles:ex})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[4108,3256,4976,217,2965,3177,3035,7469,341,636,6593,8792],()=>s(33233)),_N_E=e.O()}]);