import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits, EmbedBuilder } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('warn')
  .setDescription('Issue a warning to a member')
  .addUserOption(option =>
    option.setName('user')
      .setDescription('The user to warn')
      .setRequired(true))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for the warning')
      .setRequired(true))
  .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers);

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  const targetUser = interaction.options.getUser('user', true);
  const reason = interaction.options.getString('reason', true);

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ModerateMembers)) {
    await interaction.reply({ content: '❌ You do not have permission to moderate members.', ephemeral: true });
    return;
  }

  const member = await interaction.guild?.members.fetch(targetUser.id).catch(() => null);
  if (!member) {
    await interaction.reply({ content: '❌ Could not find that member in this guild.', ephemeral: true });
    return;
  }

  try {
    // Store warning in database
    const warning = {
      userId: targetUser.id,
      guildId: interaction.guild?.id,
      moderatorId: interaction.user.id,
      reason: reason,
      timestamp: new Date(),
      id: Date.now().toString()
    };

    await bot.database.db.collection('warnings').insertOne(warning);

    // Get user's warning count
    const warningCount = await bot.database.db
      .collection('warnings')
      .countDocuments({ userId: targetUser.id, guildId: interaction.guild?.id });

    // Create warning embed
    const embed = new EmbedBuilder()
      .setTitle('⚠️ Warning Issued')
      .setColor(0xFFAA00)
      .addFields(
        { name: 'User', value: `${targetUser.tag} (${targetUser.id})`, inline: true },
        { name: 'Moderator', value: `${interaction.user.tag}`, inline: true },
        { name: 'Warning Count', value: `${warningCount}`, inline: true },
        { name: 'Reason', value: reason, inline: false }
      )
      .setTimestamp();

    // Send DM to user
    const dmEmbed = new EmbedBuilder()
      .setTitle('⚠️ Warning Received')
      .setColor(0xFFAA00)
      .addFields(
        { name: 'Server', value: interaction.guild?.name || 'Unknown', inline: true },
        { name: 'Warning Count', value: `${warningCount}`, inline: true },
        { name: 'Reason', value: reason, inline: false }
      )
      .setTimestamp();

    await targetUser.send({ embeds: [dmEmbed] }).catch(() => {
      // User has DMs disabled, continue anyway
    });

    await interaction.reply({ embeds: [embed] });

  } catch (error) {
    console.error('Error issuing warning:', error);
    await interaction.reply({ content: '❌ Failed to issue warning. Please try again.', ephemeral: true });
  }
}

export const cooldown = 3000; 