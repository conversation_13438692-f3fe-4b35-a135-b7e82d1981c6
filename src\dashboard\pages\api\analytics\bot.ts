import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { dashboardConfig } from '../../../core/config';
import { MongoClient } from 'mongodb';

// Reuse connection pattern
let cachedClient: MongoClient | null = null;
const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
const dbName = dashboardConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const db = await getDb();
    
    // Get today's date range
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(startOfDay.getTime() + 24 * 60 * 60 * 1000);

    // Query command usage from logs or command_usage collection
    const commandsToday = await db.collection('command_logs')
      .countDocuments({
        timestamp: { $gte: startOfDay, $lt: endOfDay }
      })
      .catch(() => 0); // Fallback if collection doesn't exist

    // Helper functions for path resolution
    const fs = require('fs');
    const path = require('path');
    const YAML = require('yaml');
    
    const locateConfig = (): string => {
      const attempts = [
        '404-bot/config.yml',
        'config.yml',
        '../config.yml',
        '../../config.yml',
        '../../../config.yml',
        '../../../../config.yml',
      ].map(rel => path.resolve(process.cwd(), rel));
      
      let found = attempts.find(p => fs.existsSync(p));
      if (!found) {
        const dirBased = path.resolve(__dirname, '../../../../../../../config.yml');
        if (fs.existsSync(dirBased)) found = dirBased;
      }
      if (!found) throw new Error('config.yml not found');
      return found;
    };

    const locateAddonsDir = (): string => {
      const attempts = [
        '404-bot/src/addons',
        'src/addons',
        '../src/addons',
        '../../src/addons',
        '../../../src/addons',
        '../../../../src/addons',
      ].map(rel => path.resolve(process.cwd(), rel));
      
      let found = attempts.find(p => fs.existsSync(p));
      if (!found) {
        const dirBased = path.resolve(__dirname, '../../../../../../../src/addons');
        if (fs.existsSync(dirBased)) found = dirBased;
      }
      if (!found) throw new Error('Addons directory not found');
      return found;
    };

    // Get active addons count from config.yml and directory structure
    let activeAddons = 0;
    let inactiveAddons = 0;
    try {
      // Read config file
      const configPath = locateConfig();
      const configFile = fs.readFileSync(configPath, 'utf8');
      const config = YAML.parse(configFile);
      
      const addonsGloballyEnabled = config.addons?.enabled !== false;
      const disabled = config.addons?.disabled ?? [];
      
      if (addonsGloballyEnabled) {
        // Get addons directory
        const addonsDir = locateAddonsDir();
        const addonDirs = fs.readdirSync(addonsDir, { withFileTypes: true })
          .filter((dirent: any) => dirent.isDirectory())
          .map((dirent: any) => dirent.name);
        
        const activeDirs = addonDirs.filter((dir: string) => !disabled.includes(dir));
        const inactiveDirs = addonDirs.filter((dir: string) => disabled.includes(dir));

        console.log('Analytics API - Detected addon directories:', { addonDirs, activeDirs, inactiveDirs, disabled }); // Debug log

        activeAddons = activeDirs.length;
        inactiveAddons = inactiveDirs.length;
      }
    } catch (err) {
      console.error('Error counting addons:', err);
      activeAddons = 0;
      inactiveAddons = 0; // Fallback
    }

    // Calculate uptime
    const botStartTime = await db.collection('bot_status')
      .findOne({ key: 'start_time' })
      .catch(() => null);

    let uptime = '0d 0h 0m';
    if (botStartTime?.timestamp) {
      const uptimeMs = Date.now() - new Date(botStartTime.timestamp).getTime();
      const days = Math.floor(uptimeMs / (1000 * 60 * 60 * 24));
      const hours = Math.floor((uptimeMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((uptimeMs % (1000 * 60 * 60)) / (1000 * 60));
      uptime = `${days}d ${hours}h ${minutes}m`;
    } else {
      // If no start time recorded, estimate based on process uptime
      const uptimeSeconds = process.uptime();
      const days = Math.floor(uptimeSeconds / (60 * 60 * 24));
      const hours = Math.floor((uptimeSeconds % (60 * 60 * 24)) / (60 * 60));
      const minutes = Math.floor((uptimeSeconds % (60 * 60)) / 60);
      uptime = `${days}d ${hours}h ${minutes}m`;
    }

    // Get average response time from recent commands
    const recentCommands = await db.collection('command_logs')
      .find({ 
        timestamp: { $gte: new Date(Date.now() - 60 * 60 * 1000) }, // Last hour
        responseTime: { $exists: true }
      })
      .limit(100)
      .toArray()
      .catch(() => []);

    const avgResponseTime = recentCommands.length > 0
      ? Math.round(recentCommands.reduce((sum, cmd) => sum + (cmd.responseTime || 0), 0) / recentCommands.length)
      : 45; // Default fallback

    // Get error count today
    const errorsToday = await db.collection('error_logs')
      .countDocuments({
        timestamp: { $gte: startOfDay, $lt: endOfDay }
      })
      .catch(() => 0);

    // Weekly activity (commands & unique members per day for the last 7 days)
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6); // include today

    const logsLastWeek = await db.collection('command_logs')
      .find({ timestamp: { $gte: sevenDaysAgo } })
      .toArray()
      .catch(() => []);

    const daysMap: Record<string, { commands: number; members: Set<string> }> = {
      Mon: { commands: 0, members: new Set() },
      Tue: { commands: 0, members: new Set() },
      Wed: { commands: 0, members: new Set() },
      Thu: { commands: 0, members: new Set() },
      Fri: { commands: 0, members: new Set() },
      Sat: { commands: 0, members: new Set() },
      Sun: { commands: 0, members: new Set() },
    };

    const dayLabels = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];

    for (const log of logsLastWeek) {
      const d = new Date(log.timestamp);
      const label = dayLabels[d.getDay()];
      if (!daysMap[label]) continue; // should exist
      daysMap[label].commands += 1;
      if (log.userId) daysMap[label].members.add(log.userId);
    }

    const orderedDays = ['Mon','Tue','Wed','Thu','Fri','Sat','Sun'];
    const weeklyActivity = orderedDays.map(day => ({
      day,
      commands: daysMap[day].commands,
      members: daysMap[day].members.size,
    }));

    const botStats = {
      commandsToday,
      uptime,
      responseTime: `${avgResponseTime}ms`,
      activeAddons,
      inactiveAddons,
      weeklyActivity,
      errorsToday,
      status: 'online', // You could check actual bot status here
    };

    res.status(200).json({ botStats });
  } catch (error) {
    console.error('Error fetching bot analytics:', error);
    res.status(500).json({ 
      error: 'Failed to fetch bot analytics', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
} 