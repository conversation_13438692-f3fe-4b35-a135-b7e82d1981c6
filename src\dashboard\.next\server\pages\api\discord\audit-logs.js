"use strict";(()=>{var e={};e.id=8015,e.ids=[8015],e.modules={224:e=>{e.exports=import("@discordjs/rest")},264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},381:(e,t,r)=>{r.d(t,{_:()=>g});var a=r(2115),n=r.n(a),o=r(9021),i=r.n(o),s=r(3873),d=r.n(s);let u=function(){let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>d().resolve(process.cwd(),e)).find(e=>i().existsSync(e));if(!e){let t=d().resolve(__dirname,"../../../config.yml");i().existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");return e}(),l=n().parse(i().readFileSync(u,"utf8")),c="http://***********:3000",p=new URL(l.dashboard?.url||"http://localhost:3000"),f=`${p.protocol}//localhost:${p.port||"3000"}`,g={DISCORD_BOT_TOKEN:l.bot.token,DISCORD_CLIENT_ID:l.bot.clientId,DISCORD_CLIENT_SECRET:l.bot.clientSecret,DISCORD_GUILD_ID:l.bot.guildId,NEXTAUTH_URL:c||l.dashboard?.nextAuthUrl||l.dashboard?.url||f,NEXTAUTH_LOCALHOST_URL:f};l.bot.token,l.bot.clientId,l.bot.clientSecret,l.bot.guildId,l.bot.presence,c||l.dashboard?.url,c||l.dashboard?.nextAuthUrl||l.dashboard?.url,l.dashboard?.admins,l.dashboard?.adminRoleIds,l.dashboard?.session?.secret||l.bot.clientSecret,Object.entries(g).forEach(([e,t])=>{if(!t)throw Error(`Missing required environment variable: ${e}`)})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},935:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>l,default:()=>u,routeModule:()=>c});var n=r(3433),o=r(264),i=r(584),s=r(6374),d=e([s]);s=(d.then?(await d)():d)[0];let u=(0,i.M)(s,"default"),l=(0,i.M)(s,"config"),c=new n.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/discord/audit-logs",pathname:"/api/discord/audit-logs",bundlePath:"",filename:""},userland:s});a()}catch(e){a(e)}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},3915:e=>{e.exports=import("discord-api-types/v10")},4722:e=>{e.exports=require("next-auth/react")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6374:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var n=r(4722),o=r(224),i=r(3915),s=r(381),d=e([o,i]);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,n.getSession)({req:e}))return t.status(401).json({error:"Unauthorized"});let r=s._.DISCORD_GUILD_ID;if(!r)return t.status(500).json({error:"DISCORD_GUILD_ID not configured"});let a=new o.REST({version:"10"}).setToken(s._.DISCORD_BOT_TOKEN),{before:d,after:u,limit:l="50"}=e.query,c=new URLSearchParams;d&&"string"==typeof d&&c.append("before",d),u&&"string"==typeof u&&c.append("after",u),c.append("limit",String(Math.min(Number(l)||50,100)));let p=await a.get(`${i.Routes.guildAuditLog(r)}?${c}`),f=Array.isArray(p?.audit_log_entries)?p.audit_log_entries:[],g=new Map;if(Array.isArray(p?.users))for(let e of p.users)g.set(e.id,e);let h=f.map(e=>{let t=g.get(e.user_id);return{id:e.id,action:e.action_type,executor:t?{id:t.id,tag:`${t.username}#${t.discriminator}`,avatar:t.avatar?`https://cdn.discordapp.com/avatars/${t.id}/${t.avatar}.png`:null}:null,target:e.target_id?{id:e.target_id,type:e.target_type}:null,reason:e.reason,createdTimestamp:(()=>{let t=Number(e.id);return Math.round(t/4194304+14200704e5)})(),changes:e.changes?.map(e=>({key:e.key,old:e.old_value,new:e.new_value}))}});return t.status(200).json(h)}catch(e){return t.status(500).json({error:"Failed to fetch audit logs",message:void 0})}}[o,i]=d.then?(await d)():d,a()}catch(e){a(e)}})},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=935);module.exports=r})();