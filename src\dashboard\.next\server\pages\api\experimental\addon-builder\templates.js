"use strict";(()=>{var e={};e.id=6484,e.ids=[6484],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,n)=>{e.exports=n(5600)},3757:(e,t,n)=>{n.r(t),n.d(t,{config:()=>u,default:()=>m,routeModule:()=>p});var o={};n.r(o),n.d(o,{default:()=>c});var i=n(3433),a=n(264),r=n(584),s=n(5806),d=n(8525);let l=[{id:"basic-utility",name:"Basic Utility",description:"A simple addon with common utility commands like ping, info, and user lookup.",category:"Utility",config:{name:"my-utility-addon",version:"1.0.0",description:"A utility addon with basic commands for server management and information.",author:"",commands:[{name:"ping",description:"Check the bot's latency and status",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:`const start = Date.now();
await interaction.reply({ content: 'Pinging...', ephemeral: true });
const latency = Date.now() - start;

const embed = new EmbedBuilder()
  .setColor(0x0099FF)
  .setTitle('🏓 Pong!')
  .addFields(
    { name: 'Latency', value: \`\${latency}ms\`, inline: true },
    { name: 'API Latency', value: \`\${bot.client.ws.ping}ms\`, inline: true }
  )
  .setTimestamp();

await interaction.editReply({ content: null, embeds: [embed] });`},{name:"serverinfo",description:"Display information about the current server",type:"slash",permissions:[],cooldown:5e3,enabled:!0,code:`const guild = interaction.guild;
if (!guild) {
  await interaction.reply({ content: 'This command can only be used in a server!', ephemeral: true });
  return;
}

const embed = new EmbedBuilder()
  .setColor(0x0099FF)
  .setTitle(\`📊 \${guild.name}\`)
  .setThumbnail(guild.iconURL())
  .addFields(
    { name: 'Owner', value: \`<@\${guild.ownerId}>\`, inline: true },
    { name: 'Members', value: guild.memberCount.toString(), inline: true },
    { name: 'Created', value: guild.createdAt.toDateString(), inline: true },
    { name: 'Boost Level', value: guild.premiumTier.toString(), inline: true },
    { name: 'Boosts', value: guild.premiumSubscriptionCount?.toString() || '0', inline: true }
  )
  .setTimestamp();

await interaction.reply({ embeds: [embed] });`}],events:[{name:"ready",once:!0,code:`const logger = Logger.createAddonLogger('my-utility-addon');
logger.info(\`\${bot.client.user?.tag} is ready with utility commands!\`);`}],settings:{embedColor:"#0099FF"}}},{id:"moderation-basic",name:"Basic Moderation",description:"Essential moderation commands for server management.",category:"Moderation",config:{name:"my-moderation-addon",version:"1.0.0",description:"A moderation addon with basic commands for managing your Discord server.",author:"",commands:[{name:"kick",description:"Kick a member from the server",type:"slash",permissions:["KICK_MEMBERS"],cooldown:5e3,enabled:!0,code:`// Check if user has permission
if (!interaction.member?.permissions.has('KICK_MEMBERS')) {
  await interaction.reply({ content: 'You need the Kick Members permission to use this command!', ephemeral: true });
  return;
}

const user = interaction.options.getUser('user');
const reason = interaction.options.getString('reason') || 'No reason provided';

if (!user) {
  await interaction.reply({ content: 'Please specify a user to kick!', ephemeral: true });
  return;
}

try {
  const member = await interaction.guild?.members.fetch(user.id);
  if (!member) {
    await interaction.reply({ content: 'User not found in this server!', ephemeral: true });
    return;
  }

  await member.kick(reason);
  
  const embed = new EmbedBuilder()
    .setColor(0xFF6B6B)
    .setTitle('👢 Member Kicked')
    .addFields(
      { name: 'User', value: \`\${user.tag} (\${user.id})\`, inline: true },
      { name: 'Reason', value: reason, inline: true },
      { name: 'Moderator', value: interaction.user.tag, inline: true }
    )
    .setTimestamp();

  await interaction.reply({ embeds: [embed] });
} catch (error) {
  await interaction.reply({ content: 'Failed to kick the user. They may have higher permissions than me.', ephemeral: true });
}`},{name:"clear",description:"Delete a specified number of messages",type:"slash",permissions:["MANAGE_MESSAGES"],cooldown:5e3,enabled:!0,code:`// Check if user has permission
if (!interaction.member?.permissions.has('MANAGE_MESSAGES')) {
  await interaction.reply({ content: 'You need the Manage Messages permission to use this command!', ephemeral: true });
  return;
}

const amount = interaction.options.getInteger('amount') || 1;

if (amount < 1 || amount > 100) {
  await interaction.reply({ content: 'Please specify a number between 1 and 100!', ephemeral: true });
  return;
}

try {
  const channel = interaction.channel;
  if (!channel || !channel.isTextBased()) {
    await interaction.reply({ content: 'This command can only be used in text channels!', ephemeral: true });
    return;
  }

  await channel.bulkDelete(amount, true);
  
  await interaction.reply({ 
    content: \`✅ Successfully deleted \${amount} message(s)!\`, 
    ephemeral: true 
  });
} catch (error) {
  await interaction.reply({ 
    content: 'Failed to delete messages. Messages may be older than 14 days.', 
    ephemeral: true 
  });
}`}],events:[{name:"guildMemberAdd",once:!1,code:`const member = args[0];
const logger = Logger.createAddonLogger('my-moderation-addon');
logger.info(\`New member joined: \${member.user.tag} (\${member.id})\`);

// You can add welcome message logic here`}],settings:{embedColor:"#FF6B6B"}}},{id:"fun-commands",name:"Fun Commands",description:"Entertainment commands to engage your community.",category:"Entertainment",config:{name:"my-fun-addon",version:"1.0.0",description:"A fun addon with entertainment commands for your Discord server.",author:"",commands:[{name:"8ball",description:"Ask the magic 8-ball a question",type:"slash",permissions:[],cooldown:3e3,enabled:!0,code:`const question = interaction.options.getString('question');
if (!question) {
  await interaction.reply({ content: 'Please ask a question!', ephemeral: true });
  return;
}

const responses = [
  'It is certain', 'It is decidedly so', 'Without a doubt', 'Yes definitely',
  'You may rely on it', 'As I see it, yes', 'Most likely', 'Outlook good',
  'Yes', 'Signs point to yes', 'Reply hazy, try again', 'Ask again later',
  'Better not tell you now', 'Cannot predict now', 'Concentrate and ask again',
  'Don\\'t count on it', 'My reply is no', 'My sources say no',
  'Outlook not so good', 'Very doubtful'
];

const response = responses[Math.floor(Math.random() * responses.length)];

const embed = new EmbedBuilder()
  .setColor(0x7B68EE)
  .setTitle('🎱 Magic 8-Ball')
  .addFields(
    { name: 'Question', value: question },
    { name: 'Answer', value: response }
  )
  .setTimestamp();

await interaction.reply({ embeds: [embed] });`},{name:"dice",description:"Roll a dice with specified sides",type:"slash",permissions:[],cooldown:2e3,enabled:!0,code:`const sides = interaction.options.getInteger('sides') || 6;

if (sides < 2 || sides > 100) {
  await interaction.reply({ content: 'Please specify a number of sides between 2 and 100!', ephemeral: true });
  return;
}

const result = Math.floor(Math.random() * sides) + 1;

const embed = new EmbedBuilder()
  .setColor(0x00D4AA)
  .setTitle('🎲 Dice Roll')
  .addFields(
    { name: 'Sides', value: sides.toString(), inline: true },
    { name: 'Result', value: result.toString(), inline: true }
  )
  .setTimestamp();

await interaction.reply({ embeds: [embed] });`}],events:[{name:"messageCreate",once:!1,code:`const message = args[0];
// React to messages containing certain keywords
if (message.author.bot) return;

const content = message.content.toLowerCase();
if (content.includes('good bot')) {
  message.react('❤️');
} else if (content.includes('bad bot')) {
  message.react('😢');
}`}],settings:{embedColor:"#7B68EE"}}},{id:"blank-template",name:"Blank Template",description:"Start from scratch with a minimal addon structure.",category:"Starter",config:{name:"my-custom-addon",version:"1.0.0",description:"My custom Discord bot addon built with the Addon Builder.",author:"",commands:[],events:[],settings:{embedColor:"#0099FF"}}}];async function c(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});if(!await (0,s.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});try{let n=e.query.category,o=l;n&&"all"!==n&&(o=l.filter(e=>e.category.toLowerCase()===n.toLowerCase()));let i=Array.from(new Set(l.map(e=>e.category)));t.status(200).json({templates:o,categories:i})}catch(e){t.status(500).json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error"})}}let m=(0,r.M)(o,"default"),u=(0,r.M)(o,"config"),p=new i.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/experimental/addon-builder/templates",pathname:"/api/experimental/addon-builder/templates",bundlePath:"",filename:""},userland:o})},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,n)=>{n.r(t),n.d(t,{authOptions:()=>d,default:()=>l});var o=n(5542),i=n.n(o);let a=require("next-auth/providers/discord");var r=n.n(a),s=n(8580);let d={providers:[r()({clientId:s.dashboardConfig.bot.clientId,clientSecret:s.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:n})=>(t&&n&&(e.accessToken=t.access_token||null,e.id=n.id||null),e),async session({session:e,token:t}){if(e?.user){let n=t.id||null,o=t.accessToken||null;e.user.id=n,e.user.accessToken=o;let i=!1;if(n)if((s.dashboardConfig.dashboard.admins||[]).includes(n))i=!0;else{let e=s.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&s.dashboardConfig.bot.token&&s.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${s.dashboardConfig.bot.guildId}/members/${n}`,{headers:{Authorization:`Bot ${s.dashboardConfig.bot.token}`}});if(t.ok){let n=await t.json();i=e.some(e=>n.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let n=new URL(t),o=`${n.protocol}//localhost${n.port?`:${n.port}`:""}`;return e.startsWith(t)||e.startsWith(o)?e:t}},secret:s.dashboardConfig.dashboard.session.secret||s.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(d)},8580:(e,t,n)=>{n.r(t),n.d(t,{dashboardConfig:()=>d,default:()=>l});var o=n(9021),i=n(2115),a=n.n(i),r=n(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");s=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var n=t(t.s=3757);module.exports=n})();