{"env": {"node": true, "es2022": true}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_"}]}}