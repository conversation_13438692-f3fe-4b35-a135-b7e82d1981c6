(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2365],{40295:(e,n,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/addons",function(){return o(63071)}])},63071:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>N});var s=o(94513),r=o(94285),a=o(7746),t=o(22907),i=o(68443),d=o(59818),l=o(79156),c=o(41611),h=o(78902),u=o(62690),g=o(49217),m=o(31678),f=o(71601),x=o(71185),j=o(55631),p=o(9557),b=o(7680),y=o(52922),C=o(47847),v=o(59365),E=o(85104),S=o(28245),w=o(95845),A=o(59001),k=o(51961),z=o(25680),T=o(60341),F=o(97146),I=o(73366),D=o(77072);let O=o.n(D)()(()=>o.e(1166).then(o.bind(o,11166)),{loadableGenerated:{webpack:()=>[11166]},ssr:!1,loading:()=>(0,s.jsx)(a.E,{height:"400px"})}),_={"voice-mistress":{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"}},tickets:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"}},"welcome-goodbye":{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"}},example:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"}},default:{color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"}}},$=e=>{var n,o,a,w,A,k,z;let{addon:T,onSave:D,onToggle:$,onEdit:N,onDelete:P}=e,[L,M]=(0,r.useState)(!1),[W,Y]=(0,r.useState)(T.config||""),[B,R]=(0,r.useState)(null),[J,U]=(0,r.useState)(!1),X=(0,t.d)();(0,r.useEffect)(()=>{Y(T.config||"")},[T.config]);let q=async()=>{try{I.qg(W||""),R(null);let e=await fetch("/api/admin/addons/".concat(T.name),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:W})});if(!e.ok){let n=await e.json();throw Error(n.message||"Failed to save configuration")}D(W),M(!1),X({title:"Success",description:"Configuration saved successfully",status:"success",duration:3e3})}catch(e){R(e.message||"Invalid YAML format"),X({title:"Error",description:e.message||"Invalid YAML format",status:"error",duration:5e3})}};return(0,s.jsx)(i.Z,{bg:(null==(n=_[T.name])?void 0:n.color)?"linear-gradient(135deg, ".concat(_[T.name].gradient.from,", ").concat(_[T.name].gradient.to,")"):"gray.900",backdropFilter:"blur(10px)",borderWidth:2,borderColor:(null==(o=_[T.name])?void 0:o.color)?"".concat(_[T.name].color,".400"):"gray.600",rounded:"xl",overflow:"hidden",transition:"all 0.2s",_hover:{transform:"translateY(-2px)",boxShadow:(null==(a=_[T.name])?void 0:a.color)?"0 4px 20px ".concat(_[T.name].gradient.from):"none"},children:(0,s.jsx)(d.b,{children:L?(0,s.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(O,{value:W,onChange:e=>{Y(e),R(null)}}),B&&(0,s.jsx)(c.E,{color:"red.400",fontSize:"sm",children:B}),(0,s.jsxs)(h.z,{justify:"flex-end",spacing:2,children:[(0,s.jsx)(u.$,{variant:"ghost",onClick:()=>{M(!1),Y(T.config||""),R(null)},children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:"purple",onClick:q,isDisabled:!!B,children:"Save Changes"})]})]}):(0,s.jsxs)(l.T,{align:"stretch",spacing:4,children:[(0,s.jsxs)(h.z,{justify:"space-between",children:[(0,s.jsxs)(h.z,{children:[(0,s.jsx)(g.I,{as:F.est,color:"".concat((null==(w=_[T.name])?void 0:w.color)||"gray",".400"),boxSize:5}),(0,s.jsx)(m.D,{size:"md",color:"white",children:T.name})]}),(0,s.jsx)(f.E,{colorScheme:T.enabled&&(null==(A=_[T.name])?void 0:A.color)||"gray",children:T.enabled?"Active":"Inactive"})]}),(0,s.jsx)(c.E,{color:"gray.300",fontSize:"sm",noOfLines:2,children:T.description}),(0,s.jsxs)(h.z,{children:[(0,s.jsxs)(c.E,{color:"gray.400",fontSize:"xs",children:["v",T.version]}),(0,s.jsxs)(c.E,{color:"gray.400",fontSize:"xs",children:["by ",T.author]})]}),(0,s.jsx)(x.c,{borderColor:"whiteAlpha.200"}),(0,s.jsxs)(h.z,{justify:"space-between",children:[(0,s.jsx)(j.d,{isChecked:T.enabled,onChange:()=>$(T),colorScheme:(null==(k=_[T.name])?void 0:k.color)||"gray"}),(0,s.jsxs)(h.z,{spacing:2,children:[(0,s.jsx)(u.$,{size:"sm",leftIcon:(0,s.jsx)(g.I,{as:F.PjK}),variant:"ghost",colorScheme:(null==(z=_[T.name])?void 0:z.color)||"gray",onClick:()=>N(T),children:"Edit Config"}),T.isCustomAddon&&P&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.$,{size:"sm",leftIcon:(0,s.jsx)(g.I,{as:F.IXo}),variant:"ghost",colorScheme:"red",onClick:()=>U(!0),children:"Delete"}),(0,s.jsxs)(p.aF,{isOpen:J,onClose:()=>U(!1),children:[(0,s.jsx)(b.m,{}),(0,s.jsxs)(y.$,{children:[(0,s.jsx)(C.r,{children:"Delete Custom Addon"}),(0,s.jsx)(v.s,{}),(0,s.jsx)(E.c,{children:(0,s.jsxs)(c.E,{children:["Are you sure you want to delete ",(0,s.jsx)("strong",{children:T.name}),"? This action cannot be undone and will permanently remove all files for this custom addon."]})}),(0,s.jsxs)(S.j,{children:[(0,s.jsx)(u.$,{variant:"ghost",mr:3,onClick:()=>U(!1),children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:"red",onClick:()=>{P(T),U(!1)},children:"Delete"})]})]})]})]})]})]})]})})},T.name)};function N(){var e,n;let[o,a]=(0,r.useState)([]),[i,d]=(0,r.useState)([]),[f,x]=(0,r.useState)(null),[j,D]=(0,r.useState)(""),{isOpen:N,onOpen:P,onClose:L}=(0,w.j)(),M=(0,t.d)();(0,r.useEffect)(()=>{N||(x(null),D(""))},[N]);let W=async()=>{try{let o=await fetch("/api/admin/addons",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!o.ok){let e=await o.json();if(401===o.status||403===o.status){window.location.href="/signin";return}throw Error(e.message||"Failed to fetch addons")}let s=await o.json();if(s.builtInAddons&&s.customAddons)a(s.builtInAddons),d(s.customAddons);else{var e,n;let o=(null==(e=s.addons)?void 0:e.filter(e=>!e.isCustomAddon))||[],r=(null==(n=s.addons)?void 0:n.filter(e=>e.isCustomAddon))||[];a(o),d(r)}}catch(e){M({title:"Error",description:e instanceof Error?e.message:"Failed to fetch addons",status:"error",duration:5e3})}};(0,r.useEffect)(()=>{W()},[]);let Y=async e=>{try{if(!(await fetch("/api/admin/addons/".concat(e.name),{method:"PATCH",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:!e.enabled})})).ok)throw Error("Failed to toggle addon");try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"})}catch(e){}let n=n=>n.map(n=>n.name===e.name?{...n,enabled:!n.enabled}:n);e.isCustomAddon?d(n):a(n),M({title:"Success",description:"Addon ".concat(e.enabled?"disabled":"enabled"," successfully"),status:"success",duration:3e3})}catch(e){M({title:"Error",description:"Failed to toggle addon",status:"error",duration:5e3})}},B=(e,n)=>{let o=o=>o.map(o=>o.name===e.name?{...o,config:n}:o);e.isCustomAddon?d(o):a(o)},R=async e=>{try{let n=await fetch("/api/admin/addons/".concat(e.name,"/config"),{credentials:"include"});if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to fetch addon config: ".concat(n.status))}let o=await n.json();x(e),setTimeout(()=>{D(o.config||""),P()},100)}catch(e){M({title:"Error Fetching Config",description:e.message,status:"error",duration:5e3,isClosable:!0})}},J=async()=>{if(f)try{if(I.qg(j),!(await fetch("/api/admin/addons/".concat(f.name,"/config"),{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:j})})).ok)throw Error("Failed to save configuration");M({title:"Success",description:"Configuration saved successfully",status:"success",duration:3e3}),L(),W()}catch(e){M({title:"Error",description:e.message||"Failed to save configuration",status:"error",duration:5e3})}},U=async()=>{try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"}),M({title:"Success",description:"Addons reloaded successfully",status:"success",duration:3e3}),W()}catch(e){M({title:"Error",description:"Failed to reload addons",status:"error",duration:5e3})}},X=async()=>{try{await fetch("/api/admin/commands/refresh",{method:"POST",credentials:"include"}),M({title:"Success",description:"Discord commands refreshed successfully",status:"success",duration:3e3})}catch(e){M({title:"Error",description:"Failed to refresh commands",status:"error",duration:5e3})}},q=async e=>{try{let n=await fetch("/api/admin/addons/".concat(e.name),{method:"DELETE",credentials:"include"});if(!n.ok){let e=await n.json();throw Error(e.details||e.error||"Failed to delete addon")}try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"})}catch(e){}d(n=>n.filter(n=>n.name!==e.name)),M({title:"Success",description:"Custom addon deleted successfully",status:"success",duration:3e3})}catch(e){M({title:"Error",description:e instanceof Error?e.message:"Failed to delete addon",status:"error",duration:5e3})}};return(0,s.jsxs)(T.A,{children:[(0,s.jsx)(A.m,{maxW:"container.xl",py:8,children:(0,s.jsxs)(l.T,{spacing:8,align:"stretch",children:[(0,s.jsx)(k.a,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"purple.400",boxShadow:"0 0 15px rgba(159, 122, 234, 0.4)",children:(0,s.jsxs)(h.z,{justify:"space-between",align:"center",children:[(0,s.jsxs)(l.T,{align:"start",spacing:2,children:[(0,s.jsx)(m.D,{size:"xl",bgGradient:"linear(to-r, purple.300, pink.400)",bgClip:"text",children:"Bot Addons Management"}),(0,s.jsx)(c.E,{color:"gray.300",children:"Manage built-in addons and custom addons created with the addon builder"}),(0,s.jsx)(c.E,{color:"gray.500",fontSize:"sm",children:'Use "Reload Addons" to refresh the bot\'s addon system. Use "Refresh Commands" to update Discord\'s command list.'})]}),(0,s.jsxs)(h.z,{spacing:2,children:[(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(g.I,{as:F.jTZ}),colorScheme:"purple",variant:"outline",onClick:U,children:"Reload Addons"}),(0,s.jsx)(u.$,{leftIcon:(0,s.jsx)(g.I,{as:F.FrA}),colorScheme:"orange",variant:"outline",onClick:X,children:"Refresh Commands"})]})]})}),(0,s.jsxs)(k.a,{children:[(0,s.jsxs)(m.D,{size:"lg",mb:4,color:"blue.300",children:["\uD83D\uDEE0️ Built-in Addons (",o.length,")"]}),(0,s.jsx)(c.E,{color:"gray.400",mb:6,fontSize:"sm",children:"Core bot functionality - these addons cannot be deleted"}),(0,s.jsx)(z.r,{columns:{base:1,md:2,lg:3},spacing:6,children:o.map(e=>(0,s.jsx)($,{addon:e,onSave:n=>B(e,n),onToggle:Y,onEdit:R},e.name))}),0===o.length&&(0,s.jsx)(k.a,{bg:"gray.800",p:6,rounded:"lg",textAlign:"center",borderColor:"gray.600",borderWidth:1,children:(0,s.jsx)(c.E,{color:"gray.400",children:"No built-in addons found"})})]}),(0,s.jsxs)(k.a,{children:[(0,s.jsxs)(m.D,{size:"lg",mb:4,color:"green.300",children:["⚗️ Custom Addons (",i.length,")"]}),(0,s.jsx)(c.E,{color:"gray.400",mb:6,fontSize:"sm",children:"Addons created with the addon builder - these can be edited or deleted"}),(0,s.jsx)(z.r,{columns:{base:1,md:2,lg:3},spacing:6,children:i.map(e=>(0,s.jsx)($,{addon:e,onSave:n=>B(e,n),onToggle:Y,onEdit:R,onDelete:q},e.name))}),0===i.length&&(0,s.jsxs)(k.a,{bg:"gray.800",p:6,rounded:"lg",textAlign:"center",borderColor:"gray.600",borderWidth:1,children:[(0,s.jsx)(c.E,{color:"gray.400",children:"No custom addons created yet"}),(0,s.jsx)(c.E,{color:"gray.500",fontSize:"sm",mt:2,children:"Use the Addon Builder to create your first custom addon!"})]})]})]})}),(0,s.jsxs)(p.aF,{isOpen:N,onClose:L,size:"6xl",children:[(0,s.jsx)(b.m,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(y.$,{bg:"gray.800",border:"1px solid",borderColor:"".concat((null==(e=_[null==f?void 0:f.name])?void 0:e.color)||"gray",".500"),maxW:"1200px",children:[(0,s.jsx)(C.r,{children:(0,s.jsxs)(h.z,{children:[(0,s.jsx)(g.I,{as:F.VSk}),(0,s.jsxs)(c.E,{children:["Configure ",null==f?void 0:f.name]})]})}),(0,s.jsx)(v.s,{}),(0,s.jsx)(E.c,{maxH:"70vh",overflowY:"auto",children:(0,s.jsxs)(l.T,{spacing:4,align:"stretch",children:[(0,s.jsx)(c.E,{color:"gray.400",fontSize:"sm",children:"Edit the configuration in YAML format. Changes will be saved to config.yml"}),(0,s.jsx)(O,{value:j,onChange:D,height:"60vh"})]})}),(0,s.jsxs)(S.j,{children:[(0,s.jsx)(u.$,{variant:"ghost",mr:3,onClick:L,children:"Cancel"}),(0,s.jsx)(u.$,{colorScheme:(null==(n=_[null==f?void 0:f.name])?void 0:n.color)||"gray",leftIcon:(0,s.jsx)(g.I,{as:F.Bc_}),onClick:J,children:"Save Changes"})]})]})]})]})}}},e=>{var n=n=>e(e.s=n);e.O(0,[4108,9998,4976,217,2965,3177,4701,341,636,6593,8792],()=>n(40295)),_N_E=e.O()}]);