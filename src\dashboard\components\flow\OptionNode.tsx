import React, { useState, memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Box,
  Text,
  VStack,
  HStack,
  Input,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Switch,
  Select,
  useDisclosure,
  IconButton,
  Badge,
  Divider,
  Collapse,
  List,
  ListItem,
  ListIcon,
  Textarea,
} from '@chakra-ui/react';
import { FiSettings, FiTool, FiChevronDown, FiChevronRight, FiSearch } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';

interface DiscordAction {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: string;
  config?: any;
}

interface OptionNodeData {
  label: string;
  selectedAction?: DiscordAction;
  config?: any;
}

const discordActions: DiscordAction[] = [
  // Message or Embed
  { id: 'modal', name: '<PERSON><PERSON>', description: 'Open a modal', icon: '💬', category: 'MESSAGE OR EMBED' },
  { id: 'start-poll', name: 'Start Poll', description: 'Start a poll', icon: '📊', category: 'MESSAGE OR EMBED' },
  { id: 'stop-poll', name: 'Stop Poll', description: 'Stop a poll', icon: '🛑', category: 'MESSAGE OR EMBED' },
  { id: 'send-message', name: 'Send Message', description: 'Send a message', icon: '💬', category: 'MESSAGE OR EMBED' },
  { id: 'add-reaction', name: 'Add Reaction', description: 'Add a reaction to a message', icon: '😊', category: 'MESSAGE OR EMBED' },
  { id: 'remove-reaction', name: 'Remove Reaction', description: 'Remove a reaction from a message', icon: '❌', category: 'MESSAGE OR EMBED' },
  { id: 'forward-message', name: 'Forward Message', description: 'Forward a message', icon: '📤', category: 'MESSAGE OR EMBED' },
  { id: 'publish-message', name: 'Publish Message', description: 'Publish a message', icon: '📢', category: 'MESSAGE OR EMBED' },
  { id: 'delete-message', name: 'Delete Message', description: 'Delete a message', icon: '🗑️', category: 'MESSAGE OR EMBED' },
  { id: 'copy-message', name: 'Copy Message', description: 'Copy a message', icon: '📋', category: 'MESSAGE OR EMBED' },
  { id: 'edit-message', name: 'Edit Message', description: 'Edit a message', icon: '✏️', category: 'MESSAGE OR EMBED' },
  { id: 'pin-message', name: 'Pin Message', description: 'Pin a message', icon: '📌', category: 'MESSAGE OR EMBED' },
  
  // Integration
  { id: 'ai-actions', name: 'AI Actions', description: 'AI chat actions', icon: '🤖', category: 'INTEGRATION' },
  
  // Music
  { id: 'play-music', name: 'Play Music', description: 'Play a song', icon: '▶️', category: 'MUSIC' },
  { id: 'stop-music', name: 'Stop Music', description: 'Stop music', icon: '⏹️', category: 'MUSIC' },
  
  // Moderation
  { id: 'ban-user', name: 'Ban User', description: 'Ban a user', icon: '🔨', category: 'MODERATION' },
  { id: 'kick-user', name: 'Kick User', description: 'Kick a user', icon: '👢', category: 'MODERATION' },
  { id: 'timeout-user', name: 'Timeout User', description: 'Timeout a user', icon: '⏰', category: 'MODERATION' },
  { id: 'add-role', name: 'Add Role', description: 'Add role to user', icon: '🏷️', category: 'MODERATION' },
  { id: 'remove-role', name: 'Remove Role', description: 'Remove role from user', icon: '🏷️', category: 'MODERATION' },
  
  // Server Management
  { id: 'create-channel', name: 'Create Channel', description: 'Create a channel', icon: '📝', category: 'SERVER MANAGEMENT' },
  { id: 'delete-channel', name: 'Delete Channel', description: 'Delete a channel', icon: '🗑️', category: 'SERVER MANAGEMENT' },
  { id: 'create-role', name: 'Create Role', description: 'Create a role', icon: '🎭', category: 'SERVER MANAGEMENT' },
  { id: 'delete-role', name: 'Delete Role', description: 'Delete a role', icon: '🎭', category: 'SERVER MANAGEMENT' },
];

const OptionNode = memo(({ data, selected }: NodeProps<OptionNodeData>) => {
  const { currentScheme } = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [nodeData, setNodeData] = useState<OptionNodeData>(data);
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(new Set(['MESSAGE OR EMBED']));

  const updateNodeData = (updates: Partial<OptionNodeData>) => {
    setNodeData(prev => ({ ...prev, ...updates }));
  };

  const toggleCategory = (category: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(category)) {
        newSet.delete(category);
      } else {
        newSet.add(category);
      }
      return newSet;
    });
  };

  const selectAction = (action: DiscordAction) => {
    updateNodeData({ selectedAction: action });
    data.selectedAction = action;
  };

  const filteredActions = discordActions.filter(action =>
    action.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    action.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const groupedActions = filteredActions.reduce((acc, action) => {
    if (!acc[action.category]) {
      acc[action.category] = [];
    }
    acc[action.category].push(action);
    return acc;
  }, {} as Record<string, DiscordAction[]>);

  return (
    <>
      <Box
        bg={`linear-gradient(135deg, ${currentScheme.colors.textSecondary}, #4A5568)`}
        border={`2px solid ${selected ? currentScheme.colors.primary : currentScheme.colors.border}`}
        borderRadius="lg"
        p={4}
        minW="200px"
        boxShadow="lg"
        position="relative"
        _hover={{
          boxShadow: 'xl',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <VStack spacing={2} align="stretch">
          <HStack justify="space-between" align="center">
            <HStack spacing={2}>
              <Box
                bg="gray.600"
                color="white"
                borderRadius="full"
                p={1}
                fontSize="sm"
              >
                <FiTool />
              </Box>
              <Text fontSize="sm" fontWeight="bold" color="white">
                Actions
              </Text>
            </HStack>
            <IconButton
              icon={<FiSettings />}
              size="xs"
              variant="ghost"
              color="white"
              onClick={onOpen}
              aria-label="Configure action"
            />
          </HStack>
          
          <Box>
            {nodeData.selectedAction ? (
              <VStack align="stretch" spacing={1}>
                <HStack spacing={2}>
                  <Text fontSize="lg">{nodeData.selectedAction.icon}</Text>
                  <Text fontSize="sm" fontWeight="medium" color="white">
                    {nodeData.selectedAction.name}
                  </Text>
                </HStack>
                <Text fontSize="xs" color="gray.300">
                  {nodeData.selectedAction.description}
                </Text>
                <Badge colorScheme="purple" size="sm" alignSelf="flex-start">
                  {nodeData.selectedAction.category}
                </Badge>
              </VStack>
            ) : (
              <VStack align="stretch" spacing={1}>
                <Text fontSize="sm" color="gray.300">
                  No action selected
                </Text>
                <Text fontSize="xs" color="gray.400">
                  Click settings to choose an action
                </Text>
              </VStack>
            )}
          </Box>
        </VStack>
        
        <Handle
          type="source"
          position={Position.Bottom}
          style={{
            background: 'white',
            border: `2px solid ${currentScheme.colors.textSecondary}`,
            width: '12px',
            height: '12px',
            bottom: '-6px',
          }}
        />
      </Box>

      {/* Configuration Modal */}
      <Modal isOpen={isOpen} onClose={onClose} size="2xl">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent bg={currentScheme.colors.background} maxH="80vh">
          <ModalHeader color={currentScheme.colors.text}>
            🎯 Choose Discord Action
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6} overflowY="auto">
            <VStack spacing={4} align="stretch">
              <HStack spacing={2}>
                <FiSearch />
                <Input
                  placeholder="Search actions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                />
              </HStack>
              
              <Box maxH="400px" overflowY="auto">
                {Object.entries(groupedActions).map(([category, actions]) => (
                  <Box key={category} mb={4}>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleCategory(category)}
                      leftIcon={expandedCategories.has(category) ? <FiChevronDown /> : <FiChevronRight />}
                      color={currentScheme.colors.text}
                      fontWeight="bold"
                      justifyContent="flex-start"
                      w="full"
                      mb={2}
                    >
                      {category}
                    </Button>
                    
                    <Collapse in={expandedCategories.has(category)}>
                      <VStack spacing={2} align="stretch" pl={4}>
                        {actions.map((action) => (
                          <Box
                            key={action.id}
                            bg={nodeData.selectedAction?.id === action.id ? currentScheme.colors.primary : currentScheme.colors.background}
                            border="1px solid"
                            borderColor={currentScheme.colors.border}
                            borderRadius="md"
                            p={3}
                            cursor="pointer"
                            onClick={() => selectAction(action)}
                            _hover={{
                              bg: currentScheme.colors.surface,
                              borderColor: currentScheme.colors.primary,
                            }}
                            transition="all 0.2s"
                          >
                            <HStack spacing={3}>
                              <Box fontSize="lg">{action.icon}</Box>
                              <VStack align="start" spacing={0} flex={1}>
                                <Text fontSize="sm" fontWeight="medium" color={currentScheme.colors.text}>
                                  {action.name}
                                </Text>
                                <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                                  {action.description}
                                </Text>
                              </VStack>
                            </HStack>
                          </Box>
                        ))}
                      </VStack>
                    </Collapse>
                  </Box>
                ))}
              </Box>
              
              <Button
                colorScheme="blue"
                onClick={onClose}
                isDisabled={!nodeData.selectedAction}
              >
                Save Action
              </Button>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
});

OptionNode.displayName = 'OptionNode';

export default OptionNode; 