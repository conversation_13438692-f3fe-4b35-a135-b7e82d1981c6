// @ts-nocheck
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>dalCloseButton,
  Button,
  FormControl,
  FormLabel,
  Input,
  VStack,
  useToast,
  Checkbox,
  SimpleGrid,
  Box,
  Text,
  Divider,
  HStack,
  Icon,
} from '@chakra-ui/react';
import { useState } from 'react';
import { FiShield, FiMessageSquare, FiUsers, FiVolume2 } from 'react-icons/fi';

const PERMISSION_GROUPS = {
  General: {
    icon: FiShield,
    permissions: [
      'ADMINISTRATOR',
      'VIEW_AUDIT_LOG',
      'MANAGE_GUILD',
      'MANAGE_ROLES',
      '<PERSON>NA<PERSON>_CHANNELS',
      '<PERSON>NAGE_EMOJIS_AND_STICKERS',
      'MANAGE_WEBHOOKS',
      'VIEW_CHANNEL',
    ],
  },
  Text: {
    icon: FiMessageSquare,
    permissions: [
      'SEND_MESSAGES',
      'EMBED_LINKS',
      'AT<PERSON>CH_FILES',
      'ADD_REACTIONS',
      'USE_EXTERNAL_EMOJIS',
      'MENTION_EVERYONE',
      '<PERSON>NAGE_MESSAGES',
      'READ_MESSAGE_HISTORY',
    ],
  },
  Voice: {
    icon: FiVolume2,
    permissions: [
      'CONNECT',
      'SPEAK',
      'STREAM',
      'USE_VAD',
      'PRIORITY_SPEAKER',
      'MUTE_MEMBERS',
      'DEAFEN_MEMBERS',
      'MOVE_MEMBERS',
    ],
  },
  Members: {
    icon: FiUsers,
    permissions: [
      'KICK_MEMBERS',
      'BAN_MEMBERS',
      'CHANGE_NICKNAME',
      'MANAGE_NICKNAMES',
      'CREATE_INSTANT_INVITE',
    ],
  },
};

interface CreateRoleDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateRoleDialog({
  isOpen,
  onClose,
  onSuccess,
}: CreateRoleDialogProps) {
  const toast = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    color: '#99AAB5',
    permissions: [] as string[],
    hoist: false,
    mentionable: false,
  });

  const handleSubmit = async () => {
    if (!formData.name.trim()) {
      toast({
        title: 'Error',
        description: 'Role name is required',
        status: 'error',
        duration: 3000,
      });
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch('/api/discord/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create role');
      }

      toast({
        title: 'Success',
        description: 'Role created successfully',
        status: 'success',
        duration: 3000,
      });

      // Reset form
      setFormData({
        name: '',
        color: '#99AAB5',
        permissions: [],
        hoist: false,
        mentionable: false,
      });

      onSuccess();
      onClose();
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create role',
        status: 'error',
        duration: 5000,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handlePermissionChange = (permission: string) => {
    setFormData((prev) => ({
      ...prev,
      permissions: prev.permissions.includes(permission)
        ? prev.permissions.filter((p) => p !== permission)
        : [...prev.permissions, permission],
    }));
  };

  const handleChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="xl" scrollBehavior="inside">
      <ModalOverlay backdropFilter="blur(10px)" />
      <ModalContent bg="gray.800">
        <ModalHeader>Create New Role</ModalHeader>
        <ModalCloseButton />
        <ModalBody>
          <VStack spacing={6}>
            <FormControl isRequired>
              <FormLabel>Role Name</FormLabel>
              <Input
                placeholder="Enter role name"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
              />
            </FormControl>

            <FormControl>
              <FormLabel>Role Color</FormLabel>
              <Input
                type="color"
                value={formData.color}
                onChange={(e) => handleChange('color', e.target.value)}
              />
            </FormControl>

            <FormControl>
              <HStack spacing={4}>
                <Checkbox
                  isChecked={formData.hoist}
                  onChange={(e) => handleChange('hoist', e.target.checked)}
                >
                  Display role separately
                </Checkbox>
                <Checkbox
                  isChecked={formData.mentionable}
                  onChange={(e) => handleChange('mentionable', e.target.checked)}
                >
                  Allow anyone to @mention
                </Checkbox>
              </HStack>
            </FormControl>

            <Divider />

            <Text fontSize="lg" fontWeight="bold" alignSelf="flex-start">
              Permissions
            </Text>

            {Object.entries(PERMISSION_GROUPS).map(([groupName, group]) => (
              <Box key={groupName} w="full">
                <HStack mb={2}>
                  <Icon as={group.icon} />
                  <Text fontWeight="semibold">{groupName}</Text>
                </HStack>
                <SimpleGrid columns={2} spacing={2}>
                  {group.permissions.map((permission) => (
                    <Checkbox
                      key={permission}
                      isChecked={formData.permissions.includes(permission)}
                      onChange={() => handlePermissionChange(permission)}
                    >
                      {permission.split('_').map(word => 
                        word.charAt(0) + word.slice(1).toLowerCase()
                      ).join(' ')}
                    </Checkbox>
                  ))}
                </SimpleGrid>
              </Box>
            ))}
          </VStack>
        </ModalBody>
        <ModalFooter>
          <Button variant="ghost" mr={3} onClick={onClose}>
            Cancel
          </Button>
          <Button
            colorScheme="blue"
            onClick={handleSubmit}
            isLoading={isLoading}
          >
            Create Role
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
} 