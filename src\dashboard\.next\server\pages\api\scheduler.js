"use strict";(()=>{var e={};e.id=5350,e.ids=[5350],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},1858:(e,t,r)=>{r.d(t,{K:()=>l});var n=r(9021),s=r.n(n),o=r(3873),i=r.n(o),a=r(2115),d=r.n(a);function l(){try{let e=process.cwd();for(let t=0;t<5;t++){let t=i().join(e,"config.yml");if(s().existsSync(t)){let e=s().readFileSync(t,"utf8");return d().parse(e)}e=i().join(e,"..")}throw Error("Fatal: Could not find config.yml. The bot cannot start.")}catch(e){throw e}}},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},7464:(e,t,r)=>{r.r(t),r.d(t,{config:()=>E,default:()=>I,routeModule:()=>S});var n={};r.r(n),r.d(n,{config:()=>y,default:()=>v});var s=r(3433),o=r(264),i=r(584),a=r(5806),d=r(8525),l=r(1858),u=r(2518);let c=(0,l.K)(),f=null,m=c.database?.url||"mongodb://localhost:27017",b=c.database?.name||"discord_bot";async function p(){return f||(f=await u.MongoClient.connect(m,{...c.database?.options||{}})),f.db(b)}let h=e=>{let{eventType:t,name:r,isEnabled:n}=e;if(void 0===r||"boolean"!=typeof n||!t)return"Missing required fields: name, isEnabled, eventType";if("TIMED_MESSAGE"===t){let{channelId:t,message:r,intervalValue:n,intervalUnit:s}=e;if(!t||!r||!n||!s)return"For Timed Message, must provide: channelId, message, intervalValue, intervalUnit";if("number"!=typeof n||n<=0)return"Interval value must be a positive number.";if(!["seconds","minutes","hours","days","weeks","months"].includes(s))return"Invalid interval unit provided."}else{if("GUILD_EVENT"!==t)return"Invalid eventType specified";let{description:r,startTime:n,locationType:s}=e;if(!r||!n||!s)return"For Guild Event, must provide: description, startTime, locationType";if("VOICE"===s&&!e.eventChannelId)return"Voice channel is required for Voice Guild Events";if("EXTERNAL"===s&&!e.location)return"External URL is required for External Guild Events"}return null},g=(e,t)=>{let{name:r,isEnabled:n,eventType:s}=e,o={name:r,isEnabled:n,eventType:s,updatedAt:new Date};if(t&&(o.createdAt=new Date),"TIMED_MESSAGE"===s){let{channelId:t,message:r,intervalValue:n,intervalUnit:s}=e;o.channelId=t,o.message=r,o.intervalValue=n,o.intervalUnit=s,o.cron=null}else if("GUILD_EVENT"===s){let{description:t,startTime:r,endTime:n,locationType:s,eventChannelId:i,location:a}=e;o.description=t,o.startTime=r,o.endTime=n||null,o.locationType=s,"VOICE"===s?o.eventChannelId=i:o.location=a}return o};async function v(e,t){let r=await (0,a.getServerSession)(e,t,d.authOptions);if(!r?.user||!r.user.isAdmin)return t.status(401).json({error:"Unauthorized"});let n=(await p()).collection("scheduled_events"),{eventId:s}=e.query;try{switch(e.method){case"GET":{let e=await n.find({}).sort({createdAt:-1}).toArray();return t.status(200).json(e)}case"POST":{let r=h(e.body);if(r)return t.status(400).json({error:r});let s=g(e.body,!0),o=await n.insertOne(s);return t.status(201).json({...s,_id:o.insertedId})}case"PUT":{if(!s||"string"!=typeof s)return t.status(400).json({error:"Invalid event ID"});let r=h(e.body);if(r)return t.status(400).json({error:r});let o=g(e.body,!1),i=await n.updateOne({_id:new u.ObjectId(s)},{$set:o});if(0===i.matchedCount)return t.status(404).json({error:"Event not found"});return t.status(200).json({message:"Event updated successfully"})}case"DELETE":{if(!s||"string"!=typeof s)return t.status(400).json({error:"Invalid event ID"});let e=await n.deleteOne({_id:new u.ObjectId(s)});if(0===e.deletedCount)return t.status(404).json({error:"Event not found"});return t.status(200).json({message:"Event deleted successfully"})}default:return t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${e.method} Not Allowed`)}}catch(e){if(e.message.includes("BSONError"))return t.status(400).json({error:"Invalid event ID format"});return t.status(500).json({error:"Server error",details:e.message})}}let y={api:{bodyParser:!0}},I=(0,i.M)(n,"default"),E=(0,i.M)(n,"config"),S=new s.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/scheduler",pathname:"/api/scheduler",bundlePath:"",filename:""},userland:n})},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var n=r(5542),s=r.n(n);let o=require("next-auth/providers/discord");var i=r.n(o),a=r(8580);let d={providers:[i()({clientId:a.dashboardConfig.bot.clientId,clientSecret:a.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,n=t.accessToken||null;e.user.id=r,e.user.accessToken=n;let s=!1;if(r)if((a.dashboardConfig.dashboard.admins||[]).includes(r))s=!0;else{let e=a.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&a.dashboardConfig.bot.token&&a.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${a.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${a.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();s=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),n=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(n)?e:t}},secret:a.dashboardConfig.dashboard.session.secret||a.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var n=r(9021),s=r(2115),o=r.n(s),i=r(3873);let a={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>n.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");n.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=n.readFileSync(e,"utf8");a=o().parse(t)}catch(e){process.exit(1)}let d={bot:{token:a.bot.token,clientId:a.bot.clientId,clientSecret:a.bot.clientSecret,guildId:a.bot.guildId,ticketCategoryId:a.bot.ticketCategoryId||null,ticketLogChannelId:a.bot.ticketLogChannelId||null,prefix:a.bot.prefix},dashboard:{admins:a.dashboard?.admins||[],adminRoleIds:a.dashboard?.adminRoleIds||[],session:{secret:a.dashboard?.session?.secret||a.bot.clientSecret}},database:{url:a.database.url,name:a.database.name,options:{maxPoolSize:a.database.options?.maxPoolSize||10,minPoolSize:a.database.options?.minPoolSize||1,maxIdleTimeMS:a.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:a.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:a.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:a.database.options?.connectTimeoutMS||1e4,retryWrites:a.database.options?.retryWrites!==!1,retryReads:a.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../webpack-api-runtime.js");t.C(e);var r=t(t.s=7464);module.exports=r})();