import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Dynamically load commands from commands folder
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || config.commands[commandModule.data.name]?.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(`Failed to load command ${file}:`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  commands: await loadCommands(),

  events: [
    {
      name: 'ready',
      once: true,
      execute: async (client) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        logger.info(`${config.addon.name} addon loaded! Bot ready as ${client.user?.tag}`);
        
        // Log loaded commands
        const commandNames = addon.commands?.map(cmd => cmd.data.name).join(', ') || 'none';
        logger.info(`Loaded commands: ${commandNames}`);
      }
    },

    {
      name: 'interactionCreate',
      once: false,
      execute: async (interaction) => {
        if (!interaction.isStringSelectMenu()) return;
        
        // Handle help command select menu
        if (interaction.customId === 'help_category') {
          await handleHelpCategory(interaction);
        }
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(`Loading ${config.addon.name} addon with ${addon.commands?.length || 0} commands...`);
    
    // Initialize database collections if needed
    if (bot.database && config.database?.collections) {
      try {
        for (const collectionName of config.database.collections) {
          const collection = bot.database.db.collection(collectionName);
          
          // Create basic indexes based on collection type
          if (collectionName === 'command_usage') {
            await collection.createIndex({ userId: 1, commandName: 1, timestamp: -1 });
          } else if (collectionName === 'user_stats') {
            await collection.createIndex({ userId: 1, guildId: 1 });
          }
        }
        logger.info('Database collections and indexes initialized');
      } catch (error) {
        logger.warn('Failed to initialize database collections:', error);
      }
    }

    // Log configuration
    if (config.logging?.enabled) {
      logger.info(`Addon configuration loaded - Embed Color: #${config.settings.embedColor.toString(16)}`);
    }
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(`Unloading ${config.addon.name} addon...`);
  }
};

// Helper function for help command select menu
async function handleHelpCategory(interaction: any) {
  const category = interaction.values[0];
  let embed;

  switch (category) {
    case 'general':
      embed = {
        color: config.settings.embedColor,
        title: '⚡ General Commands',
        description: 'Commands available to all users',
        fields: [
          {
            name: '/ping',
            value: 'Shows bot latency and comprehensive system information',
            inline: false
          },
          {
            name: '/help [command]',
            value: 'Display this help message or get detailed info about a specific command',
            inline: false
          }
        ],
        footer: { text: 'Use /help <command> for detailed information' }
      };
      break;
      
    case 'developer':
      embed = {
        color: config.settings.embedColor,
        title: '🔧 Developer Commands',
        description: 'Advanced commands for administrators',
        fields: [
          {
            name: '/dev',
            value: 'Comprehensive bot statistics and system information (Admin only)',
            inline: false
          }
        ],
        footer: { text: 'These commands require Administrator permissions' }
      };
      break;
      
    case 'info':
      embed = {
        color: config.settings.embedColor,
        title: '📊 Bot Information',
        description: '404 Bot - Technical Details',
        fields: [
          {
            name: 'Version',
            value: config.addon.version,
            inline: true
          },
          {
            name: 'Technology',
            value: 'TypeScript + ES2024\nDiscord.js v14\nMongoDB',
            inline: true
          },
          {
            name: 'Features',
            value: '• Hot-reloadable addons\n• Comprehensive logging\n• Command cooldowns\n• Database integration',
            inline: false
          }
        ]
      };
      break;
      
    default:
      return;
  }

  await interaction.update({ embeds: [embed] });
}

export default addon; 