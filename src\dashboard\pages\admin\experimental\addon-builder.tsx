import React, { useState, useCallback, useRef, useMemo } from 'react';
import { NextPage } from 'next';
import { 
  Box, 
  Heading, 
  Button, 
  VStack, 
  HStack, 
  useColorModeValue, 
  Alert, 
  AlertIcon,
  Text,
  Badge,
  IconButton,
  Tooltip,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  useDisclosure,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  useToast,
  AlertDescription,
  SimpleGrid,
  Card,
  CardHeader,
  CardBody,
  CardFooter,
  Divider,
  Menu,
  MenuButton,
  MenuList,
  MenuItem,
  MenuDivider,
  Tag,
  TagLabel,
  TagCloseButton,
  Flex,
  Spacer,
  AlertDialog,
  AlertDialogOverlay,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogBody,
  AlertDialogFooter,
} from '@chakra-ui/react';
import { FiSave, FiDownload, FiUpload, FiPlay, FiSettings, FiHelpCircle, FiPackage, FiArrowLeft, FiTrash2, FiMoreVertical, FiClock, FiUser, FiFileText, FiShare2, FiCopy } from 'react-icons/fi';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/router';
import { useTheme } from '../../../contexts/ThemeContext';
import ReactFlow, {
  Node,
  Edge,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  MiniMap,
  ReactFlowProvider,
  Panel,
  NodeTypes,
  EdgeTypes,
  ReactFlowInstance,
  MarkerType,
  getSmoothStepPath,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Custom node types
import CommandNode from '../../../components/flow/CommandNode';
import EventNode from '../../../components/flow/EventNode';
import ActionNode from '../../../components/flow/ActionNode';
import ConditionNode from '../../../components/flow/ConditionNode';
import TriggerNode from '../../../components/flow/TriggerNode';
import ApiRequestNode from '../../../components/flow/ApiRequestNode';

const GradientEdge = ({ id, sourceX, sourceY, targetX, targetY, sourcePosition, targetPosition, style, markerEnd, data, animated }: any) => {
  const gradientId = `gradient-${id}`;
  const [path] = getSmoothStepPath({
    sourceX,
    sourceY,
    sourcePosition,
    targetX,
    targetY,
    targetPosition,
    borderRadius: 20,
  });

  return (
    <>
      <defs>
        <linearGradient id={gradientId} x1={sourceX} y1={sourceY} x2={targetX} y2={targetY} gradientUnits="userSpaceOnUse">
          <stop offset="0%" stopColor={data?.sourceColor || '#6b7280'} />
          <stop offset="100%" stopColor={data?.targetColor || '#6b7280'} />
        </linearGradient>
      </defs>
      <path
        d={path}
        stroke={`url(#${gradientId})`}
        strokeWidth={2}
        fill="none"
        markerEnd={markerEnd}
        style={style}
        className={animated ? 'react-flow__edge-path-animated' : ''}
      />
    </>
  );
};

const edgeTypes: EdgeTypes = {
  gradient: GradientEdge,
};

// Node colors mapping (computed per theme)
const useNodeColors = (currentScheme: any) => {
  return React.useMemo(() => ({
    trigger: currentScheme?.colors?.primary || '#6b7280',
    command: '#3b82f6',    // Could also be themed in future
    event: '#10b981',      // Green
    action: '#a855f7',     // Purple
    apiRequest: '#06b6d4', // Teal
    condition: '#f59e0b',  // Orange
  }), [currentScheme]);
};

const initialNodes: Node[] = [
  {
    id: '1',
    type: 'trigger',
    position: { x: 100, y: 200 },
    data: { label: 'Start Here' },
  },
];

const initialEdges: Edge[] = [];

const AddonBuilderPage: NextPage = () => {
  const { data: session } = useSession();
  const router = useRouter();
  const { currentScheme } = useTheme();
  const nodeColors = useNodeColors(currentScheme);
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [reactFlowInstance, setReactFlowInstance] = useState<ReactFlowInstance | null>(null);
  const [isBuilding, setIsBuilding] = useState(false);
  const reactFlowWrapper = useRef<HTMLDivElement>(null);
  const { isOpen: isHelpOpen, onOpen: onHelpOpen, onClose: onHelpClose } = useDisclosure();
  const { isOpen: isTemplateOpen, onOpen: onTemplateOpen, onClose: onTemplateClose } = useDisclosure();
  const { isOpen: isSaveOpen, onOpen: onSaveOpen, onClose: onSaveClose } = useDisclosure();
  const { isOpen: isLoadOpen, onOpen: onLoadOpen, onClose: onLoadClose } = useDisclosure();
  const { isOpen: isExportOpen, onOpen: onExportOpen, onClose: onExportClose } = useDisclosure();
  const { isOpen: isImportOpen, onOpen: onImportOpen, onClose: onImportClose } = useDisclosure();
  const [templates, setTemplates] = useState<any[]>([]);
  const [templateCategories, setTemplateCategories] = useState<string[]>([]);
  const [savedFlows, setSavedFlows] = useState<any[]>([]);
  const [saveFormData, setSaveFormData] = useState({
    name: '',
    description: '',
    author: '',
    version: '1.0.0',
    tags: [] as string[],
    isPublic: false
  });
  const [tagInput, setTagInput] = useState('');
  const [deleteConfirmFlow, setDeleteConfirmFlow] = useState<any>(null);
  const [exportData, setExportData] = useState('');
  const [importData, setImportData] = useState('');
  const cancelRef = useRef<HTMLButtonElement>(null);
  const toast = useToast();

  const isDeveloper = (session?.user as any)?.id === '933023999770918932';

  // Redirect if not developer (client-side only)
  React.useEffect(() => {
    if (session && !isDeveloper) {
      router.push('/');
    }
  }, [session, isDeveloper, router]);

  // Don't render if not developer
  if (session && !isDeveloper) {
    return null;
  }

  // Function to update node data from child components
  const updateNodeData = useCallback((nodeId: string, newData: any) => {
    setNodes((nds) => 
      nds.map((node) => 
        node.id === nodeId 
          ? { ...node, data: { ...node.data, ...newData } }
          : node
      )
    );
  }, [setNodes]);

  // Define node types - using useMemo to avoid recreation on each render
  const nodeTypes: NodeTypes = useMemo(() => ({
    command: (props: any) => <CommandNode {...props} updateNodeData={updateNodeData} />,
    event: (props: any) => <EventNode {...props} updateNodeData={updateNodeData} />,
    action: (props: any) => <ActionNode {...props} updateNodeData={updateNodeData} />,
    condition: (props: any) => <ConditionNode {...props} updateNodeData={updateNodeData} />,
    trigger: TriggerNode,
    apiRequest: (props: any) => <ApiRequestNode {...props} updateNodeData={updateNodeData} />,
  }), [updateNodeData]);

  const onConnect = useCallback(
    (params: Connection) => {
      // Validation logic to prevent invalid connections
      const sourceNode = nodes.find(node => node.id === params.source);
      const targetNode = nodes.find(node => node.id === params.target);
      
      if (!sourceNode || !targetNode) return;
      
      // Rule: Start can only connect to EITHER Command OR Event, not both
      if (sourceNode.type === 'trigger') {
        const existingConnections = edges.filter(edge => edge.source === params.source);
        
        if (existingConnections.length > 0) {
          const existingTargetType = nodes.find(node => node.id === existingConnections[0].target)?.type;
          
          if (existingTargetType && existingTargetType !== targetNode.type) {
            alert('❌ Flow Rule Violation!\n\nYou can only connect Start to EITHER:\n• Command (for slash commands)\n• Event (for automatic triggers)\n\nNot both! Please choose one path.');
            return;
          }
        }
        
        // Only allow Command or Event from Start
        if (targetNode.type !== 'command' && targetNode.type !== 'event') {
          alert('❌ Invalid Connection!\n\nStart can only connect to:\n• Command blocks\n• Event blocks');
          return;
        }
      }
      
      // Rule: Actions should be at the end (no outgoing connections)
      if (sourceNode.type === 'action') {
        alert('❌ Invalid Connection!\n\nAction blocks should be at the end of your flow.\nThey cannot connect to other blocks.');
        return;
      }
      
      // Get colors for gradient based on node types
      const sourceColor = nodeColors[sourceNode.type as keyof typeof nodeColors] || '#6b7280';
      const targetColor = nodeColors[targetNode.type as keyof typeof nodeColors] || '#6b7280';
      
      const edge = {
        ...params,
        type: 'gradient',
        animated: true,
        markerEnd: { type: MarkerType.ArrowClosed, color: targetColor },
        style: {
          strokeWidth: 2,
        },
        data: {
          sourceColor,
          targetColor,
        },
      };
      setEdges((eds: Edge[]) => addEdge(edge, eds));
    },
    [setEdges, nodes, edges]
  );

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const onDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const type = event.dataTransfer.getData('application/reactflow');

      if (typeof type === 'undefined' || !type || !reactFlowBounds) {
        return;
      }

      const position = reactFlowInstance?.project({
        x: event.clientX - reactFlowBounds.left,
        y: event.clientY - reactFlowBounds.top,
      });

      const newNode: Node = {
        id: `${type}-${Date.now()}`,
        type,
        position: position || { x: 0, y: 0 },
        data: { label: `New ${type}` },
      };

      setNodes((nds: Node[]) => nds.concat(newNode));
    },
    [reactFlowInstance, setNodes]
  );

  const onInit = (instance: ReactFlowInstance) => {
    setReactFlowInstance(instance);
  };

  const buildAddon = () => {
    setIsBuilding(true);
    
    // Open save modal to collect addon details before building
    setSaveFormData({
      name: 'Custom Addon',
      description: 'Generated addon from visual builder',
      author: (session?.user as any)?.name || 'Addon Builder',
      version: '1.0.0',
      tags: [],
      isPublic: false
    });
    
    onSaveOpen();
    
    // The actual build will happen in handleSaveFlow after user confirms
  };
  
  const executeBuild = async (formData: any) => {
    try {
      // Convert flow to addon structure
      const addonStructure = {
        name: formData.name,
        description: formData.description,
        author: formData.author,
        version: formData.version,
        nodes: nodes,
        edges: edges,
      };

      const response = await fetch('/api/admin/experimental/addon-builder/build', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(addonStructure),
      });

      if (response.ok) {
        toast({
          title: 'Addon Built Successfully!',
          description: 'Your addon has been built and is now available in the bot.',
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
        
        // Refresh the saved flows to include the newly built addon
        loadSavedFlows();
      } else {
        const errorData = await response.json();
        
        // Handle validation errors specially
        if (errorData.errors && Array.isArray(errorData.errors)) {
          // Create a detailed error message from validation errors
          const errorList = errorData.errors.map((error: string, index: number) => `${index + 1}. ${error}`).join('\n');
          
          toast({
            title: 'Configuration Issues Found',
            description: `Please fix these issues:\n\n${errorList}`,
            status: 'warning',
            duration: 10000,
            isClosable: true,
          });
        } else {
          toast({
            title: 'Build Failed',
            description: errorData.details || errorData.message || 'Failed to build addon. Please check your flow and try again.',
            status: 'error',
            duration: 5000,
            isClosable: true,
          });
        }
      }
    } catch (error) {
      console.error('Error building addon:', error);
      toast({
        title: 'Build Error',
        description: 'An error occurred while building the addon.',
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsBuilding(false);
    }
  };

  const saveFlow = () => {
    // Pre-populate form with session data
    setSaveFormData({
      name: '',
      description: '',
      author: (session?.user as any)?.name || '',
      version: '1.0.0',
      tags: [],
      isPublic: false
    });
    onSaveOpen();
  };

  const loadFlow = () => {
    loadSavedFlows();
    onLoadOpen();
  };

  const loadSavedFlows = async () => {
    // Load saved flows from localStorage
    const saved = localStorage.getItem('addon-builder-saved-flows');
    let localFlows = [];
    if (saved) {
      try {
        localFlows = JSON.parse(saved);
      } catch (error) {
        console.error('Error loading saved flows:', error);
      }
    }

    // Load existing custom addons from the server
    let builtAddons = [];
    try {
      const response = await fetch('/api/admin/addons');
      if (response.ok) {
        const data = await response.json();
        
        // Load built addons and check for original flow data
        for (const addon of (data.customAddons || []).filter((a: any) => a.isCustomAddon)) {
          try {
            // Try to fetch the original flow data
            const flowResponse = await fetch(`/api/admin/addons/${addon.name}/flow`);
            let flowData = null;
            let hasOriginalFlow = false;
            
            if (flowResponse.ok) {
              flowData = await flowResponse.json();
              hasOriginalFlow = true;
            }
            
            builtAddons.push({
              id: `built-${addon.name}`,
              name: addon.name,
              description: addon.description || 'Generated addon from visual builder',
              author: 'Addon Builder',
              version: addon.version || '1.0.0',
              tags: hasOriginalFlow ? ['built-addon', 'recoverable'] : ['built-addon', 'template-only'],
              isBuilt: true,
              hasOriginalFlow,
              isPublic: false,
              createdAt: flowData?.metadata?.createdAt || new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              nodeCount: flowData?.nodes?.length || 0,
              edgeCount: flowData?.edges?.length || 0,
              nodes: flowData?.nodes || [],
              edges: flowData?.edges || []
            });
          } catch (error) {
            console.error(`Error loading flow data for ${addon.name}:`, error);
            // Fallback to basic template info
            builtAddons.push({
              id: `built-${addon.name}`,
              name: addon.name,
              description: addon.description || 'Generated addon from visual builder',
              author: 'Addon Builder',
              version: addon.version || '1.0.0',
              tags: ['built-addon', 'template-only'],
              isBuilt: true,
              hasOriginalFlow: false,
              isPublic: false,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              nodeCount: 0,
              edgeCount: 0,
              nodes: [],
              edges: []
            });
          }
        }
      }
    } catch (error) {
      console.error('Error loading built addons:', error);
    }

    // Combine saved flows and built addons
    setSavedFlows([...localFlows, ...builtAddons]);
  };

  const handleSaveFlow = () => {
    if (!saveFormData.name.trim()) {
      toast({
        title: 'Name Required',
        description: 'Please enter a name for your addon.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }
    
    // Check if this is a build operation
    if (isBuilding) {
      executeBuild(saveFormData);
      onSaveClose();
      return;
    }

    if (!reactFlowInstance) {
      toast({
        title: 'Error',
        description: 'Flow instance not available.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const flow = reactFlowInstance.toObject();
    const flowData = {
      id: Date.now().toString(),
      name: saveFormData.name,
      description: saveFormData.description,
      author: saveFormData.author,
      version: saveFormData.version,
      tags: saveFormData.tags,
      isPublic: saveFormData.isPublic,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      nodeCount: flow.nodes.length,
      edgeCount: flow.edges.length,
      nodes: flow.nodes,
      edges: flow.edges
    };

    // Get existing saved flows
    const existingSaved = localStorage.getItem('addon-builder-saved-flows');
    let savedFlows = [];
    if (existingSaved) {
      try {
        savedFlows = JSON.parse(existingSaved);
      } catch (error) {
        console.error('Error parsing saved flows:', error);
      }
    }

    // Check if flow with same name exists
    const existingIndex = savedFlows.findIndex((f: any) => f.name === flowData.name);
    if (existingIndex !== -1) {
      // Update existing flow
      savedFlows[existingIndex] = { ...savedFlows[existingIndex], ...flowData, updatedAt: new Date().toISOString() };
    } else {
      // Add new flow
      savedFlows.push(flowData);
    }

    // Save to localStorage
    localStorage.setItem('addon-builder-saved-flows', JSON.stringify(savedFlows));

    toast({
      title: 'Flow Saved!',
      description: `"${flowData.name}" has been saved successfully.`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });

    onSaveClose();
  };

  const handleLoadFlow = (flowData: any) => {
    // Helper function to convert edges to gradient edges
    const convertToGradientEdges = (edges: Edge[], nodes: Node[]) => {
      return edges.map(edge => {
        const sourceNode = nodes.find(node => node.id === edge.source);
        const targetNode = nodes.find(node => node.id === edge.target);
        
        if (sourceNode && targetNode) {
          const sourceColor = nodeColors[sourceNode.type as keyof typeof nodeColors] || '#6b7280';
          const targetColor = nodeColors[targetNode.type as keyof typeof nodeColors] || '#6b7280';
          
          return {
            ...edge,
            type: 'gradient',
            animated: true,
            markerEnd: { type: MarkerType.ArrowClosed, color: targetColor },
            style: {
              strokeWidth: 2,
            },
            data: {
              sourceColor,
              targetColor,
            },
          };
        }
        return edge;
      });
    };

    if (flowData.isBuilt) {
      if (flowData.hasOriginalFlow && flowData.nodes.length > 0) {
        // For built addons with original flow data, load the exact flow
        setNodes(flowData.nodes);
        setEdges(convertToGradientEdges(flowData.edges, flowData.nodes));
        
        toast({
          title: 'Original Flow Recovered!',
          description: `"${flowData.name}" original flow loaded successfully. You can now edit and rebuild it.`,
          status: 'success',
          duration: 5000,
          isClosable: true,
        });
      } else {
        // For built addons without original flow data, create a basic template
        const templateNodes = [
          {
            id: '1',
            type: 'trigger',
            position: { x: 100, y: 200 },
            data: { label: 'Start Here' },
          },
          {
            id: 'info-note',
            type: 'command',
            position: { x: 300, y: 200 },
            data: { 
              label: `${flowData.name} (Template)`,
              commandName: 'example',
              description: 'This is a basic template. The original flow data was not saved when this addon was built.'
            },
          },
        ];

        const templateEdges = [
          {
            id: 'start-to-command',
            source: '1',
            target: 'info-note',
            type: 'gradient',
            animated: true,
            markerEnd: { type: MarkerType.ArrowClosed, color: nodeColors.command },
            style: {
              strokeWidth: 2,
            },
            data: {
              sourceColor: nodeColors.trigger,
              targetColor: nodeColors.command,
            },
          },
        ];

        setNodes(templateNodes);
        setEdges(templateEdges);
        
        toast({
          title: 'Template Loaded',
          description: `"${flowData.name}" basic template loaded. Original flow data not available - this addon was built before flow recovery was implemented.`,
          status: 'warning',
          duration: 6000,
          isClosable: true,
        });
      }
    } else {
      // For saved flows, load the exact nodes and edges (convert to gradient edges)
      setNodes(flowData.nodes);
      setEdges(convertToGradientEdges(flowData.edges, flowData.nodes));
      
      toast({
        title: 'Flow Loaded!',
        description: `"${flowData.name}" has been loaded successfully.`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
    }

    onLoadClose();
    
    // Fit view and center the flow after a short delay
    setTimeout(() => {
      if (reactFlowInstance) {
        reactFlowInstance.fitView({
          padding: 0.2,
          includeHiddenNodes: false,
          minZoom: 0.5,
          maxZoom: 1.5,
        });
      }
    }, 100);
  };

  const confirmDelete = (flowData: any) => {
    setDeleteConfirmFlow(flowData);
  };

  const handleDeleteFlow = async () => {
    if (!deleteConfirmFlow) return;
    
    const flowData = deleteConfirmFlow;
    setDeleteConfirmFlow(null);
    
    // If it's a built addon, delete it via API
    if (flowData.isBuilt) {
      try {
        const response = await fetch(`/api/admin/addons/${flowData.name}`, {
          method: 'DELETE',
          credentials: 'include',
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.details || errorData.error || 'Failed to delete addon');
        }

        // Remove from saved flows list
        setSavedFlows(prev => prev.filter(f => f.id !== flowData.id));
        
        toast({
          title: 'Addon Deleted',
          description: 'The built addon has been deleted successfully.',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } catch (error: any) {
        toast({
          title: 'Delete Failed',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        });
      }
    } else {
      // If it's a saved flow, delete from localStorage
      const existingSaved = localStorage.getItem('addon-builder-saved-flows');
      if (existingSaved) {
        try {
          const savedFlows = JSON.parse(existingSaved);
          const filteredFlows = savedFlows.filter((f: any) => f.id !== flowData.id);
          localStorage.setItem('addon-builder-saved-flows', JSON.stringify(filteredFlows));
          setSavedFlows(prev => prev.filter(f => f.id !== flowData.id));
          
          toast({
            title: 'Flow Deleted',
            description: 'The saved flow has been deleted successfully.',
            status: 'info',
            duration: 3000,
            isClosable: true,
          });
        } catch (error) {
          console.error('Error deleting flow:', error);
          toast({
            title: 'Delete Failed',
            description: 'Failed to delete the saved flow.',
            status: 'error',
            duration: 3000,
            isClosable: true,
          });
        }
      }
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !saveFormData.tags.includes(tagInput.trim())) {
      setSaveFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setSaveFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/admin/experimental/addon-builder/templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates);
        setTemplateCategories(data.categories);
      }
    } catch (error) {
      console.error('Error loading templates:', error);
    }
  };

  const loadTemplate = (template: any) => {
    // Helper function to convert edges to gradient edges
    const convertToGradientEdges = (edges: Edge[], nodes: Node[]) => {
      return edges.map(edge => {
        const sourceNode = nodes.find(node => node.id === edge.source);
        const targetNode = nodes.find(node => node.id === edge.target);
        
        if (sourceNode && targetNode) {
          const sourceColor = nodeColors[sourceNode.type as keyof typeof nodeColors] || '#6b7280';
          const targetColor = nodeColors[targetNode.type as keyof typeof nodeColors] || '#6b7280';
          
          return {
            ...edge,
            type: 'gradient',
            animated: true,
            markerEnd: { type: MarkerType.ArrowClosed, color: targetColor },
            style: {
              strokeWidth: 2,
            },
            data: {
              sourceColor,
              targetColor,
            },
          };
        }
        return edge;
      });
    };

    setNodes(template.nodes);
    setEdges(convertToGradientEdges(template.edges, template.nodes));
    onTemplateClose();
    
    // Fit view and center the flow after a short delay
    setTimeout(() => {
      if (reactFlowInstance) {
        reactFlowInstance.fitView({
          padding: 0.2,
          includeHiddenNodes: false,
          minZoom: 0.5,
          maxZoom: 1.5,
        });
      }
    }, 100);
    
    toast({
      title: 'Template Loaded!',
      description: `"${template.name}" has been loaded successfully.`,
      status: 'success',
      duration: 3000,
      isClosable: true,
    });
  };

  const generateFlowCode = (flowData: any) => {
    // Create a base64 encoded string from the flow data
    const jsonString = JSON.stringify(flowData);
    const base64 = btoa(jsonString);
    
    // Generate random segments for the code
    const segments = [];
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    
    // First segment: addon name prefix
    const addonName = (flowData.name || 'addon').toLowerCase().replace(/[^a-z0-9]/g, '');
    segments.push(addonName);
    
    // Add encoded data in chunks with random separators
    const chunkSize = 8;
    for (let i = 0; i < base64.length; i += chunkSize) {
      segments.push(base64.slice(i, i + chunkSize));
    }
    
    // Join with underscores
    return segments.join('_');
  };

  const handleExportFlow = () => {
    if (!reactFlowInstance) {
      toast({
        title: 'Error',
        description: 'Flow instance not available.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    const flow = reactFlowInstance.toObject();
    const exportFlowData = {
      name: saveFormData.name || 'Untitled Flow',
      description: saveFormData.description || 'Exported flow from Visual Addon Builder',
      author: saveFormData.author || (session?.user as any)?.email || 'Unknown',
      version: saveFormData.version || '1.0.0',
      tags: saveFormData.tags || [],
      exportedAt: new Date().toISOString(),
      exportedBy: (session?.user as any)?.email || 'Unknown',
      nodeCount: flow.nodes.length,
      edgeCount: flow.edges.length,
      builderVersion: '1.0.0',
      nodes: flow.nodes,
      edges: flow.edges,
    };

    const exportCode = generateFlowCode(exportFlowData);
    setExportData(exportCode);
    onExportOpen();
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: 'Copied!',
        description: 'Flow data copied to clipboard.',
        status: 'success',
        duration: 2000,
        isClosable: true,
      });
    } catch (err) {
      toast({
        title: 'Copy Failed',
        description: 'Failed to copy to clipboard. Please manually copy the text.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
    }
  };

  const downloadAsFile = (data: string, filename: string) => {
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const decodeFlowCode = (code: string) => {
    try {
      // Split by underscores and remove the addon name prefix
      const segments = code.split('_');
      const addonName = segments[0];
      const dataParts = segments.slice(1);
      
      // Reconstruct the base64 string
      const base64 = dataParts.join('');
      
      // Decode from base64
      const jsonString = atob(base64);
      const flowData = JSON.parse(jsonString);
      
      return flowData;
    } catch (error) {
      throw new Error('Invalid flow code format');
    }
  };

  const handleImportFlow = async () => {
    if (!importData.trim()) {
      toast({
        title: 'Import Error',
        description: 'Please paste the flow code to import.',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    try {
      // Check if it's a code string or JSON
      let importedFlow;
      if (importData.trim().startsWith('{')) {
        // Legacy JSON format
        importedFlow = JSON.parse(importData);
      } else {
        // New code format
        importedFlow = decodeFlowCode(importData.trim());
      }
      
      // Validate the imported data structure
      if (!importedFlow.nodes || !importedFlow.edges || !Array.isArray(importedFlow.nodes) || !Array.isArray(importedFlow.edges)) {
        throw new Error('Invalid flow data structure. Missing nodes or edges.');
      }

      // Additional validation for flow integrity
      if (importedFlow.nodes.length === 0) {
        throw new Error('Flow appears to be empty. No nodes found.');
      }

      // Check if this flow was from a built addon and if that addon still exists
      let sourceAddonStatus = null;
      if (importedFlow.name && importedFlow.isBuilt) {
        try {
          const response = await fetch('/api/admin/addons');
          if (response.ok) {
            const data = await response.json();
            const existingAddon = data.customAddons?.find((addon: any) => addon.name === importedFlow.name);
            sourceAddonStatus = existingAddon ? 'exists' : 'deleted';
          }
        } catch (error) {
          console.warn('Could not check addon status:', error);
        }
      }

      // Helper function to convert edges to gradient edges
      const convertToGradientEdges = (edges: Edge[], nodes: Node[]) => {
        return edges.map(edge => {
          const sourceNode = nodes.find(node => node.id === edge.source);
          const targetNode = nodes.find(node => node.id === edge.target);
          
          if (sourceNode && targetNode) {
            const sourceColor = nodeColors[sourceNode.type as keyof typeof nodeColors] || '#6b7280';
            const targetColor = nodeColors[targetNode.type as keyof typeof nodeColors] || '#6b7280';
            
            return {
              ...edge,
              type: 'gradient',
              animated: true,
              markerEnd: { type: MarkerType.ArrowClosed, color: targetColor },
              style: {
                strokeWidth: 2,
              },
              data: {
                sourceColor,
                targetColor,
              },
            };
          }
          return edge;
        });
      };

      // Load the imported flow
      setNodes(importedFlow.nodes);
      setEdges(convertToGradientEdges(importedFlow.edges, importedFlow.nodes));
      
      // Update form data with imported metadata
      setSaveFormData({
        name: importedFlow.name || 'Imported Flow',
        description: importedFlow.description || 'Imported from shared flow',
        author: importedFlow.author || 'Unknown',
        version: importedFlow.version || '1.0.0',
        tags: importedFlow.tags || [],
        isPublic: false
      });

      // Show appropriate success message based on source addon status
      let successMessage = `Successfully imported "${importedFlow.name}" with ${importedFlow.nodeCount || importedFlow.nodes.length} nodes.`;
      let toastStatus: 'success' | 'warning' = 'success';
      
      if (sourceAddonStatus === 'deleted') {
        successMessage = `Flow imported from "${importedFlow.name}" but the original addon has been deleted. You have the flow structure and can rebuild it.`;
        toastStatus = 'warning';
      } else if (sourceAddonStatus === 'exists') {
        successMessage = `Flow imported from "${importedFlow.name}". The original addon still exists, so you can compare or make modifications.`;
      } else if (importedFlow.isBuilt) {
        successMessage = `Flow imported from "${importedFlow.name}" (built addon). Status of original addon unknown.`;
        toastStatus = 'warning';
      }

      toast({
        title: 'Flow Imported!',
        description: successMessage,
        status: toastStatus,
        duration: 5000,
        isClosable: true,
      });

      setImportData('');
      onImportClose();
      
      // Fit view and center the flow after a short delay
      setTimeout(() => {
        if (reactFlowInstance) {
          reactFlowInstance.fitView({
            padding: 0.2,
            includeHiddenNodes: false,
            minZoom: 0.5,
            maxZoom: 1.5,
          });
        }
      }, 100);
    } catch (error: any) {
      toast({
        title: 'Import Failed',
        description: `Failed to import flow: ${error.message}`,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  // Custom deletion handler that works with Delete key only (Backspace reserved for text editing)
  const handleCustomDeletion = useCallback(() => {
    // Don't delete the Start trigger node - it's protected
    const selectedNodes = nodes.filter(node => node.selected);
    const hasStartNode = selectedNodes.some(node => node.type === 'trigger');
    
    if (hasStartNode && selectedNodes.length === 1) {
      // Don't delete if only the Start node is selected
      return;
    }
    
    // Delete selected nodes (except Start node) and edges
    setNodes(nds => nds.filter(node => !node.selected || node.type === 'trigger'));
    setEdges(eds => eds.filter(edge => !edge.selected));
  }, [setNodes, setEdges, nodes]);

  const onKeyDown = useCallback((event: KeyboardEvent) => {
    // Check if we're typing in an input field
    const target = event.target as HTMLElement;
    const isInInputField = target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable;
    
    // Only handle deletion keys when NOT in an input field
    if (!isInInputField) {
      // Only allow Delete key for block deletion (not Backspace)
      if (event.key === 'Delete') {
        event.preventDefault();
        handleCustomDeletion();
      }
      // Prevent Backspace from doing anything when not in input fields
      if (event.key === 'Backspace') {
        event.preventDefault();
        event.stopPropagation();
      }
    }
  }, [handleCustomDeletion]);

  // Override ReactFlow's deletion handlers to prevent any built-in deletion
  const onNodesDelete = useCallback(() => {
    // Do nothing - we handle deletion manually
  }, []);

  const onEdgesDelete = useCallback(() => {
    // Do nothing - we handle deletion manually  
  }, []);

  // Function to ensure all edges use gradient type
  const ensureGradientEdges = useCallback(() => {
    setEdges(currentEdges => 
      currentEdges.map(edge => {
        if (edge.type !== 'gradient') {
          const sourceNode = nodes.find(node => node.id === edge.source);
          const targetNode = nodes.find(node => node.id === edge.target);
          
          if (sourceNode && targetNode) {
            const sourceColor = nodeColors[sourceNode.type as keyof typeof nodeColors] || '#6b7280';
            const targetColor = nodeColors[targetNode.type as keyof typeof nodeColors] || '#6b7280';
            
            return {
              ...edge,
              type: 'gradient',
              animated: true,
              markerEnd: { type: MarkerType.ArrowClosed, color: targetColor },
              style: {
                strokeWidth: 2,
              },
              data: {
                sourceColor,
                targetColor,
              },
            };
          }
        }
        return edge;
      })
    );
  }, [nodes, setEdges]);

  // Update existing edge colors whenever theme changes
  React.useEffect(() => {
    setEdges((eds) => eds.map(edge => {
      const sourceNode = nodes.find(n => n.id === edge.source);
      const targetNode = nodes.find(n => n.id === edge.target);
      if (!sourceNode || !targetNode) return edge;
      const newSourceColor = nodeColors[sourceNode.type as keyof typeof nodeColors] || '#6b7280';
      const newTargetColor = nodeColors[targetNode.type as keyof typeof nodeColors] || '#6b7280';
      return {
        ...edge,
        data: {
          ...edge.data,
          sourceColor: newSourceColor,
          targetColor: newTargetColor,
        },
      };
    }));
  }, [currentScheme, nodes, nodeColors, setEdges]);

  React.useEffect(() => {
    loadTemplates();
    loadSavedFlows();
  }, []);

  // Ensure edges are gradient type when nodes change
  React.useEffect(() => {
    ensureGradientEdges();
  }, [ensureGradientEdges]);

  React.useEffect(() => {
    document.addEventListener('keydown', onKeyDown);
    return () => {
      document.removeEventListener('keydown', onKeyDown);
    };
  }, [onKeyDown]);

  return (
    <Box h="100vh" w="100%" bg={currentScheme.colors.background}>
      <VStack spacing={0} h="full">
        {/* Header */}
        <Box
          w="full"
          bg={currentScheme.colors.surface}
          borderBottom="1px solid"
          borderColor={currentScheme.colors.border}
          px={6}
          py={4}
        >
          <HStack justify="space-between" align="center">
            <VStack align="start" spacing={1}>
              <HStack align="center" spacing={3}>
                <IconButton
                  icon={<FiArrowLeft />}
                  aria-label="Go Back"
                  size="md"
                  colorScheme="blue"
                  variant="solid"
                  onClick={() => router.back()}
                  bg="blue.500"
                  color="white"
                  _hover={{ bg: "blue.600" }}
                  _active={{ bg: "blue.700" }}
                  border="1px solid"
                  borderColor="blue.400"
                />
                <Heading size="lg" color={currentScheme.colors.text}>
                  🎨 Visual Addon Builder
                </Heading>
              </HStack>
              <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                Build addons with drag-and-drop building blocks
              </Text>
            </VStack>
            
            <HStack spacing={2}>
              <Tooltip label="Help & Documentation">
                <IconButton
                  icon={<FiHelpCircle />}
                  aria-label="Help"
                  size="sm"
                  variant="ghost"
                  colorScheme="yellow"
                  onClick={onHelpOpen}
                />
              </Tooltip>
              
              <Button
                leftIcon={<FiUpload />}
                size="sm"
                variant="ghost"
                colorScheme="blue"
                onClick={loadFlow}
              >
                Load
              </Button>
              
              <Button
                leftIcon={<FiShare2 />}
                size="sm"
                variant="ghost"
                colorScheme="purple"
                onClick={handleExportFlow}
              >
                Export Code
              </Button>
              
              <Button
                leftIcon={<FiDownload />}
                size="sm"
                variant="ghost"
                colorScheme="green"
                onClick={onImportOpen}
              >
                Import Code
              </Button>
              
              <Button
                leftIcon={<FiPlay />}
                size="sm"
                colorScheme="green"
                isLoading={isBuilding}
                loadingText="Building..."
                onClick={buildAddon}
              >
                Build Addon
              </Button>
            </HStack>
          </HStack>
        </Box>

        {/* Main Content */}
        <Box flex="1" w="full" position="relative">
          <ReactFlowProvider>
            <div ref={reactFlowWrapper} style={{ width: '100%', height: '100%' }}>
              <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                onEdgesChange={onEdgesChange}
                onNodesDelete={onNodesDelete}
                onEdgesDelete={onEdgesDelete}
                onConnect={onConnect}
                onInit={onInit}
                onDrop={onDrop}
                onDragOver={onDragOver}
                nodeTypes={nodeTypes}
                edgeTypes={edgeTypes}
                fitView
                proOptions={{ hideAttribution: true }}
                deleteKeyCode={null}
                multiSelectionKeyCode={null}
                style={{
                  backgroundColor: currentScheme.colors.background,
                }}
              >
                <Background color={currentScheme.colors.border} gap={16} />
                
                {/* Node Palette */}
                                 <Panel position="top-left">
                   <VStack
                     bg={currentScheme.colors.surface}
                     border="1px solid"
                     borderColor={currentScheme.colors.border}
                     borderRadius="lg"
                     p={4}
                     spacing={3}
                     align="stretch"
                     minW="220px"
                     maxH="80vh"
                     overflowY="auto"
                   >
                     <HStack justify="space-between" align="center">
                       <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                         🎨 Building Blocks
                       </Text>
                       <HStack spacing={1}>
                         <Tooltip label="Load pre-built templates">
                           <IconButton
                             icon={<FiPackage />}
                             size="xs"
                             variant="ghost"
                             colorScheme="teal"
                             aria-label="Templates"
                             onClick={onTemplateOpen}
                           />
                         </Tooltip>
                         <Tooltip label="Hover blocks for detailed info">
                           <IconButton
                             icon={<FiHelpCircle />}
                             size="xs"
                             variant="ghost"
                             aria-label="Help"
                           />
                         </Tooltip>
                       </HStack>
                     </HStack>
                    
                                                              {[
                       { 
                         type: 'command', 
                         label: 'Command', 
                         icon: '⚡', 
                         color: 'blue',
                         description: 'Slash commands users can execute\n• /ping, /ban, /kick\n• Click to configure options',
                         rules: 'Connect directly from Start (Command path)'
                       },
                       { 
                         type: 'event', 
                         label: 'Event', 
                         icon: '📡', 
                         color: 'green',
                         description: 'Discord events that trigger actions\n• Member joins/leaves\n• Message reactions\n• Voice changes',
                         rules: 'Connect directly from Start (Event path)'
                       },
                       { 
                         type: 'action', 
                         label: 'Action', 
                         icon: '🎯', 
                         color: 'purple',
                         description: 'What happens when triggered\n• Send messages/embeds\n• Add/remove roles\n• Kick/ban users',
                         rules: 'Must connect to Command/Event/Condition'
                       },
                       { 
                         type: 'apiRequest', 
                         label: 'API Request', 
                         icon: '🌐', 
                         color: 'teal',
                         description: 'Make HTTP requests to external APIs\n• GET, POST, PUT, DELETE\n• Custom headers and body\n• Save response to variable',
                         rules: 'Can be used anywhere in your flow'
                       },
                       { 
                         type: 'condition', 
                         label: 'Condition', 
                         icon: '❓', 
                         color: 'orange',
                         description: 'Check if something is true\n• User has role/permission\n• Message contains text\n• Two outputs: TRUE/FALSE',
                         rules: 'Connect TRUE/FALSE paths to different actions'
                       },
                     ].map((item) => (
                       <Tooltip
                         key={item.type}
                         label={
                           <Box p={2}>
                             <Text fontWeight="bold" mb={1}>{item.label}</Text>
                             <Text fontSize="xs" mb={2} whiteSpace="pre-line">
                               {item.description}
                             </Text>
                             <Text fontSize="xs" color="yellow.300" fontWeight="bold">
                               💡 {item.rules}
                             </Text>
                           </Box>
                         }
                         placement="right"
                         hasArrow
                         bg={currentScheme.colors.surface}
                         color={currentScheme.colors.text}
                         borderRadius="md"
                         p={0}
                       >
                         <Box
                           draggable
                           onDragStart={(event) => {
                             event.dataTransfer.setData('application/reactflow', item.type);
                             event.dataTransfer.effectAllowed = 'move';
                           }}
                           bg={currentScheme.colors.background}
                           border="1px solid"
                           borderColor={currentScheme.colors.border}
                           borderRadius="md"
                           p={3}
                           cursor="grab"
                           _hover={{
                             bg: currentScheme.colors.surface,
                             transform: 'scale(1.02)',
                             borderColor: currentScheme.colors.primary,
                           }}
                           _active={{
                             cursor: 'grabbing',
                             transform: 'scale(0.98)',
                           }}
                           transition="all 0.2s"
                         >
                           <VStack spacing={2}>
                             <HStack spacing={2} width="full">
                               <Text fontSize="lg">{item.icon}</Text>
                               <Text fontSize="sm" fontWeight="medium" color={currentScheme.colors.text}>
                                 {item.label}
                               </Text>
                             </HStack>
                             <Badge size="sm" colorScheme={item.color} width="full">
                               Drag to canvas
                             </Badge>
                                                      </VStack>
                         </Box>
                       </Tooltip>
                     ))}
                     

                   </VStack>
                 </Panel>
              </ReactFlow>
            </div>
          </ReactFlowProvider>
        </Box>
      </VStack>

      {/* Help Modal */}
      <Modal isOpen={isHelpOpen} onClose={onHelpClose} size="xl">
        <ModalOverlay />
        <ModalContent bg={currentScheme.colors.background}>
          <ModalHeader color={currentScheme.colors.text}>
            🎨 How to Use the Visual Addon Builder
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="start">
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <VStack align="start" spacing={1}>
                  <Text fontWeight="bold">Welcome to the Visual Addon Builder!</Text>
                  <Text fontSize="sm">
                    Create Discord bot addons using intuitive drag-and-drop building blocks.
                  </Text>
                </VStack>
              </Alert>

              <Box>
                <Text fontWeight="bold" mb={2} color={currentScheme.colors.text}>
                  🔧 Building Blocks:
                </Text>
                <VStack align="start" spacing={1} pl={4}>
                  <Text fontSize="sm">⚡ <strong>Command</strong> - Slash commands users can execute</Text>
                  <Text fontSize="sm">📡 <strong>Event</strong> - Discord events like messages or reactions</Text>
                  <Text fontSize="sm">🎯 <strong>Action</strong> - What happens when triggered</Text>
                  <Text fontSize="sm">❓ <strong>Condition</strong> - Check if something is true</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color={currentScheme.colors.text}>
                  🎮 How to Create Flows:
                </Text>
                <VStack align="start" spacing={2} pl={4}>
                  <Text fontSize="sm">1. <strong>Start:</strong> Every flow begins with the "Start Here" trigger</Text>
                  <Text fontSize="sm">2. <strong>Add Logic:</strong> Drag Command or Event blocks to define when things happen</Text>
                  <Text fontSize="sm">3. <strong>Connect:</strong> Drag from the bottom of one block to the top of another</Text>
                  <Text fontSize="sm">4. <strong>Configure:</strong> Click the ⚙️ icon to set up each block</Text>
                  <Text fontSize="sm">5. <strong>Add Actions:</strong> End with Action blocks to define what happens</Text>
                  <Text fontSize="sm">6. <strong>Save & Build:</strong> Save your work and click "Build Addon"</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color={currentScheme.colors.text}>
                  🔗 Connection Rules:
                </Text>
                <VStack align="start" spacing={1} pl={4}>
                  <Text fontSize="sm">• <strong>Choose ONE path:</strong> Either Command OR Event from Start</Text>
                  <Text fontSize="sm">• <strong>Commands:</strong> For slash commands (/ping, /ban)</Text>
                  <Text fontSize="sm">• <strong>Events:</strong> For automatic triggers (joins, reactions)</Text>
                  <Text fontSize="sm">• <strong>Conditions:</strong> Have TWO outputs (True/False)</Text>
                  <Text fontSize="sm">• <strong>Actions:</strong> Should be at the end of each path</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color={currentScheme.colors.text}>
                  🗑️ Deleting Blocks:
                </Text>
                <VStack align="start" spacing={1} pl={4}>
                  <Text fontSize="sm">1. Click a block to select it (it will highlight)</Text>
                  <Text fontSize="sm">2. Press <strong>Delete</strong> key (not Backspace)</Text>
                  <Text fontSize="sm">3. The block and its connections will be removed</Text>
                  <Text fontSize="sm" color="green.400">✅ Backspace works in text fields for typing</Text>
                  <Text fontSize="sm" color="yellow.400">💡 You cannot delete the Start block</Text>
                </VStack>
              </Box>

              <Box>
                <Text fontWeight="bold" mb={2} color={currentScheme.colors.text}>
                  💡 Pro Tips:
                </Text>
                <VStack align="start" spacing={1} pl={4}>
                  <Text fontSize="sm">• Use Templates for quick starts</Text>
                  <Text fontSize="sm">• Hover over blocks in the palette for help</Text>
                  <Text fontSize="sm">• Conditions let you create smart logic</Text>
                  <Text fontSize="sm">• Always test with simple flows first</Text>
                </VStack>
              </Box>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Template Selection Modal */}
      <Modal isOpen={isTemplateOpen} onClose={onTemplateClose} size="4xl">
        <ModalOverlay />
        <ModalContent bg={currentScheme.colors.background}>
          <ModalHeader color={currentScheme.colors.text}>
            🎨 Choose a Template
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                Start with a pre-built template to create your addon faster
              </Text>
              
              <Box
                maxH="500px"
                overflowY="auto"
                pr={2}
              >
                <VStack spacing={6} align="stretch">
                  {templateCategories.map((category) => (
                    <Box key={category}>
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text} mb={4}>
                        {category}
                      </Text>
                      <Box
                        display="grid"
                        gridTemplateColumns="repeat(auto-fit, minmax(300px, 1fr))"
                        gap={4}
                      >
                        {templates
                          .filter(template => template.category === category)
                          .map((template) => (
                            <Box
                              key={template.id}
                              bg={currentScheme.colors.background}
                              border="1px solid"
                              borderColor={currentScheme.colors.border}
                              borderRadius="lg"
                              p={4}
                              cursor="pointer"
                              position="relative"
                              _hover={{
                                bg: currentScheme.colors.surface,
                                borderColor: currentScheme.colors.primary,
                                transform: 'translateY(-2px)',
                                boxShadow: 'lg',
                              }}
                              transition="all 0.2s"
                              onClick={() => loadTemplate(template)}
                            >
                              <VStack spacing={3} align="stretch">
                                <HStack justify="space-between" align="start">
                                  <HStack spacing={3}>
                                    <Box
                                      bg={currentScheme.colors.primary}
                                      color="white"
                                      borderRadius="lg"
                                      p={2}
                                      fontSize="xl"
                                    >
                                      {template.thumbnail}
                                    </Box>
                                    <VStack align="start" spacing={1}>
                                      <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text}>
                                        {template.name}
                                      </Text>
                                      <Badge colorScheme="blue" size="sm">
                                        {template.nodes.length} blocks
                                      </Badge>
                                    </VStack>
                                  </HStack>
                                </HStack>
                                <Text fontSize="sm" color={currentScheme.colors.textSecondary} lineHeight="1.4">
                                  {template.description}
                                </Text>
                                <Button
                                  size="sm"
                                  colorScheme="blue"
                                  variant="ghost"
                                  width="full"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    loadTemplate(template);
                                  }}
                                >
                                  Use Template
                                </Button>
                              </VStack>
                            </Box>
                          ))}
                      </Box>
                    </Box>
                  ))}
                </VStack>
              </Box>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Enhanced Save Modal */}
      <Modal isOpen={isSaveOpen} onClose={onSaveClose} size="lg">
        <ModalOverlay />
        <ModalContent bg={currentScheme.colors.background}>
          <ModalHeader color={currentScheme.colors.text}>
            💾 Save Addon Flow
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <AlertDescription fontSize="sm">
                  Save your addon flow with metadata for easy organization and sharing.
                </AlertDescription>
              </Alert>

              <SimpleGrid columns={2} spacing={4}>
                <FormControl isRequired>
                  <FormLabel color={currentScheme.colors.text}>Name</FormLabel>
                  <Input
                    value={saveFormData.name}
                    onChange={(e) => setSaveFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="My Awesome Addon"
                    bg={currentScheme.colors.background}
                    color={currentScheme.colors.text}
                    borderColor={currentScheme.colors.border}
                  />
                </FormControl>
                
                <FormControl>
                  <FormLabel color={currentScheme.colors.text}>Version</FormLabel>
                  <Input
                    value={saveFormData.version}
                    onChange={(e) => setSaveFormData(prev => ({ ...prev, version: e.target.value }))}
                    placeholder="1.0.0"
                    bg={currentScheme.colors.background}
                    color={currentScheme.colors.text}
                    borderColor={currentScheme.colors.border}
                  />
                </FormControl>
              </SimpleGrid>

              <FormControl>
                <FormLabel color={currentScheme.colors.text}>Author</FormLabel>
                <Input
                  value={saveFormData.author}
                  onChange={(e) => setSaveFormData(prev => ({ ...prev, author: e.target.value }))}
                  placeholder="Your Name"
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                />
              </FormControl>

              <FormControl>
                <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                <Textarea
                  value={saveFormData.description}
                  onChange={(e) => setSaveFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe what your addon does..."
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                  rows={3}
                />
              </FormControl>

              <FormControl>
                <FormLabel color={currentScheme.colors.text}>Tags</FormLabel>
                <HStack spacing={2}>
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Add tags (press Enter)"
                    onKeyDown={(e) => e.key === 'Enter' && addTag()}
                    bg={currentScheme.colors.background}
                    color={currentScheme.colors.text}
                    borderColor={currentScheme.colors.border}
                  />
                  <Button size="sm" onClick={addTag}>Add</Button>
                </HStack>
                <Flex wrap="wrap" gap={2} mt={2}>
                  {saveFormData.tags.map((tag) => (
                    <Tag key={tag} size="sm" colorScheme="blue" variant="solid">
                      <TagLabel>{tag}</TagLabel>
                      <TagCloseButton onClick={() => removeTag(tag)} />
                    </Tag>
                  ))}
                </Flex>
              </FormControl>

              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <AlertDescription fontSize="sm">
                  If an addon with the same name exists, it will be overwritten.
                </AlertDescription>
              </Alert>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onSaveClose}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleSaveFlow}>
              Save Flow
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Enhanced Load Modal */}
      <Modal isOpen={isLoadOpen} onClose={onLoadClose} size="4xl">
        <ModalOverlay />
        <ModalContent bg={currentScheme.colors.background}>
          <ModalHeader color={currentScheme.colors.text}>
            📂 Load Addon Flow
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <HStack justify="space-between" align="center">
                <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                  Choose a saved addon flow to load
                </Text>
                <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                  {savedFlows.length} saved flows
                </Text>
              </HStack>

              {savedFlows.length === 0 ? (
                <Alert status="info" borderRadius="md">
                  <AlertIcon />
                  <AlertDescription>
                    No saved flows found. Create and save your first addon flow to see it here!
                  </AlertDescription>
                </Alert>
              ) : (
                <Box maxH="500px" overflowY="auto" pr={2}>
                  <SimpleGrid columns={[1, 2]} spacing={4}>
                    {savedFlows.map((flow) => (
                      <Card key={flow.id} bg={currentScheme.colors.background} borderColor={currentScheme.colors.border}>
                        <CardHeader pb={2}>
                          <HStack justify="space-between" align="start">
                            <VStack align="start" spacing={1}>
                              <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text}>
                                {flow.name}
                              </Text>
                                                             <HStack spacing={2}>
                                 <Badge size="sm" colorScheme="blue">
                                   v{flow.version}
                                 </Badge>
                                 {flow.isBuilt ? (
                                   flow.hasOriginalFlow ? (
                                     <Badge size="sm" colorScheme="green">
                                       ✓ Recoverable
                                     </Badge>
                                   ) : (
                                     <Badge size="sm" colorScheme="orange">
                                       Template Only
                                     </Badge>
                                   )
                                 ) : (
                                   <Badge size="sm" colorScheme="blue">
                                     {flow.nodeCount} blocks
                                   </Badge>
                                 )}
                               </HStack>
                            </VStack>
                                                         <Menu>
                               <MenuButton
                                 as={IconButton}
                                 icon={<FiMoreVertical />}
                                 variant="ghost"
                                 size="sm"
                               />
                               <MenuList bg={currentScheme.colors.surface}>
                                 <MenuItem
                                   icon={<FiTrash2 />}
                                   onClick={() => confirmDelete(flow)}
                                   color="red.400"
                                 >
                                   {flow.isBuilt ? 'Delete Addon' : 'Delete Flow'}
                                 </MenuItem>
                               </MenuList>
                             </Menu>
                          </HStack>
                        </CardHeader>
                        <CardBody pt={0}>
                                                     <VStack align="start" spacing={2}>
                             <Text fontSize="sm" color={currentScheme.colors.textSecondary} noOfLines={2}>
                               {flow.isBuilt 
                                 ? flow.hasOriginalFlow
                                   ? `${flow.description} • Original flow data available! You can recover and edit the exact flow that was used to build this addon.`
                                   : `${flow.description} • This addon was built before flow recovery was implemented. Only a basic template can be provided.`
                                 : (flow.description || 'No description available')
                               }
                             </Text>
                            
                            <HStack justify="space-between" align="center" width="full">
                              <HStack spacing={2}>
                                <HStack spacing={1}>
                                  <FiUser size={12} />
                                  <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                                    {flow.author}
                                  </Text>
                                </HStack>
                                <HStack spacing={1}>
                                  <FiClock size={12} />
                                  <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                                    {new Date(flow.updatedAt).toLocaleDateString()}
                                  </Text>
                                </HStack>
                              </HStack>
                            </HStack>

                            {flow.tags && flow.tags.length > 0 && (
                              <Flex wrap="wrap" gap={1}>
                                {flow.tags.slice(0, 3).map((tag: string) => (
                                  <Tag key={tag} size="sm" colorScheme="gray" variant="outline">
                                    {tag}
                                  </Tag>
                                ))}
                                {flow.tags.length > 3 && (
                                  <Tag size="sm" colorScheme="gray" variant="outline">
                                    +{flow.tags.length - 3}
                                  </Tag>
                                )}
                              </Flex>
                            )}
                          </VStack>
                        </CardBody>
                                                 <CardFooter pt={0}>
                           <Tooltip 
                             label={
                               flow.isBuilt 
                                 ? (flow.hasOriginalFlow 
                                   ? "Load the original flow - fully recoverable!" 
                                   : "Load a basic template - original flow not saved")
                                 : "Load the exact saved flow"
                             }
                             placement="top"
                           >
                             <Button
                               size="sm"
                               colorScheme={
                                 flow.isBuilt 
                                   ? (flow.hasOriginalFlow ? "green" : "orange")
                                   : "blue"
                               }
                               width="full"
                               onClick={() => handleLoadFlow(flow)}
                             >
                               {flow.isBuilt 
                                 ? (flow.hasOriginalFlow ? "Recover Flow" : "Load Template")
                                 : "Load Flow"
                               }
                             </Button>
                           </Tooltip>
                         </CardFooter>
                      </Card>
                    ))}
                  </SimpleGrid>
                </Box>
              )}
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Export Flow Modal */}
      <Modal isOpen={isExportOpen} onClose={onExportClose} size="3xl">
        <ModalOverlay />
        <ModalContent bg={currentScheme.colors.background}>
          <ModalHeader color={currentScheme.colors.text}>
            🚀 Export Addon Flow
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Share your addon flow with others! Copy the flow code below and share it anywhere.
                  Others can import this code to reproduce your exact flow.
                </AlertDescription>
              </Alert>

              <VStack align="stretch" spacing={3}>
                <HStack justify="space-between" align="center">
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    Flow Code
                  </Text>
                  <HStack spacing={2}>
                    <Button
                      size="sm"
                      leftIcon={<FiCopy />}
                      colorScheme="blue"
                      onClick={() => copyToClipboard(exportData)}
                    >
                      Copy Code
                    </Button>
                  </HStack>
                </HStack>
                
                <Box
                  p={4}
                  bg={currentScheme.colors.surface}
                  borderColor={currentScheme.colors.border}
                  borderWidth="2px"
                  borderRadius="md"
                  borderStyle="dashed"
                >
                  <Text
                    fontFamily="monospace"
                    fontSize="lg"
                    fontWeight="bold"
                    color={currentScheme.colors.primary}
                    textAlign="center"
                    wordBreak="break-all"
                    userSelect="all"
                  >
                    {exportData}
                  </Text>
                </Box>
              </VStack>

              <Box p={3} bg={currentScheme.colors.surface} borderRadius="md" borderColor={currentScheme.colors.border} borderWidth="1px">
                <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} mb={2}>
                  📋 How to Share:
                </Text>
                <VStack align="start" spacing={1} fontSize="sm" color={currentScheme.colors.textSecondary}>
                  <Text>• Copy the flow code and paste it in Discord, email, or any text platform</Text>
                  <Text>• Much easier to share than long JSON data!</Text>
                  <Text>• The recipient can use the "Import Code" button to load your flow</Text>
                  <Text>• All your block configurations and connections will be preserved</Text>
                </VStack>
              </Box>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>

      {/* Import Flow Modal */}
      <Modal isOpen={isImportOpen} onClose={onImportClose} size="2xl">
        <ModalOverlay />
        <ModalContent bg={currentScheme.colors.background}>
          <ModalHeader color={currentScheme.colors.text}>
            📥 Import Addon Flow
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={4} align="stretch">
              <Alert status="info" borderRadius="md">
                <AlertIcon />
                <AlertDescription>
                  Import an addon flow from someone else! Paste the flow code below to load their flow configuration.
                  This will replace your current flow.
                </AlertDescription>
              </Alert>

              <VStack align="stretch" spacing={3}>
                <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                  Paste Flow Code
                </Text>
                
                <Input
                  value={importData}
                  onChange={(e) => setImportData(e.target.value)}
                  fontFamily="monospace"
                  fontSize="sm"
                  bg={currentScheme.colors.surface}
                  borderColor={currentScheme.colors.border}
                  placeholder="addon_xxxxxx_xxxxxx_xxxxxx..."
                  size="lg"
                />
              </VStack>

              <Box p={3} bg={currentScheme.colors.surface} borderRadius="md" borderColor={currentScheme.colors.border} borderWidth="1px">
                <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} mb={2}>
                  🔧 Import Instructions:
                </Text>
                <VStack align="start" spacing={1} fontSize="sm" color={currentScheme.colors.textSecondary}>
                  <Text>• Paste the flow code (looks like: addon_xxxxxx_xxxxxx_xxxxxx)</Text>
                  <Text>• Much simpler than JSON - just a single line of code!</Text>
                  <Text>• Click "Import Flow" to load the configuration</Text>
                  <Text>• Your current flow will be replaced with the imported one</Text>
                  <Text>• Legacy JSON format is still supported for backward compatibility</Text>
                </VStack>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onImportClose}>
              Cancel
            </Button>
            <Button
              colorScheme="green"
              onClick={handleImportFlow}
              isDisabled={!importData.trim()}
            >
              Import Flow
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Dialog */}
      <AlertDialog
        isOpen={!!deleteConfirmFlow}
        onClose={() => setDeleteConfirmFlow(null)}
        leastDestructiveRef={cancelRef}
      >
        <AlertDialogOverlay>
          <AlertDialogContent bg={currentScheme.colors.background}>
            <AlertDialogHeader fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
              {deleteConfirmFlow?.isBuilt ? 'Delete Addon' : 'Delete Flow'}
            </AlertDialogHeader>

            <AlertDialogBody color={currentScheme.colors.textSecondary}>
              {deleteConfirmFlow?.isBuilt ? (
                <>
                  Are you sure you want to delete the <strong>"{deleteConfirmFlow?.name}"</strong> addon? 
                  This will permanently remove it from both the source code and the bot. This action cannot be undone.
                </>
              ) : (
                <>
                  Are you sure you want to delete the <strong>"{deleteConfirmFlow?.name}"</strong> flow? 
                  This will permanently remove it from your saved flows. This action cannot be undone.
                </>
              )}
            </AlertDialogBody>

            <AlertDialogFooter>
              <Button ref={cancelRef} onClick={() => setDeleteConfirmFlow(null)}>
                Cancel
              </Button>
              <Button colorScheme="red" onClick={handleDeleteFlow} ml={3}>
                {deleteConfirmFlow?.isBuilt ? 'Delete Addon' : 'Delete Flow'}
              </Button>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialogOverlay>
      </AlertDialog>
    </Box>
  );
};

export default AddonBuilderPage; 