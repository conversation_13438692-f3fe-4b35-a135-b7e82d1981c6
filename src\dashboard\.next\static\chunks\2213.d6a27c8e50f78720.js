"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2213],{2213:(e,s,t)=>{t.r(s),t.d(s,{default:()=>b});var a=t(94513),n=t(22907),i=t(9557),r=t(7680),l=t(52922),c=t(47847),o=t(59365),d=t(85104),h=t(79156),x=t(40443),p=t(63730),m=t(64057),u=t(61481),j=t(55631),_=t(25964),f=t(28245),C=t(62690),g=t(94285);let y={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function b(e){let{isOpen:s,onClose:t,onSuccess:b,channel:w,categories:k}=e,v=(0,n.d)(),[L,I]=(0,g.useState)(!1),[E,N]=(0,g.useState)({name:"",type:0,topic:"",nsfw:!1,bitrate:64e3,userLimit:0,parent:"",rateLimitPerUser:0});(0,g.useEffect)(()=>{w&&N({name:w.name||"",type:w.raw_type||0,topic:w.topic||"",nsfw:w.nsfw||!1,bitrate:w.bitrate||64e3,userLimit:w.user_limit||0,parent:w.parent_id||"",rateLimitPerUser:w.rate_limit_per_user||0})},[w]);let S=async()=>{I(!0);try{let e=await fetch("/api/discord/channels/".concat(w.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:E.name,topic:E.topic,nsfw:E.nsfw,bitrate:E.type===y.GUILD_VOICE?E.bitrate:void 0,user_limit:E.type===y.GUILD_VOICE?E.userLimit:void 0,parent_id:E.parent||null,rate_limit_per_user:E.type===y.GUILD_TEXT?E.rateLimitPerUser:void 0})});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update channel")}v({title:"Success",description:"Channel updated successfully",status:"success",duration:3e3}),b(),t()}catch(e){v({title:"Error",description:e.message||"Failed to update channel",status:"error",duration:5e3})}finally{I(!1)}},U=(e,s)=>{N(t=>({...t,[e]:s}))};return(0,a.jsxs)(i.aF,{isOpen:s,onClose:t,size:"xl",children:[(0,a.jsx)(r.m,{backdropFilter:"blur(10px)"}),(0,a.jsxs)(l.$,{bg:"gray.800",children:[(0,a.jsx)(c.r,{children:"Edit Channel"}),(0,a.jsx)(o.s,{}),(0,a.jsx)(d.c,{children:(0,a.jsxs)(h.T,{spacing:4,children:[(0,a.jsxs)(x.MJ,{children:[(0,a.jsx)(p.l,{children:"Channel Name"}),(0,a.jsx)(m.p,{placeholder:"Enter channel name",value:E.name,onChange:e=>U("name",e.target.value)})]}),E.type===y.GUILD_TEXT&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.MJ,{children:[(0,a.jsx)(p.l,{children:"Channel Topic"}),(0,a.jsx)(m.p,{placeholder:"Enter channel topic",value:E.topic,onChange:e=>U("topic",e.target.value)})]}),(0,a.jsxs)(x.MJ,{children:[(0,a.jsx)(p.l,{children:"Slowmode (seconds)"}),(0,a.jsxs)(u.Q7,{min:0,max:21600,value:E.rateLimitPerUser,onChange:e=>U("rateLimitPerUser",parseInt(e)),children:[(0,a.jsx)(u.OO,{}),(0,a.jsxs)(u.lw,{children:[(0,a.jsx)(u.Q0,{}),(0,a.jsx)(u.Sh,{})]})]})]}),(0,a.jsxs)(x.MJ,{display:"flex",alignItems:"center",children:[(0,a.jsx)(p.l,{htmlFor:"nsfw",mb:"0",children:"Age-Restricted (NSFW)"}),(0,a.jsx)(j.d,{id:"nsfw",isChecked:E.nsfw,onChange:e=>U("nsfw",e.target.checked)})]})]}),E.type===y.GUILD_VOICE&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(x.MJ,{children:[(0,a.jsx)(p.l,{children:"Bitrate (kbps)"}),(0,a.jsxs)(u.Q7,{min:8,max:96,value:E.bitrate/1e3,onChange:e=>U("bitrate",1e3*parseInt(e)),children:[(0,a.jsx)(u.OO,{}),(0,a.jsxs)(u.lw,{children:[(0,a.jsx)(u.Q0,{}),(0,a.jsx)(u.Sh,{})]})]})]}),(0,a.jsxs)(x.MJ,{children:[(0,a.jsx)(p.l,{children:"User Limit"}),(0,a.jsxs)(u.Q7,{min:0,max:99,value:E.userLimit,onChange:e=>U("userLimit",parseInt(e)),children:[(0,a.jsx)(u.OO,{}),(0,a.jsxs)(u.lw,{children:[(0,a.jsx)(u.Q0,{}),(0,a.jsx)(u.Sh,{})]})]})]})]}),E.type!==y.GUILD_CATEGORY&&(0,a.jsxs)(x.MJ,{children:[(0,a.jsx)(p.l,{children:"Parent Category"}),(0,a.jsxs)(_.l,{value:E.parent,onChange:e=>U("parent",e.target.value),children:[(0,a.jsx)("option",{value:"",children:"None"}),(k||[]).map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]})]})]})}),(0,a.jsxs)(f.j,{children:[(0,a.jsx)(C.$,{variant:"ghost",mr:3,onClick:t,children:"Cancel"}),(0,a.jsx)(C.$,{colorScheme:"blue",onClick:S,isLoading:L,children:"Save Changes"})]})]})]})}},28245:(e,s,t)=>{t.d(s,{j:()=>o});var a=t(94513),n=t(55100),i=t(22697),r=t(9557),l=t(2923),c=t(33225);let o=(0,l.R)((e,s)=>{let{className:t,...l}=e,o=(0,i.cx)("chakra-modal__footer",t),d=(0,r.x5)(),h=(0,n.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...d.footer});return(0,a.jsx)(c.B.footer,{ref:s,...l,__css:h,className:o})});o.displayName="ModalFooter"},55631:(e,s,t)=>{t.d(s,{d:()=>h});var a=t(94513),n=t(75387),i=t(22697),r=t(94285),l=t(96027),c=t(2923),o=t(56915),d=t(33225);let h=(0,c.R)(function(e,s){let t=(0,o.o)("Switch",e),{spacing:c="0.5rem",children:h,...x}=(0,n.M)(e),{getIndicatorProps:p,getInputProps:m,getCheckboxProps:u,getRootProps:j,getLabelProps:_}=(0,l.v)(x),f=(0,r.useMemo)(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...t.container}),[t.container]),C=(0,r.useMemo)(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...t.track}),[t.track]),g=(0,r.useMemo)(()=>({userSelect:"none",marginStart:c,...t.label}),[c,t.label]);return(0,a.jsxs)(d.B.label,{...j(),className:(0,i.cx)("chakra-switch",e.className),__css:f,children:[(0,a.jsx)("input",{className:"chakra-switch__input",...m({},s)}),(0,a.jsx)(d.B.span,{...u(),className:"chakra-switch__track",__css:C,children:(0,a.jsx)(d.B.span,{__css:t.thumb,className:"chakra-switch__thumb",...p()})}),h&&(0,a.jsx)(d.B.span,{className:"chakra-switch__label",..._(),__css:g,children:h})]})});h.displayName="Switch"},59365:(e,s,t)=>{t.d(s,{s:()=>c});var a=t(94513),n=t(22697),i=t(50614),r=t(9557),l=t(33021);let c=(0,t(2923).R)((e,s)=>{let{onClick:t,className:c,...o}=e,{onClose:d}=(0,r.k3)(),h=(0,n.cx)("chakra-modal__close-btn",c),x=(0,r.x5)();return(0,a.jsx)(l.J,{ref:s,__css:x.closeButton,className:h,onClick:(0,i.H)(t,e=>{e.stopPropagation(),d()}),...o})});c.displayName="ModalCloseButton"}}]);