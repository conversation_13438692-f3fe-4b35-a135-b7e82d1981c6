"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[627],{7627:(e,s,a)=>{a.d(s,{T:()=>m});var l=a(94513),n=a(75387),t=a(25195),r=a(22697),i=a(44637),o=a(2923),c=a(56915),d=a(33225);let h=["h","minH","height","minHeight"],m=(0,o.R)((e,s)=>{let a=(0,c.V)("Textarea",e),{className:o,rows:m,...u}=(0,n.M)(e),x=(0,i.t)(u),g=m?(0,t.c)(a,h):a;return(0,l.jsx)(d.B.textarea,{ref:s,rows:m,...x,className:(0,r.cx)("chakra-textarea",o),__css:g})});m.displayName="Textarea"},28245:(e,s,a)=>{a.d(s,{j:()=>c});var l=a(94513),n=a(55100),t=a(22697),r=a(9557),i=a(2923),o=a(33225);let c=(0,i.R)((e,s)=>{let{className:a,...i}=e,c=(0,t.cx)("chakra-modal__footer",a),d=(0,r.x5)(),h=(0,n.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...d.footer});return(0,l.jsx)(o.B.footer,{ref:s,...i,__css:h,className:c})});c.displayName="ModalFooter"},55631:(e,s,a)=>{a.d(s,{d:()=>h});var l=a(94513),n=a(75387),t=a(22697),r=a(94285),i=a(96027),o=a(2923),c=a(56915),d=a(33225);let h=(0,o.R)(function(e,s){let a=(0,c.o)("Switch",e),{spacing:o="0.5rem",children:h,...m}=(0,n.M)(e),{getIndicatorProps:u,getInputProps:x,getCheckboxProps:g,getRootProps:b,getLabelProps:j}=(0,i.v)(m),y=(0,r.useMemo)(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...a.container}),[a.container]),p=(0,r.useMemo)(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...a.track}),[a.track]),f=(0,r.useMemo)(()=>({userSelect:"none",marginStart:o,...a.label}),[o,a.label]);return(0,l.jsxs)(d.B.label,{...b(),className:(0,t.cx)("chakra-switch",e.className),__css:y,children:[(0,l.jsx)("input",{className:"chakra-switch__input",...x({},s)}),(0,l.jsx)(d.B.span,{...g(),className:"chakra-switch__track",__css:p,children:(0,l.jsx)(d.B.span,{__css:a.thumb,className:"chakra-switch__thumb",...u()})}),h&&(0,l.jsx)(d.B.span,{className:"chakra-switch__label",...j(),__css:f,children:h})]})});h.displayName="Switch"},58382:(e,s,a)=>{a.d(s,{$:()=>d});var l=a(94513),n=a(94285),t=a(70423),r=a(65507),i=a(18303),o=a(43256);function c(e){return e&&(0,o.Gv)(e)&&(0,o.Gv)(e.target)}function d(e){let{colorScheme:s,size:a,variant:o,children:d,isDisabled:h}=e,{value:m,onChange:u}=function(e={}){let{defaultValue:s,value:a,onChange:l,isDisabled:t,isNative:o}=e,d=(0,r.c)(l),[h,m]=(0,i.i)({value:a,defaultValue:s||[],onChange:d}),u=(0,n.useCallback)(e=>{if(!h)return;let s=c(e)?e.target.checked:!h.includes(e),a=c(e)?e.target.value:e;m(s?[...h,a]:h.filter(e=>String(e)!==String(a)))},[m,h]),x=(0,n.useCallback)((e={})=>{let s=o?"checked":"isChecked";return{...e,[s]:h.some(s=>String(e.value)===String(s)),onChange:u}},[u,o,h]);return{value:h,isDisabled:t,onChange:u,setValue:m,getCheckboxProps:x}}(e),x=(0,n.useMemo)(()=>({size:a,onChange:u,colorScheme:s,value:m,variant:o,isDisabled:h}),[a,u,s,m,o,h]);return(0,l.jsx)(t.a,{value:x,children:d})}d.displayName="CheckboxGroup"},59365:(e,s,a)=>{a.d(s,{s:()=>o});var l=a(94513),n=a(22697),t=a(50614),r=a(9557),i=a(33021);let o=(0,a(2923).R)((e,s)=>{let{onClick:a,className:o,...c}=e,{onClose:d}=(0,r.k3)(),h=(0,n.cx)("chakra-modal__close-btn",o),m=(0,r.x5)();return(0,l.jsx)(i.J,{ref:s,__css:m.closeButton,className:h,onClick:(0,t.H)(a,e=>{e.stopPropagation(),d()}),...c})});o.displayName="ModalCloseButton"},60627:(e,s,a)=>{a.r(s),a.d(s,{default:()=>G});var l=a(94513),n=a(22907),t=a(9557),r=a(7680),i=a(52922),o=a(47847),c=a(59365),d=a(85104),h=a(79156),m=a(57561),u=a(41611),x=a(91047),g=a(83881),b=a(47402),j=a(49217),y=a(99820),p=a(72671),f=a(40443),v=a(63730),w=a(55631),C=a(25964),k=a(7627),_=a(51961),S=a(58382),M=a(25680),N=a(22237),E=a(28245),T=a(62690),F=a(94285),I=a(97146);let B={welcome:{enabled:!1,channelId:"",message:"Welcome {user} to {guild}! You are the {memberCount}th member.",autoRoles:[],embedColor:"#00FF00"},goodbye:{enabled:!1,channelId:"",message:"Goodbye {user}! We will miss you.",embedColor:"#FF0000"}};function G(e){let{isOpen:s,onClose:a,channels:G=[],roles:J=[]}=e,R=(0,n.d)(),[W,D]=(0,F.useState)(B),[H,z]=(0,F.useState)(!0),[P,$]=(0,F.useState)(!1),A=G.filter(e=>0===e.type),K=J.filter(e=>"@everyone"!==e.name),O=async()=>{z(!0);try{let e=await fetch("/api/automation/welcome");if(!e.ok)throw Error("Failed to fetch settings");let s=await e.json();D(s)}catch(e){R({title:"Error loading settings",description:e.message,status:"error",duration:5e3})}finally{z(!1)}},L=async()=>{$(!0);try{if(!(await fetch("/api/automation/welcome",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(W)})).ok)throw Error("Failed to save settings");R({title:"Settings Saved",status:"success",duration:3e3}),a()}catch(e){R({title:"Error saving settings",description:e.message,status:"error",duration:5e3})}finally{$(!1)}};(0,F.useEffect)(()=>{s&&O()},[s]);let V=(e,s)=>{D(a=>({...a,welcome:{...a.welcome,[e]:s}}))},Y=(e,s)=>{D(a=>({...a,goodbye:{...a.goodbye,[e]:s}}))};return(0,l.jsxs)(t.aF,{isOpen:s,onClose:a,size:"3xl",scrollBehavior:"inside",children:[(0,l.jsx)(r.m,{}),(0,l.jsxs)(i.$,{children:[(0,l.jsx)(o.r,{children:"Welcome & Goodbye System"}),(0,l.jsx)(c.s,{}),(0,l.jsx)(d.c,{children:H?(0,l.jsxs)(h.T,{justify:"center",h:"400px",children:[(0,l.jsx)(m.y,{size:"xl"}),(0,l.jsx)(u.E,{children:"Loading Settings..."})]}):(0,l.jsxs)(x.t,{isFitted:!0,variant:"enclosed",children:[(0,l.jsxs)(g.w,{children:[(0,l.jsxs)(b.o,{children:[(0,l.jsx)(j.I,{as:I.mEP,mr:2})," Welcome Messages"]}),(0,l.jsxs)(b.o,{children:[(0,l.jsx)(j.I,{as:I.QeK,mr:2})," Goodbye Messages"]})]}),(0,l.jsxs)(y.T,{children:[(0,l.jsx)(p.K,{children:(0,l.jsxs)(h.T,{spacing:6,align:"stretch",children:[(0,l.jsxs)(f.MJ,{display:"flex",alignItems:"center",children:[(0,l.jsx)(v.l,{htmlFor:"welcome-enabled",mb:"0",children:"Enable Welcome Messages"}),(0,l.jsx)(w.d,{id:"welcome-enabled",isChecked:W.welcome.enabled,onChange:e=>V("enabled",e.target.checked)})]}),(0,l.jsxs)(f.MJ,{isDisabled:!W.welcome.enabled,children:[(0,l.jsx)(v.l,{children:"Welcome Channel"}),(0,l.jsx)(C.l,{placeholder:"Select a channel",value:W.welcome.channelId||"",onChange:e=>V("channelId",e.target.value),children:A.map(e=>(0,l.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))})]}),(0,l.jsxs)(f.MJ,{isDisabled:!W.welcome.enabled,children:[(0,l.jsx)(v.l,{children:"Welcome Message"}),(0,l.jsx)(k.T,{value:W.welcome.message,onChange:e=>V("message",e.target.value),placeholder:"Enter your welcome message...",rows:5}),(0,l.jsxs)(u.E,{fontSize:"xs",color:"gray.500",mt:1,children:["Placeholders: ","{user}",", ","{username}",", ","{userTag}",", ","{guild}",", ","{memberCount}"]})]}),(0,l.jsxs)(f.MJ,{isDisabled:!W.welcome.enabled,children:[(0,l.jsx)(v.l,{children:"Auto-Assign Roles"}),(0,l.jsx)(_.a,{p:3,borderWidth:1,borderRadius:"md",maxH:"200px",overflowY:"auto",children:(0,l.jsx)(S.$,{value:W.welcome.autoRoles,onChange:e=>V("autoRoles",e),children:(0,l.jsx)(M.r,{columns:{base:1,md:2},spacing:2,children:K.map(e=>(0,l.jsx)(N.S,{value:e.id,children:e.name},e.id))})})})]})]})}),(0,l.jsx)(p.K,{children:(0,l.jsxs)(h.T,{spacing:6,align:"stretch",children:[(0,l.jsxs)(f.MJ,{display:"flex",alignItems:"center",children:[(0,l.jsx)(v.l,{htmlFor:"goodbye-enabled",mb:"0",children:"Enable Goodbye Messages"}),(0,l.jsx)(w.d,{id:"goodbye-enabled",isChecked:W.goodbye.enabled,onChange:e=>Y("enabled",e.target.checked)})]}),(0,l.jsxs)(f.MJ,{isDisabled:!W.goodbye.enabled,children:[(0,l.jsx)(v.l,{children:"Goodbye Channel"}),(0,l.jsx)(C.l,{placeholder:"Select a channel",value:W.goodbye.channelId||"",onChange:e=>Y("channelId",e.target.value),children:A.map(e=>(0,l.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))})]}),(0,l.jsxs)(f.MJ,{isDisabled:!W.goodbye.enabled,children:[(0,l.jsx)(v.l,{children:"Goodbye Message"}),(0,l.jsx)(k.T,{value:W.goodbye.message,onChange:e=>Y("message",e.target.value),placeholder:"Enter your goodbye message...",rows:5}),(0,l.jsxs)(u.E,{fontSize:"xs",color:"gray.500",mt:1,children:["Placeholders: ","{user}",", ","{username}",", ","{userTag}",", ","{guild}",", ","{memberCount}"]})]})]})})]})]})}),(0,l.jsxs)(E.j,{children:[(0,l.jsx)(T.$,{variant:"ghost",mr:3,onClick:a,children:"Cancel"}),(0,l.jsx)(T.$,{colorScheme:"blue",onClick:L,isLoading:P,isDisabled:H,children:"Save Settings"})]})]})]})}}}]);