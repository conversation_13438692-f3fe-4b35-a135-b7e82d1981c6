"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1677],{11677:(e,s,t)=>{t.r(s),t.d(s,{default:()=>w});var n=t(94513),a=t(22907),r=t(9557),i=t(7680),l=t(52922),c=t(47847),o=t(59365),h=t(85104),d=t(70690),x=t(40443),p=t(63730),j=t(64057),u=t(25964),m=t(61481),C=t(55631),g=t(28245),_=t(62690),y=t(94285);let f={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function w(e){let{isOpen:s,onClose:t,onSuccess:w}=e,b=(0,a.d)(),[k,v]=(0,y.useState)(!1),[E,L]=(0,y.useState)([]),[S,I]=(0,y.useState)({name:"",type:"GUILD_TEXT",parent:"",topic:"",nsfw:!1,rateLimitPerUser:0,position:0,bitrate:64e3,userLimit:0});(0,y.useEffect)(()=>{s&&T()},[s]);let T=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let s=(await e.json()).filter(e=>"number"==typeof e.raw_type?e.raw_type===f.GUILD_CATEGORY:"GUILD_CATEGORY"===e.type||"category"===e.type);L(s)}catch(e){b({title:"Error",description:"Failed to fetch categories",status:"error",duration:3e3})}},O=(e,s)=>{I(t=>({...t,[e]:s}))},G=async()=>{try{if(v(!0),!S.name.trim())return void b({title:"Error",description:"Channel name is required",status:"error",duration:3e3});let e=S.name.toLowerCase().replace(/\s+/g,"-"),s=f[S.type],n=await fetch("/api/discord/channels",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...S,name:e,type:s})});if(!n.ok){let e=await n.text(),s="Failed to create channel";try{let t=JSON.parse(e);s=t.message||t.error||s}catch(t){s=e}throw Error(s)}b({title:"Success",description:"Channel created successfully",status:"success",duration:3e3}),null==w||w(),t()}catch(e){b({title:"Error",description:e.message||"Failed to create channel",status:"error",duration:5e3})}finally{v(!1)}};return(0,y.useEffect)(()=>{s||I({name:"",type:"GUILD_TEXT",parent:"",topic:"",nsfw:!1,rateLimitPerUser:0,position:0,bitrate:64e3,userLimit:0})},[s]),(0,n.jsxs)(r.aF,{isOpen:s,onClose:t,size:"xl",children:[(0,n.jsx)(i.m,{backdropFilter:"blur(10px)"}),(0,n.jsxs)(l.$,{bg:"gray.800",border:"1px",borderColor:"blue.500",children:[(0,n.jsx)(c.r,{children:"Create Channel"}),(0,n.jsx)(o.s,{}),(0,n.jsx)(h.c,{children:(0,n.jsxs)(d.B,{spacing:4,children:[(0,n.jsxs)(x.MJ,{isRequired:!0,children:[(0,n.jsx)(p.l,{children:"Channel Name"}),(0,n.jsx)(j.p,{placeholder:"Enter channel name",value:S.name,onChange:e=>O("name",e.target.value)}),(0,n.jsx)(x.eK,{children:"Channel name will be automatically formatted (lowercase, hyphens instead of spaces)"})]}),(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"Channel Type"}),(0,n.jsxs)(u.l,{value:S.type,onChange:e=>O("type",e.target.value),children:[(0,n.jsx)("option",{value:"GUILD_TEXT",children:"Text Channel"}),(0,n.jsx)("option",{value:"GUILD_VOICE",children:"Voice Channel"}),(0,n.jsx)("option",{value:"GUILD_CATEGORY",children:"Category"})]})]}),"GUILD_CATEGORY"!==S.type&&(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"Parent Category"}),(0,n.jsxs)(u.l,{placeholder:"Select category",value:S.parent,onChange:e=>O("parent",e.target.value),children:[(0,n.jsx)("option",{value:"",children:"None"}),(E||[]).map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),"GUILD_TEXT"===S.type&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"Channel Topic"}),(0,n.jsx)(j.p,{placeholder:"Enter channel topic",value:S.topic,onChange:e=>O("topic",e.target.value)})]}),(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"Slowmode (seconds)"}),(0,n.jsxs)(m.Q7,{min:0,max:21600,value:S.rateLimitPerUser,onChange:e=>O("rateLimitPerUser",parseInt(e)),children:[(0,n.jsx)(m.OO,{}),(0,n.jsxs)(m.lw,{children:[(0,n.jsx)(m.Q0,{}),(0,n.jsx)(m.Sh,{})]})]}),(0,n.jsx)(x.eK,{children:"Set how long users must wait between sending messages (0 to disable)"})]}),(0,n.jsxs)(x.MJ,{display:"flex",alignItems:"center",children:[(0,n.jsx)(p.l,{mb:"0",children:"Age-Restricted (NSFW)"}),(0,n.jsx)(C.d,{isChecked:S.nsfw,onChange:e=>O("nsfw",e.target.checked)})]})]}),"GUILD_VOICE"===S.type&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"Bitrate (kbps)"}),(0,n.jsxs)(m.Q7,{min:8,max:96,value:S.bitrate/1e3,onChange:e=>O("bitrate",1e3*parseInt(e)),children:[(0,n.jsx)(m.OO,{}),(0,n.jsxs)(m.lw,{children:[(0,n.jsx)(m.Q0,{}),(0,n.jsx)(m.Sh,{})]})]})]}),(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"User Limit"}),(0,n.jsxs)(m.Q7,{min:0,max:99,value:S.userLimit,onChange:e=>O("userLimit",parseInt(e)),children:[(0,n.jsx)(m.OO,{}),(0,n.jsxs)(m.lw,{children:[(0,n.jsx)(m.Q0,{}),(0,n.jsx)(m.Sh,{})]})]}),(0,n.jsx)(x.eK,{children:"Set to 0 for unlimited users"})]})]}),(0,n.jsxs)(x.MJ,{children:[(0,n.jsx)(p.l,{children:"Position"}),(0,n.jsxs)(m.Q7,{min:0,value:S.position,onChange:e=>O("position",parseInt(e)),children:[(0,n.jsx)(m.OO,{}),(0,n.jsxs)(m.lw,{children:[(0,n.jsx)(m.Q0,{}),(0,n.jsx)(m.Sh,{})]})]}),(0,n.jsx)(x.eK,{children:"Channel position in the list (0 = top)"})]})]})}),(0,n.jsxs)(g.j,{children:[(0,n.jsx)(_.$,{variant:"ghost",mr:3,onClick:t,children:"Cancel"}),(0,n.jsx)(_.$,{colorScheme:"blue",onClick:G,isLoading:k,loadingText:"Creating...",children:"Create Channel"})]})]})]})}},28245:(e,s,t)=>{t.d(s,{j:()=>o});var n=t(94513),a=t(55100),r=t(22697),i=t(9557),l=t(2923),c=t(33225);let o=(0,l.R)((e,s)=>{let{className:t,...l}=e,o=(0,r.cx)("chakra-modal__footer",t),h=(0,i.x5)(),d=(0,a.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...h.footer});return(0,n.jsx)(c.B.footer,{ref:s,...l,__css:d,className:o})});o.displayName="ModalFooter"},55631:(e,s,t)=>{t.d(s,{d:()=>d});var n=t(94513),a=t(75387),r=t(22697),i=t(94285),l=t(96027),c=t(2923),o=t(56915),h=t(33225);let d=(0,c.R)(function(e,s){let t=(0,o.o)("Switch",e),{spacing:c="0.5rem",children:d,...x}=(0,a.M)(e),{getIndicatorProps:p,getInputProps:j,getCheckboxProps:u,getRootProps:m,getLabelProps:C}=(0,l.v)(x),g=(0,i.useMemo)(()=>({display:"inline-block",position:"relative",verticalAlign:"middle",lineHeight:0,...t.container}),[t.container]),_=(0,i.useMemo)(()=>({display:"inline-flex",flexShrink:0,justifyContent:"flex-start",boxSizing:"content-box",cursor:"pointer",...t.track}),[t.track]),y=(0,i.useMemo)(()=>({userSelect:"none",marginStart:c,...t.label}),[c,t.label]);return(0,n.jsxs)(h.B.label,{...m(),className:(0,r.cx)("chakra-switch",e.className),__css:g,children:[(0,n.jsx)("input",{className:"chakra-switch__input",...j({},s)}),(0,n.jsx)(h.B.span,{...u(),className:"chakra-switch__track",__css:_,children:(0,n.jsx)(h.B.span,{__css:t.thumb,className:"chakra-switch__thumb",...p()})}),d&&(0,n.jsx)(h.B.span,{className:"chakra-switch__label",...C(),__css:y,children:d})]})});d.displayName="Switch"},59365:(e,s,t)=>{t.d(s,{s:()=>c});var n=t(94513),a=t(22697),r=t(50614),i=t(9557),l=t(33021);let c=(0,t(2923).R)((e,s)=>{let{onClick:t,className:c,...o}=e,{onClose:h}=(0,i.k3)(),d=(0,a.cx)("chakra-modal__close-btn",c),x=(0,i.x5)();return(0,n.jsx)(l.J,{ref:s,__css:x.closeButton,className:d,onClick:(0,r.H)(t,e=>{e.stopPropagation(),h()}),...o})});c.displayName="ModalCloseButton"}}]);