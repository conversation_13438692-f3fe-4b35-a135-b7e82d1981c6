# Bot & Authentication Configuration
bot:
  # Your Discord bot token from https://discord.com/developers/applications
  token: "YOUR_BOT_TOKEN_HERE"
  
  # Your application ID from https://discord.com/developers/applications
  clientId: "YOUR_CLIENT_ID_HERE"
  
  # Your application's client secret from https://discord.com/developers/applications
  # This is used for OAuth2 login on the dashboard
  clientSecret: "YOUR_CLIENT_SECRET_HERE"
  
  # The ID of the Discord server this bot is primarily used in
  guildId: "YOUR_GUILD_ID_HERE"
  
  # Category where ticket channels will be created
  ticketCategoryId: "YOUR_SUPPORT_CATEGORY_ID"
  
  # Channel where ticket logs (creation/closure) will be posted
  ticketLogChannelId: "YOUR_TICKET_LOG_CHANNEL_ID"
  
  # Command prefix for legacy text commands
  prefix: "!"

  # Presence / status shown in Discord
  presence:
    # One of: online, idle, dnd, invisible
    status: "online"
    activity:
      # One of: PLAYING, STREAMING, LISTENING, WATCHING, COMPETING
      type: "PLAYING"
      # The text that follows the activity, e.g. "Playing 404"
      name: "404"

# Dashboard Configuration  
dashboard:
  # URL where your dashboard is hosted
  # For local development, use: "http://localhost:3000"
  # For local network access, use: "http://*************:3000"
  # For hosting environments, SET THE NEXTAUTH_URL ENVIRONMENT VARIABLE instead
  url: "http://localhost:3000"
  
  # Discord role IDs that have access to the admin dashboard
  adminRoleIds:
    - "123456789012345678"
  
  # Session configuration
  session:
    # Secret used to encrypt session data - defaults to clientSecret if not set
    secret: "YOUR_SESSION_SECRET_HERE"

# HOSTING ENVIRONMENT CONFIGURATION:
# For Pterodactyl, Docker, or VPS hosting, set these environment variables:
# SERVER_PORT=2999                     # Port allocated by your hosting provider
# NEXTAUTH_URL=http://*************:2999  # Your public IP/domain and port
# DASHBOARD_URL=http://*************:2999 # Alternative name for NEXTAUTH_URL
# 
# The bot will automatically:
# - Use NEXTAUTH_URL/DASHBOARD_URL if set (highest priority)
# - Use dashboard.url from this config file if no env vars
# - Auto-detect local network IP for 192.168.x.x networks
# - Default to localhost:3000 as fallback

# Logging Configuration
logging:
  level: "info" # debug, info, warn, error
  console: true
  file: 
    enabled: true
    path: "logs"
    maxSize: "10m"
    maxFiles: 5
    datePattern: "YYYY-MM-DD"
  
# Addon System Configuration
addons:
  enabled: true
  directory: "src/addons"
  autoReload: true # Enable hot reloading of addons during development
  
# Database Configuration
database:
  type: "mongodb"
  url: "mongodb://localhost:27017"  # Local MongoDB
  # url: "*******************************************" # With authentication
  # url: "mongodb+srv://username:<EMAIL>" # MongoDB Atlas
  name: "discord_bot"
  options:
    maxPoolSize: 10               # Maximum connections in pool
    minPoolSize: 1                # Minimum connections in pool
    maxIdleTimeMS: 30000          # Close connections after 30s of inactivity
    serverSelectionTimeoutMS: 5000 # How long to try selecting a server
    socketTimeoutMS: 45000        # How long a send or receive on a socket can take
    connectTimeoutMS: 10000       # How long to attempt connection before timeout
    retryWrites: true             # Retry write operations once
    retryReads: true              # Retry read operations once
  
# Feature Flags
features:
  commandCooldown: 3000 # milliseconds
  errorReporting: true
  metrics: false # Future feature for performance monitoring

# Environment-specific configurations below
# Uncomment and modify as needed

# Development Environment (local MongoDB):
# database:
#   type: "mongodb"
#   url: "mongodb://localhost:27017"
#   name: "discord_bot_dev"

# Production Environment (MongoDB Atlas):
# database:
#   type: "mongodb" 
#   url: "mongodb+srv://username:<EMAIL>"
#   name: "discord_bot_prod"
#   options:
#     maxPoolSize: 20
#     serverSelectionTimeoutMS: 10000

# Production Environment (Self-hosted with replica set):
# database:
#   type: "mongodb"
#   url: "*************************************************************************************"
#   name: "discord_bot_prod"
#   options:
#     maxPoolSize: 15
#     readPreference: "secondaryPreferred"

# Example production configuration:
# bot:
#   token: "PRODUCTION_BOT_TOKEN"
#   clientId: "123456789012345678"
#   prefix: "!"
# 
# logging:
#   level: "warn"
#   console: false
#   file:
#     enabled: true
#     path: "/var/log/discord-bot"
#     maxSize: "50m"
#     maxFiles: 10
# 
# addons:
#   enabled: true
#   directory: "dist/addons"
#   autoReload: false 