"use strict";(()=>{var e={};e.id=775,e.ids=[775],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return a}});var a=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,a){return a in t?t[a]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,a)):"function"==typeof t&&"default"===a?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,a)=>{e.exports=a(5600)},3707:(e,t,a)=>{a.r(t),a.d(t,{config:()=>d,default:()=>s,routeModule:()=>l});var o=a(3433),i=a(264),n=a(584),r=a(6732);let s=(0,n.M)(r,"default"),d=(0,n.M)(r,"config"),l=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/database/middleware",pathname:"/api/database/middleware",bundlePath:"",filename:""},userland:r})},3873:e=>{e.exports=require("path")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6732:(e,t,a)=>{a.r(t),a.d(t,{logDatabaseOperation:()=>r});var o=a(2518);let{url:i,name:n}=a(8580).dashboardConfig.database;async function r(e,t,a){let r=null;try{let s=(r=await o.MongoClient.connect(i)).db(n).collection("database_logs");await s.insertOne({timestamp:new Date,operation:e,collection:t,details:a,id:new Date().getTime().toString()})}catch(e){}finally{r&&await r.close()}}},8580:(e,t,a)=>{a.r(t),a.d(t,{dashboardConfig:()=>d,default:()=>l});var o=a(9021),i=a(2115),n=a.n(i),r=a(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");o.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=o.readFileSync(e,"utf8");s=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var a=t(t.s=3707);module.exports=a})();