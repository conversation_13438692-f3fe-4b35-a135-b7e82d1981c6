(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2063],{39137:(e,r,o)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/admin/scheduler",function(){return o(76653)}])},76653:(e,r,o)=>{"use strict";o.r(r),o.d(r,{__N_SSP:()=>er,default:()=>eo});var l=o(94513),t=o(22907),s=o(95845),n=o(59001),i=o(79156),c=o(78902),d=o(31678),a=o(49217),h=o(41611),m=o(62690),x=o(68443),b=o(20429),u=o(59818),j=o(57561),p=o(51961),g=o(15373),E=o(51927),T=o(8595),v=o(1341),I=o(35981),S=o(95497),f=o(71601),y=o(24792),C=o(73011),U=o(9557),M=o(7680),w=o(52922),D=o(47847),A=o(59365),_=o(85104),k=o(40443),F=o(63730),N=o(73496),G=o(70690),J=o(3177),V=o(91047),L=o(83881),O=o(47402),z=o(99820),q=o(72671),R=o(64057),P=o(55631),B=o(25964),W=o(61481),X=o(7627),H=o(25680),K=o(28245),$=o(94285),Q=o(97146),Z=o(60341),Y=o(53703),ee=o(12772),er=!0;let eo=()=>{let e=(0,t.d)(),{isOpen:r,onOpen:o,onClose:er}=(0,s.j)(),[eo,el]=(0,$.useState)([]),[et,es]=(0,$.useState)([]),[en,ei]=(0,$.useState)(!0),[ec,ed]=(0,$.useState)(null),{currentScheme:ea}=(0,ee.DP)(),{handleSubmit:eh,control:em,register:ex,reset:eb,formState:{errors:eu,isSubmitting:ej}}=(0,Y.mN)({defaultValues:{eventType:"TIMED_MESSAGE",isEnabled:!0,locationType:"VOICE",intervalValue:24,intervalUnit:"hours"}}),ep=(0,Y.FH)({control:em,name:"eventType"}),eg=(0,Y.FH)({control:em,name:"locationType"}),eE=(0,Y.FH)({control:em,name:"embedEnabled"}),eT=async()=>{try{let e=await fetch("/api/scheduler");if(!e.ok)throw Error("Failed to fetch events");let r=await e.json();el(r)}catch(r){e({title:"Error fetching events",description:r.message,status:"error",duration:5e3,isClosable:!0})}finally{ei(!1)}},ev=async()=>{try{let e=await fetch("/api/discord/channels");if(!e.ok)throw Error("Failed to fetch channels");let r=await e.json();es(r)}catch(r){e({title:"Error fetching channels",description:r.message,status:"error",duration:5e3,isClosable:!0})}};(0,$.useEffect)(()=>{eT(),ev()},[]);let{textChannels:eI,voiceChannels:eS,categories:ef}=(0,$.useMemo)(()=>{let e=et.filter(e=>4===e.raw_type).sort((e,r)=>e.position-r.position);return{textChannels:et.filter(e=>[0,5,10,11,12].includes(e.raw_type)),voiceChannels:et.filter(e=>[2,13].includes(e.raw_type)),categories:e}},[et]),ey=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(ed(e),e){let o={...e,startTime:e.startTime?new Date(e.startTime).toISOString().slice(0,16):"",endTime:e.endTime?new Date(e.endTime).toISOString().slice(0,16):""};if("TIMED_MESSAGE"===e.eventType&&"string"==typeof e.message)try{let d=JSON.parse(e.message);if(d&&"object"==typeof d&&!Array.isArray(d)&&d.embeds){var r,l,t,s,n,i,c;let e=d.embeds[0];o.message=d.content||"",o.embedEnabled=!0,o.embedTitle=e.title||"",o.embedDescription=e.description||"",o.embedColor=e.color?"#"+e.color.toString(16).padStart(6,"0"):"",o.embedAuthorName=(null==(r=e.author)?void 0:r.name)||"",o.embedAuthorUrl=(null==(l=e.author)?void 0:l.url)||"",o.embedAuthorIconUrl=(null==(t=e.author)?void 0:t.icon_url)||"",o.embedImageUrl=(null==(s=e.image)?void 0:s.url)||"",o.embedThumbnailUrl=(null==(n=e.thumbnail)?void 0:n.url)||"",o.embedFooterText=(null==(i=e.footer)?void 0:i.text)||"",o.embedFooterIconUrl=(null==(c=e.footer)?void 0:c.icon_url)||"",o.embedTimestamp=!!e.timestamp}else o.embedEnabled=!1}catch(e){o.embedEnabled=!1}eb(o)}else eb({eventType:"TIMED_MESSAGE",name:"",isEnabled:!0,locationType:"VOICE",message:"",description:"",channelId:"",eventChannelId:"",startTime:"",endTime:"",location:"",intervalValue:24,intervalUnit:"hours",embedEnabled:!1,embedTitle:"",embedDescription:"",embedColor:"",embedAuthorName:"",embedAuthorUrl:"",embedAuthorIconUrl:"",embedImageUrl:"",embedThumbnailUrl:"",embedFooterText:"",embedFooterIconUrl:"",embedTimestamp:!1});o()},eC=async r=>{let o=ec?"/api/scheduler?eventId=".concat(ec._id):"/api/scheduler",l=ec?"PUT":"POST",{eventType:t,name:s,isEnabled:n}=r,i={eventType:t,name:s,isEnabled:n};if("TIMED_MESSAGE"===t){if(i={...i,channelId:r.channelId,message:r.message,intervalValue:r.intervalValue,intervalUnit:r.intervalUnit},r.embedEnabled){let e={};r.embedTitle&&(e.title=r.embedTitle),r.embedDescription&&(e.description=r.embedDescription),r.embedColor&&(e.color=parseInt(r.embedColor.replace("#",""),16)),(r.embedAuthorName||r.embedAuthorUrl||r.embedAuthorIconUrl)&&(e.author={},r.embedAuthorName&&(e.author.name=r.embedAuthorName),r.embedAuthorUrl&&(e.author.url=r.embedAuthorUrl),r.embedAuthorIconUrl&&(e.author.icon_url=r.embedAuthorIconUrl)),r.embedImageUrl&&(e.image={url:r.embedImageUrl}),r.embedThumbnailUrl&&(e.thumbnail={url:r.embedThumbnailUrl}),(r.embedFooterText||r.embedFooterIconUrl)&&(e.footer={},r.embedFooterText&&(e.footer.text=r.embedFooterText),r.embedFooterIconUrl&&(e.footer.icon_url=r.embedFooterIconUrl)),r.embedTimestamp&&(e.timestamp=new Date().toISOString()),i.message=JSON.stringify({content:r.message||void 0,embeds:[e]})}}else"GUILD_EVENT"===t&&(i={...i,description:r.description,startTime:r.startTime?new Date(r.startTime).toISOString():void 0,endTime:r.endTime?new Date(r.endTime).toISOString():void 0,locationType:r.locationType},"VOICE"===r.locationType?i.eventChannelId=r.eventChannelId:i.location=r.location);try{let r=await fetch(o,{method:l,headers:{"Content-Type":"application/json"},body:JSON.stringify(i)});if(!r.ok){let e="Failed to save event";try{e=(await r.json()).error||e}catch(e){}throw Error(e)}e({title:"Event ".concat(ec?"updated":"created"),status:"success",duration:3e3,isClosable:!0}),eT(),er()}catch(r){e({title:"Error saving event",description:r.message,status:"error",duration:5e3,isClosable:!0})}},eU=async r=>{if(window.confirm("Are you sure you want to delete this event?"))try{let o=await fetch("/api/scheduler?eventId=".concat(r),{method:"DELETE"});if(!o.ok)throw Error((await o.json()).error||"Failed to delete");e({title:"Event deleted",status:"success",duration:3e3,isClosable:!0}),eT()}catch(r){e({title:"Error deleting event",description:r.message,status:"error",duration:5e3,isClosable:!0})}},eM=e=>{let r=et.find(r=>r.id===e);return r?"#".concat(r.name):"Unknown Channel"},ew=e=>{let r=ef.map(r=>({...r,children:e.filter(e=>e.parent_id===r.id).sort((e,r)=>e.position-r.position)})).filter(e=>e.children.length>0),o=e.filter(e=>!e.parent_id).sort((e,r)=>e.position-r.position);return(0,l.jsxs)(l.Fragment,{children:[r.map(e=>(0,l.jsx)("optgroup",{label:e.name,children:e.children.map(e=>(0,l.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))},e.id)),o.length>0&&(0,l.jsx)("optgroup",{label:"No Category",children:o.map(e=>(0,l.jsxs)("option",{value:e.id,children:["#",e.name]},e.id))})]})};return(0,l.jsxs)(Z.A,{children:[(0,l.jsx)(n.m,{maxW:"container.xl",py:8,children:(0,l.jsxs)(i.T,{spacing:8,align:"stretch",children:[(0,l.jsxs)(c.z,{justify:"space-between",children:[(0,l.jsxs)(i.T,{align:"start",children:[(0,l.jsxs)(d.D,{size:"lg",children:[(0,l.jsx)(a.I,{as:Q.wIk,mr:3}),"Event Scheduler"]}),(0,l.jsx)(h.E,{color:"gray.500",children:"Schedule timed messages and guild events for your server."})]}),(0,l.jsx)(m.$,{leftIcon:(0,l.jsx)(Q.GGD,{}),colorScheme:"blue",onClick:()=>ey(),children:"Create Event"})]}),(0,l.jsxs)(x.Z,{children:[(0,l.jsx)(b.a,{children:(0,l.jsx)(d.D,{size:"md",children:"Scheduled Events"})}),(0,l.jsx)(u.b,{children:en?(0,l.jsx)(j.y,{}):0===eo.length?(0,l.jsx)(h.E,{children:"No scheduled events found. Create one to get started!"}):(0,l.jsx)(p.a,{overflowX:"auto",children:(0,l.jsxs)(g.X,{variant:"simple",children:[(0,l.jsx)(E.d,{children:(0,l.jsxs)(T.Tr,{children:[(0,l.jsx)(v.Th,{children:"Status"}),(0,l.jsx)(v.Th,{children:"Type"}),(0,l.jsx)(v.Th,{children:"Name"}),(0,l.jsx)(v.Th,{children:"Target / Location"}),(0,l.jsx)(v.Th,{children:"Schedule"}),(0,l.jsx)(v.Th,{children:"Actions"})]})}),(0,l.jsx)(I.N,{children:eo.map(e=>(0,l.jsxs)(T.Tr,{children:[(0,l.jsx)(S.Td,{children:(0,l.jsx)(f.E,{colorScheme:e.isEnabled?"green":"red",children:e.isEnabled?"Enabled":"Disabled"})}),(0,l.jsx)(S.Td,{children:(0,l.jsxs)(c.z,{children:[(0,l.jsx)(a.I,{as:"GUILD_EVENT"===e.eventType?Q.usP:Q.mEP}),(0,l.jsx)(h.E,{children:"GUILD_EVENT"===e.eventType?"Guild Event":"Message"})]})}),(0,l.jsx)(S.Td,{children:e.name}),(0,l.jsx)(S.Td,{children:"TIMED_MESSAGE"===e.eventType?eM(e.channelId):"VOICE"===e.locationType?eM(e.eventChannelId):e.location}),(0,l.jsx)(S.Td,{children:"TIMED_MESSAGE"===e.eventType?(0,l.jsx)(h.E,{fontFamily:"mono",fontSize:"sm",children:e.cron||"".concat(e.intervalValue," ").concat(e.intervalUnit)}):(0,l.jsx)(h.E,{fontSize:"sm",children:new Date(e.startTime).toLocaleString()})}),(0,l.jsx)(S.Td,{children:(0,l.jsxs)(c.z,{spacing:2,children:[(0,l.jsx)(y.m,{label:"Edit Event",children:(0,l.jsx)(C.K,{"aria-label":"Edit",icon:(0,l.jsx)(Q.SG1,{}),size:"sm",onClick:()=>ey(e)})}),(0,l.jsx)(y.m,{label:"Delete Event",children:(0,l.jsx)(C.K,{"aria-label":"Delete",icon:(0,l.jsx)(Q.IXo,{}),size:"sm",colorScheme:"red",onClick:()=>eU(e._id)})})]})})]},e._id))})]})})})]})]})}),(0,l.jsxs)(U.aF,{isOpen:r,onClose:er,size:"6xl",closeOnOverlayClick:!1,children:[" ",(0,l.jsx)(M.m,{bg:"blackAlpha.600"}),(0,l.jsxs)(w.$,{bg:ea.colors.background,border:"2px solid",borderColor:"blue.400",maxW:"1200px",as:"form",onSubmit:eh(eC),children:[(0,l.jsxs)(D.r,{color:ea.colors.text,children:[ec?"Edit":"Create"," Scheduled Event"]}),(0,l.jsx)(A.s,{}),(0,l.jsx)(_.c,{pb:6,children:(0,l.jsxs)(i.T,{spacing:6,align:"stretch",children:[(0,l.jsxs)(k.MJ,{children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Event Type"}),(0,l.jsx)(Y.xI,{name:"eventType",control:em,render:e=>{let{field:r}=e;return(0,l.jsx)(N.z,{...r,children:(0,l.jsxs)(G.B,{direction:"row",spacing:5,children:[(0,l.jsx)(J.s,{value:"TIMED_MESSAGE",children:"Timed Message"}),(0,l.jsx)(J.s,{value:"GUILD_EVENT",children:"Guild Event"})]})})}})]}),(0,l.jsxs)(V.t,{variant:"enclosed",colorScheme:"blue",children:[(0,l.jsx)(L.w,{children:"TIMED_MESSAGE"===ep?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(O.o,{children:"Timed Message Settings"}),(0,l.jsx)(O.o,{children:"Message Builder"})]}):(0,l.jsx)(O.o,{children:"Guild Event Settings"})}),(0,l.jsxs)(z.T,{children:["TIMED_MESSAGE"===ep&&(0,l.jsx)(q.K,{children:(0,l.jsxs)(i.T,{spacing:4,align:"stretch",children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.name,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Event Name"}),(0,l.jsx)(R.p,{...ex("name",{required:"Event name is required"}),placeholder:"e.g., Daily Announcement",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{display:"flex",alignItems:"center",children:[(0,l.jsx)(F.l,{htmlFor:"isEnabled",mb:"0",color:ea.colors.text,children:"Enable Event"}),(0,l.jsx)(P.d,{id:"isEnabled",...ex("isEnabled")})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.channelId,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Channel"}),(0,l.jsx)(B.l,{...ex("channelId",{required:"Channel is required"}),placeholder:"Select a text channel",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border,children:ew(eI)})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.intervalValue,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Interval"}),(0,l.jsxs)(c.z,{children:[(0,l.jsx)(Y.xI,{name:"intervalValue",control:em,rules:{required:"Interval is required",min:1},render:e=>{let{field:r}=e;return(0,l.jsxs)(W.Q7,{min:1,...r,onChange:e=>r.onChange(Number(e)),children:[(0,l.jsx)(W.OO,{placeholder:"e.g., 5",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border}),(0,l.jsxs)(W.lw,{children:[(0,l.jsx)(W.Q0,{}),(0,l.jsx)(W.Sh,{})]})]})}}),(0,l.jsxs)(B.l,{...ex("intervalUnit",{required:"Unit is required"}),w:"40%",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border,children:[(0,l.jsx)("option",{value:"seconds",children:"Seconds"}),(0,l.jsx)("option",{value:"minutes",children:"Minutes"}),(0,l.jsx)("option",{value:"hours",children:"Hours"}),(0,l.jsx)("option",{value:"days",children:"Days"}),(0,l.jsx)("option",{value:"weeks",children:"Weeks"}),(0,l.jsx)("option",{value:"months",children:"Months"})]})]}),(0,l.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Example: Every 5 minutes, every 2 hours, every 1 day, etc."})]})]})}),"TIMED_MESSAGE"===ep&&(0,l.jsx)(q.K,{children:(0,l.jsxs)(i.T,{spacing:4,align:"stretch",children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.message,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Message Content"}),(0,l.jsx)(X.T,{...ex("message"),placeholder:"Enter your message content. Optional if using an embed. Supports Discord markdown.",rows:3,bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border}),(0,l.jsx)(h.E,{fontSize:"xs",color:ea.colors.textSecondary,mt:1,children:"This will be sent as plain text or alongside your embed."})]}),(0,l.jsxs)(k.MJ,{display:"flex",alignItems:"center",children:[(0,l.jsx)(F.l,{htmlFor:"embedEnabled",mb:"0",color:ea.colors.text,children:"Enable Embed"}),(0,l.jsx)(P.d,{id:"embedEnabled",...ex("embedEnabled")})]}),eE&&(0,l.jsxs)(i.T,{spacing:4,w:"full",pl:4,borderLeft:"2px solid",borderColor:"blue.200",children:[(0,l.jsx)(h.E,{fontSize:"lg",fontWeight:"bold",color:ea.colors.text,children:"Embed Details"}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedTitle,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Title"}),(0,l.jsx)(R.p,{...ex("embedTitle"),placeholder:"e.g., Important Announcement",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedDescription,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Description"}),(0,l.jsx)(X.T,{...ex("embedDescription"),placeholder:"Main content of the embed.",rows:3,bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedColor,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Color (Hex Code)"}),(0,l.jsx)(R.p,{type:"color",...ex("embedColor"),placeholder:"e.g., #00FF00",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border}),(0,l.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Enter a hex color code (e.g., #RRGGBB)."})]}),(0,l.jsx)(h.E,{fontSize:"md",fontWeight:"bold",color:ea.colors.text,mt:4,mb:2,children:"Author Fields"}),(0,l.jsxs)(H.r,{columns:2,spacing:4,children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedAuthorName,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Author Name"}),(0,l.jsx)(R.p,{...ex("embedAuthorName"),placeholder:"e.g., 404 Bot",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedAuthorUrl,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Author URL"}),(0,l.jsx)(R.p,{...ex("embedAuthorUrl"),placeholder:"https://example.com",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedAuthorIconUrl,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Author Icon URL"}),(0,l.jsx)(R.p,{...ex("embedAuthorIconUrl"),placeholder:"https://example.com/icon.png",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]})]}),(0,l.jsx)(h.E,{fontSize:"md",fontWeight:"bold",color:ea.colors.text,mt:4,mb:2,children:"Image/Thumbnail Fields"}),(0,l.jsxs)(H.r,{columns:2,spacing:4,children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedImageUrl,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Image URL"}),(0,l.jsx)(R.p,{...ex("embedImageUrl"),placeholder:"https://example.com/image.png",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border}),(0,l.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Large image displayed above the footer."})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedThumbnailUrl,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Thumbnail URL"}),(0,l.jsx)(R.p,{...ex("embedThumbnailUrl"),placeholder:"https://example.com/thumbnail.png",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border}),(0,l.jsx)(h.E,{fontSize:"xs",color:"gray.500",mt:1,children:"Small image usually in the top right."})]})]}),(0,l.jsx)(h.E,{fontSize:"md",fontWeight:"bold",color:ea.colors.text,mt:4,mb:2,children:"Footer Fields"}),(0,l.jsxs)(H.r,{columns:2,spacing:4,children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedFooterText,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Footer Text"}),(0,l.jsx)(R.p,{...ex("embedFooterText"),placeholder:"e.g., Powered by 404 Bot",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{isInvalid:!!eu.embedFooterIconUrl,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Footer Icon URL"}),(0,l.jsx)(R.p,{...ex("embedFooterIconUrl"),placeholder:"https://example.com/footer-icon.png",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]})]}),(0,l.jsxs)(k.MJ,{display:"flex",alignItems:"center",children:[(0,l.jsx)(F.l,{htmlFor:"embedTimestamp",mb:"0",color:ea.colors.text,children:"Show Timestamp"}),(0,l.jsx)(P.d,{id:"embedTimestamp",...ex("embedTimestamp")}),(0,l.jsx)(h.E,{fontSize:"xs",color:"gray.500",ml:2,children:"Adds current time to the embed footer."})]})]})]})}),"GUILD_EVENT"===ep&&(0,l.jsx)(q.K,{children:(0,l.jsxs)(i.T,{spacing:4,align:"stretch",children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.description,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Description"}),(0,l.jsx)(X.T,{...ex("description",{required:"GUILD_EVENT"===ep&&"Description is required"}),placeholder:"Describe the event.",rows:3,bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(H.r,{columns:2,spacing:4,w:"full",children:[(0,l.jsxs)(k.MJ,{isInvalid:!!eu.startTime,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Start Time"}),(0,l.jsx)(R.p,{type:"datetime-local",...ex("startTime",{required:"GUILD_EVENT"===ep&&"Start time is required"}),bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]}),(0,l.jsxs)(k.MJ,{children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"End Time (Optional)"}),(0,l.jsx)(R.p,{type:"datetime-local",...ex("endTime"),bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]})]}),(0,l.jsxs)(k.MJ,{children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Location Type"}),(0,l.jsx)(Y.xI,{name:"locationType",control:em,render:e=>{let{field:r}=e;return(0,l.jsx)(N.z,{...r,children:(0,l.jsxs)(G.B,{direction:"row",spacing:5,children:[(0,l.jsx)(J.s,{value:"VOICE",children:"Voice Channel"}),(0,l.jsx)(J.s,{value:"EXTERNAL",children:"External URL"})]})})}})]}),"VOICE"===eg?(0,l.jsxs)(k.MJ,{isInvalid:!!eu.eventChannelId,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"Voice Channel"}),(0,l.jsx)(B.l,{...ex("eventChannelId",{required:"GUILD_EVENT"===ep&&"VOICE"===eg&&"Voice channel is required"}),placeholder:"Select a voice channel",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border,children:ew(eS)})]}):(0,l.jsxs)(k.MJ,{isInvalid:!!eu.location,children:[(0,l.jsx)(F.l,{color:ea.colors.text,children:"External URL"}),(0,l.jsx)(R.p,{...ex("location",{required:"GUILD_EVENT"===ep&&"EXTERNAL"===eg&&"URL is required"}),placeholder:"https://your.event.link",bg:ea.colors.background,color:ea.colors.text,borderColor:ea.colors.border})]})]})})]})]})]})}),(0,l.jsxs)(K.j,{children:[(0,l.jsx)(m.$,{variant:"ghost",mr:3,onClick:er,children:"Cancel"}),(0,l.jsx)(m.$,{colorScheme:"blue",type:"submit",isLoading:ej,children:ec?"Save Changes":"Create Event"})]})]})]})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[4108,9998,4976,217,2965,3177,3035,4687,341,636,6593,8792],()=>r(39137)),_N_E=e.O()}]);