import { Client } from 'discord.js';
import { Logger } from './Logger.js';
import { BotInstance, Addon } from '../types/index.js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath, pathToFileURL } from 'url';
import * as chokidar from 'chokidar';
import YAML from 'yaml';
import { CommandHandler } from './CommandHandler.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export class AddonManager {
  private bot: BotInstance;
  private logger: any;
  private addonDirsToScan: string[];
  private reloadWatcher?: chokidar.FSWatcher;

  constructor(bot: BotInstance) {
    this.bot = bot;
    this.logger = Logger.createLogger();

    const distDir = path.join(__dirname, '../../dist/addons');
    const srcDir = path.join(__dirname, '../../src/addons');

    this.addonDirsToScan = [];
    if (fs.existsSync(distDir)) this.addonDirsToScan.push(distDir);
    if (fs.existsSync(srcDir)) this.addonDirsToScan.push(srcDir);

    // Set up reload signal watcher
    this.setupReloadWatcher();
  }

  private setupReloadWatcher(): void {
    const reloadSignalPath = path.resolve(process.cwd(), 'addon-reload.signal');

    // Ensure directory exists (should be root but guard anyway)
    const dir = path.dirname(reloadSignalPath);
    if (!fs.existsSync(dir)) fs.mkdirSync(dir, { recursive: true });

    this.logger.info(`Watching for reload signals at: ${reloadSignalPath}`);
    
    this.reloadWatcher = chokidar.watch(reloadSignalPath, {
      persistent: true,
      ignoreInitial: true,
      awaitWriteFinish: {
        stabilityThreshold: 100,
        pollInterval: 100
      }
    });

    this.reloadWatcher.on('add', () => this.handleReloadSignal(reloadSignalPath));
    this.reloadWatcher.on('change', () => this.handleReloadSignal(reloadSignalPath));
  }

  private async handleReloadSignal(signalPath: string): Promise<void> {
    try {
      // Read the signal file
      const signalContent = fs.readFileSync(signalPath, 'utf-8');
      const signal = JSON.parse(signalContent);
      
      this.logger.info(`Addon reload requested by ${signal.requestedBy} at ${signal.timestamp}`);
      
      // Perform the reload
      await this.reloadAllAddons();
      
      // Clean up the signal file
      if (fs.existsSync(signalPath)) {
        fs.unlinkSync(signalPath);
      }
      
      this.logger.info('Addon reload completed successfully');
    } catch (error) {
      this.logger.error('Error handling reload signal:', error);
    }
  }

  public async reloadAllAddons(): Promise<void> {
    try {
      this.logger.info('Reloading all addons...');
      
      // Clear existing addons and commands
      this.bot.addons.clear();
      this.bot.commands.clear();
      
      // Remove all listeners to prevent memory leaks
      this.bot.client.removeAllListeners();
      
      // Reload all addons
      await this.loadAllAddons();
      
      // Re-initialize command handler to attach core interaction listener again
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore - allow dynamic reassignment
      this.bot.commandHandler = new CommandHandler(this.bot);
      
      // Ensure slash commands are re-registered with Discord after reload
      await this.registerSlashCommands();
      
      this.logger.info('All addons reloaded successfully');
    } catch (error) {
      this.logger.error('Error reloading addons:', error);
      throw error;
    }
  }

  public async loadAllAddons(): Promise<void> {
    try {
      // Create addons directory if it doesn't exist
      if (!fs.existsSync(this.addonDirsToScan[0])) {
        fs.mkdirSync(this.addonDirsToScan[0], { recursive: true });
      }

      // Determine disabled addons from config.yml
      let disabled: string[] = [];
      let addonsGloballyEnabled = true;
      try {
        const configPathAttempts = [
          path.resolve(process.cwd(), 'config.yml'),
          path.resolve(process.cwd(), '404-bot', 'config.yml'),
        ];
        const cfgPath = configPathAttempts.find(p => fs.existsSync(p));
        if (cfgPath) {
          const cfgRaw = fs.readFileSync(cfgPath, 'utf8');
          const cfg = YAML.parse(cfgRaw);
          addonsGloballyEnabled = cfg.addons?.enabled !== false;
          disabled = cfg.addons?.disabled ?? [];
        }
      } catch (err) {
        this.logger.warn('Could not read config.yml to resolve disabled addons');
      }

      if (!addonsGloballyEnabled) {
        this.logger.info('Addons are globally disabled via config.yml');
        return;
      }

      // Get addon directories excluding disabled
      const addonDirs = fs.readdirSync(this.addonDirsToScan[0], { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name)
        .filter(dir => !disabled.includes(dir));

      for (const addonDir of addonDirs) {
        try {
          await this.loadAddon(addonDir);
        } catch (error) {
          this.logger.error(`Failed to load addon ${addonDir}:`, error);
        }
      }

      this.logger.info(`Loaded ${this.bot.addons.size} addons`);

      // After all addons are loaded, register slash commands with Discord
      await this.registerSlashCommands();
    } catch (error) {
      this.logger.error('Failed to load addons:', error);
    }
  }

  private async loadAddon(addonDir: string): Promise<void> {
    let addonPath = path.join(this.addonDirsToScan[0], addonDir, 'index.js');
    if (!fs.existsSync(addonPath)) {
      // Fallback to TypeScript source file
      addonPath = path.join(this.addonDirsToScan[0], addonDir, 'index.ts');
      if (!fs.existsSync(addonPath)) {
        this.logger.warn(`Addon ${addonDir} does not have an index.js or index.ts file`);
        return;
      }
    }

    try {
      // Convert the file path to a file:// URL and add cache-buster query to force fresh import
      const baseUrl = pathToFileURL(addonPath).href;
      const addonUrl = `${baseUrl}?update=${Date.now()}`;
      const addon = (await import(addonUrl)).default as Addon;

      if (!addon || !addon.info) {
        this.logger.warn(`Addon ${addonDir} is not properly formatted`);
        return;
      }

      // Register events
      if (addon.events) {
        for (const event of addon.events) {
          if (event.once) {
            this.bot.client.once(event.name, (...args) => event.execute(...args));
          } else {
            this.bot.client.on(event.name, (...args) => event.execute(...args));
          }
        }
      }

      // Call onLoad if it exists
      if (addon.onLoad) {
        await addon.onLoad(this.bot);
      }

      // Add to loaded addons
      this.bot.addons.set(addon.info.name, addon);

      // Register addon commands
      if (addon.commands && Array.isArray(addon.commands)) {
        for (const cmd of addon.commands) {
          if (!cmd?.data?.name || typeof cmd.execute !== 'function') continue;
          this.bot.commands.set(cmd.data.name, {
            ...cmd,
            addon: addon.info.name,
          });
        }
      }

      this.logger.info(`Loaded addon: ${addon.info.name} v${addon.info.version}`);

    } catch (error) {
      this.logger.error(`Error loading addon ${addonDir}:`, error);
    }
  }

  /**
   * Registers (or updates) all loaded slash commands with Discord.
   * If a guildId is present in the config they will be registered as guild commands for faster updates;
   * otherwise they will be registered globally.
   */
  public async registerSlashCommands(): Promise<void> {
    try {
      const { bot } = this.bot.config;
      if (!bot?.token || !bot?.clientId) {
        this.logger.warn('Skipping slash command registration – missing bot token or clientId in config');
        return;
      }

      const commandsPayload = Array.from(this.bot.commands.values()).map((cmd: any) => {
        // Each command should have a SlashCommandBuilder with toJSON()
        try {
          return cmd.data.toJSON();
        } catch {
          return null;
        }
      }).filter(Boolean);

      // Lazy–load REST from discord.js so we don't pay the cost if not needed
      const { REST, Routes } = await import('discord.js');
      const rest = new REST({ version: '10' }).setToken(bot.token);

      // Always use PUT to completely replace all commands (this clears deleted ones)
      if (bot.guildId) {
        await rest.put(
          Routes.applicationGuildCommands(bot.clientId, bot.guildId),
          { body: commandsPayload }
        );
        this.logger.info(`✅ Successfully registered ${commandsPayload.length} guild slash commands (cleared old ones)`);
      } else {
        await rest.put(
          Routes.applicationCommands(bot.clientId),
          { body: commandsPayload }
        );
        this.logger.info(`✅ Successfully registered ${commandsPayload.length} global slash commands (cleared old ones)`);
      }
    } catch (error) {
      this.logger.error('Failed to register slash commands with Discord:', error);
    }
  }

  /**
   * Clears all registered slash commands from Discord.
   * Useful when you want to completely reset the command list.
   */
  public async clearAllSlashCommands(): Promise<void> {
    try {
      const { bot } = this.bot.config;
      if (!bot?.token || !bot?.clientId) {
        this.logger.warn('Skipping slash command clearing – missing bot token or clientId in config');
        return;
      }

      const { REST, Routes } = await import('discord.js');
      const rest = new REST({ version: '10' }).setToken(bot.token);

      if (bot.guildId) {
        await rest.put(
          Routes.applicationGuildCommands(bot.clientId, bot.guildId),
          { body: [] }
        );
        this.logger.info('✅ Successfully cleared all guild slash commands');
      } else {
        await rest.put(
          Routes.applicationCommands(bot.clientId),
          { body: [] }
        );
        this.logger.info('✅ Successfully cleared all global slash commands');
      }
    } catch (error) {
      this.logger.error('Failed to clear slash commands from Discord:', error);
    }
  }

  public cleanup(): void {
    if (this.reloadWatcher) {
      this.reloadWatcher.close();
    }
  }
} 