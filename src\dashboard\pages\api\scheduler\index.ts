import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { loadBotConfig } from '../../../utils/config';
import { MongoClient, ObjectId } from 'mongodb';

const botConfig = loadBotConfig();

let cachedClient: MongoClient | null = null;
const mongoUrl = botConfig.database?.url || 'mongodb://localhost:27017';
const dbName = botConfig.database?.name || 'discord_bot';

async function getDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(botConfig.database?.options || {}),
    });
  }
  return cachedClient.db(dbName);
}

const convertToCron = (value: number, unit: 'seconds' | 'minutes' | 'hours' | 'days' | 'weeks' | 'months'): string | null => {
    // This function is no longer needed as cron will not be used for timed messages.
    // The intervalValue and intervalUnit will be stored directly and handled by setInterval on the bot side.
    return null;
};

const validatePayload = (body: any) => {
    const { eventType, name, isEnabled } = body;
    if (name === undefined || typeof isEnabled !== 'boolean' || !eventType) {
        return 'Missing required fields: name, isEnabled, eventType';
    }

    if (eventType === 'TIMED_MESSAGE') {
        const { channelId, message, intervalValue, intervalUnit } = body;
        if (!channelId || !message || !intervalValue || !intervalUnit) {
            return 'For Timed Message, must provide: channelId, message, intervalValue, intervalUnit';
        }
        if (typeof intervalValue !== 'number' || intervalValue <= 0) {
            return 'Interval value must be a positive number.';
        }
        // Now allow all original units, as setInterval will handle them
        if (!['seconds', 'minutes', 'hours', 'days', 'weeks', 'months'].includes(intervalUnit)) {
            return 'Invalid interval unit provided.';
        }
    } else if (eventType === 'GUILD_EVENT') {
        const { description, startTime, locationType } = body;
        if (!description || !startTime || !locationType) {
            return 'For Guild Event, must provide: description, startTime, locationType';
        }
        if (locationType === 'VOICE' && !body.eventChannelId) {
            return 'Voice channel is required for Voice Guild Events';
        }
        if (locationType === 'EXTERNAL' && !body.location) {
            return 'External URL is required for External Guild Events';
        }
    } else {
        return 'Invalid eventType specified';
    }
    return null;
};

const buildEventDocument = (body: any, isNew: boolean) => {
    const { name, isEnabled, eventType } = body;

    const doc: any = {
        name,
        isEnabled,
        eventType,
        updatedAt: new Date(),
    };

    if (isNew) {
        doc.createdAt = new Date();
    }

    if (eventType === 'TIMED_MESSAGE') {
        const { channelId, message, intervalValue, intervalUnit } = body;
        doc.channelId = channelId;
        doc.message = message;
        doc.intervalValue = intervalValue; // Store directly
        doc.intervalUnit = intervalUnit;   // Store directly
        doc.cron = null; // Ensure cron is null/undefined if it was previously set
    } else if (eventType === 'GUILD_EVENT') {
        const { description, startTime, endTime, locationType, eventChannelId, location } = body;
        doc.description = description;
        doc.startTime = startTime;
        doc.endTime = endTime || null;
        doc.locationType = locationType;
        if (locationType === 'VOICE') {
            doc.eventChannelId = eventChannelId;
        } else {
            doc.location = location;
        }
    }
    return doc;
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session?.user || !(session.user as any).isAdmin) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const db = await getDb();
  const collection = db.collection('scheduled_events');
  const { eventId } = req.query;

  try {
    switch (req.method) {
      case 'GET': {
        const events = await collection.find({}).sort({ createdAt: -1 }).toArray();
        return res.status(200).json(events);
      }

      case 'POST': {
        const validationError = validatePayload(req.body);
        if (validationError) {
            return res.status(400).json({ error: validationError });
        }
        const newEvent = buildEventDocument(req.body, true);
        const result = await collection.insertOne(newEvent);
        return res.status(201).json({ ...newEvent, _id: result.insertedId });
      }

      case 'PUT': {
        if (!eventId || typeof eventId !== 'string') {
          return res.status(400).json({ error: 'Invalid event ID' });
        }
        const validationError = validatePayload(req.body);
        if (validationError) {
            return res.status(400).json({ error: validationError });
        }
        const updatedEvent = buildEventDocument(req.body, false);

        const result = await collection.updateOne(
          { _id: new ObjectId(eventId) },
          { $set: updatedEvent }
        );
        if (result.matchedCount === 0) {
          return res.status(404).json({ error: 'Event not found' });
        }
        return res.status(200).json({ message: 'Event updated successfully' });
      }

      case 'DELETE': {
        if (!eventId || typeof eventId !== 'string') {
          return res.status(400).json({ error: 'Invalid event ID' });
        }
        const result = await collection.deleteOne({ _id: new ObjectId(eventId) });
        if (result.deletedCount === 0) {
          return res.status(404).json({ error: 'Event not found' });
        }
        return res.status(200).json({ message: 'Event deleted successfully' });
      }

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).end(`Method ${req.method} Not Allowed`);
    }
  } catch (err) {
    console.error('Scheduler API error:', err);
    const error = err as Error;
    if (error.message.includes('BSONError')) {
      return res.status(400).json({ error: 'Invalid event ID format' });
    }
    return res.status(500).json({ error: 'Server error', details: error.message });
  }
}

export const config = {
  api: {
    bodyParser: true,
  },
}; 