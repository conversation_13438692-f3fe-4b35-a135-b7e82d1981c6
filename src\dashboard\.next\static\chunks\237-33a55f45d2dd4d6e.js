(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[237],{738:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isTypedArray=function(e){return ArrayBuffer.isView(e)&&!(e instanceof DataView)}},1292:(e,t,r)=>{"use strict";r.d(t,{y:()=>V});var n=r(94285),i=r(63074),a=r.n(i),o=r(76544),l=r(3638),u=r(71998),c=r(13739),s=r(17951);function f(){return(f=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var h=(e,t,r,n,i)=>{var a,o=r-n;return"M ".concat(e,",").concat(t)+"L ".concat(e+r,",").concat(t)+"L ".concat(e+r-o/2,",").concat(t+i)+"L ".concat(e+r-o/2-n,",").concat(t+i)+"L ".concat(e,",").concat(t," Z")},p={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},d=e=>{var t=(0,c.e)(e,p),r=(0,n.useRef)(),[i,a]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&a(e)}catch(e){}},[]);var{x:o,y:d,upperWidth:y,lowerWidth:v,height:g,className:m}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isUpdateAnimationActive:O}=t;if(o!==+o||d!==+d||y!==+y||v!==+v||g!==+g||0===y&&0===v||0===g)return null;var j=(0,l.$)("recharts-trapezoid",m);return O?n.createElement(s.i,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:g,x:o,y:d},to:{upperWidth:y,lowerWidth:v,height:g,x:o,y:d},duration:w,animationEasing:b,isActive:O},e=>{var{upperWidth:a,lowerWidth:o,height:l,x:c,y:p}=e;return n.createElement(s.i,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,easing:b},n.createElement("path",f({},(0,u.J9)(t,!0),{className:j,d:h(c,p,a,o,l),ref:r})))}):n.createElement("g",null,n.createElement("path",f({},(0,u.J9)(t,!0),{className:j,d:h(o,d,y,v,g)})))},y=r(78369),v=r(53898);let g=Math.cos,m=Math.sin,b=Math.sqrt,w=Math.PI,x=2*w,O={draw(e,t){let r=b(t/w);e.moveTo(r,0),e.arc(0,0,r,0,x)}},j=b(1/3),P=2*j,A=m(w/10)/m(7*w/10),E=m(x/10)*A,M=-g(x/10)*A,S=b(3),_=b(3)/2,k=1/b(12),T=(k/2+1)*3;var C=r(31896),D=r(92389);b(3),b(3);var N=r(29427),I=["type","size","sizeType"];function z(){return(z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function L(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?L(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):L(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var B={symbolCircle:O,symbolCross:{draw(e,t){let r=b(t/5)/2;e.moveTo(-3*r,-r),e.lineTo(-r,-r),e.lineTo(-r,-3*r),e.lineTo(r,-3*r),e.lineTo(r,-r),e.lineTo(3*r,-r),e.lineTo(3*r,r),e.lineTo(r,r),e.lineTo(r,3*r),e.lineTo(-r,3*r),e.lineTo(-r,r),e.lineTo(-3*r,r),e.closePath()}},symbolDiamond:{draw(e,t){let r=b(t/P),n=r*j;e.moveTo(0,-r),e.lineTo(n,0),e.lineTo(0,r),e.lineTo(-n,0),e.closePath()}},symbolSquare:{draw(e,t){let r=b(t),n=-r/2;e.rect(n,n,r,r)}},symbolStar:{draw(e,t){let r=b(.8908130915292852*t),n=E*r,i=M*r;e.moveTo(0,-r),e.lineTo(n,i);for(let t=1;t<5;++t){let a=x*t/5,o=g(a),l=m(a);e.lineTo(l*r,-o*r),e.lineTo(o*n-l*i,l*n+o*i)}e.closePath()}},symbolTriangle:{draw(e,t){let r=-b(t/(3*S));e.moveTo(0,2*r),e.lineTo(-S*r,-r),e.lineTo(S*r,-r),e.closePath()}},symbolWye:{draw(e,t){let r=b(t/T),n=r/2,i=r*k,a=r*k+r,o=-n;e.moveTo(n,i),e.lineTo(n,a),e.lineTo(o,a),e.lineTo(-.5*n-_*i,_*n+-.5*i),e.lineTo(-.5*n-_*a,_*n+-.5*a),e.lineTo(-.5*o-_*a,_*o+-.5*a),e.lineTo(-.5*n+_*i,-.5*i-_*n),e.lineTo(-.5*n+_*a,-.5*a-_*n),e.lineTo(-.5*o+_*a,-.5*a-_*o),e.closePath()}}},U=Math.PI/180,F=e=>B["symbol".concat((0,N.Zb)(e))]||O,K=(e,t,r)=>{if("area"===t)return e;switch(r){case"cross":return 5*e*e/9;case"diamond":return .5*e*e/Math.sqrt(3);case"square":return e*e;case"star":var n=18*U;return 1.25*e*e*(Math.tan(n)-Math.tan(2*n)*Math.tan(n)**2);case"triangle":return Math.sqrt(3)*e*e/4;case"wye":return(21-10*Math.sqrt(3))*e*e/8;default:return Math.PI*e*e/4}},$=e=>{var{type:t="circle",size:r=64,sizeType:i="area"}=e,a=R(R({},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,I)),{},{type:t,size:r,sizeType:i}),{className:o,cx:c,cy:s}=a,f=(0,u.J9)(a,!0);return c===+c&&s===+s&&r===+r?n.createElement("path",z({},f,{className:(0,l.$)("recharts-symbols",o),transform:"translate(".concat(c,", ").concat(s,")"),d:(()=>{var e=F(t);return(function(e,t){let r=null,n=(0,D.i)(i);function i(){let i;if(r||(r=i=n()),e.apply(this,arguments).draw(r,+t.apply(this,arguments)),i)return r=null,i+""||null}return e="function"==typeof e?e:(0,C.A)(e||O),t="function"==typeof t?t:(0,C.A)(void 0===t?64:+t),i.type=function(t){return arguments.length?(e="function"==typeof t?t:(0,C.A)(t),i):e},i.size=function(e){return arguments.length?(t="function"==typeof e?e:(0,C.A)(+e),i):t},i.context=function(e){return arguments.length?(r=null==e?null:e,i):r},i})().type(e).size(K(r,i,t))()})()})):null};$.registerSymbol=(e,t)=>{B["symbol".concat((0,N.Zb)(e))]=t};var G=["option","shapeType","propTransformer","activeClassName","isActive"];function H(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function Z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?H(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function W(e,t){return Z(Z({},t),e)}function q(e){var{shapeType:t,elementProps:r}=e;switch(t){case"rectangle":return n.createElement(o.M,r);case"trapezoid":return n.createElement(d,r);case"sector":return n.createElement(y.h,r);case"symbols":if("symbols"===t)return n.createElement($,r);break;default:return null}}function V(e){var t,{option:r,shapeType:i,propTransformer:o=W,activeClassName:l="recharts-active-shape",isActive:u}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,G);if((0,n.isValidElement)(r))t=(0,n.cloneElement)(r,Z(Z({},c),(0,n.isValidElement)(r)?r.props:r));else if("function"==typeof r)t=r(c);else if(a()(r)&&"boolean"!=typeof r){var s=o(r,c);t=n.createElement(q,{shapeType:i,elementProps:s})}else t=n.createElement(q,{shapeType:i,elementProps:c});return u?n.createElement(v.W,{className:l},t):t}},3157:(e,t,r)=>{"use strict";r.d(t,{m:()=>n});var n={isSsr:!window.document||!window.document.createElement||!window.setTimeout}},3638:(e,t,r)=>{"use strict";function n(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n);return i}(e))&&(n&&(n+=" "),n+=t);return n}r.d(t,{$:()=>n})},4517:(e,t,r)=>{"use strict";r.d(t,{G:()=>f,j:()=>l});var n=r(79007),i=r(94285),a=r(15142),o=e=>e,l=()=>{var e=(0,i.useContext)(a.E);return e?e.store.dispatch:o},u=()=>{},c=()=>u,s=(e,t)=>e===t;function f(e){var t=(0,i.useContext)(a.E);return(0,n.useSyncExternalStoreWithSelector)(t?t.subscription.addNestedSub:c,t?t.store.getState:u,t?t.store.getState:u,t?e:u,s)}},4642:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(82707),i=r(92621),a=r(64172);t.last=function(e){if(a.isArrayLike(e))return n.last(i.toArray(e))}},4788:(e,t,r)=>{"use strict";r.d(t,{JN:()=>n,_5:()=>i,eC:()=>l,gY:()=>a,hX:()=>s,iO:()=>u,lZ:()=>c,pH:()=>f,x3:()=>o});var n=e=>e.rootProps.maxBarSize,i=e=>e.rootProps.barGap,a=e=>e.rootProps.barCategoryGap,o=e=>e.rootProps.barSize,l=e=>e.rootProps.stackOffset,u=e=>e.options.chartName,c=e=>e.rootProps.syncId,s=e=>e.rootProps.syncMethod,f=e=>e.options.eventEmitter},4792:(e,t,r)=>{"use strict";r.d(t,{CU:()=>s,Lx:()=>u,u3:()=>c});var n=r(68646),i=r(74201),a=(0,n.Z0)({name:"legend",initialState:{settings:{layout:"horizontal",align:"center",verticalAlign:"middle",itemSorter:"value"},size:{width:0,height:0},payload:[]},reducers:{setLegendSize(e,t){e.size.width=t.payload.width,e.size.height=t.payload.height},setLegendSettings(e,t){e.settings.align=t.payload.align,e.settings.layout=t.payload.layout,e.settings.verticalAlign=t.payload.verticalAlign,e.settings.itemSorter=t.payload.itemSorter},addLegendPayload(e,t){e.payload.push((0,i.h4)(t.payload))},removeLegendPayload(e,t){var r=(0,i.ss)(e).payload.indexOf((0,i.h4)(t.payload));r>-1&&e.payload.splice(r,1)}}}),{setLegendSize:o,setLegendSettings:l,addLegendPayload:u,removeLegendPayload:c}=a.actions,s=a.reducer},5124:(e,t,r)=>{"use strict";r.d(t,{x:()=>n});var n=e=>e.options.tooltipPayloadSearcher},5281:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(53689),i=r(56318);t.isEqual=function(e,t){return n.isEqualWith(e,t,i.noop)}},6037:(e,t,r)=>{"use strict";r.d(t,{J:()=>a,U:()=>i});var n=(0,r(68646).Z0)({name:"polarOptions",initialState:null,reducers:{updatePolarOptions:(e,t)=>t.payload}}),{updatePolarOptions:i}=n.actions,a=n.reducer},7411:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let r=/^(?:0|[1-9]\d*)$/;t.isIndex=function(e,t=Number.MAX_SAFE_INTEGER){switch(typeof e){case"number":return Number.isInteger(e)&&e>=0&&e<t;case"symbol":return!1;case"string":return r.test(e)}}},7507:(e,t,r)=>{"use strict";r.d(t,{E:()=>g});var n=r(94285),i=r(74643),a=r(17131),o=r(97214),l=r(86054),u=r(44251),c=r(62813),s=r(13739),f=r(38034),h=["width","height"];function p(){return(p=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var d={accessibilityLayer:!0,layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y=(0,n.forwardRef)(function(e,t){var r,i=(0,s.e)(e.categoricalChartProps,d),{width:y,height:v}=i,g=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,h);if(!(0,f.F)(y)||!(0,f.F)(v))return null;var{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,categoricalChartProps:O}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:m,defaultTooltipEventType:b,validateTooltipEventTypes:w,tooltipPayloadSearcher:x,eventEmitter:void 0}},reduxStoreName:null!=(r=O.id)?r:m},n.createElement(o.TK,{chartData:O.data}),n.createElement(l.s,{width:y,height:v,layout:i.layout,margin:i.margin}),n.createElement(u.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(c.L,p({},g,{width:y,height:v,ref:t})))}),v=["axis","item"],g=(0,n.forwardRef)((e,t)=>n.createElement(y,{chartName:"BarChart",defaultTooltipEventType:"axis",validateTooltipEventTypes:v,tooltipPayloadSearcher:i.uN,categoricalChartProps:e,ref:t}))},7746:(e,t,r)=>{"use strict";r.d(t,{E:()=>m});var n=r(94513),i=r(94285),a=r(13215),o=r(75387),l=r(22697),u=r(610),c=r(79364),s=r(33225),f=r(2923),h=r(56915);let p=(0,s.B)("div",{baseStyle:{boxShadow:"none",backgroundClip:"padding-box",cursor:"default",color:"transparent",pointerEvents:"none",userSelect:"none","&::before, &::after, *":{visibility:"hidden"}}}),d=(0,a.Vg)("skeleton-start-color"),y=(0,a.Vg)("skeleton-end-color"),v=(0,u.i7)({from:{opacity:0},to:{opacity:1}}),g=(0,u.i7)({from:{borderColor:d.reference,background:d.reference},to:{borderColor:y.reference,background:y.reference}}),m=(0,f.R)((e,t)=>{let r={...e,fadeDuration:"number"==typeof e.fadeDuration?e.fadeDuration:.4,speed:"number"==typeof e.speed?e.speed:.8},a=(0,h.V)("Skeleton",r),u=function(){let e=(0,i.useRef)(!0);return(0,i.useEffect)(()=>{e.current=!1},[]),e.current}(),{startColor:f="",endColor:m="",isLoaded:b,fadeDuration:w,speed:x,className:O,fitContent:j,animation:P,...A}=(0,o.M)(r),[E,M]=(0,c.rd)("colors",[f,m]),S=function(e){let t=(0,i.useRef)(void 0);return(0,i.useEffect)(()=>{t.current=e},[e]),t.current}(b),_=(0,l.cx)("chakra-skeleton",O),k={...E&&{[d.variable]:E},...M&&{[y.variable]:M}};if(b){let e=u||S?"none":`${v} ${w}s`;return(0,n.jsx)(s.B.div,{ref:t,className:_,__css:{animation:e},...A})}return(0,n.jsx)(p,{ref:t,className:_,...A,__css:{width:j?"fit-content":void 0,...a,...k,_dark:{...a._dark,...k},animation:P||`${x}s linear infinite alternate ${g}`}})});m.displayName="Skeleton"},8490:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObjectLike=function(e){return"object"==typeof e&&null!==e}},8569:(e,t,r)=>{"use strict";r.d(t,{r:()=>a});var n=r(94285),i=(0,n.createContext)(null),a=()=>null!=(0,n.useContext)(i)},10014:(e,t,r)=>{e.exports=r(64963).sortBy},11880:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(52312),i=r(15083);t.range=function(e,t,r){r&&"number"!=typeof r&&n.isIterateeCall(e,t,r)&&(t=r=void 0),e=i.toFinite(e),void 0===t?(t=e,e=0):t=i.toFinite(t),r=void 0===r?e<t?1:-1:i.toFinite(r);let a=Math.max(Math.ceil((t-e)/(r||1)),0),o=Array(a);for(let t=0;t<a;t++)o[t]=e,e+=r;return o}},12183:(e,t,r)=>{"use strict";r.d(t,{E:()=>f,r:()=>h});var n=r(94513),i=r(75387),a=r(29035),o=r(22697),l=r(2923),u=r(56915),c=r(33225);let[s,f]=(0,a.q)({name:"StatStylesContext",errorMessage:"useStatStyles returned is 'undefined'. Seems you forgot to wrap the components in \"<Stat />\" "}),h=(0,l.R)(function(e,t){let r=(0,u.o)("Stat",e),a={position:"relative",flex:"1 1 0%",...r.container},{className:l,children:f,...h}=(0,i.M)(e);return(0,n.jsx)(s,{value:r,children:(0,n.jsx)(c.B.div,{ref:t,...h,className:(0,o.cx)("chakra-stat",l),__css:a,children:(0,n.jsx)("dl",{children:f})})})});h.displayName="Stat"},12645:(e,t,r)=>{"use strict";r.d(t,{p:()=>c,v:()=>s});var n=r(94285),i=r(4517),a=r(83746),o=r(65084);function l(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function u(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?l(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function c(e){var t=(0,i.j)(),r=(0,n.useRef)(null);return(0,n.useEffect)(()=>{var n=u(u({},e),{},{stackId:(0,o.$8)(e.stackId)});null===r.current?t((0,a.g5)(n)):r.current!==n&&t((0,a.ZF)({prev:r.current,next:n})),r.current=n},[t,e]),(0,n.useEffect)(()=>()=>{r.current&&(t((0,a.Vi)(r.current)),r.current=null)},[t]),null}function s(e){var t=(0,i.j)();return(0,n.useEffect)(()=>(t((0,a.As)(e)),()=>{t((0,a.TK)(e))}),[t,e]),null}},12664:(e,t,r)=>{"use strict";r.d(t,{J:()=>n});var n=e=>e.tooltip},13040:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84878),i=r(7411),a=r(20794),o=r(70307);t.has=function(e,t){let r;if(0===(r=Array.isArray(t)?t:"string"==typeof t&&n.isDeepKey(t)&&e?.[t]==null?o.toPath(t):[t]).length)return!1;let l=e;for(let e=0;e<r.length;e++){let t=r[e];if((null==l||!Object.hasOwn(l,t))&&!((Array.isArray(l)||a.isArguments(l))&&i.isIndex(t)&&t<l.length))return!1;l=l[t]}return!0}},13739:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t){var r=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);return Object.keys(t).reduce((e,r)=>(void 0===e[r]&&void 0!==t[r]&&(e[r]=t[r]),e),r)}r.d(t,{e:()=>i})},14126:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if(!e||"object"!=typeof e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&"[object Object]"===Object.prototype.toString.call(e)}},15083:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(54073);t.toFinite=function(e){return e?(e=n.toNumber(e))===1/0||e===-1/0?(e<0?-1:1)*Number.MAX_VALUE:e==e?e:0:0===e?e:0}},15142:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(0,r(94285).createContext)(null)},15143:(e,t,r)=>{"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}r.d(t,{IZ:()=>l,Kg:()=>a,lY:()=>u,yy:()=>p}),r(94285);var a=Math.PI/180,o=e=>180*e/Math.PI,l=(e,t,r,n)=>({x:e+Math.cos(-a*n)*r,y:t+Math.sin(-a*n)*r}),u=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{top:0,right:0,bottom:0,left:0,width:0,height:0,brushBottom:0};return Math.min(Math.abs(e-(r.left||0)-(r.right||0)),Math.abs(t-(r.top||0)-(r.bottom||0)))/2},c=(e,t)=>{var{x:r,y:n}=e,{x:i,y:a}=t;return Math.sqrt((r-i)**2+(n-a)**2)},s=(e,t)=>{var{x:r,y:n}=e,{cx:i,cy:a}=t,l=c({x:r,y:n},{x:i,y:a});if(l<=0)return{radius:l,angle:0};var u=Math.acos((r-i)/l);return n>a&&(u=2*Math.PI-u),{radius:l,angle:o(u),angleInRadian:u}},f=e=>{var{startAngle:t,endAngle:r}=e,n=Math.min(Math.floor(t/360),Math.floor(r/360));return{startAngle:t-360*n,endAngle:r-360*n}},h=(e,t)=>{var{startAngle:r,endAngle:n}=t;return e+360*Math.min(Math.floor(r/360),Math.floor(n/360))},p=(e,t)=>{var r,{x:n,y:a}=e,{radius:o,angle:l}=s({x:n,y:a},t),{innerRadius:u,outerRadius:c}=t;if(o<u||o>c||0===o)return null;var{startAngle:p,endAngle:d}=f(t),y=l;if(p<=d){for(;y>d;)y-=360;for(;y<p;)y+=360;r=y>=p&&y<=d}else{for(;y>p;)y-=360;for(;y<d;)y+=360;r=y>=d&&y<=p}return r?i(i({},t),{},{radius:o,angle:h(y,t)}):null}},16806:(e,t,r)=>{"use strict";r.d(t,{$:()=>i});var n=r(4517),i=()=>(0,n.G)(e=>e.rootProps.accessibilityLayer)},16863:(e,t,r)=>{"use strict";r(91625)},17131:(e,t,r)=>{"use strict";r.d(t,{J:()=>eh});var n=r(94285);r(16863);var i=Symbol.for("react.forward_ref"),a=Symbol.for("react.memo");function o(e){return e.dependsOnOwnProps?!!e.dependsOnOwnProps:1!==e.length}var l={notify(){},get:()=>[]},u="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement,c="undefined"!=typeof navigator&&"ReactNative"===navigator.product,s=u||c?n.useLayoutEffect:n.useEffect;function f(e,t){return e===t?0!==e||0!==t||1/e==1/t:e!=e&&t!=t}var h={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},p={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},d={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},y={[i]:{$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},[a]:d};function v(e){return function(e){if("object"==typeof e&&null!==e){let{$$typeof:t}=e;switch(t){case null:switch(e=e.type){case null:case null:case null:case null:case null:return e;default:switch(e=e&&e.$$typeof){case null:case i:case null:case a:case null:return e;default:return t}}case null:return t}}}(e)===a?d:y[e.$$typeof]||h}var g=Object.defineProperty,m=Object.getOwnPropertyNames,b=Object.getOwnPropertySymbols,w=Object.getOwnPropertyDescriptor,x=Object.getPrototypeOf,O=Object.prototype,j=Symbol.for("react-redux-context"),P="undefined"!=typeof globalThis?globalThis:{},A=function(){if(!n.createContext)return{};let e=P[j]??=new Map,t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}(),E=function(e){let{children:t,context:r,serverState:i,store:a}=e,o=n.useMemo(()=>{let e=function(e,t){let r,n=l,i=0,a=!1;function o(){s.onStateChange&&s.onStateChange()}function u(){if(i++,!r){let t,i;r=e.subscribe(o),t=null,i=null,n={clear(){t=null,i=null},notify(){let e=t;for(;e;)e.callback(),e=e.next},get(){let e=[],r=t;for(;r;)e.push(r),r=r.next;return e},subscribe(e){let r=!0,n=i={callback:e,next:null,prev:i};return n.prev?n.prev.next=n:t=n,function(){r&&null!==t&&(r=!1,n.next?n.next.prev=n.prev:i=n.prev,n.prev?n.prev.next=n.next:t=n.next)}}}}}function c(){i--,r&&0===i&&(r(),r=void 0,n.clear(),n=l)}let s={addNestedSub:function(e){u();let t=n.subscribe(e),r=!1;return()=>{r||(r=!0,t(),c())}},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,u())},tryUnsubscribe:function(){a&&(a=!1,c())},getListeners:()=>n};return s}(a);return{store:a,subscription:e,getServerState:i?()=>i:void 0}},[a,i]),u=n.useMemo(()=>a.getState(),[a]);return s(()=>{let{subscription:e}=o;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==a.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[o,u]),n.createElement((r||A).Provider,{value:o},t)},M=r(51556),S=r(68646),_=r(74643),k=r(82284),T=r(68645),C=r(72761),D=r(20823);function N(e,t){return t instanceof HTMLElement?"HTMLElement <".concat(t.tagName,' class="').concat(t.className,'">'):t===window?"global.window":t}var I=r(91648),z=r(83746),L=r(74201),R=(0,S.Z0)({name:"referenceElements",initialState:{dots:[],areas:[],lines:[]},reducers:{addDot:(e,t)=>{e.dots.push(t.payload)},removeDot:(e,t)=>{var r=(0,L.ss)(e).dots.findIndex(e=>e===t.payload);-1!==r&&e.dots.splice(r,1)},addArea:(e,t)=>{e.areas.push(t.payload)},removeArea:(e,t)=>{var r=(0,L.ss)(e).areas.findIndex(e=>e===t.payload);-1!==r&&e.areas.splice(r,1)},addLine:(e,t)=>{e.lines.push(t.payload)},removeLine:(e,t)=>{var r=(0,L.ss)(e).lines.findIndex(e=>e===t.payload);-1!==r&&e.lines.splice(r,1)}}}),{addDot:B,removeDot:U,addArea:F,removeArea:K,addLine:$,removeLine:G}=R.actions,H=R.reducer,Z={x:0,y:0,width:0,height:0,padding:{top:0,right:0,bottom:0,left:0}},W=(0,S.Z0)({name:"brush",initialState:Z,reducers:{setBrushSettings:(e,t)=>null==t.payload?Z:t.payload}}),{setBrushSettings:q}=W.actions,V=W.reducer,Y=r(4792),J=r(96231),X=(0,S.Z0)({name:"polarAxis",initialState:{radiusAxis:{},angleAxis:{}},reducers:{addRadiusAxis(e,t){e.radiusAxis[t.payload.id]=(0,L.h4)(t.payload)},removeRadiusAxis(e,t){delete e.radiusAxis[t.payload.id]},addAngleAxis(e,t){e.angleAxis[t.payload.id]=(0,L.h4)(t.payload)},removeAngleAxis(e,t){delete e.angleAxis[t.payload.id]}}}),{addRadiusAxis:Q,removeRadiusAxis:ee,addAngleAxis:et,removeAngleAxis:er}=X.actions,en=X.reducer,ei=r(6037),ea=r(22695),eo=r(32449),el=r(71137),eu=(0,M.HY)({brush:V,cartesianAxis:I.CA,chartData:T.LV,graphicalItems:z.iZ,layout:C.Vp,legend:Y.CU,options:_.lJ,polarAxis:en,polarOptions:ei.J,referenceElements:H,rootProps:J.vE,tooltip:k.En}),ec=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"Chart";return(0,S.U1)({reducer:eu,preloadedState:e,middleware:e=>e({serializableCheck:!1}).concat([D.YF.middleware,D.fP.middleware,ea.$7.middleware,eo.x.middleware,el.k.middleware]),devTools:{serialize:{replacer:N},name:"recharts-".concat(t)}})},es=r(8569),ef=r(15142);function eh(e){var{preloadedState:t,children:r,reduxStoreName:i}=e,a=(0,es.r)(),o=(0,n.useRef)(null);if(a)return r;null==o.current&&(o.current=ec(t,i));var l=ef.E;return n.createElement(E,{context:l,store:o.current},r)}},17951:(e,t,r)=>{"use strict";r.d(t,{i:()=>D});var n=r(94285),i=r(72533),a=r.n(i),o=(e,t)=>[0,3*e,3*t-6*e,3*e-3*t+1],l=(e,t)=>e.map((e,r)=>e*t**r).reduce((e,t)=>e+t),u=(e,t)=>r=>l(o(e,t),r),c=(e,t)=>r=>l([...o(e,t).map((e,t)=>e*t).slice(1),0],r),s=function(){for(var e,t,r,n,i=arguments.length,a=Array(i),o=0;o<i;o++)a[o]=arguments[o];if(1===a.length)switch(a[0]){case"linear":[e,r,t,n]=[0,0,1,1];break;case"ease":[e,r,t,n]=[.25,.1,.25,1];break;case"ease-in":[e,r,t,n]=[.42,0,1,1];break;case"ease-out":[e,r,t,n]=[.42,0,.58,1];break;case"ease-in-out":[e,r,t,n]=[0,0,.58,1];break;default:var l=a[0].split("(");"cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length&&([e,r,t,n]=l[1].split(")")[0].split(",").map(e=>parseFloat(e)))}else 4===a.length&&([e,r,t,n]=a);var s=u(e,t),f=u(r,n),h=c(e,t),p=e=>e>1?1:e<0?0:e,d=e=>{for(var t=e>1?1:e,r=t,n=0;n<8;++n){var i=s(r)-t,a=h(r);if(1e-4>Math.abs(i-t)||a<1e-4)break;r=p(r-i/a)}return f(r)};return d.isStepper=!1,d},f=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{stiff:t=100,damping:r=8,dt:n=17}=e,i=(e,i,a)=>{var o=a+(-(e-i)*t-a*r)*n/1e3,l=a*n/1e3+e;return 1e-4>Math.abs(l-i)&&1e-4>Math.abs(o)?[i,0]:[l,o]};return i.isStepper=!0,i.dt=n,i},h=e=>{if("string"==typeof e)switch(e){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return s(e);case"spring":return f();default:if("cubic-bezier"===e.split("(")[0])return s(e)}return"function"==typeof e?e:null};function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=e=>e.replace(/([A-Z])/g,e=>"-".concat(e.toLowerCase())),v=(e,t,r)=>e.map(e=>"".concat(y(e)," ").concat(t,"ms ").concat(r)).join(","),g=(e,t)=>[Object.keys(e),Object.keys(t)].reduce((e,t)=>e.filter(e=>t.includes(e))),m=(e,t)=>Object.keys(t).reduce((r,n)=>d(d({},r),{},{[n]:e(n,t[n])}),{});function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var x=(e,t,r)=>e+(t-e)*r,O=e=>{var{from:t,to:r}=e;return t!==r},j=(e,t,r)=>{var n=m((t,r)=>{if(O(r)){var[n,i]=e(r.from,r.to,r.velocity);return w(w({},r),{},{from:n,velocity:i})}return r},t);return r<1?m((e,t)=>O(t)?w(w({},t),{},{velocity:x(t.velocity,n[e].velocity,r),from:x(t.from,n[e].from,r)}):t,t):j(e,n,r-1)};let P=(e,t,r,n,i,a)=>{var o=g(e,t);return!0===r.isStepper?function(e,t,r,n,i,a){var o,l=n.reduce((r,n)=>w(w({},r),{},{[n]:{from:e[n],velocity:0,to:t[n]}}),{}),u=()=>m((e,t)=>t.from,l),c=()=>!Object.values(l).filter(O).length,s=null,f=n=>{o||(o=n);var h=(n-o)/r.dt;l=j(r,l,h),i(w(w(w({},e),t),u())),o=n,c()||(s=a.setTimeout(f))};return()=>(s=a.setTimeout(f),()=>{s()})}(e,t,r,o,i,a):function(e,t,r,n,i,a,o){var l,u=null,c=i.reduce((r,n)=>w(w({},r),{},{[n]:[e[n],t[n]]}),{}),s=i=>{l||(l=i);var f=(i-l)/n,h=m((e,t)=>x(...t,r(f)),c);if(a(w(w(w({},e),t),h)),f<1)u=o.setTimeout(s);else{var p=m((e,t)=>x(...t,r(1)),c);a(w(w(w({},e),t),p))}};return()=>(u=o.setTimeout(s),()=>{u()})}(e,t,r,n,o,i,a)};class A{setTimeout(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=performance.now(),n=null,i=a=>{a-r>=t?e(a):"function"==typeof requestAnimationFrame&&(n=requestAnimationFrame(i))};return n=requestAnimationFrame(i),()=>{cancelAnimationFrame(n)}}}var E=["children","begin","duration","attributeName","easing","isActive","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart","animationManager"];function M(){return(M=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function S(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function _(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?S(Object(r),!0).forEach(function(t){k(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function k(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class T extends n.PureComponent{componentDidMount(){var{isActive:e,canBegin:t}=this.props;this.mounted=!0,e&&t&&this.runAnimation(this.props)}componentDidUpdate(e){var{isActive:t,canBegin:r,attributeName:n,shouldReAnimate:i,to:o,from:l}=this.props,{style:u}=this.state;if(r){if(!t){this.state&&u&&(n&&u[n]!==o||!n&&u!==o)&&this.setState({style:n?{[n]:o}:o});return}if(!a()(e.to,o)||!e.canBegin||!e.isActive){var c=!e.canBegin||!e.isActive;this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var s=c||i?l:e.to;this.state&&u&&(n&&u[n]!==s||!n&&u!==s)&&this.setState({style:n?{[n]:s}:s}),this.runAnimation(_(_({},this.props),{},{from:s,begin:0}))}}}componentWillUnmount(){this.mounted=!1;var{onAnimationEnd:e}=this.props;this.unSubscribe&&this.unSubscribe(),this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation(),e&&e()}handleStyleChange(e){this.changeStyle(e)}changeStyle(e){this.mounted&&this.setState({style:e})}runJSAnimation(e){var{from:t,to:r,duration:n,easing:i,begin:a,onAnimationEnd:o,onAnimationStart:l}=e,u=P(t,r,h(i),n,this.changeStyle,this.manager.getTimeoutController());this.manager.start([l,a,()=>{this.stopJSAnimation=u()},n,o])}runAnimation(e){var{begin:t,duration:r,attributeName:n,to:i,easing:a,onAnimationStart:o,onAnimationEnd:l,children:u}=e;if(this.unSubscribe=this.manager.subscribe(this.handleStyleChange),"function"==typeof a||"function"==typeof u||"spring"===a)return void this.runJSAnimation(e);var c=n?{[n]:i}:i,s=v(Object.keys(c),r,a);this.manager.start([o,t,_(_({},c),{},{transition:s}),r,l])}render(){var e=this.props,{children:t,begin:r,duration:i,attributeName:a,easing:o,isActive:l,from:u,to:c,canBegin:s,onAnimationEnd:f,shouldReAnimate:h,onAnimationReStart:p,animationManager:d}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,E),v=n.Children.count(t),g=this.state.style;if("function"==typeof t)return t(g);if(!l||0===v||i<=0)return t;var m=e=>{var{style:t={},className:r}=e.props;return(0,n.cloneElement)(e,_(_({},y),{},{style:_(_({},t),g),className:r}))};return 1===v?m(n.Children.only(t)):n.createElement("div",null,n.Children.map(t,e=>m(e)))}constructor(e,t){super(e,t),k(this,"mounted",!1),k(this,"manager",null),k(this,"stopJSAnimation",null),k(this,"unSubscribe",null);var{isActive:r,attributeName:n,from:i,to:a,children:o,duration:l,animationManager:u}=this.props;if(this.manager=u,this.handleStyleChange=this.handleStyleChange.bind(this),this.changeStyle=this.changeStyle.bind(this),!r||l<=0){this.state={style:{}},"function"==typeof o&&(this.state={style:a});return}if(i){if("function"==typeof o){this.state={style:i};return}this.state={style:n?{[n]:i}:i}}else this.state={style:{}}}}k(T,"displayName","Animate"),k(T,"defaultProps",{begin:0,duration:1e3,attributeName:"",easing:"ease",isActive:!0,canBegin:!0,onAnimationEnd:()=>{},onAnimationStart:()=>{}});var C=(0,n.createContext)(null);function D(e){var t,r,i,a,o,l,u,c,s=(0,n.useContext)(C);return n.createElement(T,M({},e,{animationManager:null!=(u=null!=(c=e.animationManager)?c:s)?u:(t=new A,i=()=>null,a=!1,o=null,l=e=>{if(!a){if(Array.isArray(e)){if(!e.length)return;var[r,...n]=e;if("number"==typeof r){o=t.setTimeout(l.bind(null,n),r);return}l(r),o=t.setTimeout(l.bind(null,n));return}"object"==typeof e&&i(e),"function"==typeof e&&e()}},{stop:()=>{a=!0},start:e=>{a=!1,o&&(o(),o=null),l(e)},subscribe:e=>(i=e,()=>{i=()=>null}),getTimeoutController:()=>t})}))}},19483:(e,t,r)=>{"use strict";r.d(t,{m:()=>eo});var n=r(94285),i=r(24518),a=r(10014),o=r.n(a),l=r(3638),u=r(29427);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function s(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function f(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?s(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):s(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function h(e){return Array.isArray(e)&&(0,u.vh)(e[0])&&(0,u.vh)(e[1])?e.join(" ~ "):e}var p=e=>{var{separator:t=" : ",contentStyle:r={},itemStyle:i={},labelStyle:a={},payload:s,formatter:p,itemSorter:d,wrapperClassName:y,labelClassName:v,label:g,labelFormatter:m,accessibilityLayer:b=!1}=e,w=f({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},r),x=f({margin:0},a),O=!(0,u.uy)(g),j=O?g:"",P=(0,l.$)("recharts-default-tooltip",y),A=(0,l.$)("recharts-tooltip-label",v);return O&&m&&null!=s&&(j=m(g,s)),n.createElement("div",c({className:P,style:w},b?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:A,style:x},n.isValidElement(j)?j:"".concat(j)),(()=>{if(s&&s.length){var e=(d?o()(s,d):s).map((e,r)=>{if("none"===e.type)return null;var a=e.formatter||p||h,{value:o,name:l}=e,c=o,d=l;if(a){var y=a(o,l,e,r,s);if(Array.isArray(y))[c,d]=y;else{if(null==y)return null;c=y}}var v=f({display:"block",paddingTop:4,paddingBottom:4,color:e.color||"#000"},i);return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(r),style:v},(0,u.vh)(d)?n.createElement("span",{className:"recharts-tooltip-item-name"},d):null,(0,u.vh)(d)?n.createElement("span",{className:"recharts-tooltip-item-separator"},t):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},e.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},e)}return null})())},d="recharts-tooltip-wrapper",y={visibility:"hidden"};function v(e){var{allowEscapeViewBox:t,coordinate:r,key:n,offsetTopLeft:i,position:a,reverseDirection:o,tooltipDimension:l,viewBox:c,viewBoxDimension:s}=e;if(a&&(0,u.Et)(a[n]))return a[n];var f=r[n]-l-(i>0?i:0),h=r[n]+i;if(t[n])return o[n]?f:h;var p=c[n];return null==p?0:o[n]?f<p?Math.max(h,p):Math.max(f,p):null==s?0:h+l>p+s?Math.max(f,p):Math.max(h,p)}function g(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function m(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?g(Object(r),!0).forEach(function(t){b(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function b(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class w extends n.PureComponent{componentDidMount(){document.addEventListener("keydown",this.handleKeyDown)}componentWillUnmount(){document.removeEventListener("keydown",this.handleKeyDown)}componentDidUpdate(){var e,t;this.state.dismissed&&((null==(e=this.props.coordinate)?void 0:e.x)!==this.state.dismissedAtCoordinate.x||(null==(t=this.props.coordinate)?void 0:t.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}render(){var{active:e,allowEscapeViewBox:t,animationDuration:r,animationEasing:i,children:a,coordinate:o,hasPayload:c,isAnimationActive:s,offset:f,position:h,reverseDirection:p,useTranslate3d:g,viewBox:b,wrapperStyle:w,lastBoundingBox:x,innerRef:O,hasPortalFromProps:j}=this.props,{cssClasses:P,cssProperties:A}=function(e){var t,r,n,{allowEscapeViewBox:i,coordinate:a,offsetTopLeft:o,position:c,reverseDirection:s,tooltipBox:f,useTranslate3d:h,viewBox:p}=e;return{cssProperties:t=f.height>0&&f.width>0&&a?function(e){var{translateX:t,translateY:r,useTranslate3d:n}=e;return{transform:n?"translate3d(".concat(t,"px, ").concat(r,"px, 0)"):"translate(".concat(t,"px, ").concat(r,"px)")}}({translateX:r=v({allowEscapeViewBox:i,coordinate:a,key:"x",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.width,viewBox:p,viewBoxDimension:p.width}),translateY:n=v({allowEscapeViewBox:i,coordinate:a,key:"y",offsetTopLeft:o,position:c,reverseDirection:s,tooltipDimension:f.height,viewBox:p,viewBoxDimension:p.height}),useTranslate3d:h}):y,cssClasses:function(e){var{coordinate:t,translateX:r,translateY:n}=e;return(0,l.$)(d,{["".concat(d,"-right")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r>=t.x,["".concat(d,"-left")]:(0,u.Et)(r)&&t&&(0,u.Et)(t.x)&&r<t.x,["".concat(d,"-bottom")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n>=t.y,["".concat(d,"-top")]:(0,u.Et)(n)&&t&&(0,u.Et)(t.y)&&n<t.y})}({translateX:r,translateY:n,coordinate:a})}}({allowEscapeViewBox:t,coordinate:o,offsetTopLeft:f,position:h,reverseDirection:p,tooltipBox:{height:x.height,width:x.width},useTranslate3d:g,viewBox:b}),E=j?{}:m(m({transition:s&&e?"transform ".concat(r,"ms ").concat(i):void 0},A),{},{pointerEvents:"none",visibility:!this.state.dismissed&&e&&c?"visible":"hidden",position:"absolute",top:0,left:0}),M=m(m({},E),{},{visibility:!this.state.dismissed&&e&&c?"visible":"hidden"},w);return n.createElement("div",{xmlns:"http://www.w3.org/1999/xhtml",tabIndex:-1,className:P,style:M,ref:O},a)}constructor(){super(...arguments),b(this,"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0}}),b(this,"handleKeyDown",e=>{if("Escape"===e.key){var t,r,n,i;this.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(t=null==(r=this.props.coordinate)?void 0:r.x)?t:0,y:null!=(n=null==(i=this.props.coordinate)?void 0:i.y)?n:0}})}})}}var x=r(3157),O=r(93819),j=r.n(O),P=r(21584),A=r(16806),E=r(45576),M=r(71998),S=["x","y","top","left","width","height","className"];function _(){return(_=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function k(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var T=(e,t,r,n,i,a)=>"M".concat(e,",").concat(i,"v").concat(n,"M").concat(a,",").concat(t,"h").concat(r),C=e=>{var{x:t=0,y:r=0,top:i=0,left:a=0,width:o=0,height:c=0,className:s}=e,f=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?k(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({x:t,y:r,top:i,left:a,width:o,height:c},function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,S));return(0,u.Et)(t)&&(0,u.Et)(r)&&(0,u.Et)(o)&&(0,u.Et)(c)&&(0,u.Et)(i)&&(0,u.Et)(a)?n.createElement("path",_({},(0,M.J9)(f,!0),{className:(0,l.$)("recharts-cross",s),d:T(t,r,o,c,i,a)})):null},D=r(76544),N=r(15143);function I(e){var{cx:t,cy:r,radius:n,startAngle:i,endAngle:a}=e;return{points:[(0,N.IZ)(t,r,n,i),(0,N.IZ)(t,r,n,a)],cx:t,cy:r,radius:n,startAngle:i,endAngle:a}}var z=r(78369),L=r(4517),R=r(65084),B=r(52084);function U(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?U(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var K=()=>(0,L.G)(B.Dn),$=()=>{var e=K(),t=(0,L.G)(B.R4),r=(0,L.G)(B.fl);return(0,R.Hj)(F(F({},e),{},{scale:r}),t)},G=r(70474);function H(){return(H=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function Z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function W(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Z(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function q(e){var t,r,i,{coordinate:a,payload:o,index:u,offset:c,tooltipAxisBandSize:s,layout:f,cursor:h,tooltipEventType:p,chartName:d}=e;if(!h||!a||"ScatterChart"!==d&&"axis"!==p)return null;if("ScatterChart"===d)r=a,i=C;else if("BarChart"===d)t=s/2,r={stroke:"none",fill:"#ccc",x:"horizontal"===f?a.x-t:c.left+.5,y:"horizontal"===f?c.top+.5:a.y-t,width:"horizontal"===f?s:c.width-1,height:"horizontal"===f?c.height-1:s},i=D.M;else if("radial"===f){var{cx:y,cy:v,radius:g,startAngle:m,endAngle:b}=I(a);r={cx:y,cy:v,startAngle:m,endAngle:b,innerRadius:g,outerRadius:g},i=z.h}else r={points:function(e,t,r){var n,i,a,o;if("horizontal"===e)a=n=t.x,i=r.top,o=r.top+r.height;else if("vertical"===e)o=i=t.y,n=r.left,a=r.left+r.width;else if(null!=t.cx&&null!=t.cy)if("centric"!==e)return I(t);else{var{cx:l,cy:u,innerRadius:c,outerRadius:s,angle:f}=t,h=(0,N.IZ)(l,u,c,f),p=(0,N.IZ)(l,u,s,f);n=h.x,i=h.y,a=p.x,o=p.y}return[{x:n,y:i},{x:a,y:o}]}(f,a,c)},i=E.I;var w="object"==typeof h&&"className"in h?h.className:void 0,x=W(W(W(W({stroke:"#ccc",pointerEvents:"none"},c),r),(0,M.J9)(h,!1)),{},{payload:o,payloadIndex:u,className:(0,l.$)("recharts-tooltip-cursor",w)});return(0,n.isValidElement)(h)?(0,n.cloneElement)(h,x):(0,n.createElement)(i,x)}function V(e){var t=$(),r=(0,P.W7)(),i=(0,P.WX)(),a=(0,G.fW)();return n.createElement(q,H({},e,{coordinate:e.coordinate,index:e.index,payload:e.payload,offset:r,layout:i,tooltipAxisBandSize:t,chartName:a}))}var Y=r(53941),J=r(82284),X=r(90444),Q=r(91881),ee=r(13739);function et(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function er(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?et(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function en(e){return e.dataKey}var ei=[],ea={allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",axisId:0,contentStyle:{},cursor:!0,filterNull:!0,isAnimationActive:!x.m.isSsr,itemSorter:"name",itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,wrapperStyle:{}};function eo(e){var t,r,a=(0,ee.e)(e,ea),{active:o,allowEscapeViewBox:l,animationDuration:u,animationEasing:c,content:s,filterNull:f,isAnimationActive:h,offset:d,payloadUniqBy:y,position:v,reverseDirection:g,useTranslate3d:m,wrapperStyle:b,cursor:x,shared:O,trigger:E,defaultIndex:M,portal:S,axisId:_}=a,k=(0,L.j)(),T="number"==typeof M?String(M):M;(0,n.useEffect)(()=>{k((0,J.UF)({shared:O,trigger:E,axisId:_,active:o,defaultIndex:T}))},[k,O,E,_,o,T]);var C=(0,P.sk)(),D=(0,A.$)(),N=(0,Q.Td)(O),{activeIndex:I,isActive:z}=(0,L.G)(e=>(0,G.yn)(e,N,E,T)),R=(0,L.G)(e=>(0,G.u9)(e,N,E,T)),B=(0,L.G)(e=>(0,G.BZ)(e,N,E,T)),U=(0,L.G)(e=>(0,G.dS)(e,N,E,T)),F=(0,Y.X)(),K=null!=o?o:z,[$,H]=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],[t,r]=(0,n.useState)({height:0,left:0,top:0,width:0}),i=(0,n.useCallback)(e=>{if(null!=e){var n=e.getBoundingClientRect(),i={height:n.height,left:n.left,top:n.top,width:n.width};(Math.abs(i.height-t.height)>1||Math.abs(i.left-t.left)>1||Math.abs(i.top-t.top)>1||Math.abs(i.width-t.width)>1)&&r({height:i.height,left:i.left,top:i.top,width:i.width})}},[t.width,t.height,t.top,t.left,...e]);return[t,i]}([R,K]),Z="axis"===N?B:void 0;(0,X.m7)(N,E,U,Z,I,K);var W=null!=S?S:F;if(null==W)return null;var q=null!=R?R:ei;K||(q=ei),f&&q.length&&(t=R.filter(e=>null!=e.value&&(!0!==e.hide||a.includeHidden)),q=!0===y?j()(t,en):"function"==typeof y?j()(t,y):t);var et=q.length>0,eo=n.createElement(w,{allowEscapeViewBox:l,animationDuration:u,animationEasing:c,isAnimationActive:h,active:K,coordinate:U,hasPayload:et,offset:d,position:v,reverseDirection:g,useTranslate3d:m,viewBox:C,wrapperStyle:b,lastBoundingBox:$,innerRef:H,hasPortalFromProps:!!S},(r=er(er({},a),{},{payload:q,label:Z,active:K,coordinate:U,accessibilityLayer:D}),n.isValidElement(s)?n.cloneElement(s,r):"function"==typeof s?n.createElement(s,r):n.createElement(p,r)));return n.createElement(n.Fragment,null,(0,i.createPortal)(eo,W),K&&n.createElement(V,{cursor:x,tooltipEventType:N,coordinate:U,payload:R,index:I}))}},20539:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toKey=function(e){return"string"==typeof e||"symbol"==typeof e?e:Object.is(e?.valueOf?.(),-0)?"-0":String(e)}},20617:(e,t,r)=>{"use strict";r.d(t,{E:()=>_});var n=r(94285),i=r(3638),a=r(29427),o=r(3157),l=r(71998),u=r(94263),c=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,s=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,f=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,h=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,p={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},d=Object.keys(p);class y{static parse(e){var t,[,r,n]=null!=(t=h.exec(e))?t:[];return new y(parseFloat(r),null!=n?n:"")}add(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num+e.num,this.unit)}subtract(e){return this.unit!==e.unit?new y(NaN,""):new y(this.num-e.num,this.unit)}multiply(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num*e.num,this.unit||e.unit)}divide(e){return""!==this.unit&&""!==e.unit&&this.unit!==e.unit?new y(NaN,""):new y(this.num/e.num,this.unit||e.unit)}toString(){return"".concat(this.num).concat(this.unit)}isNaN(){return(0,a.M8)(this.num)}constructor(e,t){this.num=e,this.unit=t,this.num=e,this.unit=t,(0,a.M8)(e)&&(this.unit=""),""===t||f.test(t)||(this.num=NaN,this.unit=""),d.includes(t)&&(this.num=e*p[t],this.unit="px")}}function v(e){if(e.includes("NaN"))return"NaN";for(var t=e;t.includes("*")||t.includes("/");){var r,[,n,i,a]=null!=(r=c.exec(t))?r:[],o=y.parse(null!=n?n:""),l=y.parse(null!=a?a:""),u="*"===i?o.multiply(l):o.divide(l);if(u.isNaN())return"NaN";t=t.replace(c,u.toString())}for(;t.includes("+")||/.-\d+(?:\.\d+)?/.test(t);){var f,[,h,p,d]=null!=(f=s.exec(t))?f:[],v=y.parse(null!=h?h:""),g=y.parse(null!=d?d:""),m="+"===p?v.add(g):v.subtract(g);if(m.isNaN())return"NaN";t=t.replace(s,m.toString())}return t}var g=/\(([^()]*)\)/;function m(e){var t=function(e){try{var t;return t=e.replace(/\s+/g,""),t=function(e){for(var t,r=e;null!=(t=g.exec(r));){var[,n]=t;r=r.replace(g,v(n))}return r}(t),t=v(t)}catch(e){return"NaN"}}(e.slice(5,-1));return"NaN"===t?"":t}var b=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],w=["dx","dy","angle","className","breakAll"];function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=/[ \f\n\r\t\v\u2028\u2029]+/,P=e=>{var{children:t,breakAll:r,style:n}=e;try{var i=[];(0,a.uy)(t)||(i=r?t.toString().split(""):t.toString().split(j));var o=i.map(e=>({word:e,width:(0,u.P)(e,n).width})),l=r?0:(0,u.P)("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:l}}catch(e){return null}},A=(e,t,r,n,i)=>{var o,{maxLines:l,children:u,style:c,breakAll:s}=e,f=(0,a.Et)(l),h=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.reduce((e,t)=>{var{word:a,width:o}=t,l=e[e.length-1];return l&&(null==n||i||l.width+o+r<Number(n))?(l.words.push(a),l.width+=o+r):e.push({words:[a],width:o}),e},[])},p=h(t),d=e=>e.reduce((e,t)=>e.width>t.width?e:t);if(!f||i||!(p.length>l||d(p).width>Number(n)))return p;for(var y=e=>{var t=h(P({breakAll:s,style:c,children:u.slice(0,e)+"…"}).wordsWithComputedWidth);return[t.length>l||d(t).width>Number(n),t]},v=0,g=u.length-1,m=0;v<=g&&m<=u.length-1;){var b=Math.floor((v+g)/2),[w,x]=y(b-1),[O]=y(b);if(w||O||(v=b+1),w&&O&&(g=b-1),!w&&O){o=x;break}m++}return o||p},E=e=>[{words:(0,a.uy)(e)?[]:e.toString().split(j)}],M=e=>{var{width:t,scaleToFit:r,children:n,style:i,breakAll:a,maxLines:l}=e;if((t||r)&&!o.m.isSsr){var u=P({breakAll:a,children:n,style:i});if(!u)return E(n);var{wordsWithComputedWidth:c,spaceWidth:s}=u;return A({breakAll:a,children:n,maxLines:l,style:i},c,s,t,r)}return E(n)},S="#808080",_=(0,n.forwardRef)((e,t)=>{var r,{x:o=0,y:u=0,lineHeight:c="1em",capHeight:s="0.71em",scaleToFit:f=!1,textAnchor:h="start",verticalAnchor:p="end",fill:d=S}=e,y=O(e,b),v=(0,n.useMemo)(()=>M({breakAll:y.breakAll,children:y.children,maxLines:y.maxLines,scaleToFit:f,style:y.style,width:y.width}),[y.breakAll,y.children,y.maxLines,f,y.style,y.width]),{dx:g,dy:j,angle:P,className:A,breakAll:E}=y,_=O(y,w);if(!(0,a.vh)(o)||!(0,a.vh)(u))return null;var k=o+((0,a.Et)(g)?g:0),T=u+((0,a.Et)(j)?j:0);switch(p){case"start":r=m("calc(".concat(s,")"));break;case"middle":r=m("calc(".concat((v.length-1)/2," * -").concat(c," + (").concat(s," / 2))"));break;default:r=m("calc(".concat(v.length-1," * -").concat(c,")"))}var C=[];if(f){var D=v[0].width,{width:N}=y;C.push("scale(".concat((0,a.Et)(N)?N/D:1,")"))}return P&&C.push("rotate(".concat(P,", ").concat(k,", ").concat(T,")")),C.length&&(_.transform=C.join(" ")),n.createElement("text",x({},(0,l.J9)(_,!0),{ref:t,x:k,y:T,className:(0,i.$)("recharts-text",A),textAnchor:h,fill:d.includes("url")?S:d}),v.map((e,t)=>{var i=e.words.join(E?"":" ");return n.createElement("tspan",{x:k,dy:0===t?r:c,key:"".concat(i,"-").concat(t)},i)}))});_.displayName="Text"},20794:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(90657);t.isArguments=function(e){return null!==e&&"object"==typeof e&&"[object Arguments]"===n.getTag(e)}},20823:(e,t,r)=>{"use strict";r.d(t,{YF:()=>c,dj:()=>s,fP:()=>f,ky:()=>u});var n=r(68646),i=r(82284),a=r(92769),o=r(91881),l=r(75931),u=(0,n.VP)("mouseClick"),c=(0,n.Nc)();c.startListening({actionCreator:u,effect:(e,t)=>{var r=e.payload,n=(0,a.g)(t.getState(),(0,l.w)(r));(null==n?void 0:n.activeIndex)!=null&&t.dispatch((0,i.jF)({activeIndex:n.activeIndex,activeDataKey:void 0,activeCoordinate:n.activeCoordinate}))}});var s=(0,n.VP)("mouseMove"),f=(0,n.Nc)();f.startListening({actionCreator:s,effect:(e,t)=>{var r=e.payload,n=t.getState(),u=(0,o.au)(n,n.tooltip.settings.shared),c=(0,a.g)(n,(0,l.w)(r));"axis"===u&&((null==c?void 0:c.activeIndex)!=null?t.dispatch((0,i.Nt)({activeIndex:c.activeIndex,activeDataKey:void 0,activeCoordinate:c.activeCoordinate})):t.dispatch((0,i.xS)()))}})},20859:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(64172),i=r(8490);t.isArrayLikeObject=function(e){return i.isObjectLike(e)&&n.isArrayLike(e)}},21584:(e,t,r)=>{"use strict";r.d(t,{W7:()=>s,WX:()=>d,fz:()=>p,rY:()=>h,sk:()=>u,yi:()=>f}),r(94285);var n=r(4517),i=r(90355),a=r(23487),o=r(8569),l=r(36714),u=()=>{var e,t=(0,o.r)(),r=(0,n.G)(i.Ds),a=(0,n.G)(l.U),u=null==(e=(0,n.G)(l.C))?void 0:e.padding;return t&&a&&u?{width:a.width-u.left-u.right,height:a.height-u.top-u.bottom,x:u.left,y:u.top}:r},c={top:0,bottom:0,left:0,right:0,width:0,height:0,brushBottom:0},s=()=>{var e;return null!=(e=(0,n.G)(i.HZ))?e:c},f=()=>(0,n.G)(a.Lp),h=()=>(0,n.G)(a.A$),p=e=>e.layout.layoutType,d=()=>(0,n.G)(p)},22695:(e,t,r)=>{"use strict";r.d(t,{$7:()=>f,Ru:()=>s,uZ:()=>c});var n=r(68646),i=r(82284),a=r(52084),o=r(70474),l=r(96112),u=r(79423),c=(0,n.VP)("keyDown"),s=(0,n.VP)("focus"),f=(0,n.Nc)();f.startListening({actionCreator:c,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip,c=e.payload;if("ArrowRight"===c||"ArrowLeft"===c||"Enter"===c){var s=Number((0,u.P)(n,(0,a.n4)(r))),f=(0,a.R4)(r);if("Enter"===c){var h=(0,o.pg)(r,"axis","hover",String(n.index));t.dispatch((0,i.o4)({active:!n.active,activeIndex:n.index,activeDataKey:n.dataKey,activeCoordinate:h}));return}var p=s+("ArrowRight"===c?1:-1)*("left-to-right"===(0,l._y)(r)?1:-1);if(null!=f&&!(p>=f.length)&&!(p<0)){var d=(0,o.pg)(r,"axis","hover",String(p));t.dispatch((0,i.o4)({active:!0,activeIndex:p.toString(),activeDataKey:void 0,activeCoordinate:d}))}}}}}),f.startListening({actionCreator:s,effect:(e,t)=>{var r=t.getState();if(!1!==r.rootProps.accessibilityLayer){var{keyboardInteraction:n}=r.tooltip;if(!n.active&&null==n.index){var a=(0,o.pg)(r,"axis","hover",String("0"));t.dispatch((0,i.o4)({activeDataKey:void 0,active:!0,activeIndex:"0",activeCoordinate:a}))}}}})},22907:(e,t,r)=>{"use strict";r.d(t,{d:()=>c});var n=r(94285),i=r(69012),a=r(96481),o=r(84860),l=r(18859),u=r(79364);function c(e){let{theme:t}=(0,u.UQ)(),r=(0,l.NU)();return(0,n.useMemo)(()=>(function(e,t){let r=r=>({...t,...r,position:function(e,t){let r=e??"bottom";return({"top-start":{ltr:"top-left",rtl:"top-right"},"top-end":{ltr:"top-right",rtl:"top-left"},"bottom-start":{ltr:"bottom-left",rtl:"bottom-right"},"bottom-end":{ltr:"bottom-right",rtl:"bottom-left"}})[r]?.[t]??r}(r?.position??t?.position,e)}),n=e=>{let t=r(e),n=(0,a.q)(t);return o.Z.notify(n,t)};return n.update=(e,t)=>{o.Z.update(e,r(t))},n.promise=(e,t)=>{let r=n({...t.loading,status:"loading",duration:null});e.then(e=>n.update(r,{status:"success",duration:5e3,...(0,i.J)(t.success,e)})).catch(e=>n.update(r,{status:"error",duration:5e3,...(0,i.J)(t.error,e)}))},n.closeAll=o.Z.closeAll,n.close=o.Z.close,n.isActive=o.Z.isActive,n})(t.direction,{...r,...e}),[e,t.direction,r])}},23487:(e,t,r)=>{"use strict";r.d(t,{A$:()=>i,HK:()=>o,Lp:()=>n,et:()=>a});var n=e=>e.layout.width,i=e=>e.layout.height,a=e=>e.layout.scale,o=e=>e.layout.margin},23722:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,_:()=>s});var n=r(94285),i=r(8569),a=r(21584),o=r(4517),l=r(4792),u=()=>{};function c(e){var{legendPayload:t}=e,r=(0,o.j)(),a=(0,i.r)();return(0,n.useEffect)(()=>a?u:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,a,t]),null}function s(e){var{legendPayload:t}=e,r=(0,o.j)(),i=(0,o.G)(a.fz);return(0,n.useEffect)(()=>"centric"!==i&&"radial"!==i?u:(r((0,l.Lx)(t)),()=>{r((0,l.u3)(t))}),[r,i,t]),null}},25648:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(56232),i=r(47629),a=r(32547),o=r(89166);t.iteratee=function(e){if(null==e)return n.identity;switch(typeof e){case"function":return e;case"object":if(Array.isArray(e)&&2===e.length)return o.matchesProperty(e[0],e[1]);return a.matches(e);case"string":case"symbol":case"number":return i.property(e)}}},25680:(e,t,r)=>{"use strict";r.d(t,{r:()=>s});var n=r(94513),i=r(58714),a=r(2923),o=r(33225);let l=(0,a.R)(function(e,t){let{templateAreas:r,gap:i,rowGap:a,columnGap:l,column:u,row:c,autoFlow:s,autoRows:f,templateRows:h,autoColumns:p,templateColumns:d,...y}=e;return(0,n.jsx)(o.B.div,{ref:t,__css:{display:"grid",gridTemplateAreas:r,gridGap:i,gridRowGap:a,gridColumnGap:l,gridAutoColumns:p,gridColumn:u,gridRow:c,gridAutoFlow:s,gridAutoRows:f,gridTemplateRows:h,gridTemplateColumns:d},...y})});l.displayName="Grid";var u=r(83745),c=r(79364);let s=(0,a.R)(function(e,t){var r,a,o;let{columns:s,spacingX:f,spacingY:h,spacing:p,minChildWidth:d,...y}=e,v=(0,u.D)(),g=d?(r=d,a=v,(0,i.bk)(r,e=>{let t=(0,c.gf)("sizes",e,"number"==typeof e?`${e}px`:e)(a);return null===e?null:`repeat(auto-fit, minmax(${t}, 1fr))`})):(o=s,(0,i.bk)(o,e=>null===e?null:`repeat(${e}, minmax(0, 1fr))`));return(0,n.jsx)(l,{ref:t,gap:p,columnGap:f,rowGap:h,templateColumns:g,...y})});s.displayName="SimpleGrid"},26610:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84602);t.cloneDeep=function(e){return n.cloneDeepWithImpl(e,void 0,e,new Map,void 0)}},28482:(e,t,r)=>{"use strict";r.d(t,{F:()=>ej,L:()=>eg});var n=r(94285),i=r(56797),a=r.n(i),o=r(3638),l=r(73474),u=r(92602),c=r(90355),s=r(65084),f=r(96112),h=r(21584),p=r(83364),d=r(94953),y=r(4788),v=e=>e.graphicalItems.polarItems,g=(0,l.Mz)([p.N,d.E],f.eo),m=(0,l.Mz)([v,f.DP,g],f.ec),b=(0,l.Mz)([m],f.rj),w=(0,l.Mz)([b,u.z3],f.Nk),x=(0,l.Mz)([w,f.DP,m],f.fb),O=(0,l.Mz)([w,f.DP,m],(e,t,r)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var n;return{value:(0,s.kr)(e,null!=(n=t.dataKey)?n:r.dataKey),errorDomain:[]}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,s.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]}))),j=()=>void 0,P=(0,l.Mz)([f.DP,f.AV,j,O,j],f.wL),A=(0,l.Mz)([f.DP,h.fz,w,x,y.eC,p.N,P],f.tP),E=(0,l.Mz)([A,f.DP,f.xM],f.xp);function M(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function S(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?M(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}(0,l.Mz)([f.DP,A,E,p.N],f.g1);var _=(e,t)=>t,k=[],T=(e,t,r)=>(null==r?void 0:r.length)===0?k:r,C=(0,l.Mz)([u.z3,_,T],(e,t,r)=>{var n,{chartData:i}=e;if((n=(null==t?void 0:t.data)!=null&&t.data.length>0?t.data:i)&&n.length||null==r||(n=r.map(e=>S(S({},t.presentationProps),e.props))),null!=n)return n}),D=(0,l.Mz)([C,_,T],(e,t,r)=>{if(null!=e)return e.map((e,n)=>{var i,a,o=(0,s.kr)(e,t.nameKey,t.name);return a=null!=r&&null!=(i=r[n])&&null!=(i=i.props)&&i.fill?r[n].props.fill:"object"==typeof e&&null!=e&&"fill"in e?e.fill:t.fill,{value:(0,s.uM)(o,t.dataKey),color:a,payload:e,type:t.legendType}})}),N=(0,l.Mz)([v,_],(e,t)=>{if(e.some(e=>"pie"===e.type&&t.dataKey===e.dataKey&&t.data===e.data))return t}),I=(0,l.Mz)([C,N,T,c.HZ],(e,t,r,n)=>{if(null!=t&&null!=e)return eg({offset:n,pieSettings:t,displayedData:e,cells:r})}),z=r(4517),L=r(12645),R=r(53898),B=r(45576),U=r(20617),F=r(92933),K=r(71998),$=r(3157),G=r(15143),H=r(29427),Z=r(53951),W=r(1292),q=r(49039),V=r(83249),Y=r(52084),J=r(23722),X=r(49651),Q=r(70340),ee=r(13739),et=r(17951),er=["onMouseEnter","onClick","onMouseLeave"];function en(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ei(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?en(Object(r),!0).forEach(function(t){ea(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function ea(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function eo(){return(eo=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function el(e){var t=(0,n.useMemo)(()=>(0,K.J9)(e,!1),[e]),r=(0,n.useMemo)(()=>(0,K.aS)(e.children,F.f),[e.children]),i=(0,n.useMemo)(()=>({name:e.name,nameKey:e.nameKey,tooltipType:e.tooltipType,data:e.data,dataKey:e.dataKey,cx:e.cx,cy:e.cy,startAngle:e.startAngle,endAngle:e.endAngle,minAngle:e.minAngle,paddingAngle:e.paddingAngle,innerRadius:e.innerRadius,outerRadius:e.outerRadius,cornerRadius:e.cornerRadius,legendType:e.legendType,fill:e.fill,presentationProps:t}),[e.cornerRadius,e.cx,e.cy,e.data,e.dataKey,e.endAngle,e.innerRadius,e.minAngle,e.name,e.nameKey,e.outerRadius,e.paddingAngle,e.startAngle,e.tooltipType,e.legendType,e.fill,t]),a=(0,z.G)(e=>D(e,i,r));return n.createElement(J._,{legendPayload:a})}function eu(e){var{dataKey:t,nameKey:r,sectors:n,stroke:i,strokeWidth:a,fill:o,name:l,hide:u,tooltipType:c}=e;return{dataDefinedOnItem:null==n?void 0:n.map(e=>e.tooltipPayload),positions:null==n?void 0:n.map(e=>e.tooltipPosition),settings:{stroke:i,strokeWidth:a,fill:o,dataKey:t,nameKey:r,name:(0,s.uM)(l,t),hide:u,type:c,color:o,unit:""}}}var ec=(e,t)=>e>t?"start":e<t?"end":"middle",es=(e,t,r)=>"function"==typeof t?t(e):(0,H.F4)(t,r,.8*r),ef=(e,t,r)=>{var{top:n,left:i,width:a,height:o}=t,l=(0,G.lY)(a,o),u=i+(0,H.F4)(e.cx,a,a/2),c=n+(0,H.F4)(e.cy,o,o/2),s=(0,H.F4)(e.innerRadius,l,0);return{cx:u,cy:c,innerRadius:s,outerRadius:es(r,e.outerRadius,l),maxRadius:e.maxRadius||Math.sqrt(a*a+o*o)/2}},eh=(e,t)=>(0,H.sA)(t-e)*Math.min(Math.abs(t-e),360),ep=(e,t)=>{if(n.isValidElement(e))return n.cloneElement(e,t);if("function"==typeof e)return e(t);var r=(0,o.$)("recharts-pie-label-line","boolean"!=typeof e?e.className:"");return n.createElement(B.I,eo({},t,{type:"linear",className:r}))},ed=(e,t,r)=>{if(n.isValidElement(e))return n.cloneElement(e,t);var i=r;if("function"==typeof e&&(i=e(t),n.isValidElement(i)))return i;var a=(0,o.$)("recharts-pie-label-text","boolean"!=typeof e&&"function"!=typeof e?e.className:"");return n.createElement(U.E,eo({},t,{alignmentBaseline:"middle",className:a}),i)};function ey(e){var{sectors:t,props:r,showLabels:i}=e,{label:a,labelLine:o,dataKey:l}=r;if(!i||!a||!t)return null;var u=(0,K.J9)(r,!1),c=(0,K.J9)(a,!1),f=(0,K.J9)(o,!1),h="object"==typeof a&&"offsetRadius"in a&&a.offsetRadius||20,p=t.map((e,t)=>{var r=(e.startAngle+e.endAngle)/2,i=(0,G.IZ)(e.cx,e.cy,e.outerRadius+h,r),p=ei(ei(ei(ei({},u),e),{},{stroke:"none"},c),{},{index:t,textAnchor:ec(i.x,e.cx)},i),d=ei(ei(ei(ei({},u),e),{},{fill:"none",stroke:e.fill},f),{},{index:t,points:[(0,G.IZ)(e.cx,e.cy,e.outerRadius,r),i],key:"line"});return n.createElement(R.W,{key:"label-".concat(e.startAngle,"-").concat(e.endAngle,"-").concat(e.midAngle,"-").concat(t)},o&&ep(o,d),ed(a,p,(0,s.kr)(e,l)))});return n.createElement(R.W,{className:"recharts-pie-labels"},p)}function ev(e){var{sectors:t,activeShape:r,inactiveShape:i,allOtherPieProps:a,showLabels:o}=e,l=(0,z.G)(Y.A2),{onMouseEnter:u,onClick:c,onMouseLeave:s}=a,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(a,er),h=(0,q.Cj)(u,a.dataKey),p=(0,q.Pg)(s),d=(0,q.Ub)(c,a.dataKey);return null==t?null:n.createElement(n.Fragment,null,t.map((e,o)=>{if((null==e?void 0:e.startAngle)===0&&(null==e?void 0:e.endAngle)===0&&1!==t.length)return null;var u=r&&String(o)===l,c=u?r:l?i:null,s=ei(ei({},e),{},{stroke:e.stroke,tabIndex:-1,[X.F0]:o,[X.um]:a.dataKey});return n.createElement(R.W,eo({tabIndex:-1,className:"recharts-pie-sector"},(0,Z.XC)(f,e,o),{onMouseEnter:h(e,o),onMouseLeave:p(e,o),onClick:d(e,o),key:"sector-".concat(null==e?void 0:e.startAngle,"-").concat(null==e?void 0:e.endAngle,"-").concat(e.midAngle,"-").concat(o)}),n.createElement(W.y,eo({option:c,isActive:u,shapeType:"sector"},s)))}),n.createElement(ey,{sectors:t,props:a,showLabels:o}))}function eg(e){var t,r,n,{pieSettings:i,displayedData:a,cells:o,offset:l}=e,{cornerRadius:u,startAngle:c,endAngle:f,dataKey:h,nameKey:p,tooltipType:d}=i,y=Math.abs(i.minAngle),v=eh(c,f),g=Math.abs(v),m=a.length<=1?0:null!=(t=i.paddingAngle)?t:0,b=a.filter(e=>0!==(0,s.kr)(e,h,0)).length,w=g-b*y-(g>=360?b:b-1)*m,x=a.reduce((e,t)=>{var r=(0,s.kr)(t,h,0);return e+((0,H.Et)(r)?r:0)},0);return x>0&&(r=a.map((e,t)=>{var r,a=(0,s.kr)(e,h,0),f=(0,s.kr)(e,p,t),g=ef(i,l,e),b=((0,H.Et)(a)?a:0)/x,O=ei(ei({},e),o&&o[t]&&o[t].props),j=(r=t?n.endAngle+(0,H.sA)(v)*m*(0!==a):c)+(0,H.sA)(v)*((0!==a?y:0)+b*w),P=(r+j)/2,A=(g.innerRadius+g.outerRadius)/2,E=[{name:f,value:a,payload:O,dataKey:h,type:d}],M=(0,G.IZ)(g.cx,g.cy,A,P);return n=ei(ei(ei(ei({},i.presentationProps),{},{percent:b,cornerRadius:u,name:f,tooltipPayload:E,midAngle:P,middleRadius:A,tooltipPosition:M},O),g),{},{value:(0,s.kr)(e,h),startAngle:r,endAngle:j,payload:O,paddingAngle:(0,H.sA)(v)*m})})),r}function em(e){var{props:t,previousSectorsRef:r}=e,{sectors:i,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c,activeShape:s,inactiveShape:f,onAnimationStart:h,onAnimationEnd:p}=t,d=(0,Q.n)(t,"recharts-pie-"),y=r.current,[v,g]=(0,n.useState)(!0),m=(0,n.useCallback)(()=>{"function"==typeof p&&p(),g(!1)},[p]),b=(0,n.useCallback)(()=>{"function"==typeof h&&h(),g(!0)},[h]);return n.createElement(et.i,{begin:l,duration:u,isActive:o,easing:c,from:{t:0},to:{t:1},onAnimationStart:b,onAnimationEnd:m,key:d},e=>{var{t:o}=e,l=[],u=(i&&i[0]).startAngle;return i.forEach((e,t)=>{var r=y&&y[t],n=t>0?a()(e,"paddingAngle",0):0;if(r){var i=(0,H.Dj)(r.endAngle-r.startAngle,e.endAngle-e.startAngle),c=ei(ei({},e),{},{startAngle:u+n,endAngle:u+i(o)+n});l.push(c),u=c.endAngle}else{var{endAngle:s,startAngle:f}=e,h=(0,H.Dj)(0,s-f)(o),p=ei(ei({},e),{},{startAngle:u+n,endAngle:u+h+n});l.push(p),u=p.endAngle}}),r.current=l,n.createElement(R.W,null,n.createElement(ev,{sectors:l,activeShape:s,inactiveShape:f,allOtherPieProps:t,showLabels:!v}))})}function eb(e){var{sectors:t,isAnimationActive:r,activeShape:i,inactiveShape:a}=e,o=(0,n.useRef)(null),l=o.current;return r&&t&&t.length&&(!l||l!==t)?n.createElement(em,{props:e,previousSectorsRef:o}):n.createElement(ev,{sectors:t,activeShape:i,inactiveShape:a,allOtherPieProps:e,showLabels:!0})}function ew(e){var{hide:t,className:r,rootTabIndex:i}=e,a=(0,o.$)("recharts-pie",r);return t?null:n.createElement(R.W,{tabIndex:i,className:a},n.createElement(eb,e))}var ex={animationBegin:400,animationDuration:1500,animationEasing:"ease",cx:"50%",cy:"50%",dataKey:"value",endAngle:360,fill:"#808080",hide:!1,innerRadius:0,isAnimationActive:!$.m.isSsr,labelLine:!0,legendType:"rect",minAngle:0,nameKey:"name",outerRadius:"80%",paddingAngle:0,rootTabIndex:0,startAngle:0,stroke:"#fff"};function eO(e){var t=(0,ee.e)(e,ex),r=(0,n.useMemo)(()=>(0,K.aS)(e.children,F.f),[e.children]),i=(0,K.J9)(t,!1),a=(0,n.useMemo)(()=>({name:t.name,nameKey:t.nameKey,tooltipType:t.tooltipType,data:t.data,dataKey:t.dataKey,cx:t.cx,cy:t.cy,startAngle:t.startAngle,endAngle:t.endAngle,minAngle:t.minAngle,paddingAngle:t.paddingAngle,innerRadius:t.innerRadius,outerRadius:t.outerRadius,cornerRadius:t.cornerRadius,legendType:t.legendType,fill:t.fill,presentationProps:i}),[t.cornerRadius,t.cx,t.cy,t.data,t.dataKey,t.endAngle,t.innerRadius,t.minAngle,t.name,t.nameKey,t.outerRadius,t.paddingAngle,t.startAngle,t.tooltipType,t.legendType,t.fill,i]),o=(0,z.G)(e=>I(e,a,r));return n.createElement(n.Fragment,null,n.createElement(V.r,{fn:eu,args:ei(ei({},t),{},{sectors:o})}),n.createElement(ew,eo({},t,{sectors:o})))}class ej extends n.PureComponent{render(){return n.createElement(n.Fragment,null,n.createElement(L.v,{data:this.props.data,dataKey:this.props.dataKey,hide:this.props.hide,angleAxisId:0,radiusAxisId:0,stackId:void 0,barSize:void 0,type:"pie"}),n.createElement(el,this.props),n.createElement(eO,this.props),this.props.children)}constructor(){super(...arguments),ea(this,"id",(0,H.NF)("recharts-pie-"))}}ea(ej,"displayName","Pie"),ea(ej,"defaultProps",ex)},29427:(e,t,r)=>{"use strict";r.d(t,{CG:()=>p,Dj:()=>d,Et:()=>u,F4:()=>h,M8:()=>o,NF:()=>f,Zb:()=>g,_3:()=>l,eP:()=>y,sA:()=>a,uy:()=>v,vh:()=>c});var n=r(56797),i=r.n(n),a=e=>0===e?0:e>0?1:-1,o=e=>"number"==typeof e&&e!=+e,l=e=>"string"==typeof e&&e.indexOf("%")===e.length-1,u=e=>("number"==typeof e||e instanceof Number)&&!o(e),c=e=>u(e)||"string"==typeof e,s=0,f=e=>{var t=++s;return"".concat(e||"").concat(t)},h=function(e,t){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!u(e)&&"string"!=typeof e)return n;if(l(e)){if(null==t)return n;var a=e.indexOf("%");r=t*parseFloat(e.slice(0,a))/100}else r=+e;return o(r)&&(r=n),i&&null!=t&&r>t&&(r=t),r},p=e=>{if(!Array.isArray(e))return!1;for(var t=e.length,r={},n=0;n<t;n++)if(r[e[n]])return!0;else r[e[n]]=!0;return!1},d=(e,t)=>u(e)&&u(t)?r=>e+r*(t-e):()=>t;function y(e,t,r){if(e&&e.length)return e.find(e=>e&&("function"==typeof t?t(e):i()(e,t))===r)}var v=e=>null==e,g=e=>v(e)?e:"".concat(e.charAt(0).toUpperCase()).concat(e.slice(1))},29583:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(68915);t.cloneDeep=function(e){return n.cloneDeepWith(e)}},30051:(e,t,r)=>{e.exports=r(74537).throttle},31887:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.uniqBy=function(e,t){let r=new Map;for(let n=0;n<e.length;n++){let i=e[n],a=t(i);r.has(a)||r.set(a,i)}return Array.from(r.values())}},31896:(e,t,r)=>{"use strict";function n(e){return function(){return e}}r.d(t,{A:()=>n})},32449:(e,t,r)=>{"use strict";r.d(t,{x:()=>o,y:()=>a});var n=r(68646),i=r(52084),a=(0,n.VP)("externalEvent"),o=(0,n.Nc)();o.startListening({actionCreator:a,effect:(e,t)=>{if(null!=e.payload.handler){var r=t.getState(),n={activeCoordinate:(0,i.eE)(r),activeDataKey:(0,i.Xb)(r),activeIndex:(0,i.A2)(r),activeLabel:(0,i.BZ)(r),activeTooltipIndex:(0,i.A2)(r),isTooltipActive:(0,i.yn)(r)};e.payload.handler(n,e.payload.reactEvent)}}})},32547:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84895),i=r(26610);t.matches=function(e){return e=i.cloneDeep(e),t=>n.isMatch(t,e)}},34144:(e,t,r)=>{"use strict";r.d(t,{h:()=>w});var n=r(94285),i=r(3638),a=r(41181),o=r(91648),l=r(4517),u=r(96112),c=r(90355),s=r(8569),f=e=>{var{ticks:t,label:r,labelGapWithTick:n=5,tickSize:i=0,tickMargin:a=0}=e,o=0;if(t){t.forEach(e=>{if(e){var t=e.getBoundingClientRect();t.width>o&&(o=t.width)}});var l=r?r.getBoundingClientRect().width:0;return Math.round(o+(i+a)+l+(r?n:0))}return 0},h=r(64865),p=["dangerouslySetInnerHTML","ticks"];function d(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function v(e){var t=(0,l.j)();return(0,n.useEffect)(()=>(t((0,o.cU)(e)),()=>{t((0,o.fR)(e))}),[e,t]),null}var g=e=>{var t,{yAxisId:r,className:d,width:v,label:g}=e,m=(0,n.useRef)(null),b=(0,n.useRef)(null),w=(0,l.G)(c.c2),x=(0,s.r)(),O=(0,l.j)(),j="yAxis",P=(0,l.G)(e=>(0,u.iV)(e,j,r,x)),A=(0,l.G)(e=>(0,u.wP)(e,r)),E=(0,l.G)(e=>(0,u.KR)(e,r)),M=(0,l.G)(e=>(0,u.Zi)(e,j,r,x));if((0,n.useLayoutEffect)(()=>{if(!("auto"!==v||!A||(0,h.Z)(g)||(0,n.isValidElement)(g))){var e,t=m.current,i=null==t||null==(e=t.tickRefs)?void 0:e.current,{tickSize:a,tickMargin:l}=t.props,u=f({ticks:i,label:b.current,labelGapWithTick:5,tickSize:a,tickMargin:l});Math.round(A.width)!==Math.round(u)&&O((0,o.QG)({id:r,width:u}))}},[m,null==m||null==(t=m.current)||null==(t=t.tickRefs)?void 0:t.current,null==A?void 0:A.width,A,O,g,r,v]),null==A||null==E)return null;var{dangerouslySetInnerHTML:S,ticks:_}=e,k=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,p);return n.createElement(a.u,y({},k,{ref:m,labelRef:b,scale:P,x:E.x,y:E.y,width:A.width,height:A.height,className:(0,i.$)("recharts-".concat(j," ").concat(j),d),viewBox:w,ticks:M}))},m=e=>{var t,r,i,a,o;return n.createElement(n.Fragment,null,n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.yAxisId,scale:e.scale,type:e.type,domain:e.domain,allowDataOverflow:e.allowDataOverflow,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,padding:e.padding,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,width:e.width,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter}),n.createElement(g,e))},b={allowDataOverflow:u.cd.allowDataOverflow,allowDecimals:u.cd.allowDecimals,allowDuplicatedCategory:u.cd.allowDuplicatedCategory,hide:!1,mirror:u.cd.mirror,orientation:u.cd.orientation,padding:u.cd.padding,reversed:u.cd.reversed,scale:u.cd.scale,tickCount:u.cd.tickCount,type:u.cd.type,width:u.cd.width,yAxisId:0};class w extends n.Component{render(){return n.createElement(m,this.props)}}d(w,"displayName","YAxis"),d(w,"defaultProps",b)},35556:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getSymbols=function(e){return Object.getOwnPropertySymbols(e).filter(t=>Object.prototype.propertyIsEnumerable.call(e,t))}},36077:(e,t,r)=>{"use strict";r.d(t,{y:()=>eq,L:()=>eW});var n=r(94285),i=r(3638),a=r(53898),o=r(71998),l=r(12645),u=r(8569),c=["children"],s=()=>{},f=(0,n.createContext)({addErrorBar:s,removeErrorBar:s}),h=(0,n.createContext)({data:[],xAxisId:"xAxis-0",yAxisId:"yAxis-0",dataPointFormatter:()=>({x:0,y:0,value:0}),errorBarOffset:0});function p(e){var{children:t}=e,r=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c);return n.createElement(h.Provider,{value:r},t)}var d=()=>(0,n.useContext)(h),y=e=>{var{children:t,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,data:c,stackId:s,hide:h,type:p,barSize:d}=e,[y,v]=n.useState([]),g=(0,n.useCallback)(e=>{v(t=>[...t,e])},[v]),m=(0,n.useCallback)(e=>{v(t=>t.filter(t=>t!==e))},[v]),b=(0,u.r)();return n.createElement(f.Provider,{value:{addErrorBar:g,removeErrorBar:m}},n.createElement(l.p,{type:p,data:c,xAxisId:r,yAxisId:i,zAxisId:a,dataKey:o,errorBars:y,stackId:s,hide:h,barSize:d,isPanorama:b}),t)};function v(e){var{addErrorBar:t,removeErrorBar:r}=(0,n.useContext)(f);return(0,n.useEffect)(()=>(t(e),()=>{r(e)}),[t,r,e]),null}var g=r(83844),m=r(13739),b=r(17951),w=["direction","width","dataKey","isAnimationActive","animationBegin","animationDuration","animationEasing"];function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(){return(O=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function j(e){var{direction:t,width:r,dataKey:i,isAnimationActive:l,animationBegin:u,animationDuration:c,animationEasing:s}=e,f=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,w),h=(0,o.J9)(f,!1),{data:p,dataPointFormatter:y,xAxisId:v,yAxisId:m,errorBarOffset:x}=d(),j=(0,g.ZI)(v),P=(0,g.gi)(m);if((null==j?void 0:j.scale)==null||(null==P?void 0:P.scale)==null||null==p||"x"===t&&"number"!==j.type)return null;var A=p.map(e=>{var o,f,{x:p,y:d,value:v,errorVal:g}=y(e,i,t);if(!g)return null;var m=[];if(Array.isArray(g)?[o,f]=g:o=f=g,"x"===t){var{scale:w}=j,A=d+x,E=A+r,M=A-r,S=w(v-o),_=w(v+f);m.push({x1:_,y1:E,x2:_,y2:M}),m.push({x1:S,y1:A,x2:_,y2:A}),m.push({x1:S,y1:E,x2:S,y2:M})}else if("y"===t){var{scale:k}=P,T=p+x,C=T-r,D=T+r,N=k(v-o),I=k(v+f);m.push({x1:C,y1:I,x2:D,y2:I}),m.push({x1:T,y1:N,x2:T,y2:I}),m.push({x1:C,y1:N,x2:D,y2:N})}var z="".concat(p+x,"px ").concat(d+x,"px");return n.createElement(a.W,O({className:"recharts-errorBar",key:"bar-".concat(m.map(e=>"".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2)))},h),m.map(e=>{var t=l?{transformOrigin:"".concat(e.x1-5,"px")}:void 0;return n.createElement(b.i,{from:{transform:"scaleY(0)",transformOrigin:z},to:{transform:"scaleY(1)",transformOrigin:z},begin:u,easing:s,isActive:l,duration:c,key:"line-".concat(e.x1,"-").concat(e.x2,"-").concat(e.y1,"-").concat(e.y2),style:{transformOrigin:z}},n.createElement("line",O({},e,{style:t})))}))});return n.createElement(a.W,{className:"recharts-errorBars"},A)}var P=(0,n.createContext)(void 0);function A(e){var{direction:t,children:r}=e;return n.createElement(P.Provider,{value:t},r)}var E={stroke:"black",strokeWidth:1.5,width:5,offset:0,isAnimationActive:!0,animationBegin:0,animationDuration:400,animationEasing:"ease-in-out"};function M(e){var t,r,i=(t=e.direction,r=(0,n.useContext)(P),null!=t?t:null!=r?r:"x"),{width:a,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c}=(0,m.e)(e,E);return n.createElement(n.Fragment,null,n.createElement(v,{dataKey:e.dataKey,direction:i}),n.createElement(j,O({},e,{direction:i,width:a,isAnimationActive:o,animationBegin:l,animationDuration:u,animationEasing:c})))}class S extends n.Component{render(){return n.createElement(M,this.props)}}x(S,"defaultProps",E),x(S,"displayName","ErrorBar");var _=r(92933),k=r(38991),T=r.n(k),C=r(64865),D=r(65084),N=r(29427),I=["valueAccessor"],z=["data","dataKey","clockWise","id","textBreakAll"];function L(){return(L=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function R(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function B(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?R(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):R(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function U(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var F=e=>Array.isArray(e.value)?T()(e.value):e.value;function K(e){var{valueAccessor:t=F}=e,r=U(e,I),{data:i,dataKey:l,clockWise:u,id:c,textBreakAll:s}=r,f=U(r,z);return i&&i.length?n.createElement(a.W,{className:"recharts-label-list"},i.map((e,r)=>{var i=(0,N.uy)(l)?t(e,r):(0,D.kr)(e&&e.payload,l),a=(0,N.uy)(c)?{}:{id:"".concat(c,"-").concat(r)};return n.createElement(C.J,L({},(0,o.J9)(e,!0),f,a,{parentViewBox:e.parentViewBox,value:i,textBreakAll:s,viewBox:C.J.parseViewBox((0,N.uy)(u)?e:B(B({},e),{},{clockWise:u})),key:"label-".concat(r),index:r}))})):null}K.displayName="LabelList",K.renderCallByParent=function(e,t){var r,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&i&&!e.label)return null;var{children:a}=e,l=(0,o.aS)(a,K).map((e,r)=>(0,n.cloneElement)(e,{data:t,key:"labelList-".concat(r)}));return i?[(r=e.label,r?!0===r?n.createElement(K,{key:"labelList-implicit",data:t}):n.isValidElement(r)||(0,C.Z)(r)?n.createElement(K,{key:"labelList-implicit",data:t,content:r}):"object"==typeof r?n.createElement(K,L({data:t},r,{key:"labelList-implicit"})):null:null),...l]:l};var $=r(3157),G=r(53951),H=r(1292),Z=["x","y"];function W(){return(W=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function q(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function V(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?q(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):q(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function Y(e,t){var{x:r,y:n}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,Z),a=parseInt("".concat(r),10),o=parseInt("".concat(n),10),l=parseInt("".concat(t.height||i.height),10),u=parseInt("".concat(t.width||i.width),10);return V(V(V(V(V({},t),i),a?{x:a}:{}),o?{y:o}:{}),{},{height:l,width:u,name:t.name,radius:t.radius})}function J(e){return n.createElement(H.y,W({shapeType:"rectangle",propTransformer:Y,activeClassName:"recharts-active-bar"},e))}var X=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(r,n)=>{if((0,N.Et)(e))return e;var i=(0,N.Et)(r)||(0,N.uy)(r);return i?e(r,n):(i||function(e,t){if(!e)throw Error("Invariant failed")}(!1),t)}},Q=r(49039),ee=r(83249),et=r(4517),er=r(83746),en=()=>{var e=(0,et.j)();return(0,n.useEffect)(()=>(e((0,er.lm)()),()=>{e((0,er.Ch)())})),null},ei=r(96112);function ea(e,t){var r,n,i=(0,et.G)(t=>(0,ei.Rl)(t,e)),a=(0,et.G)(e=>(0,ei.sf)(e,t)),o=null!=(r=null==i?void 0:i.allowDataOverflow)?r:ei.PU.allowDataOverflow,l=null!=(n=null==a?void 0:a.allowDataOverflow)?n:ei.cd.allowDataOverflow;return{needClip:o||l,needClipX:o,needClipY:l}}function eo(e){var{xAxisId:t,yAxisId:r,clipPathId:i}=e,a=(0,g.oM)(),{needClipX:o,needClipY:l,needClip:u}=ea(t,r);if(!u)return null;var{x:c,y:s,width:f,height:h}=a;return n.createElement("clipPath",{id:"clipPath-".concat(i)},n.createElement("rect",{x:o?c:c-f/2,y:l?s:s-h/2,width:o?f:2*f,height:l?h:2*h}))}var el=r(21584),eu=r(73474),ec=r(92602),es=r(90355),ef=r(4788),eh=r(38034);function ep(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function ed(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ep(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ep(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ey=(e,t,r,n,i)=>i,ev=(e,t,r)=>{var n=null!=r?r:e;if(!(0,N.uy)(n))return(0,N.F4)(n,t,0)},eg=(0,eu.Mz)([el.fz,ei.ld,(e,t)=>t,(e,t,r)=>r,(e,t,r,n)=>n],(e,t,r,n,i)=>t.filter(t=>"horizontal"===e?t.xAxisId===r:t.yAxisId===n).filter(e=>e.isPanorama===i).filter(e=>!1===e.hide).filter(e=>"bar"===e.type));function em(e){return null!=e.stackId&&null!=e.dataKey}var eb=(0,eu.Mz)([eg,ef.x3,(e,t,r)=>"horizontal"===(0,el.fz)(e)?(0,ei.BQ)(e,"xAxis",t):(0,ei.BQ)(e,"yAxis",r)],(e,t,r)=>{var n=e.filter(em),i=e.filter(e=>null==e.stackId);return[...Object.entries(n.reduce((e,t)=>(e[t.stackId]||(e[t.stackId]=[]),e[t.stackId].push(t),e),{})).map(e=>{var[n,i]=e;return{stackId:n,dataKeys:i.map(e=>e.dataKey),barSize:ev(t,r,i[0].barSize)}}),...i.map(e=>({stackId:void 0,dataKeys:[e.dataKey].filter(e=>null!=e),barSize:ev(t,r,e.barSize)}))]}),ew=(e,t,r,n)=>{var i,a;return"horizontal"===(0,el.fz)(e)?(i=(0,ei.Gx)(e,"xAxis",t,n),a=(0,ei.CR)(e,"xAxis",t,n)):(i=(0,ei.Gx)(e,"yAxis",r,n),a=(0,ei.CR)(e,"yAxis",r,n)),(0,D.Hj)(i,a)},ex=(0,eu.Mz)([eb,ef.JN,ef._5,ef.gY,(e,t,r,n,i)=>{var a,o,l,u,c=(0,el.fz)(e),s=(0,ef.JN)(e),{maxBarSize:f}=i,h=(0,N.uy)(f)?s:f;return"horizontal"===c?(l=(0,ei.Gx)(e,"xAxis",t,n),u=(0,ei.CR)(e,"xAxis",t,n)):(l=(0,ei.Gx)(e,"yAxis",r,n),u=(0,ei.CR)(e,"yAxis",r,n)),null!=(a=null!=(o=(0,D.Hj)(l,u,!0))?o:h)?a:0},ew,(e,t,r,n,i)=>i.maxBarSize],(e,t,r,n,i,a,o)=>{var l=function(e,t,r,n,i){var a,o=n.length;if(!(o<1)){var l=(0,N.F4)(e,r,0,!0),u=[];if((0,eh.H)(n[0].barSize)){var c=!1,s=r/o,f=n.reduce((e,t)=>e+(t.barSize||0),0);(f+=(o-1)*l)>=r&&(f-=(o-1)*l,l=0),f>=r&&s>0&&(c=!0,s*=.9,f=o*s);var h={offset:((r-f)/2|0)-l,size:0};a=n.reduce((e,t)=>{var r,n=[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:h.offset+h.size+l,size:c?s:null!=(r=t.barSize)?r:0}}];return h=n[n.length-1].position,n},u)}else{var p=(0,N.F4)(t,r,0,!0);r-2*p-(o-1)*l<=0&&(l=0);var d=(r-2*p-(o-1)*l)/o;d>1&&(d>>=0);var y=(0,eh.H)(i)?Math.min(d,i):d;a=n.reduce((e,t,r)=>[...e,{stackId:t.stackId,dataKeys:t.dataKeys,position:{offset:p+(d+l)*r+(d-y)/2,size:y}}],u)}return a}}(r,n,i!==a?i:a,e,(0,N.uy)(o)?t:o);return i!==a&&null!=l&&(l=l.map(e=>ed(ed({},e),{},{position:ed(ed({},e.position),{},{offset:e.position.offset-i/2})}))),l}),eO=(0,eu.Mz)([ex,ey],(e,t)=>{if(null!=e){var r=e.find(e=>e.stackId===t.stackId&&e.dataKeys.includes(t.dataKey));if(null!=r)return r.position}}),ej=(0,eu.Mz)([ei.ld,ey],(e,t)=>{if(e.some(e=>"bar"===e.type&&t.dataKey===e.dataKey&&t.stackId===e.stackId&&t.stackId===e.stackId))return t}),eP=(0,eu.Mz)([(e,t,r,n)=>"horizontal"===(0,el.fz)(e)?(0,ei.TC)(e,"yAxis",r,n):(0,ei.TC)(e,"xAxis",t,n),ey],(e,t)=>{if(!e||(null==t?void 0:t.dataKey)==null)return;var{stackId:r}=t;if(null!=r){var n=e[r];if(n){var{stackedData:i}=n;if(i)return i.find(e=>e.key===t.dataKey)}}}),eA=(0,eu.Mz)([es.HZ,(e,t,r,n)=>(0,ei.Gx)(e,"xAxis",t,n),(e,t,r,n)=>(0,ei.Gx)(e,"yAxis",r,n),(e,t,r,n)=>(0,ei.CR)(e,"xAxis",t,n),(e,t,r,n)=>(0,ei.CR)(e,"yAxis",r,n),eO,el.fz,ec.HS,ew,eP,ej,(e,t,r,n,i,a)=>a],(e,t,r,n,i,a,o,l,u,c,s,f)=>{var h,{chartData:p,dataStartIndex:d,dataEndIndex:y}=l;if(null!=s&&null!=a&&("horizontal"===o||"vertical"===o)&&null!=t&&null!=r&&null!=n&&null!=i&&null!=u){var{data:v}=s;if(null!=(h=null!=v&&v.length>0?v:null==p?void 0:p.slice(d,y+1)))return eW({layout:o,barSettings:s,pos:a,bandSize:u,xAxis:t,yAxis:r,xAxisTicks:n,yAxisTicks:i,stackedData:c,displayedData:h,offset:e,cells:f})}}),eE=r(52084),eM=r(23722),eS=r(70340),e_=["onMouseEnter","onMouseLeave","onClick"],ek=["value","background","tooltipPosition"],eT=["onMouseEnter","onClick","onMouseLeave"];function eC(){return(eC=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function eD(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function eN(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?eD(Object(r),!0).forEach(function(t){eI(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):eD(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function eI(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function ez(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var eL=e=>{var{dataKey:t,name:r,fill:n,legendType:i,hide:a}=e;return[{inactive:a,dataKey:t,type:i,color:n,value:(0,D.uM)(r,t),payload:e}]};function eR(e){var{dataKey:t,stroke:r,strokeWidth:n,fill:i,name:a,hide:o,unit:l}=e;return{dataDefinedOnItem:void 0,positions:void 0,settings:{stroke:r,strokeWidth:n,fill:i,dataKey:t,nameKey:void 0,name:(0,D.uM)(a,t),hide:o,type:e.tooltipType,color:e.fill,unit:l}}}function eB(e){var t=(0,et.G)(eE.A2),{data:r,dataKey:i,background:a,allOtherBarProps:l}=e,{onMouseEnter:u,onMouseLeave:c,onClick:s}=l,f=ez(l,e_),h=(0,Q.Cj)(u,i),p=(0,Q.Pg)(c),d=(0,Q.Ub)(s,i);if(!a||null==r)return null;var y=(0,o.J9)(a,!1);return n.createElement(n.Fragment,null,r.map((e,r)=>{var{value:o,background:l,tooltipPosition:u}=e,c=ez(e,ek);if(!l)return null;var s=h(e,r),v=p(e,r),g=d(e,r),m=eN(eN(eN(eN(eN({option:a,isActive:String(r)===t},c),{},{fill:"#eee"},l),y),(0,G.XC)(f,e,r)),{},{onMouseEnter:s,onMouseLeave:v,onClick:g,dataKey:i,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(J,eC({key:"background-bar-".concat(r)},m))}))}function eU(e){var{data:t,props:r,showLabels:i}=e,l=(0,o.J9)(r,!1),{shape:u,dataKey:c,activeBar:s}=r,f=(0,et.G)(eE.A2),h=(0,et.G)(eE.Xb),{onMouseEnter:p,onClick:d,onMouseLeave:y}=r,v=ez(r,eT),g=(0,Q.Cj)(p,c),m=(0,Q.Pg)(y),b=(0,Q.Ub)(d,c);return t?n.createElement(n.Fragment,null,t.map((e,t)=>{var r=s&&String(t)===f&&(null==h||c===h),i=eN(eN(eN({},l),e),{},{isActive:r,option:r?s:u,index:t,dataKey:c});return n.createElement(a.W,eC({className:"recharts-bar-rectangle"},(0,G.XC)(v,e,t),{onMouseEnter:g(e,t),onMouseLeave:m(e,t),onClick:b(e,t),key:"rectangle-".concat(null==e?void 0:e.x,"-").concat(null==e?void 0:e.y,"-").concat(null==e?void 0:e.value,"-").concat(t)}),n.createElement(J,i))}),i&&K.renderCallByParent(r,t)):null}function eF(e){var{props:t,previousRectanglesRef:r}=e,{data:i,layout:o,isAnimationActive:l,animationBegin:u,animationDuration:c,animationEasing:s,onAnimationEnd:f,onAnimationStart:h}=t,p=r.current,d=(0,eS.n)(t,"recharts-bar-"),[y,v]=(0,n.useState)(!1),g=(0,n.useCallback)(()=>{"function"==typeof f&&f(),v(!1)},[f]),m=(0,n.useCallback)(()=>{"function"==typeof h&&h(),v(!0)},[h]);return n.createElement(b.i,{begin:u,duration:c,isActive:l,easing:s,from:{t:0},to:{t:1},onAnimationEnd:g,onAnimationStart:m,key:d},e=>{var{t:l}=e,u=1===l?i:i.map((e,t)=>{var r=p&&p[t];if(r){var n=(0,N.Dj)(r.x,e.x),i=(0,N.Dj)(r.y,e.y),a=(0,N.Dj)(r.width,e.width),u=(0,N.Dj)(r.height,e.height);return eN(eN({},e),{},{x:n(l),y:i(l),width:a(l),height:u(l)})}if("horizontal"===o){var c=(0,N.Dj)(0,e.height)(l);return eN(eN({},e),{},{y:e.y+e.height-c,height:c})}var s=(0,N.Dj)(0,e.width)(l);return eN(eN({},e),{},{width:s})});return l>0&&(r.current=u),n.createElement(a.W,null,n.createElement(eU,{props:t,data:u,showLabels:!y}))})}function eK(e){var{data:t,isAnimationActive:r}=e,i=(0,n.useRef)(null);return r&&t&&t.length&&(null==i.current||i.current!==t)?n.createElement(eF,{previousRectanglesRef:i,props:e}):n.createElement(eU,{props:e,data:t,showLabels:!0})}var e$=(e,t)=>{var r=Array.isArray(e.value)?e.value[1]:e.value;return{x:e.x,y:e.y,value:r,errorVal:(0,D.kr)(e,t)}};class eG extends n.PureComponent{render(){var{hide:e,data:t,dataKey:r,className:o,xAxisId:l,yAxisId:u,needClip:c,background:s,id:f,layout:h}=this.props;if(e)return null;var p=(0,i.$)("recharts-bar",o),d=(0,N.uy)(f)?this.id:f;return n.createElement(a.W,{className:p},c&&n.createElement("defs",null,n.createElement(eo,{clipPathId:d,xAxisId:l,yAxisId:u})),n.createElement(a.W,{className:"recharts-bar-rectangles",clipPath:c?"url(#clipPath-".concat(d,")"):null},n.createElement(eB,{data:t,dataKey:r,background:s,allOtherBarProps:this.props}),n.createElement(eK,this.props)),n.createElement(A,{direction:"horizontal"===h?"y":"x"},this.props.children))}constructor(){super(...arguments),eI(this,"id",(0,N.NF)("recharts-bar-"))}}var eH={activeBar:!1,animationBegin:0,animationDuration:400,animationEasing:"ease",hide:!1,isAnimationActive:!$.m.isSsr,legendType:"rect",minPointSize:0,xAxisId:0,yAxisId:0};function eZ(e){var t,{xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:d,isAnimationActive:y}=(0,m.e)(e,eH),{needClip:v}=ea(r,i),g=(0,el.WX)(),b=(0,u.r)(),w=(0,n.useMemo)(()=>({barSize:e.barSize,data:void 0,dataKey:e.dataKey,maxBarSize:e.maxBarSize,minPointSize:c,stackId:(0,D.$8)(e.stackId)}),[e.barSize,e.dataKey,e.maxBarSize,c,e.stackId]),x=(0,o.aS)(e.children,_.f),O=(0,et.G)(e=>eA(e,r,i,b,w,x));if("vertical"!==g&&"horizontal"!==g)return null;var j=null==O?void 0:O[0];return t=null==j||null==j.height||null==j.width?0:"vertical"===g?j.height/2:j.width/2,n.createElement(p,{xAxisId:r,yAxisId:i,data:O,dataPointFormatter:e$,errorBarOffset:t},n.createElement(eG,eC({},e,{layout:g,needClip:v,data:O,xAxisId:r,yAxisId:i,hide:a,legendType:l,minPointSize:c,activeBar:s,animationBegin:f,animationDuration:h,animationEasing:d,isAnimationActive:y})))}function eW(e){var{layout:t,barSettings:{dataKey:r,minPointSize:n},pos:i,bandSize:a,xAxis:o,yAxis:l,xAxisTicks:u,yAxisTicks:c,stackedData:s,displayedData:f,offset:h,cells:p}=e,d="horizontal"===t?l:o,y=s?d.scale.domain():null,v=(0,D.DW)({numericAxis:d});return f.map((e,f)=>{s?g=(0,D._f)(s[f],y):Array.isArray(g=(0,D.kr)(e,r))||(g=[v,g]);var d=X(n,0)(g[1],f);if("horizontal"===t){var g,m,b,w,x,O,j,[P,A]=[l.scale(g[0]),l.scale(g[1])];m=(0,D.y2)({axis:o,ticks:u,bandSize:a,offset:i.offset,entry:e,index:f}),b=null!=(j=null!=A?A:P)?j:void 0,w=i.size;var E=P-A;if(x=(0,N.M8)(E)?0:E,O={x:m,y:h.top,width:w,height:h.height},Math.abs(d)>0&&Math.abs(x)<Math.abs(d)){var M=(0,N.sA)(x||d)*(Math.abs(d)-Math.abs(x));b-=M,x+=M}}else{var[S,_]=[o.scale(g[0]),o.scale(g[1])];if(m=S,b=(0,D.y2)({axis:l,ticks:c,bandSize:a,offset:i.offset,entry:e,index:f}),w=_-S,x=i.size,O={x:h.left,y:b,width:h.width,height:x},Math.abs(d)>0&&Math.abs(w)<Math.abs(d)){var k=(0,N.sA)(w||d)*(Math.abs(d)-Math.abs(w));w+=k}}return eN(eN({},e),{},{x:m,y:b,width:w,height:x,value:s?g:g[1],payload:e,background:O,tooltipPosition:{x:m+w/2,y:b+x/2}},p&&p[f]&&p[f].props)})}class eq extends n.PureComponent{render(){return n.createElement(y,{type:"bar",data:null,xAxisId:this.props.xAxisId,yAxisId:this.props.yAxisId,zAxisId:0,dataKey:this.props.dataKey,stackId:this.props.stackId,hide:this.props.hide,barSize:this.props.barSize},n.createElement(en,null),n.createElement(eM.A,{legendPayload:eL(this.props)}),n.createElement(ee.r,{fn:eR,args:this.props}),n.createElement(eZ,this.props))}}eI(eq,"displayName","Bar"),eI(eq,"defaultProps",eH)},36714:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,U:()=>u});var n=r(73474),i=r(90355),a=r(23487),o=r(29427),l=e=>e.brush,u=(0,n.Mz)([l,i.HZ,a.HK],(e,t,r)=>({height:e.height,x:(0,o.Et)(e.x)?e.x:t.left,y:(0,o.Et)(e.y)?e.y:t.top+t.height+t.brushBottom-((null==r?void 0:r.bottom)||0),width:(0,o.Et)(e.width)?e.width:t.width}))},37973:(e,t)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),h=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),t.isFragment=function(e){return function(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case n:switch(e=e.type){case a:case l:case o:case h:case p:return e;default:switch(e=e&&e.$$typeof){case s:case c:case f:case y:case d:case u:return e;default:return t}}case i:return t}}}(e)===a}},38034:(e,t,r)=>{"use strict";function n(e){return Number.isFinite(e)}function i(e){return"number"==typeof e&&e>0&&Number.isFinite(e)}r.d(t,{F:()=>i,H:()=>n})},38618:(e,t,r)=>{"use strict";r.d(t,{u:()=>f});var n=r(3638),i=r(94285),a=r(30051),o=r.n(a),l=r(29427),u=r(73661);function c(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?c(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):c(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var f=(0,i.forwardRef)((e,t)=>{var{aspect:r,initialDimension:a={width:-1,height:-1},width:c="100%",height:f="100%",minWidth:h=0,minHeight:p,maxHeight:d,children:y,debounce:v=0,id:g,className:m,onResize:b,style:w={}}=e,x=(0,i.useRef)(null),O=(0,i.useRef)();O.current=b,(0,i.useImperativeHandle)(t,()=>x.current);var[j,P]=(0,i.useState)({containerWidth:a.width,containerHeight:a.height}),A=(0,i.useCallback)((e,t)=>{P(r=>{var n=Math.round(e),i=Math.round(t);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,i.useEffect)(()=>{var e=e=>{var t,{width:r,height:n}=e[0].contentRect;A(r,n),null==(t=O.current)||t.call(O,r,n)};v>0&&(e=o()(e,v,{trailing:!0,leading:!1}));var t=new ResizeObserver(e),{width:r,height:n}=x.current.getBoundingClientRect();return A(r,n),t.observe(x.current),()=>{t.disconnect()}},[A,v]);var E=(0,i.useMemo)(()=>{var{containerWidth:e,containerHeight:t}=j;if(e<0||t<0)return null;(0,u.R)((0,l._3)(c)||(0,l._3)(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,f),(0,u.R)(!r||r>0,"The aspect(%s) must be greater than zero.",r);var n=(0,l._3)(c)?e:c,a=(0,l._3)(f)?t:f;return r&&r>0&&(n?a=n/r:a&&(n=a*r),d&&a>d&&(a=d)),(0,u.R)(n>0||a>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",n,a,c,f,h,p,r),i.Children.map(y,e=>(0,i.cloneElement)(e,{width:n,height:a,style:s({width:n,height:a},e.props.style)}))},[r,y,f,d,p,h,j,c]);return i.createElement("div",{id:g?"".concat(g):void 0,className:(0,n.$)("recharts-responsive-container",m),style:s(s({},w),{},{width:c,height:f,minWidth:h,minHeight:p,maxHeight:d}),ref:x},i.createElement("div",{style:{width:0,height:0,overflow:"visible"}},E))})},38991:(e,t,r)=>{e.exports=r(4642).last},39150:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(31887),i=r(56232),a=r(20859),o=r(25648);t.uniqBy=function(e,t=i.identity){return a.isArrayLikeObject(e)?n.uniqBy(Array.from(e),o.iteratee(t)):[]}},39318:(e,t,r)=>{"use strict";r.d(t,{r:()=>x});var n=r(94285),i=r(74643),a=r(17131),o=r(97214),l=r(86054),u=r(44251),c=r(4517),s=r(6037);function f(e){var t=(0,c.j)();return(0,n.useEffect)(()=>{t((0,s.U)(e))},[t,e]),null}var h=r(62813),p=r(13739),d=r(38034),y=["width","height","layout"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={accessibilityLayer:!0,stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index",layout:"radial"},m=(0,n.forwardRef)(function(e,t){var r,i=(0,p.e)(e.categoricalChartProps,g),{width:c,height:s,layout:m}=i,b=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(i,y);if(!(0,d.F)(c)||!(0,d.F)(s))return null;var{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:j}=e;return n.createElement(a.J,{preloadedState:{options:{chartName:w,defaultTooltipEventType:x,validateTooltipEventTypes:O,tooltipPayloadSearcher:j,eventEmitter:void 0}},reduxStoreName:null!=(r=i.id)?r:w},n.createElement(o.TK,{chartData:i.data}),n.createElement(l.s,{width:c,height:s,layout:m,margin:i.margin}),n.createElement(u.p,{accessibilityLayer:i.accessibilityLayer,barCategoryGap:i.barCategoryGap,maxBarSize:i.maxBarSize,stackOffset:i.stackOffset,barGap:i.barGap,barSize:i.barSize,syncId:i.syncId,syncMethod:i.syncMethod,className:i.className}),n.createElement(f,{cx:i.cx,cy:i.cy,startAngle:i.startAngle,endAngle:i.endAngle,innerRadius:i.innerRadius,outerRadius:i.outerRadius}),n.createElement(h.L,v({width:c,height:s},b,{ref:t})))}),b=["item"],w={layout:"centric",startAngle:0,endAngle:360,cx:"50%",cy:"50%",innerRadius:0,outerRadius:"80%"},x=(0,n.forwardRef)((e,t)=>{var r=(0,p.e)(e,w);return n.createElement(m,{chartName:"PieChart",defaultTooltipEventType:"item",validateTooltipEventTypes:b,tooltipPayloadSearcher:i.uN,categoricalChartProps:r,ref:t})})},41181:(e,t,r)=>{"use strict";r.d(t,{u:()=>O});var n=r(94285),i=r(56797),a=r.n(i),o=r(3638);function l(e,t){for(var r in e)if(({}).hasOwnProperty.call(e,r)&&(!({}).hasOwnProperty.call(t,r)||e[r]!==t[r]))return!1;for(var n in t)if(({}).hasOwnProperty.call(t,n)&&!({}).hasOwnProperty.call(e,n))return!1;return!0}var u=r(53898),c=r(20617),s=r(64865),f=r(29427),h=r(53951),p=r(71998),d=r(99442),y=["viewBox"],v=["viewBox"];function g(){return(g=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function m(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function b(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?m(Object(r),!0).forEach(function(t){x(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):m(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function w(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function x(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class O extends n.Component{shouldComponentUpdate(e,t){var{viewBox:r}=e,n=w(e,y),i=this.props,{viewBox:a}=i,o=w(i,v);return!l(r,a)||!l(n,o)||!l(t,this.state)}getTickLineCoord(e){var t,r,n,i,a,o,{x:l,y:u,width:c,height:s,orientation:h,tickSize:p,mirror:d,tickMargin:y}=this.props,v=d?-1:1,g=e.tickSize||p,m=(0,f.Et)(e.tickCoord)?e.tickCoord:e.coordinate;switch(h){case"top":t=r=e.coordinate,o=(n=(i=u+!d*s)-v*g)-v*y,a=m;break;case"left":n=i=e.coordinate,a=(t=(r=l+!d*c)-v*g)-v*y,o=m;break;case"right":n=i=e.coordinate,a=(t=(r=l+d*c)+v*g)+v*y,o=m;break;default:t=r=e.coordinate,o=(n=(i=u+d*s)+v*g)+v*y,a=m}return{line:{x1:t,y1:n,x2:r,y2:i},tick:{x:a,y:o}}}getTickTextAnchor(){var e,{orientation:t,mirror:r}=this.props;switch(t){case"left":e=r?"start":"end";break;case"right":e=r?"end":"start";break;default:e="middle"}return e}getTickVerticalAnchor(){var{orientation:e,mirror:t}=this.props;switch(e){case"left":case"right":return"middle";case"top":return t?"start":"end";default:return t?"end":"start"}}renderAxisLine(){var{x:e,y:t,width:r,height:i,orientation:l,mirror:u,axisLine:c}=this.props,s=b(b(b({},(0,p.J9)(this.props,!1)),(0,p.J9)(c,!1)),{},{fill:"none"});if("top"===l||"bottom"===l){var f=+("top"===l&&!u||"bottom"===l&&u);s=b(b({},s),{},{x1:e,y1:t+f*i,x2:e+r,y2:t+f*i})}else{var h=+("left"===l&&!u||"right"===l&&u);s=b(b({},s),{},{x1:e+h*r,y1:t,x2:e+h*r,y2:t+i})}return n.createElement("line",g({},s,{className:(0,o.$)("recharts-cartesian-axis-line",a()(c,"className"))}))}static renderTickItem(e,t,r){var i,a=(0,o.$)(t.className,"recharts-cartesian-axis-tick-value");if(n.isValidElement(e))i=n.cloneElement(e,b(b({},t),{},{className:a}));else if("function"==typeof e)i=e(b(b({},t),{},{className:a}));else{var l="recharts-cartesian-axis-tick-value";"boolean"!=typeof e&&(l=(0,o.$)(l,e.className)),i=n.createElement(c.E,g({},t,{className:l}),r)}return i}renderTicks(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],{tickLine:i,stroke:l,tick:c,tickFormatter:s,unit:f}=this.props,y=(0,d.f)(b(b({},this.props),{},{ticks:r}),e,t),v=this.getTickTextAnchor(),m=this.getTickVerticalAnchor(),w=(0,p.J9)(this.props,!1),x=(0,p.J9)(c,!1),j=b(b({},w),{},{fill:"none"},(0,p.J9)(i,!1)),P=y.map((e,t)=>{var{line:r,tick:p}=this.getTickLineCoord(e),d=b(b(b(b({textAnchor:v,verticalAnchor:m},w),{},{stroke:"none",fill:l},x),p),{},{index:t,payload:e,visibleTicksCount:y.length,tickFormatter:s});return n.createElement(u.W,g({className:"recharts-cartesian-axis-tick",key:"tick-".concat(e.value,"-").concat(e.coordinate,"-").concat(e.tickCoord)},(0,h.XC)(this.props,e,t)),i&&n.createElement("line",g({},j,r,{className:(0,o.$)("recharts-cartesian-axis-tick-line",a()(i,"className"))})),c&&O.renderTickItem(c,d,"".concat("function"==typeof s?s(e.value,t):e.value).concat(f||"")))});return P.length>0?n.createElement("g",{className:"recharts-cartesian-axis-ticks"},P):null}render(){var{axisLine:e,width:t,height:r,className:i,hide:a}=this.props;if(a)return null;var{ticks:l}=this.props;return null!=t&&t<=0||null!=r&&r<=0?null:n.createElement(u.W,{className:(0,o.$)("recharts-cartesian-axis",i),ref:e=>{if(e){var t=e.getElementsByClassName("recharts-cartesian-axis-tick-value");this.tickRefs.current=Array.from(t);var r=t[0];if(r){var n=window.getComputedStyle(r).fontSize,i=window.getComputedStyle(r).letterSpacing;(n!==this.state.fontSize||i!==this.state.letterSpacing)&&this.setState({fontSize:window.getComputedStyle(r).fontSize,letterSpacing:window.getComputedStyle(r).letterSpacing})}}}},e&&this.renderAxisLine(),this.renderTicks(this.state.fontSize,this.state.letterSpacing,l),s.J.renderCallByParent(this.props))}constructor(e){super(e),this.tickRefs=n.createRef(),this.tickRefs.current=[],this.state={fontSize:"",letterSpacing:""}}}x(O,"displayName","CartesianAxis"),x(O,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"})},41460:(e,t,r)=>{"use strict";r.d(t,{d:()=>C});var n=r(94285),i=r(73661),a=r(29427),o=r(71998),l=r(65084),u=r(99442),c=r(41181),s=r(21584),f=r(96112),h=r(4517),p=r(8569),d=r(13739),y=["x1","y1","x2","y2","key"],v=["offset"],g=["xAxisId","yAxisId"],m=["xAxisId","yAxisId"];function b(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function w(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?b(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function x(){return(x=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}var j=e=>{var{fill:t}=e;if(!t||"none"===t)return null;var{fillOpacity:r,x:i,y:a,width:o,height:l,ry:u}=e;return n.createElement("rect",{x:i,y:a,ry:u,width:o,height:l,stroke:"none",fill:t,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function P(e,t){var r;if(n.isValidElement(e))r=n.cloneElement(e,t);else if("function"==typeof e)r=e(t);else{var{x1:i,y1:a,x2:l,y2:u,key:c}=t,s=O(t,y),f=(0,o.J9)(s,!1),{offset:h}=f,p=O(f,v);r=n.createElement("line",x({},p,{x1:i,y1:a,x2:l,y2:u,fill:"none",key:c}))}return r}function A(e){var{x:t,width:r,horizontal:i=!0,horizontalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,u=O(e,g),c=a.map((e,n)=>P(i,w(w({},u),{},{x1:t,y1:e,x2:t+r,y2:e,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function E(e){var{y:t,height:r,vertical:i=!0,verticalPoints:a}=e;if(!i||!a||!a.length)return null;var{xAxisId:o,yAxisId:l}=e,u=O(e,m),c=a.map((e,n)=>P(i,w(w({},u),{},{x1:e,y1:t,x2:e,y2:t+r,key:"line-".concat(n),index:n})));return n.createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function M(e){var{horizontalFill:t,fillOpacity:r,x:i,y:a,width:o,height:l,horizontalPoints:u,horizontal:c=!0}=e;if(!c||!t||!t.length)return null;var s=u.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,u)=>{var c=s[u+1]?s[u+1]-e:a+l-e;if(c<=0)return null;var f=u%t.length;return n.createElement("rect",{key:"react-".concat(u),y:e,x:i,height:c,width:o,stroke:"none",fill:t[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function S(e){var{vertical:t=!0,verticalFill:r,fillOpacity:i,x:a,y:o,width:l,height:u,verticalPoints:c}=e;if(!t||!r||!r.length)return null;var s=c.map(e=>Math.round(e+a-a)).sort((e,t)=>e-t);a!==s[0]&&s.unshift(0);var f=s.map((e,t)=>{var c=s[t+1]?s[t+1]-e:a+l-e;if(c<=0)return null;var f=t%r.length;return n.createElement("rect",{key:"react-".concat(t),x:e,y:o,width:c,height:u,stroke:"none",fill:r[f],fillOpacity:i,className:"recharts-cartesian-grid-bg"})});return n.createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var _=(e,t)=>{var{xAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,u.f)(w(w(w({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.left,a.left+a.width,t)},k=(e,t)=>{var{yAxis:r,width:n,height:i,offset:a}=e;return(0,l.PW)((0,u.f)(w(w(w({},c.u.defaultProps),r),{},{ticks:(0,l.Rh)(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),a.top,a.top+a.height,t)},T={horizontal:!0,vertical:!0,horizontalPoints:[],verticalPoints:[],stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[],xAxisId:0,yAxisId:0};function C(e){var t=(0,s.yi)(),r=(0,s.rY)(),o=(0,s.W7)(),l=w(w({},(0,d.e)(e,T)),{},{x:(0,a.Et)(e.x)?e.x:o.left,y:(0,a.Et)(e.y)?e.y:o.top,width:(0,a.Et)(e.width)?e.width:o.width,height:(0,a.Et)(e.height)?e.height:o.height}),{xAxisId:u,yAxisId:c,x:y,y:v,width:g,height:m,syncWithTicks:b,horizontalValues:O,verticalValues:P}=l,C=(0,p.r)(),D=(0,h.G)(e=>(0,f.ZB)(e,"xAxis",u,C)),N=(0,h.G)(e=>(0,f.ZB)(e,"yAxis",c,C));if(!(0,a.Et)(g)||g<=0||!(0,a.Et)(m)||m<=0||!(0,a.Et)(y)||y!==+y||!(0,a.Et)(v)||v!==+v)return null;var I=l.verticalCoordinatesGenerator||_,z=l.horizontalCoordinatesGenerator||k,{horizontalPoints:L,verticalPoints:R}=l;if((!L||!L.length)&&"function"==typeof z){var B=O&&O.length,U=z({yAxis:N?w(w({},N),{},{ticks:B?O:N.ticks}):void 0,width:t,height:r,offset:o},!!B||b);(0,i.R)(Array.isArray(U),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(typeof U,"]")),Array.isArray(U)&&(L=U)}if((!R||!R.length)&&"function"==typeof I){var F=P&&P.length,K=I({xAxis:D?w(w({},D),{},{ticks:F?P:D.ticks}):void 0,width:t,height:r,offset:o},!!F||b);(0,i.R)(Array.isArray(K),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(typeof K,"]")),Array.isArray(K)&&(R=K)}return n.createElement("g",{className:"recharts-cartesian-grid"},n.createElement(j,{fill:l.fill,fillOpacity:l.fillOpacity,x:l.x,y:l.y,width:l.width,height:l.height,ry:l.ry}),n.createElement(M,x({},l,{horizontalPoints:L})),n.createElement(S,x({},l,{verticalPoints:R})),n.createElement(A,x({},l,{offset:o,horizontalPoints:L,xAxis:D,yAxis:N})),n.createElement(E,x({},l,{offset:o,verticalPoints:R,xAxis:D,yAxis:N})))}C.displayName="CartesianGrid"},42844:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var n=(e,t)=>{if(e&&t)return null!=e&&e.reversed?[t[1],t[0]]:t}},43060:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(65738),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.isKey=function(e,t){return!Array.isArray(e)&&(!!("number"==typeof e||"boolean"==typeof e||null==e||n.isSymbol(e))||"string"==typeof e&&(a.test(e)||!i.test(e))||null!=t&&Object.hasOwn(t,e))}},43630:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.debounce=function(e,t=0,r={}){let n;"object"!=typeof r&&(r={});let i=null,a=null,o=null,l=0,u=null,{leading:c=!1,trailing:s=!0,maxWait:f}=r,h="maxWait"in r,p=h?Math.max(Number(f)||0,t):0,d=t=>(null!==i&&(n=e.apply(a,i)),i=a=null,l=t,n),y=e=>(l=e,u=setTimeout(b,t),c&&null!==i)?d(e):n,v=e=>(u=null,s&&null!==i)?d(e):n,g=e=>{if(null===o)return!0;let r=e-o,n=h&&e-l>=p;return r>=t||r<0||n},m=e=>{let r=t-(null===o?0:e-o),n=p-(e-l);return h?Math.min(r,n):r},b=()=>{let e=Date.now();if(g(e))return v(e);u=setTimeout(b,m(e))},w=function(...e){let r=Date.now(),l=g(r);if(i=e,a=this,o=r,l){if(null===u)return y(r);if(h)return clearTimeout(u),u=setTimeout(b,t),d(r)}return null===u&&(u=setTimeout(b,t)),n};return w.cancel=()=>{null!==u&&clearTimeout(u),l=0,o=i=a=u=null},w.flush=()=>null===u?n:v(Date.now()),w}},44251:(e,t,r)=>{"use strict";r.d(t,{p:()=>o});var n=r(94285),i=r(96231),a=r(4517);function o(e){var t=(0,a.j)();return(0,n.useEffect)(()=>{t((0,i.mZ)(e))},[t,e]),null}},45256:(e,t,r)=>{e.exports=r(11880).range},45505:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPlainObject=function(e){if("object"!=typeof e||null==e)return!1;if(null===Object.getPrototypeOf(e))return!0;if("[object Object]"!==Object.prototype.toString.call(e)){let t=e[Symbol.toStringTag];return null!=t&&!!Object.getOwnPropertyDescriptor(e,Symbol.toStringTag)?.writable&&e.toString()===`[object ${t}]`}let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}},45576:(e,t,r)=>{"use strict";r.d(t,{I:()=>$});var n=r(94285);function i(){}function a(e,t,r){e._context.bezierCurveTo((2*e._x0+e._x1)/3,(2*e._y0+e._y1)/3,(e._x0+2*e._x1)/3,(e._y0+2*e._y1)/3,(e._x0+4*e._x1+t)/6,(e._y0+4*e._y1+r)/6)}function o(e){this._context=e}function l(e){this._context=e}function u(e){this._context=e}o.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:a(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},l.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._x2=e,this._y2=t;break;case 1:this._point=2,this._x3=e,this._y3=t;break;case 2:this._point=3,this._x4=e,this._y4=t,this._context.moveTo((this._x0+4*this._x1+e)/6,(this._y0+4*this._y1+t)/6);break;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}},u.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+e)/6,n=(this._y0+4*this._y1+t)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:a(this,e,t)}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t}};class c{constructor(e,t){this._context=e,this._x=t}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+e)/2,this._y0,this._x0,t,e,t):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+t)/2,e,this._y0,e,t)}this._x0=e,this._y0=t}}function s(e){this._context=e}function f(e){this._context=e}function h(e){return new f(e)}s.prototype={areaStart:i,areaEnd:i,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(e,t){e*=1,t*=1,this._point?this._context.lineTo(e,t):(this._point=1,this._context.moveTo(e,t))}};function p(e,t,r){var n=e._x1-e._x0,i=t-e._x1,a=(e._y1-e._y0)/(n||i<0&&-0),o=(r-e._y1)/(i||n<0&&-0);return((a<0?-1:1)+(o<0?-1:1))*Math.min(Math.abs(a),Math.abs(o),.5*Math.abs((a*i+o*n)/(n+i)))||0}function d(e,t){var r=e._x1-e._x0;return r?(3*(e._y1-e._y0)/r-t)/2:t}function y(e,t,r){var n=e._x0,i=e._y0,a=e._x1,o=e._y1,l=(a-n)/3;e._context.bezierCurveTo(n+l,i+l*t,a-l,o-l*r,a,o)}function v(e){this._context=e}function g(e){this._context=new m(e)}function m(e){this._context=e}function b(e){this._context=e}function w(e){var t,r,n=e.length-1,i=Array(n),a=Array(n),o=Array(n);for(i[0]=0,a[0]=2,o[0]=e[0]+2*e[1],t=1;t<n-1;++t)i[t]=1,a[t]=4,o[t]=4*e[t]+2*e[t+1];for(i[n-1]=2,a[n-1]=7,o[n-1]=8*e[n-1]+e[n],t=1;t<n;++t)r=i[t]/a[t-1],a[t]-=r,o[t]-=r*o[t-1];for(i[n-1]=o[n-1]/a[n-1],t=n-2;t>=0;--t)i[t]=(o[t]-i[t+1])/a[t];for(t=0,a[n-1]=(e[n]+i[n-1])/2;t<n-1;++t)a[t]=2*e[t+1]-i[t+1];return[i,a]}function x(e,t){this._context=e,this._t=t}f.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:this._context.lineTo(e,t)}}},v.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:y(this,this._t0,d(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(e,t){var r=NaN;if(t*=1,(e*=1)!==this._x1||t!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;break;case 2:this._point=3,y(this,d(this,r=p(this,e,t)),r);break;default:y(this,this._t0,r=p(this,e,t))}this._x0=this._x1,this._x1=e,this._y0=this._y1,this._y1=t,this._t0=r}}},(g.prototype=Object.create(v.prototype)).point=function(e,t){v.prototype.point.call(this,t,e)},m.prototype={moveTo:function(e,t){this._context.moveTo(t,e)},closePath:function(){this._context.closePath()},lineTo:function(e,t){this._context.lineTo(t,e)},bezierCurveTo:function(e,t,r,n,i,a){this._context.bezierCurveTo(t,e,n,r,a,i)}},b.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var e=this._x,t=this._y,r=e.length;if(r)if(this._line?this._context.lineTo(e[0],t[0]):this._context.moveTo(e[0],t[0]),2===r)this._context.lineTo(e[1],t[1]);else for(var n=w(e),i=w(t),a=0,o=1;o<r;++a,++o)this._context.bezierCurveTo(n[0][a],i[0][a],n[1][a],i[1][a],e[o],t[o]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(e,t){this._x.push(+e),this._y.push(+t)}},x.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(e,t){switch(e*=1,t*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(e,t):this._context.moveTo(e,t);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,t),this._context.lineTo(e,t);else{var r=this._x*(1-this._t)+e*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,t)}}this._x=e,this._y=t}};var O=r(95737),j=r(31896),P=r(92389);function A(e){return e[0]}function E(e){return e[1]}function M(e,t){var r=(0,j.A)(!0),n=null,i=h,a=null,o=(0,P.i)(l);function l(l){var u,c,s,f=(l=(0,O.A)(l)).length,h=!1;for(null==n&&(a=i(s=o())),u=0;u<=f;++u)!(u<f&&r(c=l[u],u,l))===h&&((h=!h)?a.lineStart():a.lineEnd()),h&&a.point(+e(c,u,l),+t(c,u,l));if(s)return a=null,s+""||null}return e="function"==typeof e?e:void 0===e?A:(0,j.A)(e),t="function"==typeof t?t:void 0===t?E:(0,j.A)(t),l.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),l):e},l.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),l):t},l.defined=function(e){return arguments.length?(r="function"==typeof e?e:(0,j.A)(!!e),l):r},l.curve=function(e){return arguments.length?(i=e,null!=n&&(a=i(n)),l):i},l.context=function(e){return arguments.length?(null==e?n=a=null:a=i(n=e),l):n},l}function S(e,t,r){var n=null,i=(0,j.A)(!0),a=null,o=h,l=null,u=(0,P.i)(c);function c(c){var s,f,h,p,d,y=(c=(0,O.A)(c)).length,v=!1,g=Array(y),m=Array(y);for(null==a&&(l=o(d=u())),s=0;s<=y;++s){if(!(s<y&&i(p=c[s],s,c))===v)if(v=!v)f=s,l.areaStart(),l.lineStart();else{for(l.lineEnd(),l.lineStart(),h=s-1;h>=f;--h)l.point(g[h],m[h]);l.lineEnd(),l.areaEnd()}v&&(g[s]=+e(p,s,c),m[s]=+t(p,s,c),l.point(n?+n(p,s,c):g[s],r?+r(p,s,c):m[s]))}if(d)return l=null,d+""||null}function s(){return M().defined(i).curve(o).context(a)}return e="function"==typeof e?e:void 0===e?A:(0,j.A)(+e),t="function"==typeof t?t:void 0===t?(0,j.A)(0):(0,j.A)(+t),r="function"==typeof r?r:void 0===r?E:(0,j.A)(+r),c.x=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),n=null,c):e},c.x0=function(t){return arguments.length?(e="function"==typeof t?t:(0,j.A)(+t),c):e},c.x1=function(e){return arguments.length?(n=null==e?null:"function"==typeof e?e:(0,j.A)(+e),c):n},c.y=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),r=null,c):t},c.y0=function(e){return arguments.length?(t="function"==typeof e?e:(0,j.A)(+e),c):t},c.y1=function(e){return arguments.length?(r=null==e?null:"function"==typeof e?e:(0,j.A)(+e),c):r},c.lineX0=c.lineY0=function(){return s().x(e).y(t)},c.lineY1=function(){return s().x(e).y(r)},c.lineX1=function(){return s().x(n).y(t)},c.defined=function(e){return arguments.length?(i="function"==typeof e?e:(0,j.A)(!!e),c):i},c.curve=function(e){return arguments.length?(o=e,null!=a&&(l=o(a)),c):o},c.context=function(e){return arguments.length?(null==e?a=l=null:l=o(a=e),c):a},c}var _=r(3638),k=r(53951),T=r(71998),C=r(29427),D=r(38034);function N(){return(N=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function I(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function z(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?I(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var L={curveBasisClosed:function(e){return new l(e)},curveBasisOpen:function(e){return new u(e)},curveBasis:function(e){return new o(e)},curveBumpX:function(e){return new c(e,!0)},curveBumpY:function(e){return new c(e,!1)},curveLinearClosed:function(e){return new s(e)},curveLinear:h,curveMonotoneX:function(e){return new v(e)},curveMonotoneY:function(e){return new g(e)},curveNatural:function(e){return new b(e)},curveStep:function(e){return new x(e,.5)},curveStepAfter:function(e){return new x(e,1)},curveStepBefore:function(e){return new x(e,0)}},R=e=>(0,D.H)(e.x)&&(0,D.H)(e.y),B=e=>e.x,U=e=>e.y,F=(e,t)=>{if("function"==typeof e)return e;var r="curve".concat((0,C.Zb)(e));return("curveMonotone"===r||"curveBump"===r)&&t?L["".concat(r).concat("vertical"===t?"Y":"X")]:L[r]||h},K=e=>{var t,{type:r="linear",points:n=[],baseLine:i,layout:a,connectNulls:o=!1}=e,l=F(r,a),u=o?n.filter(R):n;if(Array.isArray(i)){var c=o?i.filter(e=>R(e)):i,s=u.map((e,t)=>z(z({},e),{},{base:c[t]}));return(t="vertical"===a?S().y(U).x1(B).x0(e=>e.base.x):S().x(B).y1(U).y0(e=>e.base.y)).defined(R).curve(l),t(s)}return(t="vertical"===a&&(0,C.Et)(i)?S().y(U).x1(B).x0(i):(0,C.Et)(i)?S().x(B).y1(U).y0(i):M().x(B).y(U)).defined(R).curve(l),t(u)},$=e=>{var{className:t,points:r,path:i,pathRef:a}=e;if((!r||!r.length)&&!i)return null;var o=r&&r.length?K(e):i;return n.createElement("path",N({},(0,T.J9)(e,!1),(0,k._U)(e),{className:(0,_.$)("recharts-curve",t),d:null===o?void 0:o,ref:a}))}},47629:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(98612);t.property=function(e){return function(t){return n.get(t,e)}}},49039:(e,t,r)=>{"use strict";r.d(t,{Cj:()=>a,Pg:()=>o,Ub:()=>l});var n=r(4517),i=r(82284),a=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.RD)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}},o=e=>{var t=(0,n.j)();return(r,n)=>a=>{null==e||e(r,n,a),t((0,i.oP)())}},l=(e,t)=>{var r=(0,n.j)();return(n,a)=>o=>{null==e||e(n,a,o),r((0,i.ML)({activeIndex:String(a),activeDataKey:t,activeCoordinate:n.tooltipPosition}))}}},49651:(e,t,r)=>{"use strict";r.d(t,{F0:()=>n,tQ:()=>a,um:()=>i});var n="data-recharts-item-index",i="data-recharts-item-data-key",a=60},50985:function(e,t,r){var n;!function(i){"use strict";var a,o={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},l=!0,u="[DecimalError] ",c=u+"Invalid argument: ",s=u+"Exponent out of range: ",f=Math.floor,h=Math.pow,p=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(e,t){var r,n,i,a,o,u,c,s,f=e.constructor,h=f.precision;if(!e.s||!t.s)return t.s||(t=new f(e)),l?E(t,h):t;if(c=e.d,s=t.d,o=e.e,i=t.e,c=c.slice(),a=o-i){for(a<0?(n=c,a=-a,u=s.length):(n=s,i=o,u=c.length),a>(u=(o=Math.ceil(h/7))>u?o+1:u+1)&&(a=u,n.length=1),n.reverse();a--;)n.push(0);n.reverse()}for((u=c.length)-(a=s.length)<0&&(a=u,n=s,s=c,c=n),r=0;a;)r=(c[--a]=c[a]+s[a]+r)/1e7|0,c[a]%=1e7;for(r&&(c.unshift(r),++i),u=c.length;0==c[--u];)c.pop();return t.d=c,t.e=i,l?E(t,h):t}function g(e,t,r){if(e!==~~e||e<t||e>r)throw Error(c+e)}function m(e){var t,r,n,i=e.length-1,a="",o=e[0];if(i>0){for(a+=o,t=1;t<i;t++)(r=7-(n=e[t]+"").length)&&(a+=j(r)),a+=n;(r=7-(n=(o=e[t])+"").length)&&(a+=j(r))}else if(0===o)return"0";for(;o%10==0;)o/=10;return a+o}y.absoluteValue=y.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e},y.comparedTo=y.cmp=function(e){var t,r,n,i;if(e=new this.constructor(e),this.s!==e.s)return this.s||-e.s;if(this.e!==e.e)return this.e>e.e^this.s<0?1:-1;for(t=0,r=(n=this.d.length)<(i=e.d.length)?n:i;t<r;++t)if(this.d[t]!==e.d[t])return this.d[t]>e.d[t]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var e=this.d.length-1,t=(e-this.e)*7;if(e=this.d[e])for(;e%10==0;e/=10)t--;return t<0?0:t},y.dividedBy=y.div=function(e){return b(this,new this.constructor(e))},y.dividedToIntegerBy=y.idiv=function(e){var t=this.constructor;return E(b(this,new t(e),0,1),t.precision)},y.equals=y.eq=function(e){return!this.cmp(e)},y.exponent=function(){return x(this)},y.greaterThan=y.gt=function(e){return this.cmp(e)>0},y.greaterThanOrEqualTo=y.gte=function(e){return this.cmp(e)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(e){return 0>this.cmp(e)},y.lessThanOrEqualTo=y.lte=function(e){return 1>this.cmp(e)},y.logarithm=y.log=function(e){var t,r=this.constructor,n=r.precision,i=n+5;if(void 0===e)e=new r(10);else if((e=new r(e)).s<1||e.eq(a))throw Error(u+"NaN");if(this.s<1)throw Error(u+(this.s?"NaN":"-Infinity"));return this.eq(a)?new r(0):(l=!1,t=b(P(this,i),P(e,i),i),l=!0,E(t,n))},y.minus=y.sub=function(e){return e=new this.constructor(e),this.s==e.s?M(this,e):v(this,(e.s=-e.s,e))},y.modulo=y.mod=function(e){var t,r=this.constructor,n=r.precision;if(!(e=new r(e)).s)throw Error(u+"NaN");return this.s?(l=!1,t=b(this,e,0,1).times(e),l=!0,this.minus(t)):E(new r(this),n)},y.naturalExponential=y.exp=function(){return w(this)},y.naturalLogarithm=y.ln=function(){return P(this)},y.negated=y.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e},y.plus=y.add=function(e){return e=new this.constructor(e),this.s==e.s?v(this,e):M(this,(e.s=-e.s,e))},y.precision=y.sd=function(e){var t,r,n;if(void 0!==e&&!!e!==e&&1!==e&&0!==e)throw Error(c+e);if(t=x(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return e&&t>r?t:r},y.squareRoot=y.sqrt=function(){var e,t,r,n,i,a,o,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(u+"NaN")}for(e=x(this),l=!1,0==(i=Math.sqrt(+this))||i==1/0?(((t=m(this.d)).length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=f((e+1)/2)-(e<0||e%2),n=new c(t=i==1/0?"5e"+e:(t=i.toExponential()).slice(0,t.indexOf("e")+1)+e)):n=new c(i.toString()),i=o=(r=c.precision)+3;;)if(n=(a=n).plus(b(this,a,o+2)).times(.5),m(a.d).slice(0,o)===(t=m(n.d)).slice(0,o)){if(t=t.slice(o-3,o+1),i==o&&"4999"==t){if(E(a,r+1,0),a.times(a).eq(this)){n=a;break}}else if("9999"!=t)break;o+=4}return l=!0,E(n,r)},y.times=y.mul=function(e){var t,r,n,i,a,o,u,c,s,f=this.constructor,h=this.d,p=(e=new f(e)).d;if(!this.s||!e.s)return new f(0);for(e.s*=this.s,r=this.e+e.e,(c=h.length)<(s=p.length)&&(a=h,h=p,p=a,o=c,c=s,s=o),a=[],n=o=c+s;n--;)a.push(0);for(n=s;--n>=0;){for(t=0,i=c+n;i>n;)u=a[i]+p[n]*h[i-n-1]+t,a[i--]=u%1e7|0,t=u/1e7|0;a[i]=(a[i]+t)%1e7|0}for(;!a[--o];)a.pop();return t?++r:a.shift(),e.d=a,e.e=r,l?E(e,f.precision):e},y.toDecimalPlaces=y.todp=function(e,t){var r=this,n=r.constructor;return(r=new n(r),void 0===e)?r:(g(e,0,1e9),void 0===t?t=n.rounding:g(t,0,8),E(r,e+x(r)+1,t))},y.toExponential=function(e,t){var r,n=this,i=n.constructor;return void 0===e?r=S(n,!0):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=S(n=E(new i(n),e+1,t),!0,e+1)),r},y.toFixed=function(e,t){var r,n,i=this.constructor;return void 0===e?S(this):(g(e,0,1e9),void 0===t?t=i.rounding:g(t,0,8),r=S((n=E(new i(this),e+x(this)+1,t)).abs(),!1,e+x(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var e=this.constructor;return E(new e(this),x(this)+1,e.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(e){var t,r,n,i,o,c,s=this,h=s.constructor,p=+(e=new h(e));if(!e.s)return new h(a);if(!(s=new h(s)).s){if(e.s<1)throw Error(u+"Infinity");return s}if(s.eq(a))return s;if(n=h.precision,e.eq(a))return E(s,n);if(c=(t=e.e)>=(r=e.d.length-1),o=s.s,c){if((r=p<0?-p:p)<=0x1fffffffffffff){for(i=new h(a),t=Math.ceil(n/7+4),l=!1;r%2&&_((i=i.times(s)).d,t),0!==(r=f(r/2));)_((s=s.times(s)).d,t);return l=!0,e.s<0?new h(a).div(i):E(i,n)}}else if(o<0)throw Error(u+"NaN");return o=o<0&&1&e.d[Math.max(t,r)]?-1:1,s.s=1,l=!1,i=e.times(P(s,n+12)),l=!0,(i=w(i)).s=o,i},y.toPrecision=function(e,t){var r,n,i=this,a=i.constructor;return void 0===e?(r=x(i),n=S(i,r<=a.toExpNeg||r>=a.toExpPos)):(g(e,1,1e9),void 0===t?t=a.rounding:g(t,0,8),r=x(i=E(new a(i),e,t)),n=S(i,e<=r||r<=a.toExpNeg,e)),n},y.toSignificantDigits=y.tosd=function(e,t){var r=this.constructor;return void 0===e?(e=r.precision,t=r.rounding):(g(e,1,1e9),void 0===t?t=r.rounding:g(t,0,8)),E(new r(this),e,t)},y.toString=y.valueOf=y.val=y.toJSON=function(){var e=x(this),t=this.constructor;return S(this,e<=t.toExpNeg||e>=t.toExpPos)};var b=function(){function e(e,t){var r,n=0,i=e.length;for(e=e.slice();i--;)r=e[i]*t+n,e[i]=r%1e7|0,n=r/1e7|0;return n&&e.unshift(n),e}function t(e,t,r,n){var i,a;if(r!=n)a=r>n?1:-1;else for(i=a=0;i<r;i++)if(e[i]!=t[i]){a=e[i]>t[i]?1:-1;break}return a}function r(e,t,r){for(var n=0;r--;)e[r]-=n,n=+(e[r]<t[r]),e[r]=1e7*n+e[r]-t[r];for(;!e[0]&&e.length>1;)e.shift()}return function(n,i,a,o){var l,c,s,f,h,p,d,y,v,g,m,b,w,O,j,P,A,M,S=n.constructor,_=n.s==i.s?1:-1,k=n.d,T=i.d;if(!n.s)return new S(n);if(!i.s)throw Error(u+"Division by zero");for(s=0,c=n.e-i.e,A=T.length,j=k.length,y=(d=new S(_)).d=[];T[s]==(k[s]||0);)++s;if(T[s]>(k[s]||0)&&--c,(b=null==a?a=S.precision:o?a+(x(n)-x(i))+1:a)<0)return new S(0);if(b=b/7+2|0,s=0,1==A)for(f=0,T=T[0],b++;(s<j||f)&&b--;s++)w=1e7*f+(k[s]||0),y[s]=w/T|0,f=w%T|0;else{for((f=1e7/(T[0]+1)|0)>1&&(T=e(T,f),k=e(k,f),A=T.length,j=k.length),O=A,g=(v=k.slice(0,A)).length;g<A;)v[g++]=0;(M=T.slice()).unshift(0),P=T[0],T[1]>=1e7/2&&++P;do f=0,(l=t(T,v,A,g))<0?(m=v[0],A!=g&&(m=1e7*m+(v[1]||0)),(f=m/P|0)>1?(f>=1e7&&(f=1e7-1),p=(h=e(T,f)).length,g=v.length,1==(l=t(h,v,p,g))&&(f--,r(h,A<p?M:T,p))):(0==f&&(l=f=1),h=T.slice()),(p=h.length)<g&&h.unshift(0),r(v,h,g),-1==l&&(g=v.length,(l=t(T,v,A,g))<1&&(f++,r(v,A<g?M:T,g))),g=v.length):0===l&&(f++,v=[0]),y[s++]=f,l&&v[0]?v[g++]=k[O]||0:(v=[k[O]],g=1);while((O++<j||void 0!==v[0])&&b--)}return y[0]||y.shift(),d.e=c,E(d,o?a+x(d)+1:a)}}();function w(e,t){var r,n,i,o,u,c=0,f=0,p=e.constructor,d=p.precision;if(x(e)>16)throw Error(s+x(e));if(!e.s)return new p(a);for(null==t?(l=!1,u=d):u=t,o=new p(.03125);e.abs().gte(.1);)e=e.times(o),f+=5;for(u+=Math.log(h(2,f))/Math.LN10*2+5|0,r=n=i=new p(a),p.precision=u;;){if(n=E(n.times(e),u),r=r.times(++c),m((o=i.plus(b(n,r,u))).d).slice(0,u)===m(i.d).slice(0,u)){for(;f--;)i=E(i.times(i),u);return p.precision=d,null==t?(l=!0,E(i,d)):i}i=o}}function x(e){for(var t=7*e.e,r=e.d[0];r>=10;r/=10)t++;return t}function O(e,t,r){if(t>e.LN10.sd())throw l=!0,r&&(e.precision=r),Error(u+"LN10 precision limit exceeded");return E(new e(e.LN10),t)}function j(e){for(var t="";e--;)t+="0";return t}function P(e,t){var r,n,i,o,c,s,f,h,p,d=1,y=e,v=y.d,g=y.constructor,w=g.precision;if(y.s<1)throw Error(u+(y.s?"NaN":"-Infinity"));if(y.eq(a))return new g(0);if(null==t?(l=!1,h=w):h=t,y.eq(10))return null==t&&(l=!0),O(g,h);if(g.precision=h+=10,n=(r=m(v)).charAt(0),!(15e14>Math.abs(o=x(y))))return f=O(g,h+2,w).times(o+""),y=P(new g(n+"."+r.slice(1)),h-10).plus(f),g.precision=w,null==t?(l=!0,E(y,w)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=m((y=y.times(e)).d)).charAt(0),d++;for(o=x(y),n>1?(y=new g("0."+r),o++):y=new g(n+"."+r.slice(1)),s=c=y=b(y.minus(a),y.plus(a),h),p=E(y.times(y),h),i=3;;){if(c=E(c.times(p),h),m((f=s.plus(b(c,new g(i),h))).d).slice(0,h)===m(s.d).slice(0,h))return s=s.times(2),0!==o&&(s=s.plus(O(g,h+2,w).times(o+""))),s=b(s,new g(d),h),g.precision=w,null==t?(l=!0,E(s,w)):s;s=f,i+=2}}function A(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;48===t.charCodeAt(n);)++n;for(i=t.length;48===t.charCodeAt(i-1);)--i;if(t=t.slice(n,i)){if(i-=n,e.e=f((r=r-n-1)/7),e.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=7;n<i;)e.d.push(+t.slice(n,n+=7));n=7-(t=t.slice(n)).length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),l&&(e.e>d||e.e<-d))throw Error(s+r)}else e.s=0,e.e=0,e.d=[0];return e}function E(e,t,r){var n,i,a,o,u,c,p,y,v=e.d;for(o=1,a=v[0];a>=10;a/=10)o++;if((n=t-o)<0)n+=7,i=t,p=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(a=v.length))return e;for(o=1,p=a=v[y];a>=10;a/=10)o++;n%=7,i=n-7+o}if(void 0!==r&&(u=p/(a=h(10,o-i-1))%10|0,c=t<0||void 0!==v[y+1]||p%a,c=r<4?(u||c)&&(0==r||r==(e.s<0?3:2)):u>5||5==u&&(4==r||c||6==r&&(n>0?i>0?p/h(10,o-i):0:v[y-1])%10&1||r==(e.s<0?8:7))),t<1||!v[0])return c?(a=x(e),v.length=1,t=t-a-1,v[0]=h(10,(7-t%7)%7),e.e=f(-t/7)||0):(v.length=1,v[0]=e.e=e.s=0),e;if(0==n?(v.length=y,a=1,y--):(v.length=y+1,a=h(10,7-n),v[y]=i>0?(p/h(10,o-i)%h(10,i)|0)*a:0),c)for(;;)if(0==y){1e7==(v[0]+=a)&&(v[0]=1,++e.e);break}else{if(v[y]+=a,1e7!=v[y])break;v[y--]=0,a=1}for(n=v.length;0===v[--n];)v.pop();if(l&&(e.e>d||e.e<-d))throw Error(s+x(e));return e}function M(e,t){var r,n,i,a,o,u,c,s,f,h,p=e.constructor,d=p.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new p(e),l?E(t,d):t;if(c=e.d,h=t.d,n=t.e,s=e.e,c=c.slice(),o=s-n){for((f=o<0)?(r=c,o=-o,u=h.length):(r=h,n=s,u=c.length),o>(i=Math.max(Math.ceil(d/7),u)+2)&&(o=i,r.length=1),r.reverse(),i=o;i--;)r.push(0);r.reverse()}else{for((f=(i=c.length)<(u=h.length))&&(u=i),i=0;i<u;i++)if(c[i]!=h[i]){f=c[i]<h[i];break}o=0}for(f&&(r=c,c=h,h=r,t.s=-t.s),u=c.length,i=h.length-u;i>0;--i)c[u++]=0;for(i=h.length;i>o;){if(c[--i]<h[i]){for(a=i;a&&0===c[--a];)c[a]=1e7-1;--c[a],c[i]+=1e7}c[i]-=h[i]}for(;0===c[--u];)c.pop();for(;0===c[0];c.shift())--n;return c[0]?(t.d=c,t.e=n,l?E(t,d):t):new p(0)}function S(e,t,r){var n,i=x(e),a=m(e.d),o=a.length;return t?(r&&(n=r-o)>0?a=a.charAt(0)+"."+a.slice(1)+j(n):o>1&&(a=a.charAt(0)+"."+a.slice(1)),a=a+(i<0?"e":"e+")+i):i<0?(a="0."+j(-i-1)+a,r&&(n=r-o)>0&&(a+=j(n))):i>=o?(a+=j(i+1-o),r&&(n=r-i-1)>0&&(a=a+"."+j(n))):((n=i+1)<o&&(a=a.slice(0,n)+"."+a.slice(n)),r&&(n=r-o)>0&&(i+1===o&&(a+="."),a+=j(n))),e.s<0?"-"+a:a}function _(e,t){if(e.length>t)return e.length=t,!0}function k(e){if(!e||"object"!=typeof e)throw Error(u+"Object expected");var t,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if(void 0!==(n=e[r=i[t]]))if(f(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(c+r+": "+n);if(void 0!==(n=e[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(c+r+": "+n);return this}(o=function e(t){var r,n,i;function a(e){if(!(this instanceof a))return new a(e);if(this.constructor=a,e instanceof a){this.s=e.s,this.e=e.e,this.d=(e=e.d)?e.slice():e;return}if("number"==typeof e){if(0*e!=0)throw Error(c+e);if(e>0)this.s=1;else if(e<0)e=-e,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(e===~~e&&e<1e7){this.e=0,this.d=[e];return}return A(this,e.toString())}if("string"!=typeof e)throw Error(c+e);if(45===e.charCodeAt(0)?(e=e.slice(1),this.s=-1):this.s=1,p.test(e))A(this,e);else throw Error(c+e)}if(a.prototype=y,a.ROUND_UP=0,a.ROUND_DOWN=1,a.ROUND_CEIL=2,a.ROUND_FLOOR=3,a.ROUND_HALF_UP=4,a.ROUND_HALF_DOWN=5,a.ROUND_HALF_EVEN=6,a.ROUND_HALF_CEIL=7,a.ROUND_HALF_FLOOR=8,a.clone=e,a.config=a.set=k,void 0===t&&(t={}),t)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)t.hasOwnProperty(n=i[r++])||(t[n]=this[n]);return a.config(t),a}(o)).default=o.Decimal=o,a=new o(1),void 0===(n=(function(){return o}).call(t,r,t,e))||(e.exports=n)}(0)},51556:(e,t,r)=>{"use strict";function n(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}r.d(t,{HY:()=>c,Qd:()=>l,Tw:()=>f,Zz:()=>s,ve:()=>h,y$:()=>u});var i="function"==typeof Symbol&&Symbol.observable||"@@observable",a=()=>Math.random().toString(36).substring(7).split("").join("."),o={INIT:`@@redux/INIT${a()}`,REPLACE:`@@redux/REPLACE${a()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${a()}`};function l(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function u(e,t,r){if("function"!=typeof e)throw Error(n(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw Error(n(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw Error(n(1));return r(u)(e,t)}let a=e,c=t,s=new Map,f=s,h=0,p=!1;function d(){f===s&&(f=new Map,s.forEach((e,t)=>{f.set(t,e)}))}function y(){if(p)throw Error(n(3));return c}function v(e){if("function"!=typeof e)throw Error(n(4));if(p)throw Error(n(5));let t=!0;d();let r=h++;return f.set(r,e),function(){if(t){if(p)throw Error(n(6));t=!1,d(),f.delete(r),s=null}}}function g(e){if(!l(e))throw Error(n(7));if(void 0===e.type)throw Error(n(8));if("string"!=typeof e.type)throw Error(n(17));if(p)throw Error(n(9));try{p=!0,c=a(c,e)}finally{p=!1}return(s=f).forEach(e=>{e()}),e}return g({type:o.INIT}),{dispatch:g,subscribe:v,getState:y,replaceReducer:function(e){if("function"!=typeof e)throw Error(n(10));a=e,g({type:o.REPLACE})},[i]:function(){return{subscribe(e){if("object"!=typeof e||null===e)throw Error(n(11));function t(){e.next&&e.next(y())}return t(),{unsubscribe:v(t)}},[i](){return this}}}}}function c(e){let t,r=Object.keys(e),i={};for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof e[n]&&(i[n]=e[n])}let a=Object.keys(i);try{Object.keys(i).forEach(e=>{let t=i[e];if(void 0===t(void 0,{type:o.INIT}))throw Error(n(12));if(void 0===t(void 0,{type:o.PROBE_UNKNOWN_ACTION()}))throw Error(n(13))})}catch(e){t=e}return function(e={},r){if(t)throw t;let o=!1,l={};for(let t=0;t<a.length;t++){let u=a[t],c=i[u],s=e[u],f=c(s,r);if(void 0===f)throw r&&r.type,Error(n(14));l[u]=f,o=o||f!==s}return(o=o||a.length!==Object.keys(e).length)?l:e}}function s(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}function f(...e){return t=>(r,i)=>{let a=t(r,i),o=()=>{throw Error(n(15))},l={getState:a.getState,dispatch:(e,...t)=>o(e,...t)};return o=s(...e.map(e=>e(l)))(a.dispatch),{...a,dispatch:o}}}function h(e){return l(e)&&"type"in e&&"string"==typeof e.type}},52084:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>ea,eE:()=>ec,Xb:()=>eo,A2:()=>ei,yn:()=>es,Dn:()=>P,gL:()=>V,fl:()=>Y,R4:()=>Q,Re:()=>O,n4:()=>k});var n=r(73474),i=r(96112),a=r(21584),o=r(65084),l=r(92602),u=r(4788),c=r(29427),s=r(42844),f=r(91881),h=r(62444),p=r(68810),d=r(79423),y=r(96546),v=r(23487),g=r(90355),m=r(63700),b=r(5124),w=r(12664),x=r(58139),O=e=>{var t=(0,a.fz)(e);return"horizontal"===t?"xAxis":"vertical"===t?"yAxis":"centric"===t?"angleAxis":"radiusAxis"},j=e=>e.tooltip.settings.axisId,P=e=>{var t=O(e),r=j(e);return(0,i.Hd)(e,t,r)},A=(0,n.Mz)([P,a.fz,i.um,u.iO,O],i.sr),E=(0,n.Mz)([e=>e.graphicalItems.cartesianItems,e=>e.graphicalItems.polarItems],(e,t)=>[...e,...t]),M=(0,n.Mz)([O,j],i.eo),S=(0,n.Mz)([E,P,M],i.ec),_=(0,n.Mz)([S],i.rj),k=(0,n.Mz)([_,l.LF],i.Nk),T=(0,n.Mz)([k,P,S],i.fb),C=(0,n.Mz)([P],i.S5),D=(0,n.Mz)([k,S,u.eC],i.MK),N=(0,n.Mz)([D,l.LF,O],i.pM),I=(0,n.Mz)([S],i.IO),z=(0,n.Mz)([k,P,I,O],i.kz),L=(0,n.Mz)([i.Kr,O,j],i.P9),R=(0,n.Mz)([L,O],i.Oz),B=(0,n.Mz)([i.gT,O,j],i.P9),U=(0,n.Mz)([B,O],i.q),F=(0,n.Mz)([i.$X,O,j],i.P9),K=(0,n.Mz)([F,O],i.bb),$=(0,n.Mz)([R,K,U],i.yi),G=(0,n.Mz)([P,C,N,z,$],i.wL),H=(0,n.Mz)([P,a.fz,k,T,u.eC,O,G],i.tP),Z=(0,n.Mz)([H,P,A],i.xp),W=(0,n.Mz)([P,H,Z,O],i.g1),q=e=>{var t=O(e),r=j(e);return(0,i.D5)(e,t,r,!1)},V=(0,n.Mz)([P,q],s.I),Y=(0,n.Mz)([P,A,W,V],i.Qn),J=(0,n.Mz)([a.fz,T,P,O],i.tF),X=(0,n.Mz)([a.fz,T,P,O],i.iv),Q=(0,n.Mz)([a.fz,P,A,Y,q,J,X,O],(e,t,r,n,i,a,l,u)=>{if(t){var{type:s}=t,f=(0,o._L)(e,u);if(n){var h="scaleBand"===r&&n.bandwidth?n.bandwidth()/2:2,p="category"===s&&n.bandwidth?n.bandwidth()/h:0;return(p="angleAxis"===u&&null!=i&&(null==i?void 0:i.length)>=2?2*(0,c.sA)(i[0]-i[1])*p:p,f&&l)?l.map((e,t)=>({coordinate:n(e)+p,value:e,index:t,offset:p})):n.domain().map((e,t)=>({coordinate:n(e)+p,value:a?a[e]:e,index:t,offset:p}))}}}),ee=(0,n.Mz)([f.xH,f.Hw,e=>e.tooltip.settings],(e,t,r)=>(0,f.$g)(r.shared,e,t)),et=e=>e.tooltip.settings.trigger,er=e=>e.tooltip.settings.defaultIndex,en=(0,n.Mz)([w.J,ee,et,er],p.i),ei=(0,n.Mz)([en,k],d.P),ea=(0,n.Mz)([Q,ei],h.E),eo=(0,n.Mz)([en],e=>{if(e)return e.dataKey}),el=(0,n.Mz)([w.J,ee,et,er],m.q),eu=(0,n.Mz)([v.Lp,v.A$,a.fz,g.HZ,Q,er,el,b.x],y.o),ec=(0,n.Mz)([en,eu],(e,t)=>null!=e&&e.coordinate?e.coordinate:t),es=(0,n.Mz)([en],e=>e.active),ef=(0,n.Mz)([el,ei,l.LF,P,ea,b.x,ee],x.N);(0,n.Mz)([ef],e=>{if(null!=e)return Array.from(new Set(e.map(e=>e.payload).filter(e=>null!=e)))})},52312:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(7411),i=r(64172),a=r(71429),o=r(87095);t.isIterateeCall=function(e,t,r){return!!a.isObject(r)&&(!!("number"==typeof t&&i.isArrayLike(r)&&n.isIndex(t))&&t<r.length||"string"==typeof t&&t in r)&&o.eq(r[t],e)}},53689:(e,t,r)=>{"use strict";var n=r(56221).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(14126),a=r(35556),o=r(90657),l=r(67628),u=r(87095);t.isEqualWith=function(e,t,r){return function e(t,r,c,s,f,h,p){let d=p(t,r,c,s,f,h);if(void 0!==d)return d;if(typeof t==typeof r)switch(typeof t){case"bigint":case"string":case"boolean":case"symbol":case"undefined":case"function":return t===r;case"number":return t===r||Object.is(t,r)}return function t(r,c,s,f){if(Object.is(r,c))return!0;let h=o.getTag(r),p=o.getTag(c);if(h===l.argumentsTag&&(h=l.objectTag),p===l.argumentsTag&&(p=l.objectTag),h!==p)return!1;switch(h){case l.stringTag:return r.toString()===c.toString();case l.numberTag:{let e=r.valueOf(),t=c.valueOf();return u.eq(e,t)}case l.booleanTag:case l.dateTag:case l.symbolTag:return Object.is(r.valueOf(),c.valueOf());case l.regexpTag:return r.source===c.source&&r.flags===c.flags;case l.functionTag:return r===c}let d=(s=s??new Map).get(r),y=s.get(c);if(null!=d&&null!=y)return d===c;s.set(r,c),s.set(c,r);try{switch(h){case l.mapTag:if(r.size!==c.size)return!1;for(let[t,n]of r.entries())if(!c.has(t)||!e(n,c.get(t),t,r,c,s,f))return!1;return!0;case l.setTag:{if(r.size!==c.size)return!1;let t=Array.from(r.values()),n=Array.from(c.values());for(let i=0;i<t.length;i++){let a=t[i],o=n.findIndex(t=>e(a,t,void 0,r,c,s,f));if(-1===o)return!1;n.splice(o,1)}return!0}case l.arrayTag:case l.uint8ArrayTag:case l.uint8ClampedArrayTag:case l.uint16ArrayTag:case l.uint32ArrayTag:case l.bigUint64ArrayTag:case l.int8ArrayTag:case l.int16ArrayTag:case l.int32ArrayTag:case l.bigInt64ArrayTag:case l.float32ArrayTag:case l.float64ArrayTag:if(void 0!==n&&n.isBuffer(r)!==n.isBuffer(c)||r.length!==c.length)return!1;for(let t=0;t<r.length;t++)if(!e(r[t],c[t],t,r,c,s,f))return!1;return!0;case l.arrayBufferTag:if(r.byteLength!==c.byteLength)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.dataViewTag:if(r.byteLength!==c.byteLength||r.byteOffset!==c.byteOffset)return!1;return t(new Uint8Array(r),new Uint8Array(c),s,f);case l.errorTag:return r.name===c.name&&r.message===c.message;case l.objectTag:{if(!(t(r.constructor,c.constructor,s,f)||i.isPlainObject(r)&&i.isPlainObject(c)))return!1;let n=[...Object.keys(r),...a.getSymbols(r)],o=[...Object.keys(c),...a.getSymbols(c)];if(n.length!==o.length)return!1;for(let t=0;t<n.length;t++){let i=n[t],a=r[i];if(!Object.hasOwn(c,i))return!1;let o=c[i];if(!e(a,o,i,r,c,s,f))return!1}return!0}default:return!1}}finally{s.delete(r),s.delete(c)}}(t,r,h,p)}(e,t,void 0,void 0,void 0,void 0,r)}},53898:(e,t,r)=>{"use strict";r.d(t,{W:()=>u});var n=r(94285),i=r(3638),a=r(71998),o=["children","className"];function l(){return(l=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var u=n.forwardRef((e,t)=>{var{children:r,className:u}=e,c=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,o),s=(0,i.$)("recharts-layer",u);return n.createElement("g",l({className:s},(0,a.J9)(c,!0),{ref:t}),r)})},53941:(e,t,r)=>{"use strict";r.d(t,{$:()=>i,X:()=>a});var n=r(94285),i=(0,n.createContext)(null),a=()=>(0,n.useContext)(i)},53951:(e,t,r)=>{"use strict";r.d(t,{QQ:()=>i,VU:()=>o,XC:()=>s,_U:()=>u,j2:()=>l});var n=r(94285),i=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],a=["points","pathLength"],o={svg:["viewBox","children"],polygon:a,polyline:a},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],u=(e,t)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var r=e;if((0,n.isValidElement)(e)&&(r=e.props),"object"!=typeof r&&"function"!=typeof r)return null;var i={};return Object.keys(r).forEach(e=>{l.includes(e)&&(i[e]=t||(t=>r[e](r,t)))}),i},c=(e,t,r)=>n=>(e(t,r,n),null),s=(e,t,r)=>{if(null===e||"object"!=typeof e&&"function"!=typeof e)return null;var n=null;return Object.keys(e).forEach(i=>{var a=e[i];l.includes(i)&&"function"==typeof a&&(n||(n={}),n[i]=c(a,t,r))}),n}},54073:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(65738);t.toNumber=function(e){return n.isSymbol(e)?NaN:Number(e)}},54275:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84895),i=r(71429),a=r(97888),o=r(87095);function l(e,t,r,n){if(t===e)return!0;switch(typeof t){case"object":return function(e,t,r,n){if(null==t)return!0;if(Array.isArray(t))return u(e,t,r,n);if(t instanceof Map){var i=e,o=t,l=r,s=n;if(0===o.size)return!0;if(!(i instanceof Map))return!1;for(let[e,t]of o.entries())if(!1===l(i.get(e),t,e,i,o,s))return!1;return!0}if(t instanceof Set)return c(e,t,r,n);let f=Object.keys(t);if(null==e)return 0===f.length;if(0===f.length)return!0;if(n&&n.has(t))return n.get(t)===e;n&&n.set(t,e);try{for(let i=0;i<f.length;i++){let o=f[i];if(!a.isPrimitive(e)&&!(o in e)||void 0===t[o]&&void 0!==e[o]||null===t[o]&&null!==e[o]||!r(e[o],t[o],o,e,t,n))return!1}return!0}finally{n&&n.delete(t)}}(e,t,r,n);case"function":if(Object.keys(t).length>0)return l(e,{...t},r,n);return o.eq(e,t);default:if(!i.isObject(e))return o.eq(e,t);if("string"==typeof t)return""===t;return!0}}function u(e,t,r,n){if(0===t.length)return!0;if(!Array.isArray(e))return!1;let i=new Set;for(let a=0;a<t.length;a++){let o=t[a],l=!1;for(let u=0;u<e.length;u++){if(i.has(u))continue;let c=e[u],s=!1;if(r(c,o,a,e,t,n)&&(s=!0),s){i.add(u),l=!0;break}}if(!l)return!1}return!0}function c(e,t,r,n){return 0===t.size||e instanceof Set&&u([...e],[...t],r,n)}t.isMatchWith=function(e,t,r){return"function"!=typeof r?n.isMatch(e,t):l(e,t,function e(t,n,i,a,o,u){let c=r(t,n,i,a,o,u);return void 0!==c?!!c:l(t,n,e,u)},new Map)},t.isSetMatch=c},56221:e=>{!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=u(e),r=t[0],n=t[1];return(r+n)*3/4-n},t.toByteArray=function(e){var t,r,a=u(e),o=a[0],l=a[1],c=new i((o+l)*3/4-l),s=0,f=l>0?o-4:o;for(r=0;r<f;r+=4)t=n[e.charCodeAt(r)]<<18|n[e.charCodeAt(r+1)]<<12|n[e.charCodeAt(r+2)]<<6|n[e.charCodeAt(r+3)],c[s++]=t>>16&255,c[s++]=t>>8&255,c[s++]=255&t;return 2===l&&(t=n[e.charCodeAt(r)]<<2|n[e.charCodeAt(r+1)]>>4,c[s++]=255&t),1===l&&(t=n[e.charCodeAt(r)]<<10|n[e.charCodeAt(r+1)]<<4|n[e.charCodeAt(r+2)]>>2,c[s++]=t>>8&255,c[s++]=255&t),c},t.fromByteArray=function(e){for(var t,n=e.length,i=n%3,a=[],o=0,l=n-i;o<l;o+=16383)a.push(function(e,t,n){for(var i,a=[],o=t;o<n;o+=3)i=(e[o]<<16&0xff0000)+(e[o+1]<<8&65280)+(255&e[o+2]),a.push(r[i>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}(e,o,o+16383>l?l:o+16383));return 1===i?a.push(r[(t=e[n-1])>>2]+r[t<<4&63]+"=="):2===i&&a.push(r[(t=(e[n-2]<<8)+e[n-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),a.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,l=a.length;o<l;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function u(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var n=r===t?0:4-r%4;return[r,n]}n[45]=62,n[95]=63},72:function(e,t,r){"use strict";var n=r(675),i=r(783),a="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>0x7fffffff)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,l.prototype),t}function l(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return s(e)}return u(e,t,r)}function u(e,t,r){if("string"==typeof e){var n=e,i=t;if(("string"!=typeof i||""===i)&&(i="utf8"),!l.isEncoding(i))throw TypeError("Unknown encoding: "+i);var a=0|p(n,i),u=o(a),c=u.write(n,i);return c!==a&&(u=u.slice(0,c)),u}if(ArrayBuffer.isView(e))return f(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(_(e,ArrayBuffer)||e&&_(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(_(e,SharedArrayBuffer)||e&&_(e.buffer,SharedArrayBuffer)))return function(e,t,r){var n;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),l.prototype),n}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var s=e.valueOf&&e.valueOf();if(null!=s&&s!==e)return l.from(s,t,r);var d=function(e){if(l.isBuffer(e)){var t=0|h(e.length),r=o(t);return 0===r.length||e.copy(r,0,0,t),r}return void 0!==e.length?"number"!=typeof e.length||function(e){return e!=e}(e.length)?o(0):f(e):"Buffer"===e.type&&Array.isArray(e.data)?f(e.data):void 0}(e);if(d)return d;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return l.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function c(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function s(e){return c(e),o(e<0?0:0|h(e))}function f(e){for(var t=e.length<0?0:0|h(e.length),r=o(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}t.Buffer=l,t.SlowBuffer=function(e){return+e!=e&&(e=0),l.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=0x7fffffff,l.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),l.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(l.prototype,"parent",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.buffer}}),Object.defineProperty(l.prototype,"offset",{enumerable:!0,get:function(){if(l.isBuffer(this))return this.byteOffset}}),l.poolSize=8192,l.from=function(e,t,r){return u(e,t,r)},Object.setPrototypeOf(l.prototype,Uint8Array.prototype),Object.setPrototypeOf(l,Uint8Array),l.alloc=function(e,t,r){return(c(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},l.allocUnsafe=function(e){return s(e)},l.allocUnsafeSlow=function(e){return s(e)};function h(e){if(e>=0x7fffffff)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function p(e,t){if(l.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||_(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return A(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return M(e).length;default:if(i)return n?-1:A(e).length;t=(""+t).toLowerCase(),i=!0}}function d(e,t,r){var i,a,o,l=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",a=t;a<r;++a)i+=k[e[a]];return i}(this,t,r);case"utf8":case"utf-8":return m(this,t,r);case"ascii":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}(this,t,r);case"latin1":case"binary":return function(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}(this,t,r);case"base64":return i=this,a=t,o=r,0===a&&o===i.length?n.fromByteArray(i):n.fromByteArray(i.slice(a,o));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var n=e.slice(t,r),i="",a=0;a<n.length;a+=2)i+=String.fromCharCode(n[a]+256*n[a+1]);return i}(this,t,r);default:if(l)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),l=!0}}function y(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function v(e,t,r,n,i){var a;if(0===e.length)return -1;if("string"==typeof r?(n=r,r=0):r>0x7fffffff?r=0x7fffffff:r<-0x80000000&&(r=-0x80000000),(a=r*=1)!=a&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length)if(i)return -1;else r=e.length-1;else if(r<0)if(!i)return -1;else r=0;if("string"==typeof t&&(t=l.from(t,n)),l.isBuffer(t))return 0===t.length?-1:g(e,t,r,n,i);if("number"==typeof t){if(t&=255,"function"==typeof Uint8Array.prototype.indexOf)if(i)return Uint8Array.prototype.indexOf.call(e,t,r);else return Uint8Array.prototype.lastIndexOf.call(e,t,r);return g(e,[t],r,n,i)}throw TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var a,o=1,l=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return -1;o=2,l/=2,u/=2,r/=2}function c(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(i){var s=-1;for(a=r;a<l;a++)if(c(e,a)===c(t,-1===s?0:a-s)){if(-1===s&&(s=a),a-s+1===u)return s*o}else -1!==s&&(a-=a-s),s=-1}else for(r+u>l&&(r=l-u),a=r;a>=0;a--){for(var f=!0,h=0;h<u;h++)if(c(e,a+h)!==c(t,h)){f=!1;break}if(f)return a}return -1}l.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==l.prototype},l.compare=function(e,t){if(_(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),_(t,Uint8Array)&&(t=l.from(t,t.offset,t.byteLength)),!l.isBuffer(e)||!l.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,n=t.length,i=0,a=Math.min(r,n);i<a;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:+(n<r)},l.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},l.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return l.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,n=l.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var a=e[r];if(_(a,Uint8Array)&&(a=l.from(a)),!l.isBuffer(a))throw TypeError('"list" argument must be an Array of Buffers');a.copy(n,i),i+=a.length}return n},l.byteLength=p,l.prototype._isBuffer=!0,l.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)y(this,t,t+1);return this},l.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)y(this,t,t+3),y(this,t+1,t+2);return this},l.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)y(this,t,t+7),y(this,t+1,t+6),y(this,t+2,t+5),y(this,t+3,t+4);return this},l.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?m(this,0,e):d.apply(this,arguments)},l.prototype.toLocaleString=l.prototype.toString,l.prototype.equals=function(e){if(!l.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===l.compare(this,e)},l.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},a&&(l.prototype[a]=l.prototype.inspect),l.prototype.compare=function(e,t,r,n,i){if(_(e,Uint8Array)&&(e=l.from(e,e.offset,e.byteLength)),!l.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,n>>>=0,i>>>=0,this===e)return 0;for(var a=i-n,o=r-t,u=Math.min(a,o),c=this.slice(n,i),s=e.slice(t,r),f=0;f<u;++f)if(c[f]!==s[f]){a=c[f],o=s[f];break}return a<o?-1:+(o<a)},l.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},l.prototype.indexOf=function(e,t,r){return v(this,e,t,r,!0)},l.prototype.lastIndexOf=function(e,t,r){return v(this,e,t,r,!1)};function m(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var a,o,l,u,c=e[i],s=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(s=c);break;case 2:(192&(a=e[i+1]))==128&&(u=(31&c)<<6|63&a)>127&&(s=u);break;case 3:a=e[i+1],o=e[i+2],(192&a)==128&&(192&o)==128&&(u=(15&c)<<12|(63&a)<<6|63&o)>2047&&(u<55296||u>57343)&&(s=u);break;case 4:a=e[i+1],o=e[i+2],l=e[i+3],(192&a)==128&&(192&o)==128&&(192&l)==128&&(u=(15&c)<<18|(63&a)<<12|(63&o)<<6|63&l)>65535&&u<1114112&&(s=u)}null===s?(s=65533,f=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|1023&s),n.push(s),i+=f}var h=n,p=h.length;if(p<=4096)return String.fromCharCode.apply(String,h);for(var d="",y=0;y<p;)d+=String.fromCharCode.apply(String,h.slice(y,y+=4096));return d}function b(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function w(e,t,r,n,i,a){if(!l.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<a)throw RangeError('"value" argument is out of bounds');if(r+n>e.length)throw RangeError("Index out of range")}function x(e,t,r,n,i,a){if(r+n>e.length||r<0)throw RangeError("Index out of range")}function O(e,t,r,n,a){return t*=1,r>>>=0,a||x(e,t,r,4,34028234663852886e22,-34028234663852886e22),i.write(e,t,r,n,23,4),r+4}function j(e,t,r,n,a){return t*=1,r>>>=0,a||x(e,t,r,8,17976931348623157e292,-17976931348623157e292),i.write(e,t,r,n,52,8),r+8}l.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,a,o,l,u,c,s,f,h=this.length-t;if((void 0===r||r>h)&&(r=h),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var p=!1;;)switch(n){case"hex":return function(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var a=t.length;n>a/2&&(n=a/2);for(var o=0;o<n;++o){var l,u=parseInt(t.substr(2*o,2),16);if((l=u)!=l)break;e[r+o]=u}return o}(this,e,t,r);case"utf8":case"utf-8":return i=t,a=r,S(A(e,this.length-i),this,i,a);case"ascii":return o=t,l=r,S(E(e),this,o,l);case"latin1":case"binary":return function(e,t,r,n){return S(E(t),e,r,n)}(this,e,t,r);case"base64":return u=t,c=r,S(M(e),this,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return s=t,f=r,S(function(e,t){for(var r,n,i=[],a=0;a<e.length&&!((t-=2)<0);++a)n=(r=e.charCodeAt(a))>>8,i.push(r%256),i.push(n);return i}(e,this.length-s),this,s,f);default:if(p)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),p=!0}},l.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},l.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var n=this.subarray(e,t);return Object.setPrototypeOf(n,l.prototype),n},l.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n},l.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},l.prototype.readUInt8=function(e,t){return e>>>=0,t||b(e,1,this.length),this[e]},l.prototype.readUInt16LE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]|this[e+1]<<8},l.prototype.readUInt16BE=function(e,t){return e>>>=0,t||b(e,2,this.length),this[e]<<8|this[e+1]},l.prototype.readUInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+0x1000000*this[e+3]},l.prototype.readUInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),0x1000000*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},l.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=this[e],i=1,a=0;++a<t&&(i*=256);)n+=this[e+a]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},l.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||b(e,t,this.length);for(var n=t,i=1,a=this[e+--n];n>0&&(i*=256);)a+=this[e+--n]*i;return a>=(i*=128)&&(a-=Math.pow(2,8*t)),a},l.prototype.readInt8=function(e,t){return(e>>>=0,t||b(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},l.prototype.readInt16LE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt16BE=function(e,t){e>>>=0,t||b(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?0xffff0000|r:r},l.prototype.readInt32LE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},l.prototype.readInt32BE=function(e,t){return e>>>=0,t||b(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},l.prototype.readFloatLE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!0,23,4)},l.prototype.readFloatBE=function(e,t){return e>>>=0,t||b(e,4,this.length),i.read(this,e,!1,23,4)},l.prototype.readDoubleLE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!0,52,8)},l.prototype.readDoubleBE=function(e,t){return e>>>=0,t||b(e,8,this.length),i.read(this,e,!1,52,8)},l.prototype.writeUIntLE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var a=1,o=0;for(this[t]=255&e;++o<r&&(a*=256);)this[t+o]=e/a&255;return t+r},l.prototype.writeUIntBE=function(e,t,r,n){if(e*=1,t>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;w(this,e,t,r,i,0)}var a=r-1,o=1;for(this[t+a]=255&e;--a>=0&&(o*=256);)this[t+a]=e/o&255;return t+r},l.prototype.writeUInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,255,0),this[t]=255&e,t+1},l.prototype.writeUInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeUInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeUInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},l.prototype.writeUInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0xffffffff,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeIntLE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var a=0,o=1,l=0;for(this[t]=255&e;++a<r&&(o*=256);)e<0&&0===l&&0!==this[t+a-1]&&(l=1),this[t+a]=(e/o|0)-l&255;return t+r},l.prototype.writeIntBE=function(e,t,r,n){if(e*=1,t>>>=0,!n){var i=Math.pow(2,8*r-1);w(this,e,t,r,i-1,-i)}var a=r-1,o=1,l=0;for(this[t+a]=255&e;--a>=0&&(o*=256);)e<0&&0===l&&0!==this[t+a+1]&&(l=1),this[t+a]=(e/o|0)-l&255;return t+r},l.prototype.writeInt8=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},l.prototype.writeInt16LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},l.prototype.writeInt16BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},l.prototype.writeInt32LE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},l.prototype.writeInt32BE=function(e,t,r){return e*=1,t>>>=0,r||w(this,e,t,4,0x7fffffff,-0x80000000),e<0&&(e=0xffffffff+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},l.prototype.writeFloatLE=function(e,t,r){return O(this,e,t,!0,r)},l.prototype.writeFloatBE=function(e,t,r){return O(this,e,t,!1,r)},l.prototype.writeDoubleLE=function(e,t,r){return j(this,e,t,!0,r)},l.prototype.writeDoubleBE=function(e,t,r){return j(this,e,t,!1,r)},l.prototype.copy=function(e,t,r,n){if(!l.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i=n-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,n);else if(this===e&&r<t&&t<n)for(var a=i-1;a>=0;--a)e[a+t]=this[a+r];else Uint8Array.prototype.set.call(e,this.subarray(r,n),t);return i},l.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!l.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===e.length){var i,a=e.charCodeAt(0);("utf8"===n&&a<128||"latin1"===n)&&(e=a)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(i=t;i<r;++i)this[i]=e;else{var o=l.isBuffer(e)?e:l.from(e,n),u=o.length;if(0===u)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(i=0;i<r-t;++i)this[i+t]=o[i%u]}return this};var P=/[^+/0-9A-Za-z-_]/g;function A(e,t){t=t||1/0;for(var r,n=e.length,i=null,a=[],o=0;o<n;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(t-=3)>-1&&a.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&a.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(t-=3)>-1&&a.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;a.push(r)}else if(r<2048){if((t-=2)<0)break;a.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;a.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;a.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return a}function E(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function M(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(P,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function S(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length)&&!(i>=e.length);++i)t[i+r]=e[i];return i}function _(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var k=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)t[n+i]=e[r]+e[i];return t}()},783:function(e,t){t.read=function(e,t,r,n,i){var a,o,l=8*i-n-1,u=(1<<l)-1,c=u>>1,s=-7,f=r?i-1:0,h=r?-1:1,p=e[t+f];for(f+=h,a=p&(1<<-s)-1,p>>=-s,s+=l;s>0;a=256*a+e[t+f],f+=h,s-=8);for(o=a&(1<<-s)-1,a>>=-s,s+=n;s>0;o=256*o+e[t+f],f+=h,s-=8);if(0===a)a=1-c;else{if(a===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),a-=c}return(p?-1:1)*o*Math.pow(2,a-n)},t.write=function(e,t,r,n,i,a){var o,l,u,c=8*a-i-1,s=(1<<c)-1,f=s>>1,h=5960464477539062e-23*(23===i),p=n?0:a-1,d=n?1:-1,y=+(t<0||0===t&&1/t<0);for(isNaN(t=Math.abs(t))||t===1/0?(l=+!!isNaN(t),o=s):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+f>=1?t+=h/u:t+=h*Math.pow(2,1-f),t*u>=2&&(o++,u/=2),o+f>=s?(l=0,o=s):o+f>=1?(l=(t*u-1)*Math.pow(2,i),o+=f):(l=t*Math.pow(2,f-1)*Math.pow(2,i),o=0));i>=8;e[r+p]=255&l,p+=d,l/=256,i-=8);for(o=o<<i|l,c+=i;c>0;e[r+p]=255&o,p+=d,o/=256,c-=8);e[r+p-d]|=128*y}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,n),o=!1}finally{o&&delete r[e]}return a.exports}n.ab="//",e.exports=n(72)}()},56232:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.identity=function(e){return e}},56318:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.noop=function(){}},56797:(e,t,r)=>{e.exports=r(98612).get},56966:(e,t,r)=>{"use strict";r.d(t,{v:()=>u});var n=r(94513),i=r(22697),a=r(12183),o=r(2923),l=r(33225);let u=(0,o.R)(function(e,t){let r=(0,a.E)();return(0,n.jsx)(l.B.dt,{ref:t,...e,className:(0,i.cx)("chakra-stat__label",e.className),__css:r.label})});u.displayName="StatLabel"},57791:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(65494),i=r(43060),a=r(70307);t.orderBy=function(e,t,r,o){if(null==e)return[];r=o?void 0:r,Array.isArray(e)||(e=Object.values(e)),Array.isArray(t)||(t=null==t?[null]:[t]),0===t.length&&(t=[null]),Array.isArray(r)||(r=null==r?[]:[r]),r=r.map(e=>String(e));let l=(e,t)=>{let r=e;for(let e=0;e<t.length&&null!=r;++e)r=r[t[e]];return r},u=(e,t)=>null==t||null==e?t:"object"==typeof e&&"key"in e?Object.hasOwn(t,e.key)?t[e.key]:l(t,e.path):"function"==typeof e?e(t):Array.isArray(e)?l(t,e):"object"==typeof t?t[e]:t,c=t.map(e=>(Array.isArray(e)&&1===e.length&&(e=e[0]),null==e||"function"==typeof e||Array.isArray(e)||i.isKey(e))?e:{key:e,path:a.toPath(e)});return e.map(e=>({original:e,criteria:c.map(t=>u(t,e))})).slice().sort((e,t)=>{for(let i=0;i<c.length;i++){let a=n.compareValues(e.criteria[i],t.criteria[i],r[i]);if(0!==a)return a}return 0}).map(e=>e.original)}},58139:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(29427),i=r(65084);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(e,t,r,a,l,u,c)=>{if(null!=t&&null!=u){var{chartData:s,computedData:f,dataStartIndex:h,dataEndIndex:p}=r;return e.reduce((e,r)=>{var d,y,v,g,m,{dataDefinedOnItem:b,settings:w}=r,x=function(e,t,r){return Array.isArray(e)&&e&&t+r!==0?e.slice(t,r+1):e}((d=b,y=s,null!=d?d:y),h,p),O=null!=(v=null==w?void 0:w.dataKey)?v:null==a?void 0:a.dataKey,j=null==w?void 0:w.nameKey;return Array.isArray(g=null!=a&&a.dataKey&&Array.isArray(x)&&!Array.isArray(x[0])&&"axis"===c?(0,n.eP)(x,a.dataKey,l):u(x,t,f,j))?g.forEach(t=>{var r=o(o({},w),{},{name:t.name,unit:t.unit,color:void 0,fill:void 0});e.push((0,i.GF)({tooltipEntrySettings:r,dataKey:t.dataKey,payload:t.payload,value:(0,i.kr)(t.payload,t.dataKey),name:t.name}))}):e.push((0,i.GF)({tooltipEntrySettings:w,dataKey:O,payload:g,value:(0,i.kr)(g,O),name:null!=(m=(0,i.kr)(g,j))?m:null==w?void 0:w.name})),e},[])}}},59999:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isUnsafeProperty=function(e){return"__proto__"===e}},60941:(e,t,r)=>{"use strict";r.d(t,{W:()=>a,h:()=>i});var n=r(73474),i=(0,n.Mz)(e=>e.cartesianAxis.xAxis,e=>Object.values(e)),a=(0,n.Mz)(e=>e.cartesianAxis.yAxis,e=>Object.values(e))},61719:(e,t,r)=>{"use strict";r.d(t,{W:()=>b});var n=r(94285),i=r(3638),a=r(41181),o=r(4517),l=r(91648),u=r(96112),c=r(90355),s=r(8569),f=["children"],h=["dangerouslySetInnerHTML","ticks"];function p(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function d(){return(d=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function y(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function v(e){var t=(0,o.j)(),r=(0,n.useMemo)(()=>{var{children:t}=e;return y(e,f)},[e]),i=(0,o.G)(e=>(0,u.Rl)(e,r.id)),a=r===i;return((0,n.useEffect)(()=>(t((0,l.Vi)(r)),()=>{t((0,l.MC)(r))}),[r,t]),a)?e.children:null}var g=e=>{var{xAxisId:t,className:r}=e,l=(0,o.G)(c.c2),f=(0,s.r)(),p="xAxis",v=(0,o.G)(e=>(0,u.iV)(e,p,t,f)),g=(0,o.G)(e=>(0,u.Zi)(e,p,t,f)),m=(0,o.G)(e=>(0,u.Lw)(e,t)),b=(0,o.G)(e=>(0,u.L$)(e,t));if(null==m||null==b)return null;var{dangerouslySetInnerHTML:w,ticks:x}=e,O=y(e,h);return n.createElement(a.u,d({},O,{scale:v,x:b.x,y:b.y,width:m.width,height:m.height,className:(0,i.$)("recharts-".concat(p," ").concat(p),r),viewBox:l,ticks:g}))},m=e=>{var t,r,i,a,o;return n.createElement(v,{interval:null!=(t=e.interval)?t:"preserveEnd",id:e.xAxisId,scale:e.scale,type:e.type,padding:e.padding,allowDataOverflow:e.allowDataOverflow,domain:e.domain,dataKey:e.dataKey,allowDuplicatedCategory:e.allowDuplicatedCategory,allowDecimals:e.allowDecimals,tickCount:e.tickCount,includeHidden:null!=(r=e.includeHidden)&&r,reversed:e.reversed,ticks:e.ticks,height:e.height,orientation:e.orientation,mirror:e.mirror,hide:e.hide,unit:e.unit,name:e.name,angle:null!=(i=e.angle)?i:0,minTickGap:null!=(a=e.minTickGap)?a:5,tick:null==(o=e.tick)||o,tickFormatter:e.tickFormatter},n.createElement(g,e))};class b extends n.Component{render(){return n.createElement(m,this.props)}}p(b,"displayName","XAxis"),p(b,"defaultProps",{allowDataOverflow:u.PU.allowDataOverflow,allowDecimals:u.PU.allowDecimals,allowDuplicatedCategory:u.PU.allowDuplicatedCategory,height:u.PU.height,hide:!1,mirror:u.PU.mirror,orientation:u.PU.orientation,padding:u.PU.padding,reversed:u.PU.reversed,scale:u.PU.scale,tickCount:u.PU.tickCount,type:u.PU.type,xAxisId:0})},62444:(e,t,r)=>{"use strict";r.d(t,{E:()=>i});var n=r(29427),i=(e,t)=>{var r,i=Number(t);if(!(0,n.M8)(i)&&null!=t)return i>=0?null==e||null==(r=e[i])?void 0:r.value:void 0}},62813:(e,t,r)=>{"use strict";r.d(t,{L:()=>R});var n=r(94285),i=r(71998),a=r(21584),o=r(16806),l=r(8569),u=r(3638),c=["children","width","height","viewBox","className","style","title","desc"];function s(){return(s=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var f=(0,n.forwardRef)((e,t)=>{var{children:r,width:a,height:o,viewBox:l,className:f,style:h,title:p,desc:d}=e,y=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,c),v=l||{width:a,height:o,x:0,y:0},g=(0,u.$)("recharts-surface",f);return n.createElement("svg",s({},(0,i.J9)(y,!0,"svg"),{className:g,width:a,height:o,style:h,viewBox:"".concat(v.x," ").concat(v.y," ").concat(v.width," ").concat(v.height),ref:t}),n.createElement("title",null,p),n.createElement("desc",null,d),r)}),h=r(4517),p=r(36714),d=r(38034),y=["children"];function v(){return(v=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var g={width:"100%",height:"100%"},m=(0,n.forwardRef)((e,t)=>{var r,i,l=(0,a.yi)(),u=(0,a.rY)(),c=(0,o.$)();if(!(0,d.F)(l)||!(0,d.F)(u))return null;var{children:s,otherAttributes:h,title:p,desc:y}=e;return r="number"==typeof h.tabIndex?h.tabIndex:c?0:void 0,i="string"==typeof h.role?h.role:c?"application":void 0,n.createElement(f,v({},h,{title:p,desc:y,role:i,tabIndex:r,width:l,height:u,style:g,ref:t}),s)}),b=e=>{var{children:t}=e,r=(0,h.G)(p.U);if(!r)return null;var{width:i,height:a,y:o,x:l}=r;return n.createElement(f,{width:i,height:a,x:l,y:o},t)},w=(0,n.forwardRef)((e,t)=>{var{children:r}=e,i=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,y);return(0,l.r)()?n.createElement(b,null,r):n.createElement(m,v({ref:t},i),r)}),x=r(82284),O=r(20823),j=r(90444),P=r(22695),A=r(23487),E=r(72761),M=r(32449),S=r(71137),_=r(53941),k=(0,n.createContext)(null);function T(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}var C=(0,n.forwardRef)((e,t)=>{var{children:r,className:i,height:a,onClick:o,onContextMenu:l,onDoubleClick:c,onMouseDown:s,onMouseEnter:f,onMouseLeave:p,onMouseMove:y,onMouseUp:v,onTouchEnd:g,onTouchMove:m,onTouchStart:b,style:w,width:C}=e,D=(0,h.j)(),[N,I]=(0,n.useState)(null),[z,L]=(0,n.useState)(null);(0,j.l3)();var R=function(){var e=(0,h.j)(),[t,r]=(0,n.useState)(null),i=(0,h.G)(A.et);return(0,n.useEffect)(()=>{if(null!=t){var r=t.getBoundingClientRect().width/t.offsetWidth;(0,d.H)(r)&&r!==i&&e((0,E.hF)(r))}},[t,e,i]),r}(),B=(0,n.useCallback)(e=>{R(e),"function"==typeof t&&t(e),I(e),L(e)},[R,t,I,L]),U=(0,n.useCallback)(e=>{D((0,O.ky)(e)),D((0,M.y)({handler:o,reactEvent:e}))},[D,o]),F=(0,n.useCallback)(e=>{D((0,O.dj)(e)),D((0,M.y)({handler:f,reactEvent:e}))},[D,f]),K=(0,n.useCallback)(e=>{D((0,x.xS)()),D((0,M.y)({handler:p,reactEvent:e}))},[D,p]),$=(0,n.useCallback)(e=>{D((0,O.dj)(e)),D((0,M.y)({handler:y,reactEvent:e}))},[D,y]),G=(0,n.useCallback)(()=>{D((0,P.Ru)())},[D]),H=(0,n.useCallback)(e=>{D((0,P.uZ)(e.key))},[D]),Z=(0,n.useCallback)(e=>{D((0,M.y)({handler:l,reactEvent:e}))},[D,l]),W=(0,n.useCallback)(e=>{D((0,M.y)({handler:c,reactEvent:e}))},[D,c]),q=(0,n.useCallback)(e=>{D((0,M.y)({handler:s,reactEvent:e}))},[D,s]),V=(0,n.useCallback)(e=>{D((0,M.y)({handler:v,reactEvent:e}))},[D,v]),Y=(0,n.useCallback)(e=>{D((0,M.y)({handler:b,reactEvent:e}))},[D,b]),J=(0,n.useCallback)(e=>{D((0,S.e)(e)),D((0,M.y)({handler:m,reactEvent:e}))},[D,m]),X=(0,n.useCallback)(e=>{D((0,M.y)({handler:g,reactEvent:e}))},[D,g]);return n.createElement(_.$.Provider,{value:N},n.createElement(k.Provider,{value:z},n.createElement("div",{className:(0,u.$)("recharts-wrapper",i),style:function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?T(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):T(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({position:"relative",cursor:"default",width:C,height:a},w),onClick:U,onContextMenu:Z,onDoubleClick:W,onFocus:G,onKeyDown:H,onMouseDown:q,onMouseEnter:F,onMouseLeave:K,onMouseMove:$,onMouseUp:V,onTouchEnd:X,onTouchMove:J,onTouchStart:Y,ref:B},r)))}),D=r(29427),N=r(83844),I=(0,n.createContext)(void 0),z=e=>{var{children:t}=e,[r]=(0,n.useState)("".concat((0,D.NF)("recharts"),"-clip")),i=(0,N.oM)();if(null==i)return null;var{x:a,y:o,width:l,height:u}=i;return n.createElement(I.Provider,{value:r},n.createElement("defs",null,n.createElement("clipPath",{id:r},n.createElement("rect",{x:a,y:o,height:u,width:l}))),t)},L=["children","className","width","height","style","compact","title","desc"],R=(0,n.forwardRef)((e,t)=>{var{children:r,className:a,width:o,height:l,style:u,compact:c,title:s,desc:f}=e,h=function(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}(e,L),p=(0,i.J9)(h,!1);return c?n.createElement(w,{otherAttributes:p,title:s,desc:f},r):n.createElement(C,{className:a,style:u,width:o,height:l,onClick:e.onClick,onMouseLeave:e.onMouseLeave,onMouseEnter:e.onMouseEnter,onMouseMove:e.onMouseMove,onMouseDown:e.onMouseDown,onMouseUp:e.onMouseUp,onContextMenu:e.onContextMenu,onDoubleClick:e.onDoubleClick,onTouchStart:e.onTouchStart,onTouchMove:e.onTouchMove,onTouchEnd:e.onTouchEnd},n.createElement(w,{otherAttributes:p,title:s,desc:f,ref:t},n.createElement(z,null,r)))})},63074:(e,t,r)=>{e.exports=r(45505).isPlainObject},63449:(e,t,r)=>{"use strict";e.exports=r(37973)},63700:(e,t,r)=>{"use strict";r.d(t,{q:()=>n});var n=(e,t,r,n)=>{var i;return"axis"===t?e.tooltipItemPayloads:0===e.tooltipItemPayloads.length?[]:null==(i="hover"===r?e.itemInteraction.hover.dataKey:e.itemInteraction.click.dataKey)&&null!=n?[e.tooltipItemPayloads[0]]:e.tooltipItemPayloads.filter(e=>{var t;return(null==(t=e.settings)?void 0:t.dataKey)===i})}},64172:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(76943);t.isArrayLike=function(e){return null!=e&&"function"!=typeof e&&n.isLength(e.length)}},64865:(e,t,r)=>{"use strict";r.d(t,{J:()=>j,Z:()=>g});var n=r(94285),i=r(3638),a=r(20617),o=r(71998),l=r(29427),u=r(15143),c=r(21584),s=["offset"],f=["labelRef"];function h(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function y(){return(y=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var v=e=>{var{value:t,formatter:r}=e,n=(0,l.uy)(e.children)?t:e.children;return"function"==typeof r?r(n):n},g=e=>null!=e&&"function"==typeof e,m=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),360),b=(e,t,r)=>{var a,o,{position:c,viewBox:s,offset:f,className:h}=e,{cx:p,cy:d,innerRadius:v,outerRadius:g,startAngle:b,endAngle:w,clockWise:x}=s,O=(v+g)/2,j=m(b,w),P=j>=0?1:-1;"insideStart"===c?(a=b+P*f,o=x):"insideEnd"===c?(a=w-P*f,o=!x):"end"===c&&(a=w+P*f,o=x),o=j<=0?o:!o;var A=(0,u.IZ)(p,d,O,a),E=(0,u.IZ)(p,d,O,a+(o?1:-1)*359),M="M".concat(A.x,",").concat(A.y,"\n    A").concat(O,",").concat(O,",0,1,").concat(+!o,",\n    ").concat(E.x,",").concat(E.y),S=(0,l.uy)(e.id)?(0,l.NF)("recharts-radial-line-"):e.id;return n.createElement("text",y({},r,{dominantBaseline:"central",className:(0,i.$)("recharts-radial-bar-label",h)}),n.createElement("defs",null,n.createElement("path",{id:S,d:M})),n.createElement("textPath",{xlinkHref:"#".concat(S)},t))},w=e=>{var{viewBox:t,offset:r,position:n}=e,{cx:i,cy:a,innerRadius:o,outerRadius:l,startAngle:c,endAngle:s}=t,f=(c+s)/2;if("outside"===n){var{x:h,y:p}=(0,u.IZ)(i,a,l+r,f);return{x:h,y:p,textAnchor:h>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:a,textAnchor:"middle",verticalAnchor:"end"};var{x:d,y}=(0,u.IZ)(i,a,(o+l)/2,f);return{x:d,y,textAnchor:"middle",verticalAnchor:"middle"}},x=(e,t)=>{var{parentViewBox:r,offset:n,position:i}=e,{x:a,y:o,width:u,height:c}=t,s=c>=0?1:-1,f=s*n,h=s>0?"end":"start",p=s>0?"start":"end",y=u>=0?1:-1,v=y*n,g=y>0?"end":"start",m=y>0?"start":"end";if("top"===i)return d(d({},{x:a+u/2,y:o-s*n,textAnchor:"middle",verticalAnchor:h}),r?{height:Math.max(o-r.y,0),width:u}:{});if("bottom"===i)return d(d({},{x:a+u/2,y:o+c+f,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(o+c),0),width:u}:{});if("left"===i){var b={x:a-v,y:o+c/2,textAnchor:g,verticalAnchor:"middle"};return d(d({},b),r?{width:Math.max(b.x-r.x,0),height:c}:{})}if("right"===i){var w={x:a+u+v,y:o+c/2,textAnchor:m,verticalAnchor:"middle"};return d(d({},w),r?{width:Math.max(r.x+r.width-w.x,0),height:c}:{})}var x=r?{width:u,height:c}:{};return"insideLeft"===i?d({x:a+v,y:o+c/2,textAnchor:m,verticalAnchor:"middle"},x):"insideRight"===i?d({x:a+u-v,y:o+c/2,textAnchor:g,verticalAnchor:"middle"},x):"insideTop"===i?d({x:a+u/2,y:o+f,textAnchor:"middle",verticalAnchor:p},x):"insideBottom"===i?d({x:a+u/2,y:o+c-f,textAnchor:"middle",verticalAnchor:h},x):"insideTopLeft"===i?d({x:a+v,y:o+f,textAnchor:m,verticalAnchor:p},x):"insideTopRight"===i?d({x:a+u-v,y:o+f,textAnchor:g,verticalAnchor:p},x):"insideBottomLeft"===i?d({x:a+v,y:o+c-f,textAnchor:m,verticalAnchor:h},x):"insideBottomRight"===i?d({x:a+u-v,y:o+c-f,textAnchor:g,verticalAnchor:h},x):i&&"object"==typeof i&&((0,l.Et)(i.x)||(0,l._3)(i.x))&&((0,l.Et)(i.y)||(0,l._3)(i.y))?d({x:a+(0,l.F4)(i.x,u),y:o+(0,l.F4)(i.y,c),textAnchor:"end",verticalAnchor:"end"},x):d({x:a+u/2,y:o+c/2,textAnchor:"middle",verticalAnchor:"middle"},x)},O=e=>"cx"in e&&(0,l.Et)(e.cx);function j(e){var t,{offset:r=5}=e,u=d({offset:r},h(e,s)),{viewBox:p,position:g,value:m,children:j,content:P,className:A="",textBreakAll:E,labelRef:M}=u,S=(0,c.sk)(),_=p||S;if(!_||(0,l.uy)(m)&&(0,l.uy)(j)&&!(0,n.isValidElement)(P)&&"function"!=typeof P)return null;if((0,n.isValidElement)(P)){var{labelRef:k}=u,T=h(u,f);return(0,n.cloneElement)(P,T)}if("function"==typeof P){if(t=(0,n.createElement)(P,u),(0,n.isValidElement)(t))return t}else t=v(u);var C=O(_),D=(0,o.J9)(u,!0);if(C&&("insideStart"===g||"insideEnd"===g||"end"===g))return b(u,t,D);var N=C?w(u):x(u,_);return n.createElement(a.E,y({ref:M,className:(0,i.$)("recharts-label",A)},D,N,{breakAll:E}),t)}j.displayName="Label";var P=e=>{var{cx:t,cy:r,angle:n,startAngle:i,endAngle:a,r:o,radius:u,innerRadius:c,outerRadius:s,x:f,y:h,top:p,left:d,width:y,height:v,clockWise:g,labelViewBox:m}=e;if(m)return m;if((0,l.Et)(y)&&(0,l.Et)(v)){if((0,l.Et)(f)&&(0,l.Et)(h))return{x:f,y:h,width:y,height:v};if((0,l.Et)(p)&&(0,l.Et)(d))return{x:p,y:d,width:y,height:v}}return(0,l.Et)(f)&&(0,l.Et)(h)?{x:f,y:h,width:0,height:0}:(0,l.Et)(t)&&(0,l.Et)(r)?{cx:t,cy:r,startAngle:i||n||0,endAngle:a||n||0,innerRadius:c||0,outerRadius:s||u||o||0,clockWise:g}:e.viewBox?e.viewBox:void 0},A=(e,t,r)=>{if(!e)return null;var i={viewBox:t,labelRef:r};return!0===e?n.createElement(j,y({key:"label-implicit"},i)):(0,l.vh)(e)?n.createElement(j,y({key:"label-implicit",value:e},i)):(0,n.isValidElement)(e)?e.type===j?(0,n.cloneElement)(e,d({key:"label-implicit"},i)):n.createElement(j,y({key:"label-implicit",content:e},i)):g(e)?n.createElement(j,y({key:"label-implicit",content:e},i)):e&&"object"==typeof e?n.createElement(j,y({},e,{key:"label-implicit"},i)):null};j.parseViewBox=P,j.renderCallByParent=function(e,t){var r=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!e||!e.children&&r&&!e.label)return null;var{children:i,labelRef:a}=e,l=P(e),u=(0,o.aS)(i,j).map((e,r)=>(0,n.cloneElement)(e,{viewBox:t||l,key:"label-".concat(r)}));return r?[A(e.label,t||l,a),...u]:u}},64963:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(57791),i=r(68331),a=r(52312);t.sortBy=function(e,...t){let r=t.length;return r>1&&a.isIterateeCall(e,t[0],t[1])?t=[]:r>2&&a.isIterateeCall(t[0],t[1],t[2])&&(t=[t[0]]),n.orderBy(e,i.flatten(t),["asc"])}},65084:(e,t,r)=>{"use strict";r.d(t,{qx:()=>N,IH:()=>D,s0:()=>b,gH:()=>m,SW:()=>U,YB:()=>j,bk:()=>B,Hj:()=>I,DW:()=>_,y2:()=>S,PW:()=>x,Mk:()=>C,$8:()=>M,yy:()=>E,Rh:()=>O,GF:()=>z,uM:()=>L,kr:()=>g,r4:()=>R,_L:()=>w,_f:()=>P});var n=r(10014),i=r.n(n),a=r(56797),o=r.n(a);function l(e,t){if((i=e.length)>1)for(var r,n,i,a=1,o=e[t[0]],l=o.length;a<i;++a)for(n=o,o=e[t[a]],r=0;r<l;++r)o[r][1]+=o[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}var u=r(95737),c=r(31896);function s(e){for(var t=e.length,r=Array(t);--t>=0;)r[t]=t;return r}function f(e,t){return e[t]}function h(e){let t=[];return t.key=e,t}var p=r(29427),d=r(15143);function y(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function v(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?y(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):y(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function g(e,t,r){return(0,p.uy)(e)||(0,p.uy)(t)?r:(0,p.vh)(t)?o()(e,t,r):"function"==typeof t?t(e):r}var m=(e,t,r,n,i)=>{var a,o=-1,l=null!=(a=null==t?void 0:t.length)?a:0;if(l<=1||null==e)return 0;if("angleAxis"===n&&null!=i&&1e-6>=Math.abs(Math.abs(i[1]-i[0])-360))for(var u=0;u<l;u++){var c=u>0?r[u-1].coordinate:r[l-1].coordinate,s=r[u].coordinate,f=u>=l-1?r[0].coordinate:r[u+1].coordinate,h=void 0;if((0,p.sA)(s-c)!==(0,p.sA)(f-s)){var d=[];if((0,p.sA)(f-s)===(0,p.sA)(i[1]-i[0])){h=f;var y=s+i[1]-i[0];d[0]=Math.min(y,(y+c)/2),d[1]=Math.max(y,(y+c)/2)}else{h=c;var v=f+i[1]-i[0];d[0]=Math.min(s,(v+s)/2),d[1]=Math.max(s,(v+s)/2)}var g=[Math.min(s,(h+s)/2),Math.max(s,(h+s)/2)];if(e>g[0]&&e<=g[1]||e>=d[0]&&e<=d[1]){({index:o}=r[u]);break}}else{var m=Math.min(c,f),b=Math.max(c,f);if(e>(m+s)/2&&e<=(b+s)/2){({index:o}=r[u]);break}}}else if(t){for(var w=0;w<l;w++)if(0===w&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w>0&&w<l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2&&e<=(t[w].coordinate+t[w+1].coordinate)/2||w===l-1&&e>(t[w].coordinate+t[w-1].coordinate)/2){({index:o}=t[w]);break}}return o},b=(e,t,r)=>{if(t&&r){var{width:n,height:i}=r,{align:a,verticalAlign:o,layout:l}=t;if(("vertical"===l||"horizontal"===l&&"middle"===o)&&"center"!==a&&(0,p.Et)(e[a]))return v(v({},e),{},{[a]:e[a]+(n||0)});if(("horizontal"===l||"vertical"===l&&"center"===a)&&"middle"!==o&&(0,p.Et)(e[o]))return v(v({},e),{},{[o]:e[o]+(i||0)})}return e},w=(e,t)=>"horizontal"===e&&"xAxis"===t||"vertical"===e&&"yAxis"===t||"centric"===e&&"angleAxis"===t||"radial"===e&&"radiusAxis"===t,x=(e,t,r,n)=>{if(n)return e.map(e=>e.coordinate);var i,a,o=e.map(e=>(e.coordinate===t&&(i=!0),e.coordinate===r&&(a=!0),e.coordinate));return i||o.push(t),a||o.push(r),o},O=(e,t,r)=>{if(!e)return null;var{duplicateDomain:n,type:i,range:a,scale:o,realScaleType:l,isCategorical:u,categoricalDomain:c,tickCount:s,ticks:f,niceTicks:h,axisType:d}=e;if(!o)return null;var y="scaleBand"===l&&o.bandwidth?o.bandwidth()/2:2,v=(t||r)&&"category"===i&&o.bandwidth?o.bandwidth()/y:0;return(v="angleAxis"===d&&a&&a.length>=2?2*(0,p.sA)(a[0]-a[1])*v:v,t&&(f||h))?(f||h||[]).map((e,t)=>({coordinate:o(n?n.indexOf(e):e)+v,value:e,offset:v,index:t})).filter(e=>!(0,p.M8)(e.coordinate)):u&&c?c.map((e,t)=>({coordinate:o(e)+v,value:e,index:t,offset:v})):o.ticks&&!r&&null!=s?o.ticks(s).map((e,t)=>({coordinate:o(e)+v,value:e,offset:v,index:t})):o.domain().map((e,t)=>({coordinate:o(e)+v,value:n?n[e]:e,index:t,offset:v}))},j=e=>{var t=e.domain();if(t&&!(t.length<=2)){var r=t.length,n=e.range(),i=Math.min(n[0],n[1])-1e-4,a=Math.max(n[0],n[1])+1e-4,o=e(t[0]),l=e(t[r-1]);(o<i||o>a||l<i||l>a)&&e.domain([t[0],t[r-1]])}},P=(e,t)=>{if(!t||2!==t.length||!(0,p.Et)(t[0])||!(0,p.Et)(t[1]))return e;var r=Math.min(t[0],t[1]),n=Math.max(t[0],t[1]),i=[e[0],e[1]];return(!(0,p.Et)(e[0])||e[0]<r)&&(i[0]=r),(!(0,p.Et)(e[1])||e[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},A={sign:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0,o=0;o<t;++o){var l=(0,p.M8)(e[o][r][1])?e[o][r][0]:e[o][r][1];l>=0?(e[o][r][0]=i,e[o][r][1]=i+l,i=e[o][r][1]):(e[o][r][0]=a,e[o][r][1]=a+l,a=e[o][r][1])}},expand:function(e,t){if((n=e.length)>0){for(var r,n,i,a=0,o=e[0].length;a<o;++a){for(i=r=0;r<n;++r)i+=e[r][a][1]||0;if(i)for(r=0;r<n;++r)e[r][a][1]/=i}l(e,t)}},none:l,silhouette:function(e,t){if((r=e.length)>0){for(var r,n=0,i=e[t[0]],a=i.length;n<a;++n){for(var o=0,u=0;o<r;++o)u+=e[o][n][1]||0;i[n][1]+=i[n][0]=-u/2}l(e,t)}},wiggle:function(e,t){if((i=e.length)>0&&(n=(r=e[t[0]]).length)>0){for(var r,n,i,a=0,o=1;o<n;++o){for(var u=0,c=0,s=0;u<i;++u){for(var f=e[t[u]],h=f[o][1]||0,p=(h-(f[o-1][1]||0))/2,d=0;d<u;++d){var y=e[t[d]];p+=(y[o][1]||0)-(y[o-1][1]||0)}c+=h,s+=p*h}r[o-1][1]+=r[o-1][0]=a,c&&(a-=s/c)}r[o-1][1]+=r[o-1][0]=a,l(e,t)}},positive:e=>{var t=e.length;if(!(t<=0))for(var r=0,n=e[0].length;r<n;++r)for(var i=0,a=0;a<t;++a){var o=(0,p.M8)(e[a][r][1])?e[a][r][0]:e[a][r][1];o>=0?(e[a][r][0]=i,e[a][r][1]=i+o,i=e[a][r][1]):(e[a][r][0]=0,e[a][r][1]=0)}}},E=(e,t,r)=>{var n=A[r];return(function(){var e=(0,c.A)([]),t=s,r=l,n=f;function i(i){var a,o,l=Array.from(e.apply(this,arguments),h),c=l.length,s=-1;for(let e of i)for(a=0,++s;a<c;++a)(l[a][s]=[0,+n(e,l[a].key,s,i)]).data=e;for(a=0,o=(0,u.A)(t(l));a<c;++a)l[o[a]].index=a;return r(l,o),l}return i.keys=function(t){return arguments.length?(e="function"==typeof t?t:(0,c.A)(Array.from(t)),i):e},i.value=function(e){return arguments.length?(n="function"==typeof e?e:(0,c.A)(+e),i):n},i.order=function(e){return arguments.length?(t=null==e?s:"function"==typeof e?e:(0,c.A)(Array.from(e)),i):t},i.offset=function(e){return arguments.length?(r=null==e?l:e,i):r},i})().keys(t).value((e,t)=>+g(e,t,0)).order(s).offset(n)(e)};function M(e){return null==e?void 0:String(e)}var S=e=>{var{axis:t,ticks:r,offset:n,bandSize:i,entry:a,index:o}=e;if("category"===t.type)return r[o]?r[o].coordinate+n:null;var l=g(a,t.dataKey,t.scale.domain()[o]);return(0,p.uy)(l)?null:t.scale(l)-i/2+n},_=e=>{var{numericAxis:t}=e,r=t.scale.domain();if("number"===t.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},k=e=>{var t=e.flat(2).filter(p.Et);return[Math.min(...t),Math.max(...t)]},T=e=>[e[0]===1/0?0:e[0],e[1]===-1/0?0:e[1]],C=(e,t,r)=>{if(null!=e)return T(Object.keys(e).reduce((n,i)=>{var{stackedData:a}=e[i],o=a.reduce((e,n)=>{var i=k(n.slice(t,r+1));return[Math.min(e[0],i[0]),Math.max(e[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]))},D=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,N=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,I=(e,t,r)=>{if(e&&e.scale&&e.scale.bandwidth){var n=e.scale.bandwidth();if(!r||n>0)return n}if(e&&t&&t.length>=2){for(var a=i()(t,e=>e.coordinate),o=1/0,l=1,u=a.length;l<u;l++){var c=a[l],s=a[l-1];o=Math.min((c.coordinate||0)-(s.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0};function z(e){var{tooltipEntrySettings:t,dataKey:r,payload:n,value:i,name:a}=e;return v(v({},t),{},{dataKey:r,payload:n,value:i,name:a})}function L(e,t){return e?String(e):"string"==typeof t?t:void 0}function R(e,t,r,n,i){return"horizontal"===r||"vertical"===r?e>=i.left&&e<=i.left+i.width&&t>=i.top&&t<=i.top+i.height?{x:e,y:t}:null:n?(0,d.yy)({x:e,y:t},n):null}var B=(e,t,r,n)=>{var i=t.find(e=>e&&e.index===r);if(i){if("horizontal"===e)return{x:i.coordinate,y:n.y};if("vertical"===e)return{x:n.x,y:i.coordinate};if("centric"===e){var a=i.coordinate,{radius:o}=n;return v(v(v({},n),(0,d.IZ)(n.cx,n.cy,o,a)),{},{angle:a,radius:o})}var l=i.coordinate,{angle:u}=n;return v(v(v({},n),(0,d.IZ)(n.cx,n.cy,l,u)),{},{angle:u,radius:l})}return{x:0,y:0}},U=(e,t)=>"horizontal"===t?e.x:"vertical"===t?e.y:"centric"===t?e.angle:e.radius},65494:(e,t)=>{"use strict";function r(e){return"symbol"==typeof e?1:null===e?2:void 0===e?3:4*(e!=e)}Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.compareValues=(e,t,n)=>{if(e!==t){let i=r(e),a=r(t);if(i===a&&0===i){if(e<t)return"desc"===n?1:-1;if(e>t)return"desc"===n?-1:1}return"desc"===n?a-i:i-a}return 0}},65738:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isSymbol=function(e){return"symbol"==typeof e||e instanceof Symbol}},67628:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.argumentsTag="[object Arguments]",t.arrayBufferTag="[object ArrayBuffer]",t.arrayTag="[object Array]",t.bigInt64ArrayTag="[object BigInt64Array]",t.bigUint64ArrayTag="[object BigUint64Array]",t.booleanTag="[object Boolean]",t.dataViewTag="[object DataView]",t.dateTag="[object Date]",t.errorTag="[object Error]",t.float32ArrayTag="[object Float32Array]",t.float64ArrayTag="[object Float64Array]",t.functionTag="[object Function]",t.int16ArrayTag="[object Int16Array]",t.int32ArrayTag="[object Int32Array]",t.int8ArrayTag="[object Int8Array]",t.mapTag="[object Map]",t.numberTag="[object Number]",t.objectTag="[object Object]",t.regexpTag="[object RegExp]",t.setTag="[object Set]",t.stringTag="[object String]",t.symbolTag="[object Symbol]",t.uint16ArrayTag="[object Uint16Array]",t.uint32ArrayTag="[object Uint32Array]",t.uint8ArrayTag="[object Uint8Array]",t.uint8ClampedArrayTag="[object Uint8ClampedArray]"},68331:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.flatten=function(e,t=1){let r=[],n=Math.floor(t),i=(e,t)=>{for(let a=0;a<e.length;a++){let o=e[a];Array.isArray(o)&&t<n?i(o,t+1):r.push(o)}};return i(e,0),r}},68645:(e,t,r)=>{"use strict";r.d(t,{LV:()=>l,M:()=>a,hq:()=>i});var n=(0,r(68646).Z0)({name:"chartData",initialState:{chartData:void 0,computedData:void 0,dataStartIndex:0,dataEndIndex:0},reducers:{setChartData(e,t){if(e.chartData=t.payload,null==t.payload){e.dataStartIndex=0,e.dataEndIndex=0;return}t.payload.length>0&&e.dataEndIndex!==t.payload.length-1&&(e.dataEndIndex=t.payload.length-1)},setComputedData(e,t){e.computedData=t.payload},setDataStartEndIndexes(e,t){var{startIndex:r,endIndex:n}=t.payload;null!=r&&(e.dataStartIndex=r),null!=n&&(e.dataEndIndex=n)}}}),{setChartData:i,setDataStartEndIndexes:a,setComputedData:o}=n.actions,l=n.reducer},68646:(e,t,r)=>{"use strict";r.d(t,{U1:()=>m,VP:()=>c,Nc:()=>ey,Z0:()=>T});var n=r(51556);function i(e){return({dispatch:t,getState:r})=>n=>i=>"function"==typeof i?i(t,r,e):n(i)}var a=i(),o=r(74201),l=(r(2209),"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!=arguments.length)return"object"==typeof arguments[0]?n.Zz:n.Zz.apply(null,arguments)});"undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;var u=e=>e&&"function"==typeof e.match;function c(e,t){function r(...n){if(t){let r=t(...n);if(!r)throw Error(ex(0));return{type:e,payload:r.payload,..."meta"in r&&{meta:r.meta},..."error"in r&&{error:r.error}}}return{type:e,payload:n[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>(0,n.ve)(t)&&t.type===e,r}function s(e){return["type","payload","error","meta"].indexOf(e)>-1}var f=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function h(e){return(0,o.a6)(e)?(0,o.jM)(e,()=>{}):e}function p(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var d=()=>function(e){let{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{},l=new f;return t&&("boolean"==typeof t?l.push(a):l.push(i(t.extraArgument))),l},y=e=>t=>{setTimeout(t,e)},v=(e={type:"raf"})=>t=>(...r)=>{let n=t(...r),i=!0,a=!1,o=!1,l=new Set,u="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:y(10):"callback"===e.type?e.queueNotification:y(e.timeout),c=()=>{o=!1,a&&(a=!1,l.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){let t=n.subscribe(()=>i&&e());return l.add(e),()=>{t(),l.delete(e)}},dispatch(e){try{return(a=!(i=!e?.meta?.RTK_autoBatch))&&!o&&(o=!0,u(c)),n.dispatch(e)}finally{i=!0}}})},g=e=>function(t){let{autoBatch:r=!0}=t??{},n=new f(e);return r&&n.push(v("object"==typeof r?r:void 0)),n};function m(e){let t,r,i=d(),{reducer:a,middleware:o,devTools:u=!0,duplicateMiddlewareCheck:c=!0,preloadedState:s,enhancers:f}=e||{};if("function"==typeof a)t=a;else if((0,n.Qd)(a))t=(0,n.HY)(a);else throw Error(ex(1));r="function"==typeof o?o(i):i();let h=n.Zz;u&&(h=l({trace:!1,..."object"==typeof u&&u}));let p=g((0,n.Tw)(...r)),y=h(..."function"==typeof f?f(p):p());return(0,n.y$)(t,s,y)}function b(e){let t,r={},n=[],i={addCase(e,t){let n="string"==typeof e?e:e.type;if(!n)throw Error(ex(28));if(n in r)throw Error(ex(29));return r[n]=t,i},addMatcher:(e,t)=>(n.push({matcher:e,reducer:t}),i),addDefaultCase:e=>(t=e,i)};return e(i),[r,n,t]}var w=(e,t)=>u(e)?e.match(t):e(t);function x(...e){return t=>e.some(e=>w(e,t))}var O=(e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},j=["name","message","stack","code"],P=class{constructor(e,t){this.payload=e,this.meta=t}_type},A=class{constructor(e,t){this.payload=e,this.meta=t}_type},E=e=>{if("object"==typeof e&&null!==e){let t={};for(let r of j)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},M="External signal was aborted";function S(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var _=Symbol.for("rtk-slice-createasyncthunk"),k=(e=>(e.reducer="reducer",e.reducerWithPrepare="reducerWithPrepare",e.asyncThunk="asyncThunk",e))(k||{}),T=function({creators:e}={}){let t=e?.asyncThunk?.[_];return function(e){let r,{name:n,reducerPath:i=n}=e;if(!n)throw Error(ex(11));let a=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},l=Object.keys(a),u={},s={},f={},d=[],y={addCase(e,t){let r="string"==typeof e?e:e.type;if(!r)throw Error(ex(12));if(r in s)throw Error(ex(13));return s[r]=t,y},addMatcher:(e,t)=>(d.push({matcher:e,reducer:t}),y),exposeAction:(e,t)=>(f[e]=t,y),exposeCaseReducer:(e,t)=>(u[e]=t,y)};function v(){let[t={},r=[],n]="function"==typeof e.extraReducers?b(e.extraReducers):[e.extraReducers],i={...t,...s};return function(e,t){let r,[n,i,a]=b(t);if("function"==typeof e)r=()=>h(e());else{let t=h(e);r=()=>t}function l(e=r(),t){let u=[n[t.type],...i.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===u.filter(e=>!!e).length&&(u=[a]),u.reduce((e,r)=>{if(r)if((0,o.Qx)(e)){let n=r(e,t);return void 0===n?e:n}else{if((0,o.a6)(e))return(0,o.jM)(e,e=>r(e,t));let n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}return e},e)}return l.getInitialState=r,l}(e.initialState,e=>{for(let t in i)e.addCase(t,i[t]);for(let t of d)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}l.forEach(r=>{let i=a[r],o={reducerName:r,type:`${n}/${r}`,createNotation:"function"==typeof e.reducers};"asyncThunk"===i._reducerDefinitionType?function({type:e,reducerName:t},r,n,i){if(!i)throw Error(ex(18));let{payloadCreator:a,fulfilled:o,pending:l,rejected:u,settled:c,options:s}=r,f=i(e,a,s);n.exposeAction(t,f),o&&n.addCase(f.fulfilled,o),l&&n.addCase(f.pending,l),u&&n.addCase(f.rejected,u),c&&n.addMatcher(f.settled,c),n.exposeCaseReducer(t,{fulfilled:o||C,pending:l||C,rejected:u||C,settled:c||C})}(o,i,y,t):function({type:e,reducerName:t,createNotation:r},n,i){let a,o;if("reducer"in n){if(r&&"reducerWithPrepare"!==n._reducerDefinitionType)throw Error(ex(17));a=n.reducer,o=n.prepare}else a=n;i.addCase(e,a).exposeCaseReducer(t,a).exposeAction(t,o?c(e,o):c(e))}(o,i,y)});let g=e=>e,m=new Map,w=new WeakMap;function x(e,t){return r||(r=v()),r(e,t)}function O(){return r||(r=v()),r.getInitialState()}function j(t,r=!1){function n(e){let i=e[t];return void 0===i&&r&&(i=p(w,n,O)),i}function i(t=g){let n=p(m,r,()=>new WeakMap);return p(n,t,()=>{let n={};for(let[i,a]of Object.entries(e.selectors??{}))n[i]=function(e,t,r,n){function i(a,...o){let l=t(a);return void 0===l&&n&&(l=r()),e(l,...o)}return i.unwrapped=e,i}(a,t,()=>p(w,t,O),r);return n})}return{reducerPath:t,getSelectors:i,get selectors(){return i(n)},selectSlice:n}}let P={name:n,reducer:x,actions:f,caseReducers:u,getInitialState:O,...j(i),injectInto(e,{reducerPath:t,...r}={}){let n=t??i;return e.inject({reducerPath:n,reducer:x},r),{...P,...j(n,!0)}}};return P}}();function C(){}function D(e){return function(t,r){let n=t=>{isAction(r)&&Object.keys(r).every(s)?e(r.payload,t):e(r,t)};return(null)(t)?(n(t),t):createNextState3(t,n)}}function N(e,t){return t(e)}function I(e){return Array.isArray(e)||(e=Object.values(e)),e}var z="listener",L="completed",R="cancelled",B=`task-${R}`,U=`task-${L}`,F=`${z}-${R}`,K=`${z}-${L}`,$=class{constructor(e){this.code=e,this.message=`task ${R} (reason: ${e})`}name="TaskAbortError";message},G=(e,t)=>{if("function"!=typeof e)throw TypeError(ex(32))},H=()=>{},Z=(e,t=H)=>(e.catch(t),e),W=(e,t)=>(e.addEventListener("abort",t,{once:!0}),()=>e.removeEventListener("abort",t)),q=(e,t)=>{let r=e.signal;r.aborted||("reason"in r||Object.defineProperty(r,"reason",{enumerable:!0,value:t,configurable:!0,writable:!0}),e.abort(t))},V=e=>{if(e.aborted){let{reason:t}=e;throw new $(t)}};function Y(e,t){let r=H;return new Promise((n,i)=>{let a=()=>i(new $(e.reason));if(e.aborted)return void a();r=W(e,a),t.finally(()=>r()).then(n,i)}).finally(()=>{r=H})}var J=async(e,t)=>{try{await Promise.resolve();let t=await e();return{status:"ok",value:t}}catch(e){return{status:e instanceof $?"cancelled":"rejected",error:e}}finally{t?.()}},X=e=>t=>Z(Y(e,t).then(t=>(V(e),t))),Q=e=>{let t=X(e);return e=>t(new Promise(t=>setTimeout(t,e)))},{assign:ee}=Object,et={},er="listenerMiddleware",en=(e,t)=>{let r=t=>W(e,()=>q(t,e.reason));return(n,i)=>{G(n,"taskExecutor");let a=new AbortController;r(a);let o=J(async()=>{V(e),V(a.signal);let t=await n({pause:X(a.signal),delay:Q(a.signal),signal:a.signal});return V(a.signal),t},()=>q(a,U));return i?.autoJoin&&t.push(o.catch(H)),{result:X(e)(o),cancel(){q(a,B)}}}},ei=(e,t)=>{let r=async(r,n)=>{V(t);let i=()=>{},a=[new Promise((t,n)=>{let a=e({predicate:r,effect:(e,r)=>{r.unsubscribe(),t([e,r.getState(),r.getOriginalState()])}});i=()=>{a(),n()}})];null!=n&&a.push(new Promise(e=>setTimeout(e,n,null)));try{let e=await Y(t,Promise.race(a));return V(t),e}finally{i()}};return(e,t)=>Z(r(e,t))},ea=e=>{let{type:t,actionCreator:r,matcher:n,predicate:i,effect:a}=e;if(t)i=c(t).match;else if(r)t=r.type,i=r.match;else if(n)i=n;else if(i);else throw Error(ex(21));return G(a,"options.listener"),{predicate:i,type:t,effect:a}},eo=ee(e=>{let{type:t,predicate:r,effect:n}=ea(e);return{id:O(),effect:n,type:t,predicate:r,pending:new Set,unsubscribe:()=>{throw Error(ex(22))}}},{withTypes:()=>eo}),el=(e,t)=>{let{type:r,effect:n,predicate:i}=ea(t);return Array.from(e.values()).find(e=>("string"==typeof r?e.type===r:e.predicate===i)&&e.effect===n)},eu=e=>{e.pending.forEach(e=>{q(e,F)})},ec=e=>()=>{e.forEach(eu),e.clear()},es=(e,t,r)=>{try{e(t,r)}catch(e){setTimeout(()=>{throw e},0)}},ef=ee(c(`${er}/add`),{withTypes:()=>ef}),eh=c(`${er}/removeAll`),ep=ee(c(`${er}/remove`),{withTypes:()=>ep}),ed=(...e)=>{console.error(`${er}/error`,...e)},ey=(e={})=>{let t=new Map,{extra:r,onError:i=ed}=e;G(i,"onError");let a=e=>(e.unsubscribe=()=>t.delete(e.id),t.set(e.id,e),t=>{e.unsubscribe(),t?.cancelActive&&eu(e)}),o=e=>a(el(t,e)??eo(e));ee(o,{withTypes:()=>o});let l=e=>{let r=el(t,e);return r&&(r.unsubscribe(),e.cancelActive&&eu(r)),!!r};ee(l,{withTypes:()=>l});let u=async(e,n,a,l)=>{let u=new AbortController,c=ei(o,u.signal),s=[];try{e.pending.add(u),await Promise.resolve(e.effect(n,ee({},a,{getOriginalState:l,condition:(e,t)=>c(e,t).then(Boolean),take:c,delay:Q(u.signal),pause:X(u.signal),extra:r,signal:u.signal,fork:en(u.signal,s),unsubscribe:e.unsubscribe,subscribe:()=>{t.set(e.id,e)},cancelActiveListeners:()=>{e.pending.forEach((e,t,r)=>{e!==u&&(q(e,F),r.delete(e))})},cancel:()=>{q(u,F),e.pending.delete(u)},throwIfCancelled:()=>{V(u.signal)}})))}catch(e){e instanceof $||es(i,e,{raisedBy:"effect"})}finally{await Promise.all(s),q(u,K),e.pending.delete(u)}},c=ec(t);return{middleware:e=>r=>a=>{let s;if(!(0,n.ve)(a))return r(a);if(ef.match(a))return o(a.payload);if(eh.match(a))return void c();if(ep.match(a))return l(a.payload);let f=e.getState(),h=()=>{if(f===et)throw Error(ex(23));return f};try{if(s=r(a),t.size>0){let r=e.getState();for(let n of Array.from(t.values())){let t=!1;try{t=n.predicate(a,r,f)}catch(e){t=!1,es(i,e,{raisedBy:"predicate"})}t&&u(n,a,e,h)}}}finally{f=et}return s},startListening:o,stopListening:l,clearListeners:c}},ev=e=>"reducerPath"in e&&"string"==typeof e.reducerPath,eg=Symbol.for("rtk-state-proxy-original"),em=e=>!!e&&!!e[eg],eb=new WeakMap,ew={};function ex(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}},68810:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var n=r(82284);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o=(e,t,r,i)=>{if(null==t)return n.k_;var o=function(e,t,r){return"axis"===t?"click"===r?e.axisInteraction.click:e.axisInteraction.hover:"click"===r?e.itemInteraction.click:e.itemInteraction.hover}(e,t,r);if(null==o)return n.k_;if(o.active)return o;if(e.keyboardInteraction.active)return e.keyboardInteraction;if(e.syncInteraction.active&&null!=e.syncInteraction.index)return e.syncInteraction;var l=!0===e.settings.active;if(null!=o.index){if(l)return a(a({},o),{},{active:!0})}else if(null!=i)return{active:!0,coordinate:void 0,dataKey:void 0,index:i};return a(a({},n.k_),{},{coordinate:o.coordinate})}},68915:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84602),i=r(67628);t.cloneDeepWith=function(e,t){return n.cloneDeepWith(e,(r,a,o,l)=>{let u=t?.(r,a,o,l);if(null!=u)return u;if("object"==typeof e)switch(Object.prototype.toString.call(e)){case i.numberTag:case i.stringTag:case i.booleanTag:{let t=new e.constructor(e?.valueOf());return n.copyProperties(t,e),t}case i.argumentsTag:{let t={};return n.copyProperties(t,e),t.length=e.length,t[Symbol.iterator]=e[Symbol.iterator],t}default:return}})}},70307:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toPath=function(e){let t=[],r=e.length;if(0===r)return t;let n=0,i="",a="",o=!1;for(46===e.charCodeAt(0)&&(t.push(""),n++);n<r;){let l=e[n];a?"\\"===l&&n+1<r?i+=e[++n]:l===a?a="":i+=l:o?'"'===l||"'"===l?a=l:"]"===l?(o=!1,t.push(i),i=""):i+=l:"["===l?(o=!0,i&&(t.push(i),i="")):"."===l?i&&(t.push(i),i=""):i+=l,n++}return i&&t.push(i),t}},70340:(e,t,r)=>{"use strict";r.d(t,{n:()=>a});var n=r(94285),i=r(29427);function a(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"animation-",r=(0,n.useRef)((0,i.NF)(t)),a=(0,n.useRef)(e);return a.current!==e&&(r.current=(0,i.NF)(t),a.current=e),r.current}},70474:(e,t,r)=>{"use strict";r.d(t,{BZ:()=>D,aX:()=>z,dS:()=>C,dp:()=>_,fW:()=>O,pg:()=>T,r1:()=>E,u9:()=>N,yn:()=>I});var n=r(73474),i=r(10014),a=r.n(i),o=r(4517),l=r(65084),u=r(92602),c=r(52084),s=r(4788),f=r(21584),h=r(90355),p=r(23487),d=r(62444),y=r(68810),v=r(79423),g=r(96546),m=r(63700),b=r(5124),w=r(12664),x=r(58139),O=()=>(0,o.G)(s.iO),j=(e,t)=>t,P=(e,t,r)=>r,A=(e,t,r,n)=>n,E=(0,n.Mz)(c.R4,e=>a()(e,e=>e.coordinate)),M=(0,n.Mz)([w.J,j,P,A],y.i),S=(0,n.Mz)([M,c.n4],v.P),_=(e,t,r)=>{if(null!=t){var n=(0,w.J)(e);return"axis"===t?"hover"===r?n.axisInteraction.hover.dataKey:n.axisInteraction.click.dataKey:"hover"===r?n.itemInteraction.hover.dataKey:n.itemInteraction.click.dataKey}},k=(0,n.Mz)([w.J,j,P,A],m.q),T=(0,n.Mz)([p.Lp,p.A$,f.fz,h.HZ,c.R4,A,k,b.x],g.o),C=(0,n.Mz)([M,T],(e,t)=>{var r;return null!=(r=e.coordinate)?r:t}),D=(0,n.Mz)(c.R4,S,d.E),N=(0,n.Mz)([k,S,u.LF,c.Dn,D,b.x,j],x.N),I=(0,n.Mz)([M],e=>({isActive:e.active,activeIndex:e.index})),z=(e,t,r,n,i,a,o,u)=>{if(e&&t&&n&&i&&a){var c=(0,l.r4)(e.chartX,e.chartY,t,r,u);if(c){var s=(0,l.SW)(c,t),f=(0,l.gH)(s,o,a,n,i),h=(0,l.bk)(t,a,f,c);return{activeIndex:String(f),activeCoordinate:h}}}}},71137:(e,t,r)=>{"use strict";r.d(t,{e:()=>d,k:()=>y});var n=r(68646),i=r(82284),a=r(92769),o=r(75931),l=r(91881),u=r(49651),c=r(73474),s=r(5124),f=r(12664),h=(0,c.Mz)([f.J],e=>e.tooltipItemPayloads),p=(0,c.Mz)([h,s.x,(e,t,r)=>t,(e,t,r)=>r],(e,t,r,n)=>{var i=e.find(e=>e.settings.dataKey===n);if(null!=i){var{positions:a}=i;if(null!=a)return t(a,r)}}),d=(0,n.VP)("touchMove"),y=(0,n.Nc)();y.startListening({actionCreator:d,effect:(e,t)=>{var r=e.payload,n=t.getState(),c=(0,l.au)(n,n.tooltip.settings.shared);if("axis"===c){var s=(0,a.g)(n,(0,o.w)({clientX:r.touches[0].clientX,clientY:r.touches[0].clientY,currentTarget:r.currentTarget}));(null==s?void 0:s.activeIndex)!=null&&t.dispatch((0,i.Nt)({activeIndex:s.activeIndex,activeDataKey:void 0,activeCoordinate:s.activeCoordinate}))}else if("item"===c){var f,h=r.touches[0],d=document.elementFromPoint(h.clientX,h.clientY);if(!d||!d.getAttribute)return;var y=d.getAttribute(u.F0),v=null!=(f=d.getAttribute(u.um))?f:void 0,g=p(t.getState(),y,v);t.dispatch((0,i.RD)({activeDataKey:v,activeIndex:y,activeCoordinate:g}))}}})},71429:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isObject=function(e){return null!==e&&("object"==typeof e||"function"==typeof e)}},71998:(e,t,r)=>{"use strict";r.d(t,{J9:()=>y,aS:()=>p});var n=r(56797),i=r.n(n),a=r(94285),o=r(63449),l=r(29427),u=r(53951),c=e=>"string"==typeof e?e:e?e.displayName||e.name||"Component":"",s=null,f=null,h=e=>{if(e===s&&Array.isArray(f))return f;var t=[];return a.Children.forEach(e,e=>{(0,l.uy)(e)||((0,o.isFragment)(e)?t=t.concat(h(e.props.children)):t.push(e))}),f=t,s=e,t};function p(e,t){var r=[],n=[];return n=Array.isArray(t)?t.map(e=>c(e)):[c(t)],h(e).forEach(e=>{var t=i()(e,"type.displayName")||i()(e,"type.name");-1!==n.indexOf(t)&&r.push(e)}),r}var d=(e,t,r,n)=>{var i,a=null!=(i=n&&(null===u.VU||void 0===u.VU?void 0:u.VU[n]))?i:[];return t.startsWith("data-")||"function"!=typeof e&&(n&&a.includes(t)||u.QQ.includes(t))||r&&u.j2.includes(t)},y=(e,t,r)=>{if(!e||"function"==typeof e||"boolean"==typeof e)return null;var n=e;if((0,a.isValidElement)(e)&&(n=e.props),"object"!=typeof n&&"function"!=typeof n)return null;var i={};return Object.keys(n).forEach(e=>{var a;d(null==(a=n)?void 0:a[e],e,t,r)&&(i[e]=n[e])}),i}},72533:(e,t,r)=>{e.exports=r(5281).isEqual},72761:(e,t,r)=>{"use strict";r.d(t,{B_:()=>i,JK:()=>a,Vp:()=>u,gX:()=>o,hF:()=>l});var n=(0,r(68646).Z0)({name:"chartLayout",initialState:{layoutType:"horizontal",width:0,height:0,margin:{top:5,right:5,bottom:5,left:5},scale:1},reducers:{setLayout(e,t){e.layoutType=t.payload},setChartSize(e,t){e.width=t.payload.width,e.height=t.payload.height},setMargin(e,t){e.margin.top=t.payload.top,e.margin.right=t.payload.right,e.margin.bottom=t.payload.bottom,e.margin.left=t.payload.left},setScale(e,t){e.scale=t.payload}}}),{setMargin:i,setLayout:a,setChartSize:o,setScale:l}=n.actions,u=n.reducer},73474:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>x});var n=e=>Array.isArray(e)?e:[e],i=0,a=null,o=class{revision=i;_value;_lastValue;_isEqual=l;constructor(e,t=l){this._value=this._lastValue=e,this._isEqual=t}get value(){return a?.add(this),this._value}set value(e){this.value!==e&&(this._value=e,this.revision=++i)}};function l(e,t){return e===t}function u(e){return e instanceof o||console.warn("Not a valid cell! ",e),e.value}var c=(e,t)=>!1;function s(){return function(e,t=l){return new o(null,t)}(0,c)}var f=e=>{let t=e.collectionTag;null===t&&(t=e.collectionTag=s()),u(t)};Symbol();var h=0,p=Object.getPrototypeOf({}),d=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy(this,y);tag=s();tags={};children={};collectionTag=null;id=h++},y={get:(e,t)=>(function(){let{value:r}=e,n=Reflect.get(r,t);if("symbol"==typeof t||t in p)return n;if("object"==typeof n&&null!==n){let r=e.children[t];return void 0===r&&(r=e.children[t]=function(e){return Array.isArray(e)?new v(e):new d(e)}(n)),r.tag&&u(r.tag),r.proxy}{let r=e.tags[t];return void 0===r&&((r=e.tags[t]=s()).value=n),u(r),n}})(),ownKeys:e=>(f(e),Reflect.ownKeys(e.value)),getOwnPropertyDescriptor:(e,t)=>Reflect.getOwnPropertyDescriptor(e.value,t),has:(e,t)=>Reflect.has(e.value,t)},v=class{constructor(e){this.value=e,this.value=e,this.tag.value=e}proxy=new Proxy([this],g);tag=s();tags={};children={};collectionTag=null;id=h++},g={get:([e],t)=>("length"===t&&f(e),y.get(e,t)),ownKeys:([e])=>y.ownKeys(e),getOwnPropertyDescriptor:([e],t)=>y.getOwnPropertyDescriptor(e,t),has:([e],t)=>y.has(e,t)},m="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function b(){return{s:0,v:void 0,o:null,p:null}}function w(e,t={}){let r,n=b(),{resultEqualityCheck:i}=t,a=0;function o(){let t,o=n,{length:l}=arguments;for(let e=0;e<l;e++){let t=arguments[e];if("function"==typeof t||"object"==typeof t&&null!==t){let e=o.o;null===e&&(o.o=e=new WeakMap);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}else{let e=o.p;null===e&&(o.p=e=new Map);let r=e.get(t);void 0===r?(o=b(),e.set(t,o)):o=r}}let u=o;if(1===o.s)t=o.v;else if(t=e.apply(null,arguments),a++,i){let e=r?.deref?.()??r;null!=e&&i(e,t)&&(t=e,0!==a&&a--),r="object"==typeof t&&null!==t||"function"==typeof t?new m(t):t}return u.s=1,u.v=t,t}return o.clearCache=()=>{n=b(),o.resetResultsCount()},o.resultsCount=()=>a,o.resetResultsCount=()=>{a=0},o}var x=function(e,...t){let r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,i=(...e)=>{let t,i=0,a=0,o={},l=e.pop();"object"==typeof l&&(o=l,l=e.pop()),function(e,t=`expected a function, instead received ${typeof e}`){if("function"!=typeof e)throw TypeError(t)}(l,`createSelector expects an output function after the inputs, but received: [${typeof l}]`);let{memoize:u,memoizeOptions:c=[],argsMemoize:s=w,argsMemoizeOptions:f=[],devModeChecks:h={}}={...r,...o},p=n(c),d=n(f),y=function(e){let t=Array.isArray(e[0])?e[0]:e;return!function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){let r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}(e),v=u(function(){return i++,l.apply(null,arguments)},...p);return Object.assign(s(function(){a++;let e=function(e,t){let r=[],{length:n}=e;for(let i=0;i<n;i++)r.push(e[i].apply(null,t));return r}(y,arguments);return t=v.apply(null,e)},...d),{resultFunc:l,memoizedResultFunc:v,dependencies:y,dependencyRecomputations:()=>a,resetDependencyRecomputations:()=>{a=0},lastResult:()=>t,recomputations:()=>i,resetRecomputations:()=>{i=0},memoize:u,argsMemoize:s})};return Object.assign(i,{withTypes:()=>i}),i}(w),O=Object.assign((e,t=x)=>{!function(e,t=`expected an object, instead received ${typeof e}`){if("object"!=typeof e)throw TypeError(t)}(e,`createStructuredSelector expects first argument to be an object where each property is a selector, instead received a ${typeof e}`);let r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>O})},73661:(e,t,r)=>{"use strict";r.d(t,{R:()=>n});var n=function(e,t){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]}},74201:(e,t,r)=>{"use strict";r.d(t,{Qx:()=>c,a6:()=>s,h4:()=>H,jM:()=>G,ss:()=>K});var n,i=Symbol.for("immer-nothing"),a=Symbol.for("immer-draftable"),o=Symbol.for("immer-state");function l(e,...t){throw Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var u=Object.getPrototypeOf;function c(e){return!!e&&!!e[o]}function s(e){return!!e&&(h(e)||Array.isArray(e)||!!e[a]||!!e.constructor?.[a]||g(e)||m(e))}var f=Object.prototype.constructor.toString();function h(e){if(!e||"object"!=typeof e)return!1;let t=u(e);if(null===t)return!0;let r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===f}function p(e,t){0===d(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function d(e){let t=e[o];return t?t.type_:Array.isArray(e)?1:g(e)?2:3*!!m(e)}function y(e,t){return 2===d(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function v(e,t,r){let n=d(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function g(e){return e instanceof Map}function m(e){return e instanceof Set}function b(e){return e.copy_||e.base_}function w(e,t){if(g(e))return new Map(e);if(m(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);let r=h(e);if(!0!==t&&("class_only"!==t||r)){let t=u(e);return null!==t&&r?{...e}:Object.assign(Object.create(t),e)}{let t=Object.getOwnPropertyDescriptors(e);delete t[o];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){let i=r[n],a=t[i];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[i]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[i]})}return Object.create(u(e),t)}}function x(e,t=!1){return j(e)||c(e)||!s(e)||(d(e)>1&&(e.set=e.add=e.clear=e.delete=O),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>x(t,!0))),e}function O(){l(2)}function j(e){return Object.isFrozen(e)}var P={};function A(e){let t=P[e];return t||l(0,e),t}function E(e,t){t&&(A("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function M(e){S(e),e.drafts_.forEach(k),e.drafts_=null}function S(e){e===n&&(n=e.parent_)}function _(e){return n={drafts_:[],parent_:n,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function k(e){let t=e[o];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function T(e,t){t.unfinalizedDrafts_=t.drafts_.length;let r=t.drafts_[0];return void 0!==e&&e!==r?(r[o].modified_&&(M(t),l(4)),s(e)&&(e=C(t,e),t.parent_||N(t,e)),t.patches_&&A("Patches").generateReplacementPatches_(r[o].base_,e,t.patches_,t.inversePatches_)):e=C(t,r,[]),M(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==i?e:void 0}function C(e,t,r){if(j(t))return t;let n=t[o];if(!n)return p(t,(i,a)=>D(e,n,t,i,a,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return N(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;let t=n.copy_,i=t,a=!1;3===n.type_&&(i=new Set(t),t.clear(),a=!0),p(i,(i,o)=>D(e,n,t,i,o,r,a)),N(e,t,!1),r&&e.patches_&&A("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function D(e,t,r,n,i,a,o){if(c(i)){let o=C(e,i,a&&t&&3!==t.type_&&!y(t.assigned_,n)?a.concat(n):void 0);if(v(r,n,o),!c(o))return;e.canAutoFreeze_=!1}else o&&r.add(i);if(s(i)&&!j(i)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;C(e,i),(!t||!t.scope_.parent_)&&"symbol"!=typeof n&&Object.prototype.propertyIsEnumerable.call(r,n)&&N(e,i)}}function N(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&x(t,r)}var I={get(e,t){if(t===o)return e;let r=b(e);if(!y(r,t)){var n=e,i=r,a=t;let o=R(i,a);return o?"value"in o?o.value:o.get?.call(n.draft_):void 0}let l=r[t];return e.finalized_||!s(l)?l:l===L(e.base_,t)?(U(e),e.copy_[t]=F(l,e)):l},has:(e,t)=>t in b(e),ownKeys:e=>Reflect.ownKeys(b(e)),set(e,t,r){let n=R(b(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){let n=L(b(e),t),i=n?.[o];if(i&&i.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if((r===n?0!==r||1/r==1/n:r!=r&&n!=n)&&(void 0!==r||y(e.base_,t)))return!0;U(e),B(e)}return!!(e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t]))||(e.copy_[t]=r,e.assigned_[t]=!0,!0)},deleteProperty:(e,t)=>(void 0!==L(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,U(e),B(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){let r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){l(11)},getPrototypeOf:e=>u(e.base_),setPrototypeOf(){l(12)}},z={};function L(e,t){let r=e[o];return(r?b(r):e)[t]}function R(e,t){if(!(t in e))return;let r=u(e);for(;r;){let e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=u(r)}}function B(e){!e.modified_&&(e.modified_=!0,e.parent_&&B(e.parent_))}function U(e){e.copy_||(e.copy_=w(e.base_,e.scope_.immer_.useStrictShallowCopy_))}function F(e,t){let r=g(e)?A("MapSet").proxyMap_(e,t):m(e)?A("MapSet").proxySet_(e,t):function(e,t){let r=Array.isArray(e),i={type_:+!!r,scope_:t?t.scope_:n,modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1},a=i,o=I;r&&(a=[i],o=z);let{revoke:l,proxy:u}=Proxy.revocable(a,o);return i.draft_=u,i.revoke_=l,u}(e,t);return(t?t.scope_:n).drafts_.push(r),r}function K(e){return c(e)||l(10,e),function e(t){let r;if(!s(t)||j(t))return t;let n=t[o];if(n){if(!n.modified_)return n.base_;n.finalized_=!0,r=w(t,n.scope_.immer_.useStrictShallowCopy_)}else r=w(t,!0);return p(r,(t,n)=>{v(r,t,e(n))}),n&&(n.finalized_=!1),r}(e)}p(I,(e,t)=>{z[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),z.deleteProperty=function(e,t){return z.set.call(this,e,t,void 0)},z.set=function(e,t,r){return I.set.call(this,e[0],t,r,e[0])};var $=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{let n;if("function"==typeof e&&"function"!=typeof t){let r=t;t=e;let n=this;return function(e=r,...i){return n.produce(e,e=>t.call(this,e,...i))}}if("function"!=typeof t&&l(6),void 0!==r&&"function"!=typeof r&&l(7),s(e)){let i=_(this),a=F(e,void 0),o=!0;try{n=t(a),o=!1}finally{o?M(i):S(i)}return E(i,r),T(n,i)}if(e&&"object"==typeof e)l(1,e);else{if(void 0===(n=t(e))&&(n=e),n===i&&(n=void 0),this.autoFreeze_&&x(n,!0),r){let t=[],i=[];A("Patches").generateReplacementPatches_(e,n,t,i),r(t,i)}return n}},this.produceWithPatches=(e,t)=>{let r,n;return"function"==typeof e?(t,...r)=>this.produceWithPatches(t,t=>e(t,...r)):[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){s(e)||l(8),c(e)&&(e=K(e));let t=_(this),r=F(e,void 0);return r[o].isManual_=!0,S(t),r}finishDraft(e,t){let r=e&&e[o];r&&r.isManual_||l(9);let{scope_:n}=r;return E(n,t),T(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){let n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));let n=A("Patches").applyPatches_;return c(e)?n(e,t):this.produce(e,e=>n(e,t))}},G=$.produce;function H(e){return e}$.produceWithPatches.bind($),$.setAutoFreeze.bind($),$.setUseStrictShallowCopy.bind($),$.applyPatches.bind($),$.createDraft.bind($),$.finishDraft.bind($)},74537:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(43630);t.throttle=function(e,t=0,r={}){let{leading:i=!0,trailing:a=!0}=r;return n.debounce(e,t,{leading:i,maxWait:t,trailing:a})}},74643:(e,t,r)=>{"use strict";r.d(t,{dl:()=>u,lJ:()=>l,uN:()=>a});var n=r(68646),i=r(29427);function a(e,t){if(t){var r=Number.parseInt(t,10);if(!(0,i.M8)(r))return null==e?void 0:e[r]}}var o=(0,n.Z0)({name:"options",initialState:{chartName:"",tooltipPayloadSearcher:void 0,eventEmitter:void 0,defaultTooltipEventType:"axis"},reducers:{createEventEmitter:e=>{null==e.eventEmitter&&(e.eventEmitter=Symbol("rechartsEventEmitter"))}}}),l=o.reducer,{createEventEmitter:u}=o.actions},75931:(e,t,r)=>{"use strict";r.d(t,{w:()=>n});var n=e=>{var t=e.currentTarget.getBoundingClientRect(),r=t.width/e.currentTarget.offsetWidth,n=t.height/e.currentTarget.offsetHeight;return{chartX:Math.round((e.clientX-t.left)/r),chartY:Math.round((e.clientY-t.top)/n)}}},76544:(e,t,r)=>{"use strict";r.d(t,{M:()=>f});var n=r(94285),i=r(3638),a=r(71998),o=r(13739),l=r(17951);function u(){return(u=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var c=(e,t,r,n,i)=>{var a,o=Math.min(Math.abs(r)/2,Math.abs(n)/2),l=n>=0?1:-1,u=r>=0?1:-1,c=+(n>=0&&r>=0||n<0&&r<0);if(o>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>o?o:i[f];a="M".concat(e,",").concat(t+l*s[0]),s[0]>0&&(a+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(c,",").concat(e+u*s[0],",").concat(t)),a+="L ".concat(e+r-u*s[1],",").concat(t),s[1]>0&&(a+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(c,",\n        ").concat(e+r,",").concat(t+l*s[1])),a+="L ".concat(e+r,",").concat(t+n-l*s[2]),s[2]>0&&(a+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(c,",\n        ").concat(e+r-u*s[2],",").concat(t+n)),a+="L ".concat(e+u*s[3],",").concat(t+n),s[3]>0&&(a+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(c,",\n        ").concat(e,",").concat(t+n-l*s[3])),a+="Z"}else if(o>0&&i===+i&&i>0){var h=Math.min(o,i);a="M ".concat(e,",").concat(t+l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+u*h,",").concat(t,"\n            L ").concat(e+r-u*h,",").concat(t,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+r,",").concat(t+l*h,"\n            L ").concat(e+r,",").concat(t+n-l*h,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e+r-u*h,",").concat(t+n,"\n            L ").concat(e+u*h,",").concat(t+n,"\n            A ").concat(h,",").concat(h,",0,0,").concat(c,",").concat(e,",").concat(t+n-l*h," Z")}else a="M ".concat(e,",").concat(t," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return a},s={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},f=e=>{var t=(0,o.e)(e,s),r=(0,n.useRef)(null),[f,h]=(0,n.useState)(-1);(0,n.useEffect)(()=>{if(r.current&&r.current.getTotalLength)try{var e=r.current.getTotalLength();e&&h(e)}catch(e){}},[]);var{x:p,y:d,width:y,height:v,radius:g,className:m}=t,{animationEasing:b,animationDuration:w,animationBegin:x,isAnimationActive:O,isUpdateAnimationActive:j}=t;if(p!==+p||d!==+d||y!==+y||v!==+v||0===y||0===v)return null;var P=(0,i.$)("recharts-rectangle",m);return j?n.createElement(l.i,{canBegin:f>0,from:{width:y,height:v,x:p,y:d},to:{width:y,height:v,x:p,y:d},duration:w,animationEasing:b,isActive:j},e=>{var{width:i,height:o,x:s,y:h}=e;return n.createElement(l.i,{canBegin:f>0,from:"0px ".concat(-1===f?1:f,"px"),to:"".concat(f,"px 0px"),attributeName:"strokeDasharray",begin:x,duration:w,isActive:O,easing:b},n.createElement("path",u({},(0,a.J9)(t,!0),{className:P,d:c(s,h,i,o,g),ref:r})))}):n.createElement("path",u({},(0,a.J9)(t,!0),{className:P,d:c(p,d,y,v,g)}))}},76943:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isLength=function(e){return Number.isSafeInteger(e)&&e>=0}},78212:(e,t,r)=>{"use strict";r.d(t,{Be:()=>v,Cv:()=>O,D0:()=>P,Gl:()=>g,Dc:()=>j});var n=r(73474),i=r(23487),a=r(90355),o=r(15143),l=r(29427),u={allowDuplicatedCategory:!0,angleAxisId:0,reversed:!1,scale:"auto",tick:!0,type:"category"},c={allowDataOverflow:!1,allowDuplicatedCategory:!0,radiusAxisId:0,scale:"auto",tick:!0,tickCount:5,type:"number"},s=r(42844),f=r(21584),h={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:!1,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:u.reversed,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:u.type,unit:void 0},p={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:c.type,unit:void 0},d={allowDataOverflow:!1,allowDecimals:!1,allowDuplicatedCategory:u.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:u.angleAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:u.scale,tick:u.tick,tickCount:void 0,ticks:void 0,type:"number",unit:void 0},y={allowDataOverflow:c.allowDataOverflow,allowDecimals:!1,allowDuplicatedCategory:c.allowDuplicatedCategory,dataKey:void 0,domain:void 0,id:c.radiusAxisId,includeHidden:!1,name:void 0,reversed:!1,scale:c.scale,tick:c.tick,tickCount:c.tickCount,ticks:void 0,type:"category",unit:void 0},v=(e,t)=>null!=e.polarAxis.angleAxis[t]?e.polarAxis.angleAxis[t]:"radial"===e.layout.layoutType?d:h,g=(e,t)=>null!=e.polarAxis.radiusAxis[t]?e.polarAxis.radiusAxis[t]:"radial"===e.layout.layoutType?y:p,m=e=>e.polarOptions,b=(0,n.Mz)([i.Lp,i.A$,a.HZ],o.lY),w=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.innerRadius,t,0)}),x=(0,n.Mz)([m,b],(e,t)=>{if(null!=e)return(0,l.F4)(e.outerRadius,t,.8*t)}),O=(0,n.Mz)([m],e=>{if(null==e)return[0,0];var{startAngle:t,endAngle:r}=e;return[t,r]});(0,n.Mz)([v,O],s.I);var j=(0,n.Mz)([b,w,x],(e,t,r)=>{if(null!=e&&null!=t&&null!=r)return[t,r]});(0,n.Mz)([g,j],s.I);var P=(0,n.Mz)([f.fz,m,w,x,i.Lp,i.A$],(e,t,r,n,i,a)=>{if(("centric"===e||"radial"===e)&&null!=t&&null!=r&&null!=n){var{cx:o,cy:u,startAngle:c,endAngle:s}=t;return{cx:(0,l.F4)(o,i,i/2),cy:(0,l.F4)(u,a,a/2),innerRadius:r,outerRadius:n,startAngle:c,endAngle:s,clockWise:!1}}})},78369:(e,t,r)=>{"use strict";r.d(t,{h:()=>y});var n=r(94285),i=r(3638),a=r(71998),o=r(15143),l=r(29427),u=r(13739);function c(){return(c=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}var s=(e,t)=>(0,l.sA)(t-e)*Math.min(Math.abs(t-e),359.999),f=e=>{var{cx:t,cy:r,radius:n,angle:i,sign:a,isExternal:l,cornerRadius:u,cornerIsExternal:c}=e,s=u*(l?1:-1)+n,f=Math.asin(u/s)/o.Kg,h=c?i:i+a*f,p=(0,o.IZ)(t,r,s,h);return{center:p,circleTangency:(0,o.IZ)(t,r,n,h),lineTangency:(0,o.IZ)(t,r,s*Math.cos(f*o.Kg),c?i-a*f:i),theta:f}},h=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:a,endAngle:l}=e,u=s(a,l),c=a+u,f=(0,o.IZ)(t,r,i,a),h=(0,o.IZ)(t,r,i,c),p="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(u)>180),",").concat(+(a>c),",\n    ").concat(h.x,",").concat(h.y,"\n  ");if(n>0){var d=(0,o.IZ)(t,r,n,a),y=(0,o.IZ)(t,r,n,c);p+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(u)>180),",").concat(+(a<=c),",\n            ").concat(d.x,",").concat(d.y," Z")}else p+="L ".concat(t,",").concat(r," Z");return p},p=e=>{var{cx:t,cy:r,innerRadius:n,outerRadius:i,cornerRadius:a,forceCornerRadius:o,cornerIsExternal:u,startAngle:c,endAngle:s}=e,p=(0,l.sA)(s-c),{circleTangency:d,lineTangency:y,theta:v}=f({cx:t,cy:r,radius:i,angle:c,sign:p,cornerRadius:a,cornerIsExternal:u}),{circleTangency:g,lineTangency:m,theta:b}=f({cx:t,cy:r,radius:i,angle:s,sign:-p,cornerRadius:a,cornerIsExternal:u}),w=u?Math.abs(c-s):Math.abs(c-s)-v-b;if(w<0)return o?"M ".concat(y.x,",").concat(y.y,"\n        a").concat(a,",").concat(a,",0,0,1,").concat(2*a,",0\n        a").concat(a,",").concat(a,",0,0,1,").concat(-(2*a),",0\n      "):h({cx:t,cy:r,innerRadius:n,outerRadius:i,startAngle:c,endAngle:s});var x="M ".concat(y.x,",").concat(y.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(w>180),",").concat(+(p<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var{circleTangency:O,lineTangency:j,theta:P}=f({cx:t,cy:r,radius:n,angle:c,sign:p,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),{circleTangency:A,lineTangency:E,theta:M}=f({cx:t,cy:r,radius:n,angle:s,sign:-p,isExternal:!0,cornerRadius:a,cornerIsExternal:u}),S=u?Math.abs(c-s):Math.abs(c-s)-P-M;if(S<0&&0===a)return"".concat(x,"L").concat(t,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(S>180),",").concat(+(p>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(a,",").concat(a,",0,0,").concat(+(p<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(t,",").concat(r,"Z");return x},d={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},y=e=>{var t,r=(0,u.e)(e,d),{cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:v,forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:w,className:x}=r;if(y<f||b===w)return null;var O=(0,i.$)("recharts-sector",x),j=y-f,P=(0,l.F4)(v,j,0,!0);return t=P>0&&360>Math.abs(b-w)?p({cx:o,cy:s,innerRadius:f,outerRadius:y,cornerRadius:Math.min(P,j/2),forceCornerRadius:g,cornerIsExternal:m,startAngle:b,endAngle:w}):h({cx:o,cy:s,innerRadius:f,outerRadius:y,startAngle:b,endAngle:w}),n.createElement("path",c({},(0,a.J9)(r,!0),{className:O,d:t}))}},79423:(e,t,r)=>{"use strict";r.d(t,{P:()=>i});var n=r(38034),i=(e,t)=>{var r=null==e?void 0:e.index;if(null==r)return null;var i=Number(r);if(!(0,n.H)(i))return r;var a=Infinity;return t.length>0&&(a=t.length-1),String(Math.max(0,Math.min(i,a)))}},82284:(e,t,r)=>{"use strict";r.d(t,{E1:()=>v,En:()=>m,Ix:()=>l,ML:()=>p,Nt:()=>d,RD:()=>s,UF:()=>c,XB:()=>u,jF:()=>y,k_:()=>a,o4:()=>g,oP:()=>f,xS:()=>h});var n=r(68646),i=r(74201),a={active:!1,index:null,dataKey:void 0,coordinate:void 0},o=(0,n.Z0)({name:"tooltip",initialState:{itemInteraction:{click:a,hover:a},axisInteraction:{click:a,hover:a},keyboardInteraction:a,syncInteraction:{active:!1,index:null,dataKey:void 0,label:void 0,coordinate:void 0},tooltipItemPayloads:[],settings:{shared:void 0,trigger:"hover",axisId:0,active:!1,defaultIndex:void 0}},reducers:{addTooltipEntrySettings(e,t){e.tooltipItemPayloads.push((0,i.h4)(t.payload))},removeTooltipEntrySettings(e,t){var r=(0,i.ss)(e).tooltipItemPayloads.indexOf((0,i.h4)(t.payload));r>-1&&e.tooltipItemPayloads.splice(r,1)},setTooltipSettingsState(e,t){e.settings=t.payload},setActiveMouseOverItemIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.itemInteraction.hover.active=!0,e.itemInteraction.hover.index=t.payload.activeIndex,e.itemInteraction.hover.dataKey=t.payload.activeDataKey,e.itemInteraction.hover.coordinate=t.payload.activeCoordinate},mouseLeaveChart(e){e.itemInteraction.hover.active=!1,e.axisInteraction.hover.active=!1},mouseLeaveItem(e){e.itemInteraction.hover.active=!1},setActiveClickItemIndex(e,t){e.syncInteraction.active=!1,e.itemInteraction.click.active=!0,e.keyboardInteraction.active=!1,e.itemInteraction.click.index=t.payload.activeIndex,e.itemInteraction.click.dataKey=t.payload.activeDataKey,e.itemInteraction.click.coordinate=t.payload.activeCoordinate},setMouseOverAxisIndex(e,t){e.syncInteraction.active=!1,e.axisInteraction.hover.active=!0,e.keyboardInteraction.active=!1,e.axisInteraction.hover.index=t.payload.activeIndex,e.axisInteraction.hover.dataKey=t.payload.activeDataKey,e.axisInteraction.hover.coordinate=t.payload.activeCoordinate},setMouseClickAxisIndex(e,t){e.syncInteraction.active=!1,e.keyboardInteraction.active=!1,e.axisInteraction.click.active=!0,e.axisInteraction.click.index=t.payload.activeIndex,e.axisInteraction.click.dataKey=t.payload.activeDataKey,e.axisInteraction.click.coordinate=t.payload.activeCoordinate},setSyncInteraction(e,t){e.syncInteraction=t.payload},setKeyboardInteraction(e,t){e.keyboardInteraction.active=t.payload.active,e.keyboardInteraction.index=t.payload.activeIndex,e.keyboardInteraction.coordinate=t.payload.activeCoordinate,e.keyboardInteraction.dataKey=t.payload.activeDataKey}}}),{addTooltipEntrySettings:l,removeTooltipEntrySettings:u,setTooltipSettingsState:c,setActiveMouseOverItemIndex:s,mouseLeaveItem:f,mouseLeaveChart:h,setActiveClickItemIndex:p,setMouseOverAxisIndex:d,setMouseClickAxisIndex:y,setSyncInteraction:v,setKeyboardInteraction:g}=o.actions,m=o.reducer},82707:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.last=function(e){return e[e.length-1]}},83249:(e,t,r)=>{"use strict";r.d(t,{r:()=>l});var n=r(94285),i=r(4517),a=r(82284),o=r(8569);function l(e){var{fn:t,args:r}=e,l=(0,i.j)(),u=(0,o.r)();return(0,n.useEffect)(()=>{if(!u){var e=t(r);return l((0,a.Ix)(e)),()=>{l((0,a.XB)(e))}}},[t,r,l,u]),null}},83364:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var n=(e,t)=>t},83746:(e,t,r)=>{"use strict";r.d(t,{As:()=>f,Ch:()=>l,TK:()=>h,Vi:()=>s,ZF:()=>c,g5:()=>u,iZ:()=>p,lm:()=>o});var n=r(68646),i=r(74201),a=(0,n.Z0)({name:"graphicalItems",initialState:{countOfBars:0,cartesianItems:[],polarItems:[]},reducers:{addBar(e){e.countOfBars+=1},removeBar(e){e.countOfBars-=1},addCartesianGraphicalItem(e,t){e.cartesianItems.push((0,i.h4)(t.payload))},replaceCartesianGraphicalItem(e,t){var{prev:r,next:n}=t.payload,a=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(r));a>-1&&(e.cartesianItems[a]=(0,i.h4)(n))},removeCartesianGraphicalItem(e,t){var r=(0,i.ss)(e).cartesianItems.indexOf((0,i.h4)(t.payload));r>-1&&e.cartesianItems.splice(r,1)},addPolarGraphicalItem(e,t){e.polarItems.push((0,i.h4)(t.payload))},removePolarGraphicalItem(e,t){var r=(0,i.ss)(e).polarItems.indexOf((0,i.h4)(t.payload));r>-1&&e.polarItems.splice(r,1)}}}),{addBar:o,removeBar:l,addCartesianGraphicalItem:u,replaceCartesianGraphicalItem:c,removeCartesianGraphicalItem:s,addPolarGraphicalItem:f,removePolarGraphicalItem:h}=a.actions,p=a.reducer},83844:(e,t,r)=>{"use strict";r.d(t,{oM:()=>p,ZI:()=>f,gi:()=>h});var n=r(96112),i=r(4517),a=r(8569),o=r(73474),l=r(90355),u=(0,o.Mz)([l.HZ],e=>{if(e)return{top:e.top,bottom:e.bottom,left:e.left,right:e.right}}),c=r(23487),s=(0,o.Mz)([u,c.Lp,c.A$],(e,t,r)=>{if(e&&null!=t&&null!=r)return{x:e.left,y:e.top,width:Math.max(0,t-e.left-e.right),height:Math.max(0,r-e.top-e.bottom)}}),f=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"xAxis",e,t))},h=e=>{var t=(0,a.r)();return(0,i.G)(r=>(0,n.Gx)(r,"yAxis",e,t))},p=()=>(0,i.G)(s)},84467:(e,t,r)=>{"use strict";r.d(t,{h:()=>u});var n=r(94513),i=r(22697),a=r(12183),o=r(2923),l=r(33225);let u=(0,o.R)(function(e,t){let r=(0,a.E)();return(0,n.jsx)(l.B.dd,{ref:t,...e,className:(0,i.cx)("chakra-stat__help-text",e.className),__css:r.helpText})});u.displayName="StatHelpText"},84602:(e,t,r)=>{"use strict";var n=r(56221).Buffer;Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let i=r(35556),a=r(90657),o=r(67628),l=r(97888),u=r(738);function c(e,t,r,i=new Map,f){let h=f?.(e,t,r,i);if(null!=h)return h;if(l.isPrimitive(e))return e;if(i.has(e))return i.get(e);if(Array.isArray(e)){let t=Array(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return Object.hasOwn(e,"index")&&(t.index=e.index),Object.hasOwn(e,"input")&&(t.input=e.input),t}if(e instanceof Date)return new Date(e.getTime());if(e instanceof RegExp){let t=new RegExp(e.source,e.flags);return t.lastIndex=e.lastIndex,t}if(e instanceof Map){let t=new Map;for(let[n,a]of(i.set(e,t),e))t.set(n,c(a,n,r,i,f));return t}if(e instanceof Set){let t=new Set;for(let n of(i.set(e,t),e))t.add(c(n,void 0,r,i,f));return t}if(void 0!==n&&n.isBuffer(e))return e.subarray();if(u.isTypedArray(e)){let t=new(Object.getPrototypeOf(e)).constructor(e.length);i.set(e,t);for(let n=0;n<e.length;n++)t[n]=c(e[n],n,r,i,f);return t}if(e instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&e instanceof SharedArrayBuffer)return e.slice(0);if(e instanceof DataView){let t=new DataView(e.buffer.slice(0),e.byteOffset,e.byteLength);return i.set(e,t),s(t,e,r,i,f),t}if("undefined"!=typeof File&&e instanceof File){let t=new File([e],e.name,{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Blob){let t=new Blob([e],{type:e.type});return i.set(e,t),s(t,e,r,i,f),t}if(e instanceof Error){let t=new e.constructor;return i.set(e,t),t.message=e.message,t.name=e.name,t.stack=e.stack,t.cause=e.cause,s(t,e,r,i,f),t}if("object"==typeof e&&function(e){switch(a.getTag(e)){case o.argumentsTag:case o.arrayTag:case o.arrayBufferTag:case o.dataViewTag:case o.booleanTag:case o.dateTag:case o.float32ArrayTag:case o.float64ArrayTag:case o.int8ArrayTag:case o.int16ArrayTag:case o.int32ArrayTag:case o.mapTag:case o.numberTag:case o.objectTag:case o.regexpTag:case o.setTag:case o.stringTag:case o.symbolTag:case o.uint8ArrayTag:case o.uint8ClampedArrayTag:case o.uint16ArrayTag:case o.uint32ArrayTag:return!0;default:return!1}}(e)){let t=Object.create(Object.getPrototypeOf(e));return i.set(e,t),s(t,e,r,i,f),t}return e}function s(e,t,r=e,n,a){let o=[...Object.keys(t),...i.getSymbols(t)];for(let i=0;i<o.length;i++){let l=o[i],u=Object.getOwnPropertyDescriptor(e,l);(null==u||u.writable)&&(e[l]=c(t[l],l,r,n,a))}}t.cloneDeepWith=function(e,t){return c(e,void 0,e,new Map,t)},t.cloneDeepWithImpl=c,t.copyProperties=s},84878:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isDeepKey=function(e){switch(typeof e){case"number":case"symbol":return!1;case"string":return e.includes(".")||e.includes("[")||e.includes("]")}}},84895:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(54275);t.isMatch=function(e,t){return n.isMatchWith(e,t,()=>void 0)}},86054:(e,t,r)=>{"use strict";r.d(t,{s:()=>l});var n=r(94285),i=r(8569),a=r(72761),o=r(4517);function l(e){var{layout:t,width:r,height:l,margin:u}=e,c=(0,o.j)(),s=(0,i.r)();return(0,n.useEffect)(()=>{s||(c((0,a.JK)(t)),c((0,a.gX)({width:r,height:l})),c((0,a.B_)(u)))},[c,s,t,r,l,u]),null}},86153:(e,t,r)=>{"use strict";r.d(t,{k:()=>u});var n=r(94513),i=r(22697),a=r(12183),o=r(2923),l=r(33225);let u=(0,o.R)(function(e,t){let r=(0,a.E)();return(0,n.jsx)(l.B.dd,{ref:t,...e,className:(0,i.cx)("chakra-stat__number",e.className),__css:{...r.number,fontFeatureSettings:"pnum",fontVariantNumeric:"proportional-nums"}})});u.displayName="StatNumber"},87095:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.eq=function(e,t){return e===t||Number.isNaN(e)&&Number.isNaN(t)}},89166:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(84895),i=r(20539),a=r(29583),o=r(98612),l=r(13040);t.matchesProperty=function(e,t){switch(typeof e){case"object":Object.is(e?.valueOf(),-0)&&(e="-0");break;case"number":e=i.toKey(e)}return t=a.cloneDeep(t),function(r){let i=o.get(r,e);return void 0===i?l.has(r,e):void 0===t?void 0===i:n.isMatch(i,t)}}},90355:(e,t,r)=>{"use strict";r.d(t,{c2:()=>g,HZ:()=>y,Ds:()=>v});var n=r(73474),i=r(56797),a=r.n(i),o=r(10014),l=r.n(o),u=e=>e.legend.settings;(0,n.Mz)([e=>e.legend.payload,u],(e,t)=>{var{itemSorter:r}=t,n=e.flat(1);return r?l()(n,r):n});var c=r(65084),s=r(23487),f=r(60941),h=r(49651);function p(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function d(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?p(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var y=(0,n.Mz)([s.Lp,s.A$,s.HK,e=>e.brush.height,f.h,f.W,u,e=>e.legend.size],(e,t,r,n,i,o,l,u)=>{var s=o.reduce((e,t)=>{var{orientation:r}=t;if(!t.mirror&&!t.hide){var n="number"==typeof t.width?t.width:h.tQ;return d(d({},e),{},{[r]:e[r]+n})}return e},{left:r.left||0,right:r.right||0}),f=i.reduce((e,t)=>{var{orientation:r}=t;return t.mirror||t.hide?e:d(d({},e),{},{[r]:a()(e,"".concat(r))+t.height})},{top:r.top||0,bottom:r.bottom||0}),p=d(d({},f),s),y=p.bottom;p.bottom+=n;var v=e-(p=(0,c.s0)(p,l,u)).left-p.right,g=t-p.top-p.bottom;return d(d({brushBottom:y},p),{},{width:Math.max(v,0),height:Math.max(g,0)})}),v=(0,n.Mz)(y,e=>({x:e.left,y:e.top,width:e.width,height:e.height})),g=(0,n.Mz)(s.Lp,s.A$,(e,t)=>({x:0,y:0,width:e,height:t}))},90444:(e,t,r)=>{"use strict";r.d(t,{l3:()=>g,m7:()=>m});var n=r(94285),i=r(4517),a=r(4788),o=new(r(99898)),l="recharts.syncEvent.tooltip",u="recharts.syncEvent.brush",c=r(74643),s=r(82284),f=r(70474),h=r(52084);function p(e){return e.tooltip.syncInteraction}var d=r(21584),y=r(68645),v=()=>{};function g(){var e,t,r,f,p,g,m,b,w,x,O,j=(0,i.j)();(0,n.useEffect)(()=>{j((0,c.dl)())},[j]),e=(0,i.G)(a.lZ),t=(0,i.G)(a.pH),r=(0,i.j)(),f=(0,i.G)(a.hX),p=(0,i.G)(h.R4),g=(0,d.WX)(),m=(0,d.sk)(),b=(0,i.G)(e=>e.rootProps.className),(0,n.useEffect)(()=>{if(null==e)return v;var n=(n,i,a)=>{if(t!==a&&e===n){if("index"===f)return void r(i);if(null!=p){if("function"==typeof f){var o,l=f(p,{activeTooltipIndex:null==i.payload.index?void 0:Number(i.payload.index),isTooltipActive:i.payload.active,activeIndex:null==i.payload.index?void 0:Number(i.payload.index),activeLabel:i.payload.label,activeDataKey:i.payload.dataKey,activeCoordinate:i.payload.coordinate});o=p[l]}else"value"===f&&(o=p.find(e=>String(e.value)===i.payload.label));var{coordinate:u}=i.payload;if(null==o||!1===i.payload.active||null==u||null==m)return void r((0,s.E1)({active:!1,coordinate:void 0,dataKey:void 0,index:null,label:void 0}));var{x:c,y:h}=u,d=Math.min(c,m.x+m.width),y=Math.min(h,m.y+m.height),v={x:"horizontal"===g?o.coordinate:d,y:"horizontal"===g?y:o.coordinate};r((0,s.E1)({active:i.payload.active,coordinate:v,dataKey:i.payload.dataKey,index:String(o.index),label:i.payload.label}))}}};return o.on(l,n),()=>{o.off(l,n)}},[b,r,t,e,f,p,g,m]),w=(0,i.G)(a.lZ),x=(0,i.G)(a.pH),O=(0,i.j)(),(0,n.useEffect)(()=>{if(null==w)return v;var e=(e,t,r)=>{x!==r&&w===e&&O((0,y.M)(t))};return o.on(u,e),()=>{o.off(u,e)}},[O,x,w])}function m(e,t,r,u,c,h){var d=(0,i.G)(r=>(0,f.dp)(r,e,t)),y=(0,i.G)(a.pH),v=(0,i.G)(a.lZ),g=(0,i.G)(a.hX),m=(0,i.G)(p),b=null==m?void 0:m.active;(0,n.useEffect)(()=>{if(!b&&null!=v&&null!=y){var e=(0,s.E1)({active:h,coordinate:r,dataKey:d,index:c,label:"number"==typeof u?String(u):u});o.emit(l,v,e,y)}},[b,r,d,c,u,y,v,g,h])}},90657:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.getTag=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}},91625:(e,t,r)=>{"use strict";var n=r(94285);"function"==typeof Object.is&&Object.is,n.useSyncExternalStore,n.useRef,n.useEffect,n.useMemo,n.useDebugValue},91648:(e,t,r)=>{"use strict";r.d(t,{CA:()=>y,MC:()=>c,QG:()=>d,Vi:()=>u,cU:()=>s,fR:()=>f});var n=r(68646),i=r(74201);function a(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function o(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?a(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var l=(0,n.Z0)({name:"cartesianAxis",initialState:{xAxis:{},yAxis:{},zAxis:{}},reducers:{addXAxis(e,t){e.xAxis[t.payload.id]=(0,i.h4)(t.payload)},removeXAxis(e,t){delete e.xAxis[t.payload.id]},addYAxis(e,t){e.yAxis[t.payload.id]=(0,i.h4)(t.payload)},removeYAxis(e,t){delete e.yAxis[t.payload.id]},addZAxis(e,t){e.zAxis[t.payload.id]=(0,i.h4)(t.payload)},removeZAxis(e,t){delete e.zAxis[t.payload.id]},updateYAxisWidth(e,t){var{id:r,width:n}=t.payload;e.yAxis[r]&&(e.yAxis[r]=o(o({},e.yAxis[r]),{},{width:n}))}}}),{addXAxis:u,removeXAxis:c,addYAxis:s,removeYAxis:f,addZAxis:h,removeZAxis:p,updateYAxisWidth:d}=l.actions,y=l.reducer},91881:(e,t,r)=>{"use strict";r.d(t,{$g:()=>o,Hw:()=>a,Td:()=>u,au:()=>l,xH:()=>i});var n=r(4517),i=e=>e.options.defaultTooltipEventType,a=e=>e.options.validateTooltipEventTypes;function o(e,t,r){if(null==e)return t;var n=e?"axis":"item";return null==r?t:r.includes(n)?n:t}function l(e,t){return o(t,i(e),a(e))}function u(e){return(0,n.G)(t=>l(t,e))}},92389:(e,t,r)=>{"use strict";r.d(t,{i:()=>u});let n=Math.PI,i=2*n,a=i-1e-6;function o(e){this._+=e[0];for(let t=1,r=e.length;t<r;++t)this._+=arguments[t]+e[t]}class l{constructor(e){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==e?o:function(e){let t=Math.floor(e);if(!(t>=0))throw Error(`invalid digits: ${e}`);if(t>15)return o;let r=10**t;return function(e){this._+=e[0];for(let t=1,n=e.length;t<n;++t)this._+=Math.round(arguments[t]*r)/r+e[t]}}(e)}moveTo(e,t){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(e,t){this._append`L${this._x1=+e},${this._y1=+t}`}quadraticCurveTo(e,t,r,n){this._append`Q${+e},${+t},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(e,t,r,n,i,a){this._append`C${+e},${+t},${+r},${+n},${this._x1=+i},${this._y1=+a}`}arcTo(e,t,r,i,a){if(e*=1,t*=1,r*=1,i*=1,(a*=1)<0)throw Error(`negative radius: ${a}`);let o=this._x1,l=this._y1,u=r-e,c=i-t,s=o-e,f=l-t,h=s*s+f*f;if(null===this._x1)this._append`M${this._x1=e},${this._y1=t}`;else if(h>1e-6)if(Math.abs(f*u-c*s)>1e-6&&a){let p=r-o,d=i-l,y=u*u+c*c,v=Math.sqrt(y),g=Math.sqrt(h),m=a*Math.tan((n-Math.acos((y+h-(p*p+d*d))/(2*v*g)))/2),b=m/g,w=m/v;Math.abs(b-1)>1e-6&&this._append`L${e+b*s},${t+b*f}`,this._append`A${a},${a},0,0,${+(f*p>s*d)},${this._x1=e+w*u},${this._y1=t+w*c}`}else this._append`L${this._x1=e},${this._y1=t}`}arc(e,t,r,o,l,u){if(e*=1,t*=1,r*=1,u=!!u,r<0)throw Error(`negative radius: ${r}`);let c=r*Math.cos(o),s=r*Math.sin(o),f=e+c,h=t+s,p=1^u,d=u?o-l:l-o;null===this._x1?this._append`M${f},${h}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-h)>1e-6)&&this._append`L${f},${h}`,r&&(d<0&&(d=d%i+i),d>a?this._append`A${r},${r},0,1,${p},${e-c},${t-s}A${r},${r},0,1,${p},${this._x1=f},${this._y1=h}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${p},${this._x1=e+r*Math.cos(l)},${this._y1=t+r*Math.sin(l)}`)}rect(e,t,r,n){this._append`M${this._x0=this._x1=+e},${this._y0=this._y1=+t}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function u(e){let t=3;return e.digits=function(r){if(!arguments.length)return t;if(null==r)t=null;else{let e=Math.floor(r);if(!(e>=0))throw RangeError(`invalid digits: ${r}`);t=e}return e},()=>new l(t)}l.prototype},92602:(e,t,r)=>{"use strict";r.d(t,{HS:()=>o,LF:()=>i,z3:()=>a});var n=r(73474),i=e=>e.chartData,a=(0,n.Mz)([i],e=>{var t=null!=e.chartData?e.chartData.length-1:0;return{chartData:e.chartData,computedData:e.computedData,dataEndIndex:t,dataStartIndex:0}}),o=(e,t,r,n)=>n?a(e):i(e)},92621:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.toArray=function(e){return Array.isArray(e)?e:Array.from(e)}},92769:(e,t,r)=>{"use strict";r.d(t,{g:()=>c});var n=r(73474),i=r(21584),a=r(52084),o=r(90355),l=r(70474),u=r(78212),c=(0,n.Mz)([(e,t)=>t,i.fz,u.D0,a.Re,a.gL,a.R4,l.r1,o.HZ],l.aX)},92933:(e,t,r)=>{"use strict";r.d(t,{f:()=>n});var n=e=>null;n.displayName="Cell"},93819:(e,t,r)=>{e.exports=r(39150).uniqBy},94263:(e,t,r)=>{"use strict";r.d(t,{P:()=>c});var n=r(3157);function i(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function a(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var o={widthCache:{},cacheCount:0},l={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},u="recharts_measurement_span",c=function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==e||n.m.isSsr)return{width:0,height:0};var i=(Object.keys(t=a({},r)).forEach(e=>{t[e]||delete t[e]}),t),c=JSON.stringify({text:e,copyStyle:i});if(o.widthCache[c])return o.widthCache[c];try{var s=document.getElementById(u);s||((s=document.createElement("span")).setAttribute("id",u),s.setAttribute("aria-hidden","true"),document.body.appendChild(s));var f=a(a({},l),i);Object.assign(s.style,f),s.textContent="".concat(e);var h=s.getBoundingClientRect(),p={width:h.width,height:h.height};return o.widthCache[c]=p,++o.cacheCount>2e3&&(o.cacheCount=0,o.widthCache={}),p}catch(e){return{width:0,height:0}}}},94953:(e,t,r)=>{"use strict";r.d(t,{E:()=>n});var n=(e,t,r)=>r},95737:(e,t,r)=>{"use strict";function n(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}r.d(t,{A:()=>n}),Array.prototype.slice},96112:(e,t,r)=>{"use strict";r.d(t,{kz:()=>nD,fb:()=>nA,q:()=>nV,tP:()=>n3,g1:()=>it,iv:()=>iC,Nk:()=>nj,pM:()=>nT,Oz:()=>nW,tF:()=>ik,rj:()=>nx,ec:()=>ng,bb:()=>nJ,xp:()=>n7,wL:()=>n1,sr:()=>n8,Qn:()=>n9,MK:()=>n_,IO:()=>nb,P9:()=>nF,S5:()=>nR,PU:()=>na,cd:()=>nl,eo:()=>nd,yi:()=>nB,ZB:()=>iN,D5:()=>is,iV:()=>ip,Hd:()=>nh,Gx:()=>iL,DP:()=>nf,BQ:()=>i_,_y:()=>iB,AV:()=>n0,um:()=>np,xM:()=>n6,gT:()=>n$,Kr:()=>nU,$X:()=>nH,TC:()=>nk,Zi:()=>iI,CR:()=>iz,ld:()=>ny,L$:()=>iE,Rl:()=>no,Lw:()=>ix,KR:()=>iM,sf:()=>nu,wP:()=>iS});var n,i,a,o,l,u,c,s={};r.r(s),r.d(s,{scaleBand:()=>x,scaleDiverging:()=>function e(){var t=es(rA()(Z));return t.copy=function(){return rO(t,e())},y.apply(t,arguments)},scaleDivergingLog:()=>function e(){var t=em(rA()).domain([.1,1,10]);return t.copy=function(){return rO(t,e()).base(t.base())},y.apply(t,arguments)},scaleDivergingPow:()=>rE,scaleDivergingSqrt:()=>rM,scaleDivergingSymlog:()=>function e(){var t=ex(rA());return t.copy=function(){return rO(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleIdentity:()=>function e(t){var r;function n(e){return null==e||isNaN(e*=1)?r:e}return n.invert=n,n.domain=n.range=function(e){return arguments.length?(t=Array.from(e,G),n):t.slice()},n.unknown=function(e){return arguments.length?(r=e,n):r},n.copy=function(){return e(t).unknown(r)},t=arguments.length?Array.from(t,G):[0,1],es(n)},scaleImplicit:()=>b,scaleLinear:()=>function e(){var t=X();return t.copy=function(){return Y(t,e())},d.apply(t,arguments),es(t)},scaleLog:()=>function e(){let t=em(J()).domain([1,10]);return t.copy=()=>Y(t,e()).base(t.base()),d.apply(t,arguments),t},scaleOrdinal:()=>w,scalePoint:()=>O,scalePow:()=>eE,scaleQuantile:()=>function e(){var t,r=[],n=[],i=[];function a(){var e=0,t=Math.max(1,n.length);for(i=Array(t-1);++e<t;)i[e-1]=function(e,t,r=N){if(!(!(n=e.length)||isNaN(t*=1))){if(t<=0||n<2)return+r(e[0],0,e);if(t>=1)return+r(e[n-1],n-1,e);var n,i=(n-1)*t,a=Math.floor(i),o=+r(e[a],a,e);return o+(r(e[a+1],a+1,e)-o)*(i-a)}}(r,e/t);return o}function o(e){return null==e||isNaN(e*=1)?t:n[z(i,e)]}return o.invertExtent=function(e){var t=n.indexOf(e);return t<0?[NaN,NaN]:[t>0?i[t-1]:r[0],t<i.length?i[t]:r[r.length-1]]},o.domain=function(e){if(!arguments.length)return r.slice();for(let t of(r=[],e))null==t||isNaN(t*=1)||r.push(t);return r.sort(k),a()},o.range=function(e){return arguments.length?(n=Array.from(e),a()):n.slice()},o.unknown=function(e){return arguments.length?(t=e,o):t},o.quantiles=function(){return i.slice()},o.copy=function(){return e().domain(r).range(n).unknown(t)},d.apply(o,arguments)},scaleQuantize:()=>function e(){var t,r=0,n=1,i=1,a=[.5],o=[0,1];function l(e){return null!=e&&e<=e?o[z(a,e,0,i)]:t}function u(){var e=-1;for(a=Array(i);++e<i;)a[e]=((e+1)*n-(e-i)*r)/(i+1);return l}return l.domain=function(e){return arguments.length?([r,n]=e,r*=1,n*=1,u()):[r,n]},l.range=function(e){return arguments.length?(i=(o=Array.from(e)).length-1,u()):o.slice()},l.invertExtent=function(e){var t=o.indexOf(e);return t<0?[NaN,NaN]:t<1?[r,a[0]]:t>=i?[a[i-1],n]:[a[t-1],a[t]]},l.unknown=function(e){return arguments.length&&(t=e),l},l.thresholds=function(){return a.slice()},l.copy=function(){return e().domain([r,n]).range(o).unknown(t)},d.apply(es(l),arguments)},scaleRadial:()=>function e(){var t,r=X(),n=[0,1],i=!1;function a(e){var n,a=Math.sign(n=r(e))*Math.sqrt(Math.abs(n));return isNaN(a)?t:i?Math.round(a):a}return a.invert=function(e){return r.invert(eS(e))},a.domain=function(e){return arguments.length?(r.domain(e),a):r.domain()},a.range=function(e){return arguments.length?(r.range((n=Array.from(e,G)).map(eS)),a):n.slice()},a.rangeRound=function(e){return a.range(e).round(!0)},a.round=function(e){return arguments.length?(i=!!e,a):i},a.clamp=function(e){return arguments.length?(r.clamp(e),a):r.clamp()},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e(r.domain(),n).round(i).clamp(r.clamp()).unknown(t)},d.apply(a,arguments),es(a)},scaleSequential:()=>function e(){var t=es(rx()(Z));return t.copy=function(){return rO(t,e())},y.apply(t,arguments)},scaleSequentialLog:()=>function e(){var t=em(rx()).domain([1,10]);return t.copy=function(){return rO(t,e()).base(t.base())},y.apply(t,arguments)},scaleSequentialPow:()=>rj,scaleSequentialQuantile:()=>function e(){var t=[],r=Z;function n(e){if(null!=e&&!isNaN(e*=1))return r((z(t,e,1)-1)/(t.length-1))}return n.domain=function(e){if(!arguments.length)return t.slice();for(let r of(t=[],e))null==r||isNaN(r*=1)||t.push(r);return t.sort(k),n},n.interpolator=function(e){return arguments.length?(r=e,n):r},n.range=function(){return t.map((e,n)=>r(n/(t.length-1)))},n.quantiles=function(e){return Array.from({length:e+1},(r,n)=>(function(e,t,r){if(!(!(n=(e=Float64Array.from(function*(e,t){if(void 0===t)for(let t of e)null!=t&&(t*=1)>=t&&(yield t);else{let r=-1;for(let n of e)null!=(n=t(n,++r,e))&&(n*=1)>=n&&(yield n)}}(e,void 0))).length)||isNaN(t*=1))){if(t<=0||n<2)return ek(e);if(t>=1)return e_(e);var n,i=(n-1)*t,a=Math.floor(i),o=e_((function e(t,r,n=0,i=1/0,a){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(t.length-1,i)),!(n<=r&&r<=i))return t;for(a=void 0===a?eT:function(e=k){if(e===k)return eT;if("function"!=typeof e)throw TypeError("compare is not a function");return(t,r)=>{let n=e(t,r);return n||0===n?n:(0===e(r,r))-(0===e(t,t))}}(a);i>n;){if(i-n>600){let o=i-n+1,l=r-n+1,u=Math.log(o),c=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*c*(o-c)/o)*(l-o/2<0?-1:1),f=Math.max(n,Math.floor(r-l*c/o+s)),h=Math.min(i,Math.floor(r+(o-l)*c/o+s));e(t,r,f,h,a)}let o=t[r],l=n,u=i;for(eC(t,n,r),a(t[i],o)>0&&eC(t,n,i);l<u;){for(eC(t,l,u),++l,--u;0>a(t[l],o);)++l;for(;a(t[u],o)>0;)--u}0===a(t[n],o)?eC(t,n,u):eC(t,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return t})(e,a).subarray(0,a+1));return o+(ek(e.subarray(a+1))-o)*(i-a)}})(t,n/e))},n.copy=function(){return e(r).domain(t)},y.apply(n,arguments)},scaleSequentialSqrt:()=>rP,scaleSequentialSymlog:()=>function e(){var t=ex(rx());return t.copy=function(){return rO(t,e()).constant(t.constant())},y.apply(t,arguments)},scaleSqrt:()=>eM,scaleSymlog:()=>function e(){var t=ex(J());return t.copy=function(){return Y(t,e()).constant(t.constant())},d.apply(t,arguments)},scaleThreshold:()=>function e(){var t,r=[.5],n=[0,1],i=1;function a(e){return null!=e&&e<=e?n[z(r,e,0,i)]:t}return a.domain=function(e){return arguments.length?(i=Math.min((r=Array.from(e)).length,n.length-1),a):r.slice()},a.range=function(e){return arguments.length?(n=Array.from(e),i=Math.min(r.length,n.length-1),a):n.slice()},a.invertExtent=function(e){var t=n.indexOf(e);return[r[t-1],r[t]]},a.unknown=function(e){return arguments.length?(t=e,a):t},a.copy=function(){return e().domain(r).range(n).unknown(t)},d.apply(a,arguments)},scaleTime:()=>rb,scaleUtc:()=>rw,tickFormat:()=>ec});var f=r(73474),h=r(45256),p=r.n(h);function d(e,t){switch(arguments.length){case 0:break;case 1:this.range(e);break;default:this.range(t).domain(e)}return this}function y(e,t){switch(arguments.length){case 0:break;case 1:"function"==typeof e?this.interpolator(e):this.range(e);break;default:this.domain(e),"function"==typeof t?this.interpolator(t):this.range(t)}return this}class v extends Map{constructor(e,t=m){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:t}}),null!=e)for(let[t,r]of e)this.set(t,r)}get(e){return super.get(g(this,e))}has(e){return super.has(g(this,e))}set(e,t){return super.set(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):(e.set(n,r),r)}(this,e),t)}delete(e){return super.delete(function({_intern:e,_key:t},r){let n=t(r);return e.has(n)&&(r=e.get(n),e.delete(n)),r}(this,e))}}function g({_intern:e,_key:t},r){let n=t(r);return e.has(n)?e.get(n):r}function m(e){return null!==e&&"object"==typeof e?e.valueOf():e}let b=Symbol("implicit");function w(){var e=new v,t=[],r=[],n=b;function i(i){let a=e.get(i);if(void 0===a){if(n!==b)return n;e.set(i,a=t.push(i)-1)}return r[a%r.length]}return i.domain=function(r){if(!arguments.length)return t.slice();for(let n of(t=[],e=new v,r))e.has(n)||e.set(n,t.push(n)-1);return i},i.range=function(e){return arguments.length?(r=Array.from(e),i):r.slice()},i.unknown=function(e){return arguments.length?(n=e,i):n},i.copy=function(){return w(t,r).unknown(n)},d.apply(i,arguments),i}function x(){var e,t,r=w().unknown(void 0),n=r.domain,i=r.range,a=0,o=1,l=!1,u=0,c=0,s=.5;function f(){var r=n().length,f=o<a,h=f?o:a,p=f?a:o;e=(p-h)/Math.max(1,r-u+2*c),l&&(e=Math.floor(e)),h+=(p-h-e*(r-u))*s,t=e*(1-u),l&&(h=Math.round(h),t=Math.round(t));var d=(function(e,t,r){e*=1,t*=1,r=(i=arguments.length)<2?(t=e,e=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((t-e)/r)),a=Array(i);++n<i;)a[n]=e+n*r;return a})(r).map(function(t){return h+e*t});return i(f?d.reverse():d)}return delete r.unknown,r.domain=function(e){return arguments.length?(n(e),f()):n()},r.range=function(e){return arguments.length?([a,o]=e,a*=1,o*=1,f()):[a,o]},r.rangeRound=function(e){return[a,o]=e,a*=1,o*=1,l=!0,f()},r.bandwidth=function(){return t},r.step=function(){return e},r.round=function(e){return arguments.length?(l=!!e,f()):l},r.padding=function(e){return arguments.length?(u=Math.min(1,c=+e),f()):u},r.paddingInner=function(e){return arguments.length?(u=Math.min(1,e),f()):u},r.paddingOuter=function(e){return arguments.length?(c=+e,f()):c},r.align=function(e){return arguments.length?(s=Math.max(0,Math.min(1,e)),f()):s},r.copy=function(){return x(n(),[a,o]).round(l).paddingInner(u).paddingOuter(c).align(s)},d.apply(f(),arguments)}function O(){return function e(t){var r=t.copy;return t.padding=t.paddingOuter,delete t.paddingInner,delete t.paddingOuter,t.copy=function(){return e(r())},t}(x.apply(null,arguments).paddingInner(1))}let j=Math.sqrt(50),P=Math.sqrt(10),A=Math.sqrt(2);function E(e,t,r){let n,i,a,o=(t-e)/Math.max(0,r),l=Math.floor(Math.log10(o)),u=o/Math.pow(10,l),c=u>=j?10:u>=P?5:u>=A?2:1;return(l<0?(n=Math.round(e*(a=Math.pow(10,-l)/c)),i=Math.round(t*a),n/a<e&&++n,i/a>t&&--i,a=-a):(n=Math.round(e/(a=Math.pow(10,l)*c)),i=Math.round(t/a),n*a<e&&++n,i*a>t&&--i),i<n&&.5<=r&&r<2)?E(e,t,2*r):[n,i,a]}function M(e,t,r){if(t*=1,e*=1,!((r*=1)>0))return[];if(e===t)return[e];let n=t<e,[i,a,o]=n?E(t,e,r):E(e,t,r);if(!(a>=i))return[];let l=a-i+1,u=Array(l);if(n)if(o<0)for(let e=0;e<l;++e)u[e]=-((a-e)/o);else for(let e=0;e<l;++e)u[e]=(a-e)*o;else if(o<0)for(let e=0;e<l;++e)u[e]=-((i+e)/o);else for(let e=0;e<l;++e)u[e]=(i+e)*o;return u}function S(e,t,r){return E(e*=1,t*=1,r*=1)[2]}function _(e,t,r){t*=1,e*=1,r*=1;let n=t<e,i=n?S(t,e,r):S(e,t,r);return(n?-1:1)*(i<0?-(1/i):i)}function k(e,t){return null==e||null==t?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function T(e,t){return null==e||null==t?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function C(e){let t,r,n;function i(e,n,a=0,o=e.length){if(a<o){if(0!==t(n,n))return o;do{let t=a+o>>>1;0>r(e[t],n)?a=t+1:o=t}while(a<o)}return a}return 2!==e.length?(t=k,r=(t,r)=>k(e(t),r),n=(t,r)=>e(t)-r):(t=e===k||e===T?e:D,r=e,n=e),{left:i,center:function(e,t,r=0,a=e.length){let o=i(e,t,r,a-1);return o>r&&n(e[o-1],t)>-n(e[o],t)?o-1:o},right:function(e,n,i=0,a=e.length){if(i<a){if(0!==t(n,n))return a;do{let t=i+a>>>1;0>=r(e[t],n)?i=t+1:a=t}while(i<a)}return i}}}function D(){return 0}function N(e){return null===e?NaN:+e}let I=C(k),z=I.right;I.left,C(N).center;var L=r(62191),R=r(83576),B=r(60622),U=r(48406),F=r(61787);function K(e,t){var r,n,i=typeof t;return null==t||"boolean"===i?(0,F.A)(t):("number"===i?B.A:"string"===i?(n=(0,L.Ay)(t))?(t=n,R.Ay):U.A:t instanceof L.Ay?R.Ay:t instanceof Date?function(e,t){var r=new Date;return e*=1,t*=1,function(n){return r.setTime(e*(1-n)+t*n),r}}:!ArrayBuffer.isView(r=t)||r instanceof DataView?Array.isArray(t)?function(e,t){var r,n=t?t.length:0,i=e?Math.min(n,e.length):0,a=Array(i),o=Array(n);for(r=0;r<i;++r)a[r]=K(e[r],t[r]);for(;r<n;++r)o[r]=t[r];return function(e){for(r=0;r<i;++r)o[r]=a[r](e);return o}}:"function"!=typeof t.valueOf&&"function"!=typeof t.toString||isNaN(t)?function(e,t){var r,n={},i={};for(r in(null===e||"object"!=typeof e)&&(e={}),(null===t||"object"!=typeof t)&&(t={}),t)r in e?n[r]=K(e[r],t[r]):i[r]=t[r];return function(e){for(r in n)i[r]=n[r](e);return i}}:B.A:function(e,t){t||(t=[]);var r,n=e?Math.min(t.length,e.length):0,i=t.slice();return function(a){for(r=0;r<n;++r)i[r]=e[r]*(1-a)+t[r]*a;return i}})(e,t)}function $(e,t){return e*=1,t*=1,function(r){return Math.round(e*(1-r)+t*r)}}function G(e){return+e}var H=[0,1];function Z(e){return e}function W(e,t){var r;return(t-=e*=1)?function(r){return(r-e)/t}:(r=isNaN(t)?NaN:.5,function(){return r})}function q(e,t,r){var n=e[0],i=e[1],a=t[0],o=t[1];return i<n?(n=W(i,n),a=r(o,a)):(n=W(n,i),a=r(a,o)),function(e){return a(n(e))}}function V(e,t,r){var n=Math.min(e.length,t.length)-1,i=Array(n),a=Array(n),o=-1;for(e[n]<e[0]&&(e=e.slice().reverse(),t=t.slice().reverse());++o<n;)i[o]=W(e[o],e[o+1]),a[o]=r(t[o],t[o+1]);return function(t){var r=z(e,t,1,n)-1;return a[r](i[r](t))}}function Y(e,t){return t.domain(e.domain()).range(e.range()).interpolate(e.interpolate()).clamp(e.clamp()).unknown(e.unknown())}function J(){var e,t,r,n,i,a,o=H,l=H,u=K,c=Z;function s(){var e,t,r,u=Math.min(o.length,l.length);return c!==Z&&(e=o[0],t=o[u-1],e>t&&(r=e,e=t,t=r),c=function(r){return Math.max(e,Math.min(t,r))}),n=u>2?V:q,i=a=null,f}function f(t){return null==t||isNaN(t*=1)?r:(i||(i=n(o.map(e),l,u)))(e(c(t)))}return f.invert=function(r){return c(t((a||(a=n(l,o.map(e),B.A)))(r)))},f.domain=function(e){return arguments.length?(o=Array.from(e,G),s()):o.slice()},f.range=function(e){return arguments.length?(l=Array.from(e),s()):l.slice()},f.rangeRound=function(e){return l=Array.from(e),u=$,s()},f.clamp=function(e){return arguments.length?(c=!!e||Z,s()):c!==Z},f.interpolate=function(e){return arguments.length?(u=e,s()):u},f.unknown=function(e){return arguments.length?(r=e,f):r},function(r,n){return e=r,t=n,s()}}function X(){return J()(Z,Z)}var Q=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ee(e){var t;if(!(t=Q.exec(e)))throw Error("invalid format: "+e);return new et({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function et(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function er(e,t){if((r=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var r,n=e.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+e.slice(r+1)]}function en(e){return(e=er(Math.abs(e)))?e[1]:NaN}function ei(e,t){var r=er(e,t);if(!r)return e+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}ee.prototype=et.prototype,et.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ea={"%":(e,t)=>(100*e).toFixed(t),b:e=>Math.round(e).toString(2),c:e=>e+"",d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:(e,t)=>e.toExponential(t),f:(e,t)=>e.toFixed(t),g:(e,t)=>e.toPrecision(t),o:e=>Math.round(e).toString(8),p:(e,t)=>ei(100*e,t),r:ei,s:function(e,t){var r=er(e,t);if(!r)return e+"";var i=r[0],a=r[1],o=a-(n=3*Math.max(-8,Math.min(8,Math.floor(a/3))))+1,l=i.length;return o===l?i:o>l?i+Array(o-l+1).join("0"):o>0?i.slice(0,o)+"."+i.slice(o):"0."+Array(1-o).join("0")+er(e,Math.max(0,t+o-1))[0]},X:e=>Math.round(e).toString(16).toUpperCase(),x:e=>Math.round(e).toString(16)};function eo(e){return e}var el=Array.prototype.map,eu=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ec(e,t,r,n){var i,l,u,c=_(e,t,r);switch((n=ee(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(e),Math.abs(t));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(en(s)/3)))-en(Math.abs(c))))||(n.precision=u),o(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,en(Math.abs(Math.max(Math.abs(e),Math.abs(t)))-(i=Math.abs(i=c)))-en(i))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-en(Math.abs(c))))||(n.precision=u-("%"===n.type)*2)}return a(n)}function es(e){var t=e.domain;return e.ticks=function(e){var r=t();return M(r[0],r[r.length-1],null==e?10:e)},e.tickFormat=function(e,r){var n=t();return ec(n[0],n[n.length-1],null==e?10:e,r)},e.nice=function(r){null==r&&(r=10);var n,i,a=t(),o=0,l=a.length-1,u=a[o],c=a[l],s=10;for(c<u&&(i=u,u=c,c=i,i=o,o=l,l=i);s-- >0;){if((i=S(u,c,r))===n)return a[o]=u,a[l]=c,t(a);if(i>0)u=Math.floor(u/i)*i,c=Math.ceil(c/i)*i;else if(i<0)u=Math.ceil(u*i)/i,c=Math.floor(c*i)/i;else break;n=i}return e},e}function ef(e,t){e=e.slice();var r,n=0,i=e.length-1,a=e[n],o=e[i];return o<a&&(r=n,n=i,i=r,r=a,a=o,o=r),e[n]=t.floor(a),e[i]=t.ceil(o),e}function eh(e){return Math.log(e)}function ep(e){return Math.exp(e)}function ed(e){return-Math.log(-e)}function ey(e){return-Math.exp(-e)}function ev(e){return isFinite(e)?+("1e"+e):e<0?0:e}function eg(e){return(t,r)=>-e(-t,r)}function em(e){let t,r,n=e(eh,ep),i=n.domain,o=10;function l(){var a,l;return t=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),e=>Math.log(e)/a),r=10===(l=o)?ev:l===Math.E?Math.exp:e=>Math.pow(l,e),i()[0]<0?(t=eg(t),r=eg(r),e(ed,ey)):e(eh,ep),n}return n.base=function(e){return arguments.length?(o=+e,l()):o},n.domain=function(e){return arguments.length?(i(e),l()):i()},n.ticks=e=>{let n,a,l=i(),u=l[0],c=l[l.length-1],s=c<u;s&&([u,c]=[c,u]);let f=t(u),h=t(c),p=null==e?10:+e,d=[];if(!(o%1)&&h-f<p){if(f=Math.floor(f),h=Math.ceil(h),u>0){for(;f<=h;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>c)break;d.push(a)}}else for(;f<=h;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>c)break;d.push(a)}2*d.length<p&&(d=M(u,c,p))}else d=M(f,h,Math.min(h-f,p)).map(r);return s?d.reverse():d},n.tickFormat=(e,i)=>{if(null==e&&(e=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=ee(i)).precision||(i.trim=!0),i=a(i)),e===1/0)return i;let l=Math.max(1,o*e/n.ticks().length);return e=>{let n=e/r(Math.round(t(e)));return n*o<o-.5&&(n*=o),n<=l?i(e):""}},n.nice=()=>i(ef(i(),{floor:e=>r(Math.floor(t(e))),ceil:e=>r(Math.ceil(t(e)))})),n}function eb(e){return function(t){return Math.sign(t)*Math.log1p(Math.abs(t/e))}}function ew(e){return function(t){return Math.sign(t)*Math.expm1(Math.abs(t))*e}}function ex(e){var t=1,r=e(eb(1),ew(t));return r.constant=function(r){return arguments.length?e(eb(t=+r),ew(t)):t},es(r)}function eO(e){return function(t){return t<0?-Math.pow(-t,e):Math.pow(t,e)}}function ej(e){return e<0?-Math.sqrt(-e):Math.sqrt(e)}function eP(e){return e<0?-e*e:e*e}function eA(e){var t=e(Z,Z),r=1;return t.exponent=function(t){return arguments.length?1==(r=+t)?e(Z,Z):.5===r?e(ej,eP):e(eO(r),eO(1/r)):r},es(t)}function eE(){var e=eA(J());return e.copy=function(){return Y(e,eE()).exponent(e.exponent())},d.apply(e,arguments),e}function eM(){return eE.apply(null,arguments).exponent(.5)}function eS(e){return Math.sign(e)*e*e}function e_(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r<t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function ek(e,t){let r;if(void 0===t)for(let t of e)null!=t&&(r>t||void 0===r&&t>=t)&&(r=t);else{let n=-1;for(let i of e)null!=(i=t(i,++n,e))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}a=(i=function(e){var t,r,i,a=void 0===e.grouping||void 0===e.thousands?eo:(t=el.call(e.grouping,Number),r=e.thousands+"",function(e,n){for(var i=e.length,a=[],o=0,l=t[0],u=0;i>0&&l>0&&(u+l+1>n&&(l=Math.max(1,n-u)),a.push(e.substring(i-=l,i+l)),!((u+=l+1)>n));)l=t[o=(o+1)%t.length];return a.reverse().join(r)}),o=void 0===e.currency?"":e.currency[0]+"",l=void 0===e.currency?"":e.currency[1]+"",u=void 0===e.decimal?".":e.decimal+"",c=void 0===e.numerals?eo:(i=el.call(e.numerals,String),function(e){return e.replace(/[0-9]/g,function(e){return i[+e]})}),s=void 0===e.percent?"%":e.percent+"",f=void 0===e.minus?"−":e.minus+"",h=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ee(e)).fill,r=e.align,i=e.sign,p=e.symbol,d=e.zero,y=e.width,v=e.comma,g=e.precision,m=e.trim,b=e.type;"n"===b?(v=!0,b="g"):ea[b]||(void 0===g&&(g=12),m=!0,b="g"),(d||"0"===t&&"="===r)&&(d=!0,t="0",r="=");var w="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?l:/[%p]/.test(b)?s:"",O=ea[b],j=/[defgprs%]/.test(b);function P(e){var o,l,s,p=w,P=x;if("c"===b)P=O(e)+P,e="";else{var A=(e*=1)<0||1/e<0;if(e=isNaN(e)?h:O(Math.abs(e),g),m&&(e=function(e){e:for(var t,r=e.length,n=1,i=-1;n<r;++n)switch(e[n]){case".":i=t=n;break;case"0":0===i&&(i=n),t=n;break;default:if(!+e[n])break e;i>0&&(i=0)}return i>0?e.slice(0,i)+e.slice(t+1):e}(e)),A&&0==+e&&"+"!==i&&(A=!1),p=(A?"("===i?i:f:"-"===i||"("===i?"":i)+p,P=("s"===b?eu[8+n/3]:"")+P+(A&&"("===i?")":""),j){for(o=-1,l=e.length;++o<l;)if(48>(s=e.charCodeAt(o))||s>57){P=(46===s?u+e.slice(o+1):e.slice(o))+P,e=e.slice(0,o);break}}}v&&!d&&(e=a(e,1/0));var E=p.length+e.length+P.length,M=E<y?Array(y-E+1).join(t):"";switch(v&&d&&(e=a(M+e,M.length?y-P.length:1/0),M=""),r){case"<":e=p+e+P+M;break;case"=":e=p+M+e+P;break;case"^":e=M.slice(0,E=M.length>>1)+p+e+P+M.slice(E);break;default:e=M+p+e+P}return c(e)}return g=void 0===g?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,g)):Math.max(0,Math.min(20,g)),P.toString=function(){return e+""},P}return{format:p,formatPrefix:function(e,t){var r=p(((e=ee(e)).type="f",e)),n=3*Math.max(-8,Math.min(8,Math.floor(en(t)/3))),i=Math.pow(10,-n),a=eu[8+n/3];return function(e){return r(i*e)+a}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,o=i.formatPrefix;function eT(e,t){return(null==e||!(e>=e))-(null==t||!(t>=t))||(e<t?-1:+(e>t))}function eC(e,t,r){let n=e[t];e[t]=e[r],e[r]=n}let eD=new Date,eN=new Date;function eI(e,t,r,n){function i(t){return e(t=0==arguments.length?new Date:new Date(+t)),t}return i.floor=t=>(e(t=new Date(+t)),t),i.ceil=r=>(e(r=new Date(r-1)),t(r,1),e(r),r),i.round=e=>{let t=i(e),r=i.ceil(e);return e-t<r-e?t:r},i.offset=(e,r)=>(t(e=new Date(+e),null==r?1:Math.floor(r)),e),i.range=(r,n,a)=>{let o,l=[];if(r=i.ceil(r),a=null==a?1:Math.floor(a),!(r<n)||!(a>0))return l;do l.push(o=new Date(+r)),t(r,a),e(r);while(o<r&&r<n);return l},i.filter=r=>eI(t=>{if(t>=t)for(;e(t),!r(t);)t.setTime(t-1)},(e,n)=>{if(e>=e)if(n<0)for(;++n<=0;)for(;t(e,-1),!r(e););else for(;--n>=0;)for(;t(e,1),!r(e););}),r&&(i.count=(t,n)=>(eD.setTime(+t),eN.setTime(+n),e(eD),e(eN),Math.floor(r(eD,eN))),i.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?i.filter(n?t=>n(t)%e==0:t=>i.count(0,t)%e==0):i:null),i}let ez=eI(()=>{},(e,t)=>{e.setTime(+e+t)},(e,t)=>t-e);ez.every=e=>isFinite(e=Math.floor(e))&&e>0?e>1?eI(t=>{t.setTime(Math.floor(t/e)*e)},(t,r)=>{t.setTime(+t+r*e)},(t,r)=>(r-t)/e):ez:null,ez.range;let eL=eI(e=>{e.setTime(e-e.getMilliseconds())},(e,t)=>{e.setTime(+e+1e3*t)},(e,t)=>(t-e)/1e3,e=>e.getUTCSeconds());eL.range;let eR=eI(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds())},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getMinutes());eR.range;let eB=eI(e=>{e.setUTCSeconds(0,0)},(e,t)=>{e.setTime(+e+6e4*t)},(e,t)=>(t-e)/6e4,e=>e.getUTCMinutes());eB.range;let eU=eI(e=>{e.setTime(e-e.getMilliseconds()-1e3*e.getSeconds()-6e4*e.getMinutes())},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getHours());eU.range;let eF=eI(e=>{e.setUTCMinutes(0,0,0)},(e,t)=>{e.setTime(+e+36e5*t)},(e,t)=>(t-e)/36e5,e=>e.getUTCHours());eF.range;let eK=eI(e=>e.setHours(0,0,0,0),(e,t)=>e.setDate(e.getDate()+t),(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/864e5,e=>e.getDate()-1);eK.range;let e$=eI(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>e.getUTCDate()-1);e$.range;let eG=eI(e=>{e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+t)},(e,t)=>(t-e)/864e5,e=>Math.floor(e/864e5));function eH(e){return eI(t=>{t.setDate(t.getDate()-(t.getDay()+7-e)%7),t.setHours(0,0,0,0)},(e,t)=>{e.setDate(e.getDate()+7*t)},(e,t)=>(t-e-(t.getTimezoneOffset()-e.getTimezoneOffset())*6e4)/6048e5)}eG.range;let eZ=eH(0),eW=eH(1),eq=eH(2),eV=eH(3),eY=eH(4),eJ=eH(5),eX=eH(6);function eQ(e){return eI(t=>{t.setUTCDate(t.getUTCDate()-(t.getUTCDay()+7-e)%7),t.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCDate(e.getUTCDate()+7*t)},(e,t)=>(t-e)/6048e5)}eZ.range,eW.range,eq.range,eV.range,eY.range,eJ.range,eX.range;let e0=eQ(0),e1=eQ(1),e2=eQ(2),e4=eQ(3),e3=eQ(4),e5=eQ(5),e8=eQ(6);e0.range,e1.range,e2.range,e4.range,e3.range,e5.range,e8.range;let e6=eI(e=>{e.setDate(1),e.setHours(0,0,0,0)},(e,t)=>{e.setMonth(e.getMonth()+t)},(e,t)=>t.getMonth()-e.getMonth()+(t.getFullYear()-e.getFullYear())*12,e=>e.getMonth());e6.range;let e9=eI(e=>{e.setUTCDate(1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCMonth(e.getUTCMonth()+t)},(e,t)=>t.getUTCMonth()-e.getUTCMonth()+(t.getUTCFullYear()-e.getUTCFullYear())*12,e=>e.getUTCMonth());e9.range;let e7=eI(e=>{e.setMonth(0,1),e.setHours(0,0,0,0)},(e,t)=>{e.setFullYear(e.getFullYear()+t)},(e,t)=>t.getFullYear()-e.getFullYear(),e=>e.getFullYear());e7.every=e=>isFinite(e=Math.floor(e))&&e>0?eI(t=>{t.setFullYear(Math.floor(t.getFullYear()/e)*e),t.setMonth(0,1),t.setHours(0,0,0,0)},(t,r)=>{t.setFullYear(t.getFullYear()+r*e)}):null,e7.range;let te=eI(e=>{e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,t)=>{e.setUTCFullYear(e.getUTCFullYear()+t)},(e,t)=>t.getUTCFullYear()-e.getUTCFullYear(),e=>e.getUTCFullYear());function tt(e,t,r,n,i,a){let o=[[eL,1,1e3],[eL,5,5e3],[eL,15,15e3],[eL,30,3e4],[a,1,6e4],[a,5,3e5],[a,15,9e5],[a,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[t,1,2592e6],[t,3,7776e6],[e,1,31536e6]];function l(t,r,n){let i=Math.abs(r-t)/n,a=C(([,,e])=>e).right(o,i);if(a===o.length)return e.every(_(t/31536e6,r/31536e6,n));if(0===a)return ez.every(Math.max(_(t,r,n),1));let[l,u]=o[i/o[a-1][2]<o[a][2]/i?a-1:a];return l.every(u)}return[function(e,t,r){let n=t<e;n&&([e,t]=[t,e]);let i=r&&"function"==typeof r.range?r:l(e,t,r),a=i?i.range(e,+t+1):[];return n?a.reverse():a},l]}te.every=e=>isFinite(e=Math.floor(e))&&e>0?eI(t=>{t.setUTCFullYear(Math.floor(t.getUTCFullYear()/e)*e),t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,r)=>{t.setUTCFullYear(t.getUTCFullYear()+r*e)}):null,te.range;let[tr,tn]=tt(te,e9,e0,eG,eF,eB),[ti,ta]=tt(e7,e6,eZ,eK,eU,eR);function to(e){if(0<=e.y&&e.y<100){var t=new Date(-1,e.m,e.d,e.H,e.M,e.S,e.L);return t.setFullYear(e.y),t}return new Date(e.y,e.m,e.d,e.H,e.M,e.S,e.L)}function tl(e){if(0<=e.y&&e.y<100){var t=new Date(Date.UTC(-1,e.m,e.d,e.H,e.M,e.S,e.L));return t.setUTCFullYear(e.y),t}return new Date(Date.UTC(e.y,e.m,e.d,e.H,e.M,e.S,e.L))}function tu(e,t,r){return{y:e,m:t,d:r,H:0,M:0,S:0,L:0}}var tc={"-":"",_:" ",0:"0"},ts=/^\s*\d+/,tf=/^%/,th=/[\\^$*+?|[\]().{}]/g;function tp(e,t,r){var n=e<0?"-":"",i=(n?-e:e)+"",a=i.length;return n+(a<r?Array(r-a+1).join(t)+i:i)}function td(e){return e.replace(th,"\\$&")}function ty(e){return RegExp("^(?:"+e.map(td).join("|")+")","i")}function tv(e){return new Map(e.map((e,t)=>[e.toLowerCase(),t]))}function tg(e,t,r){var n=ts.exec(t.slice(r,r+1));return n?(e.w=+n[0],r+n[0].length):-1}function tm(e,t,r){var n=ts.exec(t.slice(r,r+1));return n?(e.u=+n[0],r+n[0].length):-1}function tb(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.U=+n[0],r+n[0].length):-1}function tw(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.V=+n[0],r+n[0].length):-1}function tx(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.W=+n[0],r+n[0].length):-1}function tO(e,t,r){var n=ts.exec(t.slice(r,r+4));return n?(e.y=+n[0],r+n[0].length):-1}function tj(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function tP(e,t,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(t.slice(r,r+6));return n?(e.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function tA(e,t,r){var n=ts.exec(t.slice(r,r+1));return n?(e.q=3*n[0]-3,r+n[0].length):-1}function tE(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.m=n[0]-1,r+n[0].length):-1}function tM(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.d=+n[0],r+n[0].length):-1}function tS(e,t,r){var n=ts.exec(t.slice(r,r+3));return n?(e.m=0,e.d=+n[0],r+n[0].length):-1}function t_(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.H=+n[0],r+n[0].length):-1}function tk(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.M=+n[0],r+n[0].length):-1}function tT(e,t,r){var n=ts.exec(t.slice(r,r+2));return n?(e.S=+n[0],r+n[0].length):-1}function tC(e,t,r){var n=ts.exec(t.slice(r,r+3));return n?(e.L=+n[0],r+n[0].length):-1}function tD(e,t,r){var n=ts.exec(t.slice(r,r+6));return n?(e.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function tN(e,t,r){var n=tf.exec(t.slice(r,r+1));return n?r+n[0].length:-1}function tI(e,t,r){var n=ts.exec(t.slice(r));return n?(e.Q=+n[0],r+n[0].length):-1}function tz(e,t,r){var n=ts.exec(t.slice(r));return n?(e.s=+n[0],r+n[0].length):-1}function tL(e,t){return tp(e.getDate(),t,2)}function tR(e,t){return tp(e.getHours(),t,2)}function tB(e,t){return tp(e.getHours()%12||12,t,2)}function tU(e,t){return tp(1+eK.count(e7(e),e),t,3)}function tF(e,t){return tp(e.getMilliseconds(),t,3)}function tK(e,t){return tF(e,t)+"000"}function t$(e,t){return tp(e.getMonth()+1,t,2)}function tG(e,t){return tp(e.getMinutes(),t,2)}function tH(e,t){return tp(e.getSeconds(),t,2)}function tZ(e){var t=e.getDay();return 0===t?7:t}function tW(e,t){return tp(eZ.count(e7(e)-1,e),t,2)}function tq(e){var t=e.getDay();return t>=4||0===t?eY(e):eY.ceil(e)}function tV(e,t){return e=tq(e),tp(eY.count(e7(e),e)+(4===e7(e).getDay()),t,2)}function tY(e){return e.getDay()}function tJ(e,t){return tp(eW.count(e7(e)-1,e),t,2)}function tX(e,t){return tp(e.getFullYear()%100,t,2)}function tQ(e,t){return tp((e=tq(e)).getFullYear()%100,t,2)}function t0(e,t){return tp(e.getFullYear()%1e4,t,4)}function t1(e,t){var r=e.getDay();return tp((e=r>=4||0===r?eY(e):eY.ceil(e)).getFullYear()%1e4,t,4)}function t2(e){var t=e.getTimezoneOffset();return(t>0?"-":(t*=-1,"+"))+tp(t/60|0,"0",2)+tp(t%60,"0",2)}function t4(e,t){return tp(e.getUTCDate(),t,2)}function t3(e,t){return tp(e.getUTCHours(),t,2)}function t5(e,t){return tp(e.getUTCHours()%12||12,t,2)}function t8(e,t){return tp(1+e$.count(te(e),e),t,3)}function t6(e,t){return tp(e.getUTCMilliseconds(),t,3)}function t9(e,t){return t6(e,t)+"000"}function t7(e,t){return tp(e.getUTCMonth()+1,t,2)}function re(e,t){return tp(e.getUTCMinutes(),t,2)}function rt(e,t){return tp(e.getUTCSeconds(),t,2)}function rr(e){var t=e.getUTCDay();return 0===t?7:t}function rn(e,t){return tp(e0.count(te(e)-1,e),t,2)}function ri(e){var t=e.getUTCDay();return t>=4||0===t?e3(e):e3.ceil(e)}function ra(e,t){return e=ri(e),tp(e3.count(te(e),e)+(4===te(e).getUTCDay()),t,2)}function ro(e){return e.getUTCDay()}function rl(e,t){return tp(e1.count(te(e)-1,e),t,2)}function ru(e,t){return tp(e.getUTCFullYear()%100,t,2)}function rc(e,t){return tp((e=ri(e)).getUTCFullYear()%100,t,2)}function rs(e,t){return tp(e.getUTCFullYear()%1e4,t,4)}function rf(e,t){var r=e.getUTCDay();return tp((e=r>=4||0===r?e3(e):e3.ceil(e)).getUTCFullYear()%1e4,t,4)}function rh(){return"+0000"}function rp(){return"%"}function rd(e){return+e}function ry(e){return Math.floor(e/1e3)}function rv(e){return new Date(e)}function rg(e){return e instanceof Date?+e:+new Date(+e)}function rm(e,t,r,n,i,a,o,l,u,c){var s=X(),f=s.invert,h=s.domain,p=c(".%L"),d=c(":%S"),y=c("%I:%M"),v=c("%I %p"),g=c("%a %d"),m=c("%b %d"),b=c("%B"),w=c("%Y");function x(e){return(u(e)<e?p:l(e)<e?d:o(e)<e?y:a(e)<e?v:n(e)<e?i(e)<e?g:m:r(e)<e?b:w)(e)}return s.invert=function(e){return new Date(f(e))},s.domain=function(e){return arguments.length?h(Array.from(e,rg)):h().map(rv)},s.ticks=function(t){var r=h();return e(r[0],r[r.length-1],null==t?10:t)},s.tickFormat=function(e,t){return null==t?x:c(t)},s.nice=function(e){var r=h();return e&&"function"==typeof e.range||(e=t(r[0],r[r.length-1],null==e?10:e)),e?h(ef(r,e)):s},s.copy=function(){return Y(s,rm(e,t,r,n,i,a,o,l,u,c))},s}function rb(){return d.apply(rm(ti,ta,e7,e6,eZ,eK,eU,eR,eL,u).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rw(){return d.apply(rm(tr,tn,te,e9,e0,e$,eF,eB,eL,c).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rx(){var e,t,r,n,i,a=0,o=1,l=Z,u=!1;function c(t){return null==t||isNaN(t*=1)?i:l(0===r?.5:(t=(n(t)-e)*r,u?Math.max(0,Math.min(1,t)):t))}function s(e){return function(t){var r,n;return arguments.length?([r,n]=t,l=e(r,n),c):[l(0),l(1)]}}return c.domain=function(i){return arguments.length?([a,o]=i,e=n(a*=1),t=n(o*=1),r=e===t?0:1/(t-e),c):[a,o]},c.clamp=function(e){return arguments.length?(u=!!e,c):u},c.interpolator=function(e){return arguments.length?(l=e,c):l},c.range=s(K),c.rangeRound=s($),c.unknown=function(e){return arguments.length?(i=e,c):i},function(i){return n=i,e=i(a),t=i(o),r=e===t?0:1/(t-e),c}}function rO(e,t){return t.domain(e.domain()).interpolator(e.interpolator()).clamp(e.clamp()).unknown(e.unknown())}function rj(){var e=eA(rx());return e.copy=function(){return rO(e,rj()).exponent(e.exponent())},y.apply(e,arguments)}function rP(){return rj.apply(null,arguments).exponent(.5)}function rA(){var e,t,r,n,i,a,o,l=0,u=.5,c=1,s=1,f=Z,h=!1;function p(e){return isNaN(e*=1)?o:(e=.5+((e=+a(e))-t)*(s*e<s*t?n:i),f(h?Math.max(0,Math.min(1,e)):e))}function d(e){return function(t){var r,n,i;return arguments.length?([r,n,i]=t,f=function(e,t){void 0===t&&(t=e,e=K);for(var r=0,n=t.length-1,i=t[0],a=Array(n<0?0:n);r<n;)a[r]=e(i,i=t[++r]);return function(e){var t=Math.max(0,Math.min(n-1,Math.floor(e*=n)));return a[t](e-t)}}(e,[r,n,i]),p):[f(0),f(.5),f(1)]}}return p.domain=function(o){return arguments.length?([l,u,c]=o,e=a(l*=1),t=a(u*=1),r=a(c*=1),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p):[l,u,c]},p.clamp=function(e){return arguments.length?(h=!!e,p):h},p.interpolator=function(e){return arguments.length?(f=e,p):f},p.range=d(K),p.rangeRound=d($),p.unknown=function(e){return arguments.length?(o=e,p):o},function(o){return a=o,e=o(l),t=o(u),r=o(c),n=e===t?0:.5/(t-e),i=t===r?0:.5/(r-t),s=t<e?-1:1,p}}function rE(){var e=eA(rA());return e.copy=function(){return rO(e,rE()).exponent(e.exponent())},y.apply(e,arguments)}function rM(){return rE.apply(null,arguments).exponent(.5)}u=(l=function(e){var t=e.dateTime,r=e.date,n=e.time,i=e.periods,a=e.days,o=e.shortDays,l=e.months,u=e.shortMonths,c=ty(i),s=tv(i),f=ty(a),h=tv(a),p=ty(o),d=tv(o),y=ty(l),v=tv(l),g=ty(u),m=tv(u),b={a:function(e){return o[e.getDay()]},A:function(e){return a[e.getDay()]},b:function(e){return u[e.getMonth()]},B:function(e){return l[e.getMonth()]},c:null,d:tL,e:tL,f:tK,g:tQ,G:t1,H:tR,I:tB,j:tU,L:tF,m:t$,M:tG,p:function(e){return i[+(e.getHours()>=12)]},q:function(e){return 1+~~(e.getMonth()/3)},Q:rd,s:ry,S:tH,u:tZ,U:tW,V:tV,w:tY,W:tJ,x:null,X:null,y:tX,Y:t0,Z:t2,"%":rp},w={a:function(e){return o[e.getUTCDay()]},A:function(e){return a[e.getUTCDay()]},b:function(e){return u[e.getUTCMonth()]},B:function(e){return l[e.getUTCMonth()]},c:null,d:t4,e:t4,f:t9,g:rc,G:rf,H:t3,I:t5,j:t8,L:t6,m:t7,M:re,p:function(e){return i[+(e.getUTCHours()>=12)]},q:function(e){return 1+~~(e.getUTCMonth()/3)},Q:rd,s:ry,S:rt,u:rr,U:rn,V:ra,w:ro,W:rl,x:null,X:null,y:ru,Y:rs,Z:rh,"%":rp},x={a:function(e,t,r){var n=p.exec(t.slice(r));return n?(e.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(e,t,r){var n=f.exec(t.slice(r));return n?(e.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(e,t,r){var n=g.exec(t.slice(r));return n?(e.m=m.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(e,t,r){var n=y.exec(t.slice(r));return n?(e.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(e,r,n){return P(e,t,r,n)},d:tM,e:tM,f:tD,g:tj,G:tO,H:t_,I:t_,j:tS,L:tC,m:tE,M:tk,p:function(e,t,r){var n=c.exec(t.slice(r));return n?(e.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:tA,Q:tI,s:tz,S:tT,u:tm,U:tb,V:tw,w:tg,W:tx,x:function(e,t,n){return P(e,r,t,n)},X:function(e,t,r){return P(e,n,t,r)},y:tj,Y:tO,Z:tP,"%":tN};function O(e,t){return function(r){var n,i,a,o=[],l=-1,u=0,c=e.length;for(r instanceof Date||(r=new Date(+r));++l<c;)37===e.charCodeAt(l)&&(o.push(e.slice(u,l)),null!=(i=tc[n=e.charAt(++l)])?n=e.charAt(++l):i="e"===n?" ":"0",(a=t[n])&&(n=a(r,i)),o.push(n),u=l+1);return o.push(e.slice(u,l)),o.join("")}}function j(e,t){return function(r){var n,i,a=tu(1900,void 0,1);if(P(a,e,r+="",0)!=r.length)return null;if("Q"in a)return new Date(a.Q);if("s"in a)return new Date(1e3*a.s+("L"in a?a.L:0));if(!t||"Z"in a||(a.Z=0),"p"in a&&(a.H=a.H%12+12*a.p),void 0===a.m&&(a.m="q"in a?a.q:0),"V"in a){if(a.V<1||a.V>53)return null;"w"in a||(a.w=1),"Z"in a?(n=(i=(n=tl(tu(a.y,0,1))).getUTCDay())>4||0===i?e1.ceil(n):e1(n),n=e$.offset(n,(a.V-1)*7),a.y=n.getUTCFullYear(),a.m=n.getUTCMonth(),a.d=n.getUTCDate()+(a.w+6)%7):(n=(i=(n=to(tu(a.y,0,1))).getDay())>4||0===i?eW.ceil(n):eW(n),n=eK.offset(n,(a.V-1)*7),a.y=n.getFullYear(),a.m=n.getMonth(),a.d=n.getDate()+(a.w+6)%7)}else("W"in a||"U"in a)&&("w"in a||(a.w="u"in a?a.u%7:+("W"in a)),i="Z"in a?tl(tu(a.y,0,1)).getUTCDay():to(tu(a.y,0,1)).getDay(),a.m=0,a.d="W"in a?(a.w+6)%7+7*a.W-(i+5)%7:a.w+7*a.U-(i+6)%7);return"Z"in a?(a.H+=a.Z/100|0,a.M+=a.Z%100,tl(a)):to(a)}}function P(e,t,r,n){for(var i,a,o=0,l=t.length,u=r.length;o<l;){if(n>=u)return -1;if(37===(i=t.charCodeAt(o++))){if(!(a=x[(i=t.charAt(o++))in tc?t.charAt(o++):i])||(n=a(e,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return b.x=O(r,b),b.X=O(n,b),b.c=O(t,b),w.x=O(r,w),w.X=O(n,w),w.c=O(t,w),{format:function(e){var t=O(e+="",b);return t.toString=function(){return e},t},parse:function(e){var t=j(e+="",!1);return t.toString=function(){return e},t},utcFormat:function(e){var t=O(e+="",w);return t.toString=function(){return e},t},utcParse:function(e){var t=j(e+="",!0);return t.toString=function(){return e},t}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,l.parse,c=l.utcFormat,l.utcParse;var rS=r(21584),r_=r(65084),rk=r(92602),rT=r(29427),rC=r(38034);function rD(e){if(Array.isArray(e)&&2===e.length){var[t,r]=e;if((0,rC.H)(t)&&(0,rC.H)(r))return!0}return!1}function rN(e,t,r){return r?e:[Math.min(e[0],t[0]),Math.max(e[1],t[1])]}var rI=r(50985),rz=r.n(rI),rL=e=>e,rR={},rB=e=>e===rR,rU=e=>function t(){return 0==arguments.length||1==arguments.length&&rB(arguments.length<=0?void 0:arguments[0])?t:e(...arguments)},rF=(e,t)=>1===e?t:rU(function(){for(var r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var a=n.filter(e=>e!==rR).length;return a>=e?t(...n):rF(e-a,rU(function(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];return t(...n.map(e=>rB(e)?r.shift():e),...r)}))}),rK=e=>rF(e.length,e),r$=(e,t)=>{for(var r=[],n=e;n<t;++n)r[n-e]=n;return r},rG=rK((e,t)=>Array.isArray(t)?t.map(e):Object.keys(t).map(e=>t[e]).map(e)),rH=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];if(!t.length)return rL;var n=t.reverse(),i=n[0],a=n.slice(1);return function(){return a.reduce((e,t)=>t(e),i(...arguments))}},rZ=e=>Array.isArray(e)?e.reverse():e.split("").reverse().join(""),rW=e=>{var t=null,r=null;return function(){for(var n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return t&&i.every((e,r)=>{var n;return e===(null==(n=t)?void 0:n[r])})?r:(t=i,r=e(...i))}};function rq(e){var t;return 0===e?1:Math.floor(new(rz())(e).abs().log(10).toNumber())+1}function rV(e,t,r){for(var n=new(rz())(e),i=0,a=[];n.lt(t)&&i<1e5;)a.push(n.toNumber()),n=n.add(r),i++;return a}rK((e,t,r)=>{var n=+e;return n+r*(t-n)}),rK((e,t,r)=>{var n=t-e;return(r-e)/(n=n||1/0)}),rK((e,t,r)=>{var n=t-e;return Math.max(0,Math.min(1,(r-e)/(n=n||1/0)))});var rY=e=>{var[t,r]=e,[n,i]=[t,r];return t>r&&([n,i]=[r,t]),[n,i]},rJ=(e,t,r)=>{if(e.lte(0))return new(rz())(0);var n=rq(e.toNumber()),i=new(rz())(10).pow(n),a=e.div(i),o=1!==n?.05:.1,l=new(rz())(Math.ceil(a.div(o).toNumber())).add(r).mul(o).mul(i);return new(rz())(t?l.toNumber():Math.ceil(l.toNumber()))},rX=(e,t,r)=>{var n=new(rz())(1),i=new(rz())(e);if(!i.isint()&&r){var a=Math.abs(e);a<1?(n=new(rz())(10).pow(rq(e)-1),i=new(rz())(Math.floor(i.div(n).toNumber())).mul(n)):a>1&&(i=new(rz())(Math.floor(e)))}else 0===e?i=new(rz())(Math.floor((t-1)/2)):r||(i=new(rz())(Math.floor(e)));var o=Math.floor((t-1)/2);return rH(rG(e=>i.add(new(rz())(e-o).mul(n)).toNumber()),r$)(0,t)},rQ=function(e,t,r,n){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((t-e)/(r-1)))return{step:new(rz())(0),tickMin:new(rz())(0),tickMax:new(rz())(0)};var o=rJ(new(rz())(t).sub(e).div(r-1),n,a),l=Math.ceil((i=e<=0&&t>=0?new(rz())(0):(i=new(rz())(e).add(t).div(2)).sub(new(rz())(i).mod(o))).sub(e).div(o).toNumber()),u=Math.ceil(new(rz())(t).sub(i).div(o).toNumber()),c=l+u+1;return c>r?rQ(e,t,r,n,a+1):(c<r&&(u=t>0?u+(r-c):u,l=t>0?l:l+(r-c)),{step:o,tickMin:i.sub(new(rz())(l).mul(o)),tickMax:i.add(new(rz())(u).mul(o))})},r0=rW(function(e){var[t,r]=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(n,2),[o,l]=rY([t,r]);if(o===-1/0||l===1/0){var u=l===1/0?[o,...r$(0,n-1).map(()=>1/0)]:[...r$(0,n-1).map(()=>-1/0),l];return t>r?rZ(u):u}if(o===l)return rX(o,n,i);var{step:c,tickMin:s,tickMax:f}=rQ(o,l,a,i,0),h=rV(s,f.add(new(rz())(.1).mul(c)),c);return t>r?rZ(h):h}),r1=rW(function(e,t){var[r,n]=e,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],[a,o]=rY([r,n]);if(a===-1/0||o===1/0)return[r,n];if(a===o)return[a];var l=Math.max(t,2),u=rJ(new(rz())(o).sub(a).div(l-1),i,0),c=[...rV(new(rz())(a),new(rz())(o),u),o];return!1===i&&(c=c.map(e=>Math.round(e))),r>n?rZ(c):c}),r2=r(23487),r4=r(60941),r3=r(90355),r5=r(36714),r8=r(4788),r6=r(78212),r9=r(83364),r7=r(94953),ne=r(42844),nt=r(49651);function nr(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function nn(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?nr(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):nr(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ni=[0,"auto"],na={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:void 0,height:30,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"bottom",padding:{left:0,right:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"category",unit:void 0},no=(e,t)=>{var r=e.cartesianAxis.xAxis[t];return null==r?na:r},nl={allowDataOverflow:!1,allowDecimals:!0,allowDuplicatedCategory:!0,angle:0,dataKey:void 0,domain:ni,hide:!0,id:0,includeHidden:!1,interval:"preserveEnd",minTickGap:5,mirror:!1,name:void 0,orientation:"left",padding:{top:0,bottom:0},reversed:!1,scale:"auto",tick:!0,tickCount:5,tickFormatter:void 0,ticks:void 0,type:"number",unit:void 0,width:nt.tQ},nu=(e,t)=>{var r=e.cartesianAxis.yAxis[t];return null==r?nl:r},nc={domain:[0,"auto"],includeHidden:!1,reversed:!1,allowDataOverflow:!1,allowDuplicatedCategory:!1,dataKey:void 0,id:0,name:"",range:[64,64],scale:"auto",type:"number",unit:""},ns=(e,t)=>{var r=e.cartesianAxis.zAxis[t];return null==r?nc:r},nf=(e,t,r)=>{switch(t){case"xAxis":return no(e,r);case"yAxis":return nu(e,r);case"zAxis":return ns(e,r);case"angleAxis":return(0,r6.Be)(e,r);case"radiusAxis":return(0,r6.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},nh=(e,t,r)=>{switch(t){case"xAxis":return no(e,r);case"yAxis":return nu(e,r);case"angleAxis":return(0,r6.Be)(e,r);case"radiusAxis":return(0,r6.Gl)(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},np=e=>e.graphicalItems.countOfBars>0;function nd(e,t){return r=>{switch(e){case"xAxis":return"xAxisId"in r&&r.xAxisId===t;case"yAxis":return"yAxisId"in r&&r.yAxisId===t;case"zAxis":return"zAxisId"in r&&r.zAxisId===t;case"angleAxis":return"angleAxisId"in r&&r.angleAxisId===t;case"radiusAxis":return"radiusAxisId"in r&&r.radiusAxisId===t;default:return!1}}}var ny=e=>e.graphicalItems.cartesianItems,nv=(0,f.Mz)([r9.N,r7.E],nd),ng=(e,t,r)=>e.filter(r).filter(e=>(null==t?void 0:t.includeHidden)===!0||!e.hide),nm=(0,f.Mz)([ny,nf,nv],ng),nb=e=>e.filter(e=>void 0===e.stackId),nw=(0,f.Mz)([nm],nb),nx=e=>e.map(e=>e.data).filter(Boolean).flat(1),nO=(0,f.Mz)([nm],nx),nj=(e,t)=>{var{chartData:r=[],dataStartIndex:n,dataEndIndex:i}=t;return e.length>0?e:r.slice(n,i+1)},nP=(0,f.Mz)([nO,rk.HS],nj),nA=(e,t,r)=>(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,r_.kr)(e,t.dataKey)})):r.length>0?r.map(e=>e.dataKey).flatMap(t=>e.map(e=>({value:(0,r_.kr)(e,t)}))):e.map(e=>({value:e})),nE=(0,f.Mz)([nP,nf,nm],nA);function nM(e,t){switch(e){case"xAxis":return"x"===t.direction;case"yAxis":return"y"===t.direction;default:return!1}}function nS(e){return e.filter(e=>(0,rT.vh)(e)||e instanceof Date).map(Number).filter(e=>!1===(0,rT.M8)(e))}var n_=(e,t,r)=>Object.fromEntries(Object.entries(t.reduce((e,t)=>(null==t.stackId||(null==e[t.stackId]&&(e[t.stackId]=[]),e[t.stackId].push(t)),e),{})).map(t=>{var[n,i]=t,a=i.map(e=>e.dataKey);return[n,{stackedData:(0,r_.yy)(e,a,r),graphicalItems:i}]})),nk=(0,f.Mz)([nP,nm,r8.eC],n_),nT=(e,t,r)=>{var{dataStartIndex:n,dataEndIndex:i}=t;if("zAxis"!==r){var a=(0,r_.Mk)(e,n,i);if(null==a||0!==a[0]||0!==a[1])return a}},nC=(0,f.Mz)([nk,rk.LF,r9.N],nT),nD=(e,t,r,n)=>r.length>0?e.flatMap(e=>r.flatMap(r=>{var i,a,o=null==(i=r.errorBars)?void 0:i.filter(e=>nM(n,e)),l=(0,r_.kr)(e,null!=(a=t.dataKey)?a:r.dataKey);return{value:l,errorDomain:function(e,t,r){return!r||"number"!=typeof t||(0,rT.M8)(t)||!r.length?[]:nS(r.flatMap(r=>{var n,i,a=(0,r_.kr)(e,r.dataKey);if(Array.isArray(a)?[n,i]=a:n=i=a,(0,rC.H)(n)&&(0,rC.H)(i))return[t-n,t+i]}))}(e,l,o)}})).filter(Boolean):(null==t?void 0:t.dataKey)!=null?e.map(e=>({value:(0,r_.kr)(e,t.dataKey),errorDomain:[]})):e.map(e=>({value:e,errorDomain:[]})),nN=(0,f.Mz)(nP,nf,nw,r9.N,nD);function nI(e){var{value:t}=e;if((0,rT.vh)(t)||t instanceof Date)return t}var nz=e=>{var t=nS(e.flatMap(e=>[e.value,e.errorDomain]).flat(1));if(0!==t.length)return[Math.min(...t),Math.max(...t)]},nL=(e,t,r)=>{var n=e.map(nI).filter(e=>null!=e);return r&&(null==t.dataKey||t.allowDuplicatedCategory&&(0,rT.CG)(n))?p()(0,e.length):t.allowDuplicatedCategory?n:Array.from(new Set(n))},nR=e=>{var t;if(null==e||!("domain"in e))return ni;if(null!=e.domain)return e.domain;if(null!=e.ticks){if("number"===e.type){var r=nS(e.ticks);return[Math.min(...r),Math.max(...r)]}if("category"===e.type)return e.ticks.map(String)}return null!=(t=null==e?void 0:e.domain)?t:ni},nB=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=t.filter(Boolean);if(0!==n.length){var i=n.flat();return[Math.min(...i),Math.max(...i)]}},nU=e=>e.referenceElements.dots,nF=(e,t,r)=>e.filter(e=>"extendDomain"===e.ifOverflow).filter(e=>"xAxis"===t?e.xAxisId===r:e.yAxisId===r),nK=(0,f.Mz)([nU,r9.N,r7.E],nF),n$=e=>e.referenceElements.areas,nG=(0,f.Mz)([n$,r9.N,r7.E],nF),nH=e=>e.referenceElements.lines,nZ=(0,f.Mz)([nH,r9.N,r7.E],nF),nW=(e,t)=>{var r=nS(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},nq=(0,f.Mz)(nK,r9.N,nW),nV=(e,t)=>{var r=nS(e.flatMap(e=>["xAxis"===t?e.x1:e.y1,"xAxis"===t?e.x2:e.y2]));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},nY=(0,f.Mz)([nG,r9.N],nV),nJ=(e,t)=>{var r=nS(e.map(e=>"xAxis"===t?e.x:e.y));if(0!==r.length)return[Math.min(...r),Math.max(...r)]},nX=(0,f.Mz)(nZ,r9.N,nJ),nQ=(0,f.Mz)(nq,nX,nY,(e,t,r)=>nB(e,r,t)),n0=(0,f.Mz)([nf],nR),n1=(e,t,r,n,i)=>{var a=function(e,t){if(t&&"function"!=typeof e&&Array.isArray(e)&&2===e.length){var r,n,[i,a]=e;if((0,rC.H)(i))r=i;else if("function"==typeof i)return;if((0,rC.H)(a))n=a;else if("function"==typeof a)return;var o=[r,n];if(rD(o))return o}}(t,e.allowDataOverflow);return null!=a?a:function(e,t,r){if(r||null!=t){if("function"==typeof e&&null!=t)try{var n=e(t,r);if(rD(n))return rN(n,t,r)}catch(e){}if(Array.isArray(e)&&2===e.length){var i,a,[o,l]=e;if("auto"===o)null!=t&&(i=Math.min(...t));else if((0,rT.Et)(o))i=o;else if("function"==typeof o)try{null!=t&&(i=o(null==t?void 0:t[0]))}catch(e){}else if("string"==typeof o&&r_.IH.test(o)){var u=r_.IH.exec(o);if(null==u||null==t)i=void 0;else{var c=+u[1];i=t[0]-c}}else i=null==t?void 0:t[0];if("auto"===l)null!=t&&(a=Math.max(...t));else if((0,rT.Et)(l))a=l;else if("function"==typeof l)try{null!=t&&(a=l(null==t?void 0:t[1]))}catch(e){}else if("string"==typeof l&&r_.qx.test(l)){var s=r_.qx.exec(l);if(null==s||null==t)a=void 0;else{var f=+s[1];a=t[1]+f}}else a=null==t?void 0:t[1];var h=[i,a];if(rD(h))return null==t?h:rN(h,t,r)}}}(t,nB(r,i,nz(n)),e.allowDataOverflow)},n2=(0,f.Mz)([nf,n0,nC,nN,nQ],n1),n4=[0,1],n3=(e,t,r,n,i,a,o)=>{if(null!=e&&null!=r&&0!==r.length){var{dataKey:l,type:u}=e,c=(0,r_._L)(t,a);return c&&null==l?p()(0,r.length):"category"===u?nL(n,e,c):"expand"===i?n4:o}},n5=(0,f.Mz)([nf,rS.fz,nP,nE,r8.eC,r9.N,n2],n3),n8=(e,t,r,n,i)=>{if(null!=e){var{scale:a,type:o}=e;if("auto"===a)return"radial"===t&&"radiusAxis"===i?"band":"radial"===t&&"angleAxis"===i?"linear":"category"===o&&n&&(n.indexOf("LineChart")>=0||n.indexOf("AreaChart")>=0||n.indexOf("ComposedChart")>=0&&!r)?"point":"category"===o?"band":"linear";if("string"==typeof a){var l="scale".concat((0,rT.Zb)(a));return l in s?l:"point"}}},n6=(0,f.Mz)([nf,rS.fz,np,r8.iO,r9.N],n8);function n9(e,t,r,n){if(null!=r&&null!=n){if("function"==typeof e.scale)return e.scale.copy().domain(r).range(n);var i=function(e){if(null!=e){if(e in s)return s[e]();var t="scale".concat((0,rT.Zb)(e));if(t in s)return s[t]()}}(t);if(null!=i){var a=i.domain(r).range(n);return(0,r_.YB)(a),a}}}var n7=(e,t,r)=>{var n=nR(t);if("auto"===r||"linear"===r){if(null!=t&&t.tickCount&&Array.isArray(n)&&("auto"===n[0]||"auto"===n[1])&&rD(e))return r0(e,t.tickCount,t.allowDecimals);if(null!=t&&t.tickCount&&"number"===t.type&&rD(e))return r1(e,t.tickCount,t.allowDecimals)}},ie=(0,f.Mz)([n5,nh,n6],n7),it=(e,t,r,n)=>"angleAxis"!==n&&(null==e?void 0:e.type)==="number"&&rD(t)&&Array.isArray(r)&&r.length>0?[Math.min(t[0],r[0]),Math.max(t[1],r[r.length-1])]:t,ir=(0,f.Mz)([nf,n5,ie,r9.N],it),ii=(0,f.Mz)(nE,nf,(e,t)=>{if(t&&"number"===t.type){var r=1/0,n=Array.from(nS(e.map(e=>e.value))).sort((e,t)=>e-t);if(n.length<2)return 1/0;var i=n[n.length-1]-n[0];if(0===i)return 1/0;for(var a=0;a<n.length-1;a++)r=Math.min(r,n[a+1]-n[a]);return r/i}}),ia=(0,f.Mz)(ii,rS.fz,r8.gY,r3.HZ,(e,t,r,n)=>n,(e,t,r,n,i)=>{if(!(0,rC.H)(e))return 0;var a="vertical"===t?n.height:n.width;if("gap"===i)return e*a/2;if("no-gap"===i){var o=(0,rT.F4)(r,e*a),l=e*a/2;return l-o-(l-o)/a*o}return 0}),io=(0,f.Mz)(no,(e,t)=>{var r=no(e,t);return null==r||"string"!=typeof r.padding?0:ia(e,"xAxis",t,r.padding)},(e,t)=>{if(null==e)return{left:0,right:0};var r,n,{padding:i}=e;return"string"==typeof i?{left:t,right:t}:{left:(null!=(r=i.left)?r:0)+t,right:(null!=(n=i.right)?n:0)+t}}),il=(0,f.Mz)(nu,(e,t)=>{var r=nu(e,t);return null==r||"string"!=typeof r.padding?0:ia(e,"yAxis",t,r.padding)},(e,t)=>{if(null==e)return{top:0,bottom:0};var r,n,{padding:i}=e;return"string"==typeof i?{top:t,bottom:t}:{top:(null!=(r=i.top)?r:0)+t,bottom:(null!=(n=i.bottom)?n:0)+t}}),iu=(0,f.Mz)([r3.HZ,io,r5.U,r5.C,(e,t,r)=>r],(e,t,r,n,i)=>{var{padding:a}=n;return i?[a.left,r.width-a.right]:[e.left+t.left,e.left+e.width-t.right]}),ic=(0,f.Mz)([r3.HZ,rS.fz,il,r5.U,r5.C,(e,t,r)=>r],(e,t,r,n,i,a)=>{var{padding:o}=i;return a?[n.height-o.bottom,o.top]:"horizontal"===t?[e.top+e.height-r.bottom,e.top+r.top]:[e.top+r.top,e.top+e.height-r.bottom]}),is=(e,t,r,n)=>{var i;switch(t){case"xAxis":return iu(e,r,n);case"yAxis":return ic(e,r,n);case"zAxis":return null==(i=ns(e,r))?void 0:i.range;case"angleAxis":return(0,r6.Cv)(e);case"radiusAxis":return(0,r6.Dc)(e,r);default:return}},ih=(0,f.Mz)([nf,is],ne.I),ip=(0,f.Mz)([nf,n6,ir,ih],n9);function id(e,t){return e.id<t.id?-1:+(e.id>t.id)}(0,f.Mz)(nm,r9.N,(e,t)=>e.flatMap(e=>{var t;return null!=(t=e.errorBars)?t:[]}).filter(e=>nM(t,e)));var iy=(e,t)=>t,iv=(e,t,r)=>r,ig=(0,f.Mz)(r4.h,iy,iv,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(id)),im=(0,f.Mz)(r4.W,iy,iv,(e,t,r)=>e.filter(e=>e.orientation===t).filter(e=>e.mirror===r).sort(id)),ib=(e,t)=>({width:e.width,height:t.height}),iw=(e,t)=>({width:"number"==typeof t.width?t.width:nt.tQ,height:e.height}),ix=(0,f.Mz)(r3.HZ,no,ib),iO=(e,t,r)=>{switch(t){case"top":return e.top;case"bottom":return r-e.bottom;default:return 0}},ij=(e,t,r)=>{switch(t){case"left":return e.left;case"right":return r-e.right;default:return 0}},iP=(0,f.Mz)(r2.A$,r3.HZ,ig,iy,iv,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=ib(t,r);null==a&&(a=iO(t,n,e));var u="top"===n&&!i||"bottom"===n&&i;o[r.id]=a-Number(u)*l.height,a+=(u?-1:1)*l.height}),o}),iA=(0,f.Mz)(r2.Lp,r3.HZ,im,iy,iv,(e,t,r,n,i)=>{var a,o={};return r.forEach(r=>{var l=iw(t,r);null==a&&(a=ij(t,n,e));var u="left"===n&&!i||"right"===n&&i;o[r.id]=a-Number(u)*l.width,a+=(u?-1:1)*l.width}),o}),iE=(e,t)=>{var r=(0,r3.HZ)(e),n=no(e,t);if(null!=n){var i=iP(e,n.orientation,n.mirror)[t];return null==i?{x:r.left,y:0}:{x:r.left,y:i}}},iM=(e,t)=>{var r=(0,r3.HZ)(e),n=nu(e,t);if(null!=n){var i=iA(e,n.orientation,n.mirror)[t];return null==i?{x:0,y:r.top}:{x:i,y:r.top}}},iS=(0,f.Mz)(r3.HZ,nu,(e,t)=>({width:"number"==typeof t.width?t.width:nt.tQ,height:e.height})),i_=(e,t,r)=>{switch(t){case"xAxis":return ix(e,r).width;case"yAxis":return iS(e,r).height;default:return}},ik=(e,t,r,n)=>{if(null!=r){var{allowDuplicatedCategory:i,type:a,dataKey:o}=r,l=(0,r_._L)(e,n),u=t.map(e=>e.value);if(o&&l&&"category"===a&&i&&(0,rT.CG)(u))return u}},iT=(0,f.Mz)([rS.fz,nE,nf,r9.N],ik),iC=(e,t,r,n)=>{if(null!=r&&null!=r.dataKey){var{type:i,scale:a}=r;if((0,r_._L)(e,n)&&("number"===i||"auto"!==a))return t.map(e=>e.value)}},iD=(0,f.Mz)([rS.fz,nE,nh,r9.N],iC),iN=(0,f.Mz)([rS.fz,(e,t,r)=>{switch(t){case"xAxis":return no(e,r);case"yAxis":return nu(e,r);default:throw Error("Unexpected axis type: ".concat(t))}},n6,ip,iT,iD,is,ie,r9.N],(e,t,r,n,i,a,o,l,u)=>{if(null==t)return null;var c=(0,r_._L)(e,u);return{angle:t.angle,interval:t.interval,minTickGap:t.minTickGap,orientation:t.orientation,tick:t.tick,tickCount:t.tickCount,tickFormatter:t.tickFormatter,ticks:t.ticks,type:t.type,unit:t.unit,axisType:u,categoricalDomain:a,duplicateDomain:i,isCategorical:c,niceTicks:l,range:o,realScaleType:r,scale:n}}),iI=(0,f.Mz)([rS.fz,nh,n6,ip,ie,is,iT,iD,r9.N],(e,t,r,n,i,a,o,l,u)=>{if(null!=t&&null!=n){var c=(0,r_._L)(e,u),{type:s,ticks:f,tickCount:h}=t,p="scaleBand"===r&&"function"==typeof n.bandwidth?n.bandwidth()/2:2,d="category"===s&&n.bandwidth?n.bandwidth()/p:0;d="angleAxis"===u&&null!=a&&a.length>=2?2*(0,rT.sA)(a[0]-a[1])*d:d;var y=f||i;return y?y.map((e,t)=>({index:t,coordinate:n(o?o.indexOf(e):e)+d,value:e,offset:d})).filter(e=>!(0,rT.M8)(e.coordinate)):c&&l?l.map((e,t)=>({coordinate:n(e)+d,value:e,index:t,offset:d})):n.ticks?n.ticks(h).map(e=>({coordinate:n(e)+d,value:e,offset:d})):n.domain().map((e,t)=>({coordinate:n(e)+d,value:o?o[e]:e,index:t,offset:d}))}}),iz=(0,f.Mz)([rS.fz,nh,ip,is,iT,iD,r9.N],(e,t,r,n,i,a,o)=>{if(null!=t&&null!=r&&null!=n&&n[0]!==n[1]){var l=(0,r_._L)(e,o),{tickCount:u}=t,c=0;return(c="angleAxis"===o&&(null==n?void 0:n.length)>=2?2*(0,rT.sA)(n[0]-n[1])*c:c,l&&a)?a.map((e,t)=>({coordinate:r(e)+c,value:e,index:t,offset:c})):r.ticks?r.ticks(u).map(e=>({coordinate:r(e)+c,value:e,offset:c})):r.domain().map((e,t)=>({coordinate:r(e)+c,value:i?i[e]:e,index:t,offset:c}))}}),iL=(0,f.Mz)(nf,ip,(e,t)=>{if(null!=e&&null!=t)return nn(nn({},e),{},{scale:t})}),iR=(0,f.Mz)([nf,n6,n5,ih],n9);(0,f.Mz)((e,t,r)=>ns(e,r),iR,(e,t)=>{if(null!=e&&null!=t)return nn(nn({},e),{},{scale:t})});var iB=(0,f.Mz)([rS.fz,r4.h,r4.W],(e,t,r)=>{switch(e){case"horizontal":return t.some(e=>e.reversed)?"right-to-left":"left-to-right";case"vertical":return r.some(e=>e.reversed)?"bottom-to-top":"top-to-bottom";case"centric":case"radial":return"left-to-right";default:return}})},96231:(e,t,r)=>{"use strict";r.d(t,{mZ:()=>l,vE:()=>o});var n=r(68646),i={accessibilityLayer:!0,barCategoryGap:"10%",barGap:4,barSize:void 0,className:void 0,maxBarSize:void 0,stackOffset:"none",syncId:void 0,syncMethod:"index"},a=(0,n.Z0)({name:"rootProps",initialState:i,reducers:{updateOptions:(e,t)=>{var r;e.accessibilityLayer=t.payload.accessibilityLayer,e.barCategoryGap=t.payload.barCategoryGap,e.barGap=null!=(r=t.payload.barGap)?r:i.barGap,e.barSize=t.payload.barSize,e.maxBarSize=t.payload.maxBarSize,e.stackOffset=t.payload.stackOffset,e.syncId=t.payload.syncId,e.syncMethod=t.payload.syncMethod,e.className=t.payload.className}}}),o=a.reducer,{updateOptions:l}=a.actions},96546:(e,t,r)=>{"use strict";r.d(t,{o:()=>n});var n=(e,t,r,n,i,a,o,l)=>{if(null!=a&&null!=l){var u=o[0],c=null==u?void 0:l(u.positions,a);if(null!=c)return c;var s=null==i?void 0:i[Number(a)];if(s)if("horizontal"===r)return{x:s.coordinate,y:(n.top+t)/2};else return{x:(n.left+e)/2,y:s.coordinate}}}},97214:(e,t,r)=>{"use strict";r.d(t,{TK:()=>l});var n=r(94285),i=r(68645),a=r(4517),o=r(8569),l=e=>{var{chartData:t}=e,r=(0,a.j)(),l=(0,o.r)();return(0,n.useEffect)(()=>l?()=>{}:(r((0,i.hq)(t)),()=>{r((0,i.hq)(void 0))}),[t,r,l]),null}},97888:(e,t)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),t.isPrimitive=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e}},98612:(e,t,r)=>{"use strict";Object.defineProperty(t,Symbol.toStringTag,{value:"Module"});let n=r(59999),i=r(84878),a=r(20539),o=r(70307);t.get=function e(t,r,l){if(null==t)return l;switch(typeof r){case"string":{if(n.isUnsafeProperty(r))return l;let a=t[r];if(void 0===a)if(i.isDeepKey(r))return e(t,o.toPath(r),l);else return l;return a}case"number":case"symbol":{"number"==typeof r&&(r=a.toKey(r));let e=t[r];if(void 0===e)return l;return e}default:{if(Array.isArray(r)){var u=t,c=r,s=l;if(0===c.length)return s;let e=u;for(let t=0;t<c.length;t++){if(null==e||n.isUnsafeProperty(c[t]))return s;e=e[c[t]]}return void 0===e?s:e}if(r=Object.is(r?.valueOf(),-0)?"-0":String(r),n.isUnsafeProperty(r))return l;let e=t[r];if(void 0===e)return l;return e}}}},99442:(e,t,r)=>{"use strict";r.d(t,{f:()=>d});var n=r(29427),i=r(94263),a=r(3157);function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function l(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}class u{static create(e){return new u(e)}get domain(){return this.scale.domain}get range(){return this.scale.range}get rangeMin(){return this.range()[0]}get rangeMax(){return this.range()[1]}get bandwidth(){return this.scale.bandwidth}apply(e){var{bandAware:t,position:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0!==e){if(r)switch(r){case"start":default:return this.scale(e);case"middle":var n=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+n;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(e)+i}if(t){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(e)+a}return this.scale(e)}}isInRange(e){var t=this.range(),r=t[0],n=t[t.length-1];return r<=n?e>=r&&e<=n:e>=n&&e<=r}constructor(e){this.scale=e}}l(u,"EPS",1e-4);var c=function(e){var{width:t,height:r}=e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,a=Math.atan(r/t);return Math.abs(i>a&&i<Math.PI-a?r/Math.sin(i):t/Math.cos(i))};function s(e,t,r){if(t<1)return[];if(1===t&&void 0===r)return e;for(var n=[],i=0;i<e.length;i+=t)if(void 0!==r&&!0!==r(e[i]))return;else n.push(e[i]);return n}function f(e,t,r,n,i){if(e*t<e*n||e*t>e*i)return!1;var a=r();return e*(t-e*a/2-n)>=0&&e*(t+e*a/2-i)<=0}function h(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function p(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?h(Object(r),!0).forEach(function(t){var n,i,a;n=e,i=t,a=r[t],(i=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(i))in n?Object.defineProperty(n,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):n[i]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function d(e,t,r){var o,{tick:l,ticks:u,viewBox:h,minTickGap:d,orientation:y,interval:v,tickFormatter:g,unit:m,angle:b}=e;if(!u||!u.length||!l)return[];if((0,n.Et)(v)||a.m.isSsr)return null!=(o=s(u,((0,n.Et)(v)?v:0)+1))?o:[];var w=[],x="top"===y||"bottom"===y?"width":"height",O=m&&"width"===x?(0,i.P)(m,{fontSize:t,letterSpacing:r}):{width:0,height:0},j=(e,n)=>{var a,o="function"==typeof g?g(e.value,n):e.value;return"width"===x?(a=(0,i.P)(o,{fontSize:t,letterSpacing:r}),c({width:a.width+O.width,height:a.height+O.height},b)):(0,i.P)(o,{fontSize:t,letterSpacing:r})[x]},P=u.length>=2?(0,n.sA)(u[1].coordinate-u[0].coordinate):1,A=function(e,t,r){var n="width"===r,{x:i,y:a,width:o,height:l}=e;return 1===t?{start:n?i:a,end:n?i+o:a+l}:{start:n?i+o:a+l,end:n?i:a}}(h,P,x);return"equidistantPreserveStart"===v?function(e,t,r,n,i){for(var a,o=(n||[]).slice(),{start:l,end:u}=t,c=0,h=1,p=l;h<=o.length;)if(a=function(){var t,a=null==n?void 0:n[c];if(void 0===a)return{v:s(n,h)};var o=c,d=()=>(void 0===t&&(t=r(a,o)),t),y=a.coordinate,v=0===c||f(e,y,d,p,u);v||(c=0,p=l,h+=1),v&&(p=y+e*(d()/2+i),c+=h)}())return a.v;return[]}(P,A,j,u,d):("preserveStart"===v||"preserveStartEnd"===v?function(e,t,r,n,i,a){var o=(n||[]).slice(),l=o.length,{start:u,end:c}=t;if(a){var s=n[l-1],h=r(s,l-1),d=e*(s.coordinate+e*h/2-c);o[l-1]=s=p(p({},s),{},{tickCoord:d>0?s.coordinate-d*e:s.coordinate}),f(e,s.tickCoord,()=>h,u,c)&&(c=s.tickCoord-e*(h/2+i),o[l-1]=p(p({},s),{},{isShow:!0}))}for(var y=a?l-1:l,v=function(t){var n,a=o[t],l=()=>(void 0===n&&(n=r(a,t)),n);if(0===t){var s=e*(a.coordinate-e*l()/2-u);o[t]=a=p(p({},a),{},{tickCoord:s<0?a.coordinate-s*e:a.coordinate})}else o[t]=a=p(p({},a),{},{tickCoord:a.coordinate});f(e,a.tickCoord,l,u,c)&&(u=a.tickCoord+e*(l()/2+i),o[t]=p(p({},a),{},{isShow:!0}))},g=0;g<y;g++)v(g);return o}(P,A,j,u,d,"preserveStartEnd"===v):function(e,t,r,n,i){for(var a=(n||[]).slice(),o=a.length,{start:l}=t,{end:u}=t,c=function(t){var n,c=a[t],s=()=>(void 0===n&&(n=r(c,t)),n);if(t===o-1){var h=e*(c.coordinate+e*s()/2-u);a[t]=c=p(p({},c),{},{tickCoord:h>0?c.coordinate-h*e:c.coordinate})}else a[t]=c=p(p({},c),{},{tickCoord:c.coordinate});f(e,c.tickCoord,s,l,u)&&(u=c.tickCoord-e*(s()/2+i),a[t]=p(p({},c),{},{isShow:!0}))},s=o-1;s>=0;s--)c(s);return a}(P,A,j,u,d)).filter(e=>e.isShow)}},99898:e=>{"use strict";var t=Object.prototype.hasOwnProperty,r="~";function n(){}function i(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function a(e,t,n,a,o){if("function"!=typeof n)throw TypeError("The listener must be a function");var l=new i(n,a||e,o),u=r?r+t:t;return e._events[u]?e._events[u].fn?e._events[u]=[e._events[u],l]:e._events[u].push(l):(e._events[u]=l,e._eventsCount++),e}function o(e,t){0==--e._eventsCount?e._events=new n:delete e._events[t]}function l(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),l.prototype.eventNames=function(){var e,n,i=[];if(0===this._eventsCount)return i;for(n in e=this._events)t.call(e,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(e)):i},l.prototype.listeners=function(e){var t=r?r+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,a=n.length,o=Array(a);i<a;i++)o[i]=n[i].fn;return o},l.prototype.listenerCount=function(e){var t=r?r+e:e,n=this._events[t];return n?n.fn?1:n.length:0},l.prototype.emit=function(e,t,n,i,a,o){var l=r?r+e:e;if(!this._events[l])return!1;var u,c,s=this._events[l],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(e,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,t),!0;case 3:return s.fn.call(s.context,t,n),!0;case 4:return s.fn.call(s.context,t,n,i),!0;case 5:return s.fn.call(s.context,t,n,i,a),!0;case 6:return s.fn.call(s.context,t,n,i,a,o),!0}for(c=1,u=Array(f-1);c<f;c++)u[c-1]=arguments[c];s.fn.apply(s.context,u)}else{var h,p=s.length;for(c=0;c<p;c++)switch(s[c].once&&this.removeListener(e,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,t);break;case 3:s[c].fn.call(s[c].context,t,n);break;case 4:s[c].fn.call(s[c].context,t,n,i);break;default:if(!u)for(h=1,u=Array(f-1);h<f;h++)u[h-1]=arguments[h];s[c].fn.apply(s[c].context,u)}}return!0},l.prototype.on=function(e,t,r){return a(this,e,t,r,!1)},l.prototype.once=function(e,t,r){return a(this,e,t,r,!0)},l.prototype.removeListener=function(e,t,n,i){var a=r?r+e:e;if(!this._events[a])return this;if(!t)return o(this,a),this;var l=this._events[a];if(l.fn)l.fn!==t||i&&!l.once||n&&l.context!==n||o(this,a);else{for(var u=0,c=[],s=l.length;u<s;u++)(l[u].fn!==t||i&&!l[u].once||n&&l[u].context!==n)&&c.push(l[u]);c.length?this._events[a]=1===c.length?c[0]:c:o(this,a)}return this},l.prototype.removeAllListeners=function(e){var t;return e?(t=r?r+e:e,this._events[t]&&o(this,t)):(this._events=new n,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=r,l.EventEmitter=l,e.exports=l}}]);