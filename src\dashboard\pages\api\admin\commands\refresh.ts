import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getServerSession(req, res, authOptions);
    
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const isAdmin = (session.user as any).isAdmin;
    if (!isAdmin) {
      return res.status(403).json({ error: 'Admin access required' });
    }

    // Create reload signal to trigger command refresh
    const projectRoot = process.cwd().includes('dashboard') 
      ? path.resolve(process.cwd(), '..', '..')
      : process.cwd();
    
    const signalPath = path.join(projectRoot, 'addon-reload.signal');
    
    fs.writeFileSync(signalPath, JSON.stringify({
      requestedBy: (session.user as any)?.email || 'dashboard',
      timestamp: Date.now(),
      action: 'commands-refresh',
      source: 'manual-refresh'
    }));

    res.status(200).json({
      message: 'Command refresh triggered successfully',
      timestamp: Date.now()
    });

  } catch (error) {
    console.error('Error triggering command refresh:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
} 