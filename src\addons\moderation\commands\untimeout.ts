import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('untimeout')
  .setDescription('Remove timeout from a member')
  .addUserOption(option =>
    option.setName('user')
      .setDescription('The user to remove timeout from')
      .setRequired(true))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for removing the timeout')
      .setRequired(false))
  .setDefaultMemberPermissions(PermissionFlagsBits.ModerateMembers);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const targetUser = interaction.options.getUser('user', true);
  const reason = interaction.options.getString('reason') ?? 'No reason provided';

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ModerateMembers)) {
    await interaction.reply({ content: '❌ You do not have permission to moderate members.', ephemeral: true });
    return;
  }

  const member = await interaction.guild?.members.fetch(targetUser.id).catch(() => null);
  if (!member) {
    await interaction.reply({ content: '❌ Could not find that member in this guild.', ephemeral: true });
    return;
  }

  if (!member.moderatable) {
    await interaction.reply({ content: '❌ I cannot moderate this user. They may have higher permissions or roles than me.', ephemeral: true });
    return;
  }

  if (!member.isCommunicationDisabled()) {
    await interaction.reply({ content: '❌ This user is not currently timed out.', ephemeral: true });
    return;
  }

  await member.timeout(null, reason);
  await interaction.reply({ content: `✅ Removed timeout from **${targetUser.tag}** | Reason: ${reason}` });
}

export const cooldown = 5000; 