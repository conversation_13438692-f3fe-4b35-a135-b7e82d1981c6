import { NextApiRequest, NextApiResponse } from 'next';
import { getSession } from 'next-auth/react';
import { REST } from '@discordjs/rest';
import { Routes } from 'discord-api-types/v10';
import { env } from '../../../core/config/index';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const session = await getSession({ req });
    if (!session) {
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Get guild ID from config
    const guildId = env.DISCORD_GUILD_ID;
    if (!guildId) {
      return res.status(500).json({ error: 'DISCORD_GUILD_ID not configured' });
    }

    // Initialize Discord REST client
    const rest = new REST({ version: '10' }).setToken(env.DISCORD_BOT_TOKEN);

    // Extract pagination parameters
    const { before, after, limit = '50' } = req.query;
    const queryParams = new URLSearchParams();

    // Add pagination parameters if provided
    if (before && typeof before === 'string') queryParams.append('before', before);
    if (after && typeof after === 'string') queryParams.append('after', after);
    queryParams.append('limit', String(Math.min(Number(limit) || 50, 100))); // Cap at 100

    // Fetch audit logs with pagination
    const response = await rest.get(
      `${Routes.guildAuditLog(guildId)}?${queryParams}`
    ) as any;

    // Discord returns an object with audit_log_entries array
    const entries: any[] = Array.isArray(response?.audit_log_entries)
      ? response.audit_log_entries
      : [];

    // Create a map of users for quick lookup
    const usersMap = new Map<string, any>();
    if (Array.isArray(response?.users)) {
      for (const user of response.users) {
        usersMap.set(user.id, user);
      }
    }

    // Transform audit log entries to match our frontend interface
    const formattedLogs = entries.map((entry: any) => {
      const user = usersMap.get(entry.user_id);
      return {
        id: entry.id,
        action: entry.action_type,
        executor: user ? {
          id: user.id,
          tag: `${user.username}#${user.discriminator}`,
          avatar: user.avatar ? `https://cdn.discordapp.com/avatars/${user.id}/${user.avatar}.png` : null
        } : null,
        target: entry.target_id ? {
          id: entry.target_id,
          type: entry.target_type
        } : null,
        reason: entry.reason,
        createdTimestamp: (() => {
          const snowflake = Number(entry.id);
          const discordEpoch = 1420070400000;
          const timestamp = snowflake / 4194304 + discordEpoch; // 2^22 = 4194304
          return Math.round(timestamp);
        })(),
        changes: entry.changes?.map((change: { key: string; old_value: any; new_value: any }) => ({
          key: change.key,
          old: change.old_value,
          new: change.new_value
        }))
      };
    });

    return res.status(200).json(formattedLogs);
  } catch (error: any) {
    console.error('Error fetching audit logs:', error);
    // Provide extra context in development for easier debugging
    return res.status(500).json({
      error: 'Failed to fetch audit logs',
      message: process.env.NODE_ENV === 'development' ? (error?.message ?? 'Unknown error') : undefined
    });
  }
} 