"use strict";(()=>{var e={};e.id=6040,e.ids=[6040],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5332:(e,t,o)=>{o.r(t),o.d(t,{config:()=>p,default:()=>h,routeModule:()=>y});var a={};o.r(a),o.d(a,{default:()=>g});var n=o(3433),s=o(264),r=o(584),i=o(5806),d=o(8525),l=o(8580),c=o(2518);let m=null,u=l.dashboardConfig.database?.url||"mongodb://localhost:27017",f=l.dashboardConfig.database?.name||"discord_bot";async function b(){return m||(m=await c.MongoClient.connect(u,{...l.dashboardConfig.database?.options||{}})),m.db(f)}async function g(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,i.getServerSession)(e,t,d.authOptions))return t.status(401).json({error:"Unauthorized"});let a=await b(),n=new Date,s=new Date(n.getFullYear(),n.getMonth(),n.getDate()),r=new Date(s.getTime()+864e5),l=await a.collection("command_logs").countDocuments({timestamp:{$gte:s,$lt:r}}).catch(()=>0),c=o(9021),m=o(3873),u=o(2115),f=0,g=0;try{let e=(()=>{let e=["404-bot/config.yml","config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>m.resolve(process.cwd(),e)).find(e=>c.existsSync(e));if(!e){let t=m.resolve(__dirname,"../../../../../../../config.yml");c.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");return e})(),t=c.readFileSync(e,"utf8"),o=u.parse(t),a=o.addons?.enabled!==!1,n=o.addons?.disabled??[];if(a){let e=(()=>{let e=["404-bot/src/addons","src/addons","../src/addons","../../src/addons","../../../src/addons","../../../../src/addons"].map(e=>m.resolve(process.cwd(),e)).find(e=>c.existsSync(e));if(!e){let t=m.resolve(__dirname,"../../../../../../../src/addons");c.existsSync(t)&&(e=t)}if(!e)throw Error("Addons directory not found");return e})(),t=c.readdirSync(e,{withFileTypes:!0}).filter(e=>e.isDirectory()).map(e=>e.name),o=t.filter(e=>!n.includes(e)),a=t.filter(e=>n.includes(e));f=o.length,g=a.length}}catch(e){f=0,g=0}let h=await a.collection("bot_status").findOne({key:"start_time"}).catch(()=>null),p="0d 0h 0m";if(h?.timestamp){let e=Date.now()-new Date(h.timestamp).getTime(),t=Math.floor(e/864e5),o=Math.floor(e%864e5/36e5),a=Math.floor(e%36e5/6e4);p=`${t}d ${o}h ${a}m`}else{let e=process.uptime(),t=Math.floor(e/86400),o=Math.floor(e%86400/3600),a=Math.floor(e%3600/60);p=`${t}d ${o}h ${a}m`}let y=await a.collection("command_logs").find({timestamp:{$gte:new Date(Date.now()-36e5)},responseTime:{$exists:!0}}).limit(100).toArray().catch(()=>[]),S=y.length>0?Math.round(y.reduce((e,t)=>e+(t.responseTime||0),0)/y.length):45,w=await a.collection("error_logs").countDocuments({timestamp:{$gte:s,$lt:r}}).catch(()=>0),x=new Date;x.setDate(x.getDate()-6);let I=await a.collection("command_logs").find({timestamp:{$gte:x}}).toArray().catch(()=>[]),M={Mon:{commands:0,members:new Set},Tue:{commands:0,members:new Set},Wed:{commands:0,members:new Set},Thu:{commands:0,members:new Set},Fri:{commands:0,members:new Set},Sat:{commands:0,members:new Set},Sun:{commands:0,members:new Set}},T=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"];for(let e of I){let t=T[new Date(e.timestamp).getDay()];M[t]&&(M[t].commands+=1,e.userId&&M[t].members.add(e.userId))}let P=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map(e=>({day:e,commands:M[e].commands,members:M[e].members.size})),A={commandsToday:l,uptime:p,responseTime:`${S}ms`,activeAddons:f,inactiveAddons:g,weeklyActivity:P,errorsToday:w,status:"online"};t.status(200).json({botStats:A})}catch(e){t.status(500).json({error:"Failed to fetch bot analytics",details:e instanceof Error?e.message:"Unknown error"})}}let h=(0,r.M)(a,"default"),p=(0,r.M)(a,"config"),y=new n.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/analytics/bot",pathname:"/api/analytics/bot",bundlePath:"",filename:""},userland:a})},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var a=o(5542),n=o.n(a);let s=require("next-auth/providers/discord");var r=o.n(s),i=o(8580);let d={providers:[r()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let n=!1;if(o)if((i.dashboardConfig.dashboard.admins||[]).includes(o))n=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();n=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=n,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=n()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var a=o(9021),n=o(2115),s=o.n(n),r=o(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");i=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=5332);module.exports=o})();