import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient, ObjectId } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

interface GameServer {
  type: string;
  host: string;
  port: number;
  name?: string;
  description?: string;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  let client;
  try {
    client = await connectToDatabase();
    const db = client.db(dbName);
    const collection = db.collection('gameservers');

    switch (req.method) {
      case 'GET':
        const servers = await collection.find({}).toArray();
        return res.status(200).json(servers);

      case 'POST':
        const newServer: GameServer = req.body;
        
        // Validate required fields
        if (!newServer.type || !newServer.host || !newServer.port) {
          return res.status(400).json({ error: 'Missing required fields' });
        }

        // Validate port number
        if (newServer.port < 1 || newServer.port > 65535) {
          return res.status(400).json({ error: 'Invalid port number' });
        }

        // Check for duplicate server
        const duplicate = await collection.findOne({
          host: newServer.host,
          port: newServer.port
        });

        if (duplicate) {
          return res.status(409).json({ error: 'Server already exists' });
        }

        const result = await collection.insertOne(newServer);
        return res.status(201).json({ ...newServer, _id: result.insertedId });

      case 'PUT':
        const { id, ...updateData } = req.body;

        // Basic validation
        if (!updateData.type || !updateData.host || !updateData.port) {
          return res.status(400).json({ error: 'Missing required fields' });
        }

        // Validate port number
        if (updateData.port < 1 || updateData.port > 65535) {
          return res.status(400).json({ error: 'Invalid port number' });
        }

        try {
          // Build a dynamic filter. Prefer _id if supplied; otherwise use host+port.
          let filter: any = null;

          if (id) {
            try {
              filter = { _id: new ObjectId(id) };
            } catch (err) {
              console.warn('Invalid ObjectId provided, falling back to host/port matching');
            }
          }

          if (!filter) {
            filter = { host: updateData.host, port: updateData.port };
          }

          const currentServer = await collection.findOne(filter);

          if (!currentServer) {
            return res.status(404).json({ error: 'Server not found' });
          }

          // If the host/port are being changed, ensure no duplicates (excluding the current doc)
          if (currentServer.host !== updateData.host || currentServer.port !== updateData.port) {
            const duplicateOnUpdate = await collection.findOne({
              _id: { $ne: currentServer._id },
              host: updateData.host,
              port: updateData.port
            });

            if (duplicateOnUpdate) {
              return res.status(409).json({ error: 'Another server with this host and port already exists' });
            }
          }

          // Perform the update
          const updateRes = await collection.updateOne(
            { _id: currentServer._id },
            { $set: updateData }
          );

          if (updateRes.matchedCount === 0) {
            return res.status(404).json({ error: 'Server not found after update' });
          }

          // Fetch the updated document
          const updatedDoc = await collection.findOne({ _id: currentServer._id });

          console.log('Server updated successfully:', updatedDoc);
          return res.status(200).json(updatedDoc);
        } catch (error) {
          console.error('Update error:', error);
          if (error instanceof Error && error.message.includes('ObjectId')) {
            return res.status(400).json({ error: 'Invalid server ID format' });
          }
          return res.status(500).json({ error: 'Failed to update server' });
        }

      case 'DELETE':
        let deleteBody;
        try {
          deleteBody = typeof req.body === 'string' ? JSON.parse(req.body) : req.body;
          console.log('Delete request body:', deleteBody);
        } catch (error) {
          console.error('Failed to parse request body:', error);
          return res.status(400).json({ error: 'Invalid request body format' });
        }

        let deleteQuery;
        if (deleteBody.id) {
          try {
            deleteQuery = { _id: new ObjectId(deleteBody.id) };
          } catch (error) {
            console.error('Invalid ObjectId:', error);
            return res.status(400).json({ error: 'Invalid server ID format' });
          }
        } else if (deleteBody.host && typeof deleteBody.port !== 'undefined') {
          deleteQuery = { 
            host: deleteBody.host.toString(),
            port: parseInt(deleteBody.port.toString())
          };
        } else {
          console.error('Missing server identification in request:', deleteBody);
          return res.status(400).json({ error: 'Missing server identification (id or host/port)' });
        }

        try {
          console.log('Attempting to delete server with query:', deleteQuery);
          const deleteResult = await collection.findOneAndDelete(deleteQuery);

          if (!deleteResult?.value) {
            console.log('Server not found for deletion');
            return res.status(404).json({ error: 'Server not found' });
          }

          console.log('Server deleted successfully');
          return res.status(200).json({ 
            message: 'Server deleted successfully',
            deletedServer: deleteResult.value
          });
        } catch (error) {
          console.error('MongoDB delete error:', error);
          return res.status(500).json({ error: 'Failed to delete server from database' });
        }

      default:
        res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
        return res.status(405).json({ error: `Method ${req.method} not allowed` });
    }
  } catch (error: unknown) {
    console.error('Server management error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 