"use strict";(()=>{var e={};e.id=5838,e.ids=[5838],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var r=o(5542),s=o.n(r);let n=require("next-auth/providers/discord");var i=o.n(n),a=o(8580);let d={providers:[i()({clientId:a.dashboardConfig.bot.clientId,clientSecret:a.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,r=t.accessToken||null;e.user.id=o,e.user.accessToken=r;let s=!1;if(o)if((a.dashboardConfig.dashboard.admins||[]).includes(o))s=!0;else{let e=a.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&a.dashboardConfig.bot.token&&a.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${a.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${a.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();s=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),r=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:a.dashboardConfig.dashboard.session.secret||a.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var r=o(9021),s=o(2115),n=o.n(s),i=o(3873);let a={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");a=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:a.bot.token,clientId:a.bot.clientId,clientSecret:a.bot.clientSecret,guildId:a.bot.guildId,ticketCategoryId:a.bot.ticketCategoryId||null,ticketLogChannelId:a.bot.ticketLogChannelId||null,prefix:a.bot.prefix},dashboard:{admins:a.dashboard?.admins||[],adminRoleIds:a.dashboard?.adminRoleIds||[],session:{secret:a.dashboard?.session?.secret||a.bot.clientSecret}},database:{url:a.database.url,name:a.database.name,options:{maxPoolSize:a.database.options?.maxPoolSize||10,minPoolSize:a.database.options?.minPoolSize||1,maxIdleTimeMS:a.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:a.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:a.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:a.database.options?.connectTimeoutMS||1e4,retryWrites:a.database.options?.retryWrites!==!1,retryReads:a.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")},9652:(e,t,o)=>{o.r(t),o.d(t,{config:()=>p,default:()=>b,routeModule:()=>g});var r={};o.r(r),o.d(r,{default:()=>f});var s=o(3433),n=o(264),i=o(584),a=o(5806),d=o(8525),l=o(9021),c=o.n(l),u=o(3873),m=o.n(u);async function f(e,t){if("POST"!==e.method)return t.status(405).json({error:"Method not allowed"});let o=await (0,a.getServerSession)(e,t,d.authOptions);if(!o)return t.status(401).json({error:"Unauthorized"});if(!o.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});try{let e=function(){let e=["404-bot",".","..","../..","../../..","../../../.."].map(e=>m().resolve(process.cwd(),e)).find(e=>{try{return c().existsSync(m().join(e,"addon-reload.signal"))||c().existsSync(m().join(e,"config.yml"))}catch{return!1}});if(!e){let t=m().resolve(__dirname,"../../../../../../..");c().existsSync(m().join(t,"config.yml"))&&(e=t)}if(!e)throw Error("Project root not found");return e}(),r=m().join(e,"addon-reload.signal"),s={timestamp:new Date().toISOString(),requestedBy:o.user?.email||"dashboard"};return c().writeFileSync(r,JSON.stringify(s,null,2)),t.status(200).json({success:!0,message:"Addon reload signal sent",timestamp:s.timestamp,path:r})}catch(e){return t.status(500).json({error:"Internal server error",details:e.message,stack:void 0})}}let b=(0,i.M)(r,"default"),p=(0,i.M)(r,"config"),g=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/addons/reload",pathname:"/api/admin/addons/reload",bundlePath:"",filename:""},userland:r})}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=9652);module.exports=o})();