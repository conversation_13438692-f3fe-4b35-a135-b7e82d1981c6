"use strict";(()=>{var e={};e.id=1524,e.ids=[1524],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},1858:(e,t,r)=>{r.d(t,{K:()=>l});var n=r(9021),s=r.n(n),o=r(3873),a=r.n(o),i=r(2115),d=r.n(i);function l(){try{let e=process.cwd();for(let t=0;t<5;t++){let t=a().join(e,"config.yml");if(s().existsSync(t)){let e=s().readFileSync(t,"utf8");return d().parse(e)}e=a().join(e,"..")}throw Error("Fatal: Could not find config.yml. The bot cannot start.")}catch(e){throw e}}},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},6154:(e,t,r)=>{r.r(t),r.d(t,{config:()=>S,default:()=>E,routeModule:()=>T});var n={};r.r(n),r.d(n,{config:()=>I,default:()=>v});var s=r(3433),o=r(264),a=r(584),i=r(5806),d=r(8525),l=r(1858),u=r(2518);let c=(0,l.K)(),f=null,m=c.database?.url||"mongodb://localhost:27017",b=c.database?.name||"discord_bot";async function p(){return f||(f=await u.MongoClient.connect(m,{...c.database?.options||{}})),f.db(b)}let h=(e,t)=>{let r=Math.max(1,Math.floor(e));switch(t){case"minutes":return`*/${r} * * * *`;case"hours":return`0 */${r} * * *`;case"days":return`0 9 */${r} * *`;default:throw Error("Invalid interval unit")}},g=e=>{let{eventType:t,name:r,isEnabled:n}=e;if(void 0===r||"boolean"!=typeof n||!t)return"Missing required fields: name, isEnabled, eventType";if("TIMED_MESSAGE"===t){let{channelId:t,message:r,intervalValue:n,intervalUnit:s}=e;if(!t||!r||!n||!s)return"For Timed Message, must provide: channelId, message, intervalValue, intervalUnit";if("number"!=typeof n||n<=0)return"Interval value must be a positive number.";if(!["minutes","hours","days"].includes(s))return"Invalid interval unit provided."}else{if("GUILD_EVENT"!==t)return"Invalid eventType specified";let{description:r,startTime:n,locationType:s}=e;if(!r||!n||!s)return"For Guild Event, must provide: description, startTime, locationType";if("VOICE"===s&&!e.eventChannelId)return"Voice channel is required for Voice Guild Events";if("EXTERNAL"===s&&!e.location)return"External URL is required for External Guild Events"}return null},y=(e,t)=>{let{name:r,isEnabled:n,eventType:s}=e,o={name:r,isEnabled:n,eventType:s,updatedAt:new Date};if(t&&(o.createdAt=new Date),"TIMED_MESSAGE"===s){let{channelId:t,message:r,intervalValue:n,intervalUnit:s}=e;o.channelId=t,o.message=r,o.cron=h(n,s)}else if("GUILD_EVENT"===s){let{description:t,startTime:r,endTime:n,locationType:s,eventChannelId:a,location:i}=e;o.description=t,o.startTime=r,o.endTime=n||null,o.locationType=s,"VOICE"===s?o.eventChannelId=a:o.location=i}return o};async function v(e,t){let r=await (0,i.getServerSession)(e,t,d.authOptions);if(!r?.user||!r.user.isAdmin)return t.status(401).json({error:"Unauthorized"});let n=(await p()).collection("scheduled_events"),{eventId:s}=e.query;try{switch(e.method){case"GET":{let e=await n.find({}).sort({createdAt:-1}).toArray();return t.status(200).json(e)}case"POST":{let r=g(e.body);if(r)return t.status(400).json({error:r});let s=y(e.body,!0),o=await n.insertOne(s);return t.status(201).json({...s,_id:o.insertedId})}case"PUT":{if(!s||"string"!=typeof s)return t.status(400).json({error:"Invalid event ID"});let r=g(e.body);if(r)return t.status(400).json({error:r});let o=y(e.body,!1),a=await n.updateOne({_id:new u.ObjectId(s)},{$set:o});if(0===a.matchedCount)return t.status(404).json({error:"Event not found"});return t.status(200).json({message:"Event updated successfully"})}case"DELETE":{if(!s||"string"!=typeof s)return t.status(400).json({error:"Invalid event ID"});let e=await n.deleteOne({_id:new u.ObjectId(s)});if(0===e.deletedCount)return t.status(404).json({error:"Event not found"});return t.status(200).json({message:"Event deleted successfully"})}default:return t.setHeader("Allow",["GET","POST","PUT","DELETE"]),t.status(405).end(`Method ${e.method} Not Allowed`)}}catch(e){if(e.message.includes("BSONError"))return t.status(400).json({error:"Invalid event ID format"});return t.status(500).json({error:"Server error",details:e.message})}}let I={api:{bodyParser:!0}},E=(0,a.M)(n,"default"),S=(0,a.M)(n,"config"),T=new s.PagesAPIRouteModule({definition:{kind:o.A.PAGES_API,page:"/api/scheduler/scheduler",pathname:"/api/scheduler/scheduler",bundlePath:"",filename:""},userland:n})},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var n=r(5542),s=r.n(n);let o=require("next-auth/providers/discord");var a=r.n(o),i=r(8580);let d={providers:[a()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,n=t.accessToken||null;e.user.id=r,e.user.accessToken=n;let s=!1;if(r)if((i.dashboardConfig.dashboard.admins||[]).includes(r))s=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();s=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),n=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(n)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var n=r(9021),s=r(2115),o=r.n(s),a=r(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>n.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");n.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=n.readFileSync(e,"utf8");i=o().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=6154);module.exports=r})();