"use strict";(()=>{var e={};e.id=8739,e.ids=[8739],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return n}});var n=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,n)=>{e.exports=n(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8508:(e,t,n)=>{n.r(t),n.d(t,{config:()=>E,default:()=>c,routeModule:()=>S});var r={};n.r(r),n.d(r,{default:()=>u});var o=n(3433),s=n(264),i=n(584),a=n(5806),d=n(8525),l=n(8580);async function u(e,t){try{let n=await (0,a.getServerSession)(e,t,d.authOptions);if(!n?.user)return t.status(401).json({error:"Unauthorized"});if(!n.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:r,token:o}=l.dashboardConfig.bot;if(!o||!r)return t.status(500).json({error:"Bot configuration missing"});if("POST"===e.method)try{let{name:n,color:s,permissions:i=[],hoist:a,mentionable:d}=e.body;if(!n)return t.status(400).json({error:"Role name is required"});let l={CREATE_INSTANT_INVITE:1n<<0n,KICK_MEMBERS:1n<<1n,BAN_MEMBERS:1n<<2n,ADMINISTRATOR:1n<<3n,MANAGE_CHANNELS:1n<<4n,MANAGE_GUILD:1n<<5n,ADD_REACTIONS:1n<<6n,VIEW_AUDIT_LOG:1n<<7n,PRIORITY_SPEAKER:1n<<8n,STREAM:1n<<9n,VIEW_CHANNEL:1n<<10n,SEND_MESSAGES:1n<<11n,SEND_TTS_MESSAGES:1n<<12n,MANAGE_MESSAGES:1n<<13n,EMBED_LINKS:1n<<14n,ATTACH_FILES:1n<<15n,READ_MESSAGE_HISTORY:1n<<16n,MENTION_EVERYONE:1n<<17n,USE_EXTERNAL_EMOJIS:1n<<18n,CONNECT:1n<<20n,SPEAK:1n<<21n,MUTE_MEMBERS:1n<<22n,DEAFEN_MEMBERS:1n<<23n,MOVE_MEMBERS:1n<<24n,USE_VAD:1n<<25n,CHANGE_NICKNAME:1n<<26n,MANAGE_NICKNAMES:1n<<27n,MANAGE_ROLES:1n<<28n,MANAGE_WEBHOOKS:1n<<29n,MANAGE_EMOJIS_AND_STICKERS:1n<<30n,USE_APPLICATION_COMMANDS:1n<<31n},u=0n;if(Array.isArray(i))for(let e of i){let t=l[e];"bigint"==typeof t&&(u|=t)}let c={name:n,hoist:!!a,mentionable:!!d};if(s){let e=parseInt(s.replace("#",""),16);isNaN(e)||(c.color=e)}0n!==u&&(c.permissions=u.toString());let E=await fetch(`https://discord.com/api/v10/guilds/${r}/roles`,{method:"POST",headers:{Authorization:`Bot ${o}`,"Content-Type":"application/json"},body:JSON.stringify(c)});if(!E.ok){let e;try{e=await E.json()}catch{e=await E.text()}return t.status(E.status).json(e)}let S=await E.json();return t.status(201).json(S)}catch(e){return t.status(500).json({error:"Failed to create role"})}if("GET"===e.method)try{let e=await fetch(`https://discord.com/api/v10/guilds/${r}/roles`,{headers:{Authorization:`Bot ${o}`}});if(!e.ok)throw Error("Failed to fetch roles");let n=await e.json();return t.status(200).json(n)}catch(e){return t.status(500).json({error:"Failed to fetch roles"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let c=(0,i.M)(r,"default"),E=(0,i.M)(r,"config"),S=new o.PagesAPIRouteModule({definition:{kind:s.A.PAGES_API,page:"/api/discord/roles",pathname:"/api/discord/roles",bundlePath:"",filename:""},userland:r})},8525:(e,t,n)=>{n.r(t),n.d(t,{authOptions:()=>d,default:()=>l});var r=n(5542),o=n.n(r);let s=require("next-auth/providers/discord");var i=n.n(s),a=n(8580);let d={providers:[i()({clientId:a.dashboardConfig.bot.clientId,clientSecret:a.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:n})=>(t&&n&&(e.accessToken=t.access_token||null,e.id=n.id||null),e),async session({session:e,token:t}){if(e?.user){let n=t.id||null,r=t.accessToken||null;e.user.id=n,e.user.accessToken=r;let o=!1;if(n)if((a.dashboardConfig.dashboard.admins||[]).includes(n))o=!0;else{let e=a.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&a.dashboardConfig.bot.token&&a.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${a.dashboardConfig.bot.guildId}/members/${n}`,{headers:{Authorization:`Bot ${a.dashboardConfig.bot.token}`}});if(t.ok){let n=await t.json();o=e.some(e=>n.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let n=new URL(t),r=`${n.protocol}//localhost${n.port?`:${n.port}`:""}`;return e.startsWith(t)||e.startsWith(r)?e:t}},secret:a.dashboardConfig.dashboard.session.secret||a.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=o()(d)},8580:(e,t,n)=>{n.r(t),n.d(t,{dashboardConfig:()=>d,default:()=>l});var r=n(9021),o=n(2115),s=n.n(o),i=n(3873);let a={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>r.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");r.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=r.readFileSync(e,"utf8");a=s().parse(t)}catch(e){process.exit(1)}let d={bot:{token:a.bot.token,clientId:a.bot.clientId,clientSecret:a.bot.clientSecret,guildId:a.bot.guildId,ticketCategoryId:a.bot.ticketCategoryId||null,ticketLogChannelId:a.bot.ticketLogChannelId||null,prefix:a.bot.prefix},dashboard:{admins:a.dashboard?.admins||[],adminRoleIds:a.dashboard?.adminRoleIds||[],session:{secret:a.dashboard?.session?.secret||a.bot.clientSecret}},database:{url:a.database.url,name:a.database.name,options:{maxPoolSize:a.database.options?.maxPoolSize||10,minPoolSize:a.database.options?.minPoolSize||1,maxIdleTimeMS:a.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:a.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:a.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:a.database.options?.connectTimeoutMS||1e4,retryWrites:a.database.options?.retryWrites!==!1,retryReads:a.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var n=t(t.s=8508);module.exports=n})();