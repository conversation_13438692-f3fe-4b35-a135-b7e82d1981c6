"use strict";(()=>{var e={};e.id=9039,e.ids=[9039],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>c});var s=o(5542),r=o.n(s);let a=require("next-auth/providers/discord");var n=o.n(a),i=o(8580);let d={providers:[n()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,s=t.accessToken||null;e.user.id=o,e.user.accessToken=s;let r=!1;if(o)if((i.dashboardConfig.dashboard.admins||[]).includes(o))r=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();r=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=r,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),s=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},c=r()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>c});var s=o(9021),r=o(2115),a=o.n(r),n=o(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>n.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=n.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");i=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let c=d},9021:e=>{e.exports=require("fs")},9538:(e,t,o)=>{o.r(t),o.d(t,{config:()=>h,default:()=>p,routeModule:()=>b});var s={};o.r(s),o.d(s,{default:()=>m});var r=o(3433),a=o(264),n=o(584);let i=require("fs/promises");var d=o.n(i),c=o(3873),l=o.n(c),u=o(5542),f=o(8525);async function m(e,t){try{if(!await (0,u.getServerSession)(e,t,f.authOptions))return t.status(401).json({error:"Unauthorized"});let{name:o}=e.query;if(!o||"string"!=typeof o)return t.status(400).json({error:"Invalid addon name"});let s=process.cwd().includes("dashboard")?l().resolve(process.cwd(),"..",".."):process.cwd(),r=[l().join(s,"src","addons"),l().join(s,"404-bot","src","addons"),l().join(s,"dist","addons"),l().join(s,"404-bot","dist","addons")],a="";for(let e of r)try{await d().access(e),a=e;break}catch{continue}if(!a)return t.status(500).json({error:"Addons directory not found",checkedPaths:r});let n=l().join(a,o);a.includes("src")||a.includes("dist");let i=[l().join(n,"config.yml"),l().join(n,"config.example.yml"),l().join(n,"example","config.yml"),l().join(n,"example","config.example.yml")];if("GET"===e.method){try{await d().access(n)}catch{return t.status(404).json({error:`Addon directory not found: ${o}`,path:n})}for(let e of i)try{let o=await d().readFile(e,"utf-8");return t.status(200).json({config:o,path:e})}catch(e){continue}return t.status(404).json({error:"Configuration not found",checkedPaths:i,addonPath:n})}if("POST"===e.method){let{config:r}=e.body;if(!r)return t.status(400).json({error:"No configuration provided"});try{await d().access(n)}catch{return t.status(404).json({error:`Addon directory not found: ${o}`,path:n})}let a=i[0],c=l().join(s,"dist","addons"),u=l().join(c,o,"config.yml"),f=[a];try{await d().access(c);let e=l().join(c,o);try{await d().access(e),f.push(u)}catch{await d().mkdir(e,{recursive:!0}),f.push(u)}}catch{}let m=[],p=!1;for(let e of f)try{await d().writeFile(e,r,"utf-8"),m.push({path:e,success:!0})}catch(t){m.push({path:e,success:!1,error:t.message}),p=!0}if(p)return t.status(500).json({error:"Failed to save configuration to some locations",results:m});return t.status(200).json({message:"Configuration saved successfully to all locations",results:m})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:e.message||"Internal server error",stack:void 0})}}let p=(0,n.M)(s,"default"),h=(0,n.M)(s,"config"),b=new r.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/admin/addons/[name]/config",pathname:"/api/admin/addons/[name]/config",bundlePath:"",filename:""},userland:s})}};var t=require("../../../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=9538);module.exports=o})();