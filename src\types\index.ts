import { Client, SlashCommandBuilder, CommandInteraction, ContextMenuCommandBuilder, MessageContextMenuCommandInteraction, UserContextMenuCommandInteraction, Collection } from 'discord.js';
import { Logger } from 'winston';
import { MongoClient, Db } from 'mongodb';
import { DatabaseManager } from '../core/DatabaseManager.js';

export interface BotConfig {
  bot: {
    token: string;
    clientId: string;
    guildId?: string;
    prefix: string;
    presence?: {
      status?: 'online' | 'idle' | 'dnd' | 'invisible';
      activity?: {
        type?: 'PLAYING' | 'STREAMING' | 'LISTENING' | 'WATCHING' | 'COMPETING';
        name?: string;
      };
    };
  };
  logging: {
    level: string;
    console: boolean;
    file: {
      enabled: boolean;
      path: string;
      maxSize: string;
      maxFiles: number;
      datePattern: string;
    };
  };
  addons: {
    enabled: boolean;
    directory: string;
    autoReload: boolean;
  };
  database: {
    type: 'mongodb';
    url: string;
    name: string;
    options?: {
      maxPoolSize?: number;
      minPoolSize?: number;
      maxIdleTimeMS?: number;
      serverSelectionTimeoutMS?: number;
      socketTimeoutMS?: number;
      connectTimeoutMS?: number;
      heartbeatFrequencyMS?: number;
      retryWrites?: boolean;
      retryReads?: boolean;
    };
  };
  features: {
    commandCooldown: number;
    errorReporting: boolean;
    metrics: boolean;
  };
}

export interface AddonInfo {
  name: string;
  version: string;
  description: string;
  author: string;
  dependencies?: string[];
}

export interface SlashCommand {
  data: SlashCommandBuilder;
  execute: (interaction: CommandInteraction, bot: BotInstance) => Promise<void>;
  cooldown?: number;
}

export interface ContextMenuCommand {
  data: ContextMenuCommandBuilder;
  execute: (interaction: MessageContextMenuCommandInteraction | UserContextMenuCommandInteraction) => Promise<void>;
  cooldown?: number;
}

export interface EventHandler {
  name: string;
  once?: boolean;
  execute: (...args: any[]) => Promise<void>;
}

export interface BotInstance {
  client: Client;
  config: any;
  logger: any;
  database: DatabaseManager;
  addons: Map<string, any>;
  commands: Map<string, any>;
  contextMenus: Map<string, any>;
  cooldowns: Map<string, any>;
}

export interface Addon {
  info: {
    name: string;
    version: string;
    description: string;
    author: string;
  };
  commands?: any[];
  events?: {
    name: string;
    once: boolean;
    execute: (...args: any[]) => Promise<void>;
  }[];
  onLoad?: (bot: BotInstance) => Promise<void>;
  onUnload?: (bot: BotInstance) => Promise<void>;
}

export interface DatabaseStats {
  collections: number;
  documents: number;
  dataSize: number;
  storageSize: number;
  indexes: number;
  indexSize: number;
} 