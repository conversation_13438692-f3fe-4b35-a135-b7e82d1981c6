import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Dynamically load commands from commands folder
async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(`Failed to load command ${file}:`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: 'uwu hug',
    version: '1.0.0',
    description: 'fucking please work',
    author: 'onedeyepete'
  },

  commands: await loadCommands(),

  events: [],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger('uwu hug');
    logger.info(`Loading uwu hug with ${addon.commands?.length || 0} commands...`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger('uwu hug');
    logger.info(`Unloading uwu hug...`);
  }
};

export default addon;