import { MongoClient } from 'mongodb';
import fs from 'fs';
import path from 'path';
import YAML from 'yaml';

async function cleanupServers() {
  // Read config file
  const configPath = path.join(process.cwd(), 'config.yml');
  const configFile = fs.readFileSync(configPath, 'utf8');
  const config = YAML.parse(configFile);

  const { url: mongoUrl, name: dbName } = config.database;

  try {
    console.log('Connecting to MongoDB...');
    const client = await MongoClient.connect(mongoUrl);
    const db = client.db(dbName);
    
    console.log('Connected to database. Listing all game servers...');
    
    // First, let's see what servers exist
    const servers = await db.collection('gameservers').find({}).toArray();
    console.log('Found servers:', servers);
    
    if (servers.length === 0) {
      console.log('No servers found in the database.');
      await client.close();
      return;
    }
    
    console.log('\nCleaning up game servers...');
    
    // Delete all game servers that match the criteria
    const result = await db.collection('gameservers').deleteMany({
      host: '*************',
      port: 26900
    });

    console.log(`Deleted ${result.deletedCount} game servers`);
    
    await client.close();
    console.log('Cleanup complete!');
  } catch (error) {
    console.error('Error during cleanup:', error);
  }
}

cleanupServers(); 