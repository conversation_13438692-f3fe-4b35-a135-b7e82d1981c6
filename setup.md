# Discord Bot Setup Guide

## Prerequisites

1. **Node.js 18+** - Download from [nodejs.org](https://nodejs.org/)
2. **pnpm** - Install globally: `npm install -g pnpm`
3. **MongoDB** - Either local installation or cloud service like MongoDB Atlas
4. **Discord Bot Token** - Get from [Discord Developer Portal](https://discord.com/developers/applications)

## Step 1: Setup MongoDB

### Option A: Local MongoDB
1. Download and install MongoDB from [mongodb.com](https://www.mongodb.com/try/download/community)
2. Start MongoDB service:
   - Windows: MongoDB should start automatically as a service
   - macOS: `brew services start mongodb-community`
   - Linux: `sudo systemctl start mongod`
3. MongoDB will be available at `mongodb://localhost:27017`

### Option B: MongoDB Atlas (Cloud)
1. Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a free account and cluster
3. Create a database user with read/write permissions
4. Get your connection string (looks like `mongodb+srv://username:<EMAIL>`)
5. Whitelist your IP address in Atlas security settings

### Option C: Docker
```bash
docker run -d -p 27017:27017 --name mongodb mongo:latest
```

## Step 2: Get Your Bot Token

1. Go to https://discord.com/developers/applications
2. Click "New Application" and give it a name
3. Go to the "Bot" section
4. Click "Reset Token" and copy the token (keep it secret!)
5. Under "Privileged Gateway Intents", enable:
   - Server Members Intent
   - Message Content Intent
6. Go to "OAuth2" > "URL Generator"
7. Select scopes: `bot` and `applications.commands`
8. Select bot permissions you need (Administrator for testing)
9. Use the generated URL to invite your bot to a server

## Step 3: Configure the Bot

1. Copy the example config:
   ```bash
   cp config.example.yml config.yml
   ```

2. Edit `config.yml` with your details:
   ```yaml
   bot:
     token: "YOUR_BOT_TOKEN_HERE"        # Paste your bot token
     clientId: "YOUR_CLIENT_ID_HERE"     # From Discord Developer Portal > General Information
     guildId: "YOUR_GUILD_ID_HERE"       # Right-click your server > Copy Server ID (optional)
     prefix: "!"

   database:
     type: "mongodb"
     url: "mongodb://localhost:27017"    # Your MongoDB connection string
     name: "discord_bot"                 # Database name
   ```

### MongoDB Connection Examples:
- **Local**: `mongodb://localhost:27017`
- **With Auth**: `*******************************************`
- **Atlas**: `mongodb+srv://username:<EMAIL>`
- **Replica Set**: `mongodb://host1:27017,host2:27017,host3:27017/database?replicaSet=rs0`

## Step 4: Run the Bot

### Development Mode (with hot reload):
```bash
pnpm dev
```

### Production Mode:
```bash
pnpm build
pnpm start
```

## Step 5: Test the Bot

Once the bot is online, you should see:
- Bot appears online in your Discord server
- Log messages showing successful startup
- Database connection confirmation
- Example addon with `/ping` and `/dbtest` commands available

Try these commands in your server:
- `/ping` - Basic ping command with database status
- `/dbtest` - Test database operations (inserts and queries data)

## MongoDB Features

The bot includes comprehensive MongoDB integration:

### Database Manager Features:
- **Connection pooling** with configurable pool sizes
- **Automatic reconnection** with exponential backoff
- **Health checks** and connection monitoring
- **Index management** for optimal performance
- **Error handling** and logging

### Example Database Operations:
The example addon demonstrates:
- Basic document insertion and querying
- Index creation
- Error handling for database operations
- Connection status checking

## Project Structure

```
404-bot/
├── src/
│   ├── addons/
│   │   └── example/        # Example addon with MongoDB operations
│   │       └── index.ts
│   ├── core/               # Core bot systems
│   │   ├── AddonManager.ts # Handles loading/unloading addons
│   │   ├── CommandHandler.ts # Handles slash commands
│   │   ├── ConfigManager.ts # YAML config management
│   │   ├── DatabaseManager.ts # MongoDB connection and operations
│   │   └── Logger.ts       # Winston logging system
│   ├── types/              # TypeScript type definitions
│   │   └── index.ts
│   └── index.ts           # Main entry point
├── dist/                  # Compiled JavaScript (auto-generated)
├── logs/                  # Log files (auto-generated)
├── config.yml            # Your bot configuration
└── package.json
```

## Creating Database-Enabled Addons

Here's how to use MongoDB in your addons:

```typescript
import { SlashCommandBuilder } from 'discord.js';
import { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

const addon: Addon = {
  info: {
    name: 'my-addon',
    version: '1.0.0',
    description: 'My database-enabled addon',
    author: 'Your Name'
  },

  commands: [
    {
      data: new SlashCommandBuilder()
        .setName('savedata')
        .setDescription('Save some data to database'),
      
      execute: async (interaction) => {
        const botInstance = (interaction.client as any).botInstance as BotInstance;
        
        if (!botInstance?.database) {
          await interaction.reply('Database not available!');
          return;
        }

        const db = botInstance.database.db;
        const collection = db.collection('user_data');
        
        try {
          await collection.insertOne({
            userId: interaction.user.id,
            data: 'Some data',
            timestamp: new Date()
          });
          
          await interaction.reply('Data saved!');
        } catch (error) {
          console.error('Database error:', error);
          await interaction.reply('Failed to save data!');
        }
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    if (bot.database) {
      // Create indexes when addon loads
      await bot.database.db.collection('user_data')
        .createIndex({ userId: 1 });
    }
  }
};

export default addon;
```

## MongoDB Best Practices

1. **Indexes**: Create indexes for frequently queried fields
2. **Connection Pooling**: Use the configured pool settings
3. **Error Handling**: Always wrap database operations in try-catch
4. **Data Validation**: Validate data before inserting
5. **Logging**: Use the addon logger for database operations

## Troubleshooting

### Bot won't start:
- Check your bot token in `config.yml`
- Verify MongoDB is running and accessible
- Check firewall settings for MongoDB port (27017)

### Database connection failed:
- Verify MongoDB connection string
- Check MongoDB server status
- For Atlas: verify IP whitelist and credentials
- Check network connectivity

### Commands not appearing:
- Wait a few minutes for global commands to sync
- Use `guildId` in config for instant deployment to test server
- Check the logs for deployment errors

### Database operations failing:
- Check MongoDB logs for connection issues
- Verify database name and collection permissions
- Look at bot logs for specific error messages

## Next Steps

- Explore MongoDB operations in the example addon
- Set up proper data models for your use case
- Implement data validation and sanitization
- Consider setting up database backups
- Monitor database performance and optimize queries

## Support

Check the logs in the `logs/` directory for detailed error information. The bot logs everything including database connections, addon loading, command execution, and errors. 