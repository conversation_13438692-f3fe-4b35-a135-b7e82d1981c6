# Dependencies
node_modules/
.pnpm-debug.log*

# Build output
dist/
build/
.vercel
.swc/
*.tsbuildinfo

# Next.js build cache (not needed for deployment)
.next/cache/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
.pnpm-lock.yaml

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel files
.cache
.parcel-cache

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Bot specific
data/
config.yml
!config.example.yml

# Hot reload files
src/dashboard/addon-reload.signal 