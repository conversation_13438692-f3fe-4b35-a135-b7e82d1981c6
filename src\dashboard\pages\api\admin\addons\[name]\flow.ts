import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../../auth/[...nextauth]';
import fs from 'fs';
import path from 'path';

// Helper function to find project root (folder containing package.json)
function findProjectRoot(start = process.cwd()): string {
  let dir = start;
  while (dir !== path.parse(dir).root) {
    if (fs.existsSync(path.join(dir, 'package.json'))) return dir;
    dir = path.dirname(dir);
  }
  return start; // fallback
}

// Helper function to find addon path
function findAddonPath(name: string): string | null {
  const projectRoot = process.cwd().includes('dashboard') 
    ? path.resolve(process.cwd(), '..', '..')
    : process.cwd();
    
  const nameSlug = name.replace(/\s+/g, '-');
  const possiblePaths = [
    path.join(projectRoot, 'src', 'addons', name),
    path.join(projectRoot, 'src', 'addons', nameSlug),
    path.join(process.cwd(), 'src', 'addons', name),
    path.join(process.cwd(), 'src', 'addons', nameSlug),
    path.join(process.cwd(), '..', '..', 'src', 'addons', name),
    path.join(process.cwd(), '..', '..', 'src', 'addons', nameSlug)
  ];
  
  for (const addonPath of possiblePaths) {
    if (fs.existsSync(addonPath)) {
      return addonPath;
    }
  }
  
  return null;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Check authentication
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  // Check admin permission
  const isAdmin = (session.user as any).isAdmin;
  if (!isAdmin) {
    return res.status(403).json({ error: 'Forbidden - Admin access required' });
  }

  const { name } = req.query;
  if (!name || typeof name !== 'string') {
    return res.status(400).json({ error: 'Invalid addon name' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Find the addon path
    const addonPath = findAddonPath(name);
    
    if (!addonPath) {
      return res.status(404).json({ error: 'Addon not found' });
    }

    // Check for flow.json file
    const flowPath = path.join(addonPath, 'flow.json');
    
    if (!fs.existsSync(flowPath)) {
      return res.status(404).json({ error: 'Original flow data not found', details: 'This addon was built before flow recovery was implemented' });
    }

    // Read and return the flow data
    const flowData = fs.readFileSync(flowPath, 'utf8');
    const parsedFlow = JSON.parse(flowData);

    return res.status(200).json(parsedFlow);

  } catch (error: any) {
    console.error('Error loading flow data:', error);
    return res.status(500).json({ error: 'Internal server error', details: error.message });
  }
} 