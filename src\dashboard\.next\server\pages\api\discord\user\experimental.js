"use strict";(()=>{var e={};e.id=7166,e.ids=[7166],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return s}});var s=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},378:(e,t,s)=>{s.r(t),s.d(t,{config:()=>h,default:()=>g,routeModule:()=>x});var a={};s.r(a),s.d(a,{default:()=>f});var o=s(3433),n=s(264),r=s(584),i=s(5806),d=s(8525),l=s(2518),c=s(8580);let u=null,b=c.dashboardConfig.database?.url||"mongodb://localhost:27017",m=c.dashboardConfig.database?.name||"discord_bot";async function p(){return u||(u=await l.MongoClient.connect(b,{...c.dashboardConfig.database?.options||{}})),u.db(m)}async function f(e,t){let s=await (0,i.getServerSession)(e,t,d.authOptions);if(!s?.user?.id)return t.status(401).json({hasAccess:!1,reason:"unauthenticated"});let a=s.user.id;if("933023999770918932"===a)return t.status(200).json({hasAccess:!0,reason:"developer"});try{let e=await p();if(await e.collection("experimental_testers").findOne({userId:a}))return t.status(200).json({hasAccess:!0,reason:"tester"});let s=await e.collection("experimental_settings").findOne({key:"applications_enabled"}),o=s?.enabled===!0;return t.status(200).json({hasAccess:o,reason:o?"open":"closed"})}catch(e){return t.status(500).json({hasAccess:!1,reason:"error"})}}let g=(0,r.M)(a,"default"),h=(0,r.M)(a,"config"),x=new o.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/discord/user/experimental",pathname:"/api/discord/user/experimental",bundlePath:"",filename:""},userland:a})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,s){return s in t?t[s]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,s)):"function"==typeof t&&"default"===s?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,s)=>{e.exports=s(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,s)=>{s.r(t),s.d(t,{authOptions:()=>d,default:()=>l});var a=s(5542),o=s.n(a);let n=require("next-auth/providers/discord");var r=s.n(n),i=s(8580);let d={providers:[r()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:s})=>(t&&s&&(e.accessToken=t.access_token||null,e.id=s.id||null),e),async session({session:e,token:t}){if(e?.user){let s=t.id||null,a=t.accessToken||null;e.user.id=s,e.user.accessToken=a;let o=!1;if(s)if((i.dashboardConfig.dashboard.admins||[]).includes(s))o=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${s}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let s=await t.json();o=e.some(e=>s.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let s=new URL(t),a=`${s.protocol}//localhost${s.port?`:${s.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=o()(d)},8580:(e,t,s)=>{s.r(t),s.d(t,{dashboardConfig:()=>d,default:()=>l});var a=s(9021),o=s(2115),n=s.n(o),r=s(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");i=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var s=t(t.s=378);module.exports=s})();