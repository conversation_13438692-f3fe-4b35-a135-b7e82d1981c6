(()=>{var e={};e.id=3659,e.ids=[3220,3659],e.modules={303:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>p});var s=r(8732),n=r(2015),i=r(6909),a=r(9733),l=r(8079),c=r(3001),d=e([i,a,c]);[i,a,c]=d.then?(await d)():d;let h={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.roles}",description:"User Roles (array)",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions (array)",icon:"\uD83D\uDD10"},{name:"{user.isBot}",description:"Is Bot (true/false)",icon:"\uD83E\uDD16"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.nsfw}",description:"Is NSFW (true/false)",icon:"\uD83D\uDD1E"},{name:"{channel.memberCount}",description:"Member Count (number)",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.memberCount}",description:"Total Members (number)",icon:"\uD83D\uDC65"},{name:"{server.boostLevel}",description:"Boost Level (number)",icon:"\uD83D\uDE80"},{name:"{server.owner}",description:"Server Owner ID",icon:"\uD83D\uDC51"}],message:[{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.length}",description:"Message Length (number)",icon:"\uD83D\uDCCF"},{name:"{message.mentions}",description:"Message Mentions (array)",icon:"\uD83D\uDCE2"},{name:"{message.attachments}",description:"Attachments Count (number)",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Embeds Count (number)",icon:"\uD83D\uDCCB"}],api:[{name:"{response.status}",description:"HTTP Status Code (number)",icon:"\uD83D\uDD22"},{name:"{response.data}",description:"Response Data",icon:"\uD83D\uDCCA"},{name:"{response.error}",description:"Error Message",icon:"❌"},{name:"{response.length}",description:"Response Array Length",icon:"\uD83D\uDCCF"}],time:[{name:"{time.hour}",description:"Current Hour (0-23)",icon:"\uD83D\uDD50"},{name:"{time.day}",description:"Day of Week (0-6)",icon:"\uD83D\uDCC5"},{name:"{time.date}",description:"Current Date",icon:"\uD83D\uDCC6"},{name:"{time.timestamp}",description:"Unix Timestamp",icon:"⏰"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.boolean}",description:"Random True/False",icon:"\uD83C\uDFAF"}]},x=[{value:"userHasRole",label:"\uD83C\uDFAD User Has Role",category:"User",description:"Check if user has a specific role"},{value:"userIsAdmin",label:"\uD83D\uDC51 User Is Admin",category:"User",description:"Check if user is server admin"},{value:"userHasPermission",label:"\uD83D\uDD10 User Has Permission",category:"User",description:"Check if user has specific permission"},{value:"userIsBot",label:"\uD83E\uDD16 User Is Bot",category:"User",description:"Check if user is a bot"},{value:"userJoinedRecently",label:"\uD83D\uDEAA User Joined Recently",category:"User",description:"Check if user joined within timeframe"},{value:"messageContains",label:"\uD83D\uDCAC Message Contains",category:"Message",description:"Check if message contains text"},{value:"messageLength",label:"\uD83D\uDCCF Message Length",category:"Message",description:"Check message character count"},{value:"messageHasMentions",label:"\uD83D\uDCE2 Message Has Mentions",category:"Message",description:"Check if message mentions users/roles"},{value:"messageHasAttachments",label:"\uD83D\uDCCE Message Has Attachments",category:"Message",description:"Check if message has files"},{value:"messageHasEmbeds",label:"\uD83D\uDCCB Message Has Embeds",category:"Message",description:"Check if message has embeds"},{value:"channelType",label:"\uD83D\uDCFA Channel Type",category:"Channel",description:"Check channel type (text, voice, etc.)"},{value:"channelIsNSFW",label:"\uD83D\uDD1E Channel Is NSFW",category:"Channel",description:"Check if channel is NSFW"},{value:"channelMemberCount",label:"\uD83D\uDC65 Channel Member Count",category:"Channel",description:"Check voice channel member count"},{value:"serverMemberCount",label:"\uD83D\uDC65 Server Member Count",category:"Server",description:"Check total server members"},{value:"serverBoostLevel",label:"\uD83D\uDE80 Server Boost Level",category:"Server",description:"Check server boost level"},{value:"serverName",label:"\uD83C\uDFE0 Server Name",category:"Server",description:"Check server name"},{value:"timeOfDay",label:"\uD83D\uDD50 Time of Day",category:"Time",description:"Check current hour of day"},{value:"dayOfWeek",label:"\uD83D\uDCC5 Day of Week",category:"Time",description:"Check day of the week"},{value:"apiResponseStatus",label:"\uD83D\uDD22 API Response Status",category:"API",description:"Check HTTP status code"},{value:"apiResponseData",label:"\uD83D\uDCCA API Response Data",category:"API",description:"Check API response content"},{value:"customVariable",label:"⚙️ Custom Variable",category:"Custom",description:"Check custom variable value"},{value:"randomChance",label:"\uD83C\uDFB2 Random Chance",category:"Custom",description:"Random percentage chance"}],m=[{value:"equals",label:"= Equals",description:"Exact match"},{value:"notEquals",label:"≠ Not Equals",description:"Does not match"},{value:"contains",label:"\uD83D\uDD0D Contains",description:"Contains substring"},{value:"notContains",label:"\uD83D\uDEAB Not Contains",description:"Does not contain substring"},{value:"startsWith",label:"▶️ Starts With",description:"Begins with text"},{value:"endsWith",label:"◀️ Ends With",description:"Ends with text"},{value:"greaterThan",label:"> Greater Than",description:"Numeric greater than"},{value:"lessThan",label:"< Less Than",description:"Numeric less than"},{value:"greaterEqual",label:"≥ Greater or Equal",description:"Numeric greater than or equal"},{value:"lessEqual",label:"≤ Less or Equal",description:"Numeric less than or equal"},{value:"regex",label:"\uD83D\uDD0D Regex Match",description:"Regular expression pattern"},{value:"inArray",label:"\uD83D\uDCCB In Array",description:"Value exists in array"},{value:"hasLength",label:"\uD83D\uDCCF Has Length",description:"Array/string has specific length"}],u=(0,n.memo)(({data:e,selected:o,id:r,updateNodeData:t})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:u,onOpen:p,onClose:g}=(0,a.useDisclosure)(),[b,j]=(0,n.useState)(()=>({operator:"equals",logicalOperator:"AND",caseSensitive:!1,conditions:[],...e})),[f,S]=(0,n.useState)(!1),v=e=>{j(o=>({...o,...e}))},y=e=>{let o=x.find(o=>o.value===e);return o?o.label.split(" ").slice(1).join(" "):e},C=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#f59e0b":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.Handle,{type:"target",position:i.Position.Top,style:{background:"#f59e0b",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(a.Box,{bg:"orange.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(l.lrG,{})}),(0,s.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Condition"})]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.VSk,{}),size:"xs",variant:"ghost",onClick:p,"aria-label":"Configure condition"})]}),(0,s.jsx)(a.Box,{children:(0,s.jsxs)(a.HStack,{spacing:1,children:[b.conditionType&&(0,s.jsx)(a.Text,{fontSize:"xs",children:(e=>{let o=x.find(o=>o.value===e);return o?o.label.split(" ")[0]:"❓"})(b.conditionType)}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:b.conditionType?y(b.conditionType):"Select Condition"})]})}),b.operator&&b.value&&(0,s.jsx)(a.Box,{children:(0,s.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:[(e=>m.find(o=>o.value===e)?.label||e)(b.operator).split(" ").slice(1).join(" "),' "',b.value.length>15?b.value.substring(0,15)+"...":b.value,'"']})}),(0,s.jsxs)(a.HStack,{spacing:1,justify:"space-between",children:[(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"green",children:"TRUE"}),(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"red",children:"FALSE"})]})]}),(0,s.jsx)(i.Handle,{type:"source",position:i.Position.Bottom,id:"true",style:{background:"#38a169",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"25%",transform:"translateX(-50%)"}}),(0,s.jsx)(i.Handle,{type:"source",position:i.Position.Bottom,id:"false",style:{background:"#e53e3e",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"75%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(a.Modal,{isOpen:u,onClose:()=>{t&&r&&t(r,b),g()},size:"4xl",children:[(0,s.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"orange.400",maxW:"1200px",children:[(0,s.jsx)(a.ModalHeader,{color:d.colors.text,children:"❓ Configure Condition"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,s.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:f?(0,s.jsx)(l._NO,{}):(0,s.jsx)(l.Vap,{}),onClick:()=>S(!f),children:[f?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your conditions! Click any variable below to copy it. Conditions determine which path (TRUE or FALSE) the flow takes."})]}),(0,s.jsx)(a.Collapse,{in:f,animateOpacity:!0,children:(0,s.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(h).map(([e,o])=>(0,s.jsxs)(a.AccordionItem,{border:"none",children:[(0,s.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,s.jsx)(a.AccordionIcon,{})]}),(0,s.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,s.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>C(e.name),children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(a.Code,{fontSize:"xs",colorScheme:"orange",children:e.name}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),C(e.name)}})]},e.name))})})]},e))})})})]}),(0,s.jsx)(a.Divider,{}),(0,s.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"orange",children:[(0,s.jsxs)(a.TabList,{children:[(0,s.jsx)(a.Tab,{children:"Basic Condition"}),(0,s.jsx)(a.Tab,{children:"Advanced"})]}),(0,s.jsxs)(a.TabPanels,{children:[(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Condition Type"}),(0,s.jsx)(a.Select,{value:b.conditionType||"",onChange:e=>v({conditionType:e.target.value}),placeholder:"Select a condition type",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:Object.entries(x.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(([e,o])=>(0,s.jsx)("optgroup",{label:e,children:o.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},e))})]}),b.conditionType&&(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:x.find(e=>e.value===b.conditionType)?.label}),(0,s.jsx)(a.Text,{fontSize:"sm",children:x.find(e=>e.value===b.conditionType)?.description})]})]}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Operator"}),(0,s.jsx)(a.Select,{value:b.operator||"equals",onChange:e=>v({operator:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Compare Value"}),(0,s.jsx)(a.Input,{value:b.value||"",onChange:e=>v({value:e.target.value}),placeholder:"userHasRole"===b.conditionType?"Member or {user.roles}":"messageContains"===b.conditionType?"hello or {message.content}":"serverMemberCount"===b.conditionType?"100 or {server.memberCount}":"timeOfDay"===b.conditionType?"14 (for 2 PM)":"Value to compare against",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,s.jsx)(a.Textarea,{value:b.description||"",onChange:e=>v({description:e.target.value}),placeholder:"Describe what this condition checks for",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]}),(0,s.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Understanding TRUE/FALSE Paths"}),(0,s.jsx)(a.Text,{fontSize:"sm",children:"• **TRUE (Left)**: Actions that run when the condition passes • **FALSE (Right)**: Actions that run when the condition fails • You can connect different actions to each path"})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,s.jsx)(a.VStack,{spacing:4,align:"stretch",children:(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.caseSensitive,onChange:e=>v({caseSensitive:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Case Sensitive"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Match exact capitalization for text comparisons"})]})]})}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:2,children:"\uD83D\uDCA1 Condition Examples:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",children:[(0,s.jsx)(a.Text,{children:'• Check if user has "Admin" role: **User Has Role** equals "Admin"'}),(0,s.jsx)(a.Text,{children:'• Check if message contains swear word: **Message Contains** contains "badword"'}),(0,s.jsx)(a.Text,{children:"• Check if server has many members: **Server Member Count** greater than 1000"}),(0,s.jsx)(a.Text,{children:"• Check if it's nighttime: **Time of Day** greater than 22"}),(0,s.jsx)(a.Text,{children:"• Check API status: **API Response Status** equals 200"})]})]})]}),(0,s.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:2,children:"⚠️ Important Notes:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",children:[(0,s.jsxs)(a.Text,{children:["• Use variables like ","{user.roles}"," to check dynamic values"]}),(0,s.jsx)(a.Text,{children:"• Number comparisons work with Greater/Less Than operators"}),(0,s.jsx)(a.Text,{children:"• Text comparisons work with Contains, Starts With, etc."}),(0,s.jsx)(a.Text,{children:"• Always connect both TRUE and FALSE paths for complete logic"})]})]})]})]})})]})]}),(0,s.jsx)(a.Button,{colorScheme:"orange",onClick:()=>{e.conditionType=b.conditionType,e.operator=b.operator,e.value=b.value,e.caseSensitive=b.caseSensitive,e.description=b.description,e.logicalOperator=b.logicalOperator,e.label=b.conditionType?y(b.conditionType):"Condition",g()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});u.displayName="ConditionNode";let p=u;t()}catch(e){t(e)}})},318:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>p});var s=r(8732),n=r(2015),i=r(6909),a=r(9733),l=r(8079),c=r(3001),d=e([i,a,c]);[i,a,c]=d.then?(await d)():d;let h={event:[{name:"{event.type}",description:"Type of event that triggered",icon:"\uD83D\uDCE1"},{name:"{event.timestamp}",description:"When the event occurred",icon:"⏰"},{name:"{event.guild}",description:"Server where event occurred",icon:"\uD83C\uDFE0"},{name:"{event.channel}",description:"Channel where event occurred",icon:"\uD83D\uDCFA"},{name:"{event.user}",description:"User who triggered the event",icon:"\uD83D\uDC64"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message creation time",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message edit time",icon:"✏️"},{name:"{message.attachments}",description:"Message attachments",icon:"\uD83D\uDCCE"},{name:"{message.embeds}",description:"Message embeds",icon:"\uD83D\uDCCB"},{name:"{message.reactions}",description:"Message reactions",icon:"\uD83D\uDC4D"},{name:"{message.mentions}",description:"Message mentions",icon:"\uD83D\uDCE2"}],member:[{name:"{member.id}",description:"Member ID",icon:"\uD83C\uDD94"},{name:"{member.username}",description:"Member username",icon:"\uD83D\uDC64"},{name:"{member.displayName}",description:"Member display name",icon:"\uD83D\uDCDD"},{name:"{member.tag}",description:"Member tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{member.mention}",description:"Member mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{member.avatar}",description:"Member avatar URL",icon:"\uD83D\uDDBC️"},{name:"{member.joinedAt}",description:"Server join date",icon:"\uD83D\uDEAA"},{name:"{member.roles}",description:"Member roles",icon:"\uD83C\uDFAD"},{name:"{member.permissions}",description:"Member permissions",icon:"\uD83D\uDD10"},{name:"{member.isBot}",description:"Is member a bot",icon:"\uD83E\uDD16"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{channel.position}",description:"Channel position",icon:"\uD83D\uDCCD"},{name:"{channel.nsfw}",description:"Is NSFW channel",icon:"\uD83D\uDD1E"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Total member count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server boost level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server boost count",icon:"\uD83D\uDC8E"},{name:"{server.createdAt}",description:"Server creation date",icon:"\uD83D\uDCC5"}],reaction:[{name:"{reaction.emoji}",description:"Reaction emoji",icon:"\uD83D\uDE00"},{name:"{reaction.count}",description:"Reaction count",icon:"\uD83D\uDD22"},{name:"{reaction.users}",description:"Users who reacted",icon:"\uD83D\uDC65"},{name:"{reaction.me}",description:"Bot reacted",icon:"\uD83E\uDD16"}],voice:[{name:"{voice.channelId}",description:"Voice channel ID",icon:"\uD83D\uDD0A"},{name:"{voice.channelName}",description:"Voice channel name",icon:"\uD83D\uDD0A"},{name:"{voice.memberCount}",description:"Voice channel member count",icon:"\uD83D\uDC65"},{name:"{voice.muted}",description:"Is member muted",icon:"\uD83D\uDD07"},{name:"{voice.deafened}",description:"Is member deafened",icon:"\uD83D\uDD07"},{name:"{voice.streaming}",description:"Is member streaming",icon:"\uD83D\uDCFA"},{name:"{voice.camera}",description:"Is member using camera",icon:"\uD83D\uDCF9"}],role:[{name:"{role.id}",description:"Role ID",icon:"\uD83C\uDD94"},{name:"{role.name}",description:"Role name",icon:"\uD83C\uDFAD"},{name:"{role.mention}",description:"Role mention (<@&id>)",icon:"\uD83D\uDCE2"},{name:"{role.color}",description:"Role color",icon:"\uD83C\uDFA8"},{name:"{role.position}",description:"Role position",icon:"\uD83D\uDCCD"},{name:"{role.permissions}",description:"Role permissions",icon:"\uD83D\uDD10"},{name:"{role.mentionable}",description:"Is role mentionable",icon:"\uD83D\uDCE2"},{name:"{role.hoisted}",description:"Is role hoisted",icon:"\uD83D\uDCCC"}]},x=[{value:"messageCreate",label:"\uD83D\uDCAC Message Created",category:"Messages",description:"When a new message is sent"},{value:"messageUpdate",label:"✏️ Message Edited",category:"Messages",description:"When a message is edited"},{value:"messageDelete",label:"\uD83D\uDDD1️ Message Deleted",category:"Messages",description:"When a message is deleted"},{value:"messageReactionAdd",label:"\uD83D\uDC4D Reaction Added",category:"Messages",description:"When a reaction is added to a message"},{value:"messageReactionRemove",label:"\uD83D\uDC4E Reaction Removed",category:"Messages",description:"When a reaction is removed from a message"},{value:"messageReactionRemoveAll",label:"\uD83E\uDDF9 All Reactions Removed",category:"Messages",description:"When all reactions are removed from a message"},{value:"guildMemberAdd",label:"\uD83D\uDEAA Member Joined",category:"Members",description:"When a new member joins the server"},{value:"guildMemberRemove",label:"\uD83D\uDC4B Member Left",category:"Members",description:"When a member leaves the server"},{value:"guildMemberUpdate",label:"\uD83D\uDC64 Member Updated",category:"Members",description:"When member info changes (roles, nickname, etc.)"},{value:"userUpdate",label:"\uD83D\uDCDD User Updated",category:"Members",description:"When user profile changes (avatar, username, etc.)"},{value:"presenceUpdate",label:"\uD83D\uDFE2 Presence Updated",category:"Members",description:"When member status/activity changes"},{value:"guildBanAdd",label:"\uD83D\uDD28 Member Banned",category:"Moderation",description:"When a member is banned"},{value:"guildBanRemove",label:"\uD83D\uDD13 Member Unbanned",category:"Moderation",description:"When a member is unbanned"},{value:"messageDeleteBulk",label:"\uD83E\uDDF9 Bulk Message Delete",category:"Moderation",description:"When multiple messages are deleted at once"},{value:"voiceStateUpdate",label:"\uD83D\uDD0A Voice State Changed",category:"Voice",description:"When member joins/leaves/mutes in voice"},{value:"channelCreate",label:"\uD83D\uDCFA Channel Created",category:"Channels",description:"When a new channel is created"},{value:"channelDelete",label:"\uD83D\uDDD1️ Channel Deleted",category:"Channels",description:"When a channel is deleted"},{value:"channelUpdate",label:"⚙️ Channel Updated",category:"Channels",description:"When channel settings change"},{value:"channelPinsUpdate",label:"\uD83D\uDCCC Channel Pins Updated",category:"Channels",description:"When pinned messages change"},{value:"roleCreate",label:"\uD83C\uDFAD Role Created",category:"Roles",description:"When a new role is created"},{value:"roleDelete",label:"\uD83D\uDDD1️ Role Deleted",category:"Roles",description:"When a role is deleted"},{value:"roleUpdate",label:"⚙️ Role Updated",category:"Roles",description:"When role settings change"},{value:"threadCreate",label:"\uD83E\uDDF5 Thread Created",category:"Threads",description:"When a thread is created"},{value:"threadDelete",label:"\uD83D\uDDD1️ Thread Deleted",category:"Threads",description:"When a thread is deleted"},{value:"threadUpdate",label:"⚙️ Thread Updated",category:"Threads",description:"When thread settings change"},{value:"threadMemberUpdate",label:"\uD83D\uDC64 Thread Member Update",category:"Threads",description:"When someone joins/leaves a thread"},{value:"interactionCreate",label:"\uD83C\uDF9B️ Interaction Created",category:"Interactions",description:"When buttons/selects are used"},{value:"applicationCommandPermissionsUpdate",label:"\uD83D\uDD10 Command Permissions Updated",category:"Interactions",description:"When command permissions change"},{value:"guildUpdate",label:"\uD83C\uDFE0 Server Updated",category:"Server",description:"When server settings change"},{value:"guildUnavailable",label:"⚠️ Server Unavailable",category:"Server",description:"When server becomes unavailable"},{value:"guildIntegrationsUpdate",label:"\uD83D\uDD17 Integrations Updated",category:"Server",description:"When server integrations change"},{value:"inviteCreate",label:"\uD83D\uDD17 Invite Created",category:"Server",description:"When an invite is created"},{value:"inviteDelete",label:"\uD83D\uDDD1️ Invite Deleted",category:"Server",description:"When an invite is deleted"},{value:"emojiCreate",label:"\uD83D\uDE00 Emoji Created",category:"Server",description:"When a custom emoji is added"},{value:"emojiDelete",label:"\uD83D\uDDD1️ Emoji Deleted",category:"Server",description:"When a custom emoji is removed"},{value:"emojiUpdate",label:"⚙️ Emoji Updated",category:"Server",description:"When a custom emoji is modified"},{value:"stickerCreate",label:"\uD83C\uDFF7️ Sticker Created",category:"Server",description:"When a custom sticker is added"},{value:"stickerDelete",label:"\uD83D\uDDD1️ Sticker Deleted",category:"Server",description:"When a custom sticker is removed"},{value:"stickerUpdate",label:"⚙️ Sticker Updated",category:"Server",description:"When a custom sticker is modified"}],m=[{value:"channel",label:"\uD83D\uDCFA Channel Filter",description:"Filter by specific channels"},{value:"role",label:"\uD83C\uDFAD Role Filter",description:"Filter by user roles"},{value:"user",label:"\uD83D\uDC64 User Filter",description:"Filter by specific users"},{value:"regex",label:"\uD83D\uDD0D Regex Pattern",description:"Filter using regular expressions"},{value:"cooldown",label:"⏰ Cooldown",description:"Rate limit event triggers"},{value:"permission",label:"\uD83D\uDD10 Permission",description:"Filter by user permissions"},{value:"content",label:"\uD83D\uDCAC Content Filter",description:"Filter by message content"},{value:"custom",label:"⚙️ Custom",description:"Custom filter condition"}],u=(0,n.memo)(({data:e,selected:o,id:r,updateNodeData:t})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:u,onOpen:p,onClose:g}=(0,a.useDisclosure)(),[b,j]=(0,n.useState)(()=>({ignoreBot:!0,ignoreSystem:!0,rateLimited:!1,rateLimit:1e3,priority:1,async:!1,retryOnError:!1,maxRetries:3,filters:[],channelRestrictions:[],roleRestrictions:[],...e})),[f,S]=(0,n.useState)(!1),v=e=>{j(o=>({...o,...e}))},y=e=>{let o=x.find(o=>o.value===e);return o?o.label.split(" ").slice(1).join(" "):e},C=(e,o)=>{let r=[...b.filters||[]];r[e]={...r[e],...o},v({filters:r})},k=e=>{let o=(b.filters||[]).filter((o,r)=>r!==e);v({filters:o})},T=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#10b981":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.Handle,{type:"target",position:i.Position.Top,style:{background:"#10b981",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(a.Box,{bg:"green.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(l.DQs,{})}),(0,s.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Event"})]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.VSk,{}),size:"xs",variant:"ghost",onClick:p,"aria-label":"Configure event"})]}),(0,s.jsx)(a.Box,{children:(0,s.jsxs)(a.HStack,{spacing:1,children:[b.eventType&&(0,s.jsx)(a.Text,{fontSize:"xs",children:(e=>{let o=x.find(o=>o.value===e);return o?o.label.split(" ")[0]:"\uD83D\uDCE1"})(b.eventType)}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:b.eventType?y(b.eventType):"Select Event"})]})}),b.description&&(0,s.jsx)(a.Box,{children:(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:b.description.length>25?b.description.substring(0,25)+"...":b.description})}),(0,s.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[(b.filters?.length??0)>0&&(0,s.jsxs)(a.Badge,{size:"xs",colorScheme:"green",children:[b.filters?.length," filter",(b.filters?.length??0)!==1?"s":""]}),b.ignoreBot&&(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"orange",children:"No Bots"}),b.rateLimited&&(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"yellow",children:"Rate Limited"})]})]}),(0,s.jsx)(i.Handle,{type:"source",position:i.Position.Bottom,style:{background:"#10b981",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(a.Modal,{isOpen:u,onClose:()=>{t&&r&&t(r,b),g()},size:"4xl",children:[(0,s.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"green.400",maxW:"1200px",children:[(0,s.jsx)(a.ModalHeader,{color:d.colors.text,children:"\uD83D\uDCE1 Configure Event"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,s.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:f?(0,s.jsx)(l._NO,{}):(0,s.jsx)(l.Vap,{}),onClick:()=>S(!f),children:[f?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your event responses! Click any variable below to copy it. Variables are replaced with actual values when your event triggers."})]}),(0,s.jsx)(a.Collapse,{in:f,animateOpacity:!0,children:(0,s.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(h).map(([e,o])=>(0,s.jsxs)(a.AccordionItem,{border:"none",children:[(0,s.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,s.jsx)(a.AccordionIcon,{})]}),(0,s.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,s.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>T(e.name),children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(a.Code,{fontSize:"xs",colorScheme:"green",children:e.name}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),T(e.name)}})]},e.name))})})]},e))})})})]}),(0,s.jsx)(a.Divider,{}),(0,s.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"green",children:[(0,s.jsxs)(a.TabList,{children:[(0,s.jsx)(a.Tab,{children:"Event Type"}),(0,s.jsx)(a.Tab,{children:"Filters"}),(0,s.jsx)(a.Tab,{children:"Settings"}),(0,s.jsx)(a.Tab,{children:"Advanced"})]}),(0,s.jsxs)(a.TabPanels,{children:[(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Event Type"}),(0,s.jsx)(a.Select,{value:b.eventType||"",onChange:e=>v({eventType:e.target.value}),placeholder:"Select an event type",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:Object.entries(x.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(([e,o])=>(0,s.jsx)("optgroup",{label:e,children:o.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},e))})]}),b.eventType&&(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:x.find(e=>e.value===b.eventType)?.label}),(0,s.jsx)(a.Text,{fontSize:"sm",children:x.find(e=>e.value===b.eventType)?.description})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,s.jsx)(a.Textarea,{value:b.description||"",onChange:e=>v({description:e.target.value}),placeholder:"Describe when this event should trigger",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Event Filters"}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.GGD,{}),onClick:()=>{v({filters:[...b.filters||[],{type:"channel",value:"",operator:"equals"}]})},colorScheme:"green",size:"sm",children:"Add Filter"})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"Filters determine when this event should trigger. Only events that pass all filters will execute the connected actions."})]}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[b.filters?.map((e,o)=>(0,s.jsxs)(a.Box,{p:4,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:3,children:[(0,s.jsxs)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,children:["Filter ",o+1]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>k(o),"aria-label":"Remove filter"})]}),(0,s.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:3,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Filter Type"}),(0,s.jsx)(a.Select,{value:e.type,onChange:e=>C(o,{type:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm",children:m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Operator"}),(0,s.jsxs)(a.Select,{value:e.operator||"equals",onChange:e=>C(o,{operator:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm",children:[(0,s.jsx)("option",{value:"equals",children:"Equals"}),(0,s.jsx)("option",{value:"contains",children:"Contains"}),(0,s.jsx)("option",{value:"startsWith",children:"Starts With"}),(0,s.jsx)("option",{value:"endsWith",children:"Ends With"}),(0,s.jsx)("option",{value:"regex",children:"Regex"})]})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Filter Value"}),(0,s.jsx)(a.Input,{value:e.value,onChange:e=>C(o,{value:e.target.value}),placeholder:"channel"===e.type?"general or {channel.name}":"role"===e.type?"Member or {role.name}":"user"===e.type?"username or {user.id}":"regex"===e.type?"^Hello.*":"content"===e.type?"hello world":"Filter value",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:m.find(o=>o.value===e.type)?.description})]})]},o)),(!b.filters||0===b.filters.length)&&(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{children:"No filters configured. This event will trigger for all occurrences of the selected event type."})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Event Settings"}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.ignoreBot,onChange:e=>v({ignoreBot:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Ignore Bot Messages"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Don't trigger on messages from bots (recommended)"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.ignoreSystem,onChange:e=>v({ignoreSystem:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Ignore System Messages"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Don't trigger on Discord system messages"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.rateLimited,onChange:e=>v({rateLimited:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Rate Limited"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Limit how often this event can trigger"})]})]}),b.rateLimited&&(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Rate Limit (milliseconds)"}),(0,s.jsxs)(a.NumberInput,{value:b.rateLimit||1e3,onChange:e=>v({rateLimit:parseInt(e)||1e3}),min:100,max:6e4,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Minimum time between triggers (1000ms = 1 second)"})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Event Priority"}),(0,s.jsxs)(a.NumberInput,{value:b.priority||1,onChange:e=>v({priority:parseInt(e)||1}),min:1,max:10,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Higher priority events execute first (1 = highest, 10 = lowest)"})]}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.async,onChange:e=>v({async:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Async Processing"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Don't wait for this event to complete before processing others"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.retryOnError,onChange:e=>v({retryOnError:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Retry on Error"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Automatically retry if event processing fails"})]})]}),b.retryOnError&&(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Max Retries"}),(0,s.jsxs)(a.NumberInput,{value:b.maxRetries||3,onChange:e=>v({maxRetries:parseInt(e)||3}),min:1,max:10,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Maximum number of retry attempts"})]})]})]})})]})]}),(0,s.jsx)(a.Button,{colorScheme:"green",onClick:()=>{e.eventType=b.eventType,e.description=b.description,e.filters=b.filters,e.ignoreBot=b.ignoreBot,e.ignoreSystem=b.ignoreSystem,e.rateLimited=b.rateLimited,e.rateLimit=b.rateLimit,e.priority=b.priority,e.async=b.async,e.retryOnError=b.retryOnError,e.maxRetries=b.maxRetries,e.channelRestrictions=b.channelRestrictions,e.roleRestrictions=b.roleRestrictions,e.label=b.eventType?y(b.eventType):"Event",g()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});u.displayName="EventNode";let p=u;t()}catch(e){t(e)}})},361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{"use strict";e.exports=require("react")},2326:e=>{"use strict";e.exports=require("react-dom")},3001:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{DP:()=>h,NP:()=>x,nk:()=>c});var s=r(8732),n=r(2015),i=r(9733),a=r(6390),l=e([i,a]);[i,a]=l.then?(await l)():l;let c=[{id:"default",name:"Default Purple",description:"Classic purple and blue gradient theme",colors:{primary:"#8b5cf6",primaryLight:"#a78bfa",primaryDark:"#7c3aed",secondary:"#5865F2",accent:"#06b6d4",background:"#1a202c",surface:"rgba(255,255,255,0.03)",text:"#f7fafc",textSecondary:"#a0aec0",border:"rgba(255,255,255,0.2)",success:"#68d391",warning:"#fbb6ce",error:"#fc8181",info:"#63b3ed"}},{id:"ocean",name:"Ocean Blue",description:"Deep blue ocean-inspired theme",colors:{primary:"#0ea5e9",primaryLight:"#38bdf8",primaryDark:"#0284c7",secondary:"#06b6d4",accent:"#8b5cf6",background:"#0f172a",surface:"rgba(59, 130, 246, 0.05)",text:"#f1f5f9",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}},{id:"forest",name:"Forest Green",description:"Nature-inspired green theme",colors:{primary:"#059669",primaryLight:"#10b981",primaryDark:"#047857",secondary:"#065f46",accent:"#8b5cf6",background:"#0f1419",surface:"rgba(16, 185, 129, 0.05)",text:"#f0fdf4",textSecondary:"#86efac",border:"rgba(16, 185, 129, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"sunset",name:"Sunset Orange",description:"Warm sunset-inspired theme",colors:{primary:"#ea580c",primaryLight:"#fb923c",primaryDark:"#c2410c",secondary:"#dc2626",accent:"#8b5cf6",background:"#1c1917",surface:"rgba(251, 146, 60, 0.05)",text:"#fef7ed",textSecondary:"#fdba74",border:"rgba(251, 146, 60, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"rose",name:"Rose Pink",description:"Elegant rose and pink theme",colors:{primary:"#e11d48",primaryLight:"#f43f5e",primaryDark:"#be123c",secondary:"#ec4899",accent:"#8b5cf6",background:"#1f1720",surface:"rgba(244, 63, 94, 0.05)",text:"#fdf2f8",textSecondary:"#fda4af",border:"rgba(244, 63, 94, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"midnight",name:"Midnight Blue",description:"Dark midnight blue theme",colors:{primary:"#1e40af",primaryLight:"#3b82f6",primaryDark:"#1e3a8a",secondary:"#4338ca",accent:"#06b6d4",background:"#0c0a1f",surface:"rgba(59, 130, 246, 0.05)",text:"#f8fafc",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}}],d=(0,n.createContext)(void 0),h=()=>{let e=(0,n.useContext)(d);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},x=({children:e})=>{let[o,r]=(0,n.useState)(c[0]),[t,l]=(0,n.useState)([]);(0,n.useEffect)(()=>{let e=localStorage.getItem("dashboard-color-scheme"),o=localStorage.getItem("dashboard-custom-schemes");if(o)try{let e=JSON.parse(o);l(e)}catch(e){}if(e){let o=c.find(o=>o.id===e);if(o)r(o);else{let o=localStorage.getItem("dashboard-custom-schemes");if(o)try{let t=JSON.parse(o).find(o=>o.id===e);t&&r(t)}catch(e){}}}},[]),(0,n.useEffect)(()=>{localStorage.setItem("dashboard-color-scheme",o.id)},[o]),(0,n.useEffect)(()=>{localStorage.setItem("dashboard-custom-schemes",JSON.stringify(t))},[t]);let h=[...c,...t],x=(0,i.extendTheme)({...a.A,colors:{...a.A.colors,brand:{50:o.colors.primaryLight+"20",100:o.colors.primaryLight+"40",200:o.colors.primaryLight+"60",300:o.colors.primaryLight+"80",400:o.colors.primaryLight,500:o.colors.primary,600:o.colors.primaryDark,700:o.colors.primaryDark+"CC",800:o.colors.primaryDark+"AA",900:o.colors.primaryDark+"88"},custom:{primary:o.colors.primary,primaryLight:o.colors.primaryLight,primaryDark:o.colors.primaryDark,secondary:o.colors.secondary,accent:o.colors.accent,background:o.colors.background,surface:o.colors.surface,text:o.colors.text,textSecondary:o.colors.textSecondary,border:o.colors.border,success:o.colors.success,warning:o.colors.warning,error:o.colors.error,info:o.colors.info}},styles:{global:{body:{bg:o.colors.background,color:o.colors.text}}}});return(0,s.jsx)(d.Provider,{value:{currentScheme:o,setColorScheme:e=>{let o=c.find(o=>o.id===e);if(o)return void r(o);let s=t.find(o=>o.id===e);s&&r(s)},colorSchemes:h,customSchemes:t,addCustomScheme:e=>{l(o=>[...o.filter(o=>o.id!==e.id),e]),r(e)},deleteCustomScheme:e=>{l(o=>o.filter(o=>o.id!==e)),o.id===e&&r(c[0])},resetToDefault:()=>{r(c[0])}},children:(0,s.jsx)(i.ChakraProvider,{theme:x,children:e})})};t()}catch(e){t(e)}})},3567:(e,o,r)=>{"use strict";r.r(o),r.d(o,{default:()=>n});var t=r(8732),s=r(8270);function n(){return(0,t.jsxs)(s.Html,{lang:"en",children:[(0,t.jsx)(s.Head,{}),(0,t.jsxs)("body",{children:[(0,t.jsx)(s.Main,{}),(0,t.jsx)(s.NextScript,{})]})]})}},3873:e=>{"use strict";e.exports=require("path")},4075:e=>{"use strict";e.exports=require("zlib")},4235:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>p});var s=r(8732),n=r(2015),i=r(6909),a=r(9733),l=r(8079),c=r(3001),d=e([i,a,c]);[i,a,c]=d.then?(await d)():d;let h={command:[{name:"{command.name}",description:"Command name that was executed",icon:"⚡"},{name:"{command.user}",description:"User who executed the command",icon:"\uD83D\uDC64"},{name:"{command.channel}",description:"Channel where command was executed",icon:"\uD83D\uDCFA"},{name:"{command.server}",description:"Server where command was executed",icon:"\uD83C\uDFE0"},{name:"{command.timestamp}",description:"When the command was executed",icon:"⏰"}],options:[{name:"{option.name}",description:"Value of a specific option",icon:"\uD83D\uDD27"},{name:"{option.user}",description:"User option value",icon:"\uD83D\uDC64"},{name:"{option.channel}",description:"Channel option value",icon:"\uD83D\uDCFA"},{name:"{option.role}",description:"Role option value",icon:"\uD83C\uDFAD"},{name:"{option.string}",description:"String option value",icon:"\uD83D\uDCAC"},{name:"{option.number}",description:"Number option value",icon:"\uD83D\uDD22"},{name:"{option.boolean}",description:"Boolean option value",icon:"✅"}],user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"}]},x=["ADMINISTRATOR","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","KICK_MEMBERS","BAN_MEMBERS","MANAGE_MESSAGES","EMBED_LINKS","ATTACH_FILES","READ_MESSAGE_HISTORY","MENTION_EVERYONE","USE_EXTERNAL_EMOJIS","CONNECT","SPEAK","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS","USE_VAD","CHANGE_NICKNAME","MANAGE_NICKNAMES","MANAGE_WEBHOOKS","MANAGE_EMOJIS","MODERATE_MEMBERS","VIEW_AUDIT_LOG","MANAGE_EVENTS","MANAGE_THREADS","CREATE_PUBLIC_THREADS","CREATE_PRIVATE_THREADS","USE_EXTERNAL_STICKERS","SEND_MESSAGES_IN_THREADS","START_EMBEDDED_ACTIVITIES"],m=[{value:"string",label:"\uD83D\uDCDD String - Text input"},{value:"integer",label:"\uD83D\uDD22 Integer - Whole number"},{value:"number",label:"\uD83D\uDD22 Number - Decimal number"},{value:"boolean",label:"✅ Boolean - True/False"},{value:"user",label:"\uD83D\uDC64 User - Discord user"},{value:"channel",label:"\uD83D\uDCFA Channel - Discord channel"},{value:"role",label:"\uD83C\uDFAD Role - Discord role"},{value:"mentionable",label:"\uD83D\uDCE2 Mentionable - User or role"},{value:"attachment",label:"\uD83D\uDCCE Attachment - File upload"}],u=(0,n.memo)(({data:e,selected:o,id:r,updateNodeData:t})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:u,onOpen:p,onClose:g}=(0,a.useDisclosure)(),[b,j]=(0,n.useState)(()=>({guildOnly:!1,adminOnly:!1,allowDMs:!1,cooldown:0,options:[],category:"general",examples:[],permissions:[],ephemeral:!1,deferReply:!1,...e})),[f,S]=(0,n.useState)(!1),v=e=>{j(o=>({...o,...e}))},y=(e,o)=>{let r=[...b.options||[]];r[e]={...r[e],...o},v({options:r})},C=e=>{let o=(b.options||[]).filter((o,r)=>r!==e);v({options:o})},k=e=>{let o=[...b.options||[]];o[e].choices||(o[e].choices=[]),o[e].choices.push({name:"",value:""}),v({options:o})},T=(e,o,r,t)=>{let s=[...b.options||[]];s[e].choices&&(s[e].choices[o][r]=t,v({options:s}))},w=(e,o)=>{let r=[...b.options||[]];r[e].choices&&(r[e].choices=r[e].choices.filter((e,r)=>r!==o),v({options:r}))},A=e=>{navigator.clipboard.writeText(e)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#3b82f6":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.Handle,{type:"target",position:i.Position.Top,style:{background:"#3b82f6",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(a.Box,{bg:"blue.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(l.FrA,{})}),(0,s.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Command"})]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.VSk,{}),size:"xs",variant:"ghost",onClick:p,"aria-label":"Configure command"})]}),(0,s.jsx)(a.Box,{children:(0,s.jsxs)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:["/",b.commandName||"unnamed"]})}),b.description&&(0,s.jsx)(a.Box,{children:(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:b.description.length>25?b.description.substring(0,25)+"...":b.description})}),(0,s.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[(b.options?.length??0)>0&&(0,s.jsxs)(a.Badge,{size:"xs",colorScheme:"blue",children:[b.options?.length," option",(b.options?.length??0)!==1?"s":""]}),b.adminOnly&&(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"red",children:"Admin"}),b.cooldown&&b.cooldown>0&&(0,s.jsxs)(a.Badge,{size:"xs",colorScheme:"orange",children:[b.cooldown,"s"]})]})]}),(0,s.jsx)(i.Handle,{type:"source",position:i.Position.Bottom,style:{background:"#3b82f6",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(a.Modal,{isOpen:u,onClose:()=>{t&&r&&t(r,b),g()},size:"4xl",children:[(0,s.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"blue.400",maxW:"1200px",children:[(0,s.jsx)(a.ModalHeader,{color:d.colors.text,children:"⚡ Configure Command"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,s.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:f?(0,s.jsx)(l._NO,{}):(0,s.jsx)(l.Vap,{}),onClick:()=>S(!f),children:[f?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your command responses! Click any variable below to copy it. Variables are replaced with actual values when your command runs."})]}),(0,s.jsx)(a.Collapse,{in:f,animateOpacity:!0,children:(0,s.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(h).map(([e,o])=>(0,s.jsxs)(a.AccordionItem,{border:"none",children:[(0,s.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,s.jsx)(a.AccordionIcon,{})]}),(0,s.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,s.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>A(e.name),children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(a.Code,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),A(e.name)}})]},e.name))})})]},e))})})})]}),(0,s.jsx)(a.Divider,{}),(0,s.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"blue",children:[(0,s.jsxs)(a.TabList,{children:[(0,s.jsx)(a.Tab,{children:"Basic Info"}),(0,s.jsx)(a.Tab,{children:"Options"}),(0,s.jsx)(a.Tab,{children:"Permissions"}),(0,s.jsx)(a.Tab,{children:"Advanced"})]}),(0,s.jsxs)(a.TabPanels,{children:[(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Command Name"}),(0,s.jsxs)(a.InputGroup,{children:[(0,s.jsx)(a.InputLeftAddon,{bg:d.colors.surface,color:d.colors.text,children:"/"}),(0,s.jsx)(a.Input,{value:b.commandName||"",onChange:e=>v({commandName:e.target.value}),placeholder:"ping",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Category"}),(0,s.jsxs)(a.Select,{value:b.category||"general",onChange:e=>v({category:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:[(0,s.jsx)("option",{value:"general",children:"General"}),(0,s.jsx)("option",{value:"moderation",children:"Moderation"}),(0,s.jsx)("option",{value:"fun",children:"Fun"}),(0,s.jsx)("option",{value:"utility",children:"Utility"}),(0,s.jsx)("option",{value:"admin",children:"Admin"}),(0,s.jsx)("option",{value:"info",children:"Info"}),(0,s.jsx)("option",{value:"music",children:"Music"}),(0,s.jsx)("option",{value:"games",children:"Games"})]})]})]}),(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,s.jsx)(a.Textarea,{value:b.description||"",onChange:e=>v({description:e.target.value}),placeholder:"What does this command do?",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Usage Examples"}),(0,s.jsx)(a.Textarea,{value:b.examples?.join("\n")||"",onChange:e=>v({examples:e.target.value.split("\n").filter(e=>e.trim())}),placeholder:`/ping
/ping server
/ping {user.mention}`,bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"One example per line"})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Command Options"}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.GGD,{}),onClick:()=>{v({options:[...b.options||[],{name:"",description:"",type:"string",required:!1,choices:[]}]})},colorScheme:"blue",size:"sm",children:"Add Option"})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"Options are parameters users can provide with your command. They appear as autocomplete fields in Discord."})]}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[b.options?.map((e,o)=>(0,s.jsxs)(a.Box,{p:4,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:3,children:[(0,s.jsxs)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,children:["Option ",o+1]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>C(o),"aria-label":"Remove option"})]}),(0,s.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:3,children:[(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Option Name"}),(0,s.jsx)(a.Input,{value:e.name,onChange:e=>y(o,{name:e.target.value}),placeholder:"user",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Option Type"}),(0,s.jsx)(a.Select,{value:e.type,onChange:e=>y(o,{type:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm",children:m.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Description"}),(0,s.jsx)(a.Input,{value:e.description,onChange:e=>y(o,{description:e.target.value}),placeholder:"The user to ping",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,s.jsxs)(a.HStack,{children:[(0,s.jsx)(a.Switch,{isChecked:e.required,onChange:e=>y(o,{required:e.target.checked}),colorScheme:"blue"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.text,children:"Required option"})]}),("string"===e.type||"integer"===e.type||"number"===e.type)&&(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Predefined Choices (Optional)"}),(0,s.jsx)(a.Button,{size:"xs",leftIcon:(0,s.jsx)(l.GGD,{}),onClick:()=>k(o),colorScheme:"blue",variant:"ghost",children:"Add Choice"})]}),(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",children:e.choices?.map((e,r)=>(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsx)(a.Input,{value:e.name,onChange:e=>T(o,r,"name",e.target.value),placeholder:"Choice name",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"}),(0,s.jsx)(a.Input,{value:e.value,onChange:e=>T(o,r,"value",e.target.value),placeholder:"Choice value",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.QLg,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>w(o,r),"aria-label":"Remove choice"})]},r))})]})]})]},o)),(!b.options||0===b.options.length)&&(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{children:"No options configured. Your command will work without any parameters."})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Command Permissions"}),(0,s.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"Be careful with permissions! Overly restrictive permissions can prevent legitimate users from using your command."})]}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.adminOnly,onChange:e=>v({adminOnly:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Admin Only"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Only server administrators can use this command"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.guildOnly,onChange:e=>v({guildOnly:e.target.checked}),colorScheme:"blue"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Server Only"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Command can only be used in servers, not DMs"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.allowDMs,onChange:e=>v({allowDMs:e.target.checked}),colorScheme:"green"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Allow DMs"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Command can be used in direct messages"})]})]})]}),(0,s.jsx)(a.Divider,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:3,children:"Required Permissions"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.textSecondary,mb:3,children:"Select the Discord permissions users need to use this command"}),(0,s.jsx)(a.CheckboxGroup,{value:b.permissions||[],onChange:e=>v({permissions:e}),children:(0,s.jsx)(a.SimpleGrid,{columns:3,spacing:2,children:x.map(e=>(0,s.jsx)(a.Checkbox,{value:e,colorScheme:"blue",size:"sm",children:(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,children:e.replace(/_/g," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase())})},e))})})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Cooldown (seconds)"}),(0,s.jsxs)(a.NumberInput,{value:b.cooldown||0,onChange:e=>v({cooldown:parseInt(e)||0}),min:0,max:3600,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"How long users must wait between uses (0 = no cooldown)"})]}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.ephemeral,onChange:e=>v({ephemeral:e.target.checked}),colorScheme:"blue"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Ephemeral Response"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Command response is only visible to the user who ran it"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:b.deferReply,onChange:e=>v({deferReply:e.target.checked}),colorScheme:"orange"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Defer Reply"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:'Show "thinking..." message while processing (for slow commands)'})]})]})]})]})})]})]}),(0,s.jsx)(a.Button,{colorScheme:"blue",onClick:()=>{e.commandName=b.commandName,e.description=b.description,e.options=b.options,e.permissions=b.permissions,e.cooldown=b.cooldown,e.guildOnly=b.guildOnly,e.adminOnly=b.adminOnly,e.allowDMs=b.allowDMs,e.category=b.category,e.examples=b.examples,e.ephemeral=b.ephemeral,e.deferReply=b.deferReply,e.label=b.commandName?`/${b.commandName}`:"Command",g()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});u.displayName="CommandNode";let p=u;t()}catch(e){t(e)}})},4722:e=>{"use strict";e.exports=require("next-auth/react")},6390:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>i});var s=r(9733),n=e([s]);s=(n.then?(await n)():n)[0];let i=(0,s.extendTheme)({config:{initialColorMode:"dark",useSystemColorMode:!1},fonts:{heading:"'Inter', sans-serif",body:"'Inter', sans-serif"},colors:{brand:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},discord:{50:"#e8e9fd",100:"#d1d3fc",200:"#b9bcfa",300:"#a2a5f9",400:"#8b8ef7",500:"#5865F2",600:"#4752c4",700:"#363f97",800:"#242c69",900:"#12193c"}},styles:{global:{body:{bg:"gray.900",color:"gray.100"}}},components:{Button:{defaultProps:{colorScheme:"brand"},variants:{solid:e=>({bg:`${e.colorScheme}.500`,color:"white",_hover:{bg:`${e.colorScheme}.600`,transform:"translateY(-2px)",boxShadow:"lg"},_active:{bg:`${e.colorScheme}.700`,transform:"translateY(0)"},transition:"all 0.2s ease"})}},Link:{baseStyle:{_hover:{textDecoration:"none"}}},Box:{baseStyle:{transition:"all 0.2s ease"}}}});t()}catch(e){t(e)}})},6566:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(o),r.d(o,{config:()=>p,default:()=>h,getServerSideProps:()=>u,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>g,routeModule:()=>y,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>f,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>b});var s=r(1292),n=r(8834),i=r(786),a=r(3567),l=r(8077),c=r(8280),d=e([l,c]);[l,c]=d.then?(await d)():d;let h=(0,i.M)(c,"default"),x=(0,i.M)(c,"getStaticProps"),m=(0,i.M)(c,"getStaticPaths"),u=(0,i.M)(c,"getServerSideProps"),p=(0,i.M)(c,"config"),g=(0,i.M)(c,"reportWebVitals"),b=(0,i.M)(c,"unstable_getStaticProps"),j=(0,i.M)(c,"unstable_getStaticPaths"),f=(0,i.M)(c,"unstable_getStaticParams"),S=(0,i.M)(c,"unstable_getServerProps"),v=(0,i.M)(c,"unstable_getServerSideProps"),y=new s.PagesRouteModule({definition:{kind:n.A.PAGES,page:"/admin/experimental/addon-builder",pathname:"/admin/experimental/addon-builder",bundlePath:"",filename:""},components:{App:l.default,Document:a.default},userland:c});t()}catch(e){t(e)}})},6909:e=>{"use strict";e.exports=import("reactflow")},7756:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>u});var s=r(8732),n=r(2015),i=r(6909),a=r(9733),l=r(8079),c=r(3001),d=e([i,a,c]);[i,a,c]=d.then?(await d)():d;let h=[{value:"sendMessage",label:"\uD83D\uDCAC Send Message",category:"Message"},{value:"sendEmbed",label:"\uD83D\uDCCB Send Embed",category:"Message"},{value:"editMessage",label:"✏️ Edit Message",category:"Message"},{value:"deleteMessage",label:"\uD83D\uDDD1️ Delete Message",category:"Message"},{value:"addReaction",label:"\uD83D\uDC4D Add Reaction",category:"Message"},{value:"removeReaction",label:"\uD83D\uDC4E Remove Reaction",category:"Message"},{value:"addRole",label:"\uD83C\uDFAD Add Role",category:"Roles"},{value:"removeRole",label:"\uD83C\uDFAD Remove Role",category:"Roles"},{value:"kickUser",label:"\uD83D\uDC62 Kick User",category:"Moderation"},{value:"banUser",label:"\uD83D\uDD28 Ban User",category:"Moderation"},{value:"timeoutUser",label:"⏰ Timeout User",category:"Moderation"},{value:"unbanUser",label:"\uD83D\uDD13 Unban User",category:"Moderation"},{value:"createChannel",label:"\uD83D\uDCFA Create Channel",category:"Channel"},{value:"deleteChannel",label:"\uD83D\uDDD1️ Delete Channel",category:"Channel"},{value:"lockChannel",label:"\uD83D\uDD12 Lock Channel",category:"Channel"},{value:"unlockChannel",label:"\uD83D\uDD13 Unlock Channel",category:"Channel"},{value:"sendDM",label:"\uD83D\uDCEC Send DM",category:"Message"},{value:"createThread",label:"\uD83E\uDDF5 Create Thread",category:"Channel"},{value:"pinMessage",label:"\uD83D\uDCCC Pin Message",category:"Message"},{value:"unpinMessage",label:"\uD83D\uDCCC Unpin Message",category:"Message"}],x={user:[{name:"{user.id}",description:"User ID",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username",icon:"\uD83D\uDC64"},{name:"{user.displayName}",description:"Display Name",icon:"\uD83D\uDCDD"},{name:"{user.tag}",description:"User Tag (username#0000)",icon:"\uD83C\uDFF7️"},{name:"{user.mention}",description:"User Mention (<@id>)",icon:"\uD83D\uDCE2"},{name:"{user.avatar}",description:"Avatar URL",icon:"\uD83D\uDDBC️"},{name:"{user.createdAt}",description:"Account Creation Date",icon:"\uD83D\uDCC5"},{name:"{user.joinedAt}",description:"Server Join Date",icon:"\uD83D\uDEAA"},{name:"{user.roles}",description:"User Roles",icon:"\uD83C\uDFAD"},{name:"{user.permissions}",description:"User Permissions",icon:"\uD83D\uDD10"}],channel:[{name:"{channel.id}",description:"Channel ID",icon:"\uD83C\uDD94"},{name:"{channel.name}",description:"Channel Name",icon:"\uD83D\uDCFA"},{name:"{channel.mention}",description:"Channel Mention (<#id>)",icon:"\uD83D\uDCE2"},{name:"{channel.type}",description:"Channel Type",icon:"\uD83D\uDCCB"},{name:"{channel.topic}",description:"Channel Topic",icon:"\uD83D\uDCAC"},{name:"{channel.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{channel.createdAt}",description:"Channel Creation Date",icon:"\uD83D\uDCC5"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDD94"},{name:"{server.name}",description:"Server Name",icon:"\uD83C\uDFE0"},{name:"{server.icon}",description:"Server Icon URL",icon:"\uD83D\uDDBC️"},{name:"{server.memberCount}",description:"Member Count",icon:"\uD83D\uDC65"},{name:"{server.createdAt}",description:"Server Creation Date",icon:"\uD83D\uDCC5"},{name:"{server.owner}",description:"Server Owner",icon:"\uD83D\uDC51"},{name:"{server.boostLevel}",description:"Server Boost Level",icon:"\uD83D\uDE80"},{name:"{server.boostCount}",description:"Server Boost Count",icon:"\uD83D\uDC8E"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83C\uDD94"},{name:"{message.content}",description:"Message Content",icon:"\uD83D\uDCAC"},{name:"{message.author}",description:"Message Author",icon:"\uD83D\uDC64"},{name:"{message.channel}",description:"Message Channel",icon:"\uD83D\uDCFA"},{name:"{message.createdAt}",description:"Message Creation Date",icon:"\uD83D\uDCC5"},{name:"{message.editedAt}",description:"Message Edit Date",icon:"✏️"},{name:"{message.reactions}",description:"Message Reactions",icon:"\uD83D\uDC4D"},{name:"{message.attachments}",description:"Message Attachments",icon:"\uD83D\uDCCE"}],api:[{name:"{response.data}",description:"API Response Data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP Status Code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response Headers",icon:"\uD83D\uDCCB"},{name:"{response.message}",description:"Response Message",icon:"\uD83D\uDCAC"},{name:"{response.error}",description:"Error Message",icon:"❌"}],random:[{name:"{random.number}",description:"Random Number (1-100)",icon:"\uD83C\uDFB2"},{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDD94"},{name:"{random.choice}",description:"Random Choice from Array",icon:"\uD83C\uDFAF"},{name:"{random.color}",description:"Random Hex Color",icon:"\uD83C\uDFA8"}],date:[{name:"{date.now}",description:"Current Date/Time",icon:"⏰"},{name:"{date.today}",description:"Today's Date",icon:"\uD83D\uDCC5"},{name:"{date.timestamp}",description:"Unix Timestamp",icon:"\uD83D\uDD50"},{name:"{date.iso}",description:"ISO Date String",icon:"\uD83D\uDCDD"}]},m=(0,n.memo)(({data:e,selected:o,id:r,updateNodeData:t})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:m,onOpen:u,onClose:p}=(0,a.useDisclosure)(),[g,b]=(0,n.useState)(()=>({embed:{fields:[],author:{name:""},footer:{text:""}},...e})),[j,f]=(0,n.useState)(!1),[S,v]=(0,n.useState)(!1),[y,C]=(0,n.useState)(null),[k,T]=(0,n.useState)(!1),w=e=>{b(o=>({...o,...e}))},A=e=>h.find(o=>o.value===e)?.label||e,D=e=>{navigator.clipboard.writeText(e)},z=async()=>{if(!y&&!k){T(!0);try{let e=await fetch("/api/admin/experimental/addon-builder/guild-data");if(e.ok){let o=await e.json();C({channels:o.channels,roles:o.roles,members:o.members})}}catch(e){}finally{T(!1)}}};(0,n.useEffect)(()=>{m&&z()},[m]);let I=(e,o,r)=>{let t=[...g.embed?.fields||[]];t[e]={...t[e],[o]:r},w({embed:{...g.embed,fields:t}})},B=e=>{let o=(g.embed?.fields||[]).filter((o,r)=>r!==e);w({embed:{...g.embed,fields:o}})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#a855f7":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.Handle,{type:"target",position:i.Position.Top,style:{background:"#a855f7",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(a.Box,{bg:"purple.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(l.x_j,{})}),(0,s.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"Action"})]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.VSk,{}),size:"xs",variant:"ghost",onClick:u,"aria-label":"Configure action"})]}),(0,s.jsx)(a.Box,{children:(0,s.jsxs)(a.HStack,{spacing:1,children:[g.actionType&&(0,s.jsx)(a.Text,{fontSize:"xs",children:(e=>{let o=h.find(o=>o.value===e);return o?.label.split(" ")[0]||"\uD83C\uDFAF"})(g.actionType)}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:g.actionType?A(g.actionType).split(" ").slice(1).join(" "):"Select Action"})]})}),g.message&&(0,s.jsx)(a.Box,{children:(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:g.message.length>20?g.message.substring(0,20)+"...":g.message})}),(0,s.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[g.channel&&(0,s.jsxs)(a.Badge,{size:"xs",colorScheme:"purple",children:["#",g.channel]}),g.role&&(0,s.jsxs)(a.Badge,{size:"xs",colorScheme:"purple",children:["@",g.role]}),g.embed?.title&&(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"blue",children:"\uD83D\uDCCB Embed"})]})]})]}),(0,s.jsxs)(a.Modal,{isOpen:m,onClose:()=>{t&&r&&t(r,g),p()},size:"4xl",children:[(0,s.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"purple.400",maxW:"1200px",children:[(0,s.jsx)(a.ModalHeader,{color:d.colors.text,children:"\uD83C\uDFAF Configure Action"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,s.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:j?(0,s.jsx)(l._NO,{}):(0,s.jsx)(l.Vap,{}),onClick:()=>f(!j),children:[j?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your actions! Click any variable below to copy it. Variables are replaced with actual values when your addon runs."})]}),(0,s.jsx)(a.Collapse,{in:j,animateOpacity:!0,children:(0,s.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(x).map(([e,o])=>(0,s.jsxs)(a.AccordionItem,{border:"none",children:[(0,s.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,s.jsx)(a.AccordionIcon,{})]}),(0,s.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,s.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>D(e.name),children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(a.Code,{fontSize:"xs",colorScheme:"blue",children:e.name}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),D(e.name)}})]},e.name))})})]},e))})})})]}),(0,s.jsx)(a.Divider,{}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Action Type"}),(0,s.jsx)(a.Select,{value:g.actionType||"",onChange:e=>w({actionType:e.target.value}),placeholder:"Select an action type",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:Object.entries(h.reduce((e,o)=>(e[o.category]||(e[o.category]=[]),e[o.category].push(o),e),{})).map(([e,o])=>(0,s.jsx)("optgroup",{label:e,children:o.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))},e))})]}),g.actionType&&(0,s.jsx)(s.Fragment,{children:"sendEmbed"===g.actionType?(0,s.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"purple",children:[(0,s.jsxs)(a.TabList,{children:[(0,s.jsx)(a.Tab,{children:"Embed Builder"}),(0,s.jsx)(a.Tab,{children:"Preview"})]}),(0,s.jsxs)(a.TabPanels,{children:[(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Channel"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Select,{value:g.channel||"",onChange:e=>w({channel:e.target.value}),placeholder:k?"Loading channels...":"Select a channel",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:y?.channels.filter(e=>"text"===e.type).map(e=>(0,s.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,s.jsx)(a.Input,{value:g.channel||"",onChange:e=>w({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Message Content (appears above embed)"}),(0,s.jsx)(a.Textarea,{value:g.message||"",onChange:e=>w({message:e.target.value}),placeholder:"Hello {user.username}! This text appears above the embed...",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"100px"})]}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Embed Title"}),(0,s.jsx)(a.Input,{value:g.embed?.title||"",onChange:e=>w({embed:{...g.embed,title:e.target.value}}),placeholder:"Welcome to {server.name}!",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Embed Color"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsx)(a.Input,{type:"color",value:g.embed?.color||"#5865F2",onChange:e=>w({embed:{...g.embed,color:e.target.value}}),w:"60px",h:"40px",p:1,bg:d.colors.background,borderColor:d.colors.border}),(0,s.jsx)(a.Input,{value:g.embed?.color||"",onChange:e=>w({embed:{...g.embed,color:e.target.value}}),placeholder:"#5865F2",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,flex:"1"})]}),(0,s.jsx)(a.HStack,{spacing:1,flexWrap:"wrap",children:["#5865F2","#57F287","#FEE75C","#EB459E","#ED4245","#FF6B35","#00ADB5","#9B59B6"].map(e=>(0,s.jsx)(a.Button,{size:"xs",bg:e,w:"30px",h:"20px",minW:"30px",p:0,onClick:()=>w({embed:{...g.embed,color:e}}),_hover:{transform:"scale(1.1)"},border:"1px solid",borderColor:d.colors.border},e))})]})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Embed Description"}),(0,s.jsx)(a.Textarea,{value:g.embed?.description||"",onChange:e=>w({embed:{...g.embed,description:e.target.value}}),placeholder:"This is the description that appears inside the embed...",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"100px"})]}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Thumbnail URL"}),(0,s.jsx)(a.Input,{value:g.embed?.thumbnail||"",onChange:e=>w({embed:{...g.embed,thumbnail:e.target.value}}),placeholder:"https://example.com/image.png",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Image URL"}),(0,s.jsx)(a.Input,{value:g.embed?.image||"",onChange:e=>w({embed:{...g.embed,image:e.target.value}}),placeholder:"https://example.com/image.png",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Author"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Input,{value:g.embed?.author?.name||"",onChange:e=>w({embed:{...g.embed,author:{...g.embed?.author,name:e.target.value}}}),placeholder:"Author name",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:2,children:[(0,s.jsx)(a.Input,{value:g.embed?.author?.url||"",onChange:e=>w({embed:{...g.embed,author:{...g.embed?.author,url:e.target.value}}}),placeholder:"Author URL",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsx)(a.Input,{value:g.embed?.author?.iconUrl||"",onChange:e=>w({embed:{...g.embed,author:{...g.embed?.author,iconUrl:e.target.value}}}),placeholder:"Author icon URL",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Footer"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Input,{value:g.embed?.footer?.text||"",onChange:e=>w({embed:{...g.embed,footer:{...g.embed?.footer,text:e.target.value}}}),placeholder:"Footer text",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsx)(a.Input,{value:g.embed?.footer?.iconUrl||"",onChange:e=>w({embed:{...g.embed,footer:{...g.embed?.footer,iconUrl:e.target.value}}}),placeholder:"Footer icon URL",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,mb:0,children:"Embed Fields"}),(0,s.jsx)(a.Button,{size:"sm",leftIcon:(0,s.jsx)(l.GGD,{}),onClick:()=>{let e=g.embed?.fields||[];w({embed:{...g.embed,fields:[...e,{name:"",value:"",inline:!1}]}})},colorScheme:"blue",children:"Add Field"})]}),(0,s.jsx)(a.VStack,{spacing:3,align:"stretch",children:g.embed?.fields?.map((e,o)=>(0,s.jsxs)(a.Box,{p:3,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:["Field ",o+1]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.IXo,{}),size:"xs",colorScheme:"red",variant:"ghost",onClick:()=>B(o),"aria-label":"Remove field"})]}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Input,{value:e.name,onChange:e=>I(o,"name",e.target.value),placeholder:"Field name",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsx)(a.Textarea,{value:e.value,onChange:e=>I(o,"value",e.target.value),placeholder:"Field value",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"}),(0,s.jsxs)(a.HStack,{children:[(0,s.jsx)(a.Switch,{isChecked:e.inline||!1,onChange:e=>I(o,"inline",e.target.checked),colorScheme:"purple"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.text,children:"Display inline"})]})]})]},o))})]}),(0,s.jsxs)(a.HStack,{children:[(0,s.jsx)(a.Switch,{isChecked:g.embed?.timestamp||!1,onChange:e=>w({embed:{...g.embed,timestamp:e.target.checked}}),colorScheme:"purple"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.text,children:"Show current timestamp"})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Embed Preview"}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"This shows your message content (above) and embed (below) as they will appear in Discord. Variables will be replaced with actual values when sent."})]}),(()=>{let e=g.embed||{};return(0,s.jsxs)(a.VStack,{spacing:3,align:"stretch",maxW:"500px",children:[g.message&&(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:3,children:[(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.text,fontWeight:"medium",children:"\uD83D\uDCE9 Message Content:"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.text,mt:1,children:g.message})]}),(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,borderLeft:`4px solid ${e.color||"#5865F2"}`,children:[e.author?.name&&(0,s.jsxs)(a.HStack,{spacing:2,mb:2,children:[e.author.iconUrl&&(0,s.jsx)(a.Box,{w:6,h:6,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"\uD83D\uDC64"}),(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:e.author.name})]}),e.title&&(0,s.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:2,children:e.title}),e.description&&(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.textSecondary,mb:3,children:e.description}),e.fields&&e.fields.length>0&&(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",mb:3,children:e.fields.map((e,o)=>(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:e.name}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.textSecondary,children:e.value})]},o))}),e.footer?.text&&(0,s.jsxs)(a.HStack,{spacing:2,mt:3,pt:2,borderTop:"1px solid",borderColor:d.colors.border,children:[e.footer.iconUrl&&(0,s.jsx)(a.Box,{w:4,h:4,borderRadius:"full",bg:"gray.300",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"xs",children:"ℹ️"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:e.footer.text})]}),e.timestamp&&(0,s.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:2,children:["\uD83D\uDD52 ",new Date().toLocaleString()]})]})]})})()]})})]})]}):(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[("sendMessage"===g.actionType||"sendDM"===g.actionType)&&(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Message Content"}),(0,s.jsx)(a.Textarea,{value:g.message||"",onChange:e=>w({message:e.target.value}),placeholder:"Hello {user.username}! Welcome to {server.name}!",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"100px"})]}),"sendDM"!==g.actionType&&(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Channel"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Select,{value:g.channel||"",onChange:e=>w({channel:e.target.value}),placeholder:k?"Loading channels...":"Select a channel",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:y?.channels.filter(e=>"text"===e.type).map(e=>(0,s.jsxs)("option",{value:e.name,children:["#",e.name]},e.id))}),(0,s.jsx)(a.Input,{value:g.channel||"",onChange:e=>w({channel:e.target.value}),placeholder:"Or type: general or {channel.name}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]})]}),("addRole"===g.actionType||"removeRole"===g.actionType)&&(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Role Name"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Select,{value:g.role||"",onChange:e=>w({role:e.target.value}),placeholder:k?"Loading roles...":"Select a role",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:y?.roles.map(e=>(0,s.jsxs)("option",{value:e.name,children:["@",e.name]},e.id))}),(0,s.jsx)(a.Input,{value:g.role||"",onChange:e=>w({role:e.target.value}),placeholder:"Or type: Member or {user.role}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Reason"}),(0,s.jsx)(a.Input,{value:g.reason||"",onChange:e=>w({reason:e.target.value}),placeholder:"Role updated by bot",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),("kickUser"===g.actionType||"banUser"===g.actionType)&&(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"User"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Select,{value:g.user||"",onChange:e=>w({user:e.target.value}),placeholder:k?"Loading members...":"Select a user",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:y?.members.map(e=>(0,s.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,s.jsx)(a.Input,{value:g.user||"",onChange:e=>w({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Reason"}),(0,s.jsx)(a.Input,{value:g.reason||"",onChange:e=>w({reason:e.target.value}),placeholder:"Violation of server rules",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),"banUser"===g.actionType&&(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Delete Message History"}),(0,s.jsx)(a.Switch,{isChecked:g.deleteMessages||!1,onChange:e=>w({deleteMessages:e.target.checked}),colorScheme:"purple"})]})]}),"timeoutUser"===g.actionType&&(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"User"}),(0,s.jsxs)(a.VStack,{spacing:2,align:"stretch",children:[(0,s.jsx)(a.Select,{value:g.user||"",onChange:e=>w({user:e.target.value}),placeholder:k?"Loading members...":"Select a user",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,isDisabled:k,children:y?.members.map(e=>(0,s.jsxs)("option",{value:e.username,children:[e.displayName," (@",e.username,")"]},e.id))}),(0,s.jsx)(a.Input,{value:g.user||"",onChange:e=>w({user:e.target.value}),placeholder:"Or type: username or {user.id}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Duration (minutes)"}),(0,s.jsxs)(a.NumberInput,{value:g.duration||10,onChange:e=>w({duration:parseInt(e)||10}),min:1,max:40320,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Reason"}),(0,s.jsx)(a.Input,{value:g.reason||"",onChange:e=>w({reason:e.target.value}),placeholder:"Timeout for spam",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})]}),"addReaction"===g.actionType&&(0,s.jsx)(a.VStack,{spacing:4,align:"stretch",children:(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Reaction (emoji)"}),(0,s.jsx)(a.Input,{value:g.reaction||"",onChange:e=>w({reaction:e.target.value}),placeholder:"\uD83D\uDC4D or :thumbsup:",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]})}),"createChannel"===g.actionType&&(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Channel Name"}),(0,s.jsx)(a.Input,{value:g.channelName||"",onChange:e=>w({channelName:e.target.value}),placeholder:"new-channel",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Channel Type"}),(0,s.jsxs)(a.Select,{value:g.channelType||"text",onChange:e=>w({channelType:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:[(0,s.jsx)("option",{value:"text",children:"Text Channel"}),(0,s.jsx)("option",{value:"voice",children:"Voice Channel"}),(0,s.jsx)("option",{value:"category",children:"Category"})]})]})]})]})}),(0,s.jsx)(a.Button,{colorScheme:"purple",onClick:()=>{e.actionType=g.actionType,e.message=g.message,e.channel=g.channel,e.role=g.role,e.user=g.user,e.embed=g.embed,e.reason=g.reason,e.duration=g.duration,e.deleteMessages=g.deleteMessages,e.reaction=g.reaction,e.channelName=g.channelName,e.channelType=g.channelType,e.label=g.actionType?A(g.actionType):"Action",p()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});m.displayName="ActionNode";let u=m;t()}catch(e){t(e)}})},7910:e=>{"use strict";e.exports=require("stream")},8077:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(o),r.d(o,{default:()=>h});var s=r(8732),n=r(4722),i=r(4959),a=r.n(i),l=r(3001),c=e([l]);function d({Component:e,pageProps:o}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a(),{children:[(0,s.jsx)("title",{children:"404 Bot Dashboard"}),(0,s.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,s.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,s.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,s.jsx)("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,s.jsx)(e,{...o})]})}function h({Component:e,pageProps:{session:o,...r}}){return(0,s.jsx)(n.SessionProvider,{session:o,children:(0,s.jsx)(l.NP,{children:(0,s.jsx)(d,{Component:e,pageProps:r})})})}l=(c.then?(await c)():c)[0],t()}catch(e){t(e)}})},8280:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.r(o),r.d(o,{default:()=>k});var s=r(8732),n=r(2015),i=r.n(n),a=r(9733),l=r(8079),c=r(4722),d=r(8358),h=r(3001),x=r(6909);r(9236);var m=r(4235),u=r(318),p=r(7756),g=r(303),b=r(8490),j=r(8515),f=e([a,h,x,m,u,p,g,b,j]);[a,h,x,m,u,p,g,b,j]=f.then?(await f)():f;let S={gradient:({id:e,sourceX:o,sourceY:r,targetX:t,targetY:n,sourcePosition:i,targetPosition:a,style:l,markerEnd:c,data:d,animated:h})=>{let m=`gradient-${e}`,[u]=(0,x.getSmoothStepPath)({sourceX:o,sourceY:r,sourcePosition:i,targetX:t,targetY:n,targetPosition:a,borderRadius:20});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("defs",{children:(0,s.jsxs)("linearGradient",{id:m,x1:o,y1:r,x2:t,y2:n,gradientUnits:"userSpaceOnUse",children:[(0,s.jsx)("stop",{offset:"0%",stopColor:d?.sourceColor||"#6b7280"}),(0,s.jsx)("stop",{offset:"100%",stopColor:d?.targetColor||"#6b7280"})]})}),(0,s.jsx)("path",{d:u,stroke:`url(#${m})`,strokeWidth:2,fill:"none",markerEnd:c,style:l,className:h?"react-flow__edge-path-animated":""})]})}},v=e=>i().useMemo(()=>({trigger:e?.colors?.primary||"#6b7280",command:"#3b82f6",event:"#10b981",action:"#a855f7",apiRequest:"#06b6d4",condition:"#f59e0b"}),[e]),y=[{id:"1",type:"trigger",position:{x:100,y:200},data:{label:"Start Here"}}],C=[],k=()=>{let{data:e}=(0,c.useSession)(),o=(0,d.useRouter)(),{currentScheme:r}=(0,h.DP)(),t=v(r),[f,k,T]=(0,x.useNodesState)(y),[w,A,D]=(0,x.useEdgesState)(C),[z,I]=(0,n.useState)(null),[B,M]=(0,n.useState)(!1),R=(0,n.useRef)(null),{isOpen:F,onOpen:E,onClose:H}=(0,a.useDisclosure)(),{isOpen:L,onOpen:N,onClose:O}=(0,a.useDisclosure)(),{isOpen:P,onOpen:W,onClose:V}=(0,a.useDisclosure)(),{isOpen:U,onOpen:_,onClose:q}=(0,a.useDisclosure)(),{isOpen:G,onOpen:$,onClose:J}=(0,a.useDisclosure)(),{isOpen:Y,onOpen:X,onClose:K}=(0,a.useDisclosure)(),[Z,Q]=(0,n.useState)([]),[ee,eo]=(0,n.useState)([]),[er,et]=(0,n.useState)([]),[es,en]=(0,n.useState)({name:"",description:"",author:"",version:"1.0.0",tags:[],isPublic:!1}),[ei,ea]=(0,n.useState)(""),[el,ec]=(0,n.useState)(null),[ed,eh]=(0,n.useState)(""),[ex,em]=(0,n.useState)(""),eu=(0,n.useRef)(null),ep=(0,a.useToast)(),eg=e?.user?.id==="933023999770918932";if(i().useEffect(()=>{e&&!eg&&o.push("/")},[e,eg,o]),e&&!eg)return null;let eb=(0,n.useCallback)((e,o)=>{k(r=>r.map(r=>r.id===e?{...r,data:{...r.data,...o}}:r))},[k]),ej=(0,n.useMemo)(()=>({command:e=>(0,s.jsx)(m.A,{...e,updateNodeData:eb}),event:e=>(0,s.jsx)(u.A,{...e,updateNodeData:eb}),action:e=>(0,s.jsx)(p.A,{...e,updateNodeData:eb}),condition:e=>(0,s.jsx)(g.A,{...e,updateNodeData:eb}),trigger:b.A,apiRequest:e=>(0,s.jsx)(j.A,{...e,updateNodeData:eb})}),[eb]),ef=(0,n.useCallback)(e=>{let o=f.find(o=>o.id===e.source),r=f.find(o=>o.id===e.target);if(!o||!r)return;if("trigger"===o.type){let o=w.filter(o=>o.source===e.source);if(o.length>0){let e=f.find(e=>e.id===o[0].target)?.type;if(e&&e!==r.type)return void alert("❌ Flow Rule Violation!\n\nYou can only connect Start to EITHER:\n• Command (for slash commands)\n• Event (for automatic triggers)\n\nNot both! Please choose one path.")}if("command"!==r.type&&"event"!==r.type)return void alert("❌ Invalid Connection!\n\nStart can only connect to:\n• Command blocks\n• Event blocks")}if("action"===o.type)return void alert("❌ Invalid Connection!\n\nAction blocks should be at the end of your flow.\nThey cannot connect to other blocks.");let s=t[o.type]||"#6b7280",n=t[r.type]||"#6b7280",i={...e,type:"gradient",animated:!0,markerEnd:{type:x.MarkerType.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:s,targetColor:n}};A(e=>(0,x.addEdge)(i,e))},[A,f,w]),eS=(0,n.useCallback)(e=>{e.preventDefault(),e.dataTransfer.dropEffect="move"},[]),ev=(0,n.useCallback)(e=>{e.preventDefault();let o=R.current?.getBoundingClientRect(),r=e.dataTransfer.getData("application/reactflow");if(void 0===r||!r||!o)return;let t=z?.project({x:e.clientX-o.left,y:e.clientY-o.top}),s={id:`${r}-${Date.now()}`,type:r,position:t||{x:0,y:0},data:{label:`New ${r}`}};k(e=>e.concat(s))},[z,k]),ey=async e=>{try{let o={name:e.name,description:e.description,author:e.author,version:e.version,nodes:f,edges:w},r=await fetch("/api/admin/experimental/addon-builder/build",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(o)});if(r.ok)ep({title:"Addon Built Successfully!",description:"Your addon has been built and is now available in the bot.",status:"success",duration:5e3,isClosable:!0}),eC();else{let e=await r.json();if(e.errors&&Array.isArray(e.errors)){let o=e.errors.map((e,o)=>`${o+1}. ${e}`).join("\n");ep({title:"Configuration Issues Found",description:`Please fix these issues:

${o}`,status:"warning",duration:1e4,isClosable:!0})}else ep({title:"Build Failed",description:e.details||e.message||"Failed to build addon. Please check your flow and try again.",status:"error",duration:5e3,isClosable:!0})}}catch(e){ep({title:"Build Error",description:"An error occurred while building the addon.",status:"error",duration:5e3,isClosable:!0})}finally{M(!1)}},eC=async()=>{let e=localStorage.getItem("addon-builder-saved-flows"),o=[];if(e)try{o=JSON.parse(e)}catch(e){}let r=[];try{let e=await fetch("/api/admin/addons");if(e.ok)for(let o of((await e.json()).customAddons||[]).filter(e=>e.isCustomAddon))try{let e=await fetch(`/api/admin/addons/${o.name}/flow`),t=null,s=!1;e.ok&&(t=await e.json(),s=!0),r.push({id:`built-${o.name}`,name:o.name,description:o.description||"Generated addon from visual builder",author:"Addon Builder",version:o.version||"1.0.0",tags:s?["built-addon","recoverable"]:["built-addon","template-only"],isBuilt:!0,hasOriginalFlow:s,isPublic:!1,createdAt:t?.metadata?.createdAt||new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:t?.nodes?.length||0,edgeCount:t?.edges?.length||0,nodes:t?.nodes||[],edges:t?.edges||[]})}catch(e){r.push({id:`built-${o.name}`,name:o.name,description:o.description||"Generated addon from visual builder",author:"Addon Builder",version:o.version||"1.0.0",tags:["built-addon","template-only"],isBuilt:!0,hasOriginalFlow:!1,isPublic:!1,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:0,edgeCount:0,nodes:[],edges:[]})}}catch(e){}et([...o,...r])},ek=e=>{let o=(e,o)=>e.map(e=>{let r=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(r&&s){let o=t[r.type]||"#6b7280",n=t[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:x.MarkerType.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:o,targetColor:n}}}return e});if(e.isBuilt)if(e.hasOriginalFlow&&e.nodes.length>0)k(e.nodes),A(o(e.edges,e.nodes)),ep({title:"Original Flow Recovered!",description:`"${e.name}" original flow loaded successfully. You can now edit and rebuild it.`,status:"success",duration:5e3,isClosable:!0});else{let o=[{id:"1",type:"trigger",position:{x:100,y:200},data:{label:"Start Here"}},{id:"info-note",type:"command",position:{x:300,y:200},data:{label:`${e.name} (Template)`,commandName:"example",description:"This is a basic template. The original flow data was not saved when this addon was built."}}],r=[{id:"start-to-command",source:"1",target:"info-note",type:"gradient",animated:!0,markerEnd:{type:x.MarkerType.ArrowClosed,color:t.command},style:{strokeWidth:2},data:{sourceColor:t.trigger,targetColor:t.command}}];k(o),A(r),ep({title:"Template Loaded",description:`"${e.name}" basic template loaded. Original flow data not available - this addon was built before flow recovery was implemented.`,status:"warning",duration:6e3,isClosable:!0})}else k(e.nodes),A(o(e.edges,e.nodes)),ep({title:"Flow Loaded!",description:`"${e.name}" has been loaded successfully.`,status:"success",duration:3e3,isClosable:!0});q(),setTimeout(()=>{z&&z.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100)},eT=e=>{ec(e)},ew=async()=>{if(el)if(ec(null),el.isBuilt)try{let e=await fetch(`/api/admin/addons/${el.name}`,{method:"DELETE",credentials:"include"});if(!e.ok){let o=await e.json();throw Error(o.details||o.error||"Failed to delete addon")}et(e=>e.filter(e=>e.id!==el.id)),ep({title:"Addon Deleted",description:"The built addon has been deleted successfully.",status:"success",duration:3e3,isClosable:!0})}catch(e){ep({title:"Delete Failed",description:e.message,status:"error",duration:5e3,isClosable:!0})}else{let e=localStorage.getItem("addon-builder-saved-flows");if(e)try{let o=JSON.parse(e).filter(e=>e.id!==el.id);localStorage.setItem("addon-builder-saved-flows",JSON.stringify(o)),et(e=>e.filter(e=>e.id!==el.id)),ep({title:"Flow Deleted",description:"The saved flow has been deleted successfully.",status:"info",duration:3e3,isClosable:!0})}catch(e){ep({title:"Delete Failed",description:"Failed to delete the saved flow.",status:"error",duration:3e3,isClosable:!0})}}},eA=()=>{ei.trim()&&!es.tags.includes(ei.trim())&&(en(e=>({...e,tags:[...e.tags,ei.trim()]})),ea(""))},eD=e=>{en(o=>({...o,tags:o.tags.filter(o=>o!==e)}))},ez=async()=>{try{let e=await fetch("/api/admin/experimental/addon-builder/templates");if(e.ok){let o=await e.json();Q(o.templates),eo(o.categories)}}catch(e){}},eI=e=>{k(e.nodes),A(((e,o)=>e.map(e=>{let r=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(r&&s){let o=t[r.type]||"#6b7280",n=t[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:x.MarkerType.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:o,targetColor:n}}}return e}))(e.edges,e.nodes)),O(),setTimeout(()=>{z&&z.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100),ep({title:"Template Loaded!",description:`"${e.name}" has been loaded successfully.`,status:"success",duration:3e3,isClosable:!0})},eB=e=>{let o=JSON.stringify(e),r=btoa(o),t=[],s=(e.name||"addon").toLowerCase().replace(/[^a-z0-9]/g,"");t.push(s);for(let e=0;e<r.length;e+=8)t.push(r.slice(e,e+8));return t.join("_")},eM=async e=>{try{await navigator.clipboard.writeText(e),ep({title:"Copied!",description:"Flow data copied to clipboard.",status:"success",duration:2e3,isClosable:!0})}catch(e){ep({title:"Copy Failed",description:"Failed to copy to clipboard. Please manually copy the text.",status:"error",duration:3e3,isClosable:!0})}},eR=e=>{try{let o=e.split("_");o[0];let r=o.slice(1).join(""),t=atob(r);return JSON.parse(t)}catch(e){throw Error("Invalid flow code format")}},eF=async()=>{if(!ex.trim())return void ep({title:"Import Error",description:"Please paste the flow code to import.",status:"error",duration:3e3,isClosable:!0});try{let e;if(!(e=ex.trim().startsWith("{")?JSON.parse(ex):eR(ex.trim())).nodes||!e.edges||!Array.isArray(e.nodes)||!Array.isArray(e.edges))throw Error("Invalid flow data structure. Missing nodes or edges.");if(0===e.nodes.length)throw Error("Flow appears to be empty. No nodes found.");let o=null;if(e.name&&e.isBuilt)try{let r=await fetch("/api/admin/addons");if(r.ok){let t=await r.json();o=t.customAddons?.find(o=>o.name===e.name)?"exists":"deleted"}}catch(e){}k(e.nodes),A(((e,o)=>e.map(e=>{let r=o.find(o=>o.id===e.source),s=o.find(o=>o.id===e.target);if(r&&s){let o=t[r.type]||"#6b7280",n=t[s.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:x.MarkerType.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:o,targetColor:n}}}return e}))(e.edges,e.nodes)),en({name:e.name||"Imported Flow",description:e.description||"Imported from shared flow",author:e.author||"Unknown",version:e.version||"1.0.0",tags:e.tags||[],isPublic:!1});let r=`Successfully imported "${e.name}" with ${e.nodeCount||e.nodes.length} nodes.`,s="success";"deleted"===o?(r=`Flow imported from "${e.name}" but the original addon has been deleted. You have the flow structure and can rebuild it.`,s="warning"):"exists"===o?r=`Flow imported from "${e.name}". The original addon still exists, so you can compare or make modifications.`:e.isBuilt&&(r=`Flow imported from "${e.name}" (built addon). Status of original addon unknown.`,s="warning"),ep({title:"Flow Imported!",description:r,status:s,duration:5e3,isClosable:!0}),em(""),K(),setTimeout(()=>{z&&z.fitView({padding:.2,includeHiddenNodes:!1,minZoom:.5,maxZoom:1.5})},100)}catch(e){ep({title:"Import Failed",description:`Failed to import flow: ${e.message}`,status:"error",duration:5e3,isClosable:!0})}},eE=(0,n.useCallback)(()=>{let e=f.filter(e=>e.selected);e.some(e=>"trigger"===e.type)&&1===e.length||(k(e=>e.filter(e=>!e.selected||"trigger"===e.type)),A(e=>e.filter(e=>!e.selected)))},[k,A,f]),eH=(0,n.useCallback)(e=>{let o=e.target;"INPUT"===o.tagName||"TEXTAREA"===o.tagName||o.isContentEditable||("Delete"===e.key&&(e.preventDefault(),eE()),"Backspace"===e.key&&(e.preventDefault(),e.stopPropagation()))},[eE]),eL=(0,n.useCallback)(()=>{},[]),eN=(0,n.useCallback)(()=>{},[]),eO=(0,n.useCallback)(()=>{A(e=>e.map(e=>{if("gradient"!==e.type){let o=f.find(o=>o.id===e.source),r=f.find(o=>o.id===e.target);if(o&&r){let s=t[o.type]||"#6b7280",n=t[r.type]||"#6b7280";return{...e,type:"gradient",animated:!0,markerEnd:{type:x.MarkerType.ArrowClosed,color:n},style:{strokeWidth:2},data:{sourceColor:s,targetColor:n}}}}return e}))},[f,A]);return i().useEffect(()=>{A(e=>e.map(e=>{let o=f.find(o=>o.id===e.source),r=f.find(o=>o.id===e.target);if(!o||!r)return e;let s=t[o.type]||"#6b7280",n=t[r.type]||"#6b7280";return{...e,data:{...e.data,sourceColor:s,targetColor:n}}}))},[r,f,t,A]),i().useEffect(()=>{ez(),eC()},[]),i().useEffect(()=>{eO()},[eO]),i().useEffect(()=>(document.addEventListener("keydown",eH),()=>{document.removeEventListener("keydown",eH)}),[eH]),(0,s.jsxs)(a.Box,{h:"100vh",w:"100%",bg:r.colors.background,children:[(0,s.jsxs)(a.VStack,{spacing:0,h:"full",children:[(0,s.jsx)(a.Box,{w:"full",bg:r.colors.surface,borderBottom:"1px solid",borderColor:r.colors.border,px:6,py:4,children:(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.VStack,{align:"start",spacing:1,children:[(0,s.jsxs)(a.HStack,{align:"center",spacing:3,children:[(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.kRp,{}),"aria-label":"Go Back",size:"md",colorScheme:"blue",variant:"solid",onClick:()=>o.back(),bg:"blue.500",color:"white",_hover:{bg:"blue.600"},_active:{bg:"blue.700"},border:"1px solid",borderColor:"blue.400"}),(0,s.jsx)(a.Heading,{size:"lg",color:r.colors.text,children:"\uD83C\uDFA8 Visual Addon Builder"})]}),(0,s.jsx)(a.Text,{fontSize:"sm",color:r.colors.textSecondary,children:"Build addons with drag-and-drop building blocks"})]}),(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsx)(a.Tooltip,{label:"Help & Documentation",children:(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.lrG,{}),"aria-label":"Help",size:"sm",variant:"ghost",colorScheme:"yellow",onClick:E})}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.B88,{}),size:"sm",variant:"ghost",colorScheme:"blue",onClick:()=>{eC(),_()},children:"Load"}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.Pum,{}),size:"sm",variant:"ghost",colorScheme:"purple",onClick:()=>{if(!z)return void ep({title:"Error",description:"Flow instance not available.",status:"error",duration:3e3,isClosable:!0});let o=z.toObject(),r={name:es.name||"Untitled Flow",description:es.description||"Exported flow from Visual Addon Builder",author:es.author||e?.user?.email||"Unknown",version:es.version||"1.0.0",tags:es.tags||[],exportedAt:new Date().toISOString(),exportedBy:e?.user?.email||"Unknown",nodeCount:o.nodes.length,edgeCount:o.edges.length,builderVersion:"1.0.0",nodes:o.nodes,edges:o.edges},t=eB(r);eh(t),$()},children:"Export Code"}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.a4x,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:X,children:"Import Code"}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.aze,{}),size:"sm",colorScheme:"green",isLoading:B,loadingText:"Building...",onClick:()=>{M(!0),en({name:"Custom Addon",description:"Generated addon from visual builder",author:e?.user?.name||"Addon Builder",version:"1.0.0",tags:[],isPublic:!1}),W()},children:"Build Addon"})]})]})}),(0,s.jsx)(a.Box,{flex:"1",w:"full",position:"relative",children:(0,s.jsx)(x.ReactFlowProvider,{children:(0,s.jsx)("div",{ref:R,style:{width:"100%",height:"100%"},children:(0,s.jsxs)(x.default,{nodes:f,edges:w,onNodesChange:T,onEdgesChange:D,onNodesDelete:eL,onEdgesDelete:eN,onConnect:ef,onInit:e=>{I(e)},onDrop:ev,onDragOver:eS,nodeTypes:ej,edgeTypes:S,fitView:!0,proOptions:{hideAttribution:!0},deleteKeyCode:null,multiSelectionKeyCode:null,style:{backgroundColor:r.colors.background},children:[(0,s.jsx)(x.Background,{color:r.colors.border,gap:16}),(0,s.jsx)(x.Panel,{position:"top-left",children:(0,s.jsxs)(a.VStack,{bg:r.colors.surface,border:"1px solid",borderColor:r.colors.border,borderRadius:"lg",p:4,spacing:3,align:"stretch",minW:"220px",maxH:"80vh",overflowY:"auto",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:r.colors.text,children:"\uD83C\uDFA8 Building Blocks"}),(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(a.Tooltip,{label:"Load pre-built templates",children:(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.est,{}),size:"xs",variant:"ghost",colorScheme:"teal","aria-label":"Templates",onClick:N})}),(0,s.jsx)(a.Tooltip,{label:"Hover blocks for detailed info",children:(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.lrG,{}),size:"xs",variant:"ghost","aria-label":"Help"})})]})]}),[{type:"command",label:"Command",icon:"⚡",color:"blue",description:"Slash commands users can execute\n• /ping, /ban, /kick\n• Click to configure options",rules:"Connect directly from Start (Command path)"},{type:"event",label:"Event",icon:"\uD83D\uDCE1",color:"green",description:"Discord events that trigger actions\n• Member joins/leaves\n• Message reactions\n• Voice changes",rules:"Connect directly from Start (Event path)"},{type:"action",label:"Action",icon:"\uD83C\uDFAF",color:"purple",description:"What happens when triggered\n• Send messages/embeds\n• Add/remove roles\n• Kick/ban users",rules:"Must connect to Command/Event/Condition"},{type:"apiRequest",label:"API Request",icon:"\uD83C\uDF10",color:"teal",description:"Make HTTP requests to external APIs\n• GET, POST, PUT, DELETE\n• Custom headers and body\n• Save response to variable",rules:"Can be used anywhere in your flow"},{type:"condition",label:"Condition",icon:"❓",color:"orange",description:"Check if something is true\n• User has role/permission\n• Message contains text\n• Two outputs: TRUE/FALSE",rules:"Connect TRUE/FALSE paths to different actions"}].map(e=>(0,s.jsx)(a.Tooltip,{label:(0,s.jsxs)(a.Box,{p:2,children:[(0,s.jsx)(a.Text,{fontWeight:"bold",mb:1,children:e.label}),(0,s.jsx)(a.Text,{fontSize:"xs",mb:2,whiteSpace:"pre-line",children:e.description}),(0,s.jsxs)(a.Text,{fontSize:"xs",color:"yellow.300",fontWeight:"bold",children:["\uD83D\uDCA1 ",e.rules]})]}),placement:"right",hasArrow:!0,bg:r.colors.surface,color:r.colors.text,borderRadius:"md",p:0,children:(0,s.jsx)(a.Box,{draggable:!0,onDragStart:o=>{o.dataTransfer.setData("application/reactflow",e.type),o.dataTransfer.effectAllowed="move"},bg:r.colors.background,border:"1px solid",borderColor:r.colors.border,borderRadius:"md",p:3,cursor:"grab",_hover:{bg:r.colors.surface,transform:"scale(1.02)",borderColor:r.colors.primary},_active:{cursor:"grabbing",transform:"scale(0.98)"},transition:"all 0.2s",children:(0,s.jsxs)(a.VStack,{spacing:2,children:[(0,s.jsxs)(a.HStack,{spacing:2,width:"full",children:[(0,s.jsx)(a.Text,{fontSize:"lg",children:e.icon}),(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"medium",color:r.colors.text,children:e.label})]}),(0,s.jsx)(a.Badge,{size:"sm",colorScheme:e.color,width:"full",children:"Drag to canvas"})]})})},e.type))]})})]})})})})]}),(0,s.jsxs)(a.Modal,{isOpen:F,onClose:H,size:"xl",children:[(0,s.jsx)(a.ModalOverlay,{}),(0,s.jsxs)(a.ModalContent,{bg:r.colors.background,children:[(0,s.jsx)(a.ModalHeader,{color:r.colors.text,children:"\uD83C\uDFA8 How to Use the Visual Addon Builder"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:4,align:"start",children:[(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(a.Text,{fontWeight:"bold",children:"Welcome to the Visual Addon Builder!"}),(0,s.jsx)(a.Text,{fontSize:"sm",children:"Create Discord bot addons using intuitive drag-and-drop building blocks."})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontWeight:"bold",mb:2,color:r.colors.text,children:"\uD83D\uDD27 Building Blocks:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsxs)(a.Text,{fontSize:"sm",children:["⚡ ",(0,s.jsx)("strong",{children:"Command"})," - Slash commands users can execute"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["\uD83D\uDCE1 ",(0,s.jsx)("strong",{children:"Event"})," - Discord events like messages or reactions"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["\uD83C\uDFAF ",(0,s.jsx)("strong",{children:"Action"})," - What happens when triggered"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["❓ ",(0,s.jsx)("strong",{children:"Condition"})," - Check if something is true"]})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontWeight:"bold",mb:2,color:r.colors.text,children:"\uD83C\uDFAE How to Create Flows:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:2,pl:4,children:[(0,s.jsxs)(a.Text,{fontSize:"sm",children:["1. ",(0,s.jsx)("strong",{children:"Start:"}),' Every flow begins with the "Start Here" trigger']}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["2. ",(0,s.jsx)("strong",{children:"Add Logic:"})," Drag Command or Event blocks to define when things happen"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["3. ",(0,s.jsx)("strong",{children:"Connect:"})," Drag from the bottom of one block to the top of another"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["4. ",(0,s.jsx)("strong",{children:"Configure:"})," Click the ⚙️ icon to set up each block"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["5. ",(0,s.jsx)("strong",{children:"Add Actions:"})," End with Action blocks to define what happens"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["6. ",(0,s.jsx)("strong",{children:"Save & Build:"}),' Save your work and click "Build Addon"']})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontWeight:"bold",mb:2,color:r.colors.text,children:"\uD83D\uDD17 Connection Rules:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsxs)(a.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Choose ONE path:"})," Either Command OR Event from Start"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Commands:"})," For slash commands (/ping, /ban)"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Events:"})," For automatic triggers (joins, reactions)"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Conditions:"})," Have TWO outputs (True/False)"]}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["• ",(0,s.jsx)("strong",{children:"Actions:"})," Should be at the end of each path"]})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontWeight:"bold",mb:2,color:r.colors.text,children:"\uD83D\uDDD1️ Deleting Blocks:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:"1. Click a block to select it (it will highlight)"}),(0,s.jsxs)(a.Text,{fontSize:"sm",children:["2. Press ",(0,s.jsx)("strong",{children:"Delete"})," key (not Backspace)"]}),(0,s.jsx)(a.Text,{fontSize:"sm",children:"3. The block and its connections will be removed"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:"green.400",children:"✅ Backspace works in text fields for typing"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:"yellow.400",children:"\uD83D\uDCA1 You cannot delete the Start block"})]})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontWeight:"bold",mb:2,color:r.colors.text,children:"\uD83D\uDCA1 Pro Tips:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,pl:4,children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:"• Use Templates for quick starts"}),(0,s.jsx)(a.Text,{fontSize:"sm",children:"• Hover over blocks in the palette for help"}),(0,s.jsx)(a.Text,{fontSize:"sm",children:"• Conditions let you create smart logic"}),(0,s.jsx)(a.Text,{fontSize:"sm",children:"• Always test with simple flows first"})]})]})]})})]})]}),(0,s.jsxs)(a.Modal,{isOpen:L,onClose:O,size:"4xl",children:[(0,s.jsx)(a.ModalOverlay,{}),(0,s.jsxs)(a.ModalContent,{bg:r.colors.background,children:[(0,s.jsx)(a.ModalHeader,{color:r.colors.text,children:"\uD83C\uDFA8 Choose a Template"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"sm",color:r.colors.textSecondary,children:"Start with a pre-built template to create your addon faster"}),(0,s.jsx)(a.Box,{maxH:"500px",overflowY:"auto",pr:2,children:(0,s.jsx)(a.VStack,{spacing:6,align:"stretch",children:ee.map(e=>(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:r.colors.text,mb:4,children:e}),(0,s.jsx)(a.Box,{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(300px, 1fr))",gap:4,children:Z.filter(o=>o.category===e).map(e=>(0,s.jsx)(a.Box,{bg:r.colors.background,border:"1px solid",borderColor:r.colors.border,borderRadius:"lg",p:4,cursor:"pointer",position:"relative",_hover:{bg:r.colors.surface,borderColor:r.colors.primary,transform:"translateY(-2px)",boxShadow:"lg"},transition:"all 0.2s",onClick:()=>eI(e),children:(0,s.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,s.jsx)(a.HStack,{justify:"space-between",align:"start",children:(0,s.jsxs)(a.HStack,{spacing:3,children:[(0,s.jsx)(a.Box,{bg:r.colors.primary,color:"white",borderRadius:"lg",p:2,fontSize:"xl",children:e.thumbnail}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:r.colors.text,children:e.name}),(0,s.jsxs)(a.Badge,{colorScheme:"blue",size:"sm",children:[e.nodes.length," blocks"]})]})]})}),(0,s.jsx)(a.Text,{fontSize:"sm",color:r.colors.textSecondary,lineHeight:"1.4",children:e.description}),(0,s.jsx)(a.Button,{size:"sm",colorScheme:"blue",variant:"ghost",width:"full",onClick:o=>{o.stopPropagation(),eI(e)},children:"Use Template"})]})},e.id))})]},e))})})]})})]})]}),(0,s.jsxs)(a.Modal,{isOpen:P,onClose:V,size:"lg",children:[(0,s.jsx)(a.ModalOverlay,{}),(0,s.jsxs)(a.ModalContent,{bg:r.colors.background,children:[(0,s.jsx)(a.ModalHeader,{color:r.colors.text,children:"\uD83D\uDCBE Save Addon Flow"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"Save your addon flow with metadata for easy organization and sharing."})]}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{color:r.colors.text,children:"Name"}),(0,s.jsx)(a.Input,{value:es.name,onChange:e=>en(o=>({...o,name:e.target.value})),placeholder:"My Awesome Addon",bg:r.colors.background,color:r.colors.text,borderColor:r.colors.border})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:r.colors.text,children:"Version"}),(0,s.jsx)(a.Input,{value:es.version,onChange:e=>en(o=>({...o,version:e.target.value})),placeholder:"1.0.0",bg:r.colors.background,color:r.colors.text,borderColor:r.colors.border})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:r.colors.text,children:"Author"}),(0,s.jsx)(a.Input,{value:es.author,onChange:e=>en(o=>({...o,author:e.target.value})),placeholder:"Your Name",bg:r.colors.background,color:r.colors.text,borderColor:r.colors.border})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:r.colors.text,children:"Description"}),(0,s.jsx)(a.Textarea,{value:es.description,onChange:e=>en(o=>({...o,description:e.target.value})),placeholder:"Describe what your addon does...",bg:r.colors.background,color:r.colors.text,borderColor:r.colors.border,rows:3})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:r.colors.text,children:"Tags"}),(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsx)(a.Input,{value:ei,onChange:e=>ea(e.target.value),placeholder:"Add tags (press Enter)",onKeyDown:e=>"Enter"===e.key&&eA(),bg:r.colors.background,color:r.colors.text,borderColor:r.colors.border}),(0,s.jsx)(a.Button,{size:"sm",onClick:eA,children:"Add"})]}),(0,s.jsx)(a.Flex,{wrap:"wrap",gap:2,mt:2,children:es.tags.map(e=>(0,s.jsxs)(a.Tag,{size:"sm",colorScheme:"blue",variant:"solid",children:[(0,s.jsx)(a.TagLabel,{children:e}),(0,s.jsx)(a.TagCloseButton,{onClick:()=>eD(e)})]},e))})]}),(0,s.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"If an addon with the same name exists, it will be overwritten."})]})]})}),(0,s.jsxs)(a.ModalFooter,{children:[(0,s.jsx)(a.Button,{variant:"ghost",mr:3,onClick:V,children:"Cancel"}),(0,s.jsx)(a.Button,{colorScheme:"blue",onClick:()=>{if(!es.name.trim())return void ep({title:"Name Required",description:"Please enter a name for your addon.",status:"error",duration:3e3,isClosable:!0});if(B){ey(es),V();return}if(!z)return void ep({title:"Error",description:"Flow instance not available.",status:"error",duration:3e3,isClosable:!0});let e=z.toObject(),o={id:Date.now().toString(),name:es.name,description:es.description,author:es.author,version:es.version,tags:es.tags,isPublic:es.isPublic,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),nodeCount:e.nodes.length,edgeCount:e.edges.length,nodes:e.nodes,edges:e.edges},r=localStorage.getItem("addon-builder-saved-flows"),t=[];if(r)try{t=JSON.parse(r)}catch(e){}let s=t.findIndex(e=>e.name===o.name);-1!==s?t[s]={...t[s],...o,updatedAt:new Date().toISOString()}:t.push(o),localStorage.setItem("addon-builder-saved-flows",JSON.stringify(t)),ep({title:"Flow Saved!",description:`"${o.name}" has been saved successfully.`,status:"success",duration:3e3,isClosable:!0}),V()},children:"Save Flow"})]})]})]}),(0,s.jsxs)(a.Modal,{isOpen:U,onClose:q,size:"4xl",children:[(0,s.jsx)(a.ModalOverlay,{}),(0,s.jsxs)(a.ModalContent,{bg:r.colors.background,children:[(0,s.jsx)(a.ModalHeader,{color:r.colors.text,children:"\uD83D\uDCC2 Load Addon Flow"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"sm",color:r.colors.textSecondary,children:"Choose a saved addon flow to load"}),(0,s.jsxs)(a.Text,{fontSize:"sm",color:r.colors.textSecondary,children:[er.length," saved flows"]})]}),0===er.length?(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{children:"No saved flows found. Create and save your first addon flow to see it here!"})]}):(0,s.jsx)(a.Box,{maxH:"500px",overflowY:"auto",pr:2,children:(0,s.jsx)(a.SimpleGrid,{columns:[1,2],spacing:4,children:er.map(e=>(0,s.jsxs)(a.Card,{bg:r.colors.background,borderColor:r.colors.border,children:[(0,s.jsx)(a.CardHeader,{pb:2,children:(0,s.jsxs)(a.HStack,{justify:"space-between",align:"start",children:[(0,s.jsxs)(a.VStack,{align:"start",spacing:1,children:[(0,s.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:r.colors.text,children:e.name}),(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsxs)(a.Badge,{size:"sm",colorScheme:"blue",children:["v",e.version]}),e.isBuilt?e.hasOriginalFlow?(0,s.jsx)(a.Badge,{size:"sm",colorScheme:"green",children:"✓ Recoverable"}):(0,s.jsx)(a.Badge,{size:"sm",colorScheme:"orange",children:"Template Only"}):(0,s.jsxs)(a.Badge,{size:"sm",colorScheme:"blue",children:[e.nodeCount," blocks"]})]})]}),(0,s.jsxs)(a.Menu,{children:[(0,s.jsx)(a.MenuButton,{as:a.IconButton,icon:(0,s.jsx)(l.ZZB,{}),variant:"ghost",size:"sm"}),(0,s.jsx)(a.MenuList,{bg:r.colors.surface,children:(0,s.jsx)(a.MenuItem,{icon:(0,s.jsx)(l.IXo,{}),onClick:()=>eT(e),color:"red.400",children:e.isBuilt?"Delete Addon":"Delete Flow"})})]})]})}),(0,s.jsx)(a.CardBody,{pt:0,children:(0,s.jsxs)(a.VStack,{align:"start",spacing:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",color:r.colors.textSecondary,noOfLines:2,children:e.isBuilt?e.hasOriginalFlow?`${e.description} • Original flow data available! You can recover and edit the exact flow that was used to build this addon.`:`${e.description} • This addon was built before flow recovery was implemented. Only a basic template can be provided.`:e.description||"No description available"}),(0,s.jsx)(a.HStack,{justify:"space-between",align:"center",width:"full",children:(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(l.JXP,{size:12}),(0,s.jsx)(a.Text,{fontSize:"xs",color:r.colors.textSecondary,children:e.author})]}),(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(l.Ohp,{size:12}),(0,s.jsx)(a.Text,{fontSize:"xs",color:r.colors.textSecondary,children:new Date(e.updatedAt).toLocaleDateString()})]})]})}),e.tags&&e.tags.length>0&&(0,s.jsxs)(a.Flex,{wrap:"wrap",gap:1,children:[e.tags.slice(0,3).map(e=>(0,s.jsx)(a.Tag,{size:"sm",colorScheme:"gray",variant:"outline",children:e},e)),e.tags.length>3&&(0,s.jsxs)(a.Tag,{size:"sm",colorScheme:"gray",variant:"outline",children:["+",e.tags.length-3]})]})]})}),(0,s.jsx)(a.CardFooter,{pt:0,children:(0,s.jsx)(a.Tooltip,{label:e.isBuilt?e.hasOriginalFlow?"Load the original flow - fully recoverable!":"Load a basic template - original flow not saved":"Load the exact saved flow",placement:"top",children:(0,s.jsx)(a.Button,{size:"sm",colorScheme:e.isBuilt?e.hasOriginalFlow?"green":"orange":"blue",width:"full",onClick:()=>ek(e),children:e.isBuilt?e.hasOriginalFlow?"Recover Flow":"Load Template":"Load Flow"})})})]},e.id))})})]})})]})]}),(0,s.jsxs)(a.Modal,{isOpen:G,onClose:J,size:"3xl",children:[(0,s.jsx)(a.ModalOverlay,{}),(0,s.jsxs)(a.ModalContent,{bg:r.colors.background,children:[(0,s.jsx)(a.ModalHeader,{color:r.colors.text,children:"\uD83D\uDE80 Export Addon Flow"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{children:"Share your addon flow with others! Copy the flow code below and share it anywhere. Others can import this code to reproduce your exact flow."})]}),(0,s.jsxs)(a.VStack,{align:"stretch",spacing:3,children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:r.colors.text,children:"Flow Code"}),(0,s.jsx)(a.HStack,{spacing:2,children:(0,s.jsx)(a.Button,{size:"sm",leftIcon:(0,s.jsx)(l.nxz,{}),colorScheme:"blue",onClick:()=>eM(ed),children:"Copy Code"})})]}),(0,s.jsx)(a.Box,{p:4,bg:r.colors.surface,borderColor:r.colors.border,borderWidth:"2px",borderRadius:"md",borderStyle:"dashed",children:(0,s.jsx)(a.Text,{fontFamily:"monospace",fontSize:"lg",fontWeight:"bold",color:r.colors.primary,textAlign:"center",wordBreak:"break-all",userSelect:"all",children:ed})})]}),(0,s.jsxs)(a.Box,{p:3,bg:r.colors.surface,borderRadius:"md",borderColor:r.colors.border,borderWidth:"1px",children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:r.colors.text,mb:2,children:"\uD83D\uDCCB How to Share:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",color:r.colors.textSecondary,children:[(0,s.jsx)(a.Text,{children:"• Copy the flow code and paste it in Discord, email, or any text platform"}),(0,s.jsx)(a.Text,{children:"• Much easier to share than long JSON data!"}),(0,s.jsx)(a.Text,{children:'• The recipient can use the "Import Code" button to load your flow'}),(0,s.jsx)(a.Text,{children:"• All your block configurations and connections will be preserved"})]})]})]})})]})]}),(0,s.jsxs)(a.Modal,{isOpen:Y,onClose:K,size:"2xl",children:[(0,s.jsx)(a.ModalOverlay,{}),(0,s.jsxs)(a.ModalContent,{bg:r.colors.background,children:[(0,s.jsx)(a.ModalHeader,{color:r.colors.text,children:"\uD83D\uDCE5 Import Addon Flow"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{children:"Import an addon flow from someone else! Paste the flow code below to load their flow configuration. This will replace your current flow."})]}),(0,s.jsxs)(a.VStack,{align:"stretch",spacing:3,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:r.colors.text,children:"Paste Flow Code"}),(0,s.jsx)(a.Input,{value:ex,onChange:e=>em(e.target.value),fontFamily:"monospace",fontSize:"sm",bg:r.colors.surface,borderColor:r.colors.border,placeholder:"addon_xxxxxx_xxxxxx_xxxxxx...",size:"lg"})]}),(0,s.jsxs)(a.Box,{p:3,bg:r.colors.surface,borderRadius:"md",borderColor:r.colors.border,borderWidth:"1px",children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:r.colors.text,mb:2,children:"\uD83D\uDD27 Import Instructions:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",color:r.colors.textSecondary,children:[(0,s.jsx)(a.Text,{children:"• Paste the flow code (looks like: addon_xxxxxx_xxxxxx_xxxxxx)"}),(0,s.jsx)(a.Text,{children:"• Much simpler than JSON - just a single line of code!"}),(0,s.jsx)(a.Text,{children:'• Click "Import Flow" to load the configuration'}),(0,s.jsx)(a.Text,{children:"• Your current flow will be replaced with the imported one"}),(0,s.jsx)(a.Text,{children:"• Legacy JSON format is still supported for backward compatibility"})]})]})]})}),(0,s.jsxs)(a.ModalFooter,{children:[(0,s.jsx)(a.Button,{variant:"ghost",mr:3,onClick:K,children:"Cancel"}),(0,s.jsx)(a.Button,{colorScheme:"green",onClick:eF,isDisabled:!ex.trim(),children:"Import Flow"})]})]})]}),(0,s.jsx)(a.AlertDialog,{isOpen:!!el,onClose:()=>ec(null),leastDestructiveRef:eu,children:(0,s.jsx)(a.AlertDialogOverlay,{children:(0,s.jsxs)(a.AlertDialogContent,{bg:r.colors.background,children:[(0,s.jsx)(a.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",color:r.colors.text,children:el?.isBuilt?"Delete Addon":"Delete Flow"}),(0,s.jsx)(a.AlertDialogBody,{color:r.colors.textSecondary,children:el?.isBuilt?(0,s.jsxs)(s.Fragment,{children:["Are you sure you want to delete the ",(0,s.jsxs)("strong",{children:['"',el?.name,'"']})," addon? This will permanently remove it from both the source code and the bot. This action cannot be undone."]}):(0,s.jsxs)(s.Fragment,{children:["Are you sure you want to delete the ",(0,s.jsxs)("strong",{children:['"',el?.name,'"']})," flow? This will permanently remove it from your saved flows. This action cannot be undone."]})}),(0,s.jsxs)(a.AlertDialogFooter,{children:[(0,s.jsx)(a.Button,{ref:eu,onClick:()=>ec(null),children:"Cancel"}),(0,s.jsx)(a.Button,{colorScheme:"red",onClick:ew,ml:3,children:el?.isBuilt?"Delete Addon":"Delete Flow"})]})]})})})]})};t()}catch(e){t(e)}})},8490:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>x});var s=r(8732),n=r(2015),i=r(6909),a=r(9733),l=r(8079),c=r(3001),d=e([i,a,c]);[i,a,c]=d.then?(await d)():d;let h=(0,n.memo)(({data:e,selected:o})=>{let{currentScheme:r}=(0,c.DP)();return(0,s.jsxs)(a.Box,{bg:r.colors.surface,border:`2px solid ${o?r.colors.primary:r.colors.border}`,borderRadius:"full",p:2,minW:"80px",minH:"80px",boxShadow:"lg",position:"relative",display:"flex",alignItems:"center",justifyContent:"center",_hover:{boxShadow:"xl",transform:"scale(1.05)",borderColor:r.colors.primary},transition:"all 0.2s",children:[(0,s.jsxs)(a.VStack,{spacing:1,align:"center",children:[(0,s.jsx)(a.Box,{bg:r.colors.primary,color:"white",borderRadius:"full",p:1,fontSize:"sm",boxShadow:"sm",children:(0,s.jsx)(l.aze,{})}),(0,s.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:r.colors.text,textAlign:"center",children:e.label})]}),(0,s.jsx)(i.Handle,{type:"source",position:i.Position.Bottom,style:{background:r.colors.background,border:`2px solid ${r.colors.primary}`,width:"16px",height:"16px",boxShadow:"0 2px 4px rgba(0,0,0,0.2)",bottom:"-8px",left:"50%",transform:"translateX(-50%)"}})]})});h.displayName="TriggerNode";let x=h;t()}catch(e){t(e)}})},8515:(e,o,r)=>{"use strict";r.a(e,async(e,t)=>{try{r.d(o,{A:()=>b});var s=r(8732),n=r(2015),i=r(6909),a=r(9733),l=r(8079),c=r(3001),d=e([i,a,c]);[i,a,c]=d.then?(await d)():d;let h={user:[{name:"{user.id}",description:"User ID for authentication",icon:"\uD83C\uDD94"},{name:"{user.username}",description:"Username for requests",icon:"\uD83D\uDC64"},{name:"{user.token}",description:"User auth token",icon:"\uD83D\uDD11"},{name:"{user.email}",description:"User email address",icon:"\uD83D\uDCE7"}],server:[{name:"{server.id}",description:"Server ID",icon:"\uD83C\uDFE0"},{name:"{server.name}",description:"Server name",icon:"\uD83D\uDCDD"},{name:"{server.memberCount}",description:"Member count",icon:"\uD83D\uDC65"},{name:"{server.region}",description:"Server region",icon:"\uD83C\uDF0D"}],message:[{name:"{message.id}",description:"Message ID",icon:"\uD83D\uDCAC"},{name:"{message.content}",description:"Message content",icon:"\uD83D\uDCDD"},{name:"{message.channelId}",description:"Channel ID",icon:"\uD83D\uDCFA"},{name:"{message.authorId}",description:"Author ID",icon:"\uD83D\uDC64"}],response:[{name:"{response.data}",description:"Full response data",icon:"\uD83D\uDCCA"},{name:"{response.status}",description:"HTTP status code",icon:"\uD83D\uDD22"},{name:"{response.headers}",description:"Response headers",icon:"\uD83D\uDCCB"},{name:"{response.error}",description:"Error message if failed",icon:"❌"}],time:[{name:"{time.now}",description:"Current timestamp",icon:"⏰"},{name:"{time.iso}",description:"ISO timestamp",icon:"\uD83D\uDCC5"},{name:"{time.unix}",description:"Unix timestamp",icon:"\uD83D\uDD50"}],random:[{name:"{random.uuid}",description:"Random UUID",icon:"\uD83C\uDFB2"},{name:"{random.number}",description:"Random number",icon:"\uD83D\uDD22"},{name:"{random.string}",description:"Random string",icon:"\uD83D\uDD24"}]},x=[{value:"GET",label:"GET",description:"Retrieve data from server",color:"green"},{value:"POST",label:"POST",description:"Send data to create new resource",color:"blue"},{value:"PUT",label:"PUT",description:"Update existing resource",color:"orange"},{value:"PATCH",label:"PATCH",description:"Partially update resource",color:"yellow"},{value:"DELETE",label:"DELETE",description:"Remove resource from server",color:"red"},{value:"HEAD",label:"HEAD",description:"Get headers only",color:"purple"},{value:"OPTIONS",label:"OPTIONS",description:"Get allowed methods",color:"gray"}],m=[{value:"json",label:"JSON",description:"JavaScript Object Notation"},{value:"form",label:"Form Data",description:"URL-encoded form data"},{value:"text",label:"Plain Text",description:"Raw text content"},{value:"xml",label:"XML",description:"Extensible Markup Language"}],u=[{value:"ignore",label:"Ignore Errors",description:"Continue flow on API errors"},{value:"log",label:"Log Errors",description:"Log errors but continue"},{value:"throw",label:"Throw Errors",description:"Stop flow on API errors"},{value:"retry",label:"Retry on Error",description:"Retry failed requests"}],p=[{key:"Authorization",value:"Bearer {user.token}"},{key:"Content-Type",value:"application/json"},{key:"User-Agent",value:"Discord Bot API Client"},{key:"Accept",value:"application/json"},{key:"X-API-Key",value:"{api.key}"}],g=(0,n.memo)(({data:e,selected:o,id:r,updateNodeData:t})=>{let{currentScheme:d}=(0,c.DP)(),{isOpen:g,onOpen:b,onClose:j}=(0,a.useDisclosure)(),f=(0,a.useToast)(),[S,v]=(0,n.useState)(()=>({method:"GET",headers:[],bodyType:"json",timeout:5e3,errorHandling:"log",retryCount:0,retryDelay:1e3,followRedirects:!0,validateSSL:!0,...e})),[y,C]=(0,n.useState)(!1),[k,T]=(0,n.useState)(null),[w,A]=(0,n.useState)(null),[D,z]=(0,n.useState)([]),[I,B]=(0,n.useState)(!1),M=e=>{v(o=>({...o,...e}))},R=e=>{navigator.clipboard.writeText(e),f({title:"Copied!",description:`Variable ${e} copied to clipboard`,status:"success",duration:2e3,isClosable:!0})},F=async()=>{if(!S.url){A("Please enter a URL first"),f({title:"Test Failed",description:"Please enter a URL first",status:"error",duration:3e3,isClosable:!0});return}C(!0),A(null),T(null);try{let e,o={};S.headers?.forEach(e=>{e.key&&e.value&&(o[e.key]=e.value)}),S.body&&("POST"===S.method||"PUT"===S.method||"PATCH"===S.method)&&("json"===S.bodyType?o["Content-Type"]="application/json":"form"===S.bodyType?o["Content-Type"]="application/x-www-form-urlencoded":"xml"===S.bodyType&&(o["Content-Type"]="application/xml"));let r={method:S.method||"GET",headers:o};if(S.body&&("POST"===S.method||"PUT"===S.method||"PATCH"===S.method))if("json"===S.bodyType)try{JSON.parse(S.body),r.body=S.body}catch(e){throw Error("Invalid JSON in request body")}else r.body=S.body;let t=await fetch(S.url,r),s=await t.text();try{e=JSON.parse(s)}catch(o){e=s}if(!t.ok)throw Error(`HTTP ${t.status}: ${t.statusText}`);T({status:t.status,statusText:t.statusText,headers:Object.fromEntries(t.headers.entries()),data:e});let n=E(e);z(n),f({title:"API Test Successful!",description:`Request completed with status ${t.status}`,status:"success",duration:3e3,isClosable:!0})}catch(o){let e=o instanceof Error?o.message:"Request failed";A(e),f({title:"API Test Failed",description:e,status:"error",duration:5e3,isClosable:!0})}finally{C(!1)}},E=(e,o="")=>{let r=[];return e&&"object"==typeof e&&(Array.isArray(e)?e.forEach((e,t)=>{let s=o?`${o}.${t}`:`${t}`;r.push(s),"object"==typeof e&&null!==e&&r.push(...E(e,s))}):Object.keys(e).forEach(t=>{let s=o?`${o}.${t}`:t;r.push(s),"object"==typeof e[t]&&null!==e[t]&&r.push(...E(e[t],s))})),r},H=(e,o,r)=>{let t=[...S.headers||[]];t[e][o]=r,M({headers:t})},L=e=>{let o=(S.headers||[]).filter((o,r)=>r!==e);M({headers:o})},N=e=>{let o=[...S.headers||[],e];M({headers:o})};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(a.Box,{bg:d.colors.surface,border:`2px solid ${o?"#06b6d4":d.colors.border}`,borderRadius:"md",p:2,minW:"140px",maxW:"180px",boxShadow:"sm",position:"relative",_hover:{boxShadow:"md",transform:"translateY(-1px)"},transition:"all 0.2s",children:[(0,s.jsx)(i.Handle,{type:"target",position:i.Position.Top,style:{background:"#06b6d4",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",top:"-6px",left:"50%",transform:"translateX(-50%)"}}),(0,s.jsxs)(a.VStack,{spacing:1,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(a.HStack,{spacing:1,children:[(0,s.jsx)(a.Box,{bg:"teal.500",color:"white",borderRadius:"full",p:.5,fontSize:"xs",children:(0,s.jsx)(l.VeH,{})}),(0,s.jsx)(a.Text,{fontSize:"xs",fontWeight:"bold",color:d.colors.text,children:"API Request"})]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.VSk,{}),size:"xs",variant:"ghost",onClick:b,"aria-label":"Configure API request"})]}),(0,s.jsx)(a.Box,{children:(0,s.jsxs)(a.HStack,{spacing:1,children:[S.method&&(0,s.jsx)(a.Text,{fontSize:"xs",children:(e=>{switch(e){case"GET":return"\uD83D\uDCE5";case"POST":return"\uD83D\uDCE4";case"PUT":return"\uD83D\uDD04";case"PATCH":return"✏️";case"DELETE":return"\uD83D\uDDD1️";default:return"\uD83C\uDF10"}})(S.method)}),(0,s.jsxs)(a.Text,{fontSize:"xs",color:d.colors.text,noOfLines:1,children:[S.method||"GET"," Request"]})]})}),S.url&&(0,s.jsx)(a.Box,{children:(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,noOfLines:1,children:S.url.length>25?S.url.substring(0,25)+"...":S.url})}),(0,s.jsxs)(a.HStack,{spacing:1,flexWrap:"wrap",children:[S.method&&(0,s.jsx)(a.Badge,{size:"xs",colorScheme:(e=>{let o=x.find(o=>o.value===e);return o?.color||"gray"})(S.method),children:S.method}),(S.headers?.length??0)>0&&(0,s.jsxs)(a.Badge,{size:"xs",colorScheme:"blue",children:[S.headers?.length," header",(S.headers?.length??0)!==1?"s":""]}),S.saveToVariable&&(0,s.jsx)(a.Badge,{size:"xs",colorScheme:"green",children:"Saves Data"})]})]}),(0,s.jsx)(i.Handle,{type:"source",position:i.Position.Bottom,style:{background:"#06b6d4",border:`2px solid ${d.colors.surface}`,width:"12px",height:"12px",bottom:"-6px",left:"50%",transform:"translateX(-50%)"}})]}),(0,s.jsxs)(a.Modal,{isOpen:g,onClose:()=>{t&&r&&t(r,S),j()},size:"6xl",children:[(0,s.jsx)(a.ModalOverlay,{bg:"blackAlpha.600"}),(0,s.jsxs)(a.ModalContent,{bg:d.colors.background,border:"2px solid",borderColor:"teal.400",maxW:"1400px",children:[(0,s.jsx)(a.ModalHeader,{color:d.colors.text,children:"\uD83C\uDF10 Configure API Request"}),(0,s.jsx)(a.ModalCloseButton,{}),(0,s.jsx)(a.ModalBody,{pb:6,children:(0,s.jsxs)(a.VStack,{spacing:6,align:"stretch",children:[(0,s.jsxs)(a.Box,{children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Available Variables"}),(0,s.jsxs)(a.Button,{size:"sm",variant:"ghost",leftIcon:I?(0,s.jsx)(l._NO,{}):(0,s.jsx)(l.Vap,{}),onClick:()=>B(!I),children:[I?"Hide":"Show"," Variables"]})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",mb:2,children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"\uD83D\uDCA1 Use variables in your API requests! Click any variable below to copy it. Variables are replaced with actual values when the request executes."})]}),(0,s.jsx)(a.Collapse,{in:I,animateOpacity:!0,children:(0,s.jsx)(a.Box,{bg:d.colors.surface,border:"1px solid",borderColor:d.colors.border,borderRadius:"md",p:4,mt:3,maxH:"400px",overflowY:"auto",children:(0,s.jsx)(a.Accordion,{allowMultiple:!0,children:Object.entries(h).map(([e,o])=>(0,s.jsxs)(a.AccordionItem,{border:"none",children:[(0,s.jsxs)(a.AccordionButton,{px:0,py:2,children:[(0,s.jsx)(a.Box,{flex:"1",textAlign:"left",children:(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,textTransform:"capitalize",children:[e," Variables"]})}),(0,s.jsx)(a.AccordionIcon,{})]}),(0,s.jsx)(a.AccordionPanel,{px:0,py:2,children:(0,s.jsx)(a.VStack,{spacing:2,align:"stretch",children:o.map(e=>(0,s.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>R(e.name),children:[(0,s.jsx)(a.Text,{fontSize:"sm",children:e.icon}),(0,s.jsx)(a.Code,{fontSize:"xs",colorScheme:"teal",children:e.name}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,flex:"1",children:e.description}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable",onClick:o=>{o.stopPropagation(),R(e.name)}})]},e.name))})})]},e))})})})]}),(0,s.jsx)(a.Divider,{}),(0,s.jsxs)(a.Tabs,{variant:"enclosed",colorScheme:"teal",children:[(0,s.jsxs)(a.TabList,{children:[(0,s.jsx)(a.Tab,{children:"Request"}),(0,s.jsx)(a.Tab,{children:"Headers"}),(0,s.jsx)(a.Tab,{children:"Body"}),(0,s.jsx)(a.Tab,{children:"Settings"}),(0,s.jsx)(a.Tab,{children:"Test"})]}),(0,s.jsxs)(a.TabPanels,{children:[(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.FormControl,{isRequired:!0,children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Request URL"}),(0,s.jsx)(a.Input,{value:S.url||"",onChange:e=>M({url:e.target.value}),placeholder:"https://api.example.com/data or {server.webhook.url}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"HTTP Method"}),(0,s.jsx)(a.Select,{value:S.method||"GET",onChange:e=>M({method:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:x.map(e=>(0,s.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Save Response To Variable"}),(0,s.jsx)(a.Input,{value:S.saveToVariable||"",onChange:e=>M({saveToVariable:e.target.value}),placeholder:"response_data (access with {response_data.field})",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Variable name to store the API response. Leave empty if you don't need the response."})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Description"}),(0,s.jsx)(a.Textarea,{value:S.description||"",onChange:e=>M({description:e.target.value}),placeholder:"Describe what this API request does",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"80px"})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Request Headers"}),(0,s.jsx)(a.Button,{leftIcon:(0,s.jsx)(l.GGD,{}),onClick:()=>{M({headers:[...S.headers||[],{key:"",value:""}]})},colorScheme:"teal",size:"sm",children:"Add Header"})]}),(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"Headers provide additional information about the request. Common headers are automatically set based on content type."})]}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:2,children:"Quick Add Common Headers:"}),(0,s.jsx)(a.Wrap,{spacing:2,children:p.map((e,o)=>(0,s.jsx)(a.WrapItem,{children:(0,s.jsx)(a.Button,{size:"sm",variant:"outline",onClick:()=>N(e),leftIcon:(0,s.jsx)(l.GGD,{}),children:e.key})},o))})]}),(0,s.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[S.headers?.map((e,o)=>(0,s.jsxs)(a.Box,{p:3,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",mb:2,children:[(0,s.jsxs)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:["Header ",o+1]}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.IXo,{}),size:"sm",colorScheme:"red",variant:"ghost",onClick:()=>L(o),"aria-label":"Remove header"})]}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:3,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Header Name"}),(0,s.jsx)(a.Input,{value:e.key,onChange:e=>H(o,"key",e.target.value),placeholder:"Authorization, Content-Type, etc.",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{fontSize:"sm",color:d.colors.text,children:"Header Value"}),(0,s.jsx)(a.Input,{value:e.value,onChange:e=>H(o,"value",e.target.value),placeholder:"Bearer {user.token}, application/json, etc.",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,size:"sm"})]})]})]},o)),(!S.headers||0===S.headers.length)&&(0,s.jsxs)(a.Alert,{status:"info",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{children:"No custom headers configured. Default headers will be set automatically based on request type."})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Request Body"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:d.colors.textSecondary,children:"Only used for POST, PUT, PATCH requests"})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Body Type"}),(0,s.jsx)(a.Select,{value:S.bodyType||"json",onChange:e=>M({bodyType:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:m.map(e=>(0,s.jsxs)("option",{value:e.value,children:[e.label," - ",e.description]},e.value))})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Request Body"}),(0,s.jsx)(a.Textarea,{value:S.body||"",onChange:e=>M({body:e.target.value}),placeholder:"json"===S.bodyType?'{"key": "value", "user": "{user.id}"}':"form"===S.bodyType?"key=value&user={user.id}":"Raw text content with {variables}",bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,minH:"200px",fontFamily:"monospace",fontSize:"sm"}),(0,s.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:["json"===S.bodyType&&"Must be valid JSON format","form"===S.bodyType&&"Use key=value&key2=value2 format","text"===S.bodyType&&"Plain text content"]})]}),(0,s.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"\uD83D\uDCA1 Body Examples:"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:1,fontSize:"sm",fontFamily:"monospace",children:[(0,s.jsxs)(a.Text,{children:["JSON: ",'{"message": "{message.content}", "user_id": "{user.id}"}']}),(0,s.jsxs)(a.Text,{children:["Form: user_id=","{user.id}","&message=","{message.content}"]}),(0,s.jsxs)(a.Text,{children:["Text: User ","{user.username}"," said: ","{message.content}"]})]})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Advanced Settings"}),(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Timeout (milliseconds)"}),(0,s.jsxs)(a.NumberInput,{value:S.timeout||5e3,onChange:e=>M({timeout:parseInt(e)||5e3}),min:1e3,max:6e4,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:"Maximum time to wait for response (5000ms = 5 seconds)"})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Error Handling"}),(0,s.jsx)(a.Select,{value:S.errorHandling||"log",onChange:e=>M({errorHandling:e.target.value}),bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border,children:u.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,mt:1,children:u.find(e=>e.value===S.errorHandling)?.description})]})]}),"retry"===S.errorHandling&&(0,s.jsxs)(a.SimpleGrid,{columns:2,spacing:4,children:[(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Retry Count"}),(0,s.jsxs)(a.NumberInput,{value:S.retryCount||0,onChange:e=>M({retryCount:parseInt(e)||0}),min:0,max:5,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]})]}),(0,s.jsxs)(a.FormControl,{children:[(0,s.jsx)(a.FormLabel,{color:d.colors.text,children:"Retry Delay (ms)"}),(0,s.jsxs)(a.NumberInput,{value:S.retryDelay||1e3,onChange:e=>M({retryDelay:parseInt(e)||1e3}),min:500,max:1e4,children:[(0,s.jsx)(a.NumberInputField,{bg:d.colors.background,color:d.colors.text,borderColor:d.colors.border}),(0,s.jsxs)(a.NumberInputStepper,{children:[(0,s.jsx)(a.NumberIncrementStepper,{}),(0,s.jsx)(a.NumberDecrementStepper,{})]})]})]})]}),(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:S.followRedirects,onChange:e=>M({followRedirects:e.target.checked}),colorScheme:"teal"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Follow Redirects"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Automatically follow HTTP redirects (3xx responses)"})]})]}),(0,s.jsxs)(a.HStack,{spacing:4,children:[(0,s.jsx)(a.Switch,{isChecked:S.validateSSL,onChange:e=>M({validateSSL:e.target.checked}),colorScheme:"red"}),(0,s.jsxs)(a.VStack,{align:"start",spacing:0,children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Validate SSL Certificates"}),(0,s.jsx)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:"Verify SSL certificates (disable only for testing)"})]})]})]})]})}),(0,s.jsx)(a.TabPanel,{children:(0,s.jsxs)(a.VStack,{spacing:4,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",align:"center",children:[(0,s.jsx)(a.Text,{fontSize:"lg",fontWeight:"bold",color:d.colors.text,children:"Test API Request"}),(0,s.jsx)(a.Button,{leftIcon:y?(0,s.jsx)(a.Spinner,{size:"sm"}):(0,s.jsx)(l.aze,{}),onClick:F,colorScheme:"teal",isLoading:y,loadingText:"Testing...",isDisabled:!S.url,children:"Test Request"})]}),(0,s.jsxs)(a.Alert,{status:"warning",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsx)(a.AlertDescription,{fontSize:"sm",children:"⚠️ This will make a real API request! Variables will not be replaced during testing. Make sure your URL and credentials are correct."})]}),w&&(0,s.jsxs)(a.Alert,{status:"error",borderRadius:"md",children:[(0,s.jsx)(a.AlertIcon,{}),(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",mb:1,children:"Request Failed"}),(0,s.jsx)(a.Text,{fontSize:"sm",children:w})]})]}),k&&(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"md",fontWeight:"bold",color:d.colors.text,mb:2,children:"Response:"}),(0,s.jsx)(a.Box,{p:4,bg:d.colors.surface,borderRadius:"md",border:"1px solid",borderColor:d.colors.border,maxH:"400px",overflowY:"auto",children:(0,s.jsxs)(a.VStack,{spacing:3,align:"stretch",children:[(0,s.jsxs)(a.HStack,{justify:"space-between",children:[(0,s.jsxs)(a.Badge,{colorScheme:"green",size:"lg",children:[k.status," ",k.statusText]}),(0,s.jsxs)(a.HStack,{spacing:2,children:[(0,s.jsx)(l.YrT,{color:"green"}),(0,s.jsx)(a.Text,{fontSize:"sm",color:"green.500",children:"Success"})]})]}),(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,children:"Response Data:"}),(0,s.jsx)(a.Box,{bg:d.colors.background,p:3,borderRadius:"md",fontFamily:"monospace",fontSize:"xs",overflowX:"auto",children:(0,s.jsx)("pre",{children:JSON.stringify(k.data,null,2)})}),D.length>0&&(0,s.jsxs)(a.Box,{children:[(0,s.jsx)(a.Text,{fontSize:"sm",fontWeight:"bold",color:d.colors.text,mb:2,children:"Available Response Variables:"}),(0,s.jsxs)(a.VStack,{spacing:1,align:"stretch",maxH:"150px",overflowY:"auto",children:[D.slice(0,20).map(e=>(0,s.jsxs)(a.HStack,{spacing:2,p:2,bg:d.colors.background,borderRadius:"md",cursor:"pointer",_hover:{bg:d.colors.surface},onClick:()=>R(`{response.${e}}`),children:[(0,s.jsx)(a.Code,{fontSize:"xs",colorScheme:"teal",children:`{response.${e}}`}),(0,s.jsx)(a.IconButton,{icon:(0,s.jsx)(l.nxz,{}),size:"xs",variant:"ghost","aria-label":"Copy variable"})]},e)),D.length>20&&(0,s.jsxs)(a.Text,{fontSize:"xs",color:d.colors.textSecondary,children:["...and ",D.length-20," more variables"]})]})]})]})})]})]})})]})]}),(0,s.jsx)(a.Button,{colorScheme:"teal",onClick:()=>{e.url=S.url,e.method=S.method,e.headers=S.headers,e.body=S.body,e.bodyType=S.bodyType,e.timeout=S.timeout,e.saveToVariable=S.saveToVariable,e.errorHandling=S.errorHandling,e.description=S.description,e.retryCount=S.retryCount,e.retryDelay=S.retryDelay,e.followRedirects=S.followRedirects,e.validateSSL=S.validateSSL,e.label=`${S.method||"GET"} Request`,j()},size:"lg",width:"full",children:"Save Configuration"})]})})]})]})]})});g.displayName="ApiRequestNode";let b=g;t()}catch(e){t(e)}})},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},9021:e=>{"use strict";e.exports=require("fs")},9236:()=>{},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")}};var o=require("../../../webpack-runtime.js");o.C(e);var r=e=>o(o.s=e),t=o.X(0,[8270,4874,752],()=>r(6566));module.exports=t})();