"use strict";(()=>{var e={};e.id=709,e.ids=[709],e.modules={224:e=>{e.exports=import("@discordjs/rest")},264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},3915:e=>{e.exports=import("discord-api-types/v10")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},5808:(e,t,o)=>{o.a(e,async(e,a)=>{try{o.r(t),o.d(t,{default:()=>u});var s=o(5806),n=o(8525),i=o(8580),r=o(224),l=o(3915),d=o(2518),c=e([r,l]);async function u(e,t){if("GET"!==e.method)return t.status(405).json({error:"Method not allowed"});try{if(!await (0,s.getServerSession)(e,t,n.authOptions))return t.status(401).json({error:"Unauthorized"});let o=new r.REST({version:"10"}).setToken(i.dashboardConfig.bot.token),a=i.dashboardConfig.bot.guildId,[d,c,u,b]=await Promise.all([o.get(l.Routes.guild(a)),o.get(l.Routes.guildChannels(a)),o.get(l.Routes.guildRoles(a)),o.get(l.Routes.guildMembers(a),{query:new URLSearchParams({limit:"1000"})})]),g=0;g=b.some(e=>e.presence)?b.filter(e=>e.user&&!e.user.bot&&e.presence?.status!=="offline").length:Math.ceil(.5*b.filter(e=>e.user&&!e.user.bot).length);let f=c.filter(e=>0===e.type).length,h=c.filter(e=>2===e.type).length,p=c.filter(e=>4===e.type).length,y=u.filter(e=>!e.managed&&"@everyone"!==e.name).length,v={totalMembers:d.member_count||b.length,onlineMembers:g,totalChannels:c.length,textChannels:f,voiceChannels:h,categories:p,totalRoles:y,serverBoosts:d.premium_subscription_count||0,boostLevel:d.premium_tier||0},S=await m(),x=new Date;x.setHours(0,0,0,0);let w=new Date(x.getTime()+864e5),P=await S.collection("member_logs").countDocuments({action:"join",timestamp:{$gte:x,$lt:w}}).catch(()=>0),I=await S.collection("member_logs").countDocuments({action:"leave",timestamp:{$gte:x,$lt:w}}).catch(()=>0),M=new Date;M.setDate(M.getDate()-6);let T=await S.collection("member_logs").find({timestamp:{$gte:M}}).toArray().catch(()=>[]),k=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],A={Mon:{joins:0,leaves:0},Tue:{joins:0,leaves:0},Wed:{joins:0,leaves:0},Thu:{joins:0,leaves:0},Fri:{joins:0,leaves:0},Sat:{joins:0,leaves:0},Sun:{joins:0,leaves:0}};for(let e of T){let t=k[new Date(e.timestamp).getDay()];A[t]&&("join"===e.action?A[t].joins+=1:"leave"===e.action&&(A[t].leaves+=1))}let C=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"].map(e=>({day:e,joins:A[e].joins,leaves:A[e].leaves}));v.newMembersToday=P,v.leftMembersToday=I,v.weeklyMembers=C,t.status(200).json({serverStats:v})}catch(e){t.status(500).json({error:"Failed to fetch server analytics",details:e instanceof Error?e.message:"Unknown error"})}}[r,l]=c.then?(await c)():c;let b=null,g=i.dashboardConfig.database?.url||"mongodb://localhost:27017",f=i.dashboardConfig.database?.name||"discord_bot";async function m(){return b||(b=await d.MongoClient.connect(g,{...i.dashboardConfig.database?.options||{}})),b.db(f)}a()}catch(e){a(e)}})},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>l,default:()=>d});var a=o(5542),s=o.n(a);let n=require("next-auth/providers/discord");var i=o.n(n),r=o(8580);let l={providers:[i()({clientId:r.dashboardConfig.bot.clientId,clientSecret:r.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,a=t.accessToken||null;e.user.id=o,e.user.accessToken=a;let s=!1;if(o)if((r.dashboardConfig.dashboard.admins||[]).includes(o))s=!0;else{let e=r.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&r.dashboardConfig.bot.token&&r.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${r.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${r.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();s=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),a=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:r.dashboardConfig.dashboard.session.secret||r.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},d=s()(l)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>l,default:()=>d});var a=o(9021),s=o(2115),n=o.n(s),i=o(3873);let r={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>i.resolve(process.cwd(),e)).find(e=>a.existsSync(e));if(!e){let t=i.resolve(__dirname,"../../../config.yml");a.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=a.readFileSync(e,"utf8");r=n().parse(t)}catch(e){process.exit(1)}let l={bot:{token:r.bot.token,clientId:r.bot.clientId,clientSecret:r.bot.clientSecret,guildId:r.bot.guildId,ticketCategoryId:r.bot.ticketCategoryId||null,ticketLogChannelId:r.bot.ticketLogChannelId||null,prefix:r.bot.prefix},dashboard:{admins:r.dashboard?.admins||[],adminRoleIds:r.dashboard?.adminRoleIds||[],session:{secret:r.dashboard?.session?.secret||r.bot.clientSecret}},database:{url:r.database.url,name:r.database.name,options:{maxPoolSize:r.database.options?.maxPoolSize||10,minPoolSize:r.database.options?.minPoolSize||1,maxIdleTimeMS:r.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:r.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:r.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:r.database.options?.connectTimeoutMS||1e4,retryWrites:r.database.options?.retryWrites!==!1,retryReads:r.database.options?.retryReads!==!1}}};l.bot.token||process.exit(1),l.bot.clientId&&l.bot.clientSecret||process.exit(1),l.bot.guildId||process.exit(1),l.database.url&&l.database.name||process.exit(1);let d=l},9021:e=>{e.exports=require("fs")},9259:(e,t,o)=>{o.a(e,async(e,a)=>{try{o.r(t),o.d(t,{config:()=>c,default:()=>d,routeModule:()=>u});var s=o(3433),n=o(264),i=o(584),r=o(5808),l=e([r]);r=(l.then?(await l)():l)[0];let d=(0,i.M)(r,"default"),c=(0,i.M)(r,"config"),u=new s.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/analytics/server",pathname:"/api/analytics/server",bundlePath:"",filename:""},userland:r});a()}catch(e){a(e)}})}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=9259);module.exports=o})();