exports.id=6281,exports.ids=[6281],exports.modules={48:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return a}});let n=r(2015),u=r(1777),l="function"==typeof IntersectionObserver,o=new Map,f=[];function a(e){let{rootRef:t,rootMargin:r,disabled:a}=e,i=a||!l,[c,s]=(0,n.useState)(!1),d=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{d.current=e},[]);return(0,n.useEffect)(()=>{if(l){if(i||c)return;let e=d.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:u,elements:l}=function(e){let t,r={root:e.root||null,margin:e.rootMargin||""},n=f.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=o.get(n)))return t;let u=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=u.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:u},f.push(r),o.set(r,t),t}(r);return l.set(e,t),u.observe(e),function(){if(l.delete(e),u.unobserve(e),0===l.size){u.disconnect(),o.delete(n);let e=f.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&f.splice(e,1)}}}(e,e=>e&&s(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,u.requestIdleCallback)(()=>s(!0));return()=>(0,u.cancelIdleCallback)(e)}},[i,r,t,c,d.current]),[p,c,(0,n.useCallback)(()=>{s(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5705:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6281:(e,t,r)=>{e.exports=r(8645)},8645:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return M},useLinkStatus:function(){return h}});let n=r(2742),u=r(8732),l=n._(r(2015)),o=r(8527),f=r(3283),a=r(6424),i=r(5958),c=r(553),s=r(8811),d=r(48),p=r(9703),b=r(6804),y=r(8727);function v(e,t,r,n){}function g(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}r(5705);let _=l.default.forwardRef(function(e,t){let r,n,{href:a,as:v,children:_,prefetch:j=null,passHref:h,replace:M,shallow:m,scroll:O,locale:P,onClick:x,onNavigate:C,onMouseEnter:R,onTouchStart:k,legacyBehavior:E=!1,...L}=e;r=_,E&&("string"==typeof r||"number"==typeof r)&&(r=(0,u.jsx)("a",{children:r}));let I=l.default.useContext(s.RouterContext),S=!1!==j,{href:T,as:w}=l.default.useMemo(()=>{if(!I){let e=g(a);return{href:e,as:v?g(v):e}}let[e,t]=(0,o.resolveHref)(I,a,!0);return{href:e,as:v?(0,o.resolveHref)(I,v):t||e}},[I,a,v]),D=l.default.useRef(T),A=l.default.useRef(w);E&&(n=l.default.Children.only(r));let K=E?n&&"object"==typeof n&&n.ref:t,[U,H,q]=(0,d.useIntersection)({rootMargin:"200px"}),z=l.default.useCallback(e=>{(A.current!==w||D.current!==T)&&(q(),A.current=w,D.current=T),U(e)},[w,T,q,U]),B=(0,y.useMergedRef)(z,K);l.default.useEffect(()=>{},[w,T,H,P,S,null==I?void 0:I.locale,I]);let N={ref:B,onClick(e){E||"function"!=typeof x||x(e),E&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),I&&(e.defaultPrevented||function(e,t,r,n,u,l,o,a,i){let{nodeName:c}=e.currentTarget;if(!("A"===c.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(r)){u&&(e.preventDefault(),location.replace(r));return}e.preventDefault(),(()=>{if(i){let e=!1;if(i({preventDefault:()=>{e=!0}}),e)return}let e=null==o||o;"beforePopState"in t?t[u?"replace":"push"](r,n,{shallow:l,locale:a,scroll:e}):t[u?"replace":"push"](n||r,{scroll:e})})()}}(e,I,T,w,M,m,O,P,C))},onMouseEnter(e){E||"function"!=typeof R||R(e),E&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){E||"function"!=typeof k||k(e),E&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,i.isAbsoluteUrl)(w))N.href=w;else if(!E||h||"a"===n.type&&!("href"in n.props)){let e=void 0!==P?P:null==I?void 0:I.locale;N.href=(null==I?void 0:I.isLocaleDomain)&&(0,p.getDomainLocale)(w,e,null==I?void 0:I.locales,null==I?void 0:I.domainLocales)||(0,b.addBasePath)((0,c.addLocale)(w,e,null==I?void 0:I.defaultLocale))}return E?l.default.cloneElement(n,N):(0,u.jsx)("a",{...L,...N,children:r})}),j=(0,l.createContext)({pending:!1}),h=()=>(0,l.useContext)(j),M=_;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8727:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return u}});let n=r(2015);function u(e,t){let r=(0,n.useRef)(null),u=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=u.current;t&&(u.current=null,t())}else e&&(r.current=l(e,n)),t&&(u.current=l(t,n))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9703:(e,t,r)=>{"use strict";function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(8107),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};