"use strict";exports.id=856,exports.ids=[856],exports.modules={856:(e,r,t)=>{t.a(e,async(e,n)=>{try{t.r(r),t.d(r,{default:()=>o});var s=t(8732),l=t(9733),a=t(2015),i=e([l]);l=(i.then?(await i)():i)[0];let c={GUILD_TEXT:0,GUILD_VOICE:2,GUILD_CATEGORY:4};function o({isOpen:e,onClose:r,onSuccess:t}){let n=(0,l.useToast)(),[i,o]=(0,a.useState)(!1),[d,p]=(0,a.useState)([]),[m,h]=(0,a.useState)({name:"",type:"GUILD_TEXT",parent:"",topic:"",nsfw:!1,rateLimitPerUser:0,position:0,bitrate:64e3,userLimit:0}),u=(e,r)=>{h(t=>({...t,[e]:r}))},x=async()=>{try{if(o(!0),!m.name.trim())return void n({title:"Error",description:"Channel name is required",status:"error",duration:3e3});let e=m.name.toLowerCase().replace(/\s+/g,"-"),s=c[m.type],l=await fetch("/api/discord/channels",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...m,name:e,type:s})});if(!l.ok){let e=await l.text(),r="Failed to create channel";try{let t=JSON.parse(e);r=t.message||t.error||r}catch(t){r=e}throw Error(r)}n({title:"Success",description:"Channel created successfully",status:"success",duration:3e3}),t?.(),r()}catch(e){n({title:"Error",description:e.message||"Failed to create channel",status:"error",duration:5e3})}finally{o(!1)}};return(0,s.jsxs)(l.Modal,{isOpen:e,onClose:r,size:"xl",children:[(0,s.jsx)(l.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(l.ModalContent,{bg:"gray.800",border:"1px",borderColor:"blue.500",children:[(0,s.jsx)(l.ModalHeader,{children:"Create Channel"}),(0,s.jsx)(l.ModalCloseButton,{}),(0,s.jsx)(l.ModalBody,{children:(0,s.jsxs)(l.Stack,{spacing:4,children:[(0,s.jsxs)(l.FormControl,{isRequired:!0,children:[(0,s.jsx)(l.FormLabel,{children:"Channel Name"}),(0,s.jsx)(l.Input,{placeholder:"Enter channel name",value:m.name,onChange:e=>u("name",e.target.value)}),(0,s.jsx)(l.FormHelperText,{children:"Channel name will be automatically formatted (lowercase, hyphens instead of spaces)"})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Channel Type"}),(0,s.jsxs)(l.Select,{value:m.type,onChange:e=>u("type",e.target.value),children:[(0,s.jsx)("option",{value:"GUILD_TEXT",children:"Text Channel"}),(0,s.jsx)("option",{value:"GUILD_VOICE",children:"Voice Channel"}),(0,s.jsx)("option",{value:"GUILD_CATEGORY",children:"Category"})]})]}),"GUILD_CATEGORY"!==m.type&&(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Parent Category"}),(0,s.jsxs)(l.Select,{placeholder:"Select category",value:m.parent,onChange:e=>u("parent",e.target.value),children:[(0,s.jsx)("option",{value:"",children:"None"}),(d||[]).map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),"GUILD_TEXT"===m.type&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Channel Topic"}),(0,s.jsx)(l.Input,{placeholder:"Enter channel topic",value:m.topic,onChange:e=>u("topic",e.target.value)})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Slowmode (seconds)"}),(0,s.jsxs)(l.NumberInput,{min:0,max:21600,value:m.rateLimitPerUser,onChange:e=>u("rateLimitPerUser",parseInt(e)),children:[(0,s.jsx)(l.NumberInputField,{}),(0,s.jsxs)(l.NumberInputStepper,{children:[(0,s.jsx)(l.NumberIncrementStepper,{}),(0,s.jsx)(l.NumberDecrementStepper,{})]})]}),(0,s.jsx)(l.FormHelperText,{children:"Set how long users must wait between sending messages (0 to disable)"})]}),(0,s.jsxs)(l.FormControl,{display:"flex",alignItems:"center",children:[(0,s.jsx)(l.FormLabel,{mb:"0",children:"Age-Restricted (NSFW)"}),(0,s.jsx)(l.Switch,{isChecked:m.nsfw,onChange:e=>u("nsfw",e.target.checked)})]})]}),"GUILD_VOICE"===m.type&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Bitrate (kbps)"}),(0,s.jsxs)(l.NumberInput,{min:8,max:96,value:m.bitrate/1e3,onChange:e=>u("bitrate",1e3*parseInt(e)),children:[(0,s.jsx)(l.NumberInputField,{}),(0,s.jsxs)(l.NumberInputStepper,{children:[(0,s.jsx)(l.NumberIncrementStepper,{}),(0,s.jsx)(l.NumberDecrementStepper,{})]})]})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"User Limit"}),(0,s.jsxs)(l.NumberInput,{min:0,max:99,value:m.userLimit,onChange:e=>u("userLimit",parseInt(e)),children:[(0,s.jsx)(l.NumberInputField,{}),(0,s.jsxs)(l.NumberInputStepper,{children:[(0,s.jsx)(l.NumberIncrementStepper,{}),(0,s.jsx)(l.NumberDecrementStepper,{})]})]}),(0,s.jsx)(l.FormHelperText,{children:"Set to 0 for unlimited users"})]})]}),(0,s.jsxs)(l.FormControl,{children:[(0,s.jsx)(l.FormLabel,{children:"Position"}),(0,s.jsxs)(l.NumberInput,{min:0,value:m.position,onChange:e=>u("position",parseInt(e)),children:[(0,s.jsx)(l.NumberInputField,{}),(0,s.jsxs)(l.NumberInputStepper,{children:[(0,s.jsx)(l.NumberIncrementStepper,{}),(0,s.jsx)(l.NumberDecrementStepper,{})]})]}),(0,s.jsx)(l.FormHelperText,{children:"Channel position in the list (0 = top)"})]})]})}),(0,s.jsxs)(l.ModalFooter,{children:[(0,s.jsx)(l.Button,{variant:"ghost",mr:3,onClick:r,children:"Cancel"}),(0,s.jsx)(l.Button,{colorScheme:"blue",onClick:x,isLoading:i,loadingText:"Creating...",children:"Create Channel"})]})]})]})}n()}catch(e){n(e)}})}};