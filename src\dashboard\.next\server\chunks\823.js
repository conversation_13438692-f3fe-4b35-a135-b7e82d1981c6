"use strict";exports.id=823,exports.ids=[823],exports.modules={823:(e,o,t)=>{t.a(e,async(e,r)=>{try{t.r(o),t.d(o,{default:()=>l});var a=t(8732);t(2015);var i=t(9733),n=t(8364),s=t.n(n),d=e([i]);i=(d.then?(await d)():d)[0],s()(async()=>{let e=await Promise.resolve().then(t.t.bind(t,2813,23));return await Promise.resolve().then(t.t.bind(t,4002,23)),await Promise.resolve().then(t.t.bind(t,6649,23)),await Promise.resolve().then(t.t.bind(t,9581,23)),e},{loadableGenerated:{modules:["components\\YamlEditor.tsx -> ace-builds/src-noconflict/ext-language_tools"]},ssr:!1});let l=({value:e,onChange:o,height:t="60vh"})=>(0,a.jsx)(i.Box,{borderWidth:"1px",borderColor:"purple.600",borderRadius:"md",overflow:"hidden",height:t,sx:{".ace_editor":{fontFamily:"'Fira Code', 'Consolas', 'Monaco', 'monospace' !important",fontSize:"14px !important",lineHeight:"1.6 !important"},".ace_gutter":{background:"#232323"},".ace_scroller":{backgroundColor:"#1e1e1e"}},children:!1});r()}catch(e){r(e)}})}};