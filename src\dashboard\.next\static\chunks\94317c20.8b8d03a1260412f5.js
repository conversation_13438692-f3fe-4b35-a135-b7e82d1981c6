(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[392],{97357:(e,t,i)=>{e=i.nmd(e),function(){var e,t,i=function(){return this}();i||"undefined"==typeof window||(i=window);var n=function(e,t,i){if("string"!=typeof e)return void(n.original?n.original.apply(this,arguments):(console.error("dropping module because define wasn't a string."),console.trace()));2==arguments.length&&(i=t),n.modules[e]||(n.payloads[e]=i,n.modules[e]=null)};n.modules={},n.payloads={};var o=function(e,t,i){if("string"==typeof t){var n=a(e,t);if(void 0!=n)return i&&i(),n}else if("[object Array]"===Object.prototype.toString.call(t)){for(var o=[],s=0,l=t.length;s<l;++s){var c=a(e,t[s]);if(void 0==c&&r.original)return;o.push(c)}return i&&i.apply(null,o)||!0}},r=function(e,t){var i=o("",e,t);return void 0==i&&r.original?r.original.apply(this,arguments):i},s=function(e,t){if(-1!==t.indexOf("!")){var i=t.split("!");return s(e,i[0])+"!"+s(e,i[1])}if("."==t.charAt(0))for(t=e.split("/").slice(0,-1).join("/")+"/"+t;-1!==t.indexOf(".")&&n!=t;){var n=t;t=t.replace(/\/\.\//,"/").replace(/[^\/]+\/\.\.\//,"")}return t},a=function(e,t){t=s(e,t);var i=n.modules[t];if(!i){if("function"==typeof(i=n.payloads[t])){var r={},a={id:t,uri:"",exports:r,packaged:!0};r=i(function(e,i){return o(t,e,i)},r,a)||a.exports,n.modules[t]=r,delete n.payloads[t]}i=n.modules[t]=r||i}return i};e="ace",t=i,i[e]||(i[e]={}),t=i[e],t.define&&t.define.packaged||(n.original=t.define,t.define=n,t.define.packaged=!0),t.require&&t.require.packaged||(r.original=t.require,t.require=r,t.require.packaged=!0)}(),ace.define("ace/lib/es6-shim",["require","exports","module"],function(e,t,i){function n(e,t,i){Object.defineProperty(e,t,{value:i,enumerable:!1,writable:!0,configurable:!0})}String.prototype.startsWith||n(String.prototype,"startsWith",function(e,t){return t=t||0,this.lastIndexOf(e,t)===t}),String.prototype.endsWith||n(String.prototype,"endsWith",function(e,t){(void 0===t||t>this.length)&&(t=this.length),t-=e.length;var i=this.indexOf(e,t);return -1!==i&&i===t}),String.prototype.repeat||n(String.prototype,"repeat",function(e){for(var t="",i=this;e>0;)1&e&&(t+=i),(e>>=1)&&(i+=i);return t}),String.prototype.includes||n(String.prototype,"includes",function(e,t){return -1!=this.indexOf(e,t)}),Object.assign||(Object.assign=function(e){if(null==e)throw TypeError("Cannot convert undefined or null to object");for(var t=Object(e),i=1;i<arguments.length;i++){var n=arguments[i];null!=n&&Object.keys(n).forEach(function(e){t[e]=n[e]})}return t}),Object.values||(Object.values=function(e){return Object.keys(e).map(function(t){return e[t]})}),Array.prototype.find||n(Array.prototype,"find",function(e){for(var t=this.length,i=arguments[1],n=0;n<t;n++){var o=this[n];if(e.call(i,o,n,this))return o}}),Array.prototype.findIndex||n(Array.prototype,"findIndex",function(e){for(var t=this.length,i=arguments[1],n=0;n<t;n++){var o=this[n];if(e.call(i,o,n,this))return n}}),Array.prototype.includes||n(Array.prototype,"includes",function(e,t){return -1!=this.indexOf(e,t)}),Array.prototype.fill||n(Array.prototype,"fill",function(e){for(var t=this.length>>>0,i=arguments[1],n=0|i,o=n<0?Math.max(t+n,0):Math.min(n,t),r=arguments[2],s=void 0===r?t:0|r,a=s<0?Math.max(t+s,0):Math.min(s,t);o<a;)this[o]=e,o++;return this}),Array.of||n(Array,"of",function(){return Array.prototype.slice.call(arguments)})}),ace.define("ace/lib/fixoldbrowsers",["require","exports","module","ace/lib/es6-shim"],function(e,t,i){"use strict";e("./es6-shim")}),ace.define("ace/lib/deep_copy",["require","exports","module"],function(e,t,i){t.deepCopy=function e(t){if("object"!=typeof t||!t)return t;if(Array.isArray(t)){i=[];for(var i,n=0;n<t.length;n++)i[n]=e(t[n]);return i}if("[object Object]"!==Object.prototype.toString.call(t))return t;for(var n in i={},t)i[n]=e(t[n]);return i}}),ace.define("ace/lib/lang",["require","exports","module","ace/lib/deep_copy"],function(e,t,i){"use strict";t.last=function(e){return e[e.length-1]},t.stringReverse=function(e){return e.split("").reverse().join("")},t.stringRepeat=function(e,t){for(var i="";t>0;)1&t&&(i+=e),(t>>=1)&&(e+=e);return i};var n=/^\s\s*/,o=/\s\s*$/;t.stringTrimLeft=function(e){return e.replace(n,"")},t.stringTrimRight=function(e){return e.replace(o,"")},t.copyObject=function(e){var t={};for(var i in e)t[i]=e[i];return t},t.copyArray=function(e){for(var t=[],i=0,n=e.length;i<n;i++)e[i]&&"object"==typeof e[i]?t[i]=this.copyObject(e[i]):t[i]=e[i];return t},t.deepCopy=e("./deep_copy").deepCopy,t.arrayToMap=function(e){for(var t={},i=0;i<e.length;i++)t[e[i]]=1;return t},t.createMap=function(e){var t=Object.create(null);for(var i in e)t[i]=e[i];return t},t.arrayRemove=function(e,t){for(var i=0;i<=e.length;i++)t===e[i]&&e.splice(i,1)},t.escapeRegExp=function(e){return e.replace(/([.*+?^${}()|[\]\/\\])/g,"\\$1")},t.escapeHTML=function(e){return(""+e).replace(/&/g,"&#38;").replace(/"/g,"&#34;").replace(/'/g,"&#39;").replace(/</g,"&#60;")},t.getMatchOffsets=function(e,t){var i=[];return e.replace(t,function(e){i.push({offset:arguments[arguments.length-2],length:e.length})}),i},t.deferredCall=function(e){var t=null,i=function(){t=null,e()},n=function(e){return n.cancel(),t=setTimeout(i,e||0),n};return n.schedule=n,n.call=function(){return this.cancel(),e(),n},n.cancel=function(){return clearTimeout(t),t=null,n},n.isPending=function(){return t},n},t.delayedCall=function(e,t){var i=null,n=function(){i=null,e()},o=function(e){null==i&&(i=setTimeout(n,e||t))};return o.delay=function(e){i&&clearTimeout(i),i=setTimeout(n,e||t)},o.schedule=o,o.call=function(){this.cancel(),e()},o.cancel=function(){i&&clearTimeout(i),i=null},o.isPending=function(){return i},o},t.supportsLookbehind=function(){try{RegExp("(?<=.)")}catch(e){return!1}return!0},t.skipEmptyMatch=function(e,t,i){return i&&e.codePointAt(t)>65535?2:1}}),ace.define("ace/lib/useragent",["require","exports","module"],function(e,t,i){"use strict";t.OS={LINUX:"LINUX",MAC:"MAC",WINDOWS:"WINDOWS"},t.getOS=function(){return t.isMac?t.OS.MAC:t.isLinux?t.OS.LINUX:t.OS.WINDOWS};var n="object"==typeof navigator?navigator:{},o=(/mac|win|linux/i.exec(n.platform)||["other"])[0].toLowerCase(),r=n.userAgent||"",s=n.appName||"";t.isWin="win"==o,t.isMac="mac"==o,t.isLinux="linux"==o,t.isIE="Microsoft Internet Explorer"==s||s.indexOf("MSAppHost")>=0?parseFloat((r.match(/(?:MSIE |Trident\/[0-9]+[\.0-9]+;.*rv:)([0-9]+[\.0-9]+)/)||[])[1]):parseFloat((r.match(/(?:Trident\/[0-9]+[\.0-9]+;.*rv:)([0-9]+[\.0-9]+)/)||[])[1]),t.isOldIE=t.isIE&&t.isIE<9,t.isGecko=t.isMozilla=r.match(/ Gecko\/\d+/),t.isOpera="object"==typeof opera&&"[object Opera]"==Object.prototype.toString.call(window.opera),t.isWebKit=parseFloat(r.split("WebKit/")[1])||void 0,t.isChrome=parseFloat(r.split(" Chrome/")[1])||void 0,t.isSafari=parseFloat(r.split(" Safari/")[1])&&!t.isChrome||void 0,t.isEdge=parseFloat(r.split(" Edge/")[1])||void 0,t.isAIR=r.indexOf("AdobeAIR")>=0,t.isAndroid=r.indexOf("Android")>=0,t.isChromeOS=r.indexOf(" CrOS ")>=0,t.isIOS=/iPad|iPhone|iPod/.test(r)&&!window.MSStream,t.isIOS&&(t.isMac=!0),t.isMobile=t.isIOS||t.isAndroid}),ace.define("ace/lib/dom",["require","exports","module","ace/lib/useragent"],function(e,t,i){"use strict";var n,o=e("./useragent");t.buildDom=function e(t,i,n){if("string"==typeof t&&t){var o=document.createTextNode(t);return i&&i.appendChild(o),o}if(!Array.isArray(t))return t&&t.appendChild&&i&&i.appendChild(t),t;if("string"!=typeof t[0]||!t[0]){for(var r=[],s=0;s<t.length;s++){var a=e(t[s],i,n);a&&r.push(a)}return r}var l=document.createElement(t[0]),c=t[1],h=1;c&&"object"==typeof c&&!Array.isArray(c)&&(h=2);for(var s=h;s<t.length;s++)e(t[s],l,n);return 2==h&&Object.keys(c).forEach(function(e){var t=c[e];"class"===e?l.className=Array.isArray(t)?t.join(" "):t:"function"==typeof t||"value"==e||"$"==e[0]?l[e]=t:"ref"===e?n&&(n[t]=l):"style"===e?"string"==typeof t&&(l.style.cssText=t):null!=t&&l.setAttribute(e,t)}),i&&i.appendChild(l),l},t.getDocumentHead=function(e){return e||(e=document),e.head||e.getElementsByTagName("head")[0]||e.documentElement},t.createElement=function(e,t){return document.createElementNS?document.createElementNS(t||"http://www.w3.org/1999/xhtml",e):document.createElement(e)},t.removeChildren=function(e){e.innerHTML=""},t.createTextNode=function(e,t){return(t?t.ownerDocument:document).createTextNode(e)},t.createFragment=function(e){return(e?e.ownerDocument:document).createDocumentFragment()},t.hasCssClass=function(e,t){return -1!==(e.className+"").split(/\s+/g).indexOf(t)},t.addCssClass=function(e,i){t.hasCssClass(e,i)||(e.className+=" "+i)},t.removeCssClass=function(e,t){for(var i=e.className.split(/\s+/g);;){var n=i.indexOf(t);if(-1==n)break;i.splice(n,1)}e.className=i.join(" ")},t.toggleCssClass=function(e,t){for(var i=e.className.split(/\s+/g),n=!0;;){var o=i.indexOf(t);if(-1==o)break;n=!1,i.splice(o,1)}return n&&i.push(t),e.className=i.join(" "),n},t.setCssClass=function(e,i,n){n?t.addCssClass(e,i):t.removeCssClass(e,i)},t.hasCssString=function(e,t){var i,n=0;if(i=(t=t||document).querySelectorAll("style")){for(;n<i.length;)if(i[n++].id===e)return!0}},t.removeElementById=function(e,t){(t=t||document).getElementById(e)&&t.getElementById(e).remove()};var r=[];function s(){var e=r;r=null,e&&e.forEach(function(e){a(e[0],e[1])})}function a(e,i,o){if("undefined"!=typeof document){if(r){if(o)s();else if(!1===o)return r.push([e,i])}if(!n){var a=o;o&&o.getRootNode&&(a=o.getRootNode())&&a!=o||(a=document);var l=a.ownerDocument||a;if(i&&t.hasCssString(i,a))return null;i&&(e+="\n/*# sourceURL=ace/css/"+i+" */");var c=t.createElement("style");c.appendChild(l.createTextNode(e)),i&&(c.id=i),a==l&&(a=t.getDocumentHead(l)),a.insertBefore(c,a.firstChild)}}}if(t.useStrictCSP=function(e){n=e,!1==e?s():r||(r=[])},t.importCssString=a,t.importCssStylsheet=function(e,i){t.buildDom(["link",{rel:"stylesheet",href:e}],t.getDocumentHead(i))},t.scrollbarWidth=function(e){var i=t.createElement("ace_inner");i.style.width="100%",i.style.minWidth="0px",i.style.height="200px",i.style.display="block";var n=t.createElement("ace_outer"),o=n.style;o.position="absolute",o.left="-10000px",o.overflow="hidden",o.width="200px",o.minWidth="0px",o.height="150px",o.display="block",n.appendChild(i);var r=e&&e.documentElement||document&&document.documentElement;if(!r)return 0;r.appendChild(n);var s=i.offsetWidth;o.overflow="scroll";var a=i.offsetWidth;return s===a&&(a=n.clientWidth),r.removeChild(n),s-a},t.computedStyle=function(e,t){return window.getComputedStyle(e,"")||{}},t.setStyle=function(e,t,i){e[t]!==i&&(e[t]=i)},t.HAS_CSS_ANIMATION=!1,t.HAS_CSS_TRANSFORMS=!1,t.HI_DPI=!o.isWin||"undefined"!=typeof window&&window.devicePixelRatio>=1.5,o.isChromeOS&&(t.HI_DPI=!1),"undefined"!=typeof document){var l=document.createElement("div");t.HI_DPI&&void 0!==l.style.transform&&(t.HAS_CSS_TRANSFORMS=!0),o.isEdge||void 0===l.style.animationName||(t.HAS_CSS_ANIMATION=!0),l=null}t.HAS_CSS_TRANSFORMS?t.translate=function(e,t,i){e.style.transform="translate("+Math.round(t)+"px, "+Math.round(i)+"px)"}:t.translate=function(e,t,i){e.style.top=Math.round(i)+"px",e.style.left=Math.round(t)+"px"}}),ace.define("ace/lib/net",["require","exports","module","ace/lib/dom"],function(e,t,i){"use strict";var n=e("./dom");t.get=function(e,t){var i=new XMLHttpRequest;i.open("GET",e,!0),i.onreadystatechange=function(){4===i.readyState&&t(i.responseText)},i.send(null)},t.loadScript=function(e,t){var i=n.getDocumentHead(),o=document.createElement("script");o.src=e,i.appendChild(o),o.onload=o.onreadystatechange=function(e,i){(i||!o.readyState||"loaded"==o.readyState||"complete"==o.readyState)&&(o=o.onload=o.onreadystatechange=null,i||t())}},t.qualifyURL=function(e){var t=document.createElement("a");return t.href=e,t.href}}),ace.define("ace/lib/oop",["require","exports","module"],function(e,t,i){"use strict";t.inherits=function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})},t.mixin=function(e,t){for(var i in t)e[i]=t[i];return e},t.implement=function(e,i){t.mixin(e,i)}}),ace.define("ace/lib/event_emitter",["require","exports","module"],function(e,t,i){"use strict";var n={},o=function(){this.propagationStopped=!0},r=function(){this.defaultPrevented=!0};n._emit=n._dispatchEvent=function(e,t){this._eventRegistry||(this._eventRegistry={}),this._defaultHandlers||(this._defaultHandlers={});var i=this._eventRegistry[e]||[],n=this._defaultHandlers[e];if(i.length||n){"object"==typeof t&&t||(t={}),t.type||(t.type=e),t.stopPropagation||(t.stopPropagation=o),t.preventDefault||(t.preventDefault=r),i=i.slice();for(var s=0;s<i.length&&(i[s](t,this),!t.propagationStopped);s++);if(n&&!t.defaultPrevented)return n(t,this)}},n._signal=function(e,t){var i=(this._eventRegistry||{})[e];if(i){i=i.slice();for(var n=0;n<i.length;n++)i[n](t,this)}},n.once=function(e,t){var i=this;if(this.on(e,function n(){i.off(e,n),t.apply(null,arguments)}),!t)return new Promise(function(e){t=e})},n.setDefaultHandler=function(e,t){var i=this._defaultHandlers;if(i||(i=this._defaultHandlers={_disabled_:{}}),i[e]){var n=i[e],o=i._disabled_[e];o||(i._disabled_[e]=o=[]),o.push(n);var r=o.indexOf(t);-1!=r&&o.splice(r,1)}i[e]=t},n.removeDefaultHandler=function(e,t){var i=this._defaultHandlers;if(i){var n=i._disabled_[e];if(i[e]==t)n&&this.setDefaultHandler(e,n.pop());else if(n){var o=n.indexOf(t);-1!=o&&n.splice(o,1)}}},n.on=n.addEventListener=function(e,t,i){this._eventRegistry=this._eventRegistry||{};var n=this._eventRegistry[e];return n||(n=this._eventRegistry[e]=[]),-1==n.indexOf(t)&&n[i?"unshift":"push"](t),t},n.off=n.removeListener=n.removeEventListener=function(e,t){this._eventRegistry=this._eventRegistry||{};var i=this._eventRegistry[e];if(i){var n=i.indexOf(t);-1!==n&&i.splice(n,1)}},n.removeAllListeners=function(e){e||(this._eventRegistry=this._defaultHandlers=void 0),this._eventRegistry&&(this._eventRegistry[e]=void 0),this._defaultHandlers&&(this._defaultHandlers[e]=void 0)},t.EventEmitter=n}),ace.define("ace/lib/report_error",["require","exports","module"],function(e,t,i){t.reportError=function(e,t){var i=Error(e);i.data=t,"object"==typeof console&&console.error&&console.error(i),setTimeout(function(){throw i})}}),ace.define("ace/lib/default_english_messages",["require","exports","module"],function(e,t,i){t.defaultEnglishMessages={"autocomplete.popup.aria-roledescription":"Autocomplete suggestions","autocomplete.popup.aria-label":"Autocomplete suggestions","autocomplete.popup.item.aria-roledescription":"item","autocomplete.loading":"Loading...","editor.scroller.aria-roledescription":"editor","editor.scroller.aria-label":"Editor content, press Enter to start editing, press Escape to exit","editor.gutter.aria-roledescription":"editor gutter","editor.gutter.aria-label":"Editor gutter, press Enter to interact with controls using arrow keys, press Escape to exit","error-marker.good-state":"Looks good!","prompt.recently-used":"Recently used","prompt.other-commands":"Other commands","prompt.no-matching-commands":"No matching commands","search-box.find.placeholder":"Search for","search-box.find-all.text":"All","search-box.replace.placeholder":"Replace with","search-box.replace-next.text":"Replace","search-box.replace-all.text":"All","search-box.toggle-replace.title":"Toggle Replace mode","search-box.toggle-regexp.title":"RegExp Search","search-box.toggle-case.title":"CaseSensitive Search","search-box.toggle-whole-word.title":"Whole Word Search","search-box.toggle-in-selection.title":"Search In Selection","search-box.search-counter":"$0 of $1","text-input.aria-roledescription":"editor","text-input.aria-label":"Cursor at row $0","gutter.code-folding.range.aria-label":"Toggle code folding, rows $0 through $1","gutter.code-folding.closed.aria-label":"Toggle code folding, rows $0 through $1","gutter.code-folding.open.aria-label":"Toggle code folding, row $0","gutter.code-folding.closed.title":"Unfold code","gutter.code-folding.open.title":"Fold code","gutter.annotation.aria-label.error":"Error, read annotations row $0","gutter.annotation.aria-label.warning":"Warning, read annotations row $0","gutter.annotation.aria-label.info":"Info, read annotations row $0","inline-fold.closed.title":"Unfold code","gutter-tooltip.aria-label.error.singular":"error","gutter-tooltip.aria-label.error.plural":"errors","gutter-tooltip.aria-label.warning.singular":"warning","gutter-tooltip.aria-label.warning.plural":"warnings","gutter-tooltip.aria-label.info.singular":"information message","gutter-tooltip.aria-label.info.plural":"information messages","gutter.annotation.aria-label.security":"Security finding, read annotations row $0","gutter.annotation.aria-label.hint":"Suggestion, read annotations row $0","gutter-tooltip.aria-label.security.singular":"security finding","gutter-tooltip.aria-label.security.plural":"security findings","gutter-tooltip.aria-label.hint.singular":"suggestion","gutter-tooltip.aria-label.hint.plural":"suggestions","editor.tooltip.disable-editing":"Editing is disabled"}}),ace.define("ace/lib/app_config",["require","exports","module","ace/lib/oop","ace/lib/event_emitter","ace/lib/report_error","ace/lib/default_english_messages"],function(e,t,i){var n,o,r=e("./oop"),s=e("./event_emitter").EventEmitter,a=e("./report_error").reportError,l=e("./default_english_messages").defaultEnglishMessages,c={setOptions:function(e){Object.keys(e).forEach(function(t){this.setOption(t,e[t])},this)},getOptions:function(e){var t={};if(e)Array.isArray(e)||(e=Object.keys(e));else{var i=this.$options;e=Object.keys(i).filter(function(e){return!i[e].hidden})}return e.forEach(function(e){t[e]=this.getOption(e)},this),t},setOption:function(e,t){if(this["$"+e]!==t){var i=this.$options[e];if(!i)return h('misspelled option "'+e+'"');if(i.forwardTo)return this[i.forwardTo]&&this[i.forwardTo].setOption(e,t);i.handlesSet||(this["$"+e]=t),i&&i.set&&i.set.call(this,t)}},getOption:function(e){var t=this.$options[e];return t?t.forwardTo?this[t.forwardTo]&&this[t.forwardTo].getOption(e):t&&t.get?t.get.call(this):this["$"+e]:h('misspelled option "'+e+'"')}};function h(e){"undefined"!=typeof console&&console.warn&&console.warn.apply(console,arguments)}var u=function(){function e(){this.$defaultOptions={},n=l,o="dollarSigns"}return e.prototype.defineOptions=function(e,t,i){return e.$options||(this.$defaultOptions[t]=e.$options={}),Object.keys(i).forEach(function(t){var n=i[t];"string"==typeof n&&(n={forwardTo:n}),n.name||(n.name=t),e.$options[n.name]=n,"initialValue"in n&&(e["$"+n.name]=n.initialValue)}),r.implement(e,c),this},e.prototype.resetOptions=function(e){Object.keys(e.$options).forEach(function(t){var i=e.$options[t];"value"in i&&e.setOption(t,i.value)})},e.prototype.setDefaultValue=function(e,t,i){if(!e){for(e in this.$defaultOptions)if(this.$defaultOptions[e][t])break;if(!this.$defaultOptions[e][t])return!1}var n=this.$defaultOptions[e]||(this.$defaultOptions[e]={});n[t]&&(n.forwardTo?this.setDefaultValue(n.forwardTo,t,i):n[t].value=i)},e.prototype.setDefaultValues=function(e,t){Object.keys(t).forEach(function(i){this.setDefaultValue(e,i,t[i])},this)},e.prototype.setMessages=function(e,t){n=e,t&&t.placeholders&&(o=t.placeholders)},e.prototype.nls=function(e,t,i){!n[e]&&(h("No message found for the key '"+e+"' in messages with id "+n.$id+", trying to find a translation for the default string '"+t+"'."),n[t]||h("No message found for the default string '"+t+"' in the provided messages. Falling back to the default English message."));var r=n[e]||n[t]||t;return i&&("dollarSigns"===o&&(r=r.replace(/\$(\$|[\d]+)/g,function(e,t){return"$"==t?"$":i[t]})),"curlyBrackets"===o&&(r=r.replace(/\{([^\}]+)\}/g,function(e,t){return i[t]}))),r},e}();u.prototype.warn=h,u.prototype.reportError=a,r.implement(u.prototype,s),t.AppConfig=u}),ace.define("ace/theme/textmate-css",["require","exports","module"],function(e,t,i){i.exports='.ace-tm .ace_gutter {\n  background: #f0f0f0;\n  color: #333;\n}\n\n.ace-tm .ace_print-margin {\n  width: 1px;\n  background: #e8e8e8;\n}\n\n.ace-tm .ace_fold {\n    background-color: #6B72E6;\n}\n\n.ace-tm {\n  background-color: #FFFFFF;\n  color: black;\n}\n\n.ace-tm .ace_cursor {\n  color: black;\n}\n        \n.ace-tm .ace_invisible {\n  color: rgb(191, 191, 191);\n}\n\n.ace-tm .ace_storage,\n.ace-tm .ace_keyword {\n  color: blue;\n}\n\n.ace-tm .ace_constant {\n  color: rgb(197, 6, 11);\n}\n\n.ace-tm .ace_constant.ace_buildin {\n  color: rgb(88, 72, 246);\n}\n\n.ace-tm .ace_constant.ace_language {\n  color: rgb(88, 92, 246);\n}\n\n.ace-tm .ace_constant.ace_library {\n  color: rgb(6, 150, 14);\n}\n\n.ace-tm .ace_invalid {\n  background-color: rgba(255, 0, 0, 0.1);\n  color: red;\n}\n\n.ace-tm .ace_support.ace_function {\n  color: rgb(60, 76, 114);\n}\n\n.ace-tm .ace_support.ace_constant {\n  color: rgb(6, 150, 14);\n}\n\n.ace-tm .ace_support.ace_type,\n.ace-tm .ace_support.ace_class {\n  color: rgb(109, 121, 222);\n}\n\n.ace-tm .ace_keyword.ace_operator {\n  color: rgb(104, 118, 135);\n}\n\n.ace-tm .ace_string {\n  color: rgb(3, 106, 7);\n}\n\n.ace-tm .ace_comment {\n  color: rgb(76, 136, 107);\n}\n\n.ace-tm .ace_comment.ace_doc {\n  color: rgb(0, 102, 255);\n}\n\n.ace-tm .ace_comment.ace_doc.ace_tag {\n  color: rgb(128, 159, 191);\n}\n\n.ace-tm .ace_constant.ace_numeric {\n  color: rgb(0, 0, 205);\n}\n\n.ace-tm .ace_variable {\n  color: rgb(49, 132, 149);\n}\n\n.ace-tm .ace_xml-pe {\n  color: rgb(104, 104, 91);\n}\n\n.ace-tm .ace_entity.ace_name.ace_function {\n  color: #0000A2;\n}\n\n\n.ace-tm .ace_heading {\n  color: rgb(12, 7, 255);\n}\n\n.ace-tm .ace_list {\n  color:rgb(185, 6, 144);\n}\n\n.ace-tm .ace_meta.ace_tag {\n  color:rgb(0, 22, 142);\n}\n\n.ace-tm .ace_string.ace_regex {\n  color: rgb(255, 0, 0)\n}\n\n.ace-tm .ace_marker-layer .ace_selection {\n  background: rgb(181, 213, 255);\n}\n.ace-tm.ace_multiselect .ace_selection.ace_start {\n  box-shadow: 0 0 3px 0px white;\n}\n.ace-tm .ace_marker-layer .ace_step {\n  background: rgb(252, 255, 0);\n}\n\n.ace-tm .ace_marker-layer .ace_stack {\n  background: rgb(164, 229, 101);\n}\n\n.ace-tm .ace_marker-layer .ace_bracket {\n  margin: -1px 0 0 -1px;\n  border: 1px solid rgb(192, 192, 192);\n}\n\n.ace-tm .ace_marker-layer .ace_active-line {\n  background: rgba(0, 0, 0, 0.07);\n}\n\n.ace-tm .ace_gutter-active-line {\n    background-color : #dcdcdc;\n}\n\n.ace-tm .ace_marker-layer .ace_selected-word {\n  background: rgb(250, 250, 255);\n  border: 1px solid rgb(200, 200, 250);\n}\n\n.ace-tm .ace_indent-guide {\n  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAAE0lEQVQImWP4////f4bLly//BwAmVgd1/w11/gAAAABJRU5ErkJggg==") right repeat-y;\n}\n\n.ace-tm .ace_indent-guide-active {\n  background: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAACCAYAAACZgbYnAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1MAAA6mAAADqYAAAXb5JfxUYAAAAZSURBVHjaYvj///9/hivKyv8BAAAA//8DACLqBhbvk+/eAAAAAElFTkSuQmCC") right repeat-y;\n}\n'}),ace.define("ace/theme/textmate",["require","exports","module","ace/theme/textmate-css","ace/lib/dom"],function(e,t,i){"use strict";t.isDark=!1,t.cssClass="ace-tm",t.cssText=e("./textmate-css"),t.$id="ace/theme/textmate",e("../lib/dom").importCssString(t.cssText,t.cssClass,!1)}),ace.define("ace/config",["require","exports","module","ace/lib/lang","ace/lib/net","ace/lib/dom","ace/lib/app_config","ace/theme/textmate"],function(e,t,i){var n,o=e("./lib/lang"),r=e("./lib/net"),s=e("./lib/dom");i.exports=t=new(e("./lib/app_config")).AppConfig;var a={packaged:!1,workerPath:null,modePath:null,themePath:null,basePath:"",suffix:".js",$moduleUrls:{},loadWorkerFromBlob:!0,sharedPopups:!1,useStrictCSP:null};t.get=function(e){if(!a.hasOwnProperty(e))throw Error("Unknown config key: "+e);return a[e]},t.set=function(e,t){if(a.hasOwnProperty(e))a[e]=t;else if(!1==this.setDefaultValue("",e,t))throw Error("Unknown config key: "+e);"useStrictCSP"==e&&s.useStrictCSP(t)},t.all=function(){return o.copyObject(a)},t.$modes={},t.moduleUrl=function(e,t){if(a.$moduleUrls[e])return a.$moduleUrls[e];var i=e.split("/"),n="snippets"==(t=t||i[i.length-2]||"")?"/":"-",o=i[i.length-1];if("worker"==t&&"-"==n){var r=RegExp("^"+t+"[\\-_]|[\\-_]"+t+"$","g");o=o.replace(r,"")}(!o||o==t)&&i.length>1&&(o=i[i.length-2]);var s=a[t+"Path"];return null==s?s=a.basePath:"/"==n&&(t=n=""),s&&"/"!=s.slice(-1)&&(s+="/"),s+t+n+o+this.get("suffix")},t.setModuleUrl=function(e,t){return a.$moduleUrls[e]=t},t.setLoader=function(e){n=e},t.dynamicModules=Object.create(null),t.$loading={},t.$loaded={},t.loadModule=function(i,o){if(Array.isArray(i))var s,a=i[0],c=i[1];else if("string"==typeof i)var c=i;var h=function(i){if(i&&!t.$loading[c])return o&&o(i);if(t.$loading[c]||(t.$loading[c]=[]),t.$loading[c].push(o),!(t.$loading[c].length>1)){var s=function(){var i,o;i=c,o=function(e,i){i&&(t.$loaded[c]=i),t._emit("load.module",{name:c,module:i});var n=t.$loading[c];t.$loading[c]=null,n.forEach(function(e){e&&e(i)})},"ace/theme/textmate"===i||"./theme/textmate"===i?o(null,e("./theme/textmate")):n?n(i,o):console.error("loader is not configured")};if(!t.get("packaged"))return s();r.loadScript(t.moduleUrl(c,a),s),l()}};if(t.dynamicModules[c])t.dynamicModules[c]().then(function(e){e.default?h(e.default):h(e)});else{try{s=this.$require(c)}catch(e){}h(s||t.$loaded[c])}},t.$require=function(e){if("function"==typeof i.require)return i.require(e)},t.setModuleLoader=function(e,i){t.dynamicModules[e]=i};var l=function(){a.basePath||a.workerPath||a.modePath||a.themePath||Object.keys(a.$moduleUrls).length||(console.error("Unable to infer path to ace from script src,","use ace.config.set('basePath', 'path') to enable dynamic loading of modes and themes","or with webpack use ace/webpack-resolver"),l=function(){})};t.version="1.43.1"}),ace.define("ace/loader_build",["require","exports","module","ace/lib/fixoldbrowsers","ace/config"],function(e,t,n){"use strict";e("./lib/fixoldbrowsers");var o=e("./config");o.setLoader(function(t,i){e([t],function(e){i(null,e)})});var r=function(){return this||"undefined"!=typeof window&&window}();function s(t){if(r&&r.document){o.set("packaged",t||e.packaged||n.packaged||r.define&&i.amdD.packaged);var s={},a="",l=document.currentScript||document._currentScript,c=l&&l.ownerDocument||document;l&&l.src&&(a=l.src.split(/[?#]/)[0].split("/").slice(0,-1).join("/")||"");for(var h=c.getElementsByTagName("script"),u=0;u<h.length;u++){var d=h[u],p=d.src||d.getAttribute("src");if(p){for(var g=d.attributes,f=0,m=g.length;f<m;f++){var y=g[f];0===y.name.indexOf("data-ace-")&&(s[y.name.replace(/^data-ace-/,"").replace(/-(.)/g,function(e,t){return t.toUpperCase()})]=y.value)}var v=p.match(/^(.*)\/ace([\-.]\w+)?\.js(\?|$)/);v&&(a=v[1])}}for(var w in a&&(s.base=s.base||a,s.packaged=!0),s.basePath=s.base,s.workerPath=s.workerPath||s.base,s.modePath=s.modePath||s.base,s.themePath=s.themePath||s.base,delete s.base,s)void 0!==s[w]&&o.set(w,s[w])}}n.exports=function(t){o.init=s,o.$require=e,t.require=e,t.define=i.amdD},s(!0)}),ace.define("ace/range",["require","exports","module"],function(e,t,i){"use strict";var n=function(){function e(e,t,i,n){this.start={row:e,column:t},this.end={row:i,column:n}}return e.prototype.isEqual=function(e){return this.start.row===e.start.row&&this.end.row===e.end.row&&this.start.column===e.start.column&&this.end.column===e.end.column},e.prototype.toString=function(){return"Range: ["+this.start.row+"/"+this.start.column+"] -> ["+this.end.row+"/"+this.end.column+"]"},e.prototype.contains=function(e,t){return 0==this.compare(e,t)},e.prototype.compareRange=function(e){var t,i=e.end,n=e.start;return 1==(t=this.compare(i.row,i.column))?1==(t=this.compare(n.row,n.column))?2:+(0==t):-1==t?-2:-1==(t=this.compare(n.row,n.column))?-1:42*(1==t)},e.prototype.comparePoint=function(e){return this.compare(e.row,e.column)},e.prototype.containsRange=function(e){return 0==this.comparePoint(e.start)&&0==this.comparePoint(e.end)},e.prototype.intersects=function(e){var t=this.compareRange(e);return -1==t||0==t||1==t},e.prototype.isEnd=function(e,t){return this.end.row==e&&this.end.column==t},e.prototype.isStart=function(e,t){return this.start.row==e&&this.start.column==t},e.prototype.setStart=function(e,t){"object"==typeof e?(this.start.column=e.column,this.start.row=e.row):(this.start.row=e,this.start.column=t)},e.prototype.setEnd=function(e,t){"object"==typeof e?(this.end.column=e.column,this.end.row=e.row):(this.end.row=e,this.end.column=t)},e.prototype.inside=function(e,t){if(0==this.compare(e,t)&&!(this.isEnd(e,t)||this.isStart(e,t)))return!0;return!1},e.prototype.insideStart=function(e,t){if(0==this.compare(e,t)&&!this.isEnd(e,t))return!0;return!1},e.prototype.insideEnd=function(e,t){if(0==this.compare(e,t)&&!this.isStart(e,t))return!0;return!1},e.prototype.compare=function(e,t){return this.isMultiLine()||e!==this.start.row?e<this.start.row?-1:e>this.end.row?1:this.start.row===e?t>=this.start.column?0:-1:this.end.row===e?t<=this.end.column?0:1:0:t<this.start.column?-1:+(t>this.end.column)},e.prototype.compareStart=function(e,t){return this.start.row==e&&this.start.column==t?-1:this.compare(e,t)},e.prototype.compareEnd=function(e,t){return this.end.row==e&&this.end.column==t?1:this.compare(e,t)},e.prototype.compareInside=function(e,t){return this.end.row==e&&this.end.column==t?1:this.start.row==e&&this.start.column==t?-1:this.compare(e,t)},e.prototype.clipRows=function(t,i){if(this.end.row>i)var n={row:i+1,column:0};else if(this.end.row<t)var n={row:t,column:0};if(this.start.row>i)var o={row:i+1,column:0};else if(this.start.row<t)var o={row:t,column:0};return e.fromPoints(o||this.start,n||this.end)},e.prototype.extend=function(t,i){var n=this.compare(t,i);if(0==n)return this;if(-1==n)var o={row:t,column:i};else var r={row:t,column:i};return e.fromPoints(o||this.start,r||this.end)},e.prototype.isEmpty=function(){return this.start.row===this.end.row&&this.start.column===this.end.column},e.prototype.isMultiLine=function(){return this.start.row!==this.end.row},e.prototype.clone=function(){return e.fromPoints(this.start,this.end)},e.prototype.collapseRows=function(){return 0==this.end.column?new e(this.start.row,0,Math.max(this.start.row,this.end.row-1),0):new e(this.start.row,0,this.end.row,0)},e.prototype.toScreenRange=function(t){var i=t.documentToScreenPosition(this.start),n=t.documentToScreenPosition(this.end);return new e(i.row,i.column,n.row,n.column)},e.prototype.moveBy=function(e,t){this.start.row+=e,this.start.column+=t,this.end.row+=e,this.end.column+=t},e}();n.fromPoints=function(e,t){return new n(e.row,e.column,t.row,t.column)},n.comparePoints=function(e,t){return e.row-t.row||e.column-t.column},t.Range=n}),ace.define("ace/lib/keys",["require","exports","module","ace/lib/oop"],function(e,t,i){"use strict";for(var n=e("./oop"),o={MODIFIER_KEYS:{16:"Shift",17:"Ctrl",18:"Alt",224:"Meta",91:"MetaLeft",92:"MetaRight",93:"ContextMenu"},KEY_MODS:{ctrl:1,alt:2,option:2,shift:4,super:8,meta:8,command:8,cmd:8,control:1},FUNCTION_KEYS:{8:"Backspace",9:"Tab",13:"Return",19:"Pause",27:"Esc",32:"Space",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",44:"Print",45:"Insert",46:"Delete","-13":"NumpadEnter",144:"Numlock",145:"Scrolllock"},PRINTABLE_KEYS:{32:" ",59:";",61:"=",107:"+",109:"-",110:".",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",111:"/",106:"*"}},r={Command:224,Backspace:8,Tab:9,Return:13,Enter:13,Pause:19,Escape:27,PageUp:33,PageDown:34,End:35,Home:36,Insert:45,Delete:46,ArrowLeft:37,ArrowUp:38,ArrowRight:39,ArrowDown:40,Backquote:192,Minus:189,Equal:187,BracketLeft:219,Backslash:220,BracketRight:221,Semicolon:186,Quote:222,Comma:188,Period:190,Slash:191,Space:32,NumpadAdd:107,NumpadDecimal:110,NumpadSubtract:109,NumpadDivide:111,NumpadMultiply:106},s=0;s<10;s++)r["Digit"+s]=48+s,r["Numpad"+s]=96+s,o.PRINTABLE_KEYS[48+s]=""+s,o.FUNCTION_KEYS[96+s]="Numpad"+s;for(var s=65;s<91;s++){var a=String.fromCharCode(s+32);r["Key"+a.toUpperCase()]=s,o.PRINTABLE_KEYS[s]=a}for(var s=1;s<13;s++)r["F"+s]=111+s,o.FUNCTION_KEYS[111+s]="F"+s;var l={Shift:16,Control:17,Alt:18,Meta:224};for(var c in l)r[c]=r[c+"Left"]=r[c+"Right"]=l[c];for(var h in t.$codeToKeyCode=r,o.PRINTABLE_KEYS[173]="-",o.FUNCTION_KEYS){var u=o.FUNCTION_KEYS[h].toLowerCase();o[u]=parseInt(h,10)}for(var h in o.PRINTABLE_KEYS){var u=o.PRINTABLE_KEYS[h].toLowerCase();o[u]=parseInt(h,10)}n.mixin(o,o.MODIFIER_KEYS),n.mixin(o,o.PRINTABLE_KEYS),n.mixin(o,o.FUNCTION_KEYS),o.enter=o.return,o.escape=o.esc,o.del=o.delete;for(var d=["cmd","ctrl","alt","shift"],p=Math.pow(2,d.length);p--;)o.KEY_MODS[p]=d.filter(function(e){return p&o.KEY_MODS[e]}).join("-")+"-";o.KEY_MODS[0]="",o.KEY_MODS[-1]="input-",n.mixin(t,o),t.default=t,t.keyCodeToString=function(e){var t=o[e];return"string"!=typeof t&&(t=String.fromCharCode(e)),t.toLowerCase()}}),ace.define("ace/lib/event",["require","exports","module","ace/lib/keys","ace/lib/useragent"],function(e,t,i){"use strict";var n,o=e("./keys"),r=e("./useragent"),s=null,a=0;function l(){return void 0==n&&function(){n=!1;try{document.createComment("").addEventListener("test",function(){},{get passive(){return n={passive:!1},!0}})}catch(e){}}(),n}function c(e,t,i){this.elem=e,this.type=t,this.callback=i}c.prototype.destroy=function(){u(this.elem,this.type,this.callback),this.elem=this.type=this.callback=void 0};var h=t.addListener=function(e,t,i,n){e.addEventListener(t,i,l()),n&&n.$toDestroy.push(new c(e,t,i))},u=t.removeListener=function(e,t,i){e.removeEventListener(t,i,l())};function d(e){return 0|!!e.ctrlKey|2*!!e.altKey|4*!!e.shiftKey|8*!!e.metaKey}function p(){s=Object.create(null)}if(t.stopEvent=function(e){return t.stopPropagation(e),t.preventDefault(e),!1},t.stopPropagation=function(e){e.stopPropagation&&e.stopPropagation()},t.preventDefault=function(e){e.preventDefault&&e.preventDefault()},t.getButton=function(e){return"dblclick"==e.type?0:"contextmenu"==e.type||r.isMac&&e.ctrlKey&&!e.altKey&&!e.shiftKey?2:e.button},t.capture=function(e,t,i){var n=e&&e.ownerDocument||document;function o(e){t&&t(e),i&&i(e),u(n,"mousemove",t),u(n,"mouseup",o),u(n,"dragstart",o)}return h(n,"mousemove",t),h(n,"mouseup",o),h(n,"dragstart",o),o},t.addMouseWheelListener=function(e,t,i){h(e,"wheel",function(e){var i=e.deltaX||0,n=e.deltaY||0;switch(e.deltaMode){case e.DOM_DELTA_PIXEL:e.wheelX=.15*i,e.wheelY=.15*n;break;case e.DOM_DELTA_LINE:e.wheelX=15*i,e.wheelY=15*n;break;case e.DOM_DELTA_PAGE:e.wheelX=150*i,e.wheelY=150*n}t(e)},i)},t.addMultiMouseDownListener=function(e,i,n,o,s){var a,l,c,u=0,d={2:"dblclick",3:"tripleclick",4:"quadclick"};function p(e){if(0!==t.getButton(e)?u=0:e.detail>1?++u>4&&(u=1):u=1,r.isIE){var s=Math.abs(e.clientX-a)>5||Math.abs(e.clientY-l)>5;(!c||s)&&(u=1),c&&clearTimeout(c),c=setTimeout(function(){c=null},i[u-1]||600),1==u&&(a=e.clientX,l=e.clientY)}if(e._clicks=u,n[o]("mousedown",e),u>4)u=0;else if(u>1)return n[o](d[u],e)}Array.isArray(e)||(e=[e]),e.forEach(function(e){h(e,"mousedown",p,s)})},t.getModifierString=function(e){return o.KEY_MODS[d(e)]},t.addCommandKeyListener=function(e,i,n){var l=null;h(e,"keydown",function(e){s[e.keyCode]=(s[e.keyCode]||0)+1;var t=function(e,t,i){var n=d(t);if(!i&&t.code&&(i=o.$codeToKeyCode[t.code]||i),!r.isMac&&s){if(t.getModifierState&&(t.getModifierState("OS")||t.getModifierState("Win"))&&(n|=8),s.altGr)if((3&n)==3)return;else s.altGr=0;if(18===i||17===i){var l=t.location;17===i&&1===l?1==s[i]&&(a=t.timeStamp):18===i&&3===n&&2===l&&t.timeStamp-a<50&&(s.altGr=!0)}}if(i in o.MODIFIER_KEYS&&(i=-1),n||13!==i||3!==t.location||(e(t,n,-i),!t.defaultPrevented)){if(r.isChromeOS&&8&n){if(e(t,n,i),t.defaultPrevented)return;n&=-9}return(!!n||i in o.FUNCTION_KEYS||i in o.PRINTABLE_KEYS)&&e(t,n,i)}}(i,e,e.keyCode);return l=e.defaultPrevented,t},n),h(e,"keypress",function(e){l&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey)&&(t.stopEvent(e),l=null)},n),h(e,"keyup",function(e){s[e.keyCode]=null},n),s||(p(),h(window,"focus",p))},"object"==typeof window&&window.postMessage&&!r.isOldIE){var g=1;t.nextTick=function(e,i){i=i||window;var n="zero-timeout-message-"+g++,o=function(r){r.data==n&&(t.stopPropagation(r),u(i,"message",o),e())};h(i,"message",o),i.postMessage(n,"*")}}t.$idleBlocked=!1,t.onIdle=function(e,i){return setTimeout(function i(){t.$idleBlocked?setTimeout(i,100):e()},i)},t.$idleBlockId=null,t.blockIdle=function(e){t.$idleBlockId&&clearTimeout(t.$idleBlockId),t.$idleBlocked=!0,t.$idleBlockId=setTimeout(function(){t.$idleBlocked=!1},e||100)},t.nextFrame="object"==typeof window&&(window.requestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame),t.nextFrame?t.nextFrame=t.nextFrame.bind(window):t.nextFrame=function(e){setTimeout(e,17)}}),ace.define("ace/clipboard",["require","exports","module"],function(e,t,i){"use strict";var n;i.exports={lineMode:!1,pasteCancelled:function(){return!!(n&&n>Date.now()-50)||(n=!1)},cancel:function(){n=Date.now()}}}),ace.define("ace/keyboard/textinput",["require","exports","module","ace/lib/event","ace/config","ace/lib/useragent","ace/lib/dom","ace/lib/lang","ace/clipboard","ace/lib/keys"],function(e,t,i){"use strict";var n=e("../lib/event"),o=e("../config").nls,r=e("../lib/useragent"),s=e("../lib/dom"),a=e("../lib/lang"),l=e("../clipboard"),c=r.isChrome<18,h=r.isIE,u=r.isChrome>63,d=e("../lib/keys"),p=d.KEY_MODS,g=r.isIOS,f=g?/\s/:/\n/,m=r.isMobile;t.TextInput=function(){function e(e,t){var i=this;this.host=t,this.text=s.createElement("textarea"),this.text.className="ace_text-input",this.text.setAttribute("wrap","off"),this.text.setAttribute("autocorrect","off"),this.text.setAttribute("autocapitalize","off"),this.text.setAttribute("spellcheck","false"),this.text.style.opacity="0",e.insertBefore(this.text,e.firstChild),this.copied=!1,this.pasted=!1,this.inComposition=!1,this.sendingText=!1,this.tempStyle="",m||(this.text.style.fontSize="1px"),this.commandMode=!1,this.ignoreFocusEvents=!1,this.lastValue="",this.lastSelectionStart=0,this.lastSelectionEnd=0,this.lastRestoreEnd=0,this.rowStart=Number.MAX_SAFE_INTEGER,this.rowEnd=Number.MIN_SAFE_INTEGER,this.numberOfExtraLines=0;try{this.$isFocused=document.activeElement===this.text}catch(e){}this.cancelComposition=this.cancelComposition.bind(this),this.setAriaOptions({role:"textbox"}),n.addListener(this.text,"blur",function(e){i.ignoreFocusEvents||(t.onBlur(e),i.$isFocused=!1)},t),n.addListener(this.text,"focus",function(e){if(!i.ignoreFocusEvents){if(i.$isFocused=!0,r.isEdge)try{if(!document.hasFocus())return}catch(e){}t.onFocus(e),r.isEdge?setTimeout(i.resetSelection.bind(i)):i.resetSelection()}},t),this.$focusScroll=!1,t.on("beforeEndOperation",function(){var e=t.curOp,n=e&&e.command&&e.command.name;if("insertstring"!=n){var o=n&&(e.docChanged||e.selectionChanged);i.inComposition&&o&&(i.lastValue=i.text.value="",i.onCompositionEnd()),i.resetSelection()}}),t.on("changeSelection",this.setAriaLabel.bind(this)),this.resetSelection=g?this.$resetSelectionIOS:this.$resetSelection,this.$isFocused&&t.onFocus(),this.inputHandler=null,this.afterContextMenu=!1,n.addCommandKeyListener(this.text,function(e,n,o){if(!i.inComposition)return t.onCommandKey(e,n,o)},t),n.addListener(this.text,"select",this.onSelect.bind(this),t),n.addListener(this.text,"input",this.onInput.bind(this),t),n.addListener(this.text,"cut",this.onCut.bind(this),t),n.addListener(this.text,"copy",this.onCopy.bind(this),t),n.addListener(this.text,"paste",this.onPaste.bind(this),t),"oncut"in this.text&&"oncopy"in this.text&&"onpaste"in this.text||n.addListener(e,"keydown",function(e){if((!r.isMac||e.metaKey)&&e.ctrlKey)switch(e.keyCode){case 67:i.onCopy(e);break;case 86:i.onPaste(e);break;case 88:i.onCut(e)}},t),this.syncComposition=a.delayedCall(this.onCompositionUpdate.bind(this),50).schedule.bind(null,null),n.addListener(this.text,"compositionstart",this.onCompositionStart.bind(this),t),n.addListener(this.text,"compositionupdate",this.onCompositionUpdate.bind(this),t),n.addListener(this.text,"keyup",this.onKeyup.bind(this),t),n.addListener(this.text,"keydown",this.syncComposition.bind(this),t),n.addListener(this.text,"compositionend",this.onCompositionEnd.bind(this),t),this.closeTimeout,n.addListener(this.text,"mouseup",this.$onContextMenu.bind(this),t),n.addListener(this.text,"mousedown",function(e){e.preventDefault(),i.onContextMenuClose()},t),n.addListener(t.renderer.scroller,"contextmenu",this.$onContextMenu.bind(this),t),n.addListener(this.text,"contextmenu",this.$onContextMenu.bind(this),t),g&&this.addIosSelectionHandler(e,t,this.text)}return e.prototype.addIosSelectionHandler=function(e,t,i){var n=this,o=null,r=!1;i.addEventListener("keydown",function(e){o&&clearTimeout(o),r=!0},!0),i.addEventListener("keyup",function(e){o=setTimeout(function(){r=!1},100)},!0);var s=function(e){if(document.activeElement===i&&!r&&!n.inComposition&&!t.$mouseHandler.isMousePressed&&!n.copied){var o=i.selectionStart,s=i.selectionEnd,a=null,l=0;if(0==o?a=d.up:1==o?a=d.home:s>n.lastSelectionEnd&&"\n"==n.lastValue[s]?a=d.end:o<n.lastSelectionStart&&" "==n.lastValue[o-1]?(a=d.left,l=p.option):o<n.lastSelectionStart||o==n.lastSelectionStart&&n.lastSelectionEnd!=n.lastSelectionStart&&o==s?a=d.left:s>n.lastSelectionEnd&&n.lastValue.slice(0,s).split("\n").length>2?a=d.down:s>n.lastSelectionEnd&&" "==n.lastValue[s-1]?(a=d.right,l=p.option):(s>n.lastSelectionEnd||s==n.lastSelectionEnd&&n.lastSelectionEnd!=n.lastSelectionStart&&o==s)&&(a=d.right),o!==s&&(l|=p.shift),a){if(!t.onCommandKey({},l,a)&&t.commands){a=d.keyCodeToString(a);var c=t.commands.findKeyCommand(l,a);c&&t.execCommand(c)}n.lastSelectionStart=o,n.lastSelectionEnd=s,n.resetSelection("")}}};document.addEventListener("selectionchange",s),t.on("destroy",function(){document.removeEventListener("selectionchange",s)})},e.prototype.onContextMenuClose=function(){var e=this;clearTimeout(this.closeTimeout),this.closeTimeout=setTimeout(function(){e.tempStyle&&(e.text.style.cssText=e.tempStyle,e.tempStyle=""),e.host.renderer.$isMousePressed=!1,e.host.renderer.$keepTextAreaAtCursor&&e.host.renderer.$moveTextAreaToCursor()},0)},e.prototype.$onContextMenu=function(e){this.host.textInput.onContextMenu(e),this.onContextMenuClose()},e.prototype.onKeyup=function(e){27==e.keyCode&&this.text.value.length<this.text.selectionStart&&(this.inComposition||(this.lastValue=this.text.value),this.lastSelectionStart=this.lastSelectionEnd=-1,this.resetSelection()),this.syncComposition()},e.prototype.cancelComposition=function(){this.ignoreFocusEvents=!0,this.text.blur(),this.text.focus(),this.ignoreFocusEvents=!1},e.prototype.onCompositionStart=function(e){if(!this.inComposition&&this.host.onCompositionStart&&!this.host.$readOnly&&(this.inComposition={},!this.commandMode)){e.data&&(this.inComposition.useTextareaForIME=!1),setTimeout(this.onCompositionUpdate.bind(this),0),this.host._signal("compositionStart"),this.host.on("mousedown",this.cancelComposition);var t=this.host.getSelectionRange();t.end.row=t.start.row,t.end.column=t.start.column,this.inComposition.markerRange=t,this.inComposition.selectionStart=this.lastSelectionStart,this.host.onCompositionStart(this.inComposition),this.inComposition.useTextareaForIME?(this.lastValue=this.text.value="",this.lastSelectionStart=0,this.lastSelectionEnd=0):(this.text.msGetInputContext&&(this.inComposition.context=this.text.msGetInputContext()),this.text.getInputContext&&(this.inComposition.context=this.text.getInputContext()))}},e.prototype.onCompositionUpdate=function(){if(this.inComposition&&this.host.onCompositionUpdate&&!this.host.$readOnly){if(this.commandMode)return this.cancelComposition();if(this.inComposition.useTextareaForIME)this.host.onCompositionUpdate(this.text.value);else{var e=this.text.value;this.sendText(e),this.inComposition.markerRange&&(this.inComposition.context&&(this.inComposition.markerRange.start.column=this.inComposition.selectionStart=this.inComposition.context.compositionStartOffset),this.inComposition.markerRange.end.column=this.inComposition.markerRange.start.column+this.lastSelectionEnd-this.inComposition.selectionStart+this.lastRestoreEnd)}}},e.prototype.onCompositionEnd=function(e){this.host.onCompositionEnd&&!this.host.$readOnly&&(this.inComposition=!1,this.host.onCompositionEnd(),this.host.off("mousedown",this.cancelComposition),e&&this.onInput())},e.prototype.onCut=function(e){this.doCopy(e,!0)},e.prototype.onCopy=function(e){this.doCopy(e,!1)},e.prototype.onPaste=function(e){var t=this.handleClipboardData(e);l.pasteCancelled()||("string"==typeof t?(t&&this.host.onPaste(t,e),r.isIE&&setTimeout(this.resetSelection),n.preventDefault(e)):(this.text.value="",this.pasted=!0))},e.prototype.doCopy=function(e,t){var i=this,o=this.host.getCopyText();if(!o)return n.preventDefault(e);this.handleClipboardData(e,o)?(g&&(this.resetSelection(o),this.copied=o,setTimeout(function(){i.copied=!1},10)),t?this.host.onCut():this.host.onCopy(),n.preventDefault(e)):(this.copied=!0,this.text.value=o,this.text.select(),setTimeout(function(){i.copied=!1,i.resetSelection(),t?i.host.onCut():i.host.onCopy()}))},e.prototype.handleClipboardData=function(e,t,i){var n=e.clipboardData||window.clipboardData;if(n&&!c){var o=h||i?"Text":"text/plain";try{if(t)return!1!==n.setData(o,t);return n.getData(o)}catch(e){if(!i)return this.handleClipboardData(e,t,!0)}}},e.prototype.onInput=function(e){if(this.inComposition)return this.onCompositionUpdate();if(e&&e.inputType){if("historyUndo"==e.inputType)return this.host.execCommand("undo");if("historyRedo"==e.inputType)return this.host.execCommand("redo")}var t=this.text.value,i=this.sendText(t,!0);(t.length>500||f.test(i)||m&&this.lastSelectionStart<1&&this.lastSelectionStart==this.lastSelectionEnd)&&this.resetSelection()},e.prototype.sendText=function(e,t){if(this.afterContextMenu&&(this.afterContextMenu=!1),this.pasted)return this.resetSelection(),e&&this.host.onPaste(e),this.pasted=!1,"";for(var i=this.text.selectionStart,n=this.text.selectionEnd,o=this.lastSelectionStart,s=this.lastValue.length-this.lastSelectionEnd,a=e,l=e.length-i,c=e.length-n,h=0;o>0&&this.lastValue[h]==e[h];)h++,o--;for(a=a.slice(h),h=1;s>0&&this.lastValue.length-h>this.lastSelectionStart-1&&this.lastValue[this.lastValue.length-h]==e[e.length-h];)h++,s--;l-=h-1,c-=h-1;var u=a.length-h+1;if(u<0&&(o=-u,u=0),a=a.slice(0,u),!t&&!a&&!l&&!o&&!s&&!c)return"";this.sendingText=!0;var d=!1;return r.isAndroid&&". "==a&&(a="  ",d=!0),(!a||o||s||l||c)&&!this.commandMode?this.host.onTextInput(a,{extendLeft:o,extendRight:s,restoreStart:l,restoreEnd:c}):this.host.onTextInput(a),this.sendingText=!1,this.lastValue=e,this.lastSelectionStart=i,this.lastSelectionEnd=n,this.lastRestoreEnd=c,d?"\n":a},e.prototype.onSelect=function(e){var t;!this.inComposition&&(this.copied?this.copied=!1:0===(t=this.text).selectionStart&&t.selectionEnd>=this.lastValue.length&&t.value===this.lastValue&&this.lastValue&&t.selectionEnd!==this.lastSelectionEnd?(this.host.selectAll(),this.resetSelection()):m&&this.text.selectionStart!=this.lastSelectionStart&&this.resetSelection())},e.prototype.$resetSelectionIOS=function(e){if(this.$isFocused&&(!this.copied||e)&&!this.sendingText){e||(e="");var t="\n ab"+e+"cde fg\n";t!=this.text.value&&(this.text.value=this.lastValue=t);var i=4+(e.length||+!this.host.selection.isEmpty());(4!=this.lastSelectionStart||this.lastSelectionEnd!=i)&&this.text.setSelectionRange(4,i),this.lastSelectionStart=4,this.lastSelectionEnd=i}},e.prototype.$resetSelection=function(){var e=this;if(!this.inComposition&&!this.sendingText&&(this.$isFocused||this.afterContextMenu)){this.inComposition=!0;var t=0,i=0,n="",o=function(t,i){for(var n=i,o=1;o<=t-e.rowStart&&o<2*e.numberOfExtraLines+1;o++)n+=e.host.session.getLine(t-o).length+1;return n};if(this.host.session){var r=this.host.selection,s=r.getRange(),a=r.cursor.row;a===this.rowEnd+1?(this.rowStart=this.rowEnd+1,this.rowEnd=this.rowStart+2*this.numberOfExtraLines):a===this.rowStart-1?(this.rowEnd=this.rowStart-1,this.rowStart=this.rowEnd-2*this.numberOfExtraLines):(a<this.rowStart-1||a>this.rowEnd+1)&&(this.rowStart=a>this.numberOfExtraLines?a-this.numberOfExtraLines:0,this.rowEnd=a>this.numberOfExtraLines?a+this.numberOfExtraLines:2*this.numberOfExtraLines);for(var l=[],c=this.rowStart;c<=this.rowEnd;c++)l.push(this.host.session.getLine(c));if(n=l.join("\n"),t=o(s.start.row,s.start.column),i=o(s.end.row,s.end.column),s.start.row<this.rowStart){var h=this.host.session.getLine(this.rowStart-1);t=s.start.row<this.rowStart-1?0:t,i+=h.length+1,n=h+"\n"+n}else if(s.end.row>this.rowEnd){var u=this.host.session.getLine(this.rowEnd+1);i=(s.end.row>this.rowEnd+1?u.length:s.end.column)+(n.length+1),n=n+"\n"+u}else m&&a>0&&(n="\n"+n,i+=1,t+=1);n.length>400&&(t<400&&i<400?n=n.slice(0,400):(n="\n",t==i?t=i=0:(t=0,i=1)));var d=n+"\n\n";d!=this.lastValue&&(this.text.value=this.lastValue=d,this.lastSelectionStart=this.lastSelectionEnd=d.length)}if(this.afterContextMenu&&(this.lastSelectionStart=this.text.selectionStart,this.lastSelectionEnd=this.text.selectionEnd),this.lastSelectionEnd!=i||this.lastSelectionStart!=t||this.text.selectionEnd!=this.lastSelectionEnd)try{this.text.setSelectionRange(t,i),this.lastSelectionStart=t,this.lastSelectionEnd=i}catch(e){}this.inComposition=!1}},e.prototype.setHost=function(e){this.host=e},e.prototype.setNumberOfExtraLines=function(e){if(this.rowStart=Number.MAX_SAFE_INTEGER,this.rowEnd=Number.MIN_SAFE_INTEGER,e<0){this.numberOfExtraLines=0;return}this.numberOfExtraLines=e},e.prototype.setAriaLabel=function(){var e="";this.host.$textInputAriaLabel&&(e+="".concat(this.host.$textInputAriaLabel,", ")),this.host.session&&(e+=o("text-input.aria-label","Cursor at row $0",[this.host.session.selection.cursor.row+1])),this.text.setAttribute("aria-label",e)},e.prototype.setAriaOptions=function(e){e.activeDescendant?(this.text.setAttribute("aria-haspopup","true"),this.text.setAttribute("aria-autocomplete",e.inline?"both":"list"),this.text.setAttribute("aria-activedescendant",e.activeDescendant)):(this.text.setAttribute("aria-haspopup","false"),this.text.setAttribute("aria-autocomplete","both"),this.text.removeAttribute("aria-activedescendant")),e.role&&this.text.setAttribute("role",e.role),e.setLabel&&(this.text.setAttribute("aria-roledescription",o("text-input.aria-roledescription","editor")),this.setAriaLabel())},e.prototype.focus=function(){var e=this;if(this.setAriaOptions({setLabel:this.host.renderer.enableKeyboardAccessibility}),this.tempStyle||u||"browser"==this.$focusScroll)return this.text.focus({preventScroll:!0});var t=this.text.style.top;this.text.style.position="fixed",this.text.style.top="0px";try{var i=0!=this.text.getBoundingClientRect().top}catch(e){return}var n=[];if(i)for(var o=this.text.parentElement;o&&1==o.nodeType;)n.push(o),o.setAttribute("ace_nocontext","true"),o=!o.parentElement&&o.getRootNode?o.getRootNode().host:o.parentElement;this.text.focus({preventScroll:!0}),i&&n.forEach(function(e){e.removeAttribute("ace_nocontext")}),setTimeout(function(){e.text.style.position="","0px"==e.text.style.top&&(e.text.style.top=t)},0)},e.prototype.blur=function(){this.text.blur()},e.prototype.isFocused=function(){return this.$isFocused},e.prototype.setInputHandler=function(e){this.inputHandler=e},e.prototype.getInputHandler=function(){return this.inputHandler},e.prototype.getElement=function(){return this.text},e.prototype.setCommandMode=function(e){this.commandMode=e,this.text.readOnly=!1},e.prototype.setReadOnly=function(e){this.commandMode||(this.text.readOnly=e)},e.prototype.setCopyWithEmptySelection=function(e){},e.prototype.onContextMenu=function(e){this.afterContextMenu=!0,this.resetSelection(),this.host._emit("nativecontextmenu",{target:this.host,domEvent:e}),this.moveToMouse(e,!0)},e.prototype.moveToMouse=function(e,t){var i=this;this.tempStyle||(this.tempStyle=this.text.style.cssText),this.text.style.cssText=(t?"z-index:100000;":"")+(r.isIE?"opacity:0.1;":"")+"text-indent: -"+(this.lastSelectionStart+this.lastSelectionEnd)*this.host.renderer.characterWidth*.5+"px;";var o=this.host.container.getBoundingClientRect(),a=s.computedStyle(this.host.container),l=o.top+(parseInt(a.borderTopWidth)||0),c=o.left+(parseInt(a.borderLeftWidth)||0),h=o.bottom-l-this.text.clientHeight-2,u=function(e){s.translate(i.text,e.clientX-c-2,Math.min(e.clientY-l-2,h))};u(e),"mousedown"==e.type&&(this.host.renderer.$isMousePressed=!0,clearTimeout(this.closeTimeout),r.isWin&&n.capture(this.host.container,u,this.onContextMenuClose.bind(this)))},e.prototype.destroy=function(){this.text.parentElement&&this.text.parentElement.removeChild(this.text)},e}(),t.$setUserAgentForTests=function(e,t){m=e,g=t}}),ace.define("ace/mouse/default_handlers",["require","exports","module","ace/lib/useragent"],function(e,t,i){"use strict";var n=e("../lib/useragent"),o=function(){function e(e){e.$clickSelection=null;var t=e.editor;t.setDefaultHandler("mousedown",this.onMouseDown.bind(e)),t.setDefaultHandler("dblclick",this.onDoubleClick.bind(e)),t.setDefaultHandler("tripleclick",this.onTripleClick.bind(e)),t.setDefaultHandler("quadclick",this.onQuadClick.bind(e)),t.setDefaultHandler("mousewheel",this.onMouseWheel.bind(e)),["select","startSelect","selectEnd","selectAllEnd","selectByWordsEnd","selectByLinesEnd","dragWait","dragWaitEnd","focusWait"].forEach(function(t){e[t]=this[t]},this),e.selectByLines=this.extendSelectionBy.bind(e,"getLineRange"),e.selectByWords=this.extendSelectionBy.bind(e,"getWordRange")}return e.prototype.onMouseDown=function(e){var t=e.inSelection(),i=e.getDocumentPosition();this.mousedownEvent=e;var o=this.editor,r=e.getButton();if(0!==r){(o.getSelectionRange().isEmpty()||1==r)&&o.selection.moveToPosition(i),2==r&&(o.textInput.onContextMenu(e.domEvent),n.isMozilla||e.preventDefault());return}if(this.mousedownEvent.time=Date.now(),t&&!o.isFocused()&&(o.focus(),this.$focusTimeout&&!this.$clickSelection&&!o.inMultiSelectMode)){this.setState("focusWait"),this.captureMouse(e);return}return this.captureMouse(e),this.startSelect(i,e.domEvent._clicks>1),e.preventDefault()},e.prototype.startSelect=function(e,t){e=e||this.editor.renderer.screenToTextCoordinates(this.x,this.y);var i=this.editor;this.mousedownEvent&&(this.mousedownEvent.getShiftKey()?i.selection.selectToPosition(e):t||i.selection.moveToPosition(e),t||this.select(),i.setStyle("ace_selecting"),this.setState("select"))},e.prototype.select=function(){var e,t=this.editor,i=t.renderer.screenToTextCoordinates(this.x,this.y);if(this.$clickSelection){var n=this.$clickSelection.comparePoint(i);if(-1==n)e=this.$clickSelection.end;else if(1==n)e=this.$clickSelection.start;else{var o=r(this.$clickSelection,i);i=o.cursor,e=o.anchor}t.selection.setSelectionAnchor(e.row,e.column)}t.selection.selectToPosition(i),t.renderer.scrollCursorIntoView()},e.prototype.extendSelectionBy=function(e){var t,i=this.editor,n=i.renderer.screenToTextCoordinates(this.x,this.y),o=i.selection[e](n.row,n.column);if(this.$clickSelection){var s=this.$clickSelection.comparePoint(o.start),a=this.$clickSelection.comparePoint(o.end);if(-1==s&&a<=0)t=this.$clickSelection.end,(o.end.row!=n.row||o.end.column!=n.column)&&(n=o.start);else if(1==a&&s>=0)t=this.$clickSelection.start,(o.start.row!=n.row||o.start.column!=n.column)&&(n=o.end);else if(-1==s&&1==a)n=o.end,t=o.start;else{var l=r(this.$clickSelection,n);n=l.cursor,t=l.anchor}i.selection.setSelectionAnchor(t.row,t.column)}i.selection.selectToPosition(n),i.renderer.scrollCursorIntoView()},e.prototype.selectByLinesEnd=function(){this.$clickSelection=null,this.editor.unsetStyle("ace_selecting")},e.prototype.focusWait=function(){var e,t,i,n=(e=this.mousedownEvent.x,t=this.mousedownEvent.y,i=this.x,Math.sqrt(Math.pow(i-e,2)+Math.pow(this.y-t,2))),o=Date.now();(n>0||o-this.mousedownEvent.time>this.$focusTimeout)&&this.startSelect(this.mousedownEvent.getDocumentPosition())},e.prototype.onDoubleClick=function(e){var t=e.getDocumentPosition(),i=this.editor,n=i.session.getBracketRange(t);n?(n.isEmpty()&&(n.start.column--,n.end.column++),this.setState("select")):(n=i.selection.getWordRange(t.row,t.column),this.setState("selectByWords")),this.$clickSelection=n,this.select()},e.prototype.onTripleClick=function(e){var t=e.getDocumentPosition(),i=this.editor;this.setState("selectByLines");var n=i.getSelectionRange();n.isMultiLine()&&n.contains(t.row,t.column)?(this.$clickSelection=i.selection.getLineRange(n.start.row),this.$clickSelection.end=i.selection.getLineRange(n.end.row).end):this.$clickSelection=i.selection.getLineRange(t.row),this.select()},e.prototype.onQuadClick=function(e){var t=this.editor;t.selectAll(),this.$clickSelection=t.getSelectionRange(),this.setState("selectAll")},e.prototype.onMouseWheel=function(e){if(!e.getAccelKey()){e.getShiftKey()&&e.wheelY&&!e.wheelX&&(e.wheelX=e.wheelY,e.wheelY=0);var t=this.editor;this.$lastScroll||(this.$lastScroll={t:0,vx:0,vy:0,allowed:0});var i=this.$lastScroll,n=e.domEvent.timeStamp,o=n-i.t,r=o?e.wheelX/o:i.vx,s=o?e.wheelY/o:i.vy;o<550&&(r=(r+i.vx)/2,s=(s+i.vy)/2);var a=Math.abs(r/s),l=!1;if(a>=1&&t.renderer.isScrollableBy(e.wheelX*e.speed,0)&&(l=!0),a<=1&&t.renderer.isScrollableBy(0,e.wheelY*e.speed)&&(l=!0),l?i.allowed=n:n-i.allowed<550&&(Math.abs(r)<=1.5*Math.abs(i.vx)&&Math.abs(s)<=1.5*Math.abs(i.vy)?(l=!0,i.allowed=n):i.allowed=0),i.t=n,i.vx=r,i.vy=s,l)return t.renderer.scrollBy(e.wheelX*e.speed,e.wheelY*e.speed),e.stop()}},e}();function r(e,t){if(e.start.row==e.end.row)var i=2*t.column-e.start.column-e.end.column;else if(e.start.row!=e.end.row-1||e.start.column||e.end.column)var i=2*t.row-e.start.row-e.end.row;else var i=t.column-4;return i<0?{cursor:e.start,anchor:e.end}:{cursor:e.end,anchor:e.start}}o.prototype.selectEnd=o.prototype.selectByLinesEnd,o.prototype.selectAllEnd=o.prototype.selectByLinesEnd,o.prototype.selectByWordsEnd=o.prototype.selectByLinesEnd,t.DefaultHandlers=o}),ace.define("ace/lib/scroll",["require","exports","module"],function(e,t,i){t.preventParentScroll=function(e){e.stopPropagation();var t=e.currentTarget;t.scrollHeight>t.clientHeight||e.preventDefault()}}),ace.define("ace/tooltip",["require","exports","module","ace/lib/dom","ace/lib/event","ace/range","ace/lib/scroll"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&e[t],n=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=e("./lib/dom");e("./lib/event");var a=e("./range").Range,l=e("./lib/scroll").preventParentScroll,c="ace_tooltip",h=function(){function e(e){this.isOpen=!1,this.$element=null,this.$parentNode=e}return e.prototype.$init=function(){return this.$element=s.createElement("div"),this.$element.className=c,this.$element.style.display="none",this.$parentNode.appendChild(this.$element),this.$element},e.prototype.getElement=function(){return this.$element||this.$init()},e.prototype.setText=function(e){this.getElement().textContent=e},e.prototype.setHtml=function(e){this.getElement().innerHTML=e},e.prototype.setPosition=function(e,t){this.getElement().style.left=e+"px",this.getElement().style.top=t+"px"},e.prototype.setClassName=function(e){s.addCssClass(this.getElement(),e)},e.prototype.setTheme=function(e){this.$element.className=c+" "+(e.isDark?"ace_dark ":"")+(e.cssClass||"")},e.prototype.show=function(e,t,i){null!=e&&this.setText(e),null!=t&&null!=i&&this.setPosition(t,i),this.isOpen||(this.getElement().style.display="block",this.isOpen=!0)},e.prototype.hide=function(e){this.isOpen&&(this.getElement().style.display="none",this.getElement().className=c,this.isOpen=!1)},e.prototype.getHeight=function(){return this.getElement().offsetHeight},e.prototype.getWidth=function(){return this.getElement().offsetWidth},e.prototype.destroy=function(){this.isOpen=!1,this.$element&&this.$element.parentNode&&this.$element.parentNode.removeChild(this.$element)},e}(),u=new(function(){function e(){this.popups=[]}return e.prototype.addPopup=function(e){this.popups.push(e),this.updatePopups()},e.prototype.removePopup=function(e){var t=this.popups.indexOf(e);-1!==t&&(this.popups.splice(t,1),this.updatePopups())},e.prototype.updatePopups=function(){this.popups.sort(function(e,t){return t.priority-e.priority});var e,t,i,n,o=[];try{for(var s=r(this.popups),a=s.next();!a.done;a=s.next()){var l=a.value,c=!0;try{for(var h=(i=void 0,r(o)),u=h.next();!u.done;u=h.next()){var d=u.value;if(this.doPopupsOverlap(d,l)){c=!1;break}}}catch(e){i={error:e}}finally{try{u&&!u.done&&(n=h.return)&&n.call(h)}finally{if(i)throw i.error}}c?o.push(l):l.hide()}}catch(t){e={error:t}}finally{try{a&&!a.done&&(t=s.return)&&t.call(s)}finally{if(e)throw e.error}}},e.prototype.doPopupsOverlap=function(e,t){var i=e.getElement().getBoundingClientRect(),n=t.getElement().getBoundingClientRect();return i.left<n.right&&i.right>n.left&&i.top<n.bottom&&i.bottom>n.top},e}());t.popupManager=u,t.Tooltip=h,t.HoverTooltip=function(e){function t(t){void 0===t&&(t=document.body);var i=e.call(this,t)||this;i.timeout=void 0,i.lastT=0,i.idleTime=350,i.lastEvent=void 0,i.onMouseOut=i.onMouseOut.bind(i),i.onMouseMove=i.onMouseMove.bind(i),i.waitForHover=i.waitForHover.bind(i),i.hide=i.hide.bind(i);var n=i.getElement();return n.style.whiteSpace="pre-wrap",n.style.pointerEvents="auto",n.addEventListener("mouseout",i.onMouseOut),n.tabIndex=-1,n.addEventListener("blur",(function(){n.contains(document.activeElement)||this.hide()}).bind(i)),n.addEventListener("wheel",l),i}return o(t,e),t.prototype.addToEditor=function(e){e.on("mousemove",this.onMouseMove),e.on("mousedown",this.hide),e.renderer.getMouseEventTarget().addEventListener("mouseout",this.onMouseOut,!0)},t.prototype.removeFromEditor=function(e){e.off("mousemove",this.onMouseMove),e.off("mousedown",this.hide),e.renderer.getMouseEventTarget().removeEventListener("mouseout",this.onMouseOut,!0),this.timeout&&(clearTimeout(this.timeout),this.timeout=null)},t.prototype.onMouseMove=function(e,t){this.lastEvent=e,this.lastT=Date.now();var i=t.$mouseHandler.isMousePressed;if(this.isOpen){var n=this.lastEvent&&this.lastEvent.getDocumentPosition();(!this.range||!this.range.contains(n.row,n.column)||i||this.isOutsideOfText(this.lastEvent))&&this.hide()}this.timeout||i||(this.lastEvent=e,this.timeout=setTimeout(this.waitForHover,this.idleTime))},t.prototype.waitForHover=function(){this.timeout&&clearTimeout(this.timeout);var e=Date.now()-this.lastT;if(this.idleTime-e>10){this.timeout=setTimeout(this.waitForHover,this.idleTime-e);return}this.timeout=null,this.lastEvent&&!this.isOutsideOfText(this.lastEvent)&&this.$gatherData(this.lastEvent,this.lastEvent.editor)},t.prototype.isOutsideOfText=function(e){var t=e.editor,i=e.getDocumentPosition(),n=t.session.getLine(i.row);if(i.column==n.length){var o=t.renderer.pixelToScreenCoordinates(e.clientX,e.clientY),r=t.session.documentToScreenPosition(i.row,i.column);if(r.column!=o.column||r.row!=o.row)return!0}return!1},t.prototype.setDataProvider=function(e){this.$gatherData=e},t.prototype.showForRange=function(e,t,i,n){if((!n||n==this.lastEvent)&&(!this.isOpen||document.activeElement!=this.getElement())){var o=e.renderer;this.isOpen||(u.addPopup(this),this.$registerCloseEvents(),this.setTheme(o.theme)),this.isOpen=!0,this.addMarker(t,e.session),this.range=a.fromPoints(t.start,t.end);var r=o.textToScreenCoordinates(t.start.row,t.start.column),s=o.scroller.getBoundingClientRect();r.pageX<s.left&&(r.pageX=s.left);var l=this.getElement();l.innerHTML="",l.appendChild(i),l.style.maxHeight="",l.style.display="block";var c=l.clientHeight,h=l.clientWidth,d=window.innerHeight-r.pageY-o.lineHeight,p=!0;r.pageY-c<0&&r.pageY<d&&(p=!1),l.style.maxHeight=(p?r.pageY:d)-10+"px",l.style.top=p?"":r.pageY+o.lineHeight+"px",l.style.bottom=p?window.innerHeight-r.pageY+"px":"",l.style.left=Math.min(r.pageX,window.innerWidth-h-10)+"px"}},t.prototype.addMarker=function(e,t){this.marker&&this.$markerSession.removeMarker(this.marker),this.$markerSession=t,this.marker=t&&t.addMarker(e,"ace_highlight-marker","text")},t.prototype.hide=function(e){(e||document.activeElement!=this.getElement())&&!(e&&e.target&&("keydown"!=e.type||e.ctrlKey||e.metaKey)&&this.$element.contains(e.target))&&(this.lastEvent=null,this.timeout&&clearTimeout(this.timeout),this.timeout=null,this.addMarker(null),this.isOpen&&(this.$removeCloseEvents(),this.getElement().style.display="none",this.isOpen=!1,u.removePopup(this)))},t.prototype.$registerCloseEvents=function(){window.addEventListener("keydown",this.hide,!0),window.addEventListener("wheel",this.hide,!0),window.addEventListener("mousedown",this.hide,!0)},t.prototype.$removeCloseEvents=function(){window.removeEventListener("keydown",this.hide,!0),window.removeEventListener("wheel",this.hide,!0),window.removeEventListener("mousedown",this.hide,!0)},t.prototype.onMouseOut=function(e){this.timeout&&(clearTimeout(this.timeout),this.timeout=null),this.lastEvent=null,!this.isOpen||!e.relatedTarget||this.getElement().contains(e.relatedTarget)||!(e&&e.currentTarget.contains(e.relatedTarget))&&(e.relatedTarget.classList.contains("ace_content")||this.hide())},t}(h)}),ace.define("ace/mouse/default_gutter_handler",["require","exports","module","ace/lib/dom","ace/lib/event","ace/tooltip","ace/config"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&e[t],n=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=e("../lib/dom"),a=e("../lib/event"),l=e("../tooltip").Tooltip,c=e("../config").nls;t.GUTTER_TOOLTIP_LEFT_OFFSET=5,t.GUTTER_TOOLTIP_TOP_OFFSET=3,t.GutterHandler=function(e){var t,i,n=e.editor,o=n.renderer.$gutterLayer,r=new h(n,!0);function l(e){e&&"keydown"===e.type&&(e.ctrlKey||e.metaKey)||!(e&&"mouseout"===e.type&&(!e.relatedTarget||r.getElement().contains(e.relatedTarget)))&&(t&&(t=clearTimeout(t)),r.isOpen&&(r.hideTooltip(),n.off("mousewheel",l),n.off("changeSession",l),window.removeEventListener("keydown",l,!0)))}function c(e){r.setPosition(e.x,e.y)}e.editor.setDefaultHandler("guttermousedown",function(t){if(n.isFocused()&&0==t.getButton()&&"foldWidgets"!=o.getRegion(t)){var i=t.getDocumentPosition().row,r=n.session.selection;if(t.getShiftKey())r.selectTo(i,0);else{if(2==t.domEvent.detail)return n.selectAll(),t.preventDefault();e.$clickSelection=n.selection.getLineRange(i)}return e.setState("selectByLines"),e.captureMouse(t),t.preventDefault()}}),e.editor.setDefaultHandler("guttermousemove",function(a){var h=a.domEvent.target||a.domEvent.srcElement;if(s.hasCssClass(h,"ace_fold-widget")||s.hasCssClass(h,"ace_custom-widget"))return l();r.isOpen&&e.$tooltipFollowsMouse&&c(a),i=a,t||(t=setTimeout(function(){t=null,i&&!e.isMousePressed&&function(){var t=i.getDocumentPosition().row;if(t==n.session.getLength()){var s=n.renderer.pixelToScreenCoordinates(0,i.y).row,a=i.$pos;if(s>n.session.documentToScreenRow(a.row,a.column))return l()}if(r.showTooltip(t),r.isOpen)if(n.on("mousewheel",l),n.on("changeSession",l),window.addEventListener("keydown",l,!0),e.$tooltipFollowsMouse)c(i);else{var h=i.getGutterRow(),u=o.$lines.get(h);if(u){var d=u.element.querySelector(".ace_gutter_annotation").getBoundingClientRect(),p=r.getElement().style;p.left=d.right-5+"px",p.top=d.bottom-3+"px"}else c(i)}}()},50))}),a.addListener(n.renderer.$gutter,"mouseout",function(e){i=null,r.isOpen&&(t=setTimeout(function(){t=null,l(e)},50))},n)};var h=function(e){function t(i,n){void 0===n&&(n=!1);var o=e.call(this,i.container)||this;o.id="gt"+ ++t.$uid,o.editor=i,o.visibleTooltipRow;var r=o.getElement();return r.setAttribute("role","tooltip"),r.setAttribute("id",o.id),r.style.pointerEvents="auto",n&&(o.onMouseOut=o.onMouseOut.bind(o),r.addEventListener("mouseout",o.onMouseOut)),o}return o(t,e),t.prototype.onMouseOut=function(e){!(!this.isOpen||!e.relatedTarget||this.getElement().contains(e.relatedTarget))&&(e&&e.currentTarget.contains(e.relatedTarget)||this.hideTooltip())},t.prototype.setPosition=function(e,t){var i=window.innerWidth||document.documentElement.clientWidth,n=window.innerHeight||document.documentElement.clientHeight,o=this.getWidth(),r=this.getHeight();t+=15,(e+=15)+o>i&&(e-=e+o-i),t+r>n&&(t-=20+r),l.prototype.setPosition.call(this,e,t)},Object.defineProperty(t,"annotationLabels",{get:function(){return{error:{singular:c("gutter-tooltip.aria-label.error.singular","error"),plural:c("gutter-tooltip.aria-label.error.plural","errors")},security:{singular:c("gutter-tooltip.aria-label.security.singular","security finding"),plural:c("gutter-tooltip.aria-label.security.plural","security findings")},warning:{singular:c("gutter-tooltip.aria-label.warning.singular","warning"),plural:c("gutter-tooltip.aria-label.warning.plural","warnings")},info:{singular:c("gutter-tooltip.aria-label.info.singular","information message"),plural:c("gutter-tooltip.aria-label.info.plural","information messages")},hint:{singular:c("gutter-tooltip.aria-label.hint.singular","suggestion"),plural:c("gutter-tooltip.aria-label.hint.plural","suggestions")}}},enumerable:!1,configurable:!0}),t.prototype.showTooltip=function(e){var i=this.editor.renderer.$gutterLayer,n=i.$annotations[e];a=n?{displayText:Array.from(n.displayText),type:Array.from(n.type)}:{displayText:[],type:[]};var o=i.session.getFoldLine(e);if(o&&i.$showFoldedAnnotations){for(var r,a,l,c={error:[],security:[],warning:[],info:[],hint:[]},h={error:1,security:2,warning:3,info:4,hint:5},u=e+1;u<=o.end.row;u++)if(i.$annotations[u])for(var d=0;d<i.$annotations[u].text.length;d++){var p=i.$annotations[u].type[d];c[p].push(i.$annotations[u].text[d]),(!l||h[p]<h[l])&&(l=p)}if(["error","security","warning"].includes(l)){var g="".concat(t.annotationsToSummaryString(c)," in folded code.");a.displayText.push(g),a.type.push(l+"_fold")}}if(0===a.displayText.length)return this.hideTooltip();for(var f={error:[],security:[],warning:[],info:[],hint:[]},m=i.$useSvgGutterIcons?"ace_icon_svg":"ace_icon",u=0;u<a.displayText.length;u++){var y=s.createElement("span"),v=s.createElement("span");(r=v.classList).add.apply(r,["ace_".concat(a.type[u]),m]),v.setAttribute("aria-label","".concat(t.annotationLabels[a.type[u].replace("_fold","")].singular)),v.setAttribute("role","img"),v.appendChild(s.createTextNode(" ")),y.appendChild(v),y.appendChild(s.createTextNode(a.displayText[u])),y.appendChild(s.createElement("br")),f[a.type[u].replace("_fold","")].push(y)}var w=this.getElement();s.removeChildren(w),f.error.forEach(function(e){return w.appendChild(e)}),f.security.forEach(function(e){return w.appendChild(e)}),f.warning.forEach(function(e){return w.appendChild(e)}),f.info.forEach(function(e){return w.appendChild(e)}),f.hint.forEach(function(e){return w.appendChild(e)}),w.setAttribute("aria-live","polite"),this.isOpen||(this.setTheme(this.editor.renderer.theme),this.setClassName("ace_gutter-tooltip"));var b=this.$findLinkedAnnotationNode(e);b&&b.setAttribute("aria-describedby",this.id),this.show(),this.visibleTooltipRow=e,this.editor._signal("showGutterTooltip",this)},t.prototype.$findLinkedAnnotationNode=function(e){var t=this.$findCellByRow(e);if(t){var i=t.element;if(i.childNodes.length>2)return i.childNodes[2]}},t.prototype.$findCellByRow=function(e){return this.editor.renderer.$gutterLayer.$lines.cells.find(function(t){return t.row===e})},t.prototype.hideTooltip=function(){if(this.isOpen){if(this.$element.removeAttribute("aria-live"),this.hide(),void 0!=this.visibleTooltipRow){var e=this.$findLinkedAnnotationNode(this.visibleTooltipRow);e&&e.removeAttribute("aria-describedby")}this.visibleTooltipRow=void 0,this.editor._signal("hideGutterTooltip",this)}},t.annotationsToSummaryString=function(e){var i,n,o=[];try{for(var s=r(["error","security","warning","info","hint"]),a=s.next();!a.done;a=s.next()){var l=a.value;if(e[l].length){var c=1===e[l].length?t.annotationLabels[l].singular:t.annotationLabels[l].plural;o.push("".concat(e[l].length," ").concat(c))}}}catch(e){i={error:e}}finally{try{a&&!a.done&&(n=s.return)&&n.call(s)}finally{if(i)throw i.error}}return o.join(", ")},t}(l);h.$uid=0,t.GutterTooltip=h}),ace.define("ace/mouse/mouse_event",["require","exports","module","ace/lib/event","ace/lib/useragent"],function(e,t,i){"use strict";var n=e("../lib/event"),o=e("../lib/useragent");t.MouseEvent=function(){function e(e,t){this.speed,this.wheelX,this.wheelY,this.domEvent=e,this.editor=t,this.x=this.clientX=e.clientX,this.y=this.clientY=e.clientY,this.$pos=null,this.$inSelection=null,this.propagationStopped=!1,this.defaultPrevented=!1}return e.prototype.stopPropagation=function(){n.stopPropagation(this.domEvent),this.propagationStopped=!0},e.prototype.preventDefault=function(){n.preventDefault(this.domEvent),this.defaultPrevented=!0},e.prototype.stop=function(){this.stopPropagation(),this.preventDefault()},e.prototype.getDocumentPosition=function(){return this.$pos||(this.$pos=this.editor.renderer.screenToTextCoordinates(this.clientX,this.clientY)),this.$pos},e.prototype.getGutterRow=function(){var e=this.getDocumentPosition().row;return this.editor.session.documentToScreenRow(e,0)-this.editor.session.documentToScreenRow(this.editor.renderer.$gutterLayer.$lines.get(0).row,0)},e.prototype.inSelection=function(){if(null!==this.$inSelection)return this.$inSelection;var e=this.editor.getSelectionRange();if(e.isEmpty())this.$inSelection=!1;else{var t=this.getDocumentPosition();this.$inSelection=e.contains(t.row,t.column)}return this.$inSelection},e.prototype.getButton=function(){return n.getButton(this.domEvent)},e.prototype.getShiftKey=function(){return this.domEvent.shiftKey},e.prototype.getAccelKey=function(){return o.isMac?this.domEvent.metaKey:this.domEvent.ctrlKey},e}()}),ace.define("ace/mouse/dragdrop_handler",["require","exports","module","ace/lib/dom","ace/lib/event","ace/lib/useragent"],function(e,t,i){"use strict";var n=e("../lib/dom"),o=e("../lib/event"),r=e("../lib/useragent");function s(e){var t,i,s,l,c,h=e.editor,u=n.createElement("div");u.style.cssText="top:-100px;position:absolute;z-index:2147483647;opacity:0.5",u.textContent="\xa0",["dragWait","dragWaitEnd","startDrag","dragReadyEnd","onMouseDrag"].forEach(function(t){e[t]=this[t]},this),h.on("mousedown",this.onMouseDown.bind(e));var d,p,g,f,m,y,v=h.container,w=0;function b(){var e,t,i,n,o,r,u,d,f,m,v,w,b,$,C,S,x=y;e=y=h.renderer.screenToTextCoordinates(p,g),t=Date.now(),i=!x||e.row!=x.row,n=!x||e.column!=x.column,!l||i||n?(h.moveCursorToPosition(e),l=t,c={x:p,y:g}):a(c.x,c.y,p,g)>5?l=null:t-l>=200&&(h.renderer.scrollCursorIntoView(),l=null),o=y,r=Date.now(),u=h.renderer.layerConfig.lineHeight,d=h.renderer.layerConfig.characterWidth,f=h.renderer.scroller.getBoundingClientRect(),v=Math.min((m={x:{left:p-f.left,right:f.right-p},y:{top:g-f.top,bottom:f.bottom-g}}).x.left,m.x.right),w=Math.min(m.y.top,m.y.bottom),b={row:o.row,column:o.column},v/d<=2&&(b.column+=m.x.left<m.x.right?-3:2),w/u<=1&&(b.row+=m.y.top<m.y.bottom?-1:1),$=o.row!=b.row,C=o.column!=b.column,S=!x||o.row!=x.row,$||C&&!S?s?r-s>=200&&h.renderer.scrollCursorIntoView(b):s=r:s=null}function $(){m=h.selection.toOrientedRange(),d=h.session.addMarker(m,"ace_selection",h.getSelectionStyle()),h.clearSelection(),h.isFocused()&&h.renderer.$cursorLayer.setBlinking(!1),clearInterval(f),b(),f=setInterval(b,20),w=0,o.addListener(document,"mousemove",x)}function C(){clearInterval(f),h.session.removeMarker(d),d=null,h.selection.fromOrientedRange(m),h.isFocused()&&!i&&h.$resetCursorStyle(),m=null,y=null,w=0,s=null,l=null,o.removeListener(document,"mousemove",x)}this.onDragStart=function(e){if(this.cancelDrag||!v.draggable){var t=this;return setTimeout(function(){t.startSelect(),t.captureMouse(e)},0),e.preventDefault()}m=h.getSelectionRange();var n=e.dataTransfer;n.effectAllowed=h.getReadOnly()?"copy":"copyMove",h.container.appendChild(u),n.setDragImage&&n.setDragImage(u,0,0),setTimeout(function(){h.container.removeChild(u)}),n.clearData(),n.setData("Text",h.session.getTextRange()),i=!0,this.setState("drag")},this.onDragEnd=function(e){if(v.draggable=!1,i=!1,this.setState(null),!h.getReadOnly()){var n=e.dataTransfer.dropEffect;t||"move"!=n||h.session.remove(h.getSelectionRange()),h.$resetCursorStyle()}this.editor.unsetStyle("ace_dragging"),this.editor.renderer.setCursorStyle("")},this.onDragEnter=function(e){if(!h.getReadOnly()&&A(e.dataTransfer))return p=e.clientX,g=e.clientY,d||$(),w++,e.dataTransfer.dropEffect=t=M(e),o.preventDefault(e)},this.onDragOver=function(e){if(!h.getReadOnly()&&A(e.dataTransfer))return p=e.clientX,g=e.clientY,!d&&($(),w++),null!==S&&(S=null),e.dataTransfer.dropEffect=t=M(e),o.preventDefault(e)},this.onDragLeave=function(e){if(--w<=0&&d)return C(),t=null,o.preventDefault(e)},this.onDrop=function(e){if(y){var n=e.dataTransfer;if(i)switch(t){case"move":m=m.contains(y.row,y.column)?{start:y,end:y}:h.moveText(m,y);break;case"copy":m=h.moveText(m,y,!0)}else{var r=n.getData("Text");m={start:y,end:h.session.insert(y,r)},h.focus(),t=null}return C(),o.preventDefault(e)}},o.addListener(v,"dragstart",this.onDragStart.bind(e),h),o.addListener(v,"dragend",this.onDragEnd.bind(e),h),o.addListener(v,"dragenter",this.onDragEnter.bind(e),h),o.addListener(v,"dragover",this.onDragOver.bind(e),h),o.addListener(v,"dragleave",this.onDragLeave.bind(e),h),o.addListener(v,"drop",this.onDrop.bind(e),h);var S=null;function x(){null==S&&(S=setTimeout(function(){null!=S&&d&&C()},20))}function A(e){var t=e.types;return!t||Array.prototype.some.call(t,function(e){return"text/plain"==e||"Text"==e})}function M(e){var t=["copy","copymove","all","uninitialized"],i=r.isMac?e.altKey:e.ctrlKey,n="uninitialized";try{n=e.dataTransfer.effectAllowed.toLowerCase()}catch(e){}var o="none";return i&&t.indexOf(n)>=0?o="copy":["move","copymove","linkmove","all","uninitialized"].indexOf(n)>=0?o="move":t.indexOf(n)>=0&&(o="copy"),o}}function a(e,t,i,n){return Math.sqrt(Math.pow(i-e,2)+Math.pow(n-t,2))}(function(){this.dragWait=function(){Date.now()-this.mousedownEvent.time>this.editor.getDragDelay()&&this.startDrag()},this.dragWaitEnd=function(){this.editor.container.draggable=!1,this.startSelect(this.mousedownEvent.getDocumentPosition()),this.selectEnd()},this.dragReadyEnd=function(e){this.editor.$resetCursorStyle(),this.editor.unsetStyle("ace_dragging"),this.editor.renderer.setCursorStyle(""),this.dragWaitEnd()},this.startDrag=function(){this.cancelDrag=!1;var e=this.editor;e.container.draggable=!0,e.renderer.$cursorLayer.setBlinking(!1),e.setStyle("ace_dragging");var t=r.isWin?"default":"move";e.renderer.setCursorStyle(t),this.setState("dragReady")},this.onMouseDrag=function(e){var t=this.editor.container;if(r.isIE&&"dragReady"==this.state){var i=a(this.mousedownEvent.x,this.mousedownEvent.y,this.x,this.y);i>3&&t.dragDrop()}if("dragWait"===this.state){var i=a(this.mousedownEvent.x,this.mousedownEvent.y,this.x,this.y);i>0&&(t.draggable=!1,this.startSelect(this.mousedownEvent.getDocumentPosition()))}},this.onMouseDown=function(e){if(this.$dragEnabled){this.mousedownEvent=e;var t=this.editor,i=e.inSelection(),n=e.getButton();if(1===(e.domEvent.detail||1)&&0===n&&i){if(e.editor.inMultiSelectMode&&(e.getAccelKey()||e.getShiftKey()))return;this.mousedownEvent.time=Date.now();var o=e.domEvent.target||e.domEvent.srcElement;"unselectable"in o&&(o.unselectable="on"),t.getDragDelay()?(r.isWebKit&&(this.cancelDrag=!0,t.container.draggable=!0),this.setState("dragWait")):this.startDrag(),this.captureMouse(e,this.onMouseDrag.bind(this)),e.defaultPrevented=!0}}}}).call(s.prototype),t.DragdropHandler=s}),ace.define("ace/mouse/touch_handler",["require","exports","module","ace/mouse/mouse_event","ace/lib/event","ace/lib/dom"],function(e,t,i){"use strict";var n=e("./mouse_event").MouseEvent,o=e("../lib/event"),r=e("../lib/dom");t.addTouchListeners=function(e,t){var i,s,a,l,c,h,u,d,p,g="scroll",f=0,m=0,y=0,v=0;function w(){if(!t.getOption("enableMobileMenu")){p&&b();return}p||(e=window.navigator&&window.navigator.clipboard,i=!1,n=function(){var n=t.getCopyText(),s=t.session.getUndoManager().hasUndo();p.replaceChild(r.buildDom(i?["span",!n&&o("selectall")&&["span",{class:"ace_mobile-button",action:"selectall"},"Select All"],n&&o("copy")&&["span",{class:"ace_mobile-button",action:"copy"},"Copy"],n&&o("cut")&&["span",{class:"ace_mobile-button",action:"cut"},"Cut"],e&&o("paste")&&["span",{class:"ace_mobile-button",action:"paste"},"Paste"],s&&o("undo")&&["span",{class:"ace_mobile-button",action:"undo"},"Undo"],o("find")&&["span",{class:"ace_mobile-button",action:"find"},"Find"],o("openCommandPalette")&&["span",{class:"ace_mobile-button",action:"openCommandPalette"},"Palette"]]:["span"]),p.firstChild)},o=function(e){return t.commands.canExecute(e,t)},s=function(o){var r=o.target.getAttribute("action");if("more"==r||!i)return i=!i,n();"paste"==r?e.readText().then(function(e){t.execCommand(r,e)}):r&&(("cut"==r||"copy"==r)&&(e?e.writeText(t.getCopyText()):document.execCommand("copy")),t.execCommand(r)),p.firstChild.style.display="none",i=!1,"openCommandPalette"!=r&&t.focus()},p=r.buildDom(["div",{class:"ace_mobile-menu",ontouchstart:function(e){g="menu",e.stopPropagation(),e.preventDefault(),t.textInput.focus()},ontouchend:function(e){e.stopPropagation(),e.preventDefault(),s(e)},onclick:s},["span"],["span",{class:"ace_mobile-button",action:"more"},"..."]],t.container));var e,i,n,o,s,a=t.selection.cursor,l=t.renderer.textToScreenCoordinates(a.row,a.column),c=t.renderer.textToScreenCoordinates(0,0).pageX,h=t.renderer.scrollLeft,u=t.container.getBoundingClientRect();p.style.top=l.pageY-u.top-3+"px",l.pageX-u.left<u.width-70?(p.style.left="",p.style.right="10px"):(p.style.right="",p.style.left=c+h-u.left+"px"),p.style.display="",p.firstChild.style.display="none",t.on("input",b)}function b(e){p&&(p.style.display="none"),t.off("input",b)}function $(){clearTimeout(c=null);var e=t.selection.getRange(),i=e.contains(u.row,u.column);(e.isEmpty()||!i)&&(t.selection.moveToPosition(u),t.selection.selectWord()),g="wait",w()}o.addListener(e,"contextmenu",function(e){d&&t.textInput.getElement().focus()},t),o.addListener(e,"touchstart",function(e){var o,r=e.touches;if(c||r.length>1){clearTimeout(c),c=null,a=-1,g="zoom";return}d=t.$mouseHandler.isMousePressed=!0;var h=t.renderer.layerConfig.lineHeight,p=t.renderer.layerConfig.lineHeight,w=e.timeStamp;l=w;var b=r[0],C=b.clientX,S=b.clientY;if(Math.abs(i-C)+Math.abs(s-S)>h&&(a=-1),i=e.clientX=C,s=e.clientY=S,y=v=0,u=new n(e,t).getDocumentPosition(),w-a<500&&1==r.length&&!f)m++,e.preventDefault(),e.button=0,clearTimeout(c=null),t.selection.moveToPosition(u),(o=m>=2?t.selection.getLineRange(u.row):t.session.getBracketRange(u))&&!o.isEmpty()?t.selection.setRange(o):t.selection.selectWord(),g="wait";else{m=0;var x=t.selection.cursor,A=t.selection.isEmpty()?x:t.selection.anchor,M=t.renderer.$cursorLayer.getPixelPosition(x,!0),k=t.renderer.$cursorLayer.getPixelPosition(A,!0),L=t.renderer.scroller.getBoundingClientRect(),T=t.renderer.layerConfig.offset,E=t.renderer.scrollLeft,R=function(e,t){return(e/=p)*e+(t=t/h-.75)*t};if(e.clientX<L.left){g="zoom";return}var _=R(e.clientX-L.left-M.left+E,e.clientY-L.top-M.top+T),I=R(e.clientX-L.left-k.left+E,e.clientY-L.top-k.top+T);_<3.5&&I<3.5&&(g=_>I?"cursor":"anchor"),g=I<3.5?"anchor":_<3.5?"cursor":"scroll",c=setTimeout($,450)}a=w},t),o.addListener(e,"touchend",function(e){d=t.$mouseHandler.isMousePressed=!1,h&&clearInterval(h),"zoom"==g?(g="",f=0):c?(t.selection.moveToPosition(u),f=0,w()):"scroll"==g?(f+=60,h=setInterval(function(){f--<=0&&(clearInterval(h),h=null),.01>Math.abs(y)&&(y=0),.01>Math.abs(v)&&(v=0),f<20&&(y*=.9),f<20&&(v*=.9);var e=t.session.getScrollTop();t.renderer.scrollBy(10*y,10*v),e==t.session.getScrollTop()&&(f=0)},10),b()):w(),clearTimeout(c),c=null},t),o.addListener(e,"touchmove",function(e){c&&(clearTimeout(c),c=null);var o=e.touches;if(!(o.length>1)&&"zoom"!=g){var r=o[0],a=i-r.clientX,h=s-r.clientY;if("wait"==g)if(!(a*a+h*h>4))return e.preventDefault();else g="cursor";i=r.clientX,s=r.clientY,e.clientX=r.clientX,e.clientY=r.clientY;var u=e.timeStamp,d=u-l;if(l=u,"scroll"==g){var p=new n(e,t);p.speed=1,p.wheelX=a,p.wheelY=h,10*Math.abs(a)<Math.abs(h)&&(a=0),10*Math.abs(h)<Math.abs(a)&&(h=0),0!=d&&(y=a/d,v=h/d),t._emit("mousewheel",p),p.propagationStopped||(y=v=0)}else{var f=new n(e,t).getDocumentPosition();"cursor"==g?t.selection.moveCursorToPosition(f):"anchor"==g&&t.selection.setSelectionAnchor(f.row,f.column),t.renderer.scrollCursorIntoView(f),e.preventDefault()}}},t)}}),ace.define("ace/mouse/mouse_handler",["require","exports","module","ace/lib/event","ace/lib/useragent","ace/mouse/default_handlers","ace/mouse/default_gutter_handler","ace/mouse/mouse_event","ace/mouse/dragdrop_handler","ace/mouse/touch_handler","ace/config"],function(e,t,i){"use strict";var n=e("../lib/event"),o=e("../lib/useragent"),r=e("./default_handlers").DefaultHandlers,s=e("./default_gutter_handler").GutterHandler,a=e("./mouse_event").MouseEvent,l=e("./dragdrop_handler").DragdropHandler,c=e("./touch_handler").addTouchListeners,h=e("../config"),u=function(){function e(e){this.$dragDelay,this.$dragEnabled,this.$mouseMoved,this.mouseEvent,this.$focusTimeout;var t=this;this.editor=e,new r(this),new s(this),new l(this);var i=function(t){document.hasFocus&&document.hasFocus()&&(e.isFocused()||document.activeElement!=(e.textInput&&e.textInput.getElement()))||window.focus(),e.focus(),setTimeout(function(){e.isFocused()||e.focus()})},a=e.renderer.getMouseEventTarget();n.addListener(a,"click",this.onMouseEvent.bind(this,"click"),e),n.addListener(a,"mousemove",this.onMouseMove.bind(this,"mousemove"),e),n.addMultiMouseDownListener([a,e.renderer.scrollBarV&&e.renderer.scrollBarV.inner,e.renderer.scrollBarH&&e.renderer.scrollBarH.inner,e.textInput&&e.textInput.getElement()].filter(Boolean),[400,300,250],this,"onMouseEvent",e),n.addMouseWheelListener(e.container,this.onMouseWheel.bind(this,"mousewheel"),e),c(e.container,e);var h=e.renderer.$gutter;n.addListener(h,"mousedown",this.onMouseEvent.bind(this,"guttermousedown"),e),n.addListener(h,"click",this.onMouseEvent.bind(this,"gutterclick"),e),n.addListener(h,"dblclick",this.onMouseEvent.bind(this,"gutterdblclick"),e),n.addListener(h,"mousemove",this.onMouseEvent.bind(this,"guttermousemove"),e),n.addListener(a,"mousedown",i,e),n.addListener(h,"mousedown",i,e),o.isIE&&e.renderer.scrollBarV&&(n.addListener(e.renderer.scrollBarV.element,"mousedown",i,e),n.addListener(e.renderer.scrollBarH.element,"mousedown",i,e)),e.on("mousemove",function(i){if(!t.state&&!t.$dragDelay&&t.$dragEnabled){var n=e.renderer.screenToTextCoordinates(i.x,i.y),o=e.session.selection.getRange(),r=e.renderer;!o.isEmpty()&&o.insideStart(n.row,n.column)?r.setCursorStyle("default"):r.setCursorStyle("")}},e)}return e.prototype.onMouseEvent=function(e,t){this.editor.session&&this.editor._emit(e,new a(t,this.editor))},e.prototype.onMouseMove=function(e,t){var i=this.editor._eventRegistry&&this.editor._eventRegistry.mousemove;i&&i.length&&this.editor._emit(e,new a(t,this.editor))},e.prototype.onMouseWheel=function(e,t){var i=new a(t,this.editor);i.speed=2*this.$scrollSpeed,i.wheelX=t.wheelX,i.wheelY=t.wheelY,this.editor._emit(e,i)},e.prototype.setState=function(e){this.state=e},e.prototype.captureMouse=function(e,t){this.x=e.x,this.y=e.y,this.isMousePressed=!0;var i=this.editor,r=this.editor.renderer;r.$isMousePressed=!0;var s=this,l=!0,c=function(e){if(e){if(o.isWebKit&&!e.which&&s.releaseMouse)return s.releaseMouse();s.x=e.clientX,s.y=e.clientY,t&&t(e),s.mouseEvent=new a(e,s.editor),s.$mouseMoved=!0}},h=function(e){i.off("beforeEndOperation",p),l=!1,i.session&&u(),s[s.state+"End"]&&s[s.state+"End"](e),s.state="",s.isMousePressed=r.$isMousePressed=!1,r.$keepTextAreaAtCursor&&r.$moveTextAreaToCursor(),s.$onCaptureMouseMove=s.releaseMouse=null,e&&s.onMouseEvent("mouseup",e),i.endOperation()},u=function(){s[s.state]&&s[s.state](),s.$mouseMoved=!1},d=function(){l&&(u(),n.nextFrame(d))};if(o.isOldIE&&"dblclick"==e.domEvent.type)return setTimeout(function(){h(e)});var p=function(e){s.releaseMouse&&i.curOp.command.name&&i.curOp.selectionChanged&&(s[s.state+"End"]&&s[s.state+"End"](),s.state="",s.releaseMouse())};i.on("beforeEndOperation",p),i.startOperation({command:{name:"mouse"}}),s.$onCaptureMouseMove=c,s.releaseMouse=n.capture(this.editor.container,c,h),d()},e.prototype.cancelContextMenu=function(){var e=(function(t){(!t||!t.domEvent||"contextmenu"==t.domEvent.type)&&(this.editor.off("nativecontextmenu",e),t&&t.domEvent&&n.stopEvent(t.domEvent))}).bind(this);setTimeout(e,10),this.editor.on("nativecontextmenu",e)},e.prototype.destroy=function(){this.releaseMouse&&this.releaseMouse()},e}();u.prototype.releaseMouse=null,h.defineOptions(u.prototype,"mouseHandler",{scrollSpeed:{initialValue:2},dragDelay:{initialValue:150*!!o.isMac},dragEnabled:{initialValue:!0},focusTimeout:{initialValue:0},tooltipFollowsMouse:{initialValue:!0}}),t.MouseHandler=u}),ace.define("ace/mouse/fold_handler",["require","exports","module","ace/lib/dom"],function(e,t,i){"use strict";var n=e("../lib/dom");t.FoldHandler=function(e){e.on("click",function(t){var i=t.getDocumentPosition(),o=e.session,r=o.getFoldAt(i.row,i.column,1);r&&(t.getAccelKey()?o.removeFold(r):o.expandFold(r),t.stop());var s=t.domEvent&&t.domEvent.target;s&&n.hasCssClass(s,"ace_inline_button")&&n.hasCssClass(s,"ace_toggle_wrap")&&(o.setOption("wrap",!o.getUseWrapMode()),e.renderer.scrollCursorIntoView())}),e.on("gutterclick",function(t){if("foldWidgets"==e.renderer.$gutterLayer.getRegion(t)){var i=t.getDocumentPosition().row,n=e.session;n.foldWidgets&&n.foldWidgets[i]&&e.session.onFoldWidgetClick(i,t),e.isFocused()||e.focus(),t.stop()}}),e.on("gutterdblclick",function(t){if("foldWidgets"==e.renderer.$gutterLayer.getRegion(t)){var i=t.getDocumentPosition().row,n=e.session,o=n.getParentFoldRangeData(i,!0),r=o.range||o.firstRange;if(r){i=r.start.row;var s=n.getFoldAt(i,n.getLine(i).length,1);s?n.removeFold(s):(n.addFold("...",r),e.renderer.scrollCursorIntoView({row:r.start.row,column:0}))}t.stop()}})}}),ace.define("ace/keyboard/keybinding",["require","exports","module","ace/lib/keys","ace/lib/event"],function(e,t,i){"use strict";var n=e("../lib/keys"),o=e("../lib/event");t.KeyBinding=function(){function e(e){this.$editor=e,this.$data={editor:e},this.$handlers=[],this.setDefaultHandler(e.commands)}return e.prototype.setDefaultHandler=function(e){this.removeKeyboardHandler(this.$defaultHandler),this.$defaultHandler=e,this.addKeyboardHandler(e,0)},e.prototype.setKeyboardHandler=function(e){var t=this.$handlers;if(t[t.length-1]!=e){for(;t[t.length-1]&&t[t.length-1]!=this.$defaultHandler;)this.removeKeyboardHandler(t[t.length-1]);this.addKeyboardHandler(e,1)}},e.prototype.addKeyboardHandler=function(e,t){if(e){"function"!=typeof e||e.handleKeyboard||(e.handleKeyboard=e);var i=this.$handlers.indexOf(e);-1!=i&&this.$handlers.splice(i,1),void 0==t?this.$handlers.push(e):this.$handlers.splice(t,0,e),-1==i&&e.attach&&e.attach(this.$editor)}},e.prototype.removeKeyboardHandler=function(e){var t=this.$handlers.indexOf(e);return -1!=t&&(this.$handlers.splice(t,1),e.detach&&e.detach(this.$editor),!0)},e.prototype.getKeyboardHandler=function(){return this.$handlers[this.$handlers.length-1]},e.prototype.getStatusText=function(){var e=this.$data,t=e.editor;return this.$handlers.map(function(i){return i.getStatusText&&i.getStatusText(t,e)||""}).filter(Boolean).join(" ")},e.prototype.$callKeyboardHandlers=function(e,t,i,n){for(var r,s=!1,a=this.$editor.commands,l=this.$handlers.length;l--&&(!(r=this.$handlers[l].handleKeyboard(this.$data,e,t,i,n))||!r.command||((s="null"==r.command||a.exec(r.command,this.$editor,r.args,n))&&n&&-1!=e&&!0!=r.passEvent&&!0!=r.command.passEvent&&o.stopEvent(n),!s)););return s||-1!=e||(r={command:"insertstring"},s=a.exec("insertstring",this.$editor,t)),s&&this.$editor._signal&&this.$editor._signal("keyboardActivity",r),s},e.prototype.onCommandKey=function(e,t,i){var o=n.keyCodeToString(i);return this.$callKeyboardHandlers(t,o,i,e)},e.prototype.onTextInput=function(e){return this.$callKeyboardHandlers(-1,e)},e}()}),ace.define("ace/lib/bidiutil",["require","exports","module"],function(e,t,i){"use strict";var n=0,o=0,r=!1,s=!1,a=!1,l=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],c=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],h=[18,18,18,18,18,18,18,18,18,6,5,6,8,5,18,18,18,18,18,18,18,18,18,18,18,18,18,18,5,5,5,6,8,4,4,11,11,11,4,4,4,4,4,10,9,10,9,9,2,2,2,2,2,2,2,2,2,2,9,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,4,4,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,4,4,4,18,18,18,18,18,18,5,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,18,9,4,11,11,11,11,4,4,4,4,0,4,4,18,4,4,11,11,2,2,4,0,4,4,4,2,0,4,4,4,4,4],u=[8,8,8,8,8,8,8,8,8,8,8,18,18,18,0,1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,8,5,13,14,15,16,17,9,11,11,11,11,11,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,9,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,8];function d(e,t,i){if(!(o<e)){if(1==e&&1==n&&!s)return void i.reverse();for(var r,a,l,c,h=i.length,u=0;u<h;){if(t[u]>=e){for(r=u+1;r<h&&t[r]>=e;)r++;for(a=u,l=r-1;a<l;a++,l--)c=i[a],i[a]=i[l],i[l]=c;u=r}u++}}}function p(e){var t=e.charCodeAt(0),i=t>>8;if(0==i)return t>191?0:h[t];if(5==i)return+!!/[\u0591-\u05f4]/.test(e);if(6==i)if(/[\u0610-\u061a\u064b-\u065f\u06d6-\u06e4\u06e7-\u06ed]/.test(e))return 12;else if(/[\u0660-\u0669\u066b-\u066c]/.test(e))return 3;else if(1642==t)return 11;else if(/[\u06f0-\u06f9]/.test(e))return 2;else return 7;if(32==i&&t<=8287)return u[255&t];if(254==i)return t>=65136?7:4;return 4}t.L=0,t.R=1,t.EN=2,t.ON_R=3,t.AN=4,t.R_H=5,t.B=6,t.RLE=7,t.DOT="\xb7",t.doBidiReorder=function(e,i,h){if(e.length<2)return{};var u=e.split(""),g=Array(u.length),f=Array(u.length),m=[];n=+!!h,function(e,t,i,h){var u=n?c:l,d=null,g=null,f=null,m=0,y=null,v=null,w=-1,b=null,$=null,C=[];if(!h)for(b=0,h=[];b<i;b++)h[b]=p(e[b]);for($=0,o=n,r=!1,s=!1,a=!1;$<i;$++){if(d=m,C[$]=g=function(e,t,i,o){var l,c,h,u,d=t[o];switch(d){case 0:case 1:r=!1;case 4:case 3:return d;case 2:return r?3:2;case 7:return r=!0,1;case 8:return 4;case 9:if(o<1||o+1>=t.length||2!=(l=i[o-1])&&3!=l||2!=(c=t[o+1])&&3!=c)return 4;return r&&(c=3),c==l?c:4;case 10:if(2==(l=o>0?i[o-1]:5)&&o+1<t.length&&2==t[o+1])return 2;return 4;case 11:if(o>0&&2==i[o-1])return 2;if(r)return 4;for(u=o+1,h=t.length;u<h&&11==t[u];)u++;if(u<h&&2==t[u])return 2;return 4;case 12:for(h=t.length,u=o+1;u<h&&12==t[u];)u++;if(u<h){var p=e[o];if(l=t[u],(p>=1425&&p<=2303||64286==p)&&(1==l||7==l))return 1}if(o<1||5==(l=t[o-1]))return 4;return i[o-1];case 5:return r=!1,s=!0,n;case 6:return a=!0,4;case 13:case 14:case 16:case 17:case 15:r=!1;case 18:return 4}}(e,h,C,$),y=240&(m=u[d][g]),m&=15,t[$]=f=u[m][5],y>0)if(16==y){for(b=w;b<$;b++)t[b]=1;w=-1}else w=-1;if(u[m][6])-1==w&&(w=$);else if(w>-1){for(b=w;b<$;b++)t[b]=f;w=-1}5==h[$]&&(t[$]=0),o|=f}if(a){for(b=0;b<i;b++)if(6==h[b]){t[b]=n;for(var S=b-1;S>=0;S--)if(8==h[S])t[S]=n;else break}}}(u,m,u.length,i);for(var y=0;y<g.length;g[y]=y,y++);d(2,m,g),d(1,m,g);for(var y=0;y<g.length-1;y++)3===i[y]?m[y]=t.AN:1===m[y]&&(i[y]>7&&i[y]<13||4===i[y]||18===i[y])?m[y]=t.ON_R:y>0&&"ل"===u[y-1]&&/\u0622|\u0623|\u0625|\u0627/.test(u[y])&&(m[y-1]=m[y]=t.R_H,y++);u[u.length-1]===t.DOT&&(m[u.length-1]=t.B),"‫"===u[0]&&(m[0]=t.RLE);for(var y=0;y<g.length;y++)f[y]=m[g[y]];return{logicalFromVisual:g,bidiLevels:f}},t.hasBidiCharacters=function(e,t){for(var i=!1,n=0;n<e.length;n++)t[n]=p(e.charAt(n)),i||1!=t[n]&&7!=t[n]&&3!=t[n]||(i=!0);return i},t.getVisualFromLogicalIdx=function(e,t){for(var i=0;i<t.logicalFromVisual.length;i++)if(t.logicalFromVisual[i]==e)return i;return 0}}),ace.define("ace/bidihandler",["require","exports","module","ace/lib/bidiutil","ace/lib/lang"],function(e,t,i){"use strict";var n=e("./lib/bidiutil"),o=e("./lib/lang"),r=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\u202B]/;t.BidiHandler=function(){function e(e){this.session=e,this.bidiMap={},this.currentRow=null,this.bidiUtil=n,this.charWidths=[],this.EOL="\xac",this.showInvisibles=!0,this.isRtlDir=!1,this.$isRtl=!1,this.line="",this.wrapIndent=0,this.EOF="\xb6",this.RLE="‫",this.contentWidth=0,this.fontMetrics=null,this.rtlLineOffset=0,this.wrapOffset=0,this.isMoveLeftOperation=!1,this.seenBidi=r.test(e.getValue())}return e.prototype.isBidiRow=function(e,t,i){return!!this.seenBidi&&(e!==this.currentRow&&(this.currentRow=e,this.updateRowLine(t,i),this.updateBidiMap()),this.bidiMap.bidiLevels)},e.prototype.onChange=function(e){this.seenBidi?this.currentRow=null:"insert"==e.action&&r.test(e.lines.join("\n"))&&(this.seenBidi=!0,this.currentRow=null)},e.prototype.getDocumentRow=function(){var e=0,t=this.session.$screenRowCache;if(t.length){var i=this.session.$getRowCacheIndex(t,this.currentRow);i>=0&&(e=this.session.$docRowCache[i])}return e},e.prototype.getSplitIndex=function(){var e=0,t=this.session.$screenRowCache;if(t.length)for(var i,n=this.session.$getRowCacheIndex(t,this.currentRow);this.currentRow-e>0&&(i=this.session.$getRowCacheIndex(t,this.currentRow-e-1))===n;)n=i,e++;else e=this.currentRow;return e},e.prototype.updateRowLine=function(e,t){void 0===e&&(e=this.getDocumentRow());var i=e===this.session.getLength()-1?this.EOF:this.EOL;if(this.wrapIndent=0,this.line=this.session.getLine(e),this.isRtlDir=this.$isRtl||this.line.charAt(0)===this.RLE,this.session.$useWrapMode){var r=this.session.$wrapData[e];r&&(void 0===t&&(t=this.getSplitIndex()),t>0&&r.length?(this.wrapIndent=r.indent,this.wrapOffset=this.wrapIndent*this.charWidths[n.L],this.line=t<r.length?this.line.substring(r[t-1],r[t]):this.line.substring(r[r.length-1])):this.line=this.line.substring(0,r[t]),t==r.length&&(this.line+=this.showInvisibles?i:n.DOT))}else this.line+=this.showInvisibles?i:n.DOT;var s,a=this.session,l=0;this.line=this.line.replace(/\t|[\u1100-\u2029, \u202F-\uFFE6]/g,function(e,t){return"	"===e||a.isFullWidth(e.charCodeAt(0))?(s="	"===e?a.getScreenTabSize(t+l):2,l+=s-1,o.stringRepeat(n.DOT,s)):e}),this.isRtlDir&&(this.fontMetrics.$main.textContent=this.line.charAt(this.line.length-1)==n.DOT?this.line.substr(0,this.line.length-1):this.line,this.rtlLineOffset=this.contentWidth-this.fontMetrics.$main.getBoundingClientRect().width)},e.prototype.updateBidiMap=function(){var e=[];n.hasBidiCharacters(this.line,e)||this.isRtlDir?this.bidiMap=n.doBidiReorder(this.line,e,this.isRtlDir):this.bidiMap={}},e.prototype.markAsDirty=function(){this.currentRow=null},e.prototype.updateCharacterWidths=function(e){if(this.characterWidth!==e.$characterSize.width){this.fontMetrics=e;var t=this.characterWidth=e.$characterSize.width,i=e.$measureCharWidth("ה");this.charWidths[n.L]=this.charWidths[n.EN]=this.charWidths[n.ON_R]=t,this.charWidths[n.R]=this.charWidths[n.AN]=i,this.charWidths[n.R_H]=.45*i,this.charWidths[n.B]=this.charWidths[n.RLE]=0,this.currentRow=null}},e.prototype.setShowInvisibles=function(e){this.showInvisibles=e,this.currentRow=null},e.prototype.setEolChar=function(e){this.EOL=e},e.prototype.setContentWidth=function(e){this.contentWidth=e},e.prototype.isRtlLine=function(e){return!!this.$isRtl||(void 0!=e?this.session.getLine(e).charAt(0)==this.RLE:this.isRtlDir)},e.prototype.setRtlDirection=function(e,t){for(var i=e.getCursorPosition(),n=e.selection.getSelectionAnchor().row;n<=i.row;n++)t||e.session.getLine(n).charAt(0)!==e.session.$bidiHandler.RLE?t&&e.session.getLine(n).charAt(0)!==e.session.$bidiHandler.RLE&&e.session.doc.insert({column:0,row:n},e.session.$bidiHandler.RLE):e.session.doc.removeInLine(n,0,1)},e.prototype.getPosLeft=function(e){e-=this.wrapIndent;var t=+(this.line.charAt(0)===this.RLE),i=e>t?this.session.getOverwrite()?e:e-1:t,o=n.getVisualFromLogicalIdx(i,this.bidiMap),r=this.bidiMap.bidiLevels,s=0;!this.session.getOverwrite()&&e<=t&&r[o]%2!=0&&o++;for(var a=0;a<o;a++)s+=this.charWidths[r[a]];return!this.session.getOverwrite()&&e>t&&r[o]%2==0&&(s+=this.charWidths[r[o]]),this.wrapIndent&&(s+=this.isRtlDir?-1*this.wrapOffset:this.wrapOffset),this.isRtlDir&&(s+=this.rtlLineOffset),s},e.prototype.getSelections=function(e,t){var i,n=this.bidiMap,o=n.bidiLevels,r=[],s=0,a=Math.min(e,t)-this.wrapIndent,l=Math.max(e,t)-this.wrapIndent,c=!1,h=!1,u=0;this.wrapIndent&&(s+=this.isRtlDir?-1*this.wrapOffset:this.wrapOffset);for(var d,p=0;p<o.length;p++)d=n.logicalFromVisual[p],i=o[p],(c=d>=a&&d<l)&&!h?u=s:!c&&h&&r.push({left:u,width:s-u}),s+=this.charWidths[i],h=c;if(c&&p===o.length&&r.push({left:u,width:s-u}),this.isRtlDir)for(var g=0;g<r.length;g++)r[g].left+=this.rtlLineOffset;return r},e.prototype.offsetToCol=function(e){this.isRtlDir&&(e-=this.rtlLineOffset);var t=0,e=Math.max(e,0),i=0,n=0,o=this.bidiMap.bidiLevels,r=this.charWidths[o[n]];for(this.wrapIndent&&(e-=this.isRtlDir?-1*this.wrapOffset:this.wrapOffset);e>i+r/2;){if(i+=r,n===o.length-1){r=0;break}r=this.charWidths[o[++n]]}return n>0&&o[n-1]%2!=0&&o[n]%2==0?(e<i&&n--,t=this.bidiMap.logicalFromVisual[n]):n>0&&o[n-1]%2==0&&o[n]%2!=0?t=1+(e>i?this.bidiMap.logicalFromVisual[n]:this.bidiMap.logicalFromVisual[n-1]):this.isRtlDir&&n===o.length-1&&0===r&&o[n-1]%2==0||!this.isRtlDir&&0===n&&o[n]%2!=0?t=1+this.bidiMap.logicalFromVisual[n]:(n>0&&o[n-1]%2!=0&&0!==r&&n--,t=this.bidiMap.logicalFromVisual[n]),0===t&&this.isRtlDir&&t++,t+this.wrapIndent},e}()}),ace.define("ace/selection",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/lib/event_emitter","ace/range"],function(e,t,i){"use strict";var n=e("./lib/oop"),o=e("./lib/lang"),r=e("./lib/event_emitter").EventEmitter,s=e("./range").Range,a=function(){function e(e){this.session=e,this.doc=e.getDocument(),this.clearSelection(),this.cursor=this.lead=this.doc.createAnchor(0,0),this.anchor=this.doc.createAnchor(0,0),this.$silent=!1;var t=this;this.cursor.on("change",function(e){t.$cursorChanged=!0,t.$silent||t._emit("changeCursor"),t.$isEmpty||t.$silent||t._emit("changeSelection"),t.$keepDesiredColumnOnChange||e.old.column==e.value.column||(t.$desiredColumn=null)}),this.anchor.on("change",function(){t.$anchorChanged=!0,t.$isEmpty||t.$silent||t._emit("changeSelection")})}return e.prototype.isEmpty=function(){return this.$isEmpty||this.anchor.row==this.lead.row&&this.anchor.column==this.lead.column},e.prototype.isMultiLine=function(){return!this.$isEmpty&&this.anchor.row!=this.cursor.row},e.prototype.getCursor=function(){return this.lead.getPosition()},e.prototype.setAnchor=function(e,t){this.$isEmpty=!1,this.anchor.setPosition(e,t)},e.prototype.getAnchor=function(){return this.$isEmpty?this.getSelectionLead():this.anchor.getPosition()},e.prototype.getSelectionLead=function(){return this.lead.getPosition()},e.prototype.isBackwards=function(){var e=this.anchor,t=this.lead;return e.row>t.row||e.row==t.row&&e.column>t.column},e.prototype.getRange=function(){var e=this.anchor,t=this.lead;return this.$isEmpty?s.fromPoints(t,t):this.isBackwards()?s.fromPoints(t,e):s.fromPoints(e,t)},e.prototype.clearSelection=function(){this.$isEmpty||(this.$isEmpty=!0,this._emit("changeSelection"))},e.prototype.selectAll=function(){this.$setSelection(0,0,Number.MAX_VALUE,Number.MAX_VALUE)},e.prototype.setRange=function(e,t){var i=t?e.end:e.start,n=t?e.start:e.end;this.$setSelection(i.row,i.column,n.row,n.column)},e.prototype.$setSelection=function(e,t,i,n){if(!this.$silent){var o=this.$isEmpty,r=this.inMultiSelectMode;this.$silent=!0,this.$cursorChanged=this.$anchorChanged=!1,this.anchor.setPosition(e,t),this.cursor.setPosition(i,n),this.$isEmpty=!s.comparePoints(this.anchor,this.cursor),this.$silent=!1,this.$cursorChanged&&this._emit("changeCursor"),(this.$cursorChanged||this.$anchorChanged||o!=this.$isEmpty||r)&&this._emit("changeSelection")}},e.prototype.$moveSelection=function(e){var t=this.lead;this.$isEmpty&&this.setSelectionAnchor(t.row,t.column),e.call(this)},e.prototype.selectTo=function(e,t){this.$moveSelection(function(){this.moveCursorTo(e,t)})},e.prototype.selectToPosition=function(e){this.$moveSelection(function(){this.moveCursorToPosition(e)})},e.prototype.moveTo=function(e,t){this.clearSelection(),this.moveCursorTo(e,t)},e.prototype.moveToPosition=function(e){this.clearSelection(),this.moveCursorToPosition(e)},e.prototype.selectUp=function(){this.$moveSelection(this.moveCursorUp)},e.prototype.selectDown=function(){this.$moveSelection(this.moveCursorDown)},e.prototype.selectRight=function(){this.$moveSelection(this.moveCursorRight)},e.prototype.selectLeft=function(){this.$moveSelection(this.moveCursorLeft)},e.prototype.selectLineStart=function(){this.$moveSelection(this.moveCursorLineStart)},e.prototype.selectLineEnd=function(){this.$moveSelection(this.moveCursorLineEnd)},e.prototype.selectFileEnd=function(){this.$moveSelection(this.moveCursorFileEnd)},e.prototype.selectFileStart=function(){this.$moveSelection(this.moveCursorFileStart)},e.prototype.selectWordRight=function(){this.$moveSelection(this.moveCursorWordRight)},e.prototype.selectWordLeft=function(){this.$moveSelection(this.moveCursorWordLeft)},e.prototype.getWordRange=function(e,t){if(void 0===t){var i=e||this.lead;e=i.row,t=i.column}return this.session.getWordRange(e,t)},e.prototype.selectWord=function(){this.setSelectionRange(this.getWordRange())},e.prototype.selectAWord=function(){var e=this.getCursor(),t=this.session.getAWordRange(e.row,e.column);this.setSelectionRange(t)},e.prototype.getLineRange=function(e,t){var i,n="number"==typeof e?e:this.lead.row,o=this.session.getFoldLine(n);return(o?(n=o.start.row,i=o.end.row):i=n,!0===t)?new s(n,0,i,this.session.getLine(i).length):new s(n,0,i+1,0)},e.prototype.selectLine=function(){this.setSelectionRange(this.getLineRange())},e.prototype.moveCursorUp=function(){this.moveCursorBy(-1,0)},e.prototype.moveCursorDown=function(){this.moveCursorBy(1,0)},e.prototype.wouldMoveIntoSoftTab=function(e,t,i){var n=e.column,o=e.column+t;return i<0&&(n=e.column-t,o=e.column),this.session.isTabStop(e)&&this.doc.getLine(e.row).slice(n,o).split(" ").length-1==t},e.prototype.moveCursorLeft=function(){var e,t=this.lead.getPosition();if(e=this.session.getFoldAt(t.row,t.column,-1))this.moveCursorTo(e.start.row,e.start.column);else if(0===t.column)t.row>0&&this.moveCursorTo(t.row-1,this.doc.getLine(t.row-1).length);else{var i=this.session.getTabSize();this.wouldMoveIntoSoftTab(t,i,-1)&&!this.session.getNavigateWithinSoftTabs()?this.moveCursorBy(0,-i):this.moveCursorBy(0,-1)}},e.prototype.moveCursorRight=function(){var e,t=this.lead.getPosition();if(e=this.session.getFoldAt(t.row,t.column,1))this.moveCursorTo(e.end.row,e.end.column);else if(this.lead.column==this.doc.getLine(this.lead.row).length)this.lead.row<this.doc.getLength()-1&&this.moveCursorTo(this.lead.row+1,0);else{var i=this.session.getTabSize(),t=this.lead;this.wouldMoveIntoSoftTab(t,i,1)&&!this.session.getNavigateWithinSoftTabs()?this.moveCursorBy(0,i):this.moveCursorBy(0,1)}},e.prototype.moveCursorLineStart=function(){var e=this.lead.row,t=this.lead.column,i=this.session.documentToScreenRow(e,t),n=this.session.screenToDocumentPosition(i,0),o=this.session.getDisplayLine(e,null,n.row,n.column).match(/^\s*/);o[0].length==t||this.session.$useEmacsStyleLineStart||(n.column+=o[0].length),this.moveCursorToPosition(n)},e.prototype.moveCursorLineEnd=function(){var e=this.lead,t=this.session.getDocumentLastRowColumnPosition(e.row,e.column);if(this.lead.column==t.column){var i=this.session.getLine(t.row);if(t.column==i.length){var n=i.search(/\s+$/);n>0&&(t.column=n)}}this.moveCursorTo(t.row,t.column)},e.prototype.moveCursorFileEnd=function(){var e=this.doc.getLength()-1,t=this.doc.getLine(e).length;this.moveCursorTo(e,t)},e.prototype.moveCursorFileStart=function(){this.moveCursorTo(0,0)},e.prototype.moveCursorLongWordRight=function(){var e=this.lead.row,t=this.lead.column,i=this.doc.getLine(e),n=i.substring(t);this.session.nonTokenRe.lastIndex=0,this.session.tokenRe.lastIndex=0;var o=this.session.getFoldAt(e,t,1);if(o)return void this.moveCursorTo(o.end.row,o.end.column);if(this.session.nonTokenRe.exec(n)&&(t+=this.session.nonTokenRe.lastIndex,this.session.nonTokenRe.lastIndex=0,n=i.substring(t)),t>=i.length){this.moveCursorTo(e,i.length),this.moveCursorRight(),e<this.doc.getLength()-1&&this.moveCursorWordRight();return}this.session.tokenRe.exec(n)&&(t+=this.session.tokenRe.lastIndex,this.session.tokenRe.lastIndex=0),this.moveCursorTo(e,t)},e.prototype.moveCursorLongWordLeft=function(){var e,t=this.lead.row,i=this.lead.column;if(e=this.session.getFoldAt(t,i,-1))return void this.moveCursorTo(e.start.row,e.start.column);var n=this.session.getFoldStringAt(t,i,-1);null==n&&(n=this.doc.getLine(t).substring(0,i));var r=o.stringReverse(n);if(this.session.nonTokenRe.lastIndex=0,this.session.tokenRe.lastIndex=0,this.session.nonTokenRe.exec(r)&&(i-=this.session.nonTokenRe.lastIndex,r=r.slice(this.session.nonTokenRe.lastIndex),this.session.nonTokenRe.lastIndex=0),i<=0){this.moveCursorTo(t,0),this.moveCursorLeft(),t>0&&this.moveCursorWordLeft();return}this.session.tokenRe.exec(r)&&(i-=this.session.tokenRe.lastIndex,this.session.tokenRe.lastIndex=0),this.moveCursorTo(t,i)},e.prototype.$shortWordEndIndex=function(e){var t,i=0,n=/\s/,o=this.session.tokenRe;if(o.lastIndex=0,this.session.tokenRe.exec(e))i=this.session.tokenRe.lastIndex;else{for(;(t=e[i])&&n.test(t);)i++;if(i<1){for(o.lastIndex=0;(t=e[i])&&!o.test(t);)if(o.lastIndex=0,i++,n.test(t))if(i>2){i--;break}else{for(;(t=e[i])&&n.test(t);)i++;if(i>2)break}}}return o.lastIndex=0,i},e.prototype.moveCursorShortWordRight=function(){var e=this.lead.row,t=this.lead.column,i=this.doc.getLine(e),n=i.substring(t),o=this.session.getFoldAt(e,t,1);if(o)return this.moveCursorTo(o.end.row,o.end.column);if(t==i.length){var r=this.doc.getLength();do e++,n=this.doc.getLine(e);while(e<r&&/^\s*$/.test(n));/^\s+/.test(n)||(n=""),t=0}var s=this.$shortWordEndIndex(n);this.moveCursorTo(e,t+s)},e.prototype.moveCursorShortWordLeft=function(){var e,t=this.lead.row,i=this.lead.column;if(e=this.session.getFoldAt(t,i,-1))return this.moveCursorTo(e.start.row,e.start.column);var n=this.session.getLine(t).substring(0,i);if(0===i){do t--,n=this.doc.getLine(t);while(t>0&&/^\s*$/.test(n));i=n.length,/\s+$/.test(n)||(n="")}var r=o.stringReverse(n),s=this.$shortWordEndIndex(r);return this.moveCursorTo(t,i-s)},e.prototype.moveCursorWordRight=function(){this.session.$selectLongWords?this.moveCursorLongWordRight():this.moveCursorShortWordRight()},e.prototype.moveCursorWordLeft=function(){this.session.$selectLongWords?this.moveCursorLongWordLeft():this.moveCursorShortWordLeft()},e.prototype.moveCursorBy=function(e,t){var i,n=this.session.documentToScreenPosition(this.lead.row,this.lead.column);if(0===t&&(0!==e&&(this.session.$bidiHandler.isBidiRow(n.row,this.lead.row)?(i=this.session.$bidiHandler.getPosLeft(n.column),n.column=Math.round(i/this.session.$bidiHandler.charWidths[0])):i=n.column*this.session.$bidiHandler.charWidths[0]),this.$desiredColumn?n.column=this.$desiredColumn:this.$desiredColumn=n.column),0!=e&&this.session.lineWidgets&&this.session.lineWidgets[this.lead.row]){var o=this.session.lineWidgets[this.lead.row];e<0?e-=o.rowsAbove||0:e>0&&(e+=o.rowCount-(o.rowsAbove||0))}var r=this.session.screenToDocumentPosition(n.row+e,n.column,i);0!==e&&0===t&&r.row===this.lead.row&&(r.column,this.lead.column),this.moveCursorTo(r.row,r.column+t,0===t)},e.prototype.moveCursorToPosition=function(e){this.moveCursorTo(e.row,e.column)},e.prototype.moveCursorTo=function(e,t,i){var n=this.session.getFoldAt(e,t,1);n&&(e=n.start.row,t=n.start.column),this.$keepDesiredColumnOnChange=!0;var o=this.session.getLine(e);/[\uDC00-\uDFFF]/.test(o.charAt(t))&&o.charAt(t-1)&&(this.lead.row==e&&this.lead.column==t+1?t-=1:t+=1),this.lead.setPosition(e,t),this.$keepDesiredColumnOnChange=!1,i||(this.$desiredColumn=null)},e.prototype.moveCursorToScreen=function(e,t,i){var n=this.session.screenToDocumentPosition(e,t);this.moveCursorTo(n.row,n.column,i)},e.prototype.detach=function(){this.lead.detach(),this.anchor.detach()},e.prototype.fromOrientedRange=function(e){this.setSelectionRange(e,e.cursor==e.start),this.$desiredColumn=e.desiredColumn||this.$desiredColumn},e.prototype.toOrientedRange=function(e){var t=this.getRange();return e?(e.start.column=t.start.column,e.start.row=t.start.row,e.end.column=t.end.column,e.end.row=t.end.row):e=t,e.cursor=this.isBackwards()?e.start:e.end,e.desiredColumn=this.$desiredColumn,e},e.prototype.getRangeOfMovements=function(e){var t=this.getCursor();try{e(this);var i=this.getCursor();return s.fromPoints(t,i)}catch(e){return s.fromPoints(t,t)}finally{this.moveCursorToPosition(t)}},e.prototype.toJSON=function(){if(this.rangeCount)var e=this.ranges.map(function(e){var t=e.clone();return t.isBackwards=e.cursor==e.start,t});else{var e=this.getRange();e.isBackwards=this.isBackwards()}return e},e.prototype.fromJSON=function(e){if(void 0==e.start)if(this.rangeList&&e.length>1){this.toSingleRange(e[0]);for(var t=e.length;t--;){var i=s.fromPoints(e[t].start,e[t].end);e[t].isBackwards&&(i.cursor=i.start),this.addRange(i,!0)}return}else e=e[0];this.rangeList&&this.toSingleRange(e),this.setSelectionRange(e,e.isBackwards)},e.prototype.isEqual=function(e){if((e.length||this.rangeCount)&&e.length!=this.rangeCount)return!1;if(!e.length||!this.ranges)return this.getRange().isEqual(e);for(var t=this.ranges.length;t--;)if(!this.ranges[t].isEqual(e[t]))return!1;return!0},e}();a.prototype.setSelectionAnchor=a.prototype.setAnchor,a.prototype.getSelectionAnchor=a.prototype.getAnchor,a.prototype.setSelectionRange=a.prototype.setRange,n.implement(a.prototype,r),t.Selection=a}),ace.define("ace/tokenizer",["require","exports","module","ace/lib/report_error"],function(e,t,i){"use strict";var n=e("./lib/report_error").reportError,o=2e3,r=function(){function e(e){for(var t in this.splitRegex,this.states=e,this.regExps={},this.matchMappings={},this.states){for(var i=this.states[t],n=[],o=0,r=this.matchMappings[t]={defaultToken:"text"},s="g",a=[],l=0;l<i.length;l++){var c=i[l];if(c.defaultToken&&(r.defaultToken=c.defaultToken),c.caseInsensitive&&-1===s.indexOf("i")&&(s+="i"),c.unicode&&-1===s.indexOf("u")&&(s+="u"),null!=c.regex){c.regex instanceof RegExp&&(c.regex=c.regex.toString().slice(1,-1));var h=c.regex,u=RegExp("(?:("+h+")|(.))").exec("a").length-2;Array.isArray(c.token)?1==c.token.length||1==u?c.token=c.token[0]:u-1!=c.token.length?(this.reportError("number of classes and regexp groups doesn't match",{rule:c,groupCount:u-1}),c.token=c.token[0]):(c.tokenArray=c.token,c.token=null,c.onMatch=this.$arrayTokens):"function"!=typeof c.token||c.onMatch||(u>1?c.onMatch=this.$applyToken:c.onMatch=c.token),u>1&&(/\\\d/.test(c.regex)?h=c.regex.replace(/\\([0-9]+)/g,function(e,t){return"\\"+(parseInt(t,10)+o+1)}):(u=1,h=this.removeCapturingGroups(c.regex)),c.splitRegex||"string"==typeof c.token||a.push(c)),r[o]=l,o+=u,n.push(h),c.onMatch||(c.onMatch=null)}}n.length||(r[0]=0,n.push("$")),a.forEach(function(e){e.splitRegex=this.createSplitterRegexp(e.regex,s)},this),this.regExps[t]=RegExp("("+n.join(")|(")+")|($)",s)}}return e.prototype.$setMaxTokenCount=function(e){o=0|e},e.prototype.$applyToken=function(e){var t=this.splitRegex.exec(e).slice(1),i=this.token.apply(this,t);if("string"==typeof i)return[{type:i,value:e}];for(var n=[],o=0,r=i.length;o<r;o++)t[o]&&(n[n.length]={type:i[o],value:t[o]});return n},e.prototype.$arrayTokens=function(e){if(!e)return[];var t=this.splitRegex.exec(e);if(!t)return"text";for(var i=[],n=this.tokenArray,o=0,r=n.length;o<r;o++)t[o+1]&&(i[i.length]={type:n[o],value:t[o+1]});return i},e.prototype.removeCapturingGroups=function(e){return e.replace(/\\.|\[(?:\\.|[^\\\]])*|\(\?[:=!<]|(\()/g,function(e,t){return t?"(?:":e})},e.prototype.createSplitterRegexp=function(e,t){if(-1!=e.indexOf("(?=")){var i=0,n=!1,o={};e.replace(/(\\.)|(\((?:\?[=!])?)|(\))|([\[\]])/g,function(e,t,r,s,a,l){return n?n="]"!=a:a?n=!0:s?(i==o.stack&&(o.end=l+1,o.stack=-1),i--):r&&(i++,1!=r.length&&(o.stack=i,o.start=l)),e}),null!=o.end&&/^\)*$/.test(e.substr(o.end))&&(e=e.substring(0,o.start)+e.substr(o.end))}return"^"!=e.charAt(0)&&(e="^"+e),"$"!=e.charAt(e.length-1)&&(e+="$"),new RegExp(e,(t||"").replace("g",""))},e.prototype.getLineTokens=function(e,t){if(t&&"string"!=typeof t){var i=t.slice(0);"#tmp"===(t=i[0])&&(i.shift(),t=i.shift())}else var i=[];var n=t||"start",r=this.states[n];r||(n="start",r=this.states[n]);var s=this.matchMappings[n],a=this.regExps[n];a.lastIndex=0;for(var l,c=[],h=0,u=0,d={type:null,value:""};l=a.exec(e);){var p=s.defaultToken,g=null,f=l[0],m=a.lastIndex;if(m-f.length>h){var y=e.substring(h,m-f.length);d.type==p?d.value+=y:(d.type&&c.push(d),d={type:p,value:y})}for(var v=0;v<l.length-2;v++)if(void 0!==l[v+1]){p=(g=r[s[v]]).onMatch?g.onMatch(f,n,i,e):g.token,g.next&&(n="string"==typeof g.next?g.next:g.next(n,i),(r=this.states[n])||(this.reportError("state doesn't exist",n),n="start",r=this.states[n]),s=this.matchMappings[n],h=m,(a=this.regExps[n]).lastIndex=m),g.consumeLineEnd&&(h=m);break}if(f){if("string"==typeof p)g&&!1===g.merge||d.type!==p?(d.type&&c.push(d),d={type:p,value:f}):d.value+=f;else if(p){d.type&&c.push(d),d={type:null,value:""};for(var v=0;v<p.length;v++)c.push(p[v])}}if(h==e.length)break;if(h=m,u++>o){for(u>2*e.length&&this.reportError("infinite loop with in ace tokenizer",{startState:t,line:e});h<e.length;)d.type&&c.push(d),d={value:e.substring(h,h+=500),type:"overflow"};n="start",i=[];break}}return d.type&&c.push(d),i.length>1&&i[0]!==n&&i.unshift("#tmp",n),{tokens:c,state:i.length?i:n}},e}();r.prototype.reportError=n,t.Tokenizer=r}),ace.define("ace/mode/text_highlight_rules",["require","exports","module","ace/lib/deep_copy"],function(e,t,i){"use strict";var n,o=e("../lib/deep_copy").deepCopy;(function(){this.addRules=function(e,t){if(!t){for(var i in e)this.$rules[i]=e[i];return}for(var i in e){for(var n=e[i],o=0;o<n.length;o++){var r=n[o];(r.next||r.onMatch)&&("string"==typeof r.next&&0!==r.next.indexOf(t)&&(r.next=t+r.next),r.nextState&&0!==r.nextState.indexOf(t)&&(r.nextState=t+r.nextState))}this.$rules[t+i]=n}},this.getRules=function(){return this.$rules},this.embedRules=function(e,t,i,n,r){var s="function"==typeof e?new e().getRules():e;if(n)for(var a=0;a<n.length;a++)n[a]=t+n[a];else for(var l in n=[],s)n.push(t+l);if(this.addRules(s,t),i)for(var c=Array.prototype[r?"push":"unshift"],a=0;a<n.length;a++)c.apply(this.$rules[n[a]],o(i));this.$embeds||(this.$embeds=[]),this.$embeds.push(t)},this.getEmbeds=function(){return this.$embeds};var e=function(e,t){return("start"!=e||t.length)&&t.unshift(this.nextState,e),this.nextState},t=function(e,t){return t.shift(),t.shift()||"start"};this.normalizeRules=function(){var i=0,n=this.$rules;Object.keys(n).forEach(function o(r){var s=n[r];s.processed=!0;for(var a=0;a<s.length;a++){var l=s[a],c=null;Array.isArray(l)&&(c=l,l={}),!l.regex&&l.start&&(l.regex=l.start,l.next||(l.next=[]),l.next.push({defaultToken:l.token},{token:l.token+".end",regex:l.end||l.start,next:"pop"}),l.token=l.token+".start",l.push=!0);var h=l.next||l.push;if(h&&Array.isArray(h)){var u=l.stateName;!u&&("string"!=typeof(u=l.token)&&(u=u[0]||""),n[u]&&(u+=i++)),n[u]=h,l.next=u,o(u)}else"pop"==h&&(l.next=t);if(l.push&&(l.nextState=l.next||l.push,l.next=e,delete l.push),l.rules)for(var d in l.rules)n[d]?n[d].push&&n[d].push.apply(n[d],l.rules[d]):n[d]=l.rules[d];var p="string"==typeof l?l:l.include;if(p&&("$self"===p&&(p="start"),c=Array.isArray(p)?p.map(function(e){return n[e]}):n[p]),c){var g=[a,1].concat(c);l.noEscape&&(g=g.filter(function(e){return!e.next})),s.splice.apply(s,g),a--}l.keywordMap&&(l.token=this.createKeywordMapper(l.keywordMap,l.defaultToken||"text",l.caseInsensitive),delete l.defaultToken)}},this)},this.createKeywordMapper=function(e,t,i,n){var o=Object.create(null);return this.$keywordList=[],Object.keys(e).forEach(function(t){for(var r=e[t].split(n||"|"),s=r.length;s--;){var a=r[s];this.$keywordList.push(a),i&&(a=a.toLowerCase()),o[a]=t}},this),e=null,i?function(e){return o[e.toLowerCase()]||t}:function(e){return o[e]||t}},this.getKeywords=function(){return this.$keywords}}).call((n=function(){this.$rules={start:[{token:"empty_line",regex:"^$"},{defaultToken:"text"}]}}).prototype),t.TextHighlightRules=n}),ace.define("ace/mode/behaviour",["require","exports","module"],function(e,t,i){"use strict";var n;(function(){this.add=function(e,t,i){switch(void 0){case this.$behaviours:this.$behaviours={};case this.$behaviours[e]:this.$behaviours[e]={}}this.$behaviours[e][t]=i},this.addBehaviours=function(e){for(var t in e)for(var i in e[t])this.add(t,i,e[t][i])},this.remove=function(e){this.$behaviours&&this.$behaviours[e]&&delete this.$behaviours[e]},this.inherit=function(e,t){if("function"==typeof e)var i=new e().getBehaviours(t);else var i=e.getBehaviours(t);this.addBehaviours(i)},this.getBehaviours=function(e){if(!e)return this.$behaviours;for(var t={},i=0;i<e.length;i++)this.$behaviours[e[i]]&&(t[e[i]]=this.$behaviours[e[i]]);return t}}).call((n=function(){this.$behaviours={}}).prototype),t.Behaviour=n}),ace.define("ace/token_iterator",["require","exports","module","ace/range"],function(e,t,i){"use strict";var n=e("./range").Range;t.TokenIterator=function(){function e(e,t,i){this.$session=e,this.$row=t,this.$rowTokens=e.getTokens(t);var n=e.getTokenAt(t,i);this.$tokenIndex=n?n.index:-1}return e.prototype.stepBackward=function(){for(this.$tokenIndex-=1;this.$tokenIndex<0;){if(this.$row-=1,this.$row<0)return this.$row=0,null;this.$rowTokens=this.$session.getTokens(this.$row),this.$tokenIndex=this.$rowTokens.length-1}return this.$rowTokens[this.$tokenIndex]},e.prototype.stepForward=function(){var e;for(this.$tokenIndex+=1;this.$tokenIndex>=this.$rowTokens.length;){if(this.$row+=1,e||(e=this.$session.getLength()),this.$row>=e)return this.$row=e-1,null;this.$rowTokens=this.$session.getTokens(this.$row),this.$tokenIndex=0}return this.$rowTokens[this.$tokenIndex]},e.prototype.getCurrentToken=function(){return this.$rowTokens[this.$tokenIndex]},e.prototype.getCurrentTokenRow=function(){return this.$row},e.prototype.getCurrentTokenColumn=function(){var e=this.$rowTokens,t=this.$tokenIndex,i=e[t].start;if(void 0!==i)return i;for(i=0;t>0;)t-=1,i+=e[t].value.length;return i},e.prototype.getCurrentTokenPosition=function(){return{row:this.$row,column:this.getCurrentTokenColumn()}},e.prototype.getCurrentTokenRange=function(){var e=this.$rowTokens[this.$tokenIndex],t=this.getCurrentTokenColumn();return new n(this.$row,t,this.$row,t+e.value.length)},e}()}),ace.define("ace/mode/behaviour/cstyle",["require","exports","module","ace/lib/oop","ace/mode/behaviour","ace/token_iterator","ace/lib/lang"],function(e,t,i){"use strict";var n,o,r=e("../../lib/oop"),s=e("../behaviour").Behaviour,a=e("../../token_iterator").TokenIterator,l=e("../../lib/lang"),c=["text","paren.rparen","rparen","paren","punctuation.operator"],h=["text","paren.rparen","rparen","paren","punctuation.operator","comment"],u={},d={'"':'"',"'":"'"},p=function(e){var t=-1;if(e.multiSelect&&(t=e.selection.index,u.rangeCount!=e.multiSelect.rangeCount&&(u={rangeCount:e.multiSelect.rangeCount})),u[t])return n=u[t];n=u[t]={autoInsertedBrackets:0,autoInsertedRow:-1,autoInsertedLineEnd:"",maybeInsertedBrackets:0,maybeInsertedRow:-1,maybeInsertedLineStart:"",maybeInsertedLineEnd:""}},g=function(e,t,i,n){var o=e.end.row-e.start.row;return{text:i+t+n,selection:[0,e.start.column+1,o,e.end.column+ +!o]}};(o=function(e){e=e||{},this.add("braces","insertion",function(t,i,r,s,a){var c=r.getCursorPosition(),h=s.doc.getLine(c.row);if("{"==a){p(r);var u=r.getSelectionRange(),d=s.doc.getTextRange(u),f=s.getTokenAt(c.row,c.column);if(""!==d&&"{"!==d&&r.getWrapBehavioursEnabled())return g(u,d,"{","}");if(f&&/(?:string)\.quasi|\.xml/.test(f.type)){if([/tag\-(?:open|name)/,/attribute\-name/].some(function(e){return e.test(f.type)})||/(string)\.quasi/.test(f.type)&&"$"!==f.value[c.column-f.start-1])return;return o.recordAutoInsert(r,s,"}"),{text:"{}",selection:[1,1]}}if(o.isSaneInsertion(r,s))if(/[\]\}\)]/.test(h[c.column])||r.inMultiSelectMode||e.braces)return o.recordAutoInsert(r,s,"}"),{text:"{}",selection:[1,1]};else return o.recordMaybeInsert(r,s,"{"),{text:"{",selection:[1,1]}}else if("}"==a){p(r);var m=h.substring(c.column,c.column+1);if("}"==m&&null!==s.$findOpeningBracket("}",{column:c.column+1,row:c.row})&&o.isAutoInsertedClosing(c,h,a))return o.popAutoInsertedClosing(),{text:"",selection:[1,1]}}else if("\n"==a||"\r\n"==a){p(r);var y="";o.isMaybeInsertedClosing(c,h)&&(y=l.stringRepeat("}",n.maybeInsertedBrackets),o.clearMaybeInsertedClosing());var m=h.substring(c.column,c.column+1);if("}"===m){var v=s.findMatchingBracket({row:c.row,column:c.column+1},"}");if(!v)return null;var w=this.$getIndent(s.getLine(v.row))}else if(!y)return void o.clearMaybeInsertedClosing();else var w=this.$getIndent(h);var b=w+s.getTabString();return{text:"\n"+b+"\n"+w+y,selection:[1,b.length,1,b.length]}}else o.clearMaybeInsertedClosing()}),this.add("braces","deletion",function(e,t,i,o,r){var s=o.doc.getTextRange(r);if(!r.isMultiLine()&&"{"==s){if(p(i),"}"==o.doc.getLine(r.start.row).substring(r.end.column,r.end.column+1))return r.end.column++,r;n.maybeInsertedBrackets--}}),this.add("parens","insertion",function(e,t,i,n,r){if("("==r){p(i);var s=i.getSelectionRange(),a=n.doc.getTextRange(s);if(""!==a&&i.getWrapBehavioursEnabled())return g(s,a,"(",")");if(o.isSaneInsertion(i,n))return o.recordAutoInsert(i,n,")"),{text:"()",selection:[1,1]}}else if(")"==r){p(i);var l=i.getCursorPosition(),c=n.doc.getLine(l.row);if(")"==c.substring(l.column,l.column+1)&&null!==n.$findOpeningBracket(")",{column:l.column+1,row:l.row})&&o.isAutoInsertedClosing(l,c,r))return o.popAutoInsertedClosing(),{text:"",selection:[1,1]}}}),this.add("parens","deletion",function(e,t,i,n,o){var r=n.doc.getTextRange(o);if(!o.isMultiLine()&&"("==r&&(p(i),")"==n.doc.getLine(o.start.row).substring(o.start.column+1,o.start.column+2)))return o.end.column++,o}),this.add("brackets","insertion",function(e,t,i,n,r){if("["==r){p(i);var s=i.getSelectionRange(),a=n.doc.getTextRange(s);if(""!==a&&i.getWrapBehavioursEnabled())return g(s,a,"[","]");if(o.isSaneInsertion(i,n))return o.recordAutoInsert(i,n,"]"),{text:"[]",selection:[1,1]}}else if("]"==r){p(i);var l=i.getCursorPosition(),c=n.doc.getLine(l.row);if("]"==c.substring(l.column,l.column+1)&&null!==n.$findOpeningBracket("]",{column:l.column+1,row:l.row})&&o.isAutoInsertedClosing(l,c,r))return o.popAutoInsertedClosing(),{text:"",selection:[1,1]}}}),this.add("brackets","deletion",function(e,t,i,n,o){var r=n.doc.getTextRange(o);if(!o.isMultiLine()&&"["==r&&(p(i),"]"==n.doc.getLine(o.start.row).substring(o.start.column+1,o.start.column+2)))return o.end.column++,o}),this.add("string_dquotes","insertion",function(e,t,i,n,o){var r=n.$mode.$quotes||d;if(1==o.length&&r[o]){if(this.lineCommentStart&&-1!=this.lineCommentStart.indexOf(o))return;p(i);var s=i.getSelectionRange(),a=n.doc.getTextRange(s);if(""!==a&&(1!=a.length||!r[a])&&i.getWrapBehavioursEnabled())return g(s,a,o,o);if(!a){var l,c=i.getCursorPosition(),h=n.doc.getLine(c.row),u=h.substring(c.column-1,c.column),f=h.substring(c.column,c.column+1),m=n.getTokenAt(c.row,c.column),y=n.getTokenAt(c.row,c.column+1);if("\\"==u&&m&&/escape/.test(m.type))return null;var v=m&&/string|escape/.test(m.type),w=!y||/string|escape/.test(y.type);if(f==o)(l=v!==w)&&/string\.end/.test(y.type)&&(l=!1);else{if(v&&!w||v&&w)return null;var b=n.$mode.tokenRe;b.lastIndex=0;var $=b.test(u);b.lastIndex=0;var C=b.test(f),S=n.$mode.$pairQuotesAfter;if(!(S&&S[o]&&S[o].test(u))&&$||C||f&&!/[\s;,.})\]\\]/.test(f))return null;var x=h[c.column-2];if(u==o&&(x==o||b.test(x)))return null;l=!0}return{text:l?o+o:"",selection:[1,1]}}}}),this.add("string_dquotes","deletion",function(e,t,i,n,o){var r=n.$mode.$quotes||d,s=n.doc.getTextRange(o);if(!o.isMultiLine()&&r.hasOwnProperty(s)&&(p(i),n.doc.getLine(o.start.row).substring(o.start.column+1,o.start.column+2)==s))return o.end.column++,o}),!1!==e.closeDocComment&&this.add("doc comment end","insertion",function(e,t,i,n,o){if("doc-start"===e&&("\n"===o||"\r\n"===o)&&i.selection.isEmpty()){var r=i.getCursorPosition();if(0!==r.column){for(var s=n.doc.getLine(r.row),a=n.doc.getLine(r.row+1),l=n.getTokens(r.row),c=0,h=0;h<l.length;h++){c+=l[h].value.length;var u=l[h];if(c>=r.column){if(c===r.column){if(!/\.doc/.test(u.type))return;if(/\*\//.test(u.value)){var d=l[h+1];if(!d||!/\.doc/.test(d.type))return}}var p=r.column-(c-u.value.length),g=u.value.indexOf("*/"),f=u.value.indexOf("/**",g>-1?g+2:0);if(-1!==f&&p>f&&p<f+3||-1!==g&&-1!==f&&p>=g&&p<=f||!/\.doc/.test(u.type))return;break}}var m=this.$getIndent(s);if(/\s*\*/.test(a))if(/^\s*\*/.test(s))return{text:o+m+"* ",selection:[1,2+m.length,1,2+m.length]};else return{text:o+m+" * ",selection:[1,3+m.length,1,3+m.length]};if(/\/\*\*/.test(s.substring(0,r.column)))return{text:o+m+" * "+o+" "+m+"*/",selection:[1,4+m.length,1,4+m.length]}}}})}).isSaneInsertion=function(e,t){var i=e.getCursorPosition(),n=new a(t,i.row,i.column);if(!this.$matchTokenType(n.getCurrentToken()||"text",c)){if(/[)}\]]/.test(e.session.getLine(i.row)[i.column]))return!0;var o=new a(t,i.row,i.column+1);if(!this.$matchTokenType(o.getCurrentToken()||"text",c))return!1}return n.stepForward(),n.getCurrentTokenRow()!==i.row||this.$matchTokenType(n.getCurrentToken()||"text",h)},o.$matchTokenType=function(e,t){return t.indexOf(e.type||e)>-1},o.recordAutoInsert=function(e,t,i){var o=e.getCursorPosition(),r=t.doc.getLine(o.row);this.isAutoInsertedClosing(o,r,n.autoInsertedLineEnd[0])||(n.autoInsertedBrackets=0),n.autoInsertedRow=o.row,n.autoInsertedLineEnd=i+r.substr(o.column),n.autoInsertedBrackets++},o.recordMaybeInsert=function(e,t,i){var o=e.getCursorPosition(),r=t.doc.getLine(o.row);this.isMaybeInsertedClosing(o,r)||(n.maybeInsertedBrackets=0),n.maybeInsertedRow=o.row,n.maybeInsertedLineStart=r.substr(0,o.column)+i,n.maybeInsertedLineEnd=r.substr(o.column),n.maybeInsertedBrackets++},o.isAutoInsertedClosing=function(e,t,i){return n.autoInsertedBrackets>0&&e.row===n.autoInsertedRow&&i===n.autoInsertedLineEnd[0]&&t.substr(e.column)===n.autoInsertedLineEnd},o.isMaybeInsertedClosing=function(e,t){return n.maybeInsertedBrackets>0&&e.row===n.maybeInsertedRow&&t.substr(e.column)===n.maybeInsertedLineEnd&&t.substr(0,e.column)==n.maybeInsertedLineStart},o.popAutoInsertedClosing=function(){n.autoInsertedLineEnd=n.autoInsertedLineEnd.substr(1),n.autoInsertedBrackets--},o.clearMaybeInsertedClosing=function(){n&&(n.maybeInsertedBrackets=0,n.maybeInsertedRow=-1)},r.inherits(o,s),t.CstyleBehaviour=o}),ace.define("ace/unicode",["require","exports","module"],function(e,t,i){"use strict";for(var n=[48,9,8,25,5,0,2,25,48,0,11,0,5,0,6,22,2,30,2,457,5,11,15,4,8,0,2,0,18,116,2,1,3,3,9,0,2,2,2,0,2,19,2,82,2,138,2,4,3,155,12,37,3,0,8,38,10,44,2,0,2,1,2,1,2,0,9,26,6,2,30,10,7,61,2,9,5,101,2,7,3,9,2,18,3,0,17,58,3,100,15,53,5,0,6,45,211,57,3,18,2,5,3,11,3,9,2,1,7,6,2,2,2,7,3,1,3,21,2,6,2,0,4,3,3,8,3,1,3,3,9,0,5,1,2,4,3,11,16,2,2,5,5,1,3,21,2,6,2,1,2,1,2,1,3,0,2,4,5,1,3,2,4,0,8,3,2,0,8,15,12,2,2,8,2,2,2,21,2,6,2,1,2,4,3,9,2,2,2,2,3,0,16,3,3,9,18,2,2,7,3,1,3,21,2,6,2,1,2,4,3,8,3,1,3,2,9,1,5,1,2,4,3,9,2,0,17,1,2,5,4,2,2,3,4,1,2,0,2,1,4,1,4,2,4,11,5,4,4,2,2,3,3,0,7,0,15,9,18,2,2,7,2,2,2,22,2,9,2,4,4,7,2,2,2,3,8,1,2,1,7,3,3,9,19,1,2,7,2,2,2,22,2,9,2,4,3,8,2,2,2,3,8,1,8,0,2,3,3,9,19,1,2,7,2,2,2,22,2,15,4,7,2,2,2,3,10,0,9,3,3,9,11,5,3,1,2,17,4,23,2,8,2,0,3,6,4,0,5,5,2,0,2,7,19,1,14,57,6,14,2,9,40,1,2,0,3,1,2,0,3,0,7,3,2,6,2,2,2,0,2,0,3,1,2,12,2,2,3,4,2,0,2,5,3,9,3,1,35,0,24,1,7,9,12,0,2,0,2,0,5,9,2,35,5,19,2,5,5,7,2,35,10,0,58,73,7,77,3,37,11,42,2,0,4,328,2,3,3,6,2,0,2,3,3,40,2,3,3,32,2,3,3,6,2,0,2,3,3,14,2,56,2,3,3,66,5,0,33,15,17,84,13,619,3,16,2,25,6,74,22,12,2,6,12,20,12,19,13,12,2,2,2,1,13,51,3,29,4,0,5,1,3,9,34,2,3,9,7,87,9,42,6,69,11,28,4,11,5,11,11,39,3,4,12,43,5,25,7,10,38,27,5,62,2,28,3,10,7,9,14,0,89,75,5,9,18,8,13,42,4,11,71,55,9,9,4,48,83,2,2,30,14,230,23,280,3,5,3,37,3,5,3,7,2,0,2,0,2,0,2,30,3,52,2,6,2,0,4,2,2,6,4,3,3,5,5,12,6,2,2,6,67,1,20,0,29,0,14,0,17,4,60,12,5,0,4,11,18,0,5,0,3,9,2,0,4,4,7,0,2,0,2,0,2,3,2,10,3,3,6,4,5,0,53,1,2684,46,2,46,2,132,7,6,15,37,11,53,10,0,17,22,10,6,2,6,2,6,2,6,2,6,2,6,2,6,2,6,2,31,48,0,470,1,36,5,2,4,6,1,5,85,3,1,3,2,2,89,2,3,6,40,4,93,18,23,57,15,513,6581,75,20939,53,1164,68,45,3,268,4,27,21,31,3,13,13,1,2,24,9,69,11,1,38,8,3,102,3,1,111,44,25,51,13,68,12,9,7,23,4,0,5,45,3,35,13,28,4,64,15,10,39,54,10,13,3,9,7,22,4,1,5,66,25,2,227,42,2,1,3,9,7,11171,13,22,5,48,8453,301,3,61,3,105,39,6,13,4,6,11,2,12,2,4,2,0,2,1,2,1,2,107,34,362,19,63,3,53,41,11,5,15,17,6,13,1,25,2,33,4,2,134,20,9,8,25,5,0,2,25,12,88,4,5,3,5,3,5,3,2],o=0,r=[],s=0;s<n.length;s+=2)r.push(o+=n[s]),n[s+1]&&r.push(45,o+=n[s+1]);t.wordChars=String.fromCharCode.apply(null,r)}),ace.define("ace/mode/text",["require","exports","module","ace/config","ace/tokenizer","ace/mode/text_highlight_rules","ace/mode/behaviour/cstyle","ace/unicode","ace/lib/lang","ace/token_iterator","ace/range"],function(e,t,i){"use strict";var n,o=e("../config"),r=e("../tokenizer").Tokenizer,s=e("./text_highlight_rules").TextHighlightRules,a=e("./behaviour/cstyle").CstyleBehaviour,l=e("../unicode"),c=e("../lib/lang"),h=e("../token_iterator").TokenIterator,u=e("../range").Range;(function(){this.$defaultBehaviour=new a,this.tokenRe=RegExp("^["+l.wordChars+"\\$_]+","g"),this.nonTokenRe=RegExp("^(?:[^"+l.wordChars+"\\$_]|\\s])+","g"),this.getTokenizer=function(){return this.$tokenizer||(this.$highlightRules=this.$highlightRules||new this.HighlightRules(this.$highlightRuleConfig),this.$tokenizer=new r(this.$highlightRules.getRules())),this.$tokenizer},this.lineCommentStart="",this.blockComment="",this.toggleCommentLines=function(e,t,i,n){var o=t.doc,r=!0,s=!0,a=1/0,l=t.getTabSize(),h=!1;if(this.lineCommentStart){if(Array.isArray(this.lineCommentStart))var u=this.lineCommentStart.map(c.escapeRegExp).join("|"),d=this.lineCommentStart[0];else var u=c.escapeRegExp(this.lineCommentStart),d=this.lineCommentStart;u=RegExp("^(\\s*)(?:"+u+") ?"),h=t.getUseSoftTabs();var p=function(e,t){var i=e.match(u);if(i){var n=i[1].length,r=i[0].length;!y(e,n,r)&&" "==i[0][r-1]&&r--,o.removeInLine(t,n,r)}},g=d+" ",f=function(e,t){(!r||/\S/.test(e))&&(y(e,a,a)?o.insertInLine({row:t,column:a},g):o.insertInLine({row:t,column:a},d))},m=function(e,t){return u.test(e)},y=function(e,t,i){for(var n=0;t--&&" "==e.charAt(t);)n++;if(n%l!=0)return!1;for(var n=0;" "==e.charAt(i++);)n++;return l>2?n%l!=l-1:n%l==0}}else{if(!this.blockComment)return!1;var d=this.blockComment.start,v=this.blockComment.end,u=RegExp("^(\\s*)(?:"+c.escapeRegExp(d)+")"),w=RegExp("(?:"+c.escapeRegExp(v)+")\\s*$"),f=function(e,t){!m(e,t)&&(!r||/\S/.test(e))&&(o.insertInLine({row:t,column:e.length},v),o.insertInLine({row:t,column:a},d))},p=function(e,t){var i;(i=e.match(w))&&o.removeInLine(t,e.length-i[0].length,e.length),(i=e.match(u))&&o.removeInLine(t,i[1].length,i[0].length)},m=function(e,i){if(u.test(e))return!0;for(var n=t.getTokens(i),o=0;o<n.length;o++)if("comment"===n[o].type)return!0}}function b(e){for(var t=i;t<=n;t++)e(o.getLine(t),t)}var $=1/0;b(function(e,t){var i=e.search(/\S/);-1!==i?(i<a&&(a=i),s&&!m(e,t)&&(s=!1)):$>e.length&&($=e.length)}),a==1/0&&(a=$,r=!1,s=!1),h&&a%l!=0&&(a=Math.floor(a/l)*l),b(s?p:f)},this.toggleBlockComment=function(e,t,i,n){var o=this.blockComment;if(o){!o.start&&o[0]&&(o=o[0]);var r=new h(t,n.row,n.column),s=r.getCurrentToken();t.selection;var a=t.selection.toOrientedRange();if(s&&/comment/.test(s.type)){for(;s&&/comment/.test(s.type);){var l,c,d,p,g=s.value.indexOf(o.start);if(-1!=g){var f=r.getCurrentTokenRow(),m=r.getCurrentTokenColumn()+g;d=new u(f,m,f,m+o.start.length);break}s=r.stepBackward()}for(var r=new h(t,n.row,n.column),s=r.getCurrentToken();s&&/comment/.test(s.type);){var g=s.value.indexOf(o.end);if(-1!=g){var f=r.getCurrentTokenRow(),m=r.getCurrentTokenColumn()+g;p=new u(f,m,f,m+o.end.length);break}s=r.stepForward()}p&&t.remove(p),d&&(t.remove(d),l=d.start.row,c=-o.start.length)}else c=o.start.length,l=i.start.row,t.insert(i.end,o.end),t.insert(i.start,o.start);a.start.row==l&&(a.start.column+=c),a.end.row==l&&(a.end.column+=c),t.selection.fromOrientedRange(a)}},this.getNextLineIndent=function(e,t,i){return this.$getIndent(t)},this.checkOutdent=function(e,t,i){return!1},this.autoOutdent=function(e,t,i){},this.$getIndent=function(e){return e.match(/^\s*/)[0]},this.createWorker=function(e){return null},this.createModeDelegates=function(e){for(var t in this.$embeds=[],this.$modes={},e)if(e[t]){var i=e[t],n=i.prototype.$id,r=o.$modes[n];r||(o.$modes[n]=r=new i),o.$modes[t]||(o.$modes[t]=r),this.$embeds.push(t),this.$modes[t]=r}for(var s=["toggleBlockComment","toggleCommentLines","getNextLineIndent","checkOutdent","autoOutdent","transformAction","getCompletions"],a=this,t=0;t<s.length;t++)!function(e){var t,i;i=a[t=s[e]],a[s[e]]=function(){return this.$delegator(t,arguments,i)}}(t)},this.$delegator=function(e,t,i){var n=t[0]||"start";if("string"!=typeof n){if(Array.isArray(n[2])){var o=n[2][n[2].length-1],r=this.$modes[o];if(r)return r[e].apply(r,[n[1]].concat([].slice.call(t,1)))}n=n[0]||"start"}for(var s=0;s<this.$embeds.length;s++)if(this.$modes[this.$embeds[s]]){var a=n.split(this.$embeds[s]);if(!a[0]&&a[1]){t[0]=a[1];var r=this.$modes[this.$embeds[s]];return r[e].apply(r,t)}}var l=i.apply(this,t);return i?l:void 0},this.transformAction=function(e,t,i,n,o){if(this.$behaviour){var r=this.$behaviour.getBehaviours();for(var s in r)if(r[s][t]){var a=r[s][t].apply(this,arguments);if(a)return a}}},this.getKeywords=function(e){if(!this.completionKeywords){var t=this.$tokenizer.rules,i=[];for(var n in t)for(var o=t[n],r=0,s=o.length;r<s;r++)if("string"==typeof o[r].token)/keyword|support|storage/.test(o[r].token)&&i.push(o[r].regex);else if("object"==typeof o[r].token){for(var a=0,l=o[r].token.length;a<l;a++)if(/keyword|support|storage/.test(o[r].token[a])){var n=o[r].regex.match(/\(.+?\)/g)[a];i.push(n.substr(1,n.length-2))}}this.completionKeywords=i}return e?i.concat(this.$keywordList||[]):this.$keywordList},this.$createKeywordList=function(){return this.$highlightRules||this.getTokenizer(),this.$keywordList=this.$highlightRules.$keywordList||[]},this.getCompletions=function(e,t,i,n){return(this.$keywordList||this.$createKeywordList()).map(function(e){return{name:e,value:e,score:0,meta:"keyword"}})},this.$id="ace/mode/text"}).call((n=function(){this.HighlightRules=s}).prototype),t.Mode=n}),ace.define("ace/line_widgets",["require","exports","module","ace/lib/dom"],function(e,t,i){"use strict";var n=e("./lib/dom");t.LineWidgets=function(){function e(e){this.session=e,this.session.widgetManager=this,this.session.getRowLength=this.getRowLength,this.session.$getWidgetScreenLength=this.$getWidgetScreenLength,this.updateOnChange=this.updateOnChange.bind(this),this.renderWidgets=this.renderWidgets.bind(this),this.measureWidgets=this.measureWidgets.bind(this),this.session._changedWidgets=[],this.$onChangeEditor=this.$onChangeEditor.bind(this),this.session.on("change",this.updateOnChange),this.session.on("changeFold",this.updateOnFold),this.session.on("changeEditor",this.$onChangeEditor)}return e.prototype.getRowLength=function(e){var t;return(t=this.lineWidgets&&this.lineWidgets[e]&&this.lineWidgets[e].rowCount||0,this.$useWrapMode&&this.$wrapData[e])?this.$wrapData[e].length+1+t:1+t},e.prototype.$getWidgetScreenLength=function(){var e=0;return this.lineWidgets.forEach(function(t){t&&t.rowCount&&!t.hidden&&(e+=t.rowCount)}),e},e.prototype.$onChangeEditor=function(e){this.attach(e.editor)},e.prototype.attach=function(e){e&&e.widgetManager&&e.widgetManager!=this&&e.widgetManager.detach(),this.editor!=e&&(this.detach(),this.editor=e,e&&(e.widgetManager=this,e.renderer.on("beforeRender",this.measureWidgets),e.renderer.on("afterRender",this.renderWidgets)))},e.prototype.detach=function(e){var t=this.editor;if(t){this.editor=null,t.widgetManager=null,t.renderer.off("beforeRender",this.measureWidgets),t.renderer.off("afterRender",this.renderWidgets);var i=this.session.lineWidgets;i&&i.forEach(function(e){e&&e.el&&e.el.parentNode&&(e._inDocument=!1,e.el.parentNode.removeChild(e.el))})}},e.prototype.updateOnFold=function(e,t){var i=t.lineWidgets;if(i&&e.action){for(var n=e.data,o=n.start.row,r=n.end.row,s="add"==e.action,a=o+1;a<r;a++)i[a]&&(i[a].hidden=s);i[r]&&(s?i[o]?i[r].hidden=s:i[o]=i[r]:(i[o]==i[r]&&(i[o]=void 0),i[r].hidden=s))}},e.prototype.updateOnChange=function(e){var t=this.session.lineWidgets;if(t){var i=e.start.row,n=e.end.row-i;if(0===n);else if("remove"==e.action){var o=t.splice(i+1,n);!t[i]&&o[o.length-1]&&(t[i]=o.pop()),o.forEach(function(e){e&&this.removeLineWidget(e)},this),this.$updateRows()}else{var r=Array(n);t[i]&&null!=t[i].column&&e.start.column>t[i].column&&i++,r.unshift(i,0),t.splice.apply(t,r),this.$updateRows()}}},e.prototype.$updateRows=function(){var e=this.session.lineWidgets;if(e){var t=!0;e.forEach(function(e,i){if(e)for(t=!1,e.row=i;e.$oldWidget;)e.$oldWidget.row=i,e=e.$oldWidget}),t&&(this.session.lineWidgets=null)}},e.prototype.$registerLineWidget=function(e){this.session.lineWidgets||(this.session.lineWidgets=Array(this.session.getLength()));var t=this.session.lineWidgets[e.row];return t&&(e.$oldWidget=t,t.el&&t.el.parentNode&&(t.el.parentNode.removeChild(t.el),t._inDocument=!1)),this.session.lineWidgets[e.row]=e,e},e.prototype.addLineWidget=function(e){if(this.$registerLineWidget(e),e.session=this.session,!this.editor)return e;var t=this.editor.renderer;e.html&&!e.el&&(e.el=n.createElement("div"),e.el.innerHTML=e.html),e.text&&!e.el&&(e.el=n.createElement("div"),e.el.textContent=e.text),e.el&&(n.addCssClass(e.el,"ace_lineWidgetContainer"),e.className&&n.addCssClass(e.el,e.className),e.el.style.position="absolute",e.el.style.zIndex="5",t.container.appendChild(e.el),e._inDocument=!0,e.coverGutter||(e.el.style.zIndex="3"),null==e.pixelHeight&&(e.pixelHeight=e.el.offsetHeight)),null==e.rowCount&&(e.rowCount=e.pixelHeight/t.layerConfig.lineHeight);var i=this.session.getFoldAt(e.row,0);if(e.$fold=i,i){var o=this.session.lineWidgets;e.row!=i.end.row||o[i.start.row]?e.hidden=!0:o[i.start.row]=e}return this.session._emit("changeFold",{data:{start:{row:e.row}}}),this.$updateRows(),this.renderWidgets(null,t),this.onWidgetChanged(e),e},e.prototype.removeLineWidget=function(e){if(e._inDocument=!1,e.session=null,e.el&&e.el.parentNode&&e.el.parentNode.removeChild(e.el),e.editor&&e.editor.destroy)try{e.editor.destroy()}catch(e){}if(this.session.lineWidgets){var t=this.session.lineWidgets[e.row];if(t==e)this.session.lineWidgets[e.row]=e.$oldWidget,e.$oldWidget&&this.onWidgetChanged(e.$oldWidget);else for(;t;){if(t.$oldWidget==e){t.$oldWidget=e.$oldWidget;break}t=t.$oldWidget}}this.session._emit("changeFold",{data:{start:{row:e.row}}}),this.$updateRows()},e.prototype.getWidgetsAtRow=function(e){for(var t=this.session.lineWidgets,i=t&&t[e],n=[];i;)n.push(i),i=i.$oldWidget;return n},e.prototype.onWidgetChanged=function(e){this.session._changedWidgets.push(e),this.editor&&this.editor.renderer.updateFull()},e.prototype.measureWidgets=function(e,t){var i=this.session._changedWidgets,n=t.layerConfig;if(i&&i.length){for(var o=1/0,r=0;r<i.length;r++){var s=i[r];if(s&&s.el&&s.session==this.session){if(!s._inDocument){if(this.session.lineWidgets[s.row]!=s)continue;s._inDocument=!0,t.container.appendChild(s.el)}s.h=s.el.offsetHeight,s.fixedWidth||(s.w=s.el.offsetWidth,s.screenWidth=Math.ceil(s.w/n.characterWidth));var a=s.h/n.lineHeight;s.coverLine&&(a-=this.session.getRowLineCount(s.row))<0&&(a=0),s.rowCount!=a&&(s.rowCount=a,s.row<o&&(o=s.row))}}o!=1/0&&(this.session._emit("changeFold",{data:{start:{row:o}}}),this.session.lineWidgetWidth=null),this.session._changedWidgets=[]}},e.prototype.renderWidgets=function(e,t){var i=t.layerConfig,n=this.session.lineWidgets;if(n){for(var o=Math.min(this.firstRow,i.firstRow),r=Math.max(this.lastRow,i.lastRow,n.length);o>0&&!n[o];)o--;this.firstRow=i.firstRow,this.lastRow=i.lastRow,t.$cursorLayer.config=i;for(var s=o;s<=r;s++){var a=n[s];if(a&&a.el){if(a.hidden){a.el.style.top=-100-(a.pixelHeight||0)+"px";continue}a._inDocument||(a._inDocument=!0,t.container.appendChild(a.el));var l=t.$cursorLayer.getPixelPosition({row:s,column:0},!0).top;a.coverLine||(l+=i.lineHeight*this.session.getRowLineCount(a.row)),a.el.style.top=l-i.offset+"px";var c=a.coverGutter?0:t.gutterWidth;a.fixedWidth||(c-=t.scrollLeft),a.el.style.left=c+"px",a.fullWidth&&a.screenWidth&&(a.el.style.minWidth=i.width+2*i.padding+"px"),a.fixedWidth?a.el.style.right=t.scrollBar.getWidth()+"px":a.el.style.right=""}}}},e}()}),ace.define("ace/apply_delta",["require","exports","module"],function(e,t,i){"use strict";t.applyDelta=function(e,t,i){var n=t.start.row,o=t.start.column,r=e[n]||"";switch(t.action){case"insert":if(1===t.lines.length)e[n]=r.substring(0,o)+t.lines[0]+r.substring(o);else{var s=[n,1].concat(t.lines);e.splice.apply(e,s),e[n]=r.substring(0,o)+e[n],e[n+t.lines.length-1]+=r.substring(o)}break;case"remove":var a=t.end.column,l=t.end.row;n===l?e[n]=r.substring(0,o)+r.substring(a):e.splice(n,l-n+1,r.substring(0,o)+e[l].substring(a))}}}),ace.define("ace/anchor",["require","exports","module","ace/lib/oop","ace/lib/event_emitter"],function(e,t,i){"use strict";var n=e("./lib/oop"),o=e("./lib/event_emitter").EventEmitter,r=function(){function e(e,t,i){this.$onChange=this.onChange.bind(this),this.attach(e),"number"!=typeof t?this.setPosition(t.row,t.column):this.setPosition(t,i)}return e.prototype.getPosition=function(){return this.$clipPositionToDocument(this.row,this.column)},e.prototype.getDocument=function(){return this.document},e.prototype.onChange=function(e){if((e.start.row!=e.end.row||e.start.row==this.row)&&!(e.start.row>this.row)){var t,i,n,o,r,a,l,c,h=(t=e,i={row:this.row,column:this.column},n=this.$insertRight,r=((o="insert"==t.action)?1:-1)*(t.end.row-t.start.row),a=(o?1:-1)*(t.end.column-t.start.column),l=t.start,c=o?l:t.end,s(i,l,n)?{row:i.row,column:i.column}:s(c,i,!n)?{row:i.row+r,column:i.column+(i.row==c.row?a:0)}:{row:l.row,column:l.column});this.setPosition(h.row,h.column,!0)}},e.prototype.setPosition=function(e,t,i){if(n=i?{row:e,column:t}:this.$clipPositionToDocument(e,t),this.row!=n.row||this.column!=n.column){var n,o={row:this.row,column:this.column};this.row=n.row,this.column=n.column,this._signal("change",{old:o,value:n})}},e.prototype.detach=function(){this.document.off("change",this.$onChange)},e.prototype.attach=function(e){this.document=e||this.document,this.document.on("change",this.$onChange)},e.prototype.$clipPositionToDocument=function(e,t){var i={};return e>=this.document.getLength()?(i.row=Math.max(0,this.document.getLength()-1),i.column=this.document.getLine(i.row).length):e<0?(i.row=0,i.column=0):(i.row=e,i.column=Math.min(this.document.getLine(i.row).length,Math.max(0,t))),t<0&&(i.column=0),i},e}();function s(e,t,i){var n=i?e.column<=t.column:e.column<t.column;return e.row<t.row||e.row==t.row&&n}r.prototype.$insertRight=!1,n.implement(r.prototype,o),t.Anchor=r}),ace.define("ace/document",["require","exports","module","ace/lib/oop","ace/apply_delta","ace/lib/event_emitter","ace/range","ace/anchor"],function(e,t,i){"use strict";var n=e("./lib/oop"),o=e("./apply_delta").applyDelta,r=e("./lib/event_emitter").EventEmitter,s=e("./range").Range,a=e("./anchor").Anchor,l=function(){function e(e){this.$lines=[""],0===e.length?this.$lines=[""]:Array.isArray(e)?this.insertMergedLines({row:0,column:0},e):this.insert({row:0,column:0},e)}return e.prototype.setValue=function(e){var t=this.getLength()-1;this.remove(new s(0,0,t,this.getLine(t).length)),this.insert({row:0,column:0},e||"")},e.prototype.getValue=function(){return this.getAllLines().join(this.getNewLineCharacter())},e.prototype.createAnchor=function(e,t){return new a(this,e,t)},e.prototype.$detectNewLine=function(e){var t=e.match(/^.*?(\r\n|\r|\n)/m);this.$autoNewLine=t?t[1]:"\n",this._signal("changeNewLineMode")},e.prototype.getNewLineCharacter=function(){switch(this.$newLineMode){case"windows":return"\r\n";case"unix":return"\n";default:return this.$autoNewLine||"\n"}},e.prototype.setNewLineMode=function(e){this.$newLineMode!==e&&(this.$newLineMode=e,this._signal("changeNewLineMode"))},e.prototype.getNewLineMode=function(){return this.$newLineMode},e.prototype.isNewLine=function(e){return"\r\n"==e||"\r"==e||"\n"==e},e.prototype.getLine=function(e){return this.$lines[e]||""},e.prototype.getLines=function(e,t){return this.$lines.slice(e,t+1)},e.prototype.getAllLines=function(){return this.getLines(0,this.getLength())},e.prototype.getLength=function(){return this.$lines.length},e.prototype.getTextRange=function(e){return this.getLinesForRange(e).join(this.getNewLineCharacter())},e.prototype.getLinesForRange=function(e){var t;if(e.start.row===e.end.row)t=[this.getLine(e.start.row).substring(e.start.column,e.end.column)];else{(t=this.getLines(e.start.row,e.end.row))[0]=(t[0]||"").substring(e.start.column);var i=t.length-1;e.end.row-e.start.row==i&&(t[i]=t[i].substring(0,e.end.column))}return t},e.prototype.insertLines=function(e,t){return console.warn("Use of document.insertLines is deprecated. Use the insertFullLines method instead."),this.insertFullLines(e,t)},e.prototype.removeLines=function(e,t){return console.warn("Use of document.removeLines is deprecated. Use the removeFullLines method instead."),this.removeFullLines(e,t)},e.prototype.insertNewLine=function(e){return console.warn("Use of document.insertNewLine is deprecated. Use insertMergedLines(position, ['', '']) instead."),this.insertMergedLines(e,["",""])},e.prototype.insert=function(e,t){return 1>=this.getLength()&&this.$detectNewLine(t),this.insertMergedLines(e,this.$split(t))},e.prototype.insertInLine=function(e,t){var i=this.clippedPos(e.row,e.column),n=this.pos(e.row,e.column+t.length);return this.applyDelta({start:i,end:n,action:"insert",lines:[t]},!0),this.clonePos(n)},e.prototype.clippedPos=function(e,t){var i=this.getLength();void 0===e?e=i:e<0?e=0:e>=i&&(e=i-1,t=void 0);var n=this.getLine(e);return void 0==t&&(t=n.length),{row:e,column:t=Math.min(Math.max(t,0),n.length)}},e.prototype.clonePos=function(e){return{row:e.row,column:e.column}},e.prototype.pos=function(e,t){return{row:e,column:t}},e.prototype.$clipPosition=function(e){var t=this.getLength();return e.row>=t?(e.row=Math.max(0,t-1),e.column=this.getLine(t-1).length):(e.row=Math.max(0,e.row),e.column=Math.min(Math.max(e.column,0),this.getLine(e.row).length)),e},e.prototype.insertFullLines=function(e,t){e=Math.min(Math.max(e,0),this.getLength());var i=0;e<this.getLength()?(t=t.concat([""]),i=0):(t=[""].concat(t),e--,i=this.$lines[e].length),this.insertMergedLines({row:e,column:i},t)},e.prototype.insertMergedLines=function(e,t){var i=this.clippedPos(e.row,e.column),n={row:i.row+t.length-1,column:(1==t.length?i.column:0)+t[t.length-1].length};return this.applyDelta({start:i,end:n,action:"insert",lines:t}),this.clonePos(n)},e.prototype.remove=function(e){var t=this.clippedPos(e.start.row,e.start.column),i=this.clippedPos(e.end.row,e.end.column);return this.applyDelta({start:t,end:i,action:"remove",lines:this.getLinesForRange({start:t,end:i})}),this.clonePos(t)},e.prototype.removeInLine=function(e,t,i){var n=this.clippedPos(e,t),o=this.clippedPos(e,i);return this.applyDelta({start:n,end:o,action:"remove",lines:this.getLinesForRange({start:n,end:o})},!0),this.clonePos(n)},e.prototype.removeFullLines=function(e,t){e=Math.min(Math.max(0,e),this.getLength()-1);var i=(t=Math.min(Math.max(0,t),this.getLength()-1))==this.getLength()-1&&e>0,n=t<this.getLength()-1,o=i?e-1:e,r=i?this.getLine(o).length:0,a=n?t+1:t,l=n?0:this.getLine(a).length,c=new s(o,r,a,l),h=this.$lines.slice(e,t+1);return this.applyDelta({start:c.start,end:c.end,action:"remove",lines:this.getLinesForRange(c)}),h},e.prototype.removeNewLine=function(e){e<this.getLength()-1&&e>=0&&this.applyDelta({start:this.pos(e,this.getLine(e).length),end:this.pos(e+1,0),action:"remove",lines:["",""]})},e.prototype.replace=function(e,t){var i;return(e instanceof s||(e=s.fromPoints(e.start,e.end)),0===t.length&&e.isEmpty())?e.start:t==this.getTextRange(e)?e.end:(this.remove(e),t?this.insert(e.start,t):e.start)},e.prototype.applyDeltas=function(e){for(var t=0;t<e.length;t++)this.applyDelta(e[t])},e.prototype.revertDeltas=function(e){for(var t=e.length-1;t>=0;t--)this.revertDelta(e[t])},e.prototype.applyDelta=function(e,t){var i="insert"==e.action;(i?!(e.lines.length<=1)||e.lines[0]:s.comparePoints(e.start,e.end))&&(i&&e.lines.length>2e4?this.$splitAndapplyLargeDelta(e,2e4):(o(this.$lines,e,t),this._signal("change",e)))},e.prototype.$safeApplyDelta=function(e){var t=this.$lines.length;("remove"==e.action&&e.start.row<t&&e.end.row<t||"insert"==e.action&&e.start.row<=t)&&this.applyDelta(e)},e.prototype.$splitAndapplyLargeDelta=function(e,t){for(var i=e.lines,n=i.length-t+1,o=e.start.row,r=e.start.column,s=0,a=0;s<n;s=a){a+=t-1;var l=i.slice(s,a);l.push(""),this.applyDelta({start:this.pos(o+s,r),end:this.pos(o+a,r=0),action:e.action,lines:l},!0)}e.lines=i.slice(s),e.start.row=o+s,e.start.column=r,this.applyDelta(e,!0)},e.prototype.revertDelta=function(e){this.$safeApplyDelta({start:this.clonePos(e.start),end:this.clonePos(e.end),action:"insert"==e.action?"remove":"insert",lines:e.lines.slice()})},e.prototype.indexToPosition=function(e,t){for(var i=this.$lines||this.getAllLines(),n=this.getNewLineCharacter().length,o=t||0,r=i.length;o<r;o++)if((e-=i[o].length+n)<0)return{row:o,column:e+i[o].length+n};return{row:r-1,column:e+i[r-1].length+n}},e.prototype.positionToIndex=function(e,t){for(var i=this.$lines||this.getAllLines(),n=this.getNewLineCharacter().length,o=0,r=Math.min(e.row,i.length),s=t||0;s<r;++s)o+=i[s].length+n;return o+e.column},e.prototype.$split=function(e){return e.split(/\r\n|\r|\n/)},e}();l.prototype.$autoNewLine="",l.prototype.$newLineMode="auto",n.implement(l.prototype,r),t.Document=l}),ace.define("ace/background_tokenizer",["require","exports","module","ace/lib/oop","ace/lib/event_emitter"],function(e,t,i){"use strict";var n=e("./lib/oop"),o=e("./lib/event_emitter").EventEmitter,r=function(){function e(e,t){this.running=!1,this.lines=[],this.states=[],this.currentLine=0,this.tokenizer=e;var i=this;this.$worker=function(){if(i.running){for(var e=new Date,t=i.currentLine,n=-1,o=i.doc,r=t;i.lines[t];)t++;var s=o.getLength(),a=0;for(i.running=!1;t<s;){i.$tokenizeRow(t),n=t;do t++;while(i.lines[t]);if(++a%5==0&&new Date-e>20){i.running=setTimeout(i.$worker,20);break}}i.currentLine=t,-1==n&&(n=t),r<=n&&i.fireUpdateEvent(r,n)}}}return e.prototype.setTokenizer=function(e){this.tokenizer=e,this.lines=[],this.states=[],this.start(0)},e.prototype.setDocument=function(e){this.doc=e,this.lines=[],this.states=[],this.stop()},e.prototype.fireUpdateEvent=function(e,t){this._signal("update",{data:{first:e,last:t}})},e.prototype.start=function(e){this.currentLine=Math.min(e||0,this.currentLine,this.doc.getLength()),this.lines.splice(this.currentLine,this.lines.length),this.states.splice(this.currentLine,this.states.length),this.stop(),this.running=setTimeout(this.$worker,700)},e.prototype.scheduleStart=function(){this.running||(this.running=setTimeout(this.$worker,700))},e.prototype.$updateOnChange=function(e){var t=e.start.row,i=e.end.row-t;if(0===i)this.lines[t]=null;else if("remove"==e.action)this.lines.splice(t,i+1,null),this.states.splice(t,i+1,null);else{var n=Array(i+1);n.unshift(t,1),this.lines.splice.apply(this.lines,n),this.states.splice.apply(this.states,n)}this.currentLine=Math.min(t,this.currentLine,this.doc.getLength()),this.stop()},e.prototype.stop=function(){this.running&&clearTimeout(this.running),this.running=!1},e.prototype.getTokens=function(e){return this.lines[e]||this.$tokenizeRow(e)},e.prototype.getState=function(e){return this.currentLine==e&&this.$tokenizeRow(e),this.states[e]||"start"},e.prototype.$tokenizeRow=function(e){var t=this.doc.getLine(e),i=this.states[e-1],n=this.tokenizer.getLineTokens(t,i,e);return this.states[e]+""!=n.state+""?(this.states[e]=n.state,this.lines[e+1]=null,this.currentLine>e+1&&(this.currentLine=e+1)):this.currentLine==e&&(this.currentLine=e+1),this.lines[e]=n.tokens},e.prototype.cleanup=function(){this.running=!1,this.lines=[],this.states=[],this.currentLine=0,this.removeAllListeners()},e}();n.implement(r.prototype,o),t.BackgroundTokenizer=r}),ace.define("ace/search_highlight",["require","exports","module","ace/lib/lang","ace/range"],function(e,t,i){"use strict";var n=e("./lib/lang"),o=e("./range").Range,r=function(){function e(e,t,i){void 0===i&&(i="text"),this.setRegexp(e),this.clazz=t,this.type=i,this.docLen=0}return e.prototype.setRegexp=function(e){this.regExp+""!=e+""&&(this.regExp=e,this.cache=[])},e.prototype.update=function(e,t,i,r){if(this.regExp){for(var s=r.firstRow,a=r.lastRow,l={},c=i.$editor&&i.$editor.$search,h=c&&c.$isMultilineSearch(i.$editor.getLastSearchOptions()),u=s;u<=a;u++){var d=this.cache[u];if(null==d||i.getValue().length!=this.docLen){if(h){d=[];var p=c.$multiLineForward(i,this.regExp,u,a);if(p){var g=p.endRow<=a?p.endRow-1:a;g>u&&(u=g),d.push(new o(p.startRow,p.startCol,p.endRow,p.endCol))}d.length>this.MAX_RANGES&&(d=d.slice(0,this.MAX_RANGES))}else(d=n.getMatchOffsets(i.getLine(u),this.regExp)).length>this.MAX_RANGES&&(d=d.slice(0,this.MAX_RANGES)),d=d.map(function(e){return new o(u,e.offset,u,e.offset+e.length)});this.cache[u]=d.length?d:""}if(0!==d.length)for(var f=d.length;f--;){var m=d[f].toScreenRange(i),y=m.toString();l[y]||(l[y]=!0,t.drawSingleLineMarker(e,m,this.clazz,r))}}this.docLen=i.getValue().length}},e}();r.prototype.MAX_RANGES=500,t.SearchHighlight=r}),ace.define("ace/undomanager",["require","exports","module","ace/range"],function(e,t,i){"use strict";var n=function(){function e(){this.$keepRedoStack,this.$maxRev=0,this.$fromUndo=!1,this.$undoDepth=1/0,this.reset()}return e.prototype.addSession=function(e){this.$session=e},e.prototype.add=function(e,t,i){if(!this.$fromUndo&&e!=this.$lastDelta){if(this.$keepRedoStack||(this.$redoStack.length=0),!1===t||!this.lastDeltas){this.lastDeltas=[];var n=this.$undoStack.length;n>this.$undoDepth-1&&this.$undoStack.splice(0,n-this.$undoDepth+1),this.$undoStack.push(this.lastDeltas),e.id=this.$rev=++this.$maxRev}("remove"==e.action||"insert"==e.action)&&(this.$lastDelta=e),this.lastDeltas.push(e)}},e.prototype.addSelection=function(e,t){this.selections.push({value:e,rev:t||this.$rev})},e.prototype.startNewGroup=function(){return this.lastDeltas=null,this.$rev},e.prototype.markIgnored=function(e,t){null==t&&(t=this.$rev+1);for(var i=this.$undoStack,n=i.length;n--;){var o=i[n][0];if(o.id<=e)break;o.id<t&&(o.ignore=!0)}this.lastDeltas=null},e.prototype.getSelection=function(e,t){for(var i=this.selections,n=i.length;n--;){var o=i[n];if(o.rev<e)return t&&(o=i[n+1]),o}},e.prototype.getRevision=function(){return this.$rev},e.prototype.getDeltas=function(e,t){null==t&&(t=this.$rev+1);for(var i=this.$undoStack,n=null,o=0,r=i.length;r--;){var s=i[r][0];if(s.id<t&&!n&&(n=r+1),s.id<=e){o=r+1;break}}return i.slice(o,n)},e.prototype.getChangedRanges=function(e,t){null==t&&(t=this.$rev+1)},e.prototype.getChangedLines=function(e,t){null==t&&(t=this.$rev+1)},e.prototype.undo=function(e,t){this.lastDeltas=null;var i=this.$undoStack;if(function(e,t){for(var i=t;i--;){var n=e[i];if(n&&!n[0].ignore){for(;i<t-1;){var o=function(e,t){for(var i=e.length;i--;)for(var n=0;n<t.length;n++)if(!c(e[i],t[n])){for(;i<e.length;){for(;n--;)c(t[n],e[i]);n=t.length,i++}return[e,t]}return e.selectionBefore=t.selectionBefore=e.selectionAfter=t.selectionAfter=null,[t,e]}(e[i],e[i+1]);e[i]=o[0],e[i+1]=o[1],i++}return!0}}}(i,i.length)){e||(e=this.$session),this.$redoStackBaseRev!==this.$rev&&this.$redoStack.length&&(this.$redoStack=[]),this.$fromUndo=!0;var n=i.pop(),o=null;return n&&(o=e.undoChanges(n,t),this.$redoStack.push(n),this.$syncRev()),this.$fromUndo=!1,o}},e.prototype.redo=function(e,t){if(this.lastDeltas=null,e||(e=this.$session),this.$fromUndo=!0,this.$redoStackBaseRev!=this.$rev){var i=this.getDeltas(this.$redoStackBaseRev,this.$rev+1);(function(e,t){for(var i=0;i<t.length;i++)for(var n=t[i],a=0;a<n.length;a++)!function(e,t){var i;t={start:s((i=t).start),end:s(i.end),action:i.action,lines:i.lines.slice()};for(var n=e.length;n--;){for(var a=e[n],l=0;l<a.length;l++){var c=function(e,t){var i,n,s="insert"==e.action,a="insert"==t.action;if(s&&a)0>r(e.start,t.start)?h(t,e,1):h(e,t,1);else if(s&&!a)r(e.start,t.end)>=0?h(e,t,-1):(0>=r(e.start,t.start)||h(e,o.fromPoints(t.start,e.start),-1),h(t,e,1));else if(!s&&a)r(t.start,e.end)>=0?h(t,e,-1):(0>=r(t.start,e.start)||h(t,o.fromPoints(e.start,t.start),-1),h(e,t,1));else if(!s&&!a)if(r(t.start,e.end)>=0)h(t,e,-1);else{if(!(0>=r(t.end,e.start)))return 0>r(e.start,t.start)&&(i=e,e=d(e,t.start)),r(e.end,t.end)>0&&(n=d(e,t.end)),u(t.end,e.start,e.end,-1),n&&!i&&(e.lines=n.lines,e.start=n.start,e.end=n.end,n=e),[t,i,n].filter(Boolean);h(e,t,-1)}return[t,e]}(a[l],t);t=c[0],2!=c.length&&(c[2]?(a.splice(l+1,1,c[1],c[2]),l++):!c[1]&&(a.splice(l,1),l--))}a.length||e.splice(n,1)}}(e,n[a])})(this.$redoStack,i),this.$redoStackBaseRev=this.$rev,this.$redoStack.forEach(function(e){e[0].id=++this.$maxRev},this)}var n=this.$redoStack.pop(),a=null;return n&&(a=e.redoChanges(n,t),this.$undoStack.push(n),this.$syncRev()),this.$fromUndo=!1,a},e.prototype.$syncRev=function(){var e=this.$undoStack,t=e[e.length-1],i=t&&t[0].id||0;this.$redoStackBaseRev=i,this.$rev=i},e.prototype.reset=function(){this.lastDeltas=null,this.$lastDelta=null,this.$undoStack=[],this.$redoStack=[],this.$rev=0,this.mark=0,this.$redoStackBaseRev=this.$rev,this.selections=[]},e.prototype.canUndo=function(){return this.$undoStack.length>0},e.prototype.canRedo=function(){return this.$redoStack.length>0},e.prototype.bookmark=function(e){void 0==e&&(e=this.$rev),this.mark=e},e.prototype.isAtBookmark=function(){return this.$rev===this.mark},e.prototype.toJSON=function(){return{$redoStack:this.$redoStack,$undoStack:this.$undoStack}},e.prototype.fromJSON=function(e){this.reset(),this.$undoStack=e.$undoStack,this.$redoStack=e.$redoStack},e.prototype.$prettyPrint=function(e){return e?a(e):a(this.$undoStack)+"\n---\n"+a(this.$redoStack)},e}();n.prototype.hasUndo=n.prototype.canUndo,n.prototype.hasRedo=n.prototype.canRedo,n.prototype.isClean=n.prototype.isAtBookmark,n.prototype.markClean=n.prototype.bookmark;var o=e("./range").Range,r=o.comparePoints;function s(e){return{row:e.row,column:e.column}}function a(e){if(Array.isArray(e=e||this))return e.map(a).join("\n");var t="";return e.action?t=("insert"==e.action?"+":"-")+("["+e.lines)+"]":e.value&&(t=Array.isArray(e.value)?e.value.map(l).join("\n"):l(e.value)),e.start&&(t+=l(e)),(e.id||e.rev)&&(t+="	("+(e.id||e.rev)+")"),t}function l(e){return e.start.row+":"+e.start.column+"=>"+e.end.row+":"+e.end.column}function c(e,t){var i="insert"==e.action,n="insert"==t.action;if(i&&n)if(r(t.start,e.end)>=0)h(t,e,-1);else{if(!(0>=r(t.start,e.start)))return null;h(e,t,1)}else if(i&&!n)if(r(t.start,e.end)>=0)h(t,e,-1);else{if(!(0>=r(t.end,e.start)))return null;h(e,t,-1)}else if(!i&&n)if(r(t.start,e.start)>=0)h(t,e,1);else{if(!(0>=r(t.start,e.start)))return null;h(e,t,1)}else if(!i&&!n)if(r(t.start,e.start)>=0)h(t,e,1);else{if(!(0>=r(t.end,e.start)))return null;h(e,t,-1)}return[t,e]}function h(e,t,i){u(e.start,t.start,t.end,i),u(e.end,t.start,t.end,i)}function u(e,t,i,n){e.row==(1==n?t:i).row&&(e.column+=n*(i.column-t.column)),e.row+=n*(i.row-t.row)}function d(e,t){var i=e.lines,n=e.end;e.end=s(t);var o=e.end.row-e.start.row,r=i.splice(o,i.length),a=o?t.column:t.column-e.start.column;return i.push(r[0].substring(0,a)),r[0]=r[0].substr(a),{start:s(t),end:n,lines:r,action:e.action}}o.comparePoints,t.UndoManager=n}),ace.define("ace/edit_session/fold_line",["require","exports","module","ace/range"],function(e,t,i){"use strict";var n=e("../range").Range;t.FoldLine=function(){function e(e,t){this.foldData=e,Array.isArray(t)?this.folds=t:t=this.folds=[t];var i=t[t.length-1];this.range=new n(t[0].start.row,t[0].start.column,i.end.row,i.end.column),this.start=this.range.start,this.end=this.range.end,this.folds.forEach(function(e){e.setFoldLine(this)},this)}return e.prototype.shiftRow=function(e){this.start.row+=e,this.end.row+=e,this.folds.forEach(function(t){t.start.row+=e,t.end.row+=e})},e.prototype.addFold=function(e){if(e.sameRow){if(e.start.row<this.startRow||e.endRow>this.endRow)throw Error("Can't add a fold to this FoldLine as it has no connection");this.folds.push(e),this.folds.sort(function(e,t){return-e.range.compareEnd(t.start.row,t.start.column)}),this.range.compareEnd(e.start.row,e.start.column)>0?(this.end.row=e.end.row,this.end.column=e.end.column):0>this.range.compareStart(e.end.row,e.end.column)&&(this.start.row=e.start.row,this.start.column=e.start.column)}else if(e.start.row==this.end.row)this.folds.push(e),this.end.row=e.end.row,this.end.column=e.end.column;else if(e.end.row==this.start.row)this.folds.unshift(e),this.start.row=e.start.row,this.start.column=e.start.column;else throw Error("Trying to add fold to FoldRow that doesn't have a matching row");e.foldLine=this},e.prototype.containsRow=function(e){return e>=this.start.row&&e<=this.end.row},e.prototype.walk=function(e,t,i){var n,o,r,s=0,a=this.folds,l=!0;null==t&&(t=this.end.row,i=this.end.column);for(var c=0;c<a.length;c++){if(-1==(o=(n=a[c]).range.compareStart(t,i)))return void e(null,t,i,s,l);if(!e(null,n.start.row,n.start.column,s,l)&&e(n.placeholder,n.start.row,n.start.column,s)||0===o)return;l=!n.sameRow,s=n.end.column}e(null,t,i,s,l)},e.prototype.getNextFoldTo=function(e,t){for(var i,n,o=0;o<this.folds.length;o++){if(-1==(n=(i=this.folds[o]).range.compareEnd(e,t)))return{fold:i,kind:"after"};if(0===n)return{fold:i,kind:"inside"}}return null},e.prototype.addRemoveChars=function(e,t,i){var n,o,r=this.getNextFoldTo(e,t);if(r){if(n=r.fold,"inside"==r.kind&&n.start.column!=t&&n.start.row!=e)window.console&&window.console.log(e,t,n);else if(n.start.row==e){var s=(o=this.folds).indexOf(n);for(0===s&&(this.start.column+=i);s<o.length;s++){if(n=o[s],n.start.column+=i,!n.sameRow)return;n.end.column+=i}this.end.column+=i}}},e.prototype.split=function(t,i){var n=this.getNextFoldTo(t,i);if(!n||"inside"==n.kind)return null;var o=n.fold,r=this.folds,s=this.foldData,a=r.indexOf(o),l=r[a-1];this.end.row=l.end.row,this.end.column=l.end.column;var c=new e(s,r=r.splice(a,r.length-a));return s.splice(s.indexOf(this)+1,0,c),c},e.prototype.merge=function(e){for(var t=e.folds,i=0;i<t.length;i++)this.addFold(t[i]);var n=this.foldData;n.splice(n.indexOf(e),1)},e.prototype.toString=function(){var e=[this.range.toString()+": ["];return this.folds.forEach(function(t){e.push("  "+t.toString())}),e.push("]"),e.join("\n")},e.prototype.idxToPosition=function(e){for(var t=0,i=0;i<this.folds.length;i++){var n=this.folds[i];if((e-=n.start.column-t)<0)return{row:n.start.row,column:n.start.column+e};if((e-=n.placeholder.length)<0)return n.start;t=n.end.column}return{row:this.end.row,column:this.end.column+e}},e}()}),ace.define("ace/range_list",["require","exports","module","ace/range"],function(e,t,i){"use strict";var n=e("./range").Range.comparePoints,o=function(){function e(){this.ranges=[],this.$bias=1}return e.prototype.pointIndex=function(e,t,i){for(var o=this.ranges,r=i||0;r<o.length;r++){var s=o[r],a=n(e,s.end);if(!(a>0)){var l=n(e,s.start);if(0===a)return t&&0!==l?-r-2:r;if(l>0||0===l&&!t)return r;break}}return-r-1},e.prototype.add=function(e){var t=!e.isEmpty(),i=this.pointIndex(e.start,t);i<0&&(i=-i-1);var n=this.pointIndex(e.end,t,i);return n<0?n=-n-1:n++,this.ranges.splice(i,n-i,e)},e.prototype.addList=function(e){for(var t=[],i=e.length;i--;)t.push.apply(t,this.add(e[i]));return t},e.prototype.substractPoint=function(e){var t=this.pointIndex(e);if(t>=0)return this.ranges.splice(t,1)},e.prototype.merge=function(){for(var e,t=[],i=this.ranges,o=(i=i.sort(function(e,t){return n(e.start,t.start)}))[0],r=1;r<i.length;r++){e=o,o=i[r];var s=n(e.end,o.start);!(s<0)&&(0!=s||e.isEmpty()||o.isEmpty())&&(0>n(e.end,o.end)&&(e.end.row=o.end.row,e.end.column=o.end.column),i.splice(r,1),t.push(o),o=e,r--)}return this.ranges=i,t},e.prototype.contains=function(e,t){return this.pointIndex({row:e,column:t})>=0},e.prototype.containsPoint=function(e){return this.pointIndex(e)>=0},e.prototype.rangeAtPoint=function(e){var t=this.pointIndex(e);if(t>=0)return this.ranges[t]},e.prototype.clipRows=function(e,t){var i=this.ranges;if(i[0].start.row>t||i[i.length-1].start.row<e)return[];var n=this.pointIndex({row:e,column:0});n<0&&(n=-n-1);var o=this.pointIndex({row:t,column:0},n);o<0&&(o=-o-1);for(var r=[],s=n;s<o;s++)r.push(i[s]);return r},e.prototype.removeAll=function(){return this.ranges.splice(0,this.ranges.length)},e.prototype.attach=function(e){this.session&&this.detach(),this.session=e,this.onChange=this.$onChange.bind(this),this.session.on("change",this.onChange)},e.prototype.detach=function(){this.session&&(this.session.removeListener("change",this.onChange),this.session=null)},e.prototype.$onChange=function(e){for(var t=e.start,i=e.end,n=t.row,o=i.row,r=this.ranges,s=0,a=r.length;s<a;s++){var l=r[s];if(l.end.row>=n)break}if("insert"==e.action)for(var c=o-n,h=-t.column+i.column;s<a;s++){var l=r[s];if(l.start.row>n)break;if(l.start.row==n&&l.start.column>=t.column&&(l.start.column==t.column&&this.$bias<=0||(l.start.column+=h,l.start.row+=c)),l.end.row==n&&l.end.column>=t.column){if(l.end.column==t.column&&this.$bias<0)continue;l.end.column==t.column&&h>0&&s<a-1&&l.end.column>l.start.column&&l.end.column==r[s+1].start.column&&(l.end.column-=h),l.end.column+=h,l.end.row+=c}}else for(var c=n-o,h=t.column-i.column;s<a;s++){var l=r[s];if(l.start.row>o)break;l.end.row<o&&(n<l.end.row||n==l.end.row&&t.column<l.end.column)?(l.end.row=n,l.end.column=t.column):l.end.row==o?l.end.column<=i.column?(c||l.end.column>t.column)&&(l.end.column=t.column,l.end.row=t.row):(l.end.column+=h,l.end.row+=c):l.end.row>o&&(l.end.row+=c),l.start.row<o&&(n<l.start.row||n==l.start.row&&t.column<l.start.column)?(l.start.row=n,l.start.column=t.column):l.start.row==o?l.start.column<=i.column?(c||l.start.column>t.column)&&(l.start.column=t.column,l.start.row=t.row):(l.start.column+=h,l.start.row+=c):l.start.row>o&&(l.start.row+=c)}if(0!=c&&s<a)for(;s<a;s++){var l=r[s];l.start.row+=c,l.end.row+=c}},e}();o.prototype.comparePoints=n,t.RangeList=o}),ace.define("ace/edit_session/fold",["require","exports","module","ace/range_list"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)});function r(e,t){e.row-=t.row,0==e.row&&(e.column-=t.column)}function s(e,t){0==e.row&&(e.column+=t.column),e.row+=t.row}t.Fold=function(e){function t(t,i){var n=e.call(this)||this;return n.foldLine=null,n.placeholder=i,n.range=t,n.start=t.start,n.end=t.end,n.sameRow=t.start.row==t.end.row,n.subFolds=n.ranges=[],n}return o(t,e),t.prototype.toString=function(){return'"'+this.placeholder+'" '+this.range.toString()},t.prototype.setFoldLine=function(e){this.foldLine=e,this.subFolds.forEach(function(t){t.setFoldLine(e)})},t.prototype.clone=function(){var e=new t(this.range.clone(),this.placeholder);return this.subFolds.forEach(function(t){e.subFolds.push(t.clone())}),e.collapseChildren=this.collapseChildren,e},t.prototype.addSubFold=function(e){if(!this.range.isEqual(e)){t=e,i=this.start,r(t.start,i),r(t.end,i);for(var t,i,n=e.start.row,o=e.start.column,s=0,a=-1;s<this.subFolds.length&&1==(a=this.subFolds[s].range.compare(n,o));s++);var l=this.subFolds[s],c=0;if(0==a)if(l.range.containsRange(e))return l.addSubFold(e);else c=1;for(var n=e.range.end.row,o=e.range.end.column,h=s,a=-1;h<this.subFolds.length&&1==(a=this.subFolds[h].range.compare(n,o));h++);0==a&&h++;for(var u=this.subFolds.splice(s,h-s,e),d=0==a?u.length-1:u.length,p=c;p<d;p++)e.addSubFold(u[p]);return e.setFoldLine(this.foldLine),e}},t.prototype.restoreRange=function(e){var t,i;return t=e,i=this.start,void(s(t.start,i),s(t.end,i))},t}(e("../range_list").RangeList)}),ace.define("ace/edit_session/folding",["require","exports","module","ace/range","ace/edit_session/fold_line","ace/edit_session/fold","ace/token_iterator","ace/mouse/mouse_event"],function(e,t,i){"use strict";var n=e("../range").Range,o=e("./fold_line").FoldLine,r=e("./fold").Fold,s=e("../token_iterator").TokenIterator,a=e("../mouse/mouse_event").MouseEvent;t.Folding=function(){this.getFoldAt=function(e,t,i){var n=this.getFoldLine(e);if(!n)return null;for(var o=n.folds,r=0;r<o.length;r++){var s=o[r].range;if(s.contains(e,t)){if(1==i&&s.isEnd(e,t)&&!s.isEmpty()||-1==i&&s.isStart(e,t)&&!s.isEmpty())continue;return o[r]}}},this.getFoldsInRange=function(e){var t=e.start,i=e.end,n=this.$foldData,o=[];t.column+=1,i.column-=1;for(var r=0;r<n.length;r++){var s=n[r].range.compareRange(e);if(2!=s){if(-2==s)break;for(var a=n[r].folds,l=0;l<a.length;l++){var c=a[l];if(-2==(s=c.range.compareRange(e)))break;if(2!=s){if(42==s)break;o.push(c)}}}}return t.column-=1,i.column+=1,o},this.getFoldsInRangeList=function(e){if(Array.isArray(e)){var t=[];e.forEach(function(e){t=t.concat(this.getFoldsInRange(e))},this)}else var t=this.getFoldsInRange(e);return t},this.getAllFolds=function(){for(var e=[],t=this.$foldData,i=0;i<t.length;i++)for(var n=0;n<t[i].folds.length;n++)e.push(t[i].folds[n]);return e},this.getFoldStringAt=function(e,t,i,n){if(!(n=n||this.getFoldLine(e)))return null;for(var o,r,s={end:{column:0}},a=0;a<n.folds.length;a++){var l=(r=n.folds[a]).range.compareEnd(e,t);if(-1==l){o=this.getLine(r.start.row).substring(s.end.column,r.start.column);break}if(0===l)return null;s=r}return(o||(o=this.getLine(r.start.row).substring(s.end.column)),-1==i)?o.substring(0,t-s.end.column):1==i?o.substring(t-s.end.column):o},this.getFoldLine=function(e,t){var i=this.$foldData,n=0;for(t&&(n=i.indexOf(t)),-1==n&&(n=0);n<i.length;n++){var o=i[n];if(o.start.row<=e&&o.end.row>=e)return o;if(o.end.row>e)break}return null},this.getNextFoldLine=function(e,t){var i=this.$foldData,n=0;for(t&&(n=i.indexOf(t)),-1==n&&(n=0);n<i.length;n++){var o=i[n];if(o.end.row>=e)return o}return null},this.getFoldedRowCount=function(e,t){for(var i=this.$foldData,n=t-e+1,o=0;o<i.length;o++){var r=i[o],s=r.end.row,a=r.start.row;if(s>=t){a<t&&(a>=e?n-=t-a:n=0);break}s>=e&&(a>=e?n-=s-a:n-=s-e+1)}return n},this.$addFoldLine=function(e){return this.$foldData.push(e),this.$foldData.sort(function(e,t){return e.start.row-t.start.row}),e},this.addFold=function(e,t){var i,n=this.$foldData,s=!1;e instanceof r?i=e:(i=new r(t,e)).collapseChildren=t.collapseChildren,this.$clipRangeToDocument(i.range);var a=i.start.row,l=i.start.column,c=i.end.row,h=i.end.column,u=this.getFoldAt(a,l,1),d=this.getFoldAt(c,h,-1);if(u&&d==u)return u.addSubFold(i);u&&!u.range.isStart(a,l)&&this.removeFold(u),d&&!d.range.isEnd(c,h)&&this.removeFold(d);var p=this.getFoldsInRange(i.range);p.length>0&&(this.removeFolds(p),i.collapseChildren||p.forEach(function(e){i.addSubFold(e)}));for(var g=0;g<n.length;g++){var f=n[g];if(c==f.start.row){f.addFold(i),s=!0;break}if(a==f.end.row){if(f.addFold(i),s=!0,!i.sameRow){var m=n[g+1];m&&m.start.row==c&&f.merge(m)}break}if(c<=f.start.row)break}return s||(f=this.$addFoldLine(new o(this.$foldData,i))),this.$useWrapMode?this.$updateWrapData(f.start.row,f.start.row):this.$updateRowLengthCache(f.start.row,f.start.row),this.$modified=!0,this._signal("changeFold",{data:i,action:"add"}),i},this.addFolds=function(e){e.forEach(function(e){this.addFold(e)},this)},this.removeFold=function(e){var t=e.foldLine,i=t.start.row,n=t.end.row,o=this.$foldData,r=t.folds;if(1==r.length)o.splice(o.indexOf(t),1);else if(t.range.isEnd(e.end.row,e.end.column))r.pop(),t.end.row=r[r.length-1].end.row,t.end.column=r[r.length-1].end.column;else if(t.range.isStart(e.start.row,e.start.column))r.shift(),t.start.row=r[0].start.row,t.start.column=r[0].start.column;else if(e.sameRow)r.splice(r.indexOf(e),1);else{var s=t.split(e.start.row,e.start.column);(r=s.folds).shift(),s.start.row=r[0].start.row,s.start.column=r[0].start.column}this.$updating||(this.$useWrapMode?this.$updateWrapData(i,n):this.$updateRowLengthCache(i,n)),this.$modified=!0,this._signal("changeFold",{data:e,action:"remove"})},this.removeFolds=function(e){for(var t=[],i=0;i<e.length;i++)t.push(e[i]);t.forEach(function(e){this.removeFold(e)},this),this.$modified=!0},this.expandFold=function(e){this.removeFold(e),e.subFolds.forEach(function(t){e.restoreRange(t),this.addFold(t)},this),e.collapseChildren>0&&this.foldAll(e.start.row+1,e.end.row,e.collapseChildren-1),e.subFolds=[]},this.expandFolds=function(e){e.forEach(function(e){this.expandFold(e)},this)},this.unfold=function(e,t){if(null==e)i=new n(0,0,this.getLength(),0),null==t&&(t=!0);else if("number"==typeof e)i=new n(e,0,e,this.getLine(e).length);else if("row"in e)i=n.fromPoints(e,e);else{if(Array.isArray(e))return o=[],e.forEach(function(e){o=o.concat(this.unfold(e))},this),o;i=e}for(var i,o=this.getFoldsInRangeList(i),r=o;1==o.length&&0>n.comparePoints(o[0].start,i.start)&&n.comparePoints(o[0].end,i.end)>0;)this.expandFolds(o),o=this.getFoldsInRangeList(i);if(!1!=t?this.removeFolds(o):this.expandFolds(o),r.length)return r},this.isRowFolded=function(e,t){return!!this.getFoldLine(e,t)},this.getRowFoldEnd=function(e,t){var i=this.getFoldLine(e,t);return i?i.end.row:e},this.getRowFoldStart=function(e,t){var i=this.getFoldLine(e,t);return i?i.start.row:e},this.getFoldDisplayLine=function(e,t,i,n,o){null==n&&(n=e.start.row),null==o&&(o=0),null==t&&(t=e.end.row),null==i&&(i=this.getLine(t).length);var r=this.doc,s="";return e.walk(function(e,t,i,a){if(!(t<n)){if(t==n){if(i<o)return;a=Math.max(o,a)}null!=e?s+=e:s+=r.getLine(t).substring(a,i)}},t,i),s},this.getDisplayLine=function(e,t,i,n){var o,r=this.getFoldLine(e);return r?this.getFoldDisplayLine(r,e,t,i,n):(o=this.doc.getLine(e)).substring(n||0,t||o.length)},this.$cloneFoldData=function(){var e=[];return e=this.$foldData.map(function(t){var i=t.folds.map(function(e){return e.clone()});return new o(e,i)})},this.toggleFold=function(e){var t,i,n=this.selection.getRange();if(n.isEmpty()){var o=n.start;if(t=this.getFoldAt(o.row,o.column))return void this.expandFold(t);if(e){var r=this.getFoldLine(o.row);r&&this.expandFolds(r.folds);return}(i=this.findMatchingBracket(o))?1==n.comparePoint(i)?n.end=i:(n.start=i,n.start.column++,n.end.column--):(i=this.findMatchingBracket({row:o.row,column:o.column+1}))?(1==n.comparePoint(i)?n.end=i:n.start=i,n.start.column++):n=this.getCommentFoldRange(o.row,o.column)||n}else{var s=this.getFoldsInRange(n);if(e&&s.length)return void this.expandFolds(s);1==s.length&&(t=s[0])}if(t||(t=this.getFoldAt(n.start.row,n.start.column)),t&&t.range.toString()==n.toString())return void this.expandFold(t);var a="...";if(!n.isMultiLine()){if((a=this.getTextRange(n)).length<4)return;a=a.trim().substring(0,2)+".."}this.addFold(a,n)},this.getCommentFoldRange=function(e,t,i){var o=new s(this,e,t),r=o.getCurrentToken(),a=r&&r.type;if(r&&/^comment|string/.test(a)){"comment"==(a=a.match(/comment|string/)[0])&&(a+="|doc-start|\\.doc");var l=new RegExp(a),c=new n;if(1!=i){do r=o.stepBackward();while(r&&l.test(r.type));r=o.stepForward()}c.start.row=o.getCurrentTokenRow(),c.start.column=o.getCurrentTokenColumn()+r.value.length,o=new s(this,e,t);var h=this.getState(o.$row);if(-1!=i){var u=-1;do if(r=o.stepForward(),-1==u){var d=this.getState(o.$row);h.toString()!==d.toString()&&(u=o.$row)}else if(o.$row>u)break;while(r&&l.test(r.type));r=o.stepBackward()}else r=o.getCurrentToken();if(c.end.row=o.getCurrentTokenRow(),c.end.column=o.getCurrentTokenColumn(),c.start.row==c.end.row&&c.start.column>c.end.column)return;return c}},this.foldAll=function(e,t,i,n){void 0==i&&(i=1e5);var o=this.foldWidgets;if(o){t=t||this.getLength(),e=e||0;for(var r=e;r<t;r++)if((null==o[r]&&(o[r]=this.getFoldWidget(r)),"start"==o[r])&&(!n||n(r))){var s=this.getFoldWidgetRange(r);s&&s.isMultiLine()&&s.end.row<=t&&s.start.row>=e&&(r=s.end.row,s.collapseChildren=i,this.addFold("...",s))}}},this.foldToLevel=function(e){for(this.foldAll();e-- >0;)this.unfold(null,!1)},this.foldAllComments=function(){var e=this;this.foldAll(null,null,null,function(t){for(var i=e.getTokens(t),n=0;n<i.length;n++){var o=i[n];if(!("text"==o.type&&/^\s+$/.test(o.value))){if(/comment/.test(o.type))return!0;return!1}}})},this.$foldStyles={manual:1,markbegin:1,markbeginend:1},this.$foldStyle="markbegin",this.setFoldStyle=function(e){if(!this.$foldStyles[e])throw Error("invalid fold style: "+e+"["+Object.keys(this.$foldStyles).join(", ")+"]");if(this.$foldStyle!=e){this.$foldStyle=e,"manual"==e&&this.unfold();var t=this.$foldMode;this.$setFolding(null),this.$setFolding(t)}},this.$setFolding=function(e){if(this.$foldMode!=e){if(this.$foldMode=e,this.off("change",this.$updateFoldWidgets),this.off("tokenizerUpdate",this.$tokenizerUpdateFoldWidgets),this._signal("changeAnnotation"),!e||"manual"==this.$foldStyle){this.foldWidgets=null;return}this.foldWidgets=[],this.getFoldWidget=e.getFoldWidget.bind(e,this,this.$foldStyle),this.getFoldWidgetRange=e.getFoldWidgetRange.bind(e,this,this.$foldStyle),this.$updateFoldWidgets=this.updateFoldWidgets.bind(this),this.$tokenizerUpdateFoldWidgets=this.tokenizerUpdateFoldWidgets.bind(this),this.on("change",this.$updateFoldWidgets),this.on("tokenizerUpdate",this.$tokenizerUpdateFoldWidgets)}},this.getParentFoldRangeData=function(e,t){var i=this.foldWidgets;if(!i||t&&i[e])return{};for(var n,o=e-1;o>=0;){var r=i[o];if(null==r&&(r=i[o]=this.getFoldWidget(o)),"start"==r){var s=this.getFoldWidgetRange(o);if(n||(n=s),s&&s.end.row>=e)break}o--}return{range:-1!==o&&s,firstRange:n}},this.onFoldWidgetClick=function(e,t){t instanceof a&&(t=t.domEvent);var i={children:t.shiftKey,all:t.ctrlKey||t.metaKey,siblings:t.altKey};if(!this.$toggleFoldWidget(e,i)){var n=t.target||t.srcElement;n&&/ace_fold-widget/.test(n.className)&&(n.className+=" ace_invalid")}},this.$toggleFoldWidget=function(e,t){if(this.getFoldWidget){var i=this.getFoldWidget(e),n=this.getLine(e),o="end"===i?-1:1,r=this.getFoldAt(e,-1===o?0:n.length,o);if(r)return t.children||t.all?this.removeFold(r):this.expandFold(r),r;var s=this.getFoldWidgetRange(e,!0);if(s&&!s.isMultiLine()&&(r=this.getFoldAt(s.start.row,s.start.column,1))&&s.isEqual(r.range))return this.removeFold(r),r;if(t.siblings){var a=this.getParentFoldRangeData(e);if(a.range)var l=a.range.start.row+1,c=a.range.end.row;this.foldAll(l,c,1e4*!!t.all)}else t.children?(c=s?s.end.row:this.getLength(),this.foldAll(e+1,c,1e4*!!t.all)):s&&(t.all&&(s.collapseChildren=1e4),this.addFold("...",s));return s}},this.toggleFoldWidget=function(e){var t=this.selection.getCursor().row;t=this.getRowFoldStart(t);var i=this.$toggleFoldWidget(t,{});if(!i){var n=this.getParentFoldRangeData(t,!0);if(i=n.range||n.firstRange){t=i.start.row;var o=this.getFoldAt(t,this.getLine(t).length,1);o?this.removeFold(o):this.addFold("...",i)}}},this.updateFoldWidgets=function(e){var t=e.start.row,i=e.end.row-t;if(0===i)this.foldWidgets[t]=null;else if("remove"==e.action)this.foldWidgets.splice(t,i+1,null);else{var n=Array(i+1);n.unshift(t,1),this.foldWidgets.splice.apply(this.foldWidgets,n)}},this.tokenizerUpdateFoldWidgets=function(e){var t=e.data;t.first!=t.last&&this.foldWidgets.length>t.first&&this.foldWidgets.splice(t.first,this.foldWidgets.length)}}}),ace.define("ace/edit_session/bracket_match",["require","exports","module","ace/token_iterator","ace/range"],function(e,t,i){"use strict";var n=e("../token_iterator").TokenIterator,o=e("../range").Range;t.BracketMatch=function(){this.findMatchingBracket=function(e,t){if(0==e.column)return null;var i=t||this.getLine(e.row).charAt(e.column-1);if(""==i)return null;var n=i.match(/([\(\[\{])|([\)\]\}])/);return n?n[1]?this.$findClosingBracket(n[1],e):this.$findOpeningBracket(n[2],e):null},this.getBracketRange=function(e){var t,i=this.getLine(e.row),n=!0,r=i.charAt(e.column-1),s=r&&r.match(/([\(\[\{])|([\)\]\}])/);if(s||(r=i.charAt(e.column),e={row:e.row,column:e.column+1},s=r&&r.match(/([\(\[\{])|([\)\]\}])/),n=!1),!s)return null;if(s[1]){var a=this.$findClosingBracket(s[1],e);if(!a)return null;t=o.fromPoints(e,a),!n&&(t.end.column++,t.start.column--),t.cursor=t.end}else{var a=this.$findOpeningBracket(s[2],e);if(!a)return null;t=o.fromPoints(a,e),!n&&(t.start.column++,t.end.column--),t.cursor=t.start}return t},this.getMatchingBracketRanges=function(e,t){var i=this.getLine(e.row),n=/([\(\[\{])|([\)\]\}])/,r=!t&&i.charAt(e.column-1),s=r&&r.match(n);if(s||(r=(void 0===t||t)&&i.charAt(e.column),e={row:e.row,column:e.column+1},s=r&&r.match(n)),!s)return null;var a=new o(e.row,e.column-1,e.row,e.column),l=s[1]?this.$findClosingBracket(s[1],e):this.$findOpeningBracket(s[2],e);return l?[a,new o(l.row,l.column,l.row,l.column+1)]:[a]},this.$brackets={")":"(","(":")","]":"[","[":"]","{":"}","}":"{","<":">",">":"<"},this.$findOpeningBracket=function(e,t,i){var o=this.$brackets[e],r=1,s=new n(this,t.row,t.column),a=s.getCurrentToken();if(a||(a=s.stepForward()),a){i||(i=RegExp("(\\.?"+a.type.replace(".","\\.").replace("rparen",".paren").replace(/\b(?:end)\b/,"(?:start|begin|end)").replace(/-close\b/,"-(close|open)")+")+"));for(var l=t.column-s.getCurrentTokenColumn()-2,c=a.value;;){for(;l>=0;){var h=c.charAt(l);if(h==o){if(0==(r-=1))return{row:s.getCurrentTokenRow(),column:l+s.getCurrentTokenColumn()}}else h==e&&(r+=1);l-=1}do a=s.stepBackward();while(a&&!i.test(a.type));if(null==a)break;l=(c=a.value).length-1}return null}},this.$findClosingBracket=function(e,t,i){var o=this.$brackets[e],r=1,s=new n(this,t.row,t.column),a=s.getCurrentToken();if(a||(a=s.stepForward()),a){i||(i=RegExp("(\\.?"+a.type.replace(".","\\.").replace("lparen",".paren").replace(/\b(?:start|begin)\b/,"(?:start|begin|end)").replace(/-open\b/,"-(close|open)")+")+"));for(var l=t.column-s.getCurrentTokenColumn();;){for(var c=a.value,h=c.length;l<h;){var u=c.charAt(l);if(u==o){if(0==(r-=1))return{row:s.getCurrentTokenRow(),column:l+s.getCurrentTokenColumn()}}else u==e&&(r+=1);l+=1}do a=s.stepForward();while(a&&!i.test(a.type));if(null==a)break;l=0}return null}},this.getMatchingTags=function(e){var t=new n(this,e.row,e.column),i=this.$findTagName(t);if(i)return"<"===t.stepBackward().value?this.$findClosingTag(t,i):this.$findOpeningTag(t,i)},this.$findTagName=function(e){var t=e.getCurrentToken(),i=!1,n=!1;if(t&&-1===t.type.indexOf("tag-name"))do(t=n?e.stepBackward():e.stepForward())&&("/>"===t.value?n=!0:-1!==t.type.indexOf("tag-name")&&(i=!0));while(t&&!i);return t},this.$findClosingTag=function(e,t){var i,n=t.value,r=t.value,s=0,a=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1);t=e.stepForward();var l=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+t.value.length),c=!1;do{if(-1!==(i=t).type.indexOf("tag-close")&&!c){var h=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1);c=!0}if(t=e.stepForward()){if(">"===t.value&&!c){var h=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1);c=!0}if(-1!==t.type.indexOf("tag-name")){if(r===(n=t.value)){if("<"===i.value)s++;else if("</"===i.value&&--s<0){e.stepBackward();var u=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+2);t=e.stepForward();var d=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+t.value.length);if(-1===t.type.indexOf("tag-close")&&(t=e.stepForward()),!t||">"!==t.value)return;var p=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1)}}}else if(r===n&&"/>"===t.value&&--s<0)var u=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+2),d=u,p=d,h=new o(l.end.row,l.end.column,l.end.row,l.end.column+1)}}while(t&&s>=0);if(a&&h&&u&&p&&l&&d)return{openTag:new o(a.start.row,a.start.column,h.end.row,h.end.column),closeTag:new o(u.start.row,u.start.column,p.end.row,p.end.column),openTagName:l,closeTagName:d}},this.$findOpeningTag=function(e,t){var i=e.getCurrentToken(),n=t.value,r=0,s=e.getCurrentTokenRow(),a=e.getCurrentTokenColumn(),l=a+2,c=new o(s,a,s,l);e.stepForward();var h=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+t.value.length);if(-1===t.type.indexOf("tag-close")&&(t=e.stepForward()),t&&">"===t.value){var u=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1);e.stepBackward(),e.stepBackward();do if(t=i,s=e.getCurrentTokenRow(),l=(a=e.getCurrentTokenColumn())+t.value.length,i=e.stepBackward(),t){if(-1!==t.type.indexOf("tag-name")){if(n===t.value)if("<"===i.value){if(++r>0){var d=new o(s,a,s,l),p=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1);do t=e.stepForward();while(t&&">"!==t.value);var g=new o(e.getCurrentTokenRow(),e.getCurrentTokenColumn(),e.getCurrentTokenRow(),e.getCurrentTokenColumn()+1)}}else"</"===i.value&&r--}else if("/>"===t.value){for(var f=0,m=i;m;){if(-1!==m.type.indexOf("tag-name")&&m.value===n){r--;break}if("<"===m.value)break;m=e.stepBackward(),f++}for(var y=0;y<f;y++)e.stepForward()}}while(i&&r<=0);if(p&&g&&c&&u&&d&&h)return{openTag:new o(p.start.row,p.start.column,g.end.row,g.end.column),closeTag:new o(c.start.row,c.start.column,u.end.row,u.end.column),openTagName:d,closeTagName:h}}}}}),ace.define("ace/edit_session",["require","exports","module","ace/lib/oop","ace/lib/lang","ace/bidihandler","ace/config","ace/lib/event_emitter","ace/selection","ace/mode/text","ace/range","ace/line_widgets","ace/document","ace/background_tokenizer","ace/search_highlight","ace/undomanager","ace/edit_session/folding","ace/edit_session/bracket_match"],function(e,t,i){"use strict";var n=e("./lib/oop"),o=e("./lib/lang"),r=e("./bidihandler").BidiHandler,s=e("./config"),a=e("./lib/event_emitter").EventEmitter,l=e("./selection").Selection,c=e("./mode/text").Mode,h=e("./range").Range,u=e("./line_widgets").LineWidgets,d=e("./document").Document,p=e("./background_tokenizer").BackgroundTokenizer,g=e("./search_highlight").SearchHighlight,f=e("./undomanager").UndoManager,m=function(){function e(t,i){this.doc,this.$breakpoints=[],this.$decorations=[],this.$frontMarkers={},this.$backMarkers={},this.$markerId=1,this.$undoSelect=!0,this.$editor=null,this.prevOp={},this.$foldData=[],this.id="session"+ ++e.$uid,this.$foldData.toString=function(){return this.join("\n")},this.$gutterCustomWidgets={},this.bgTokenizer=new p(new c().getTokenizer(),this);var n=this;this.bgTokenizer.on("update",function(e){n._signal("tokenizerUpdate",e)}),this.on("changeFold",this.onChangeFold.bind(this)),this.$onChange=this.onChange.bind(this),"object"==typeof t&&t.getLine||(t=new d(t)),this.setDocument(t),this.selection=new l(this),this.$onSelectionChange=this.onSelectionChange.bind(this),this.selection.on("changeSelection",this.$onSelectionChange),this.selection.on("changeCursor",this.$onSelectionChange),this.$bidiHandler=new r(this),s.resetOptions(this),this.setMode(i),s._signal("session",this),this.destroyed=!1,this.$initOperationListeners()}return e.prototype.$initOperationListeners=function(){var e=this;this.curOp=null,this.on("change",function(){e.curOp||(e.startOperation(),e.curOp.selectionBefore=e.$lastSel),e.curOp.docChanged=!0},!0),this.on("changeSelection",function(){e.curOp||(e.startOperation(),e.curOp.selectionBefore=e.$lastSel),e.curOp.selectionChanged=!0},!0),this.$operationResetTimer=o.delayedCall(this.endOperation.bind(this,!0))},e.prototype.startOperation=function(e){if(this.curOp){if(!e||this.curOp.command)return;this.prevOp=this.curOp}e||(e={}),this.$operationResetTimer.schedule(),this.curOp={command:e.command||{},args:e.args},this.curOp.selectionBefore=this.selection.toJSON(),this._signal("startOperation",e)},e.prototype.endOperation=function(e){if(this.curOp){if(e&&!1===e.returnValue){this.curOp=null,this._signal("endOperation",e);return}if(!0!=e||!this.curOp.command||"mouse"!=this.curOp.command.name){var t=this.selection.toJSON();this.curOp.selectionAfter=t,this.$lastSel=this.selection.toJSON(),this.getUndoManager().addSelection(t),this._signal("beforeEndOperation"),this.prevOp=this.curOp,this.curOp=null,this._signal("endOperation",e)}}},e.prototype.setDocument=function(e){this.doc&&this.doc.off("change",this.$onChange),this.doc=e,e.on("change",this.$onChange,!0),this.bgTokenizer.setDocument(this.getDocument()),this.resetCaches()},e.prototype.getDocument=function(){return this.doc},Object.defineProperty(e.prototype,"widgetManager",{get:function(){var e=new u(this);return this.widgetManager=e,this.$editor&&e.attach(this.$editor),e},set:function(e){Object.defineProperty(this,"widgetManager",{writable:!0,enumerable:!0,configurable:!0,value:e})},enumerable:!1,configurable:!0}),e.prototype.$resetRowCache=function(e){if(!e){this.$docRowCache=[],this.$screenRowCache=[];return}var t=this.$docRowCache.length,i=this.$getRowCacheIndex(this.$docRowCache,e)+1;t>i&&(this.$docRowCache.splice(i,t),this.$screenRowCache.splice(i,t))},e.prototype.$getRowCacheIndex=function(e,t){for(var i=0,n=e.length-1;i<=n;){var o=i+n>>1,r=e[o];if(t>r)i=o+1;else{if(!(t<r))return o;n=o-1}}return i-1},e.prototype.resetCaches=function(){this.$modified=!0,this.$wrapData=[],this.$rowLengthCache=[],this.$resetRowCache(0),this.destroyed||this.bgTokenizer.start(0)},e.prototype.onChangeFold=function(e){var t=e.data;this.$resetRowCache(t.start.row)},e.prototype.onChange=function(e){this.$modified=!0,this.$bidiHandler.onChange(e),this.$resetRowCache(e.start.row);var t=this.$updateInternalDataOnChange(e);!this.$fromUndo&&this.$undoManager&&(t&&t.length&&(this.$undoManager.add({action:"removeFolds",folds:t},this.mergeUndoDeltas),this.mergeUndoDeltas=!0),this.$undoManager.add(e,this.mergeUndoDeltas),this.mergeUndoDeltas=!0,this.$informUndoManager.schedule()),this.bgTokenizer.$updateOnChange(e),this._signal("change",e)},e.prototype.onSelectionChange=function(){this._signal("changeSelection")},e.prototype.setValue=function(e){this.doc.setValue(e),this.selection.moveTo(0,0),this.$resetRowCache(0),this.setUndoManager(this.$undoManager),this.getUndoManager().reset()},e.fromJSON=function(t){"string"==typeof t&&(t=JSON.parse(t));var i=new f;i.$undoStack=t.history.undo,i.$redoStack=t.history.redo,i.mark=t.history.mark,i.$rev=t.history.rev;var n=new e(t.value);return t.folds.forEach(function(e){n.addFold("...",h.fromPoints(e.start,e.end))}),n.setAnnotations(t.annotations),n.setBreakpoints(t.breakpoints),n.setMode(t.mode),n.setScrollLeft(t.scrollLeft),n.setScrollTop(t.scrollTop),n.setUndoManager(i),n.selection.fromJSON(t.selection),n},e.prototype.toJSON=function(){return{annotations:this.$annotations,breakpoints:this.$breakpoints,folds:this.getAllFolds().map(function(e){return e.range}),history:this.getUndoManager(),mode:this.$mode.$id,scrollLeft:this.$scrollLeft,scrollTop:this.$scrollTop,selection:this.selection.toJSON(),value:this.doc.getValue()}},e.prototype.toString=function(){return this.doc.getValue()},e.prototype.getSelection=function(){return this.selection},e.prototype.getState=function(e){return this.bgTokenizer.getState(e)},e.prototype.getTokens=function(e){return this.bgTokenizer.getTokens(e)},e.prototype.getTokenAt=function(e,t){var i,n=this.bgTokenizer.getTokens(e),o=0;if(null==t){var r=n.length-1;o=this.getLine(e).length}else for(var r=0;r<n.length&&!((o+=n[r].value.length)>=t);r++);return(i=n[r])?(i.index=r,i.start=o-i.value.length,i):null},e.prototype.setUndoManager=function(e){if(this.$undoManager=e,this.$informUndoManager&&this.$informUndoManager.cancel(),e){var t=this;e.addSession(this),this.$syncInformUndoManager=function(){t.$informUndoManager.cancel(),t.mergeUndoDeltas=!1},this.$informUndoManager=o.delayedCall(this.$syncInformUndoManager)}else this.$syncInformUndoManager=function(){}},e.prototype.markUndoGroup=function(){this.$syncInformUndoManager&&this.$syncInformUndoManager()},e.prototype.getUndoManager=function(){return this.$undoManager||this.$defaultUndoManager},e.prototype.getTabString=function(){return this.getUseSoftTabs()?o.stringRepeat(" ",this.getTabSize()):"	"},e.prototype.setUseSoftTabs=function(e){this.setOption("useSoftTabs",e)},e.prototype.getUseSoftTabs=function(){return this.$useSoftTabs&&!this.$mode.$indentWithTabs},e.prototype.setTabSize=function(e){this.setOption("tabSize",e)},e.prototype.getTabSize=function(){return this.$tabSize},e.prototype.isTabStop=function(e){return this.$useSoftTabs&&e.column%this.$tabSize==0},e.prototype.setNavigateWithinSoftTabs=function(e){this.setOption("navigateWithinSoftTabs",e)},e.prototype.getNavigateWithinSoftTabs=function(){return this.$navigateWithinSoftTabs},e.prototype.setOverwrite=function(e){this.setOption("overwrite",e)},e.prototype.getOverwrite=function(){return this.$overwrite},e.prototype.toggleOverwrite=function(){this.setOverwrite(!this.$overwrite)},e.prototype.addGutterDecoration=function(e,t){this.$decorations[e]||(this.$decorations[e]=""),this.$decorations[e]+=" "+t,this._signal("changeBreakpoint",{})},e.prototype.removeGutterCustomWidget=function(e){this.$editor&&this.$editor.renderer.$gutterLayer.$removeCustomWidget(e)},e.prototype.addGutterCustomWidget=function(e,t){this.$editor&&this.$editor.renderer.$gutterLayer.$addCustomWidget(e,t)},e.prototype.removeGutterDecoration=function(e,t){this.$decorations[e]=(this.$decorations[e]||"").replace(" "+t,""),this._signal("changeBreakpoint",{})},e.prototype.getBreakpoints=function(){return this.$breakpoints},e.prototype.setBreakpoints=function(e){this.$breakpoints=[];for(var t=0;t<e.length;t++)this.$breakpoints[e[t]]="ace_breakpoint";this._signal("changeBreakpoint",{})},e.prototype.clearBreakpoints=function(){this.$breakpoints=[],this._signal("changeBreakpoint",{})},e.prototype.setBreakpoint=function(e,t){void 0===t&&(t="ace_breakpoint"),t?this.$breakpoints[e]=t:delete this.$breakpoints[e],this._signal("changeBreakpoint",{})},e.prototype.clearBreakpoint=function(e){delete this.$breakpoints[e],this._signal("changeBreakpoint",{})},e.prototype.addMarker=function(e,t,i,n){var o=this.$markerId++,r={range:e,type:i||"line",renderer:"function"==typeof i?i:null,clazz:t,inFront:!!n,id:o};return n?(this.$frontMarkers[o]=r,this._signal("changeFrontMarker")):(this.$backMarkers[o]=r,this._signal("changeBackMarker")),o},e.prototype.addDynamicMarker=function(e,t){if(e.update){var i=this.$markerId++;return e.id=i,e.inFront=!!t,t?(this.$frontMarkers[i]=e,this._signal("changeFrontMarker")):(this.$backMarkers[i]=e,this._signal("changeBackMarker")),e}},e.prototype.removeMarker=function(e){var t=this.$frontMarkers[e]||this.$backMarkers[e];if(t){var i=t.inFront?this.$frontMarkers:this.$backMarkers;delete i[e],this._signal(t.inFront?"changeFrontMarker":"changeBackMarker")}},e.prototype.getMarkers=function(e){return e?this.$frontMarkers:this.$backMarkers},e.prototype.highlight=function(e){if(!this.$searchHighlight){var t=new g(null,"ace_selected-word","text");this.$searchHighlight=this.addDynamicMarker(t)}this.$searchHighlight.setRegexp(e)},e.prototype.highlightLines=function(e,t,i,n){"number"!=typeof t&&(i=t,t=e),i||(i="ace_step");var o=new h(e,0,t,1/0);return o.id=this.addMarker(o,i,"fullLine",n),o},e.prototype.setAnnotations=function(e){this.$annotations=e,this._signal("changeAnnotation",{})},e.prototype.getAnnotations=function(){return this.$annotations||[]},e.prototype.clearAnnotations=function(){this.setAnnotations([])},e.prototype.$detectNewLine=function(e){var t=e.match(/^.*?(\r?\n)/m);t?this.$autoNewLine=t[1]:this.$autoNewLine="\n"},e.prototype.getWordRange=function(e,t){var i=this.getLine(e),n=!1;if(t>0&&(n=!!i.charAt(t-1).match(this.tokenRe)),n||(n=!!i.charAt(t).match(this.tokenRe)),n)var o=this.tokenRe;else if(/^\s+$/.test(i.slice(t-1,t+1)))var o=/\s/;else var o=this.nonTokenRe;var r=t;if(r>0){do r--;while(r>=0&&i.charAt(r).match(o));r++}for(var s=t;s<i.length&&i.charAt(s).match(o);)s++;return new h(e,r,e,s)},e.prototype.getAWordRange=function(e,t){for(var i=this.getWordRange(e,t),n=this.getLine(i.end.row);n.charAt(i.end.column).match(/[ \t]/);)i.end.column+=1;return i},e.prototype.setNewLineMode=function(e){this.doc.setNewLineMode(e)},e.prototype.getNewLineMode=function(){return this.doc.getNewLineMode()},e.prototype.setUseWorker=function(e){this.setOption("useWorker",e)},e.prototype.getUseWorker=function(){return this.$useWorker},e.prototype.onReloadTokenizer=function(e){var t=e.data;this.bgTokenizer.start(t.first),this._signal("tokenizerUpdate",e)},e.prototype.setMode=function(e,t){if(e&&"object"==typeof e){if(e.getTokenizer)return this.$onChangeMode(e);var i=e,n=i.path}else n=e||"ace/mode/text";if(this.$modes["ace/mode/text"]||(this.$modes["ace/mode/text"]=new c),this.$modes[n]&&!i){this.$onChangeMode(this.$modes[n]),t&&t();return}this.$modeId=n,s.loadModule(["mode",n],(function(e){if(!this.destroyed){if(this.$modeId!==n)return t&&t();this.$modes[n]&&!i?this.$onChangeMode(this.$modes[n]):e&&e.Mode&&(e=new e.Mode(i),i||(this.$modes[n]=e,e.$id=n),this.$onChangeMode(e)),t&&t()}}).bind(this)),this.$mode||this.$onChangeMode(this.$modes["ace/mode/text"],!0)},e.prototype.$onChangeMode=function(e,t){if(t||(this.$modeId=e.$id),this.$mode!==e){var i=this.$mode;this.$mode=e,this.$stopWorker(),this.$useWorker&&this.$startWorker();var n=e.getTokenizer();if(void 0!==n.on){var o=this.onReloadTokenizer.bind(this);n.on("update",o)}this.bgTokenizer.setTokenizer(n),this.bgTokenizer.setDocument(this.getDocument()),this.tokenRe=e.tokenRe,this.nonTokenRe=e.nonTokenRe,t||(e.attachToSession&&e.attachToSession(this),this.$options.wrapMethod.set.call(this,this.$wrapMethod),this.$setFolding(e.foldingRules),this.bgTokenizer.start(0),this._emit("changeMode",{oldMode:i,mode:e}))}},e.prototype.$stopWorker=function(){this.$worker&&(this.$worker.terminate(),this.$worker=null)},e.prototype.$startWorker=function(){try{this.$worker=this.$mode.createWorker(this)}catch(e){s.warn("Could not load worker",e),this.$worker=null}},e.prototype.getMode=function(){return this.$mode},e.prototype.setScrollTop=function(e){this.$scrollTop===e||isNaN(e)||(this.$scrollTop=e,this._signal("changeScrollTop",e))},e.prototype.getScrollTop=function(){return this.$scrollTop},e.prototype.setScrollLeft=function(e){this.$scrollLeft===e||isNaN(e)||(this.$scrollLeft=e,this._signal("changeScrollLeft",e))},e.prototype.getScrollLeft=function(){return this.$scrollLeft},e.prototype.getScreenWidth=function(){return(this.$computeWidth(),this.lineWidgets)?Math.max(this.getLineWidgetMaxWidth(),this.screenWidth):this.screenWidth},e.prototype.getLineWidgetMaxWidth=function(){if(null!=this.lineWidgetsWidth)return this.lineWidgetsWidth;var e=0;return this.lineWidgets.forEach(function(t){t&&t.screenWidth>e&&(e=t.screenWidth)}),this.lineWidgetWidth=e},e.prototype.$computeWidth=function(e){if(this.$modified||e){if(this.$modified=!1,this.$useWrapMode)return this.screenWidth=this.$wrapLimit;for(var t=this.doc.getAllLines(),i=this.$rowLengthCache,n=0,o=0,r=this.$foldData[o],s=r?r.start.row:1/0,a=t.length,l=0;l<a;l++){if(l>s){if((l=r.end.row+1)>=a)break;s=(r=this.$foldData[o++])?r.start.row:1/0}null==i[l]&&(i[l]=this.$getStringScreenWidth(t[l])[0]),i[l]>n&&(n=i[l])}this.screenWidth=n}},e.prototype.getLine=function(e){return this.doc.getLine(e)},e.prototype.getLines=function(e,t){return this.doc.getLines(e,t)},e.prototype.getLength=function(){return this.doc.getLength()},e.prototype.getTextRange=function(e){return this.doc.getTextRange(e||this.selection.getRange())},e.prototype.insert=function(e,t){return this.doc.insert(e,t)},e.prototype.remove=function(e){return this.doc.remove(e)},e.prototype.removeFullLines=function(e,t){return this.doc.removeFullLines(e,t)},e.prototype.undoChanges=function(e,t){if(e.length){this.$fromUndo=!0;for(var i=e.length-1;-1!=i;i--){var n=e[i];"insert"==n.action||"remove"==n.action?this.doc.revertDelta(n):n.folds&&this.addFolds(n.folds)}!t&&this.$undoSelect&&(e.selectionBefore?this.selection.fromJSON(e.selectionBefore):this.selection.setRange(this.$getUndoSelection(e,!0))),this.$fromUndo=!1}},e.prototype.redoChanges=function(e,t){if(e.length){this.$fromUndo=!0;for(var i=0;i<e.length;i++){var n=e[i];("insert"==n.action||"remove"==n.action)&&this.doc.$safeApplyDelta(n)}!t&&this.$undoSelect&&(e.selectionAfter?this.selection.fromJSON(e.selectionAfter):this.selection.setRange(this.$getUndoSelection(e,!1))),this.$fromUndo=!1}},e.prototype.setUndoSelect=function(e){this.$undoSelect=e},e.prototype.$getUndoSelection=function(e,t){function i(e){return t?"insert"!==e.action:"insert"===e.action}for(var n,o,r=0;r<e.length;r++){var s=e[r];if(s.start){if(!n){n=i(s)?h.fromPoints(s.start,s.end):h.fromPoints(s.start,s.start);continue}i(s)?(o=s.start,-1==n.compare(o.row,o.column)&&n.setStart(o),o=s.end,1==n.compare(o.row,o.column)&&n.setEnd(o)):(o=s.start,-1==n.compare(o.row,o.column)&&(n=h.fromPoints(s.start,s.start)))}}return n},e.prototype.replace=function(e,t){return this.doc.replace(e,t)},e.prototype.moveText=function(e,t,i){var n=this.getTextRange(e),o=this.getFoldsInRange(e),r=h.fromPoints(t,t);if(!i){this.remove(e);var s=e.start.row-e.end.row,a=s?-e.end.column:e.start.column-e.end.column;a&&(r.start.row==e.end.row&&r.start.column>e.end.column&&(r.start.column+=a),r.end.row==e.end.row&&r.end.column>e.end.column&&(r.end.column+=a)),s&&r.start.row>=e.end.row&&(r.start.row+=s,r.end.row+=s)}if(r.end=this.insert(r.start,n),o.length){var l=e.start,c=r.start,s=c.row-l.row,a=c.column-l.column;this.addFolds(o.map(function(e){return(e=e.clone()).start.row==l.row&&(e.start.column+=a),e.end.row==l.row&&(e.end.column+=a),e.start.row+=s,e.end.row+=s,e}))}return r},e.prototype.indentRows=function(e,t,i){i=i.replace(/\t/g,this.getTabString());for(var n=e;n<=t;n++)this.doc.insertInLine({row:n,column:0},i)},e.prototype.outdentRows=function(e){for(var t=e.collapseRows(),i=new h(0,0,0,0),n=this.getTabSize(),o=t.start.row;o<=t.end.row;++o){var r=this.getLine(o);i.start.row=o,i.end.row=o;for(var s=0;s<n&&" "==r.charAt(s);++s);s<n&&"	"==r.charAt(s)?(i.start.column=s,i.end.column=s+1):(i.start.column=0,i.end.column=s),this.remove(i)}},e.prototype.$moveLines=function(e,t,i){if(e=this.getRowFoldStart(e),t=this.getRowFoldEnd(t),i<0){var n=this.getRowFoldStart(e+i);if(n<0)return 0;var o=n-e}else if(i>0){var n=this.getRowFoldEnd(t+i);if(n>this.doc.getLength()-1)return 0;var o=n-t}else{e=this.$clipRowToDocument(e);var o=(t=this.$clipRowToDocument(t))-e+1}var r=new h(e,0,t,Number.MAX_VALUE),s=this.getFoldsInRange(r).map(function(e){return e=e.clone(),e.start.row+=o,e.end.row+=o,e}),a=0==i?this.doc.getLines(e,t):this.doc.removeFullLines(e,t);return this.doc.insertFullLines(e+o,a),s.length&&this.addFolds(s),o},e.prototype.moveLinesUp=function(e,t){return this.$moveLines(e,t,-1)},e.prototype.moveLinesDown=function(e,t){return this.$moveLines(e,t,1)},e.prototype.duplicateLines=function(e,t){return this.$moveLines(e,t,0)},e.prototype.$clipRowToDocument=function(e){return Math.max(0,Math.min(e,this.doc.getLength()-1))},e.prototype.$clipColumnToRow=function(e,t){return t<0?0:Math.min(this.doc.getLine(e).length,t)},e.prototype.$clipPositionToDocument=function(e,t){if(t=Math.max(0,t),e<0)e=0,t=0;else{var i=this.doc.getLength();e>=i?(e=i-1,t=this.doc.getLine(i-1).length):t=Math.min(this.doc.getLine(e).length,t)}return{row:e,column:t}},e.prototype.$clipRangeToDocument=function(e){e.start.row<0?(e.start.row=0,e.start.column=0):e.start.column=this.$clipColumnToRow(e.start.row,e.start.column);var t=this.doc.getLength()-1;return e.end.row>t?(e.end.row=t,e.end.column=this.doc.getLine(t).length):e.end.column=this.$clipColumnToRow(e.end.row,e.end.column),e},e.prototype.setUseWrapMode=function(e){if(e!=this.$useWrapMode){if(this.$useWrapMode=e,this.$modified=!0,this.$resetRowCache(0),e){var t=this.getLength();this.$wrapData=Array(t),this.$updateWrapData(0,t-1)}this._signal("changeWrapMode")}},e.prototype.getUseWrapMode=function(){return this.$useWrapMode},e.prototype.setWrapLimitRange=function(e,t){(this.$wrapLimitRange.min!==e||this.$wrapLimitRange.max!==t)&&(this.$wrapLimitRange={min:e,max:t},this.$modified=!0,this.$bidiHandler.markAsDirty(),this.$useWrapMode&&this._signal("changeWrapMode"))},e.prototype.adjustWrapLimit=function(e,t){var i=this.$wrapLimitRange;i.max<0&&(i={min:t,max:t});var n=this.$constrainWrapLimit(e,i.min,i.max);return n!=this.$wrapLimit&&n>1&&(this.$wrapLimit=n,this.$modified=!0,this.$useWrapMode&&(this.$updateWrapData(0,this.getLength()-1),this.$resetRowCache(0),this._signal("changeWrapLimit")),!0)},e.prototype.$constrainWrapLimit=function(e,t,i){return t&&(e=Math.max(t,e)),i&&(e=Math.min(i,e)),e},e.prototype.getWrapLimit=function(){return this.$wrapLimit},e.prototype.setWrapLimit=function(e){this.setWrapLimitRange(e,e)},e.prototype.getWrapLimitRange=function(){return{min:this.$wrapLimitRange.min,max:this.$wrapLimitRange.max}},e.prototype.$updateInternalDataOnChange=function(e){var t=this.$useWrapMode,i=e.action,n=e.start,o=e.end,r=n.row,s=o.row,a=s-r,l=null;if(this.$updating=!0,0!=a)if("remove"===i){this[t?"$wrapData":"$rowLengthCache"].splice(r,a);var c=this.$foldData;l=this.getFoldsInRange(e),this.removeFolds(l);var h=this.getFoldLine(o.row),u=0;if(h){h.addRemoveChars(o.row,o.column,n.column-o.column),h.shiftRow(-a);var d=this.getFoldLine(r);d&&d!==h&&(d.merge(h),h=d),u=c.indexOf(h)+1}for(;u<c.length;u++){var h=c[u];h.start.row>=o.row&&h.shiftRow(-a)}s=r}else{var p=Array(a);p.unshift(r,0);var g=t?this.$wrapData:this.$rowLengthCache;g.splice.apply(g,p);var c=this.$foldData,h=this.getFoldLine(r),u=0;if(h){var f=h.range.compareInside(n.row,n.column);0==f?(h=h.split(n.row,n.column))&&(h.shiftRow(a),h.addRemoveChars(s,0,o.column-n.column)):-1==f&&(h.addRemoveChars(r,0,o.column-n.column),h.shiftRow(a)),u=c.indexOf(h)+1}for(;u<c.length;u++){var h=c[u];h.start.row>=r&&h.shiftRow(a)}}else{a=Math.abs(e.start.column-e.end.column),"remove"===i&&(l=this.getFoldsInRange(e),this.removeFolds(l),a=-a);var h=this.getFoldLine(r);h&&h.addRemoveChars(r,n.column,a)}return t&&this.$wrapData.length!=this.doc.getLength()&&console.error("doc.getLength() and $wrapData.length have to be the same!"),this.$updating=!1,t?this.$updateWrapData(r,s):this.$updateRowLengthCache(r,s),l},e.prototype.$updateRowLengthCache=function(e,t){this.$rowLengthCache[e]=null,this.$rowLengthCache[t]=null},e.prototype.$updateWrapData=function(e,t){var i,n,o=this.doc.getAllLines(),r=this.getTabSize(),s=this.$wrapData,a=this.$wrapLimit,l=e;for(t=Math.min(t,o.length-1);l<=t;)(n=this.getFoldLine(l,n))?(i=[],n.walk((function(e,t,n,r){var s;if(null!=e){(s=this.$getDisplayTokens(e,i.length))[0]=w;for(var a=1;a<s.length;a++)s[a]=b}else s=this.$getDisplayTokens(o[t].substring(r,n),i.length);i=i.concat(s)}).bind(this),n.end.row,o[n.end.row].length+1),s[n.start.row]=this.$computeWrapSplits(i,a,r),l=n.end.row+1):(i=this.$getDisplayTokens(o[l]),s[l]=this.$computeWrapSplits(i,a,r),l++)},e.prototype.$computeWrapSplits=function(e,t,i){if(0==e.length)return[];var n=[],o=e.length,r=0,s=0,a=this.$wrapAsCode,l=this.$indentedSoftWrap,c=t<=Math.max(2*i,8)||!1===l?0:Math.floor(t/2);function h(t){for(var o=t-r,h=r;h<t;h++){var d=e[h];(12===d||2===d)&&(o-=1)}n.length||(n.indent=u=function(){var t=0;if(0===c)return t;if(l)for(var n=0;n<e.length;n++){var o=e[n];if(o==C)t+=1;else if(o==S)t+=i;else if(o==x)continue;else break}return a&&!1!==l&&(t+=i),Math.min(t,c)}()),s+=o,n.push(s),r=t}for(var u=0;o-r>t-u;){var d=r+t-u;if(e[d-1]>=C&&e[d]>=C){h(d);continue}if(e[d]==w||e[d]==b){for(;d!=r-1&&e[d]!=w;d--);if(d>r){h(d);continue}for(d=r+t;d<e.length&&e[d]==b;d++);if(d==e.length)break;h(d);continue}for(var p=Math.max(d-(t-(t>>2)),r-1);d>p&&e[d]<w;)d--;if(a){for(;d>p&&e[d]<w;)d--;for(;d>p&&e[d]==$;)d--}else for(;d>p&&e[d]<C;)d--;if(d>p){h(++d);continue}e[d=r+t]==v&&d--,h(d-u)}return n},e.prototype.$getDisplayTokens=function(e,t){var i,n=[];t=t||0;for(var o=0;o<e.length;o++){var r=e.charCodeAt(o);if(9==r){i=this.getScreenTabSize(n.length+t),n.push(S);for(var s=1;s<i;s++)n.push(x)}else 32==r?n.push(C):r>39&&r<48||r>57&&r<64?n.push($):r>=4352&&A(r)?n.push(y,v):n.push(y)}return n},e.prototype.$getStringScreenWidth=function(e,t,i){var n,o;if(0==t)return[0,0];for(null==t&&(t=1/0),i=i||0,o=0;o<e.length&&(9==(n=e.charCodeAt(o))?i+=this.getScreenTabSize(i):n>=4352&&A(n)?i+=2:i+=1,!(i>t));o++);return[i,o]},e.prototype.getRowLength=function(e){var t=1;return(this.lineWidgets&&(t+=this.lineWidgets[e]&&this.lineWidgets[e].rowCount||0),this.$useWrapMode&&this.$wrapData[e])?this.$wrapData[e].length+t:t},e.prototype.getRowLineCount=function(e){return this.$useWrapMode&&this.$wrapData[e]?this.$wrapData[e].length+1:1},e.prototype.getRowWrapIndent=function(e){if(!this.$useWrapMode)return 0;var t=this.screenToDocumentPosition(e,Number.MAX_VALUE),i=this.$wrapData[t.row];return i.length&&i[0]<t.column?i.indent:0},e.prototype.getScreenLastRowColumn=function(e){var t=this.screenToDocumentPosition(e,Number.MAX_VALUE);return this.documentToScreenColumn(t.row,t.column)},e.prototype.getDocumentLastRowColumn=function(e,t){var i=this.documentToScreenRow(e,t);return this.getScreenLastRowColumn(i)},e.prototype.getDocumentLastRowColumnPosition=function(e,t){var i=this.documentToScreenRow(e,t);return this.screenToDocumentPosition(i,Number.MAX_VALUE/10)},e.prototype.getRowSplitData=function(e){if(this.$useWrapMode)return this.$wrapData[e]},e.prototype.getScreenTabSize=function(e){return this.$tabSize-(e%this.$tabSize|0)},e.prototype.screenToDocumentRow=function(e,t){return this.screenToDocumentPosition(e,t).row},e.prototype.screenToDocumentColumn=function(e,t){return this.screenToDocumentPosition(e,t).column},e.prototype.screenToDocumentPosition=function(e,t,i){if(e<0)return{row:0,column:0};var n,o,r=0,s=0,a=0,l=0,c=this.$screenRowCache,h=this.$getRowCacheIndex(c,e),u=c.length;if(u&&h>=0)var a=c[h],r=this.$docRowCache[h],d=e>c[u-1];else var d=!u;for(var p=this.getLength()-1,g=this.getNextFoldLine(r),f=g?g.start.row:1/0;a<=e&&!(a+(l=this.getRowLength(r))>e)&&!(r>=p);)a+=l,++r>f&&(r=g.end.row+1,f=(g=this.getNextFoldLine(r,g))?g.start.row:1/0),d&&(this.$docRowCache.push(r),this.$screenRowCache.push(a));if(g&&g.start.row<=r)n=this.getFoldDisplayLine(g),r=g.start.row;else{if(a+l<=e||r>p)return{row:p,column:this.getLine(p).length};n=this.getLine(r),g=null}var m=0,y=Math.floor(e-a);if(this.$useWrapMode){var v=this.$wrapData[r];v&&(o=v[y],y>0&&v.length&&(m=v.indent,s=v[y-1]||v[v.length-1],n=n.substring(s)))}return(void 0!==i&&this.$bidiHandler.isBidiRow(a+y,r,y)&&(t=this.$bidiHandler.offsetToCol(i)),s+=this.$getStringScreenWidth(n,t-m)[1],this.$useWrapMode&&s>=o&&(s=o-1),g)?g.idxToPosition(s):{row:r,column:s}},e.prototype.documentToScreenPosition=function(e,t){if(void 0===t)var i=this.$clipPositionToDocument(e.row,e.column);else i=this.$clipPositionToDocument(e,t);e=i.row,t=i.column;var n=0,o=null,r=null;(r=this.getFoldAt(e,t,1))&&(e=r.start.row,t=r.start.column);var s,a=0,l=this.$docRowCache,c=this.$getRowCacheIndex(l,e),h=l.length;if(h&&c>=0)var a=l[c],n=this.$screenRowCache[c],u=e>l[h-1];else var u=!h;for(var d=this.getNextFoldLine(a),p=d?d.start.row:1/0;a<e;){if(a>=p){if((s=d.end.row+1)>e)break;p=(d=this.getNextFoldLine(s,d))?d.start.row:1/0}else s=a+1;n+=this.getRowLength(a),a=s,u&&(this.$docRowCache.push(a),this.$screenRowCache.push(n))}var g="";d&&a>=p?(g=this.getFoldDisplayLine(d,e,t),o=d.start.row):(g=this.getLine(e).substring(0,t),o=e);var f=0;if(this.$useWrapMode){var m=this.$wrapData[o];if(m){for(var y=0;g.length>=m[y];)n++,y++;g=g.substring(m[y-1]||0,g.length),f=y>0?m.indent:0}}return this.lineWidgets&&this.lineWidgets[a]&&this.lineWidgets[a].rowsAbove&&(n+=this.lineWidgets[a].rowsAbove),{row:n,column:f+this.$getStringScreenWidth(g)[0]}},e.prototype.documentToScreenColumn=function(e,t){return this.documentToScreenPosition(e,t).column},e.prototype.documentToScreenRow=function(e,t){return this.documentToScreenPosition(e,t).row},e.prototype.getScreenLength=function(){var e=0,t=null;if(this.$useWrapMode)for(var i=this.$wrapData.length,n=0,o=0,t=this.$foldData[o++],r=t?t.start.row:1/0;n<i;){var s=this.$wrapData[n];e+=s?s.length+1:1,++n>r&&(n=t.end.row+1,r=(t=this.$foldData[o++])?t.start.row:1/0)}else{e=this.getLength();for(var a=this.$foldData,o=0;o<a.length;o++)e-=(t=a[o]).end.row-t.start.row}return this.lineWidgets&&(e+=this.$getWidgetScreenLength()),e},e.prototype.$setFontMetrics=function(e){this.$enableVarChar&&(this.$getStringScreenWidth=function(t,i,n){var o,r;if(0===i)return[0,0];for(i||(i=1/0),n=n||0,r=0;r<t.length&&("	"===(o=t.charAt(r))?n+=this.getScreenTabSize(n):n+=e.getCharacterWidth(o),!(n>i));r++);return[n,r]})},e.prototype.getPrecedingCharacter=function(){var e=this.selection.getCursor();return 0===e.column?0===e.row?"":this.doc.getNewLineCharacter():this.getLine(e.row)[e.column-1]},e.prototype.destroy=function(){this.destroyed||(this.bgTokenizer.setDocument(null),this.bgTokenizer.cleanup(),this.destroyed=!0),this.endOperation(),this.$stopWorker(),this.removeAllListeners(),this.doc&&this.doc.off("change",this.$onChange),this.selection&&(this.selection.off("changeCursor",this.$onSelectionChange),this.selection.off("changeSelection",this.$onSelectionChange)),this.selection.detach()},e}();m.$uid=0,m.prototype.$modes=s.$modes,m.prototype.getValue=m.prototype.toString,m.prototype.$defaultUndoManager={undo:function(){},redo:function(){},hasUndo:function(){},hasRedo:function(){},reset:function(){},add:function(){},addSelection:function(){},startNewGroup:function(){},addSession:function(){}},m.prototype.$overwrite=!1,m.prototype.$mode=null,m.prototype.$modeId=null,m.prototype.$scrollTop=0,m.prototype.$scrollLeft=0,m.prototype.$wrapLimit=80,m.prototype.$useWrapMode=!1,m.prototype.$wrapLimitRange={min:null,max:null},m.prototype.lineWidgets=null,m.prototype.isFullWidth=A,n.implement(m.prototype,a);var y=1,v=2,w=3,b=4,$=9,C=10,S=11,x=12;function A(e){return!(e<4352)&&(e>=4352&&e<=4447||e>=4515&&e<=4519||e>=4602&&e<=4607||e>=9001&&e<=9002||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12283||e>=12288&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12589||e>=12593&&e<=12686||e>=12688&&e<=12730||e>=12736&&e<=12771||e>=12784&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=13054||e>=13056&&e<=19903||e>=19968&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=55216&&e<=55238||e>=55243&&e<=55291||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=65281&&e<=65376||e>=65504&&e<=65510)}e("./edit_session/folding").Folding.call(m.prototype),e("./edit_session/bracket_match").BracketMatch.call(m.prototype),s.defineOptions(m.prototype,"session",{wrap:{set:function(e){if(e&&"off"!=e?"free"==e?e=!0:"printMargin"==e?e=-1:"string"==typeof e&&(e=parseInt(e,10)||!1):e=!1,this.$wrap!=e)if(this.$wrap=e,e){var t="number"==typeof e?e:null;this.setWrapLimitRange(t,t),this.setUseWrapMode(!0)}else this.setUseWrapMode(!1)},get:function(){return this.getUseWrapMode()?-1==this.$wrap?"printMargin":this.getWrapLimitRange().min?this.$wrap:"free":"off"},handlesSet:!0},wrapMethod:{set:function(e){(e="auto"==e?"text"!=this.$mode.type:"text"!=e)!=this.$wrapAsCode&&(this.$wrapAsCode=e,this.$useWrapMode&&(this.$useWrapMode=!1,this.setUseWrapMode(!0)))},initialValue:"auto"},indentedSoftWrap:{set:function(){this.$useWrapMode&&(this.$useWrapMode=!1,this.setUseWrapMode(!0))},initialValue:!0},firstLineNumber:{set:function(){this._signal("changeBreakpoint")},initialValue:1},useWorker:{set:function(e){this.$useWorker=e,this.$stopWorker(),e&&this.$startWorker()},initialValue:!0},useSoftTabs:{initialValue:!0},tabSize:{set:function(e){(e=parseInt(e))>0&&this.$tabSize!==e&&(this.$modified=!0,this.$rowLengthCache=[],this.$tabSize=e,this._signal("changeTabSize"))},initialValue:4,handlesSet:!0},navigateWithinSoftTabs:{initialValue:!1},foldStyle:{set:function(e){this.setFoldStyle(e)},handlesSet:!0},overwrite:{set:function(e){this._signal("changeOverwrite")},initialValue:!1},newLineMode:{set:function(e){this.doc.setNewLineMode(e)},get:function(){return this.doc.getNewLineMode()},handlesSet:!0},mode:{set:function(e){this.setMode(e)},get:function(){return this.$modeId},handlesSet:!0}}),t.EditSession=m}),ace.define("ace/search",["require","exports","module","ace/lib/lang","ace/lib/oop","ace/range"],function(e,t,i){"use strict";var n=e("./lib/lang"),o=e("./lib/oop"),r=e("./range").Range;function s(e,t){var i=e.doc.positionToIndex({row:t,column:0});return e.doc.indexToPosition(i+5e3).row+1}t.Search=function(){function e(){this.$options={}}return e.prototype.set=function(e){return o.mixin(this.$options,e),this},e.prototype.getOptions=function(){return n.copyObject(this.$options)},e.prototype.setOptions=function(e){this.$options=e},e.prototype.find=function(e){var t=this.$options,i=this.$matchIterator(e,t);if(!i)return!1;var n=null;return i.forEach(function(e,i,o,s){return n=new r(e,i,o,s),!(i==s&&t.start&&t.start.start&&!1!=t.skipCurrent&&n.isEqual(t.start))||(n=null,!1)}),n},e.prototype.findAll=function(e){var t=this.$options;if(!t.needle)return[];this.$assembleRegExp(t);var i=t.range,o=i?e.getLines(i.start.row,i.end.row):e.doc.getAllLines(),s=[],a=t.re;if(t.$isMultiLine){var l,c=a.length,h=o.length-c;e:for(var u=a.offset||0;u<=h;u++){for(var d=0;d<c;d++)if(-1==o[u+d].search(a[d]))continue e;var p=o[u],g=o[u+c-1],f=p.length-p.match(a[0])[0].length,m=g.match(a[c-1])[0].length;l&&l.end.row===u&&l.end.column>f||(s.push(l=new r(u,f,u+c-1,m)),c>2&&(u=u+c-2))}}else for(var y,v=0;v<o.length;v++)if(this.$isMultilineSearch(t)){var w=o.length-1;if(y=this.$multiLineForward(e,a,v,w)){var b=y.endRow<=w?y.endRow-1:w;b>v&&(v=b),s.push(new r(y.startRow,y.startCol,y.endRow,y.endCol))}}else{y=n.getMatchOffsets(o[v],a);for(var d=0;d<y.length;d++){var $=y[d];s.push(new r(v,$.offset,v,$.offset+$.length))}}if(i){for(var C=i.start.column,S=i.end.column,v=0,d=s.length-1;v<d&&s[v].start.column<C&&0==s[v].start.row;)v++;for(var x=i.end.row-i.start.row;v<d&&s[d].end.column>S&&s[d].end.row==x;)d--;for(s=s.slice(v,d+1),v=0,d=s.length;v<d;v++)s[v].start.row+=i.start.row,s[v].end.row+=i.start.row}return s},e.prototype.parseReplaceString=function(e){for(var t={DollarSign:36,Ampersand:38,Digit0:48,Digit1:49,Digit9:57,Backslash:92,n:110,t:116},i="",n=0,o=e.length;n<o;n++){var r=e.charCodeAt(n);if(r===t.Backslash){if(++n>=o){i+="\\";break}switch(e.charCodeAt(n)){case t.Backslash:i+="\\";break;case t.n:i+="\n";break;case t.t:i+="	"}continue}if(r===t.DollarSign){if(++n>=o){i+="$";break}var s=e.charCodeAt(n);if(s===t.DollarSign){i+="$$";continue}if(s===t.Digit0||s===t.Ampersand){i+="$&";continue}if(t.Digit1<=s&&s<=t.Digit9){i+="$"+e[n];continue}}i+=e[n]}return i||e},e.prototype.replace=function(e,t){var i=this.$options,n=this.$assembleRegExp(i);if(i.$isMultiLine)return t;if(n){var o=this.$isMultilineSearch(i);o&&(e=e.replace(/\r\n|\r|\n/g,"\n"));var r=n.exec(e);if(!r||!o&&r[0].length!=e.length)return null;if(t=i.regExp?this.parseReplaceString(t):t.replace(/\$/g,"$$$$"),t=e.replace(n,t),i.preserveCase){t=t.split("");for(var s=Math.min(e.length,e.length);s--;){var a=e[s];a&&a.toLowerCase()!=a?t[s]=t[s].toUpperCase():t[s]=t[s].toLowerCase()}t=t.join("")}return t}},e.prototype.$assembleRegExp=function(e,t){if(e.needle instanceof RegExp)return e.re=e.needle;var i=e.needle;if(!e.needle)return e.re=!1;e.regExp||(i=n.escapeRegExp(i));var o=e.caseSensitive?"gm":"gmi";try{RegExp(i,"u"),e.$supportsUnicodeFlag=!0,o+="u"}catch(t){e.$supportsUnicodeFlag=!1}if(e.wholeWord&&(i=function(e,t){var i=n.supportsLookbehind();function o(e,n){if(void 0===n&&(n=!0),(i&&t.$supportsUnicodeFlag?RegExp("[\\p{L}\\p{N}_]","u"):RegExp("\\w")).test(e)||t.regExp)return i&&t.$supportsUnicodeFlag?n?"(?<=^|[^\\p{L}\\p{N}_])":"(?=[^\\p{L}\\p{N}_]|$)":"\\b";return""}var r=Array.from(e),s=r[0],a=r[r.length-1];return o(s)+e+o(a,!1)}(i,e)),e.$isMultiLine=!t&&/[\n\r]/.test(i),e.$isMultiLine)return e.re=this.$assembleMultilineRegExp(i,o);try{var r=new RegExp(i,o)}catch(e){r=!1}return e.re=r},e.prototype.$assembleMultilineRegExp=function(e,t){for(var i=e.replace(/\r\n|\r|\n/g,"$\n^").split("\n"),n=[],o=0;o<i.length;o++)try{n.push(new RegExp(i[o],t))}catch(e){return!1}return n},e.prototype.$isMultilineSearch=function(e){return e.re&&/\\r\\n|\\r|\\n/.test(e.re.source)&&e.regExp&&!e.$isMultiLine},e.prototype.$multiLineForward=function(e,t,i,n){for(var o,r=s(e,i),a=i;a<=n;){for(var l=0;l<r&&!(a>n);l++){var c=e.getLine(a++);o=null==o?c:o+"\n"+c}var h=t.exec(o);if(t.lastIndex=0,h){var u=o.slice(0,h.index).split("\n"),d=h[0].split("\n"),p=i+u.length-1,g=u[u.length-1].length,f=p+d.length-1,m=1==d.length?g+d[0].length:d[d.length-1].length;return{startRow:p,startCol:g,endRow:f,endCol:m}}}return null},e.prototype.$multiLineBackward=function(e,t,i,n,o){for(var r,a=s(e,n),l=e.getLine(n).length-i,c=n;c>=o;){for(var h=0;h<a&&c>=o;h++){var u=e.getLine(c--);r=null==r?u:u+"\n"+r}var d=function(e,t,i){for(var n=null,o=0;o<=e.length;){t.lastIndex=o;var r=t.exec(e);if(!r)break;var s=r.index+r[0].length;if(s>e.length-i)break;(!n||s>n.index+n[0].length)&&(n=r),o=r.index+1}return n}(r,t,l);if(d){var p=r.slice(0,d.index).split("\n"),g=d[0].split("\n"),f=c+p.length,m=p[p.length-1].length,y=f+g.length-1,v=1==g.length?m+g[0].length:g[g.length-1].length;return{startRow:f,startCol:m,endRow:y,endCol:v}}}return null},e.prototype.$matchIterator=function(e,t){var i=this.$assembleRegExp(t);if(!i)return!1;var o=this.$isMultilineSearch(t),r=this.$multiLineForward,s=this.$multiLineBackward,a=!0==t.backwards,l=!1!=t.skipCurrent,c=i.unicode,h=t.range,u=t.start;u||(u=h?h[a?"end":"start"]:e.selection.getRange()),u.start&&(u=u[l!=a?"end":"start"]);var d=h?h.start.row:0,p=h?h.end.row:e.getLength()-1;if(a)var g=function(e){var i=u.row;if(!m(i,u.column,e)){for(i--;i>=d;i--)if(m(i,Number.MAX_VALUE,e))return;if(!1!=t.wrap){for(i=p,d=u.row;i>=d;i--)if(m(i,Number.MAX_VALUE,e))return}}};else var g=function(e){var i=u.row;if(!m(i,u.column,e)){for(i+=1;i<=p;i++)if(m(i,0,e))return;if(!1!=t.wrap){for(i=d,p=u.row;i<=p;i++)if(m(i,0,e))return}}};if(t.$isMultiLine)var f=i.length,m=function(t,n,o){var r=a?t-f+1:t;if(!(r<0||r+f>e.getLength())){var s=e.getLine(r),l=s.search(i[0]);if((a||!(l<n))&&-1!==l){for(var c=1;c<f;c++)if(-1==(s=e.getLine(r+c)).search(i[c]))return;var h=s.match(i[f-1])[0].length;if((!a||!(h>n))&&o(r,l,r+f-1,h))return!0}}};else if(a)var m=function(t,r,a){if(o){var l=s(e,i,r,t,d);if(!l)return!1;if(a(l.startRow,l.startCol,l.endRow,l.endCol))return!0}else{var h,u=e.getLine(t),p=[],g=0;for(i.lastIndex=0;h=i.exec(u);){var f=h[0].length;if(g=h.index,!f){if(g>=u.length)break;i.lastIndex=g+=n.skipEmptyMatch(u,g,c)}if(h.index+f>r)break;p.push(h.index,f)}for(var m=p.length-1;m>=0;m-=2){var y=p[m-1],f=p[m];if(a(t,y,t,y+f))return!0}}};else var m=function(t,s,a){if(i.lastIndex=s,o){var l=r(e,i,t,p);if(l){var h=l.endRow<=p?l.endRow-1:p;h>t&&(t=h)}if(!l)return!1;if(a(l.startRow,l.startCol,l.endRow,l.endCol))return!0}else for(var u,d,g=e.getLine(t);d=i.exec(g);){var f=d[0].length;if(a(t,u=d.index,t,u+f))return!0;if(!f&&(i.lastIndex=u+=n.skipEmptyMatch(g,u,c),u>=g.length))return!1}};return{forEach:g}},e}()}),ace.define("ace/keyboard/hash_handler",["require","exports","module","ace/lib/keys","ace/lib/useragent"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=e("../lib/keys"),s=e("../lib/useragent"),a=r.KEY_MODS,l=function(){function e(e,t){this.$init(e,t,!1)}return e.prototype.$init=function(e,t,i){this.platform=t||(s.isMac?"mac":"win"),this.commands={},this.commandKeyBinding={},this.addCommands(e),this.$singleCommand=i},e.prototype.addCommand=function(e){this.commands[e.name]&&this.removeCommand(e),this.commands[e.name]=e,e.bindKey&&this._buildKeyHash(e)},e.prototype.removeCommand=function(e,t){var i=e&&("string"==typeof e?e:e.name);e=this.commands[i],t||delete this.commands[i];var n=this.commandKeyBinding;for(var o in n){var r=n[o];if(r==e)delete n[o];else if(Array.isArray(r)){var s=r.indexOf(e);-1!=s&&(r.splice(s,1),1==r.length&&(n[o]=r[0]))}}},e.prototype.bindKey=function(e,t,i){if("object"==typeof e&&e&&(void 0==i&&(i=e.position),e=e[this.platform]),e){if("function"==typeof t)return this.addCommand({exec:t,bindKey:e,name:t.name||e});e.split("|").forEach(function(e){var n="";if(-1!=e.indexOf(" ")){var o=e.split(/\s+/);e=o.pop(),o.forEach(function(e){var t=this.parseKeys(e),i=a[t.hashId]+t.key;n+=(n?" ":"")+i,this._addCommandToBinding(n,"chainKeys")},this),n+=" "}var r=this.parseKeys(e),s=a[r.hashId]+r.key;this._addCommandToBinding(n+s,t,i)},this)}},e.prototype._addCommandToBinding=function(e,t,i){var n,o=this.commandKeyBinding;if(t)if(!o[e]||this.$singleCommand)o[e]=t;else{Array.isArray(o[e])?-1!=(n=o[e].indexOf(t))&&o[e].splice(n,1):o[e]=[o[e]],"number"!=typeof i&&(i=c(t));var r=o[e];for(n=0;n<r.length&&!(c(r[n])>i);n++);r.splice(n,0,t)}else delete o[e]},e.prototype.addCommands=function(e){e&&Object.keys(e).forEach(function(t){var i=e[t];if(i){if("string"==typeof i)return this.bindKey(i,t);"function"==typeof i&&(i={exec:i}),"object"==typeof i&&(i.name||(i.name=t),this.addCommand(i))}},this)},e.prototype.removeCommands=function(e){Object.keys(e).forEach(function(t){this.removeCommand(e[t])},this)},e.prototype.bindKeys=function(e){Object.keys(e).forEach(function(t){this.bindKey(t,e[t])},this)},e.prototype._buildKeyHash=function(e){this.bindKey(e.bindKey,e)},e.prototype.parseKeys=function(e){var t=e.toLowerCase().split(/[\-\+]([\-\+])?/).filter(function(e){return e}),i=t.pop(),n=r[i];if(r.FUNCTION_KEYS[n])i=r.FUNCTION_KEYS[n].toLowerCase();else if(!t.length)return{key:i,hashId:-1};else if(1==t.length&&"shift"==t[0])return{key:i.toUpperCase(),hashId:-1};for(var o=0,s=t.length;s--;){var a=r.KEY_MODS[t[s]];if(null==a)return"undefined"!=typeof console&&console.error("invalid modifier "+t[s]+" in "+e),!1;o|=a}return{key:i,hashId:o}},e.prototype.findKeyCommand=function(e,t){var i=a[e]+t;return this.commandKeyBinding[i]},e.prototype.handleKeyboard=function(e,t,i,n){if(!(n<0)){var o=a[t]+i,r=this.commandKeyBinding[o];return(e.$keyChain&&(e.$keyChain+=" "+o,r=this.commandKeyBinding[e.$keyChain]||r),r&&("chainKeys"==r||"chainKeys"==r[r.length-1]))?(e.$keyChain=e.$keyChain||o,{command:"null"}):(e.$keyChain&&(t&&4!=t||1!=i.length?(-1==t||n>0)&&(e.$keyChain=""):e.$keyChain=e.$keyChain.slice(0,-o.length-1)),{command:r})}},e.prototype.getStatusText=function(e,t){return t.$keyChain||""},e}();function c(e){return"object"==typeof e&&e.bindKey&&e.bindKey.position||(e.isDefault?-100:0)}var h=function(e){function t(t,i){var n=e.call(this,t,i)||this;return n.$singleCommand=!0,n}return o(t,e),t}(l);h.call=function(e,t,i){l.prototype.$init.call(e,t,i,!0)},l.call=function(e,t,i){l.prototype.$init.call(e,t,i,!1)},t.HashHandler=h,t.MultiHashHandler=l}),ace.define("ace/commands/command_manager",["require","exports","module","ace/lib/oop","ace/keyboard/hash_handler","ace/lib/event_emitter"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=e("../lib/oop"),s=e("../keyboard/hash_handler").MultiHashHandler,a=e("../lib/event_emitter").EventEmitter,l=function(e){function t(t,i){var n=e.call(this,i,t)||this;return n.byName=n.commands,n.setDefaultHandler("exec",function(e){return e.args?e.command.exec(e.editor,e.args,e.event,!1):e.command.exec(e.editor,{},e.event,!0)}),n}return o(t,e),t.prototype.exec=function(e,t,i){if(Array.isArray(e)){for(var n=e.length;n--;)if(this.exec(e[n],t,i))return!0;return!1}"string"==typeof e&&(e=this.commands[e]);var o={editor:t,command:e,args:i};return this.canExecute(e,t)?(o.returnValue=this._emit("exec",o),this._signal("afterExec",o),!1!==o.returnValue):(this._signal("commandUnavailable",o),!1)},t.prototype.canExecute=function(e,t){return"string"==typeof e&&(e=this.commands[e]),!!e&&(!t||!t.$readOnly||!!e.readOnly)&&(!1==this.$checkCommandState||!e.isAvailable||!!e.isAvailable(t))},t.prototype.toggleRecording=function(e){if(!this.$inReplay)return(e&&e._emit("changeStatus"),this.recording)?(this.macro.pop(),this.off("exec",this.$addCommandToMacro),this.macro.length||(this.macro=this.oldMacro),this.recording=!1):(this.$addCommandToMacro||(this.$addCommandToMacro=(function(e){this.macro.push([e.command,e.args])}).bind(this)),this.oldMacro=this.macro,this.macro=[],this.on("exec",this.$addCommandToMacro),this.recording=!0)},t.prototype.replay=function(e){if(!this.$inReplay&&this.macro){if(this.recording)return this.toggleRecording(e);try{this.$inReplay=!0,this.macro.forEach(function(t){"string"==typeof t?this.exec(t,e):this.exec(t[0],e,t[1])},this)}finally{this.$inReplay=!1}}},t.prototype.trimMacro=function(e){return e.map(function(e){return"string"!=typeof e[0]&&(e[0]=e[0].name),e[1]||(e=e[0]),e})},t}(s);r.implement(l.prototype,a),t.CommandManager=l}),ace.define("ace/commands/default_commands",["require","exports","module","ace/lib/lang","ace/config","ace/range"],function(e,t,i){"use strict";var n=e("../lib/lang"),o=e("../config"),r=e("../range").Range;function s(e,t){return{win:e,mac:t}}t.commands=[{name:"showSettingsMenu",description:"Show settings menu",bindKey:s("Ctrl-,","Command-,"),exec:function(e){o.loadModule("ace/ext/settings_menu",function(t){t.init(e),e.showSettingsMenu()})},readOnly:!0},{name:"goToNextError",description:"Go to next error",bindKey:s("Alt-E","F4"),exec:function(e){o.loadModule("ace/ext/error_marker",function(t){t.showErrorMarker(e,1)})},scrollIntoView:"animate",readOnly:!0},{name:"goToPreviousError",description:"Go to previous error",bindKey:s("Alt-Shift-E","Shift-F4"),exec:function(e){o.loadModule("ace/ext/error_marker",function(t){t.showErrorMarker(e,-1)})},scrollIntoView:"animate",readOnly:!0},{name:"selectall",description:"Select all",bindKey:s("Ctrl-A","Command-A"),exec:function(e){e.selectAll()},readOnly:!0},{name:"centerselection",description:"Center selection",bindKey:s(null,"Ctrl-L"),exec:function(e){e.centerSelection()},readOnly:!0},{name:"gotoline",description:"Go to line...",bindKey:s("Ctrl-L","Command-L"),exec:function(e,t){"number"!=typeof t||isNaN(t)||e.gotoLine(t),e.prompt({$type:"gotoLine"})},readOnly:!0},{name:"fold",bindKey:s("Alt-L|Ctrl-F1","Command-Alt-L|Command-F1"),exec:function(e){e.session.toggleFold(!1)},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"unfold",bindKey:s("Alt-Shift-L|Ctrl-Shift-F1","Command-Alt-Shift-L|Command-Shift-F1"),exec:function(e){e.session.toggleFold(!0)},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"toggleFoldWidget",description:"Toggle fold widget",bindKey:s("F2","F2"),exec:function(e){e.session.toggleFoldWidget()},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"toggleParentFoldWidget",description:"Toggle parent fold widget",bindKey:s("Alt-F2","Alt-F2"),exec:function(e){e.session.toggleFoldWidget(!0)},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"foldall",description:"Fold all",bindKey:s(null,"Ctrl-Command-Option-0"),exec:function(e){e.session.foldAll()},scrollIntoView:"center",readOnly:!0},{name:"foldAllComments",description:"Fold all comments",bindKey:s(null,"Ctrl-Command-Option-0"),exec:function(e){e.session.foldAllComments()},scrollIntoView:"center",readOnly:!0},{name:"foldOther",description:"Fold other",bindKey:s("Alt-0","Command-Option-0"),exec:function(e){e.session.foldAll(),e.session.unfold(e.selection.getAllRanges())},scrollIntoView:"center",readOnly:!0},{name:"unfoldall",description:"Unfold all",bindKey:s("Alt-Shift-0","Command-Option-Shift-0"),exec:function(e){e.session.unfold()},scrollIntoView:"center",readOnly:!0},{name:"findnext",description:"Find next",bindKey:s("Ctrl-K","Command-G"),exec:function(e){e.findNext()},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"findprevious",description:"Find previous",bindKey:s("Ctrl-Shift-K","Command-Shift-G"),exec:function(e){e.findPrevious()},multiSelectAction:"forEach",scrollIntoView:"center",readOnly:!0},{name:"selectOrFindNext",description:"Select or find next",bindKey:s("Alt-K","Ctrl-G"),exec:function(e){e.selection.isEmpty()?e.selection.selectWord():e.findNext()},readOnly:!0},{name:"selectOrFindPrevious",description:"Select or find previous",bindKey:s("Alt-Shift-K","Ctrl-Shift-G"),exec:function(e){e.selection.isEmpty()?e.selection.selectWord():e.findPrevious()},readOnly:!0},{name:"find",description:"Find",bindKey:s("Ctrl-F","Command-F"),exec:function(e){o.loadModule("ace/ext/searchbox",function(t){t.Search(e)})},readOnly:!0},{name:"overwrite",description:"Overwrite",bindKey:"Insert",exec:function(e){e.toggleOverwrite()},readOnly:!0},{name:"selecttostart",description:"Select to start",bindKey:s("Ctrl-Shift-Home","Command-Shift-Home|Command-Shift-Up"),exec:function(e){e.getSelection().selectFileStart()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"gotostart",description:"Go to start",bindKey:s("Ctrl-Home","Command-Home|Command-Up"),exec:function(e){e.navigateFileStart()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"selectup",description:"Select up",bindKey:s("Shift-Up","Shift-Up|Ctrl-Shift-P"),exec:function(e){e.getSelection().selectUp()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"golineup",description:"Go line up",bindKey:s("Up","Up|Ctrl-P"),exec:function(e,t){e.navigateUp(t.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selecttoend",description:"Select to end",bindKey:s("Ctrl-Shift-End","Command-Shift-End|Command-Shift-Down"),exec:function(e){e.getSelection().selectFileEnd()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"gotoend",description:"Go to end",bindKey:s("Ctrl-End","Command-End|Command-Down"),exec:function(e){e.navigateFileEnd()},multiSelectAction:"forEach",readOnly:!0,scrollIntoView:"animate",aceCommandGroup:"fileJump"},{name:"selectdown",description:"Select down",bindKey:s("Shift-Down","Shift-Down|Ctrl-Shift-N"),exec:function(e){e.getSelection().selectDown()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"golinedown",description:"Go line down",bindKey:s("Down","Down|Ctrl-N"),exec:function(e,t){e.navigateDown(t.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectwordleft",description:"Select word left",bindKey:s("Ctrl-Shift-Left","Option-Shift-Left"),exec:function(e){e.getSelection().selectWordLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotowordleft",description:"Go to word left",bindKey:s("Ctrl-Left","Option-Left"),exec:function(e){e.navigateWordLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selecttolinestart",description:"Select to line start",bindKey:s("Alt-Shift-Left","Command-Shift-Left|Ctrl-Shift-A"),exec:function(e){e.getSelection().selectLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotolinestart",description:"Go to line start",bindKey:s("Alt-Left|Home","Command-Left|Home|Ctrl-A"),exec:function(e){e.navigateLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectleft",description:"Select left",bindKey:s("Shift-Left","Shift-Left|Ctrl-Shift-B"),exec:function(e){e.getSelection().selectLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotoleft",description:"Go to left",bindKey:s("Left","Left|Ctrl-B"),exec:function(e,t){e.navigateLeft(t.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectwordright",description:"Select word right",bindKey:s("Ctrl-Shift-Right","Option-Shift-Right"),exec:function(e){e.getSelection().selectWordRight()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotowordright",description:"Go to word right",bindKey:s("Ctrl-Right","Option-Right"),exec:function(e){e.navigateWordRight()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selecttolineend",description:"Select to line end",bindKey:s("Alt-Shift-Right","Command-Shift-Right|Shift-End|Ctrl-Shift-E"),exec:function(e){e.getSelection().selectLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotolineend",description:"Go to line end",bindKey:s("Alt-Right|End","Command-Right|End|Ctrl-E"),exec:function(e){e.navigateLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectright",description:"Select right",bindKey:s("Shift-Right","Shift-Right"),exec:function(e){e.getSelection().selectRight()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"gotoright",description:"Go to right",bindKey:s("Right","Right|Ctrl-F"),exec:function(e,t){e.navigateRight(t.times)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectpagedown",description:"Select page down",bindKey:"Shift-PageDown",exec:function(e){e.selectPageDown()},readOnly:!0},{name:"pagedown",description:"Page down",bindKey:s(null,"Option-PageDown"),exec:function(e){e.scrollPageDown()},readOnly:!0},{name:"gotopagedown",description:"Go to page down",bindKey:s("PageDown","PageDown|Ctrl-V"),exec:function(e){e.gotoPageDown()},readOnly:!0},{name:"selectpageup",description:"Select page up",bindKey:"Shift-PageUp",exec:function(e){e.selectPageUp()},readOnly:!0},{name:"pageup",description:"Page up",bindKey:s(null,"Option-PageUp"),exec:function(e){e.scrollPageUp()},readOnly:!0},{name:"gotopageup",description:"Go to page up",bindKey:"PageUp",exec:function(e){e.gotoPageUp()},readOnly:!0},{name:"scrollup",description:"Scroll up",bindKey:s("Ctrl-Up",null),exec:function(e){e.renderer.scrollBy(0,-2*e.renderer.layerConfig.lineHeight)},readOnly:!0},{name:"scrolldown",description:"Scroll down",bindKey:s("Ctrl-Down",null),exec:function(e){e.renderer.scrollBy(0,2*e.renderer.layerConfig.lineHeight)},readOnly:!0},{name:"selectlinestart",description:"Select line start",bindKey:"Shift-Home",exec:function(e){e.getSelection().selectLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"selectlineend",description:"Select line end",bindKey:"Shift-End",exec:function(e){e.getSelection().selectLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"togglerecording",description:"Toggle recording",bindKey:s("Ctrl-Alt-E","Command-Option-E"),exec:function(e){e.commands.toggleRecording(e)},readOnly:!0},{name:"replaymacro",description:"Replay macro",bindKey:s("Ctrl-Shift-E","Command-Shift-E"),exec:function(e){e.commands.replay(e)},readOnly:!0},{name:"jumptomatching",description:"Jump to matching",bindKey:s("Ctrl-\\|Ctrl-P","Command-\\"),exec:function(e){e.jumpToMatching()},multiSelectAction:"forEach",scrollIntoView:"animate",readOnly:!0},{name:"selecttomatching",description:"Select to matching",bindKey:s("Ctrl-Shift-\\|Ctrl-Shift-P","Command-Shift-\\"),exec:function(e){e.jumpToMatching(!0)},multiSelectAction:"forEach",scrollIntoView:"animate",readOnly:!0},{name:"expandToMatching",description:"Expand to matching",bindKey:s("Ctrl-Shift-M","Ctrl-Shift-M"),exec:function(e){e.jumpToMatching(!0,!0)},multiSelectAction:"forEach",scrollIntoView:"animate",readOnly:!0},{name:"passKeysToBrowser",description:"Pass keys to browser",bindKey:s(null,null),exec:function(){},passEvent:!0,readOnly:!0},{name:"copy",description:"Copy",exec:function(e){},readOnly:!0},{name:"cut",description:"Cut",exec:function(e){var t=e.$copyWithEmptySelection&&e.selection.isEmpty()?e.selection.getLineRange():e.selection.getRange();e._emit("cut",t),t.isEmpty()||e.session.remove(t),e.clearSelection()},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"paste",description:"Paste",exec:function(e,t){e.$handlePaste(t)},scrollIntoView:"cursor"},{name:"removeline",description:"Remove line",bindKey:s("Ctrl-D","Command-D"),exec:function(e){e.removeLines()},scrollIntoView:"cursor",multiSelectAction:"forEachLine"},{name:"duplicateSelection",description:"Duplicate selection",bindKey:s("Ctrl-Shift-D","Command-Shift-D"),exec:function(e){e.duplicateSelection()},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"sortlines",description:"Sort lines",bindKey:s("Ctrl-Alt-S","Command-Alt-S"),exec:function(e){e.sortLines()},scrollIntoView:"selection",multiSelectAction:"forEachLine"},{name:"togglecomment",description:"Toggle comment",bindKey:s("Ctrl-/","Command-/"),exec:function(e){e.toggleCommentLines()},multiSelectAction:"forEachLine",scrollIntoView:"selectionPart"},{name:"toggleBlockComment",description:"Toggle block comment",bindKey:s("Ctrl-Shift-/","Command-Shift-/"),exec:function(e){e.toggleBlockComment()},multiSelectAction:"forEach",scrollIntoView:"selectionPart"},{name:"modifyNumberUp",description:"Modify number up",bindKey:s("Ctrl-Shift-Up","Alt-Shift-Up"),exec:function(e){e.modifyNumber(1)},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"modifyNumberDown",description:"Modify number down",bindKey:s("Ctrl-Shift-Down","Alt-Shift-Down"),exec:function(e){e.modifyNumber(-1)},scrollIntoView:"cursor",multiSelectAction:"forEach"},{name:"replace",description:"Replace",bindKey:s("Ctrl-H","Command-Option-F"),exec:function(e){o.loadModule("ace/ext/searchbox",function(t){t.Search(e,!0)})}},{name:"undo",description:"Undo",bindKey:s("Ctrl-Z","Command-Z"),exec:function(e){e.undo()}},{name:"redo",description:"Redo",bindKey:s("Ctrl-Shift-Z|Ctrl-Y","Command-Shift-Z|Command-Y"),exec:function(e){e.redo()}},{name:"copylinesup",description:"Copy lines up",bindKey:s("Alt-Shift-Up","Command-Option-Up"),exec:function(e){e.copyLinesUp()},scrollIntoView:"cursor"},{name:"movelinesup",description:"Move lines up",bindKey:s("Alt-Up","Option-Up"),exec:function(e){e.moveLinesUp()},scrollIntoView:"cursor"},{name:"copylinesdown",description:"Copy lines down",bindKey:s("Alt-Shift-Down","Command-Option-Down"),exec:function(e){e.copyLinesDown()},scrollIntoView:"cursor"},{name:"movelinesdown",description:"Move lines down",bindKey:s("Alt-Down","Option-Down"),exec:function(e){e.moveLinesDown()},scrollIntoView:"cursor"},{name:"del",description:"Delete",bindKey:s("Delete","Delete|Ctrl-D|Shift-Delete"),exec:function(e){e.remove("right")},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"backspace",description:"Backspace",bindKey:s("Shift-Backspace|Backspace","Ctrl-Backspace|Shift-Backspace|Backspace|Ctrl-H"),exec:function(e){e.remove("left")},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"cut_or_delete",description:"Cut or delete",bindKey:s("Shift-Delete",null),exec:function(e){if(!e.selection.isEmpty())return!1;e.remove("left")},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolinestart",description:"Remove to line start",bindKey:s("Alt-Backspace","Command-Backspace"),exec:function(e){e.removeToLineStart()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolineend",description:"Remove to line end",bindKey:s("Alt-Delete","Ctrl-K|Command-Delete"),exec:function(e){e.removeToLineEnd()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolinestarthard",description:"Remove to line start hard",bindKey:s("Ctrl-Shift-Backspace",null),exec:function(e){var t=e.selection.getRange();t.start.column=0,e.session.remove(t)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removetolineendhard",description:"Remove to line end hard",bindKey:s("Ctrl-Shift-Delete",null),exec:function(e){var t=e.selection.getRange();t.end.column=Number.MAX_VALUE,e.session.remove(t)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removewordleft",description:"Remove word left",bindKey:s("Ctrl-Backspace","Alt-Backspace|Ctrl-Alt-Backspace"),exec:function(e){e.removeWordLeft()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"removewordright",description:"Remove word right",bindKey:s("Ctrl-Delete","Alt-Delete"),exec:function(e){e.removeWordRight()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"outdent",description:"Outdent",bindKey:s("Shift-Tab","Shift-Tab"),exec:function(e){e.blockOutdent()},multiSelectAction:"forEach",scrollIntoView:"selectionPart"},{name:"indent",description:"Indent",bindKey:s("Tab","Tab"),exec:function(e){e.indent()},multiSelectAction:"forEach",scrollIntoView:"selectionPart"},{name:"blockoutdent",description:"Block outdent",bindKey:s("Ctrl-[","Ctrl-["),exec:function(e){e.blockOutdent()},multiSelectAction:"forEachLine",scrollIntoView:"selectionPart"},{name:"blockindent",description:"Block indent",bindKey:s("Ctrl-]","Ctrl-]"),exec:function(e){e.blockIndent()},multiSelectAction:"forEachLine",scrollIntoView:"selectionPart"},{name:"insertstring",description:"Insert string",exec:function(e,t){e.insert(t)},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"inserttext",description:"Insert text",exec:function(e,t){e.insert(n.stringRepeat(t.text||"",t.times||1))},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"splitline",description:"Split line",bindKey:s(null,"Ctrl-O"),exec:function(e){e.splitLine()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"transposeletters",description:"Transpose letters",bindKey:s("Alt-Shift-X","Ctrl-T"),exec:function(e){e.transposeLetters()},multiSelectAction:function(e){e.transposeSelections(1)},scrollIntoView:"cursor"},{name:"touppercase",description:"To uppercase",bindKey:s("Ctrl-U","Ctrl-U"),exec:function(e){e.toUpperCase()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"tolowercase",description:"To lowercase",bindKey:s("Ctrl-Shift-U","Ctrl-Shift-U"),exec:function(e){e.toLowerCase()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"autoindent",description:"Auto Indent",bindKey:s(null,null),exec:function(e){e.autoIndent()},scrollIntoView:"animate"},{name:"expandtoline",description:"Expand to line",bindKey:s("Ctrl-Shift-L","Command-Shift-L"),exec:function(e){var t=e.selection.getRange();t.start.column=t.end.column=0,t.end.row++,e.selection.setRange(t,!1)},multiSelectAction:"forEach",scrollIntoView:"cursor",readOnly:!0},{name:"openlink",bindKey:s("Ctrl+F3","F3"),exec:function(e){e.openLink()}},{name:"joinlines",description:"Join lines",bindKey:s(null,null),exec:function(e){for(var t=e.selection.isBackwards(),i=t?e.selection.getSelectionLead():e.selection.getSelectionAnchor(),o=t?e.selection.getSelectionAnchor():e.selection.getSelectionLead(),s=e.session.doc.getLine(i.row).length,a=e.session.doc.getTextRange(e.selection.getRange()).replace(/\n\s*/," ").length,l=e.session.doc.getLine(i.row),c=i.row+1;c<=o.row+1;c++){var h=n.stringTrimLeft(n.stringTrimRight(e.session.doc.getLine(c)));0!==h.length&&(h=" "+h),l+=h}o.row+1<e.session.doc.getLength()-1&&(l+=e.session.doc.getNewLineCharacter()),e.clearSelection(),e.session.doc.replace(new r(i.row,0,o.row+2,0),l),a>0?(e.selection.moveCursorTo(i.row,i.column),e.selection.selectTo(i.row,i.column+a)):(s=e.session.doc.getLine(i.row).length>s?s+1:s,e.selection.moveCursorTo(i.row,s))},multiSelectAction:"forEach",readOnly:!0},{name:"invertSelection",description:"Invert selection",bindKey:s(null,null),exec:function(e){var t=e.session.doc.getLength()-1,i=e.session.doc.getLine(t).length,n=e.selection.rangeList.ranges,o=[];n.length<1&&(n=[e.selection.getRange()]);for(var s=0;s<n.length;s++)s==n.length-1&&(n[s].end.row!==t||n[s].end.column!==i)&&o.push(new r(n[s].end.row,n[s].end.column,t,i)),0===s?(0!==n[s].start.row||0!==n[s].start.column)&&o.push(new r(0,0,n[s].start.row,n[s].start.column)):o.push(new r(n[s-1].end.row,n[s-1].end.column,n[s].start.row,n[s].start.column));e.exitMultiSelectMode(),e.clearSelection();for(var s=0;s<o.length;s++)e.selection.addRange(o[s],!1)},readOnly:!0,scrollIntoView:"none"},{name:"addLineAfter",description:"Add new line after the current line",exec:function(e){e.selection.clearSelection(),e.navigateLineEnd(),e.insert("\n")},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"addLineBefore",description:"Add new line before the current line",exec:function(e){e.selection.clearSelection();var t=e.getCursorPosition();e.selection.moveTo(t.row-1,Number.MAX_VALUE),e.insert("\n"),0===t.row&&e.navigateUp()},multiSelectAction:"forEach",scrollIntoView:"cursor"},{name:"openCommandPallete",exec:function(e){console.warn("This is an obsolete command. Please use `openCommandPalette` instead."),e.prompt({$type:"commands"})},readOnly:!0},{name:"openCommandPalette",description:"Open command palette",bindKey:s("F1","F1"),exec:function(e){e.prompt({$type:"commands"})},readOnly:!0},{name:"modeSelect",description:"Change language mode...",bindKey:s(null,null),exec:function(e){e.prompt({$type:"modes"})},readOnly:!0}];for(var a=1;a<9;a++)t.commands.push({name:"foldToLevel"+a,description:"Fold To Level "+a,level:a,exec:function(e){e.session.foldToLevel(this.level)},scrollIntoView:"center",readOnly:!0})}),ace.define("ace/keyboard/gutter_handler",["require","exports","module","ace/lib/keys","ace/mouse/default_gutter_handler"],function(e,t,i){"use strict";var n=e("../lib/keys"),o=e("../mouse/default_gutter_handler").GutterTooltip;t.GutterKeyboardHandler=function(){function e(e){this.editor=e,this.gutterLayer=e.renderer.$gutterLayer,this.element=e.renderer.$gutter,this.lines=e.renderer.$gutterLayer.$lines,this.activeRowIndex=null,this.activeLane=null,this.annotationTooltip=new o(this.editor)}return e.prototype.addListener=function(){this.element.addEventListener("keydown",this.$onGutterKeyDown.bind(this)),this.element.addEventListener("focusout",this.$blurGutter.bind(this)),this.editor.on("mousewheel",this.$blurGutter.bind(this))},e.prototype.removeListener=function(){this.element.removeEventListener("keydown",this.$onGutterKeyDown.bind(this)),this.element.removeEventListener("focusout",this.$blurGutter.bind(this)),this.editor.off("mousewheel",this.$blurGutter.bind(this))},e.prototype.$onGutterKeyDown=function(e){if(this.annotationTooltip.isOpen){e.preventDefault(),e.keyCode===n.escape&&this.annotationTooltip.hideTooltip();return}if(e.target===this.element){if(e.keyCode!=n.enter)return;e.preventDefault();var t=this.editor.getCursorPosition().row;this.editor.isRowVisible(t)||this.editor.scrollToLine(t,!0,!0),setTimeout((function(){var e=this.$rowToRowIndex(this.gutterLayer.$cursorCell.row),t=this.$findNearestFoldLaneWidget(e),i=this.$findNearestAnnotation(e);if(null!==t||null!==i)return this.$findClosestNumber(t,i,e)===t?(this.activeLane="fold",this.activeRowIndex=t,this.$isCustomWidgetVisible(t))?void this.$focusCustomWidget(this.activeRowIndex):void this.$focusFoldWidget(this.activeRowIndex):(this.activeRowIndex=i,this.activeLane="annotation",this.$focusAnnotation(this.activeRowIndex),void 0)}).bind(this),10);return}this.$handleGutterKeyboardInteraction(e),setTimeout((function(){this.editor._signal("gutterkeydown",new r(e,this))}).bind(this),10)},e.prototype.$handleGutterKeyboardInteraction=function(e){if(e.keyCode===n.tab)return void e.preventDefault();if(e.keyCode===n.escape){e.preventDefault(),this.$blurGutter(),this.element.focus(),this.lane=null;return}if(e.keyCode===n.up){switch(e.preventDefault(),this.activeLane){case"fold":this.$moveFoldWidgetUp();break;case"annotation":this.$moveAnnotationUp()}return}if(e.keyCode===n.down){switch(e.preventDefault(),this.activeLane){case"fold":this.$moveFoldWidgetDown();break;case"annotation":this.$moveAnnotationDown()}return}if(e.keyCode===n.left){e.preventDefault(),this.$switchLane("annotation");return}if(e.keyCode===n.right){e.preventDefault(),this.$switchLane("fold");return}if(e.keyCode===n.enter||e.keyCode===n.space){switch(e.preventDefault(),this.activeLane){case"fold":var t=this.$rowIndexToRow(this.activeRowIndex),i=this.editor.session.$gutterCustomWidgets[t];i?i.callbacks&&i.callbacks.onClick&&i.callbacks.onClick(e,t):"start"===this.gutterLayer.session.foldWidgets[t]?(this.editor.session.onFoldWidgetClick(this.$rowIndexToRow(this.activeRowIndex),e),setTimeout((function(){this.$rowIndexToRow(this.activeRowIndex)!==t&&(this.$blurFoldWidget(this.activeRowIndex),this.activeRowIndex=this.$rowToRowIndex(t),this.$focusFoldWidget(this.activeRowIndex))}).bind(this),10)):this.gutterLayer.session.foldWidgets[this.$rowIndexToRow(this.activeRowIndex)];break;case"annotation":var o=this.lines.cells[this.activeRowIndex].element.childNodes[2].getBoundingClientRect(),r=this.annotationTooltip.getElement().style;r.left=o.right+"px",r.top=o.bottom+"px",this.annotationTooltip.showTooltip(this.$rowIndexToRow(this.activeRowIndex))}return}},e.prototype.$blurGutter=function(){if(null!==this.activeRowIndex)switch(this.activeLane){case"fold":this.$blurFoldWidget(this.activeRowIndex),this.$blurCustomWidget(this.activeRowIndex);break;case"annotation":this.$blurAnnotation(this.activeRowIndex)}this.annotationTooltip.isOpen&&this.annotationTooltip.hideTooltip()},e.prototype.$isFoldWidgetVisible=function(e){var t=this.editor.isRowFullyVisible(this.$rowIndexToRow(e)),i="none"!==this.$getFoldWidget(e).style.display;return t&&i},e.prototype.$isCustomWidgetVisible=function(e){var t=this.editor.isRowFullyVisible(this.$rowIndexToRow(e)),i=!!this.$getCustomWidget(e);return t&&i},e.prototype.$isAnnotationVisible=function(e){var t=this.editor.isRowFullyVisible(this.$rowIndexToRow(e)),i="none"!==this.$getAnnotation(e).style.display;return t&&i},e.prototype.$getFoldWidget=function(e){return this.lines.get(e).element.childNodes[1]},e.prototype.$getCustomWidget=function(e){return this.lines.get(e).element.childNodes[3]},e.prototype.$getAnnotation=function(e){return this.lines.get(e).element.childNodes[2]},e.prototype.$findNearestFoldLaneWidget=function(e){if(this.$isCustomWidgetVisible(e)||this.$isFoldWidgetVisible(e))return e;for(var t=0;e-t>0||e+t<this.lines.getLength()-1;){if(e-++t>=0&&this.$isCustomWidgetVisible(e-t))return e-t;if(e+t<=this.lines.getLength()-1&&this.$isCustomWidgetVisible(e+t))return e+t;if(e-t>=0&&this.$isFoldWidgetVisible(e-t))return e-t;if(e+t<=this.lines.getLength()-1&&this.$isFoldWidgetVisible(e+t))return e+t}return null},e.prototype.$findNearestAnnotation=function(e){if(this.$isAnnotationVisible(e))return e;for(var t=0;e-t>0||e+t<this.lines.getLength()-1;){if(e-++t>=0&&this.$isAnnotationVisible(e-t))return e-t;if(e+t<=this.lines.getLength()-1&&this.$isAnnotationVisible(e+t))return e+t}return null},e.prototype.$focusFoldWidget=function(e){if(null!=e){var t=this.$getFoldWidget(e);t.classList.add(this.editor.renderer.keyboardFocusClassName),t.focus()}},e.prototype.$focusCustomWidget=function(e){if(null!=e){var t=this.$getCustomWidget(e);t&&(t.classList.add(this.editor.renderer.keyboardFocusClassName),t.focus())}},e.prototype.$focusAnnotation=function(e){if(null!=e){var t=this.$getAnnotation(e);t.classList.add(this.editor.renderer.keyboardFocusClassName),t.focus()}},e.prototype.$blurFoldWidget=function(e){var t=this.$getFoldWidget(e);t.classList.remove(this.editor.renderer.keyboardFocusClassName),t.blur()},e.prototype.$blurCustomWidget=function(e){var t=this.$getCustomWidget(e);t&&(t.classList.remove(this.editor.renderer.keyboardFocusClassName),t.blur())},e.prototype.$blurAnnotation=function(e){var t=this.$getAnnotation(e);t.classList.remove(this.editor.renderer.keyboardFocusClassName),t.blur()},e.prototype.$moveFoldWidgetUp=function(){for(var e=this.activeRowIndex;e>0;)if(e--,this.$isFoldWidgetVisible(e)||this.$isCustomWidgetVisible(e)){this.$blurFoldWidget(this.activeRowIndex),this.$blurCustomWidget(this.activeRowIndex),this.activeRowIndex=e,this.$isFoldWidgetVisible(e)?this.$focusFoldWidget(this.activeRowIndex):this.$focusCustomWidget(this.activeRowIndex);break}},e.prototype.$moveFoldWidgetDown=function(){for(var e=this.activeRowIndex;e<this.lines.getLength()-1;)if(e++,this.$isFoldWidgetVisible(e)||this.$isCustomWidgetVisible(e)){this.$blurFoldWidget(this.activeRowIndex),this.$blurCustomWidget(this.activeRowIndex),this.activeRowIndex=e,this.$isFoldWidgetVisible(e)?this.$focusFoldWidget(this.activeRowIndex):this.$focusCustomWidget(this.activeRowIndex);break}},e.prototype.$moveAnnotationUp=function(){for(var e=this.activeRowIndex;e>0;)if(e--,this.$isAnnotationVisible(e)){this.$blurAnnotation(this.activeRowIndex),this.activeRowIndex=e,this.$focusAnnotation(this.activeRowIndex);break}},e.prototype.$moveAnnotationDown=function(){for(var e=this.activeRowIndex;e<this.lines.getLength()-1;)if(e++,this.$isAnnotationVisible(e)){this.$blurAnnotation(this.activeRowIndex),this.activeRowIndex=e,this.$focusAnnotation(this.activeRowIndex);break}},e.prototype.$findClosestNumber=function(e,t,i){return null===e?t:null===t||Math.abs(i-e)<=Math.abs(i-t)?e:t},e.prototype.$switchLane=function(e){switch(e){case"annotation":if("annotation"===this.activeLane)break;var t=this.$findNearestAnnotation(this.activeRowIndex);if(null==t)break;this.activeLane="annotation",this.$blurFoldWidget(this.activeRowIndex),this.$blurCustomWidget(this.activeRowIndex),this.activeRowIndex=t,this.$focusAnnotation(this.activeRowIndex);break;case"fold":if("fold"===this.activeLane)break;var i=this.$findNearestFoldLaneWidget(this.activeRowIndex);if(null===i)break;this.activeLane="fold",this.$blurAnnotation(this.activeRowIndex),this.activeRowIndex=i,this.$isCustomWidgetVisible(i)?this.$focusCustomWidget(this.activeRowIndex):this.$focusFoldWidget(this.activeRowIndex)}},e.prototype.$rowIndexToRow=function(e){var t=this.lines.get(e);return t?t.row:null},e.prototype.$rowToRowIndex=function(e){for(var t=0;t<this.lines.getLength();t++)if(this.lines.get(t).row==e)return t;return null},e}();var r=function(){function e(e,t){this.gutterKeyboardHandler=t,this.domEvent=e}return e.prototype.getKey=function(){return n.keyCodeToString(this.domEvent.keyCode)},e.prototype.getRow=function(){return this.gutterKeyboardHandler.$rowIndexToRow(this.gutterKeyboardHandler.activeRowIndex)},e.prototype.isInAnnotationLane=function(){return"annotation"===this.gutterKeyboardHandler.activeLane},e.prototype.isInFoldLane=function(){return"fold"===this.gutterKeyboardHandler.activeLane},e}();t.GutterKeyboardEvent=r}),ace.define("ace/editor",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/lib/useragent","ace/keyboard/textinput","ace/mouse/mouse_handler","ace/mouse/fold_handler","ace/keyboard/keybinding","ace/edit_session","ace/search","ace/range","ace/lib/event_emitter","ace/commands/command_manager","ace/commands/default_commands","ace/config","ace/token_iterator","ace/keyboard/gutter_handler","ace/config","ace/clipboard","ace/lib/keys","ace/lib/event","ace/tooltip"],function(e,t,i){"use strict";var n=this&&this.__values||function(e){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&e[t],n=0;if(i)return i.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},o=e("./lib/oop"),r=e("./lib/dom"),s=e("./lib/lang"),a=e("./lib/useragent"),l=e("./keyboard/textinput").TextInput,c=e("./mouse/mouse_handler").MouseHandler,h=e("./mouse/fold_handler").FoldHandler,u=e("./keyboard/keybinding").KeyBinding,d=e("./edit_session").EditSession,p=e("./search").Search,g=e("./range").Range,f=e("./lib/event_emitter").EventEmitter,m=e("./commands/command_manager").CommandManager,y=e("./commands/default_commands").commands,v=e("./config"),w=e("./token_iterator").TokenIterator,b=e("./keyboard/gutter_handler").GutterKeyboardHandler,$=e("./config").nls,C=e("./clipboard"),S=e("./lib/keys"),x=e("./lib/event"),A=e("./tooltip").HoverTooltip,M=function(){function e(t,i,n){this.id="editor"+ ++e.$uid,this.session,this.$toDestroy=[];var o=t.getContainerElement();this.container=o,this.renderer=t,this.commands=new m(a.isMac?"mac":"win",y),"object"==typeof document&&(this.textInput=new l(t.getTextAreaContainer(),this),this.renderer.textarea=this.textInput.getElement(),this.$mouseHandler=new c(this),new h(this)),this.keyBinding=new u(this),this.$search=new p().set({wrap:!0}),this.$historyTracker=this.$historyTracker.bind(this),this.commands.on("exec",this.$historyTracker),this.$initOperationListeners(),this._$emitInputEvent=s.delayedCall((function(){this._signal("input",{}),this.session&&!this.session.destroyed&&this.session.bgTokenizer.scheduleStart()}).bind(this)),this.on("change",function(e,t){t._$emitInputEvent.schedule(31)}),this.setSession(i||n&&n.session||new d("")),v.resetOptions(this),n&&this.setOptions(n),v._signal("editor",this)}return e.prototype.$initOperationListeners=function(){this.commands.on("exec",this.startOperation.bind(this),!0),this.commands.on("afterExec",this.endOperation.bind(this),!0)},e.prototype.startOperation=function(e){this.session.startOperation(e)},e.prototype.endOperation=function(e){this.session.endOperation(e)},e.prototype.onStartOperation=function(e){this.curOp=this.session.curOp,this.curOp.scrollTop=this.renderer.scrollTop,this.prevOp=this.session.prevOp,e||(this.previousCommand=null)},e.prototype.onEndOperation=function(e){if(this.curOp&&this.session){if(e&&!1===e.returnValue){this.curOp=null;return}if(this._signal("beforeEndOperation"),this.curOp){var t=this.curOp.command,i=t&&t.scrollIntoView;if(i){switch(i){case"center-animate":i="animate";case"center":this.renderer.scrollCursorIntoView(null,.5);break;case"animate":case"cursor":this.renderer.scrollCursorIntoView();break;case"selectionPart":var n=this.selection.getRange(),o=this.renderer.layerConfig;(n.start.row>=o.lastRow||n.end.row<=o.firstRow)&&this.renderer.scrollSelectionIntoView(this.selection.anchor,this.selection.lead)}"animate"==i&&this.renderer.animateScrolling(this.curOp.scrollTop)}this.$lastSel=this.session.selection.toJSON(),this.prevOp=this.curOp,this.curOp=null}}},e.prototype.$historyTracker=function(e){if(this.$mergeUndoDeltas){var t=this.prevOp,i=this.$mergeableCommands,n=t.command&&e.command.name==t.command.name;if("insertstring"==e.command.name){var o=e.args;void 0===this.mergeNextCommand&&(this.mergeNextCommand=!0),n=n&&this.mergeNextCommand&&(!/\s/.test(o)||/\s/.test(t.args)),this.mergeNextCommand=!0}else n=n&&-1!==i.indexOf(e.command.name);"always"!=this.$mergeUndoDeltas&&Date.now()-this.sequenceStartTime>2e3&&(n=!1),n?this.session.mergeUndoDeltas=!0:-1!==i.indexOf(e.command.name)&&(this.sequenceStartTime=Date.now())}},e.prototype.setKeyboardHandler=function(e,t){if(e&&"string"==typeof e&&"ace"!=e){this.$keybindingId=e;var i=this;v.loadModule(["keybinding",e],function(n){i.$keybindingId==e&&i.keyBinding.setKeyboardHandler(n&&n.handler),t&&t()})}else this.$keybindingId=null,this.keyBinding.setKeyboardHandler(e),t&&t()},e.prototype.getKeyboardHandler=function(){return this.keyBinding.getKeyboardHandler()},e.prototype.setSession=function(e){if(this.session!=e){this.curOp&&this.endOperation(),this.curOp={};var t=this.session;if(t){this.session.off("change",this.$onDocumentChange),this.session.off("changeMode",this.$onChangeMode),this.session.off("tokenizerUpdate",this.$onTokenizerUpdate),this.session.off("changeTabSize",this.$onChangeTabSize),this.session.off("changeWrapLimit",this.$onChangeWrapLimit),this.session.off("changeWrapMode",this.$onChangeWrapMode),this.session.off("changeFold",this.$onChangeFold),this.session.off("changeFrontMarker",this.$onChangeFrontMarker),this.session.off("changeBackMarker",this.$onChangeBackMarker),this.session.off("changeBreakpoint",this.$onChangeBreakpoint),this.session.off("changeAnnotation",this.$onChangeAnnotation),this.session.off("changeOverwrite",this.$onCursorChange),this.session.off("changeScrollTop",this.$onScrollTopChange),this.session.off("changeScrollLeft",this.$onScrollLeftChange),this.session.off("startOperation",this.$onStartOperation),this.session.off("endOperation",this.$onEndOperation);var i=this.session.getSelection();i.off("changeCursor",this.$onCursorChange),i.off("changeSelection",this.$onSelectionChange)}this.session=e,e?(this.$onDocumentChange=this.onDocumentChange.bind(this),e.on("change",this.$onDocumentChange),this.renderer.setSession(e),this.$onChangeMode=this.onChangeMode.bind(this),e.on("changeMode",this.$onChangeMode),this.$onTokenizerUpdate=this.onTokenizerUpdate.bind(this),e.on("tokenizerUpdate",this.$onTokenizerUpdate),this.$onChangeTabSize=this.renderer.onChangeTabSize.bind(this.renderer),e.on("changeTabSize",this.$onChangeTabSize),this.$onChangeWrapLimit=this.onChangeWrapLimit.bind(this),e.on("changeWrapLimit",this.$onChangeWrapLimit),this.$onChangeWrapMode=this.onChangeWrapMode.bind(this),e.on("changeWrapMode",this.$onChangeWrapMode),this.$onChangeFold=this.onChangeFold.bind(this),e.on("changeFold",this.$onChangeFold),this.$onChangeFrontMarker=this.onChangeFrontMarker.bind(this),this.session.on("changeFrontMarker",this.$onChangeFrontMarker),this.$onChangeBackMarker=this.onChangeBackMarker.bind(this),this.session.on("changeBackMarker",this.$onChangeBackMarker),this.$onChangeBreakpoint=this.onChangeBreakpoint.bind(this),this.session.on("changeBreakpoint",this.$onChangeBreakpoint),this.$onChangeAnnotation=this.onChangeAnnotation.bind(this),this.session.on("changeAnnotation",this.$onChangeAnnotation),this.$onCursorChange=this.onCursorChange.bind(this),this.session.on("changeOverwrite",this.$onCursorChange),this.$onScrollTopChange=this.onScrollTopChange.bind(this),this.session.on("changeScrollTop",this.$onScrollTopChange),this.$onScrollLeftChange=this.onScrollLeftChange.bind(this),this.session.on("changeScrollLeft",this.$onScrollLeftChange),this.selection=e.getSelection(),this.selection.on("changeCursor",this.$onCursorChange),this.$onSelectionChange=this.onSelectionChange.bind(this),this.selection.on("changeSelection",this.$onSelectionChange),this.$onStartOperation=this.onStartOperation.bind(this),this.session.on("startOperation",this.$onStartOperation),this.$onEndOperation=this.onEndOperation.bind(this),this.session.on("endOperation",this.$onEndOperation),this.onChangeMode(),this.onCursorChange(),this.onScrollTopChange(),this.onScrollLeftChange(),this.onSelectionChange(),this.onChangeFrontMarker(),this.onChangeBackMarker(),this.onChangeBreakpoint(),this.onChangeAnnotation(),this.session.getUseWrapMode()&&this.renderer.adjustWrapLimit(),this.renderer.updateFull()):(this.selection=null,this.renderer.setSession(e)),this._signal("changeSession",{session:e,oldSession:t}),this.curOp=null,t&&t._signal("changeEditor",{oldEditor:this}),t&&(t.$editor=null),e&&e._signal("changeEditor",{editor:this}),e&&(e.$editor=this),e&&!e.destroyed&&e.bgTokenizer.scheduleStart()}},e.prototype.getSession=function(){return this.session},e.prototype.setValue=function(e,t){return this.session.doc.setValue(e),t?1==t?this.navigateFileEnd():-1==t&&this.navigateFileStart():this.selectAll(),e},e.prototype.getValue=function(){return this.session.getValue()},e.prototype.getSelection=function(){return this.selection},e.prototype.resize=function(e){this.renderer.onResize(e)},e.prototype.setTheme=function(e,t){this.renderer.setTheme(e,t)},e.prototype.getTheme=function(){return this.renderer.getTheme()},e.prototype.setStyle=function(e,t){this.renderer.setStyle(e,t)},e.prototype.unsetStyle=function(e){this.renderer.unsetStyle(e)},e.prototype.getFontSize=function(){return this.getOption("fontSize")||r.computedStyle(this.container).fontSize},e.prototype.setFontSize=function(e){this.setOption("fontSize",e)},e.prototype.$highlightBrackets=function(){if(!this.$highlightPending){var e=this;this.$highlightPending=!0,setTimeout(function(){e.$highlightPending=!1;var t=e.session;if(t&&!t.destroyed){t.$bracketHighlight&&(t.$bracketHighlight.markerIds.forEach(function(e){t.removeMarker(e)}),t.$bracketHighlight=null);var i=e.getCursorPosition(),n=e.getKeyboardHandler(),o=n&&n.$getDirectionForHighlight&&n.$getDirectionForHighlight(e),r=t.getMatchingBracketRanges(i,o);if(!r){var s=new w(t,i.row,i.column).getCurrentToken();if(s&&/\b(?:tag-open|tag-name)/.test(s.type)){var a=t.getMatchingTags(i);a&&(r=[a.openTagName.isEmpty()?a.openTag:a.openTagName,a.closeTagName.isEmpty()?a.closeTag:a.closeTagName])}}if(!r&&t.$mode.getMatching&&(r=t.$mode.getMatching(e.session)),!r){e.getHighlightIndentGuides()&&e.renderer.$textLayer.$highlightIndentGuide();return}var l="ace_bracket";Array.isArray(r)?1==r.length&&(l="ace_error_bracket"):r=[r],2==r.length&&(0==g.comparePoints(r[0].end,r[1].start)?r=[g.fromPoints(r[0].start,r[1].end)]:0==g.comparePoints(r[0].start,r[1].end)&&(r=[g.fromPoints(r[1].start,r[0].end)])),t.$bracketHighlight={ranges:r,markerIds:r.map(function(e){return t.addMarker(e,l,"text")})},e.getHighlightIndentGuides()&&e.renderer.$textLayer.$highlightIndentGuide()}},50)}},e.prototype.focus=function(){this.textInput.focus()},e.prototype.isFocused=function(){return this.textInput.isFocused()},e.prototype.blur=function(){this.textInput.blur()},e.prototype.onFocus=function(e){this.$isFocused||(this.$isFocused=!0,this.renderer.showCursor(),this.renderer.visualizeFocus(),this._emit("focus",e))},e.prototype.onBlur=function(e){this.$isFocused&&(this.$isFocused=!1,this.renderer.hideCursor(),this.renderer.visualizeBlur(),this._emit("blur",e))},e.prototype.$cursorChange=function(){this.renderer.updateCursor(),this.$highlightBrackets(),this.$updateHighlightActiveLine()},e.prototype.onDocumentChange=function(e){var t=this.session.$useWrapMode,i=e.start.row==e.end.row?e.end.row:1/0;this.renderer.updateLines(e.start.row,i,t),this._signal("change",e),this.$cursorChange()},e.prototype.onTokenizerUpdate=function(e){var t=e.data;this.renderer.updateLines(t.first,t.last)},e.prototype.onScrollTopChange=function(){this.renderer.scrollToY(this.session.getScrollTop())},e.prototype.onScrollLeftChange=function(){this.renderer.scrollToX(this.session.getScrollLeft())},e.prototype.onCursorChange=function(){this.$cursorChange(),this._signal("changeSelection")},e.prototype.$updateHighlightActiveLine=function(){var e,t=this.getSession();if(this.$highlightActiveLine&&("line"==this.$selectionStyle&&this.selection.isMultiLine()||(e=this.getCursorPosition()),this.renderer.theme&&this.renderer.theme.$selectionColorConflict&&!this.selection.isEmpty()&&(e=!1),this.renderer.$maxLines&&1===this.session.getLength()&&!(this.renderer.$minLines>1)&&(e=!1)),t.$highlightLineMarker&&!e)t.removeMarker(t.$highlightLineMarker.id),t.$highlightLineMarker=null;else if(!t.$highlightLineMarker&&e){var i=new g(e.row,e.column,e.row,1/0);i.id=t.addMarker(i,"ace_active-line","screenLine"),t.$highlightLineMarker=i}else e&&(t.$highlightLineMarker.start.row=e.row,t.$highlightLineMarker.end.row=e.row,t.$highlightLineMarker.start.column=e.column,t._signal("changeBackMarker"))},e.prototype.onSelectionChange=function(e){var t=this.session;if(t.$selectionMarker&&t.removeMarker(t.$selectionMarker),t.$selectionMarker=null,this.selection.isEmpty())this.$updateHighlightActiveLine();else{var i=this.selection.getRange(),n=this.getSelectionStyle();t.$selectionMarker=t.addMarker(i,"ace_selection",n)}var o=this.$highlightSelectedWord&&this.$getSelectionHighLightRegexp();this.session.highlight(o),this._signal("changeSelection")},e.prototype.$getSelectionHighLightRegexp=function(){var e=this.session,t=this.getSelectionRange();if(!(t.isEmpty()||t.isMultiLine())){var i=t.start.column,n=t.end.column,o=e.getLine(t.start.row),r=o.substring(i,n);if(!(r.length>5e3)&&/[\w\d]/.test(r)){var s=this.$search.$assembleRegExp({wholeWord:!0,caseSensitive:!0,needle:r}),a=o.substring(i-1,n+1);if(s.test(a))return s}}},e.prototype.onChangeFrontMarker=function(){this.renderer.updateFrontMarkers()},e.prototype.onChangeBackMarker=function(){this.renderer.updateBackMarkers()},e.prototype.onChangeBreakpoint=function(){this.renderer.updateBreakpoints()},e.prototype.onChangeAnnotation=function(){this.renderer.setAnnotations(this.session.getAnnotations())},e.prototype.onChangeMode=function(e){this.renderer.updateText(),this._emit("changeMode",e)},e.prototype.onChangeWrapLimit=function(){this.renderer.updateFull()},e.prototype.onChangeWrapMode=function(){this.renderer.onResize(!0)},e.prototype.onChangeFold=function(){this.$updateHighlightActiveLine(),this.renderer.updateFull()},e.prototype.getSelectedText=function(){return this.session.getTextRange(this.getSelectionRange())},e.prototype.getCopyText=function(){var e=this.getSelectedText(),t=this.session.doc.getNewLineCharacter(),i=!1;if(!e&&this.$copyWithEmptySelection){i=!0;for(var n=this.selection.getAllRanges(),o=0;o<n.length;o++){var r=n[o];o&&n[o-1].start.row==r.start.row||(e+=this.session.getLine(r.start.row)+t)}}var s={text:e};return this._signal("copy",s),C.lineMode=!!i&&s.text,s.text},e.prototype.onCopy=function(){this.commands.exec("copy",this)},e.prototype.onCut=function(){this.commands.exec("cut",this)},e.prototype.onPaste=function(e,t){this.commands.exec("paste",this,{text:e,event:t})},e.prototype.$handlePaste=function(e){"string"==typeof e&&(e={text:e}),this._signal("paste",e);var t=e.text,i=t===C.lineMode,n=this.session;if(!this.inMultiSelectMode||this.inVirtualSelectionMode)i?n.insert({row:this.selection.lead.row,column:0},t):this.insert(t);else if(i)this.selection.rangeList.ranges.forEach(function(e){n.insert({row:e.start.row,column:0},t)});else{var o=t.split(/\r\n|\r|\n/),r=this.selection.rangeList.ranges,s=2==o.length&&(!o[0]||!o[1]);if(o.length!=r.length||s)return this.commands.exec("insertstring",this,t);for(var a=r.length;a--;){var l=r[a];l.isEmpty()||n.remove(l),n.insert(l.start,o[a])}}},e.prototype.execCommand=function(e,t){return this.commands.exec(e,this,t)},e.prototype.insert=function(e,t){var i=this.session,n=i.getMode(),o=this.getCursorPosition();if(this.getBehavioursEnabled()&&!t){var r=n.transformAction(i.getState(o.row),"insertion",this,i,e);r&&(e===r.text||this.inVirtualSelectionMode||(this.session.mergeUndoDeltas=!1,this.mergeNextCommand=!1),e=r.text)}if("	"==e&&(e=this.session.getTabString()),this.selection.isEmpty()){if(this.session.getOverwrite()&&-1==e.indexOf("\n")){var s=g.fromPoints(o,o);s.end.column+=e.length,this.session.remove(s)}}else{var s=this.getSelectionRange();o=this.session.remove(s),this.clearSelection()}if("\n"==e||"\r\n"==e){var a=i.getLine(o.row);if(o.column>a.search(/\S|$/)){var l=a.substr(o.column).search(/\S|$/);i.doc.removeInLine(o.row,o.column,o.column+l)}}this.clearSelection();var c=o.column,h=i.getState(o.row),a=i.getLine(o.row),u=n.checkOutdent(h,a,e);if(i.insert(o,e),r&&r.selection&&(2==r.selection.length?this.selection.setSelectionRange(new g(o.row,c+r.selection[0],o.row,c+r.selection[1])):this.selection.setSelectionRange(new g(o.row+r.selection[0],r.selection[1],o.row+r.selection[2],r.selection[3]))),this.$enableAutoIndent){if(i.getDocument().isNewLine(e)){var d=n.getNextLineIndent(h,a.slice(0,o.column),i.getTabString());i.insert({row:o.row+1,column:0},d)}u&&n.autoOutdent(h,i,o.row)}},e.prototype.autoIndent=function(){for(var e=this.session,t=e.getMode(),i=this.selection.isEmpty()?[new g(0,0,e.doc.getLength()-1,0)]:this.selection.getAllRanges(),n="",o="",r="",s=e.getTabString(),a=0;a<i.length;a++)for(var l=i[a].start.row,c=i[a].end.row,h=l;h<=c;h++){h>0&&(n=e.getState(h-1),o=e.getLine(h-1),r=t.getNextLineIndent(n,o,s));var u=e.getLine(h),d=t.$getIndent(u);if(r!==d){if(d.length>0){var p=new g(h,0,h,d.length);e.remove(p)}r.length>0&&e.insert({row:h,column:0},r)}t.autoOutdent(n,e,h)}},e.prototype.onTextInput=function(e,t){if(!t)return this.keyBinding.onTextInput(e);this.startOperation({command:{name:"insertstring"}});var i=this.applyComposition.bind(this,e,t);this.selection.rangeCount?this.forEachSelection(i):i(),this.endOperation()},e.prototype.applyComposition=function(e,t){if(t.extendLeft||t.extendRight){var i=this.selection.getRange();i.start.column-=t.extendLeft,i.end.column+=t.extendRight,i.start.column<0&&(i.start.row--,i.start.column+=this.session.getLine(i.start.row).length+1),this.selection.setRange(i),e||i.isEmpty()||this.remove()}if((e||!this.selection.isEmpty())&&this.insert(e,!0),t.restoreStart||t.restoreEnd){var i=this.selection.getRange();i.start.column-=t.restoreStart,i.end.column-=t.restoreEnd,this.selection.setRange(i)}},e.prototype.onCommandKey=function(e,t,i){return this.keyBinding.onCommandKey(e,t,i)},e.prototype.setOverwrite=function(e){this.session.setOverwrite(e)},e.prototype.getOverwrite=function(){return this.session.getOverwrite()},e.prototype.toggleOverwrite=function(){this.session.toggleOverwrite()},e.prototype.setScrollSpeed=function(e){this.setOption("scrollSpeed",e)},e.prototype.getScrollSpeed=function(){return this.getOption("scrollSpeed")},e.prototype.setDragDelay=function(e){this.setOption("dragDelay",e)},e.prototype.getDragDelay=function(){return this.getOption("dragDelay")},e.prototype.setSelectionStyle=function(e){this.setOption("selectionStyle",e)},e.prototype.getSelectionStyle=function(){return this.getOption("selectionStyle")},e.prototype.setHighlightActiveLine=function(e){this.setOption("highlightActiveLine",e)},e.prototype.getHighlightActiveLine=function(){return this.getOption("highlightActiveLine")},e.prototype.setHighlightGutterLine=function(e){this.setOption("highlightGutterLine",e)},e.prototype.getHighlightGutterLine=function(){return this.getOption("highlightGutterLine")},e.prototype.setHighlightSelectedWord=function(e){this.setOption("highlightSelectedWord",e)},e.prototype.getHighlightSelectedWord=function(){return this.$highlightSelectedWord},e.prototype.setAnimatedScroll=function(e){this.renderer.setAnimatedScroll(e)},e.prototype.getAnimatedScroll=function(){return this.renderer.getAnimatedScroll()},e.prototype.setShowInvisibles=function(e){this.renderer.setShowInvisibles(e)},e.prototype.getShowInvisibles=function(){return this.renderer.getShowInvisibles()},e.prototype.setDisplayIndentGuides=function(e){this.renderer.setDisplayIndentGuides(e)},e.prototype.getDisplayIndentGuides=function(){return this.renderer.getDisplayIndentGuides()},e.prototype.setHighlightIndentGuides=function(e){this.renderer.setHighlightIndentGuides(e)},e.prototype.getHighlightIndentGuides=function(){return this.renderer.getHighlightIndentGuides()},e.prototype.setShowPrintMargin=function(e){this.renderer.setShowPrintMargin(e)},e.prototype.getShowPrintMargin=function(){return this.renderer.getShowPrintMargin()},e.prototype.setPrintMarginColumn=function(e){this.renderer.setPrintMarginColumn(e)},e.prototype.getPrintMarginColumn=function(){return this.renderer.getPrintMarginColumn()},e.prototype.setReadOnly=function(e){this.setOption("readOnly",e)},e.prototype.getReadOnly=function(){return this.getOption("readOnly")},e.prototype.setBehavioursEnabled=function(e){this.setOption("behavioursEnabled",e)},e.prototype.getBehavioursEnabled=function(){return this.getOption("behavioursEnabled")},e.prototype.setWrapBehavioursEnabled=function(e){this.setOption("wrapBehavioursEnabled",e)},e.prototype.getWrapBehavioursEnabled=function(){return this.getOption("wrapBehavioursEnabled")},e.prototype.setShowFoldWidgets=function(e){this.setOption("showFoldWidgets",e)},e.prototype.getShowFoldWidgets=function(){return this.getOption("showFoldWidgets")},e.prototype.setFadeFoldWidgets=function(e){this.setOption("fadeFoldWidgets",e)},e.prototype.getFadeFoldWidgets=function(){return this.getOption("fadeFoldWidgets")},e.prototype.remove=function(e){this.selection.isEmpty()&&("left"==e?this.selection.selectLeft():this.selection.selectRight());var t=this.getSelectionRange();if(this.getBehavioursEnabled()){var i=this.session,n=i.getState(t.start.row),o=i.getMode().transformAction(n,"deletion",this,i,t);if(0===t.end.column){var r=i.getTextRange(t);if("\n"==r[r.length-1]){var s=i.getLine(t.end.row);/^\s+$/.test(s)&&(t.end.column=s.length)}}o&&(t=o)}this.session.remove(t),this.clearSelection()},e.prototype.removeWordRight=function(){this.selection.isEmpty()&&this.selection.selectWordRight(),this.session.remove(this.getSelectionRange()),this.clearSelection()},e.prototype.removeWordLeft=function(){this.selection.isEmpty()&&this.selection.selectWordLeft(),this.session.remove(this.getSelectionRange()),this.clearSelection()},e.prototype.removeToLineStart=function(){this.selection.isEmpty()&&this.selection.selectLineStart(),this.selection.isEmpty()&&this.selection.selectLeft(),this.session.remove(this.getSelectionRange()),this.clearSelection()},e.prototype.removeToLineEnd=function(){this.selection.isEmpty()&&this.selection.selectLineEnd();var e=this.getSelectionRange();e.start.column==e.end.column&&e.start.row==e.end.row&&(e.end.column=0,e.end.row++),this.session.remove(e),this.clearSelection()},e.prototype.splitLine=function(){this.selection.isEmpty()||(this.session.remove(this.getSelectionRange()),this.clearSelection());var e=this.getCursorPosition();this.insert("\n"),this.moveCursorToPosition(e)},e.prototype.setGhostText=function(e,t){this.renderer.setGhostText(e,t)},e.prototype.removeGhostText=function(){this.renderer.removeGhostText()},e.prototype.transposeLetters=function(){if(this.selection.isEmpty()){var e,t,i=this.getCursorPosition(),n=i.column;if(0!==n){var o=this.session.getLine(i.row);n<o.length?(e=o.charAt(n)+o.charAt(n-1),t=new g(i.row,n-1,i.row,n+1)):(e=o.charAt(n-1)+o.charAt(n-2),t=new g(i.row,n-2,i.row,n)),this.session.replace(t,e),this.session.selection.moveToPosition(t.end)}}},e.prototype.toLowerCase=function(){var e=this.getSelectionRange();this.selection.isEmpty()&&this.selection.selectWord();var t=this.getSelectionRange(),i=this.session.getTextRange(t);this.session.replace(t,i.toLowerCase()),this.selection.setSelectionRange(e)},e.prototype.toUpperCase=function(){var e=this.getSelectionRange();this.selection.isEmpty()&&this.selection.selectWord();var t=this.getSelectionRange(),i=this.session.getTextRange(t);this.session.replace(t,i.toUpperCase()),this.selection.setSelectionRange(e)},e.prototype.indent=function(){var e=this.session,t=this.getSelectionRange();if(t.start.row<t.end.row){var i=this.$getSelectedRows();e.indentRows(i.first,i.last,"	");return}if(t.start.column<t.end.column){var n=e.getTextRange(t);if(!/^\s+$/.test(n)){var i=this.$getSelectedRows();e.indentRows(i.first,i.last,"	");return}}var o=e.getLine(t.start.row),r=t.start,a=e.getTabSize(),l=e.documentToScreenColumn(r.row,r.column);if(this.session.getUseSoftTabs())var c=a-l%a,h=s.stringRepeat(" ",c);else{for(var c=l%a;" "==o[t.start.column-1]&&c;)t.start.column--,c--;this.selection.setSelectionRange(t),h="	"}return this.insert(h)},e.prototype.blockIndent=function(){var e=this.$getSelectedRows();this.session.indentRows(e.first,e.last,"	")},e.prototype.blockOutdent=function(){var e=this.session.getSelection();this.session.outdentRows(e.getRange())},e.prototype.sortLines=function(){for(var e=this.$getSelectedRows(),t=this.session,i=[],n=e.first;n<=e.last;n++)i.push(t.getLine(n));i.sort(function(e,t){return e.toLowerCase()<t.toLowerCase()?-1:+(e.toLowerCase()>t.toLowerCase())});for(var o=new g(0,0,0,0),n=e.first;n<=e.last;n++){var r=t.getLine(n);o.start.row=n,o.end.row=n,o.end.column=r.length,t.replace(o,i[n-e.first])}},e.prototype.toggleCommentLines=function(){var e=this.session.getState(this.getCursorPosition().row),t=this.$getSelectedRows();this.session.getMode().toggleCommentLines(e,this.session,t.first,t.last)},e.prototype.toggleBlockComment=function(){var e=this.getCursorPosition(),t=this.session.getState(e.row),i=this.getSelectionRange();this.session.getMode().toggleBlockComment(t,this.session,i,e)},e.prototype.getNumberAt=function(e,t){var i=/[\-]?[0-9]+(?:\.[0-9]+)?/g;i.lastIndex=0;for(var n=this.session.getLine(e);i.lastIndex<t;){var o=i.exec(n);if(o.index<=t&&o.index+o[0].length>=t)return{value:o[0],start:o.index,end:o.index+o[0].length}}return null},e.prototype.modifyNumber=function(e){var t=this.selection.getCursor().row,i=this.selection.getCursor().column,n=new g(t,i-1,t,i),o=this.session.getTextRange(n);if(!isNaN(parseFloat(o))&&isFinite(o)){var r=this.getNumberAt(t,i);if(r){var s=r.value.indexOf(".")>=0?r.start+r.value.indexOf(".")+1:r.end,a=r.start+r.value.length-s,l=parseFloat(r.value);l*=Math.pow(10,a),s!==r.end&&i<s?e*=Math.pow(10,r.end-i-1):e*=Math.pow(10,r.end-i),l+=e;var c=(l/=Math.pow(10,a)).toFixed(a),h=new g(t,r.start,t,r.end);this.session.replace(h,c),this.moveCursorTo(t,Math.max(r.start+1,i+c.length-r.value.length))}}else this.toggleWord()},e.prototype.toggleWord=function(){var e,t=this.selection.getCursor().row,i=this.selection.getCursor().column;this.selection.selectWord();var n=this.getSelectedText(),o=this.selection.getWordRange().start.column,r=n.replace(/([a-z]+|[A-Z]+)(?=[A-Z_]|$)/g,"$1 ").split(/\s/),a=i-o-1;a<0&&(a=0);var l=0,c=0,h=this;n.match(/[A-Za-z0-9_]+/)&&r.forEach(function(e,i){c=l+e.length,a>=l&&a<=c&&(n=e,h.selection.clearSelection(),h.moveCursorTo(t,l+o),h.selection.selectTo(t,c+o)),l=c});for(var u=this.$toggleWordPairs,d=0;d<u.length;d++)for(var p=u[d],g=0;g<=1;g++){var f=+!g,m=n.match(RegExp("^\\s?_?("+s.escapeRegExp(p[g])+")\\s?$","i"));m&&n.match(RegExp("([_]|^|\\s)("+s.escapeRegExp(m[1])+")($|\\s)","g"))&&(e=n.replace(RegExp(s.escapeRegExp(p[g]),"i"),function(e){var t=p[f];return e.toUpperCase()==e?t=t.toUpperCase():e.charAt(0).toUpperCase()==e.charAt(0)&&(t=t.substr(0,0)+p[f].charAt(0).toUpperCase()+t.substr(1)),t}),this.insert(e),e="")}},e.prototype.findLinkAt=function(e,t){var i=this.session.getLine(e).split(/((?:https?|ftp):\/\/[\S]+)/),o=t;o<0&&(o=0);var r,s,a,l=0,c=0;try{for(var h=n(i),u=h.next();!u.done;u=h.next()){var d=u.value;if(c=l+d.length,o>=l&&o<=c&&d.match(/((?:https?|ftp):\/\/[\S]+)/)){a=d.replace(/[\s:.,'";}\]]+$/,"");break}l=c}}catch(e){r={error:e}}finally{try{u&&!u.done&&(s=h.return)&&s.call(h)}finally{if(r)throw r.error}}return a},e.prototype.openLink=function(){var e=this.selection.getCursor(),t=this.findLinkAt(e.row,e.column);return t&&window.open(t,"_blank"),null!=t},e.prototype.removeLines=function(){var e=this.$getSelectedRows();this.session.removeFullLines(e.first,e.last),this.clearSelection()},e.prototype.duplicateSelection=function(){var e=this.selection,t=this.session,i=e.getRange(),n=e.isBackwards();if(i.isEmpty()){var o=i.start.row;t.duplicateLines(o,o)}else{var r=n?i.start:i.end,s=t.insert(r,t.getTextRange(i));i.start=r,i.end=s,e.setSelectionRange(i,n)}},e.prototype.moveLinesDown=function(){this.$moveLines(1,!1)},e.prototype.moveLinesUp=function(){this.$moveLines(-1,!1)},e.prototype.moveText=function(e,t,i){return this.session.moveText(e,t,i)},e.prototype.copyLinesUp=function(){this.$moveLines(-1,!0)},e.prototype.copyLinesDown=function(){this.$moveLines(1,!0)},e.prototype.$moveLines=function(e,t){var i,n,o=this.selection;if(!o.inMultiSelectMode||this.inVirtualSelectionMode){var r=o.toOrientedRange();i=this.$getSelectedRows(r),n=this.session.$moveLines(i.first,i.last,t?0:e),t&&-1==e&&(n=0),r.moveBy(n,0),o.fromOrientedRange(r)}else{var s=o.rangeList.ranges;o.rangeList.detach(this.session),this.inVirtualSelectionMode=!0;for(var a=0,l=0,c=s.length,h=0;h<c;h++){var u=h;s[h].moveBy(a,0);for(var d=(i=this.$getSelectedRows(s[h])).first,p=i.last;++h<c;){l&&s[h].moveBy(l,0);var g=this.$getSelectedRows(s[h]);if(t&&g.first!=p||!t&&g.first>p+1)break;p=g.last}for(h--,a=this.session.$moveLines(d,p,t?0:e),t&&-1==e&&(u=h+1);u<=h;)s[u].moveBy(a,0),u++;t||(a=0),l+=a}o.fromOrientedRange(o.ranges[0]),o.rangeList.attach(this.session),this.inVirtualSelectionMode=!1}},e.prototype.$getSelectedRows=function(e){return e=(e||this.getSelectionRange()).collapseRows(),{first:this.session.getRowFoldStart(e.start.row),last:this.session.getRowFoldEnd(e.end.row)}},e.prototype.onCompositionStart=function(e){this.renderer.showComposition(e)},e.prototype.onCompositionUpdate=function(e){this.renderer.setCompositionText(e)},e.prototype.onCompositionEnd=function(){this.renderer.hideComposition()},e.prototype.getFirstVisibleRow=function(){return this.renderer.getFirstVisibleRow()},e.prototype.getLastVisibleRow=function(){return this.renderer.getLastVisibleRow()},e.prototype.isRowVisible=function(e){return e>=this.getFirstVisibleRow()&&e<=this.getLastVisibleRow()},e.prototype.isRowFullyVisible=function(e){return e>=this.renderer.getFirstFullyVisibleRow()&&e<=this.renderer.getLastFullyVisibleRow()},e.prototype.$getVisibleRowCount=function(){return this.renderer.getScrollBottomRow()-this.renderer.getScrollTopRow()+1},e.prototype.$moveByPage=function(e,t){var i=this.renderer,n=this.renderer.layerConfig,o=e*Math.floor(n.height/n.lineHeight);!0===t?this.selection.$moveSelection(function(){this.moveCursorBy(o,0)}):!1===t&&(this.selection.moveCursorBy(o,0),this.selection.clearSelection());var r=i.scrollTop;i.scrollBy(0,o*n.lineHeight),null!=t&&i.scrollCursorIntoView(null,.5),i.animateScrolling(r)},e.prototype.selectPageDown=function(){this.$moveByPage(1,!0)},e.prototype.selectPageUp=function(){this.$moveByPage(-1,!0)},e.prototype.gotoPageDown=function(){this.$moveByPage(1,!1)},e.prototype.gotoPageUp=function(){this.$moveByPage(-1,!1)},e.prototype.scrollPageDown=function(){this.$moveByPage(1)},e.prototype.scrollPageUp=function(){this.$moveByPage(-1)},e.prototype.scrollToRow=function(e){this.renderer.scrollToRow(e)},e.prototype.scrollToLine=function(e,t,i,n){this.renderer.scrollToLine(e,t,i,n)},e.prototype.centerSelection=function(){var e=this.getSelectionRange(),t={row:Math.floor(e.start.row+(e.end.row-e.start.row)/2),column:Math.floor(e.start.column+(e.end.column-e.start.column)/2)};this.renderer.alignCursor(t,.5)},e.prototype.getCursorPosition=function(){return this.selection.getCursor()},e.prototype.getCursorPositionScreen=function(){return this.session.documentToScreenPosition(this.getCursorPosition())},e.prototype.getSelectionRange=function(){return this.selection.getRange()},e.prototype.selectAll=function(){this.selection.selectAll()},e.prototype.clearSelection=function(){this.selection.clearSelection()},e.prototype.moveCursorTo=function(e,t){this.selection.moveCursorTo(e,t)},e.prototype.moveCursorToPosition=function(e){this.selection.moveCursorToPosition(e)},e.prototype.jumpToMatching=function(e,t){var i,n,o,r,s=this.getCursorPosition(),a=new w(this.session,s.row,s.column),l=a.getCurrentToken(),c=0;l&&-1!==l.type.indexOf("tag-name")&&(l=a.stepBackward());var h=l||a.stepForward();if(h){var u=!1,d={},p=s.column-h.start,f={")":"(","(":"(","]":"[","[":"[","{":"{","}":"{"};do{if(h.value.match(/[{}()\[\]]/g)){for(;p<h.value.length&&!u;p++)if(f[h.value[p]])switch(isNaN(d[n=f[h.value[p]]+"."+h.type.replace("rparen","lparen")])&&(d[n]=0),h.value[p]){case"(":case"[":case"{":d[n]++;break;case")":case"]":case"}":d[n]--,-1===d[n]&&(i="bracket",u=!0)}}else -1!==h.type.indexOf("tag-name")&&(isNaN(d[h.value])&&(d[h.value]=0),"<"===l.value&&c>1?d[h.value]++:"</"===l.value&&d[h.value]--,-1===d[h.value]&&(i="tag",u=!0));u||(l=h,c++,h=a.stepForward(),p=0)}while(h&&!u);if(i){if("bracket"===i)!(o=this.session.getBracketRange(s))&&(r=(o=new g(a.getCurrentTokenRow(),a.getCurrentTokenColumn()+p-1,a.getCurrentTokenRow(),a.getCurrentTokenColumn()+p-1)).start,(t||r.row===s.row&&2>Math.abs(r.column-s.column))&&(o=this.session.getBracketRange(r)));else if("tag"===i){if(!h||-1===h.type.indexOf("tag-name"))return;if(0===(o=new g(a.getCurrentTokenRow(),a.getCurrentTokenColumn()-2,a.getCurrentTokenRow(),a.getCurrentTokenColumn()-2)).compare(s.row,s.column)){var m=this.session.getMatchingTags(s);m&&(m.openTag.contains(s.row,s.column)?r=(o=m.closeTag).start:(o=m.openTag,r=m.closeTag.start.row===s.row&&m.closeTag.start.column===s.column?o.end:o.start))}r=r||o.start}(r=o&&o.cursor||r)&&(e?o&&t?this.selection.setRange(o):o&&o.isEqual(this.getSelectionRange())?this.clearSelection():this.selection.selectTo(r.row,r.column):this.selection.moveTo(r.row,r.column))}}},e.prototype.gotoLine=function(e,t,i){this.selection.clearSelection(),this.session.unfold({row:e-1,column:t||0}),this.exitMultiSelectMode&&this.exitMultiSelectMode(),this.moveCursorTo(e-1,t||0),this.isRowFullyVisible(e-1)||this.scrollToLine(e-1,!0,i)},e.prototype.navigateTo=function(e,t){this.selection.moveTo(e,t)},e.prototype.navigateUp=function(e){if(this.selection.isMultiLine()&&!this.selection.isBackwards()){var t=this.selection.anchor.getPosition();return this.moveCursorToPosition(t)}this.selection.clearSelection(),this.selection.moveCursorBy(-e||-1,0)},e.prototype.navigateDown=function(e){if(this.selection.isMultiLine()&&this.selection.isBackwards()){var t=this.selection.anchor.getPosition();return this.moveCursorToPosition(t)}this.selection.clearSelection(),this.selection.moveCursorBy(e||1,0)},e.prototype.navigateLeft=function(e){if(this.selection.isEmpty())for(e=e||1;e--;)this.selection.moveCursorLeft();else{var t=this.getSelectionRange().start;this.moveCursorToPosition(t)}this.clearSelection()},e.prototype.navigateRight=function(e){if(this.selection.isEmpty())for(e=e||1;e--;)this.selection.moveCursorRight();else{var t=this.getSelectionRange().end;this.moveCursorToPosition(t)}this.clearSelection()},e.prototype.navigateLineStart=function(){this.selection.moveCursorLineStart(),this.clearSelection()},e.prototype.navigateLineEnd=function(){this.selection.moveCursorLineEnd(),this.clearSelection()},e.prototype.navigateFileEnd=function(){this.selection.moveCursorFileEnd(),this.clearSelection()},e.prototype.navigateFileStart=function(){this.selection.moveCursorFileStart(),this.clearSelection()},e.prototype.navigateWordRight=function(){this.selection.moveCursorWordRight(),this.clearSelection()},e.prototype.navigateWordLeft=function(){this.selection.moveCursorWordLeft(),this.clearSelection()},e.prototype.replace=function(e,t){t&&this.$search.set(t);var i=this.$search.find(this.session),n=0;return i&&(this.$tryReplace(i,e)&&(n=1),this.selection.setSelectionRange(i),this.renderer.scrollSelectionIntoView(i.start,i.end)),n},e.prototype.replaceAll=function(e,t){t&&this.$search.set(t);var i=this.$search.findAll(this.session),n=0;if(!i.length)return n;var o=this.getSelectionRange();this.selection.moveTo(0,0);for(var r=i.length-1;r>=0;--r)this.$tryReplace(i[r],e)&&n++;return this.selection.setSelectionRange(o),n},e.prototype.$tryReplace=function(e,t){var i=this.session.getTextRange(e);return null!==(t=this.$search.replace(i,t))?(e.end=this.session.replace(e,t),e):null},e.prototype.getLastSearchOptions=function(){return this.$search.getOptions()},e.prototype.find=function(e,t,i){t||(t={}),"string"==typeof e||e instanceof RegExp?t.needle=e:"object"==typeof e&&o.mixin(t,e);var n=this.selection.getRange();null==t.needle&&((e=this.session.getTextRange(n)||this.$search.$options.needle)||(n=this.session.getWordRange(n.start.row,n.start.column),e=this.session.getTextRange(n)),this.$search.set({needle:e})),this.$search.set(t),t.start||this.$search.set({start:n});var r=this.$search.find(this.session);return t.preventScroll?r:r?(this.revealRange(r,i),r):void(t.backwards?n.start=n.end:n.end=n.start,this.selection.setRange(n))},e.prototype.findNext=function(e,t){this.find({skipCurrent:!0,backwards:!1},e,t)},e.prototype.findPrevious=function(e,t){this.find(e,{skipCurrent:!0,backwards:!0},t)},e.prototype.revealRange=function(e,t){this.session.unfold(e),this.selection.setSelectionRange(e);var i=this.renderer.scrollTop;this.renderer.scrollSelectionIntoView(e.start,e.end,.5),!1!==t&&this.renderer.animateScrolling(i)},e.prototype.undo=function(){this.session.getUndoManager().undo(this.session),this.renderer.scrollCursorIntoView(null,.5)},e.prototype.redo=function(){this.session.getUndoManager().redo(this.session),this.renderer.scrollCursorIntoView(null,.5)},e.prototype.destroy=function(){this.$toDestroy&&(this.$toDestroy.forEach(function(e){e.destroy()}),this.$toDestroy=null),this.$mouseHandler&&this.$mouseHandler.destroy(),this.renderer.destroy(),this._signal("destroy",this),this.session&&this.session.destroy(),this._$emitInputEvent&&this._$emitInputEvent.cancel(),this.removeAllListeners()},e.prototype.setAutoScrollEditorIntoView=function(e){if(e){var t,i=this,n=!1;this.$scrollAnchor||(this.$scrollAnchor=document.createElement("div"));var o=this.$scrollAnchor;o.style.cssText="position:absolute",this.container.insertBefore(o,this.container.firstChild);var r=this.on("changeSelection",function(){n=!0}),s=this.renderer.on("beforeRender",function(){n&&(t=i.renderer.container.getBoundingClientRect())}),a=this.renderer.on("afterRender",function(){if(n&&t&&(i.isFocused()||i.searchBox&&i.searchBox.isFocused())){var e=i.renderer,r=e.$cursorLayer.$pixelPos,s=e.layerConfig,a=r.top-s.offset;null!=(n=r.top>=0&&a+t.top<0||(!(r.top<s.height)||!(r.top+t.top+s.lineHeight>window.innerHeight))&&null)&&(o.style.top=a+"px",o.style.left=r.left+"px",o.style.height=s.lineHeight+"px",o.scrollIntoView(n)),n=t=null}});this.setAutoScrollEditorIntoView=function(e){e||(delete this.setAutoScrollEditorIntoView,this.off("changeSelection",r),this.renderer.off("afterRender",a),this.renderer.off("beforeRender",s))}}},e.prototype.$resetCursorStyle=function(){var e=this.$cursorStyle||"ace",t=this.renderer.$cursorLayer;t&&(t.setSmoothBlinking(/smooth/.test(e)),t.isBlinking=!this.$readOnly&&"wide"!=e,r.setCssClass(t.element,"ace_slim-cursors",/slim/.test(e)))},e.prototype.prompt=function(e,t,i){var n=this;v.loadModule("ace/ext/prompt",function(o){o.prompt(n,e,t,i)})},e}();M.$uid=0,M.prototype.curOp=null,M.prototype.prevOp={},M.prototype.$mergeableCommands=["backspace","del","insertstring"],M.prototype.$toggleWordPairs=[["first","last"],["true","false"],["yes","no"],["width","height"],["top","bottom"],["right","left"],["on","off"],["x","y"],["get","set"],["max","min"],["horizontal","vertical"],["show","hide"],["add","remove"],["up","down"],["before","after"],["even","odd"],["in","out"],["inside","outside"],["next","previous"],["increase","decrease"],["attach","detach"],["&&","||"],["==","!="]],o.implement(M.prototype,f),v.defineOptions(M.prototype,"editor",{selectionStyle:{set:function(e){this.onSelectionChange(),this._signal("changeSelectionStyle",{data:e})},initialValue:"line"},highlightActiveLine:{set:function(){this.$updateHighlightActiveLine()},initialValue:!0},highlightSelectedWord:{set:function(e){this.$onSelectionChange()},initialValue:!0},readOnly:{set:function(e){var t=this;this.textInput.setReadOnly(e),this.$resetCursorStyle(),this.$readOnlyCallback||(this.$readOnlyCallback=function(e){var i=!1;if(e&&"keydown"==e.type){if(!(i=e&&e.key&&1==e.key.length&&!e.ctrlKey&&!e.metaKey))return}else e&&"exec"!==e.type&&(i=!0);if(i){t.hoverTooltip||(t.hoverTooltip=new A);var n=r.createElement("div");n.textContent=$("editor.tooltip.disable-editing","Editing is disabled"),t.hoverTooltip.isOpen||t.hoverTooltip.showForRange(t,t.getSelectionRange(),n)}else t.hoverTooltip&&t.hoverTooltip.isOpen&&t.hoverTooltip.hide()});var i=this.textInput.getElement();e?(x.addListener(i,"keydown",this.$readOnlyCallback,this),this.commands.on("exec",this.$readOnlyCallback),this.commands.on("commandUnavailable",this.$readOnlyCallback)):(x.removeListener(i,"keydown",this.$readOnlyCallback),this.commands.off("exec",this.$readOnlyCallback),this.commands.off("commandUnavailable",this.$readOnlyCallback),this.hoverTooltip&&(this.hoverTooltip.destroy(),this.hoverTooltip=null))},initialValue:!1},copyWithEmptySelection:{set:function(e){this.textInput.setCopyWithEmptySelection(e)},initialValue:!1},cursorStyle:{set:function(e){this.$resetCursorStyle()},values:["ace","slim","smooth","wide"],initialValue:"ace"},mergeUndoDeltas:{values:[!1,!0,"always"],initialValue:!0},behavioursEnabled:{initialValue:!0},wrapBehavioursEnabled:{initialValue:!0},enableAutoIndent:{initialValue:!0},autoScrollEditorIntoView:{set:function(e){this.setAutoScrollEditorIntoView(e)}},keyboardHandler:{set:function(e){this.setKeyboardHandler(e)},get:function(){return this.$keybindingId},handlesSet:!0},value:{set:function(e){this.session.setValue(e)},get:function(){return this.getValue()},handlesSet:!0,hidden:!0},session:{set:function(e){this.setSession(e)},get:function(){return this.session},handlesSet:!0,hidden:!0},showLineNumbers:{set:function(e){this.renderer.$gutterLayer.setShowLineNumbers(e),this.renderer.$loop.schedule(this.renderer.CHANGE_GUTTER),e&&this.$relativeLineNumbers?k.attach(this):k.detach(this)},initialValue:!0},relativeLineNumbers:{set:function(e){this.$showLineNumbers&&e?k.attach(this):k.detach(this)}},placeholder:{set:function(e){this.$updatePlaceholder||(this.$updatePlaceholder=(function(){var e=this.session&&(this.renderer.$composition||this.session.getLength()>1||this.session.getLine(0).length>0);if(e&&this.renderer.placeholderNode)this.renderer.off("afterRender",this.$updatePlaceholder),r.removeCssClass(this.container,"ace_hasPlaceholder"),this.renderer.placeholderNode.remove(),this.renderer.placeholderNode=null;else if(e||this.renderer.placeholderNode)!e&&this.renderer.placeholderNode&&(this.renderer.placeholderNode.textContent=this.$placeholder||"");else{this.renderer.on("afterRender",this.$updatePlaceholder),r.addCssClass(this.container,"ace_hasPlaceholder");var t=r.createElement("div");t.className="ace_placeholder",t.textContent=this.$placeholder||"",this.renderer.placeholderNode=t,this.renderer.content.appendChild(this.renderer.placeholderNode)}}).bind(this),this.on("input",this.$updatePlaceholder)),this.$updatePlaceholder()}},enableKeyboardAccessibility:{set:function(e){var t,i={name:"blurTextInput",description:"Set focus to the editor content div to allow tabbing through the page",bindKey:"Esc",exec:function(e){e.blur(),e.renderer.scroller.focus()},readOnly:!0},n=function(e){if(e.target==this.renderer.scroller&&e.keyCode===S.enter){e.preventDefault();var t=this.getCursorPosition().row;this.isRowVisible(t)||this.scrollToLine(t,!0,!0),this.focus()}};e?(this.renderer.enableKeyboardAccessibility=!0,this.renderer.keyboardFocusClassName="ace_keyboard-focus",this.textInput.getElement().setAttribute("tabindex",-1),this.textInput.setNumberOfExtraLines(3*!!a.isWin),this.renderer.scroller.setAttribute("tabindex",0),this.renderer.scroller.setAttribute("role","group"),this.renderer.scroller.setAttribute("aria-roledescription",$("editor.scroller.aria-roledescription","editor")),this.renderer.scroller.classList.add(this.renderer.keyboardFocusClassName),this.renderer.scroller.setAttribute("aria-label",$("editor.scroller.aria-label","Editor content, press Enter to start editing, press Escape to exit")),this.renderer.scroller.addEventListener("keyup",n.bind(this)),this.commands.addCommand(i),this.renderer.$gutter.setAttribute("tabindex",0),this.renderer.$gutter.setAttribute("aria-hidden",!1),this.renderer.$gutter.setAttribute("role","group"),this.renderer.$gutter.setAttribute("aria-roledescription",$("editor.gutter.aria-roledescription","editor gutter")),this.renderer.$gutter.setAttribute("aria-label",$("editor.gutter.aria-label","Editor gutter, press Enter to interact with controls using arrow keys, press Escape to exit")),this.renderer.$gutter.classList.add(this.renderer.keyboardFocusClassName),this.renderer.content.setAttribute("aria-hidden",!0),t||(t=new b(this)),t.addListener(),this.textInput.setAriaOptions({setLabel:!0})):(this.renderer.enableKeyboardAccessibility=!1,this.textInput.getElement().setAttribute("tabindex",0),this.textInput.setNumberOfExtraLines(0),this.renderer.scroller.setAttribute("tabindex",-1),this.renderer.scroller.removeAttribute("role"),this.renderer.scroller.removeAttribute("aria-roledescription"),this.renderer.scroller.classList.remove(this.renderer.keyboardFocusClassName),this.renderer.scroller.removeAttribute("aria-label"),this.renderer.scroller.removeEventListener("keyup",n.bind(this)),this.commands.removeCommand(i),this.renderer.content.removeAttribute("aria-hidden"),this.renderer.$gutter.setAttribute("tabindex",-1),this.renderer.$gutter.setAttribute("aria-hidden",!0),this.renderer.$gutter.removeAttribute("role"),this.renderer.$gutter.removeAttribute("aria-roledescription"),this.renderer.$gutter.removeAttribute("aria-label"),this.renderer.$gutter.classList.remove(this.renderer.keyboardFocusClassName),t&&t.removeListener())},initialValue:!1},textInputAriaLabel:{set:function(e){this.$textInputAriaLabel=e},initialValue:""},enableMobileMenu:{set:function(e){this.$enableMobileMenu=e},initialValue:!0},customScrollbar:"renderer",hScrollBarAlwaysVisible:"renderer",vScrollBarAlwaysVisible:"renderer",highlightGutterLine:"renderer",animatedScroll:"renderer",showInvisibles:"renderer",showPrintMargin:"renderer",printMarginColumn:"renderer",printMargin:"renderer",fadeFoldWidgets:"renderer",showFoldWidgets:"renderer",displayIndentGuides:"renderer",highlightIndentGuides:"renderer",showGutter:"renderer",fontSize:"renderer",fontFamily:"renderer",maxLines:"renderer",minLines:"renderer",scrollPastEnd:"renderer",fixedWidthGutter:"renderer",theme:"renderer",hasCssTransforms:"renderer",maxPixelHeight:"renderer",useTextareaForIME:"renderer",useResizeObserver:"renderer",useSvgGutterIcons:"renderer",showFoldedAnnotations:"renderer",scrollSpeed:"$mouseHandler",dragDelay:"$mouseHandler",dragEnabled:"$mouseHandler",focusTimeout:"$mouseHandler",tooltipFollowsMouse:"$mouseHandler",firstLineNumber:"session",overwrite:"session",newLineMode:"session",useWorker:"session",useSoftTabs:"session",navigateWithinSoftTabs:"session",tabSize:"session",wrap:"session",indentedSoftWrap:"session",foldStyle:"session",mode:"session"});var k={getText:function(e,t){return(Math.abs(e.selection.lead.row-t)||t+1+(t<9?"\xb7":""))+""},getWidth:function(e,t,i){return Math.max(t.toString().length,(i.lastRow+1).toString().length,2)*i.characterWidth},update:function(e,t){t.renderer.$loop.schedule(t.renderer.CHANGE_GUTTER)},attach:function(e){e.renderer.$gutterLayer.$renderer=this,e.on("changeSelection",this.update),this.update(null,e)},detach:function(e){e.renderer.$gutterLayer.$renderer==this&&(e.renderer.$gutterLayer.$renderer=null),e.off("changeSelection",this.update),this.update(null,e)}};t.Editor=M}),ace.define("ace/layer/lines",["require","exports","module","ace/lib/dom"],function(e,t,i){"use strict";var n=e("../lib/dom");t.Lines=function(){function e(e,t){this.element=e,this.canvasHeight=t||5e5,this.element.style.height=2*this.canvasHeight+"px",this.cells=[],this.cellCache=[],this.$offsetCoefficient=0}return e.prototype.moveContainer=function(e){n.translate(this.element,0,-(e.firstRowScreen*e.lineHeight%this.canvasHeight)-e.offset*this.$offsetCoefficient)},e.prototype.pageChanged=function(e,t){return Math.floor(e.firstRowScreen*e.lineHeight/this.canvasHeight)!==Math.floor(t.firstRowScreen*t.lineHeight/this.canvasHeight)},e.prototype.computeLineTop=function(e,t,i){var n=Math.floor(t.firstRowScreen*t.lineHeight/this.canvasHeight);return i.documentToScreenRow(e,0)*t.lineHeight-n*this.canvasHeight},e.prototype.computeLineHeight=function(e,t,i){return t.lineHeight*i.getRowLineCount(e)},e.prototype.getLength=function(){return this.cells.length},e.prototype.get=function(e){return this.cells[e]},e.prototype.shift=function(){this.$cacheCell(this.cells.shift())},e.prototype.pop=function(){this.$cacheCell(this.cells.pop())},e.prototype.push=function(e){if(Array.isArray(e)){this.cells.push.apply(this.cells,e);for(var t=n.createFragment(this.element),i=0;i<e.length;i++)t.appendChild(e[i].element);this.element.appendChild(t)}else this.cells.push(e),this.element.appendChild(e.element)},e.prototype.unshift=function(e){if(Array.isArray(e)){this.cells.unshift.apply(this.cells,e);for(var t=n.createFragment(this.element),i=0;i<e.length;i++)t.appendChild(e[i].element);this.element.firstChild?this.element.insertBefore(t,this.element.firstChild):this.element.appendChild(t)}else this.cells.unshift(e),this.element.insertAdjacentElement("afterbegin",e.element)},e.prototype.last=function(){return this.cells.length?this.cells[this.cells.length-1]:null},e.prototype.$cacheCell=function(e){e&&(e.element.remove(),this.cellCache.push(e))},e.prototype.createCell=function(e,t,i,o){var r=this.cellCache.pop();if(!r){var s=n.createElement("div");o&&o(s),this.element.appendChild(s),r={element:s,text:"",row:e}}return r.row=e,r},e}()}),ace.define("ace/layer/gutter",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/lang","ace/lib/event_emitter","ace/layer/lines","ace/config"],function(e,t,i){"use strict";var n=e("../lib/dom"),o=e("../lib/oop"),r=e("../lib/lang"),s=e("../lib/event_emitter").EventEmitter,a=e("./lines").Lines,l=e("../config").nls,c=function(){function e(e){this.element=n.createElement("div"),this.element.className="ace_layer ace_gutter-layer",e.appendChild(this.element),this.setShowFoldWidgets(this.$showFoldWidgets),this.gutterWidth=0,this.$annotations=[],this.$updateAnnotations=this.$updateAnnotations.bind(this),this.$lines=new a(this.element),this.$lines.$offsetCoefficient=1}return e.prototype.setSession=function(e){this.session&&this.session.off("change",this.$updateAnnotations),this.session=e,e&&e.on("change",this.$updateAnnotations)},e.prototype.addGutterDecoration=function(e,t){window.console&&console.warn&&console.warn("deprecated use session.addGutterDecoration"),this.session.addGutterDecoration(e,t)},e.prototype.removeGutterDecoration=function(e,t){window.console&&console.warn&&console.warn("deprecated use session.removeGutterDecoration"),this.session.removeGutterDecoration(e,t)},e.prototype.setAnnotations=function(e){this.$annotations=[];for(var t=0;t<e.length;t++){var i=e[t],n=i.row,o=this.$annotations[n];o||(o=this.$annotations[n]={text:[],type:[],displayText:[]});var s=i.text,a=i.text,l=i.type;s=s?r.escapeHTML(s):i.html||"",a=a||i.html||"",-1===o.text.indexOf(s)&&(o.text.push(s),o.type.push(l),o.displayText.push(a));var c=i.className;c?o.className=c:"error"===l?o.className=" ace_error":"security"!==l||/\bace_error\b/.test(o.className)?"warning"!==l||/\bace_(error|security)\b/.test(o.className)?"info"!==l||o.className?"hint"!==l||o.className||(o.className=" ace_hint"):o.className=" ace_info":o.className=" ace_warning":o.className=" ace_security"}},e.prototype.$updateAnnotations=function(e){if(this.$annotations.length){var t=e.start.row,i=e.end.row-t;if(0===i);else if("remove"==e.action)this.$annotations.splice(t,i+1,null);else{var n=Array(i+1);n.unshift(t,1),this.$annotations.splice.apply(this.$annotations,n)}}},e.prototype.update=function(e){this.config=e;var t=this.session,i=e.firstRow,n=Math.min(e.lastRow+e.gutterOffset,t.getLength()-1);this.oldLastRow=n,this.config=e,this.$lines.moveContainer(e),this.$updateCursorRow();for(var o=t.getNextFoldLine(i),r=o?o.start.row:1/0,s=null,a=-1,l=i;;){if(l>r&&(l=o.end.row+1,r=(o=t.getNextFoldLine(l,o))?o.start.row:1/0),l>n){for(;this.$lines.getLength()>a+1;)this.$lines.pop();break}(s=this.$lines.get(++a))?s.row=l:(s=this.$lines.createCell(l,e,this.session,h),this.$lines.push(s)),this.$renderCell(s,e,o,l),l++}this._signal("afterRender"),this.$updateGutterWidth(e)},e.prototype.$updateGutterWidth=function(e){var t=this.session,i=t.gutterRenderer||this.$renderer,n=t.$firstLineNumber,o=this.$lines.last()?this.$lines.last().text:"";(this.$fixedWidth||t.$useWrapMode)&&(o=t.getLength()+n-1);var r=i?i.getWidth(t,o,e):o.toString().length*e.characterWidth,s=this.$padding||this.$computePadding();(r+=s.left+s.right)===this.gutterWidth||isNaN(r)||(this.gutterWidth=r,this.element.parentNode.style.width=this.element.style.width=Math.ceil(this.gutterWidth)+"px",this._signal("changeGutterWidth",r))},e.prototype.$updateCursorRow=function(){if(this.$highlightGutterLine){var e=this.session.selection.getCursor();this.$cursorRow!==e.row&&(this.$cursorRow=e.row)}},e.prototype.updateLineHighlight=function(){if(this.$highlightGutterLine){var e=this.session.selection.cursor.row;if(this.$cursorRow=e,!this.$cursorCell||this.$cursorCell.row!=e){this.$cursorCell&&(this.$cursorCell.element.className=this.$cursorCell.element.className.replace("ace_gutter-active-line ",""));var t=this.$lines.cells;this.$cursorCell=null;for(var i=0;i<t.length;i++){var n=t[i];if(n.row>=this.$cursorRow){if(n.row>this.$cursorRow){var o=this.session.getFoldLine(this.$cursorRow);if(i>0&&o&&o.start.row==t[i-1].row)n=t[i-1];else break}n.element.className="ace_gutter-active-line "+n.element.className,this.$cursorCell=n;break}}}}},e.prototype.scrollLines=function(e){var t=this.config;if(this.config=e,this.$updateCursorRow(),this.$lines.pageChanged(t,e))return this.update(e);this.$lines.moveContainer(e);var i=Math.min(e.lastRow+e.gutterOffset,this.session.getLength()-1),n=this.oldLastRow;if(this.oldLastRow=i,!t||n<e.firstRow||i<t.firstRow)return this.update(e);if(t.firstRow<e.firstRow)for(var o=this.session.getFoldedRowCount(t.firstRow,e.firstRow-1);o>0;o--)this.$lines.shift();if(n>i)for(var o=this.session.getFoldedRowCount(i+1,n);o>0;o--)this.$lines.pop();e.firstRow<t.firstRow&&this.$lines.unshift(this.$renderLines(e,e.firstRow,t.firstRow-1)),i>n&&this.$lines.push(this.$renderLines(e,n+1,i)),this.updateLineHighlight(),this._signal("afterRender"),this.$updateGutterWidth(e)},e.prototype.$renderLines=function(e,t,i){for(var n=[],o=t,r=this.session.getNextFoldLine(o),s=r?r.start.row:1/0;o>s&&(o=r.end.row+1,s=(r=this.session.getNextFoldLine(o,r))?r.start.row:1/0),!(o>i);){var a=this.$lines.createCell(o,e,this.session,h);this.$renderCell(a,e,r,o),n.push(a),o++}return n},e.prototype.$renderCell=function(e,t,i,o){var r,s=e.element,a=this.session,c=s.childNodes[0],h=s.childNodes[1],u=s.childNodes[2],d=s.childNodes[3],p=u.firstChild,g=a.$firstLineNumber,f=a.$breakpoints,m=a.$decorations,y=a.gutterRenderer||this.$renderer,v=this.$showFoldWidgets&&a.foldWidgets,w=i?i.start.row:Number.MAX_VALUE,b=t.lineHeight+"px",$=this.$useSvgGutterIcons?"ace_gutter-cell_svg-icons ":"ace_gutter-cell ",C=this.$useSvgGutterIcons?"ace_icon_svg":"ace_icon",S=(y?y.getText(a,o):o+g).toString();if(this.$highlightGutterLine&&(o==this.$cursorRow||i&&o<this.$cursorRow&&o>=w&&this.$cursorRow<=i.end.row)&&($+="ace_gutter-active-line ",this.$cursorCell!=e&&(this.$cursorCell&&(this.$cursorCell.element.className=this.$cursorCell.element.className.replace("ace_gutter-active-line ","")),this.$cursorCell=e)),f[o]&&($+=f[o]),m[o]&&($+=m[o]),this.$annotations[o]&&o!==w&&($+=this.$annotations[o].className),v){var x=v[o];null==x&&(x=v[o]=a.getFoldWidget(o))}if(x){var A="ace_fold-widget ace_"+x,M="start"==x&&o==w&&o<i.end.row;if(M){A+=" ace_closed";for(var k="",L=!1,T=o+1;T<=i.end.row;T++)if(this.$annotations[T]){if(" ace_error"===this.$annotations[T].className){L=!0,k=" ace_error_fold";break}" ace_security"===this.$annotations[T].className?(L=!0,k=" ace_security_fold"):" ace_warning"===this.$annotations[T].className&&" ace_security_fold"!==k&&(L=!0,k=" ace_warning_fold")}$+=k}else A+=" ace_open";h.className!=A&&(h.className=A),n.setStyle(h.style,"height",b),n.setStyle(h.style,"display","inline-block"),h.setAttribute("role","button"),h.setAttribute("tabindex","-1");var E=a.getFoldWidgetRange(o);E?h.setAttribute("aria-label",l("gutter.code-folding.range.aria-label","Toggle code folding, rows $0 through $1",[E.start.row+1,E.end.row+1])):i?h.setAttribute("aria-label",l("gutter.code-folding.closed.aria-label","Toggle code folding, rows $0 through $1",[i.start.row+1,i.end.row+1])):h.setAttribute("aria-label",l("gutter.code-folding.open.aria-label","Toggle code folding, row $0",[o+1])),M?(h.setAttribute("aria-expanded","false"),h.setAttribute("title",l("gutter.code-folding.closed.title","Unfold code"))):(h.setAttribute("aria-expanded","true"),h.setAttribute("title",l("gutter.code-folding.open.title","Fold code")))}else h&&(n.setStyle(h.style,"display","none"),h.setAttribute("tabindex","0"),h.removeAttribute("role"),h.removeAttribute("aria-label"));var R=this.session.$gutterCustomWidgets[o];if(R?this.$addCustomWidget(o,R,e):d&&this.$removeCustomWidget(o,e),L&&this.$showFoldedAnnotations){switch(u.className="ace_gutter_annotation",p.className=C,p.className+=k,n.setStyle(p.style,"height",b),n.setStyle(u.style,"display","block"),n.setStyle(u.style,"height",b),k){case" ace_error_fold":r=l("gutter.annotation.aria-label.error","Error, read annotations row $0",[S]);break;case" ace_security_fold":r=l("gutter.annotation.aria-label.security","Security finding, read annotations row $0",[S]);break;case" ace_warning_fold":r=l("gutter.annotation.aria-label.warning","Warning, read annotations row $0",[S])}u.setAttribute("aria-label",r),u.setAttribute("tabindex","-1"),u.setAttribute("role","button")}else if(this.$annotations[o]){switch(u.className="ace_gutter_annotation",p.className=C,this.$useSvgGutterIcons?p.className+=this.$annotations[o].className:s.classList.add(this.$annotations[o].className.replace(" ","")),n.setStyle(p.style,"height",b),n.setStyle(u.style,"display","block"),n.setStyle(u.style,"height",b),this.$annotations[o].className){case" ace_error":r=l("gutter.annotation.aria-label.error","Error, read annotations row $0",[S]);break;case" ace_security":r=l("gutter.annotation.aria-label.security","Security finding, read annotations row $0",[S]);break;case" ace_warning":r=l("gutter.annotation.aria-label.warning","Warning, read annotations row $0",[S]);break;case" ace_info":r=l("gutter.annotation.aria-label.info","Info, read annotations row $0",[S]);break;case" ace_hint":r=l("gutter.annotation.aria-label.hint","Suggestion, read annotations row $0",[S])}u.setAttribute("aria-label",r),u.setAttribute("tabindex","-1"),u.setAttribute("role","button")}else n.setStyle(u.style,"display","none"),u.removeAttribute("aria-label"),u.removeAttribute("role"),u.setAttribute("tabindex","0");return S!==c.data&&(c.data=S),s.className!=$&&(s.className=$),n.setStyle(e.element.style,"height",this.$lines.computeLineHeight(o,t,a)+"px"),n.setStyle(e.element.style,"top",this.$lines.computeLineTop(o,t,a)+"px"),e.text=S,"none"!==u.style.display||"none"!==h.style.display||R?e.element.setAttribute("aria-hidden",!1):e.element.setAttribute("aria-hidden",!0),e},e.prototype.setHighlightGutterLine=function(e){this.$highlightGutterLine=e},e.prototype.setShowLineNumbers=function(e){this.$renderer=!e&&{getWidth:function(){return 0},getText:function(){return""}}},e.prototype.getShowLineNumbers=function(){return this.$showLineNumbers},e.prototype.setShowFoldWidgets=function(e){e?n.addCssClass(this.element,"ace_folding-enabled"):n.removeCssClass(this.element,"ace_folding-enabled"),this.$showFoldWidgets=e,this.$padding=null},e.prototype.getShowFoldWidgets=function(){return this.$showFoldWidgets},e.prototype.$hideFoldWidget=function(e,t){var i=t||this.$getGutterCell(e);if(i&&i.element){var o=i.element.childNodes[1];o&&n.setStyle(o.style,"display","none")}},e.prototype.$showFoldWidget=function(e,t){var i=t||this.$getGutterCell(e);if(i&&i.element){var o=i.element.childNodes[1];o&&this.session.foldWidgets&&this.session.foldWidgets[i.row]&&n.setStyle(o.style,"display","inline-block")}},e.prototype.$getGutterCell=function(e){var t=this.$lines.cells,i=this.session.documentToScreenRow(e,0);return t[e-this.config.firstRowScreen-(e-i)]},e.prototype.$addCustomWidget=function(e,t,i){var o=t.className,r=t.label,s=t.title,a=t.callbacks;this.session.$gutterCustomWidgets[e]={className:o,label:r,title:s,callbacks:a},this.$hideFoldWidget(e,i);var l=i||this.$getGutterCell(e);if(l&&l.element){var c=l.element.querySelector(".ace_custom-widget");c&&c.remove(),(c=n.createElement("span")).className="ace_custom-widget ".concat(o),c.setAttribute("tabindex","-1"),c.setAttribute("role","button"),c.setAttribute("aria-label",r),c.setAttribute("title",s),n.setStyle(c.style,"display","inline-block"),n.setStyle(c.style,"height","inherit"),a&&a.onClick&&c.addEventListener("click",function(t){a.onClick(t,e),t.stopPropagation()}),l.element.appendChild(c)}},e.prototype.$removeCustomWidget=function(e,t){delete this.session.$gutterCustomWidgets[e],this.$showFoldWidget(e,t);var i=t||this.$getGutterCell(e);if(i&&i.element){var n=i.element.querySelector(".ace_custom-widget");n&&i.element.removeChild(n)}},e.prototype.$computePadding=function(){if(!this.element.firstChild)return{left:0,right:0};var e=n.computedStyle(this.element.firstChild);return this.$padding={},this.$padding.left=(parseInt(e.borderLeftWidth)||0)+(parseInt(e.paddingLeft)||0)+1,this.$padding.right=(parseInt(e.borderRightWidth)||0)+(parseInt(e.paddingRight)||0),this.$padding},e.prototype.getRegion=function(e){var t=this.$padding||this.$computePadding(),i=this.element.getBoundingClientRect();return e.x<t.left+i.left?"markers":this.$showFoldWidgets&&e.x>i.right-t.right?"foldWidgets":void 0},e}();function h(e){var t=document.createTextNode("");e.appendChild(t);var i=n.createElement("span");e.appendChild(i);var o=n.createElement("span");e.appendChild(o);var r=n.createElement("span");return o.appendChild(r),e}c.prototype.$fixedWidth=!1,c.prototype.$highlightGutterLine=!0,c.prototype.$renderer="",c.prototype.$showLineNumbers=!0,c.prototype.$showFoldWidgets=!0,o.implement(c.prototype,s),t.Gutter=c}),ace.define("ace/layer/marker",["require","exports","module","ace/range","ace/lib/dom"],function(e,t,i){"use strict";var n=e("../range").Range,o=e("../lib/dom"),r=function(){function e(e){this.element=o.createElement("div"),this.element.className="ace_layer ace_marker-layer",e.appendChild(this.element)}return e.prototype.setPadding=function(e){this.$padding=e},e.prototype.setSession=function(e){this.session=e},e.prototype.setMarkers=function(e){this.markers=e},e.prototype.elt=function(e,t){var i=-1!=this.i&&this.element.childNodes[this.i];i?this.i++:(i=document.createElement("div"),this.element.appendChild(i),this.i=-1),i.style.cssText=t,i.className=e},e.prototype.update=function(e){if(e){for(var t in this.config=e,this.i=0,this.markers){var i,n=this.markers[t];if(!n.range){n.update(i,this,this.session,e);continue}var o=n.range.clipRows(e.firstRow,e.lastRow);if(!o.isEmpty())if(o=o.toScreenRange(this.session),n.renderer){var r=this.$getTop(o.start.row,e),s=this.$padding+o.start.column*e.characterWidth;n.renderer(i,o,s,r,e)}else"fullLine"==n.type?this.drawFullLineMarker(i,o,n.clazz,e):"screenLine"==n.type?this.drawScreenLineMarker(i,o,n.clazz,e):o.isMultiLine()?"text"==n.type?this.drawTextMarker(i,o,n.clazz,e):this.drawMultiLineMarker(i,o,n.clazz,e):this.drawSingleLineMarker(i,o,n.clazz+" ace_start ace_br15",e)}if(-1!=this.i)for(;this.i<this.element.childElementCount;)this.element.removeChild(this.element.lastChild)}},e.prototype.$getTop=function(e,t){return(e-t.firstRowScreen)*t.lineHeight},e.prototype.drawTextMarker=function(e,t,i,o,r){for(var s,a=this.session,l=t.start.row,c=t.end.row,h=l,u=0,d=0,p=a.getScreenLastRowColumn(h),g=new n(h,t.start.column,h,d);h<=c;h++){g.start.row=g.end.row=h,g.start.column=h==l?t.start.column:a.getRowWrapIndent(h),g.end.column=p,u=d,d=p,p=h+1<c?a.getScreenLastRowColumn(h+1):h==c?0:t.end.column,this.drawSingleLineMarker(e,g,i+(h==l?" ace_start":"")+" ace_br"+(s=h==l||h==l+1&&t.start.column,!!s|2*!!(u<d)|4*!!(d>p)|8*(h==c)),o,+(h!=c),r)}},e.prototype.drawMultiLineMarker=function(e,t,i,n,o){var r=this.$padding,s=n.lineHeight,a=this.$getTop(t.start.row,n),l=r+t.start.column*n.characterWidth;if(o=o||"",this.session.$bidiHandler.isBidiRow(t.start.row)){var c=t.clone();c.end.row=c.start.row,c.end.column=this.session.getLine(c.start.row).length,this.drawBidiSingleLineMarker(e,c,i+" ace_br1 ace_start",n,null,o)}else this.elt(i+" ace_br1 ace_start","height:"+s+"px;right:"+r+"px;top:"+a+"px;left:"+l+"px;"+(o||""));if(this.session.$bidiHandler.isBidiRow(t.end.row)){var c=t.clone();c.start.row=c.end.row,c.start.column=0,this.drawBidiSingleLineMarker(e,c,i+" ace_br12",n,null,o)}else{a=this.$getTop(t.end.row,n);var h=t.end.column*n.characterWidth;this.elt(i+" ace_br12","height:"+s+"px;width:"+h+"px;top:"+a+"px;left:"+r+"px;"+(o||""))}if(!((s=(t.end.row-t.start.row-1)*n.lineHeight)<=0)){a=this.$getTop(t.start.row+1,n);var u=!!t.start.column|8*!t.end.column;this.elt(i+(u?" ace_br"+u:""),"height:"+s+"px;right:"+r+"px;top:"+a+"px;left:"+r+"px;"+(o||""))}},e.prototype.drawSingleLineMarker=function(e,t,i,n,o,r){if(this.session.$bidiHandler.isBidiRow(t.start.row))return this.drawBidiSingleLineMarker(e,t,i,n,o,r);var s=n.lineHeight,a=(t.end.column+(o||0)-t.start.column)*n.characterWidth,l=this.$getTop(t.start.row,n),c=this.$padding+t.start.column*n.characterWidth;this.elt(i,"height:"+s+"px;width:"+a+"px;top:"+l+"px;left:"+c+"px;"+(r||""))},e.prototype.drawBidiSingleLineMarker=function(e,t,i,n,o,r){var s=n.lineHeight,a=this.$getTop(t.start.row,n),l=this.$padding;this.session.$bidiHandler.getSelections(t.start.column,t.end.column).forEach(function(e){this.elt(i,"height:"+s+"px;width:"+(e.width+(o||0))+"px;top:"+a+"px;left:"+(l+e.left)+"px;"+(r||""))},this)},e.prototype.drawFullLineMarker=function(e,t,i,n,o){var r=this.$getTop(t.start.row,n),s=n.lineHeight;t.start.row!=t.end.row&&(s+=this.$getTop(t.end.row,n)-r),this.elt(i,"height:"+s+"px;top:"+r+"px;left:0;right:0;"+(o||""))},e.prototype.drawScreenLineMarker=function(e,t,i,n,o){var r=this.$getTop(t.start.row,n),s=n.lineHeight;this.elt(i,"height:"+s+"px;top:"+r+"px;left:0;right:0;"+(o||""))},e}();r.prototype.$padding=0,t.Marker=r}),ace.define("ace/layer/text_util",["require","exports","module"],function(e,t,i){var n=new Set(["text","rparen","lparen"]);t.isTextToken=function(e){return n.has(e)}}),ace.define("ace/layer/text",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/layer/lines","ace/lib/event_emitter","ace/config","ace/layer/text_util"],function(e,t,i){"use strict";var n=e("../lib/oop"),o=e("../lib/dom"),r=e("../lib/lang"),s=e("./lines").Lines,a=e("../lib/event_emitter").EventEmitter,l=e("../config").nls,c=e("./text_util").isTextToken,h=function(){function e(e){this.dom=o,this.element=this.dom.createElement("div"),this.element.className="ace_layer ace_text-layer",e.appendChild(this.element),this.$updateEolChar=this.$updateEolChar.bind(this),this.$lines=new s(this.element)}return e.prototype.$updateEolChar=function(){var e=this.session.doc,t="\n"==e.getNewLineCharacter()&&"windows"!=e.getNewLineMode()?this.EOL_CHAR_LF:this.EOL_CHAR_CRLF;if(this.EOL_CHAR!=t)return this.EOL_CHAR=t,!0},e.prototype.setPadding=function(e){this.$padding=e,this.element.style.margin="0 "+e+"px"},e.prototype.getLineHeight=function(){return this.$fontMetrics.$characterSize.height||0},e.prototype.getCharacterWidth=function(){return this.$fontMetrics.$characterSize.width||0},e.prototype.$setFontMetrics=function(e){this.$fontMetrics=e,this.$fontMetrics.on("changeCharacterSize",(function(e){this._signal("changeCharacterSize",e)}).bind(this)),this.$pollSizeChanges()},e.prototype.checkForSizeChanges=function(){this.$fontMetrics.checkForSizeChanges()},e.prototype.$pollSizeChanges=function(){return this.$pollSizeChangesTimer=this.$fontMetrics.$pollSizeChanges()},e.prototype.setSession=function(e){this.session=e,e&&this.$computeTabString()},e.prototype.setShowInvisibles=function(e){return this.showInvisibles!=e&&(this.showInvisibles=e,"string"==typeof e?(this.showSpaces=/tab/i.test(e),this.showTabs=/space/i.test(e),this.showEOL=/eol/i.test(e)):this.showSpaces=this.showTabs=this.showEOL=e,this.$computeTabString(),!0)},e.prototype.setDisplayIndentGuides=function(e){return this.displayIndentGuides!=e&&(this.displayIndentGuides=e,this.$computeTabString(),!0)},e.prototype.setHighlightIndentGuides=function(e){return this.$highlightIndentGuides!==e&&(this.$highlightIndentGuides=e,e)},e.prototype.$computeTabString=function(){var e=this.session.getTabSize();this.tabSize=e;for(var t=this.$tabStrings=[0],i=1;i<e+1;i++)if(this.showTabs){var n=this.dom.createElement("span");n.className="ace_invisible ace_invisible_tab",n.textContent=r.stringRepeat(this.TAB_CHAR,i),t.push(n)}else t.push(this.dom.createTextNode(r.stringRepeat(" ",i),this.element));if(this.displayIndentGuides){this.$indentGuideRe=/\s\S| \t|\t |\s$/;var o="ace_indent-guide",s=this.showSpaces?" ace_invisible ace_invisible_space":"",a=this.showSpaces?r.stringRepeat(this.SPACE_CHAR,this.tabSize):r.stringRepeat(" ",this.tabSize),l=this.showTabs?" ace_invisible ace_invisible_tab":"",c=this.showTabs?r.stringRepeat(this.TAB_CHAR,this.tabSize):a,n=this.dom.createElement("span");n.className=o+s,n.textContent=a,this.$tabStrings[" "]=n;var n=this.dom.createElement("span");n.className=o+l,n.textContent=c,this.$tabStrings["	"]=n}},e.prototype.updateLines=function(e,t,i){if(this.config.lastRow!=e.lastRow||this.config.firstRow!=e.firstRow)return this.update(e);this.config=e;for(var n=Math.max(t,e.firstRow),o=Math.min(i,e.lastRow),r=this.element.childNodes,s=0,a=e.firstRow;a<n;a++){var l=this.session.getFoldLine(a);if(l)if(l.containsRow(n)){n=l.start.row;break}else a=l.end.row;s++}for(var c=!1,a=n,l=this.session.getNextFoldLine(a),h=l?l.start.row:1/0;a>h&&(a=l.end.row+1,h=(l=this.session.getNextFoldLine(a,l))?l.start.row:1/0),!(a>o);){var u=r[s++];if(u){this.dom.removeChildren(u),this.$renderLine(u,a,a==h&&l),c&&(u.style.top=this.$lines.computeLineTop(a,e,this.session)+"px");var d=e.lineHeight*this.session.getRowLength(a)+"px";u.style.height!=d&&(c=!0,u.style.height=d)}a++}if(c)for(;s<this.$lines.cells.length;){var p=this.$lines.cells[s++];p.element.style.top=this.$lines.computeLineTop(p.row,e,this.session)+"px"}},e.prototype.scrollLines=function(e){var t=this.config;if(this.config=e,this.$lines.pageChanged(t,e))return this.update(e);this.$lines.moveContainer(e);var i=e.lastRow,n=t?t.lastRow:-1;if(!t||n<e.firstRow||i<t.firstRow||!t||t.lastRow<e.firstRow||e.lastRow<t.firstRow)return this.update(e);if(t.firstRow<e.firstRow)for(var o=this.session.getFoldedRowCount(t.firstRow,e.firstRow-1);o>0;o--)this.$lines.shift();if(t.lastRow>e.lastRow)for(var o=this.session.getFoldedRowCount(e.lastRow+1,t.lastRow);o>0;o--)this.$lines.pop();e.firstRow<t.firstRow&&this.$lines.unshift(this.$renderLinesFragment(e,e.firstRow,t.firstRow-1)),e.lastRow>t.lastRow&&this.$lines.push(this.$renderLinesFragment(e,t.lastRow+1,e.lastRow)),this.$highlightIndentGuide()},e.prototype.$renderLinesFragment=function(e,t,i){for(var n=[],r=t,s=this.session.getNextFoldLine(r),a=s?s.start.row:1/0;r>a&&(r=s.end.row+1,a=(s=this.session.getNextFoldLine(r,s))?s.start.row:1/0),!(r>i);){var l=this.$lines.createCell(r,e,this.session),c=l.element;this.dom.removeChildren(c),o.setStyle(c.style,"height",this.$lines.computeLineHeight(r,e,this.session)+"px"),o.setStyle(c.style,"top",this.$lines.computeLineTop(r,e,this.session)+"px"),this.$renderLine(c,r,r==a&&s),this.$useLineGroups()?c.className="ace_line_group":c.className="ace_line",n.push(l),r++}return n},e.prototype.update=function(e){this.$lines.moveContainer(e),this.config=e;for(var t=e.firstRow,i=e.lastRow,n=this.$lines;n.getLength();)n.pop();n.push(this.$renderLinesFragment(e,t,i))},e.prototype.$renderToken=function(e,t,i,n){for(var o,s=/(\t)|( +)|([\x00-\x1f\x80-\xa0\xad\u1680\u180E\u2000-\u200f\u2028\u2029\u202F\u205F\uFEFF\uFFF9-\uFFFC\u2066\u2067\u2068\u202A\u202B\u202D\u202E\u202C\u2069\u2060\u2061\u2062\u2063\u2064\u206A\u206B\u206B\u206C\u206D\u206E\u206F]+)|(\u3000)|([\u1100-\u115F\u11A3-\u11A7\u11FA-\u11FF\u2329-\u232A\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFB\u3001-\u303E\u3041-\u3096\u3099-\u30FF\u3105-\u312D\u3131-\u318E\u3190-\u31BA\u31C0-\u31E3\u31F0-\u321E\u3220-\u3247\u3250-\u32FE\u3300-\u4DBF\u4E00-\uA48C\uA490-\uA4C6\uA960-\uA97C\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFAFF\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE66\uFE68-\uFE6B\uFF01-\uFF60\uFFE0-\uFFE6]|[\uD800-\uDBFF][\uDC00-\uDFFF])/g,a=this.dom.createFragment(this.element),h=0;o=s.exec(n);){var u=o[1],d=o[2],p=o[3],g=o[4],f=o[5];if(this.showSpaces||!d){var m=h!=o.index?n.slice(h,o.index):"";if(h=o.index+o[0].length,m&&a.appendChild(this.dom.createTextNode(m,this.element)),u){var y=this.session.getScreenTabSize(t+o.index);a.appendChild(this.$tabStrings[y].cloneNode(!0)),t+=y-1}else if(d)if(this.showSpaces){var v=this.dom.createElement("span");v.className="ace_invisible ace_invisible_space",v.textContent=r.stringRepeat(this.SPACE_CHAR,d.length),a.appendChild(v)}else a.appendChild(this.dom.createTextNode(d,this.element));else if(p){var v=this.dom.createElement("span");v.className="ace_invisible ace_invisible_space ace_invalid",v.textContent=r.stringRepeat(this.SPACE_CHAR,p.length),a.appendChild(v)}else if(g){t+=1;var v=this.dom.createElement("span");v.style.width=2*this.config.characterWidth+"px",v.className=this.showSpaces?"ace_cjk ace_invisible ace_invisible_space":"ace_cjk",v.textContent=this.showSpaces?this.SPACE_CHAR:g,a.appendChild(v)}else if(f){t+=1;var v=this.dom.createElement("span");v.style.width=2*this.config.characterWidth+"px",v.className="ace_cjk",v.textContent=f,a.appendChild(v)}}}if(a.appendChild(this.dom.createTextNode(h?n.slice(h):n,this.element)),c(i.type))e.appendChild(a);else{var w="ace_"+i.type.replace(/\./g," ace_"),v=this.dom.createElement("span");"fold"==i.type&&(v.style.width=i.value.length*this.config.characterWidth+"px",v.setAttribute("title",l("inline-fold.closed.title","Unfold code"))),v.className=w,v.appendChild(a),e.appendChild(v)}return t+n.length},e.prototype.renderIndentGuide=function(e,t,i){var n=t.search(this.$indentGuideRe);if(n<=0||n>=i)return t;if(" "==t[0]){for(var o=(n-=n%this.tabSize)/this.tabSize,r=0;r<o;r++)e.appendChild(this.$tabStrings[" "].cloneNode(!0));return this.$highlightIndentGuide(),t.substr(n)}if("	"==t[0]){for(var r=0;r<n;r++)e.appendChild(this.$tabStrings["	"].cloneNode(!0));return this.$highlightIndentGuide(),t.substr(n)}return this.$highlightIndentGuide(),t},e.prototype.$highlightIndentGuide=function(){if(this.$highlightIndentGuides&&this.displayIndentGuides){this.$highlightIndentGuideMarker={indentLevel:void 0,start:void 0,end:void 0,dir:void 0};var e=this.session.doc.$lines;if(e){var t=this.session.selection.getCursor(),i=/^\s*/.exec(this.session.doc.getLine(t.row))[0].length,n=Math.floor(i/this.tabSize);if(this.$highlightIndentGuideMarker={indentLevel:n,start:t.row},this.session.$bracketHighlight){for(var o=this.session.$bracketHighlight.ranges,r=0;r<o.length;r++)if(t.row!==o[r].start.row){this.$highlightIndentGuideMarker.end=o[r].start.row+1,t.row>o[r].start.row?this.$highlightIndentGuideMarker.dir=-1:this.$highlightIndentGuideMarker.dir=1;break}}if(!this.$highlightIndentGuideMarker.end&&""!==e[t.row]&&t.column===e[t.row].length){this.$highlightIndentGuideMarker.dir=1;for(var r=t.row+1;r<e.length;r++){var s=e[r],a=/^\s*/.exec(s)[0].length;if(""!==s&&(this.$highlightIndentGuideMarker.end=r,a<=i))break}}this.$renderHighlightIndentGuide()}}},e.prototype.$clearActiveIndentGuide=function(){for(var e=this.element.querySelectorAll(".ace_indent-guide-active"),t=0;t<e.length;t++)e[t].classList.remove("ace_indent-guide-active")},e.prototype.$setIndentGuideActive=function(e,t){if(""!==this.session.doc.getLine(e.row)){var i=e.element;if(e.element.classList&&e.element.classList.contains("ace_line_group"))if(!(e.element.childNodes.length>0))return;else i=e.element.childNodes[0];var n=i.childNodes;if(n){var o=n[t-1];o&&o.classList&&o.classList.contains("ace_indent-guide")&&o.classList.add("ace_indent-guide-active")}}},e.prototype.$renderHighlightIndentGuide=function(){if(this.$lines){var e=this.$lines.cells;this.$clearActiveIndentGuide();var t=this.$highlightIndentGuideMarker.indentLevel;if(0!==t)if(1===this.$highlightIndentGuideMarker.dir)for(var i=0;i<e.length;i++){var n=e[i];if(this.$highlightIndentGuideMarker.end&&n.row>=this.$highlightIndentGuideMarker.start+1){if(n.row>=this.$highlightIndentGuideMarker.end)break;this.$setIndentGuideActive(n,t)}}else for(var i=e.length-1;i>=0;i--){var n=e[i];if(this.$highlightIndentGuideMarker.end&&n.row<this.$highlightIndentGuideMarker.start){if(n.row<this.$highlightIndentGuideMarker.end)break;this.$setIndentGuideActive(n,t)}}}},e.prototype.$createLineElement=function(e){var t=this.dom.createElement("div");return t.className="ace_line",t.style.height=this.config.lineHeight+"px",t},e.prototype.$renderWrappedLine=function(e,t,i){var n=0,o=0,s=i[0],a=0,l=this.$createLineElement();e.appendChild(l);for(var c=0;c<t.length;c++){var h=t[c],u=h.value;if(0==c&&this.displayIndentGuides){if(n=u.length,!(u=this.renderIndentGuide(l,u,s)))continue;n-=u.length}if(n+u.length<s)a=this.$renderToken(l,a,h,u),n+=u.length;else{for(;n+u.length>=s;)a=this.$renderToken(l,a,h,u.substring(0,s-n)),u=u.substring(s-n),n=s,l=this.$createLineElement(),e.appendChild(l),l.appendChild(this.dom.createTextNode(r.stringRepeat("\xa0",i.indent),this.element)),a=0,s=i[++o]||Number.MAX_VALUE;0!=u.length&&(n+=u.length,a=this.$renderToken(l,a,h,u))}}i[i.length-1]>this.MAX_LINE_LENGTH&&this.$renderOverflowMessage(l,a,null,"",!0)},e.prototype.$renderSimpleLine=function(e,t){for(var i=0,n=0;n<t.length;n++){var o=t[n],r=o.value;if(0!=n||!this.displayIndentGuides||(r=this.renderIndentGuide(e,r))){if(i+r.length>this.MAX_LINE_LENGTH)return this.$renderOverflowMessage(e,i,o,r);i=this.$renderToken(e,i,o,r)}}},e.prototype.$renderOverflowMessage=function(e,t,i,n,o){i&&this.$renderToken(e,t,i,n.slice(0,this.MAX_LINE_LENGTH-t));var r=this.dom.createElement("span");r.className="ace_inline_button ace_keyword ace_toggle_wrap",r.textContent=o?"<hide>":"<click to see more...>",e.appendChild(r)},e.prototype.$renderLine=function(e,t,i){if(i||!1==i||(i=this.session.getFoldLine(t)),i)var n=this.$getFoldLineTokens(t,i);else var n=this.session.getTokens(t);var o=e;if(n.length){var r=this.session.getRowSplitData(t);if(r&&r.length){this.$renderWrappedLine(e,n,r);var o=e.lastChild}else{var o=e;this.$useLineGroups()&&(o=this.$createLineElement(),e.appendChild(o)),this.$renderSimpleLine(o,n)}}else this.$useLineGroups()&&(o=this.$createLineElement(),e.appendChild(o));if(this.showEOL&&o){i&&(t=i.end.row);var s=this.dom.createElement("span");s.className="ace_invisible ace_invisible_eol",s.textContent=t==this.session.getLength()-1?this.EOF_CHAR:this.EOL_CHAR,o.appendChild(s)}},e.prototype.$getFoldLineTokens=function(e,t){var i=this.session,n=[],o=i.getTokens(e);return t.walk(function(e,t,r,s,a){null!=e?n.push({type:"fold",value:e}):(a&&(o=i.getTokens(t)),o.length&&function(e,t,i){for(var o=0,r=0;r+e[o].value.length<t;)if(r+=e[o].value.length,++o==e.length)return;if(r!=t){var s=e[o].value.substring(t-r);s.length>i-t&&(s=s.substring(0,i-t)),n.push({type:e[o].type,value:s}),r=t+s.length,o+=1}for(;r<i&&o<e.length;){var s=e[o].value;s.length+r>i?n.push({type:e[o].type,value:s.substring(0,i-r)}):n.push(e[o]),r+=s.length,o+=1}}(o,s,r))},t.end.row,this.session.getLine(t.end.row).length),n},e.prototype.$useLineGroups=function(){return this.session.getUseWrapMode()},e}();h.prototype.EOF_CHAR="\xb6",h.prototype.EOL_CHAR_LF="\xac",h.prototype.EOL_CHAR_CRLF="\xa4",h.prototype.EOL_CHAR=h.prototype.EOL_CHAR_LF,h.prototype.TAB_CHAR="—",h.prototype.SPACE_CHAR="\xb7",h.prototype.$padding=0,h.prototype.MAX_LINE_LENGTH=1e4,h.prototype.showInvisibles=!1,h.prototype.showSpaces=!1,h.prototype.showTabs=!1,h.prototype.showEOL=!1,h.prototype.displayIndentGuides=!0,h.prototype.$highlightIndentGuides=!0,h.prototype.$tabStrings=[],h.prototype.destroy={},h.prototype.onChangeTabSize=h.prototype.$computeTabString,n.implement(h.prototype,a),t.Text=h}),ace.define("ace/layer/cursor",["require","exports","module","ace/lib/dom"],function(e,t,i){"use strict";var n=e("../lib/dom"),o=function(){function e(e){this.element=n.createElement("div"),this.element.className="ace_layer ace_cursor-layer",e.appendChild(this.element),this.isVisible=!1,this.isBlinking=!0,this.blinkInterval=1e3,this.smoothBlinking=!1,this.cursors=[],this.cursor=this.addCursor(),n.addCssClass(this.element,"ace_hidden-cursors"),this.$updateCursors=this.$updateOpacity.bind(this)}return e.prototype.$updateOpacity=function(e){for(var t=this.cursors,i=t.length;i--;)n.setStyle(t[i].style,"opacity",e?"":"0")},e.prototype.$startCssAnimation=function(){for(var e=this.cursors,t=e.length;t--;)e[t].style.animationDuration=this.blinkInterval+"ms";this.$isAnimating=!0,setTimeout((function(){this.$isAnimating&&n.addCssClass(this.element,"ace_animate-blinking")}).bind(this))},e.prototype.$stopCssAnimation=function(){this.$isAnimating=!1,n.removeCssClass(this.element,"ace_animate-blinking")},e.prototype.setPadding=function(e){this.$padding=e},e.prototype.setSession=function(e){this.session=e},e.prototype.setBlinking=function(e){e!=this.isBlinking&&(this.isBlinking=e,this.restartTimer())},e.prototype.setBlinkInterval=function(e){e!=this.blinkInterval&&(this.blinkInterval=e,this.restartTimer())},e.prototype.setSmoothBlinking=function(e){e!=this.smoothBlinking&&(this.smoothBlinking=e,n.setCssClass(this.element,"ace_smooth-blinking",e),this.$updateCursors(!0),this.restartTimer())},e.prototype.addCursor=function(){var e=n.createElement("div");return e.className="ace_cursor",this.element.appendChild(e),this.cursors.push(e),e},e.prototype.removeCursor=function(){if(this.cursors.length>1){var e=this.cursors.pop();return e.parentNode.removeChild(e),e}},e.prototype.hideCursor=function(){this.isVisible=!1,n.addCssClass(this.element,"ace_hidden-cursors"),this.restartTimer()},e.prototype.showCursor=function(){this.isVisible=!0,n.removeCssClass(this.element,"ace_hidden-cursors"),this.restartTimer()},e.prototype.restartTimer=function(){var e=this.$updateCursors;if(clearInterval(this.intervalId),clearTimeout(this.timeoutId),this.$stopCssAnimation(),this.smoothBlinking&&(this.$isSmoothBlinking=!1,n.removeCssClass(this.element,"ace_smooth-blinking")),e(!0),!this.isBlinking||!this.blinkInterval||!this.isVisible)return void this.$stopCssAnimation();if(this.smoothBlinking&&(this.$isSmoothBlinking=!0,setTimeout((function(){this.$isSmoothBlinking&&n.addCssClass(this.element,"ace_smooth-blinking")}).bind(this))),n.HAS_CSS_ANIMATION)this.$startCssAnimation();else{var t=(function(){this.timeoutId=setTimeout(function(){e(!1)},.6*this.blinkInterval)}).bind(this);this.intervalId=setInterval(function(){e(!0),t()},this.blinkInterval),t()}},e.prototype.getPixelPosition=function(e,t){if(!this.config||!this.session)return{left:0,top:0};e||(e=this.session.selection.getCursor());var i=this.session.documentToScreenPosition(e);return{left:this.$padding+(this.session.$bidiHandler.isBidiRow(i.row,e.row)?this.session.$bidiHandler.getPosLeft(i.column):i.column*this.config.characterWidth),top:(i.row-(t?this.config.firstRowScreen:0))*this.config.lineHeight}},e.prototype.isCursorInView=function(e,t){return e.top>=0&&e.top<t.maxHeight},e.prototype.update=function(e){this.config=e;var t=this.session.$selectionMarkers,i=0,o=0;(void 0===t||0===t.length)&&(t=[{cursor:null}]);for(var i=0,r=t.length;i<r;i++){var s=this.getPixelPosition(t[i].cursor,!0);if(!(s.top>e.height+e.offset)&&!(s.top<0)||!(i>1)){var a=this.cursors[o++]||this.addCursor(),l=a.style;this.drawCursor?this.drawCursor(a,s,e,t[i],this.session):this.isCursorInView(s,e)?(n.setStyle(l,"display","block"),n.translate(a,s.left,s.top),n.setStyle(l,"width",Math.round(e.characterWidth)+"px"),n.setStyle(l,"height",e.lineHeight+"px")):n.setStyle(l,"display","none")}}for(;this.cursors.length>o;)this.removeCursor();var c=this.session.getOverwrite();this.$setOverwrite(c),this.$pixelPos=s,this.restartTimer()},e.prototype.$setOverwrite=function(e){e!=this.overwrite&&(this.overwrite=e,e?n.addCssClass(this.element,"ace_overwrite-cursors"):n.removeCssClass(this.element,"ace_overwrite-cursors"))},e.prototype.destroy=function(){clearInterval(this.intervalId),clearTimeout(this.timeoutId)},e}();o.prototype.$padding=0,o.prototype.drawCursor=null,t.Cursor=o}),ace.define("ace/scrollbar",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/event","ace/lib/event_emitter"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=e("./lib/oop"),s=e("./lib/dom"),a=e("./lib/event"),l=e("./lib/event_emitter").EventEmitter,c=function(){function e(e,t){this.element=s.createElement("div"),this.element.className="ace_scrollbar ace_scrollbar"+t,this.inner=s.createElement("div"),this.inner.className="ace_scrollbar-inner",this.inner.textContent="\xa0",this.element.appendChild(this.inner),e.appendChild(this.element),this.setVisible(!1),this.skipEvent=!1,a.addListener(this.element,"scroll",this.onScroll.bind(this)),a.addListener(this.element,"mousedown",a.preventDefault)}return e.prototype.setVisible=function(e){this.element.style.display=e?"":"none",this.isVisible=e,this.coeff=1},e}();r.implement(c.prototype,l);var h=function(e){function t(t,i){var n=e.call(this,t,"-v")||this;return n.scrollTop=0,n.scrollHeight=0,i.$scrollbarWidth=n.width=s.scrollbarWidth(t.ownerDocument),n.inner.style.width=n.element.style.width=(n.width||15)+5+"px",n.$minWidth=0,n}return o(t,e),t.prototype.onScroll=function(){if(!this.skipEvent){if(this.scrollTop=this.element.scrollTop,1!=this.coeff){var e=this.element.clientHeight/this.scrollHeight;this.scrollTop=this.scrollTop*(1-e)/(this.coeff-e)}this._emit("scroll",{data:this.scrollTop})}this.skipEvent=!1},t.prototype.getWidth=function(){return Math.max(this.isVisible?this.width:0,this.$minWidth||0)},t.prototype.setHeight=function(e){this.element.style.height=e+"px"},t.prototype.setScrollHeight=function(e){this.scrollHeight=e,e>32768?(this.coeff=32768/e,e=32768):1!=this.coeff&&(this.coeff=1),this.inner.style.height=e+"px"},t.prototype.setScrollTop=function(e){this.scrollTop!=e&&(this.skipEvent=!0,this.scrollTop=e,this.element.scrollTop=e*this.coeff)},t}(c);h.prototype.setInnerHeight=h.prototype.setScrollHeight;var u=function(e){function t(t,i){var n=e.call(this,t,"-h")||this;return n.scrollLeft=0,n.height=i.$scrollbarWidth,n.inner.style.height=n.element.style.height=(n.height||15)+5+"px",n}return o(t,e),t.prototype.onScroll=function(){this.skipEvent||(this.scrollLeft=this.element.scrollLeft,this._emit("scroll",{data:this.scrollLeft})),this.skipEvent=!1},t.prototype.getHeight=function(){return this.isVisible?this.height:0},t.prototype.setWidth=function(e){this.element.style.width=e+"px"},t.prototype.setInnerWidth=function(e){this.inner.style.width=e+"px"},t.prototype.setScrollWidth=function(e){this.inner.style.width=e+"px"},t.prototype.setScrollLeft=function(e){this.scrollLeft!=e&&(this.skipEvent=!0,this.scrollLeft=this.element.scrollLeft=e)},t}(c);t.ScrollBar=h,t.ScrollBarV=h,t.ScrollBarH=u,t.VScrollBar=h,t.HScrollBar=u}),ace.define("ace/scrollbar_custom",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/event","ace/lib/event_emitter"],function(e,t,i){"use strict";var n,o=this&&this.__extends||(n=function(e,t){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])})(e,t)},function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}n(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}),r=e("./lib/oop"),s=e("./lib/dom"),a=e("./lib/event"),l=e("./lib/event_emitter").EventEmitter;s.importCssString(".ace_editor>.ace_sb-v div, .ace_editor>.ace_sb-h div{\n  position: absolute;\n  background: rgba(128, 128, 128, 0.6);\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n  border: 1px solid #bbb;\n  border-radius: 2px;\n  z-index: 8;\n}\n.ace_editor>.ace_sb-v, .ace_editor>.ace_sb-h {\n  position: absolute;\n  z-index: 6;\n  background: none;\n  overflow: hidden!important;\n}\n.ace_editor>.ace_sb-v {\n  z-index: 6;\n  right: 0;\n  top: 0;\n  width: 12px;\n}\n.ace_editor>.ace_sb-v div {\n  z-index: 8;\n  right: 0;\n  width: 100%;\n}\n.ace_editor>.ace_sb-h {\n  bottom: 0;\n  left: 0;\n  height: 12px;\n}\n.ace_editor>.ace_sb-h div {\n  bottom: 0;\n  height: 100%;\n}\n.ace_editor>.ace_sb_grabbed {\n  z-index: 8;\n  background: #000;\n}","ace_scrollbar.css",!1);var c=function(){function e(e,t){this.element=s.createElement("div"),this.element.className="ace_sb"+t,this.inner=s.createElement("div"),this.inner.className="",this.element.appendChild(this.inner),this.VScrollWidth=12,this.HScrollHeight=12,e.appendChild(this.element),this.setVisible(!1),this.skipEvent=!1,a.addMultiMouseDownListener(this.element,[500,300,300],this,"onMouseDown")}return e.prototype.setVisible=function(e){this.element.style.display=e?"":"none",this.isVisible=e,this.coeff=1},e}();r.implement(c.prototype,l);var h=function(e){function t(t,i){var n=e.call(this,t,"-v")||this;return n.scrollTop=0,n.scrollHeight=0,n.parent=t,n.width=n.VScrollWidth,n.renderer=i,n.inner.style.width=n.element.style.width=(n.width||15)+"px",n.$minWidth=0,n}return o(t,e),t.prototype.onMouseDown=function(e,t){if("mousedown"===e&&0===a.getButton(t)&&2!==t.detail){if(t.target===this.inner){var i=this,n=t.clientY,o=t.clientY,r=this.thumbTop;a.capture(this.inner,function(e){n=e.clientY},function(){clearInterval(s)});var s=setInterval(function(){if(void 0!==n){var e=i.scrollTopFromThumbTop(r+n-o);e!==i.scrollTop&&i._emit("scroll",{data:e})}},20);return a.preventDefault(t)}var l=t.clientY-this.element.getBoundingClientRect().top-this.thumbHeight/2;return this._emit("scroll",{data:this.scrollTopFromThumbTop(l)}),a.preventDefault(t)}},t.prototype.getHeight=function(){return this.height},t.prototype.scrollTopFromThumbTop=function(e){var t=e*(this.pageHeight-this.viewHeight)/(this.slideHeight-this.thumbHeight);return(t|=0)<0?t=0:t>this.pageHeight-this.viewHeight&&(t=this.pageHeight-this.viewHeight),t},t.prototype.getWidth=function(){return Math.max(this.isVisible?this.width:0,this.$minWidth||0)},t.prototype.setHeight=function(e){this.height=Math.max(0,e),this.slideHeight=this.height,this.viewHeight=this.height,this.setScrollHeight(this.pageHeight,!0)},t.prototype.setScrollHeight=function(e,t){(this.pageHeight!==e||t)&&(this.pageHeight=e,this.thumbHeight=this.slideHeight*this.viewHeight/this.pageHeight,this.thumbHeight>this.slideHeight&&(this.thumbHeight=this.slideHeight),this.thumbHeight<15&&(this.thumbHeight=15),this.inner.style.height=this.thumbHeight+"px",this.scrollTop>this.pageHeight-this.viewHeight&&(this.scrollTop=this.pageHeight-this.viewHeight,this.scrollTop<0&&(this.scrollTop=0),this._emit("scroll",{data:this.scrollTop})))},t.prototype.setScrollTop=function(e){this.scrollTop=e,e<0&&(e=0),this.thumbTop=e*(this.slideHeight-this.thumbHeight)/(this.pageHeight-this.viewHeight),this.inner.style.top=this.thumbTop+"px"},t}(c);h.prototype.setInnerHeight=h.prototype.setScrollHeight;var u=function(e){function t(t,i){var n=e.call(this,t,"-h")||this;return n.scrollLeft=0,n.scrollWidth=0,n.height=n.HScrollHeight,n.inner.style.height=n.element.style.height=(n.height||12)+"px",n.renderer=i,n}return o(t,e),t.prototype.onMouseDown=function(e,t){if("mousedown"===e&&0===a.getButton(t)&&2!==t.detail){if(t.target===this.inner){var i=this,n=t.clientX,o=t.clientX,r=this.thumbLeft;a.capture(this.inner,function(e){n=e.clientX},function(){clearInterval(s)});var s=setInterval(function(){if(void 0!==n){var e=i.scrollLeftFromThumbLeft(r+n-o);e!==i.scrollLeft&&i._emit("scroll",{data:e})}},20);return a.preventDefault(t)}var l=t.clientX-this.element.getBoundingClientRect().left-this.thumbWidth/2;return this._emit("scroll",{data:this.scrollLeftFromThumbLeft(l)}),a.preventDefault(t)}},t.prototype.getHeight=function(){return this.isVisible?this.height:0},t.prototype.scrollLeftFromThumbLeft=function(e){var t=e*(this.pageWidth-this.viewWidth)/(this.slideWidth-this.thumbWidth);return(t|=0)<0?t=0:t>this.pageWidth-this.viewWidth&&(t=this.pageWidth-this.viewWidth),t},t.prototype.setWidth=function(e){this.width=Math.max(0,e),this.element.style.width=this.width+"px",this.slideWidth=this.width,this.viewWidth=this.width,this.setScrollWidth(this.pageWidth,!0)},t.prototype.setScrollWidth=function(e,t){(this.pageWidth!==e||t)&&(this.pageWidth=e,this.thumbWidth=this.slideWidth*this.viewWidth/this.pageWidth,this.thumbWidth>this.slideWidth&&(this.thumbWidth=this.slideWidth),this.thumbWidth<15&&(this.thumbWidth=15),this.inner.style.width=this.thumbWidth+"px",this.scrollLeft>this.pageWidth-this.viewWidth&&(this.scrollLeft=this.pageWidth-this.viewWidth,this.scrollLeft<0&&(this.scrollLeft=0),this._emit("scroll",{data:this.scrollLeft})))},t.prototype.setScrollLeft=function(e){this.scrollLeft=e,e<0&&(e=0),this.thumbLeft=e*(this.slideWidth-this.thumbWidth)/(this.pageWidth-this.viewWidth),this.inner.style.left=this.thumbLeft+"px"},t}(c);u.prototype.setInnerWidth=u.prototype.setScrollWidth,t.ScrollBar=h,t.ScrollBarV=h,t.ScrollBarH=u,t.VScrollBar=h,t.HScrollBar=u}),ace.define("ace/renderloop",["require","exports","module","ace/lib/event"],function(e,t,i){"use strict";var n=e("./lib/event");t.RenderLoop=function(){function e(e,t){this.onRender=e,this.pending=!1,this.changes=0,this.$recursionLimit=2,this.window=t||window;var i=this;this._flush=function(e){i.pending=!1;var t=i.changes;if(t&&(n.blockIdle(100),i.changes=0,i.onRender(t)),i.changes){if(i.$recursionLimit--<0)return;i.schedule()}else i.$recursionLimit=2}}return e.prototype.schedule=function(e){this.changes=this.changes|e,this.changes&&!this.pending&&(n.nextFrame(this._flush),this.pending=!0)},e.prototype.clear=function(e){var t=this.changes;return this.changes=0,t},e}()}),ace.define("ace/layer/font_metrics",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/lib/event","ace/lib/useragent","ace/lib/event_emitter"],function(e,t,i){var n=e("../lib/oop"),o=e("../lib/dom"),r=e("../lib/lang"),s=e("../lib/event"),a=e("../lib/useragent"),l=e("../lib/event_emitter").EventEmitter,c="function"==typeof ResizeObserver,h=function(){function e(e){this.el=o.createElement("div"),this.$setMeasureNodeStyles(this.el.style,!0),this.$main=o.createElement("div"),this.$setMeasureNodeStyles(this.$main.style),this.$measureNode=o.createElement("div"),this.$setMeasureNodeStyles(this.$measureNode.style),this.el.appendChild(this.$main),this.el.appendChild(this.$measureNode),e.appendChild(this.el),this.$measureNode.textContent=r.stringRepeat("X",512),this.$characterSize={width:0,height:0},c?this.$addObserver():this.checkForSizeChanges()}return e.prototype.$setMeasureNodeStyles=function(e,t){e.width=e.height="auto",e.left=e.top="0px",e.visibility="hidden",e.position="absolute",e.whiteSpace="pre",a.isIE<8?e["font-family"]="inherit":e.font="inherit",e.overflow=t?"hidden":"visible"},e.prototype.checkForSizeChanges=function(e){if(void 0===e&&(e=this.$measureSizes()),e&&(this.$characterSize.width!==e.width||this.$characterSize.height!==e.height)){this.$measureNode.style.fontWeight="bold";var t=this.$measureSizes();this.$measureNode.style.fontWeight="",this.$characterSize=e,this.charSizes=Object.create(null),this.allowBoldFonts=t&&t.width===e.width&&t.height===e.height,this._emit("changeCharacterSize",{data:e})}},e.prototype.$addObserver=function(){var e=this;this.$observer=new window.ResizeObserver(function(t){e.checkForSizeChanges()}),this.$observer.observe(this.$measureNode)},e.prototype.$pollSizeChanges=function(){if(this.$pollSizeChangesTimer||this.$observer)return this.$pollSizeChangesTimer;var e=this;return this.$pollSizeChangesTimer=s.onIdle(function t(){e.checkForSizeChanges(),s.onIdle(t,500)},500)},e.prototype.setPolling=function(e){e?this.$pollSizeChanges():this.$pollSizeChangesTimer&&(clearInterval(this.$pollSizeChangesTimer),this.$pollSizeChangesTimer=0)},e.prototype.$measureSizes=function(e){var t={height:(e||this.$measureNode).clientHeight,width:(e||this.$measureNode).clientWidth/512};return 0===t.width||0===t.height?null:t},e.prototype.$measureCharWidth=function(e){return this.$main.textContent=r.stringRepeat(e,512),this.$main.getBoundingClientRect().width/512},e.prototype.getCharacterWidth=function(e){var t=this.charSizes[e];return void 0===t&&(t=this.charSizes[e]=this.$measureCharWidth(e)/this.$characterSize.width),t},e.prototype.destroy=function(){clearInterval(this.$pollSizeChangesTimer),this.$observer&&this.$observer.disconnect(),this.el&&this.el.parentNode&&this.el.parentNode.removeChild(this.el)},e.prototype.$getZoom=function(e){return e&&e.parentElement?(Number(window.getComputedStyle(e).zoom)||1)*this.$getZoom(e.parentElement):1},e.prototype.$initTransformMeasureNodes=function(){var e=function(e,t){return["div",{style:"position: absolute;top:"+e+"px;left:"+t+"px;"}]};this.els=o.buildDom([e(0,0),e(200,0),e(0,200),e(200,200)],this.el)},e.prototype.transformCoordinates=function(e,t){function i(e,t,i){var n=e[1]*t[0]-e[0]*t[1];return[(-t[1]*i[0]+t[0]*i[1])/n,(e[1]*i[0]-e[0]*i[1])/n]}function n(e,t){return[e[0]-t[0],e[1]-t[1]]}function o(e,t){return[e[0]+t[0],e[1]+t[1]]}function r(e,t){return[e*t[0],e*t[1]]}function s(e){var t=e.getBoundingClientRect();return[t.left,t.top]}e&&(e=r(1/this.$getZoom(this.el),e)),this.els||this.$initTransformMeasureNodes();var a=s(this.els[0]),l=s(this.els[1]),c=s(this.els[2]),h=s(this.els[3]),u=i(n(h,l),n(h,c),n(o(l,c),o(h,a))),d=r(1+u[0],n(l,a)),p=r(1+u[1],n(c,a));if(t){var g=u[0]*t[0]/200+u[1]*t[1]/200+1,f=o(r(t[0],d),r(t[1],p));return o(r(1/g/200,f),a)}var m=n(e,a),y=i(n(d,r(u[0],m)),n(p,r(u[1],m)),m);return r(200,y)},e}();h.prototype.$characterSize={width:0,height:0},n.implement(h.prototype,l),t.FontMetrics=h}),ace.define("ace/css/editor-css",["require","exports","module"],function(e,t,i){i.exports='\n.ace_br1 {border-top-left-radius    : 3px;}\n.ace_br2 {border-top-right-radius   : 3px;}\n.ace_br3 {border-top-left-radius    : 3px; border-top-right-radius:    3px;}\n.ace_br4 {border-bottom-right-radius: 3px;}\n.ace_br5 {border-top-left-radius    : 3px; border-bottom-right-radius: 3px;}\n.ace_br6 {border-top-right-radius   : 3px; border-bottom-right-radius: 3px;}\n.ace_br7 {border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px;}\n.ace_br8 {border-bottom-left-radius : 3px;}\n.ace_br9 {border-top-left-radius    : 3px; border-bottom-left-radius:  3px;}\n.ace_br10{border-top-right-radius   : 3px; border-bottom-left-radius:  3px;}\n.ace_br11{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-left-radius:  3px;}\n.ace_br12{border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}\n.ace_br13{border-top-left-radius    : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}\n.ace_br14{border-top-right-radius   : 3px; border-bottom-right-radius: 3px; border-bottom-left-radius:  3px;}\n.ace_br15{border-top-left-radius    : 3px; border-top-right-radius:    3px; border-bottom-right-radius: 3px; border-bottom-left-radius: 3px;}\n\n\n.ace_editor {\n    position: relative;\n    overflow: hidden;\n    padding: 0;\n    font: 12px/normal \'Monaco\', \'Menlo\', \'Ubuntu Mono\', \'Consolas\', \'Source Code Pro\', \'source-code-pro\', monospace;\n    direction: ltr;\n    text-align: left;\n    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);\n    forced-color-adjust: none;\n}\n\n.ace_scroller {\n    position: absolute;\n    overflow: hidden;\n    top: 0;\n    bottom: 0;\n    background-color: inherit;\n    -ms-user-select: none;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: text;\n}\n\n.ace_content {\n    position: absolute;\n    box-sizing: border-box;\n    min-width: 100%;\n    contain: style size layout;\n    font-variant-ligatures: no-common-ligatures;\n}\n.ace_invisible {\n    font-variant-ligatures: none;\n}\n\n.ace_keyboard-focus:focus {\n    box-shadow: inset 0 0 0 2px #5E9ED6;\n    outline: none;\n}\n\n.ace_dragging .ace_scroller:before{\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    content: \'\';\n    background: rgba(250, 250, 250, 0.01);\n    z-index: 1000;\n}\n.ace_dragging.ace_dark .ace_scroller:before{\n    background: rgba(0, 0, 0, 0.01);\n}\n\n.ace_gutter {\n    position: absolute;\n    overflow : hidden;\n    width: auto;\n    top: 0;\n    bottom: 0;\n    left: 0;\n    cursor: default;\n    z-index: 4;\n    -ms-user-select: none;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    contain: style size layout;\n}\n\n.ace_gutter-active-line {\n    position: absolute;\n    left: 0;\n    right: 0;\n}\n\n.ace_scroller.ace_scroll-left:after {\n    content: "";\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    box-shadow: 17px 0 16px -16px rgba(0, 0, 0, 0.4) inset;\n    pointer-events: none;\n}\n\n.ace_gutter-cell, .ace_gutter-cell_svg-icons {\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n    padding-left: 19px;\n    padding-right: 6px;\n    background-repeat: no-repeat;\n}\n\n.ace_gutter-cell_svg-icons .ace_gutter_annotation {\n    margin-left: -14px;\n    float: left;\n}\n\n.ace_gutter-cell .ace_gutter_annotation {\n    margin-left: -19px;\n    float: left;\n}\n\n.ace_gutter-cell.ace_error, .ace_icon.ace_error, .ace_icon.ace_error_fold, .ace_gutter-cell.ace_security, .ace_icon.ace_security, .ace_icon.ace_security_fold {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAABOFBMVEX/////////QRswFAb/Ui4wFAYwFAYwFAaWGAfDRymzOSH/PxswFAb/SiUwFAYwFAbUPRvjQiDllog5HhHdRybsTi3/Tyv9Tir+Syj/UC3////XurebMBIwFAb/RSHbPx/gUzfdwL3kzMivKBAwFAbbvbnhPx66NhowFAYwFAaZJg8wFAaxKBDZurf/RB6mMxb/SCMwFAYwFAbxQB3+RB4wFAb/Qhy4Oh+4QifbNRcwFAYwFAYwFAb/QRzdNhgwFAYwFAbav7v/Uy7oaE68MBK5LxLewr/r2NXewLswFAaxJw4wFAbkPRy2PyYwFAaxKhLm1tMwFAazPiQwFAaUGAb/QBrfOx3bvrv/VC/maE4wFAbRPBq6MRO8Qynew8Dp2tjfwb0wFAbx6eju5+by6uns4uH9/f36+vr/GkHjAAAAYnRSTlMAGt+64rnWu/bo8eAA4InH3+DwoN7j4eLi4xP99Nfg4+b+/u9B/eDs1MD1mO7+4PHg2MXa347g7vDizMLN4eG+Pv7i5evs/v79yu7S3/DV7/498Yv24eH+4ufQ3Ozu/v7+y13sRqwAAADLSURBVHjaZc/XDsFgGIBhtDrshlitmk2IrbHFqL2pvXf/+78DPokj7+Fz9qpU/9UXJIlhmPaTaQ6QPaz0mm+5gwkgovcV6GZzd5JtCQwgsxoHOvJO15kleRLAnMgHFIESUEPmawB9ngmelTtipwwfASilxOLyiV5UVUyVAfbG0cCPHig+GBkzAENHS0AstVF6bacZIOzgLmxsHbt2OecNgJC83JERmePUYq8ARGkJx6XtFsdddBQgZE2nPR6CICZhawjA4Fb/chv+399kfR+MMMDGOQAAAABJRU5ErkJggg==");\n    background-repeat: no-repeat;\n    background-position: 2px center;\n}\n\n.ace_gutter-cell.ace_warning, .ace_icon.ace_warning, .ace_icon.ace_warning_fold {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAmVBMVEX///8AAAD///8AAAAAAABPSzb/5sAAAAB/blH/73z/ulkAAAAAAAD85pkAAAAAAAACAgP/vGz/rkDerGbGrV7/pkQICAf////e0IsAAAD/oED/qTvhrnUAAAD/yHD/njcAAADuv2r/nz//oTj/p064oGf/zHAAAAA9Nir/tFIAAAD/tlTiuWf/tkIAAACynXEAAAAAAAAtIRW7zBpBAAAAM3RSTlMAABR1m7RXO8Ln31Z36zT+neXe5OzooRDfn+TZ4p3h2hTf4t3k3ucyrN1K5+Xaks52Sfs9CXgrAAAAjklEQVR42o3PbQ+CIBQFYEwboPhSYgoYunIqqLn6/z8uYdH8Vmdnu9vz4WwXgN/xTPRD2+sgOcZjsge/whXZgUaYYvT8QnuJaUrjrHUQreGczuEafQCO/SJTufTbroWsPgsllVhq3wJEk2jUSzX3CUEDJC84707djRc5MTAQxoLgupWRwW6UB5fS++NV8AbOZgnsC7BpEAAAAABJRU5ErkJggg==");\n    background-repeat: no-repeat;\n    background-position: 2px center;\n}\n\n.ace_gutter-cell.ace_info, .ace_icon.ace_info, .ace_gutter-cell.ace_hint, .ace_icon.ace_hint {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAAAAAA6mKC9AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAAJ0Uk5TAAB2k804AAAAPklEQVQY02NgIB68QuO3tiLznjAwpKTgNyDbMegwisCHZUETUZV0ZqOquBpXj2rtnpSJT1AEnnRmL2OgGgAAIKkRQap2htgAAAAASUVORK5CYII=");\n    background-repeat: no-repeat;\n    background-position: 2px center;\n}\n\n.ace_dark .ace_gutter-cell.ace_info, .ace_dark .ace_icon.ace_info, .ace_dark .ace_gutter-cell.ace_hint, .ace_dark .ace_icon.ace_hint {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQBAMAAADt3eJSAAAAJFBMVEUAAAChoaGAgIAqKiq+vr6tra1ZWVmUlJSbm5s8PDxubm56enrdgzg3AAAAAXRSTlMAQObYZgAAAClJREFUeNpjYMAPdsMYHegyJZFQBlsUlMFVCWUYKkAZMxZAGdxlDMQBAG+TBP4B6RyJAAAAAElFTkSuQmCC");\n}\n\n.ace_icon_svg.ace_error {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiI+CjxnIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlPSJyZWQiIHNoYXBlLXJlbmRlcmluZz0iZ2VvbWV0cmljUHJlY2lzaW9uIj4KPGNpcmNsZSBmaWxsPSJub25lIiBjeD0iOCIgY3k9IjgiIHI9IjciIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPGxpbmUgeDE9IjExIiB5MT0iNSIgeDI9IjUiIHkyPSIxMSIvPgo8bGluZSB4MT0iMTEiIHkxPSIxMSIgeDI9IjUiIHkyPSI1Ii8+CjwvZz4KPC9zdmc+");\n    background-color: crimson;\n}\n.ace_icon_svg.ace_security {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjAgMTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgICA8ZyBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZT0iZGFya29yYW5nZSIgZmlsbD0ibm9uZSIgc2hhcGUtcmVuZGVyaW5nPSJnZW9tZXRyaWNQcmVjaXNpb24iPgogICAgICAgIDxwYXRoIGNsYXNzPSJzdHJva2UtbGluZWpvaW4tcm91bmQiIGQ9Ik04IDE0LjgzMDdDOCAxNC44MzA3IDIgMTIuOTA0NyAyIDguMDg5OTJWMy4yNjU0OEM1LjMxIDMuMjY1NDggNy45ODk5OSAxLjM0OTE4IDcuOTg5OTkgMS4zNDkxOEM3Ljk4OTk5IDEuMzQ5MTggMTAuNjkgMy4yNjU0OCAxNCAzLjI2NTQ4VjguMDg5OTJDMTQgMTIuOTA0NyA4IDE0LjgzMDcgOCAxNC44MzA3WiIvPgogICAgICAgIDxwYXRoIGQ9Ik0yIDguMDg5OTJWMy4yNjU0OEM1LjMxIDMuMjY1NDggNy45ODk5OSAxLjM0OTE4IDcuOTg5OTkgMS4zNDkxOCIvPgogICAgICAgIDxwYXRoIGQ9Ik0xMy45OSA4LjA4OTkyVjMuMjY1NDhDMTAuNjggMy4yNjU0OCA4IDEuMzQ5MTggOCAxLjM0OTE4Ii8+CiAgICAgICAgPHBhdGggY2xhc3M9InN0cm9rZS1saW5lam9pbi1yb3VuZCIgZD0iTTggNFY5Ii8+CiAgICAgICAgPHBhdGggY2xhc3M9InN0cm9rZS1saW5lam9pbi1yb3VuZCIgZD0iTTggMTBWMTIiLz4KICAgIDwvZz4KPC9zdmc+");\n    background-color: crimson;\n}\n.ace_icon_svg.ace_warning {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiI+CjxnIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlPSJkYXJrb3JhbmdlIiBzaGFwZS1yZW5kZXJpbmc9Imdlb21ldHJpY1ByZWNpc2lvbiI+Cjxwb2x5Z29uIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGZpbGw9Im5vbmUiIHBvaW50cz0iOCAxIDE1IDE1IDEgMTUgOCAxIi8+CjxyZWN0IHg9IjgiIHk9IjEyIiB3aWR0aD0iMC4wMSIgaGVpZ2h0PSIwLjAxIi8+CjxsaW5lIHgxPSI4IiB5MT0iNiIgeDI9IjgiIHkyPSIxMCIvPgo8L2c+Cjwvc3ZnPg==");\n    background-color: darkorange;\n}\n.ace_icon_svg.ace_info {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiI+CjxnIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlPSJibHVlIiBzaGFwZS1yZW5kZXJpbmc9Imdlb21ldHJpY1ByZWNpc2lvbiI+CjxjaXJjbGUgZmlsbD0ibm9uZSIgY3g9IjgiIGN5PSI4IiByPSI3IiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjxwb2x5bGluZSBwb2ludHM9IjggMTEgOCA4Ii8+Cjxwb2x5bGluZSBwb2ludHM9IjkgOCA2IDgiLz4KPGxpbmUgeDE9IjEwIiB5MT0iMTEiIHgyPSI2IiB5Mj0iMTEiLz4KPHJlY3QgeD0iOCIgeT0iNSIgd2lkdGg9IjAuMDEiIGhlaWdodD0iMC4wMSIvPgo8L2c+Cjwvc3ZnPg==");\n    background-color: royalblue;\n}\n.ace_icon_svg.ace_hint {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB2aWV3Qm94PSIwIDAgMjAgMTYiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgICA8ZyBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZT0ic2lsdmVyIiBmaWxsPSJub25lIiBzaGFwZS1yZW5kZXJpbmc9Imdlb21ldHJpY1ByZWNpc2lvbiI+CiAgICAgICAgPHBhdGggY2xhc3M9InN0cm9rZS1saW5lam9pbi1yb3VuZCIgZD0iTTYgMTRIMTAiLz4KICAgICAgICA8cGF0aCBkPSJNOCAxMUg5QzkgOS40NzAwMiAxMiA4LjU0MDAyIDEyIDUuNzYwMDJDMTIuMDIgNC40MDAwMiAxMS4zOSAzLjM2MDAyIDEwLjQzIDIuNjcwMDJDOSAxLjY0MDAyIDcuMDAwMDEgMS42NDAwMiA1LjU3MDAxIDIuNjcwMDJDNC42MTAwMSAzLjM2MDAyIDMuOTggNC40MDAwMiA0IDUuNzYwMDJDNCA4LjU0MDAyIDcuMDAwMDEgOS40NzAwMiA3LjAwMDAxIDExSDhaIi8+CiAgICA8L2c+Cjwvc3ZnPg==");\n    background-color: silver;\n}\n\n.ace_icon_svg.ace_error_fold {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyMCAxNiIgZmlsbD0ibm9uZSI+CiAgPHBhdGggZD0ibSAxOC45Mjk4NTEsNy44Mjk4MDc2IGMgMC4xNDYzNTMsNi4zMzc0NjA0IC02LjMyMzE0Nyw3Ljc3Nzg0NDQgLTcuNDc3OTEyLDcuNzc3ODQ0NCAtMi4xMDcyNzI2LC0wLjEyODc1IDUuMTE3Njc4LDAuMzU2MjQ5IDUuMDUxNjk4LC03Ljg3MDA2MTggLTAuNjA0NjcyLC04LjAwMzk3MzQ5IC03LjA3NzI3MDYsLTcuNTYzMTE4OSAtNC44NTczLC03LjQzMDM5NTU2IDEuNjA2LC0wLjExNTE0MjI1IDYuODk3NDg1LDEuMjYyNTQ1OTYgNy4yODM1MTQsNy41MjI2MTI5NiB6IiBmaWxsPSJjcmltc29uIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8cGF0aCBmaWxsLXJ1bGU9ImV2ZW5vZGQiIGNsaXAtcnVsZT0iZXZlbm9kZCIgZD0ibSA4LjExNDc1NjIsMi4wNTI5ODI4IGMgMy4zNDkxNjk4LDAgNi4wNjQxMzI4LDIuNjc2ODYyNyA2LjA2NDEzMjgsNS45Nzg5NTMgMCwzLjMwMjExMjIgLTIuNzE0OTYzLDUuOTc4OTIwMiAtNi4wNjQxMzI4LDUuOTc4OTIwMiAtMy4zNDkxNDczLDAgLTYuMDY0MTc3MiwtMi42NzY4MDggLTYuMDY0MTc3MiwtNS45Nzg5MjAyIDAuMDA1MzksLTMuMjk5ODg2MSAyLjcxNzI2NTYsLTUuOTczNjQwOCA2LjA2NDE3NzIsLTUuOTc4OTUzIHogbSAwLC0xLjczNTgyNzE5IGMgLTQuMzIxNDgzNiwwIC03LjgyNDc0MDM4LDMuNDU0MDE4NDkgLTcuODI0NzQwMzgsNy43MTQ3ODAxOSAwLDQuMjYwNzI4MiAzLjUwMzI1Njc4LDcuNzE0NzQ1MiA3LjgyNDc0MDM4LDcuNzE0NzQ1MiA0LjMyMTQ0OTgsMCA3LjgyNDY5OTgsLTMuNDU0MDE3IDcuODI0Njk5OCwtNy43MTQ3NDUyIDAsLTIuMDQ2MDkxNCAtMC44MjQzOTIsLTQuMDA4MzY3MiAtMi4yOTE3NTYsLTUuNDU1MTc0NiBDIDEyLjE4MDIyNSwxLjEyOTk2NDggMTAuMTkwMDEzLDAuMzE3MTU1NjEgOC4xMTQ3NTYyLDAuMzE3MTU1NjEgWiBNIDYuOTM3NDU2Myw4LjI0MDU5ODUgNC42NzE4Njg1LDEwLjQ4NTg1MiA2LjAwODY4MTQsMTEuODc2NzI4IDguMzE3MDAzNSw5LjYwMDc5MTEgMTAuNjI1MzM3LDExLjg3NjcyOCAxMS45NjIxMzgsMTAuNDg1ODUyIDkuNjk2NTUwOCw4LjI0MDU5ODUgMTEuOTYyMTM4LDYuMDA2ODA2NiAxMC41NzMyNDYsNC42Mzc0MzM1IDguMzE3MDAzNSw2Ljg3MzQyOTcgNi4wNjA3NjA3LDQuNjM3NDMzNSA0LjY3MTg2ODUsNi4wMDY4MDY2IFoiIGZpbGw9ImNyaW1zb24iIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4=");\n    background-color: crimson;\n}\n.ace_icon_svg.ace_security_fold {\n    -webkit-mask-image: url("data:image/svg+xml;base64,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");\n    background-color: crimson;\n}\n.ace_icon_svg.ace_warning_fold {\n    -webkit-mask-image: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAyMCAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNC43NzY5IDE0LjczMzdMOC42NTE5MiAyLjQ4MzY5QzguMzI5NDYgMS44Mzg3NyA3LjQwOTEzIDEuODM4NzcgNy4wODY2NyAyLjQ4MzY5TDAuOTYxNjY5IDE0LjczMzdDMC42NzA3NzUgMTUuMzE1NSAxLjA5MzgzIDE2IDEuNzQ0MjkgMTZIMTMuOTk0M0MxNC42NDQ4IDE2IDE1LjA2NzggMTUuMzE1NSAxNC43NzY5IDE0LjczMzdaTTMuMTYwMDcgMTQuMjVMNy44NjkyOSA0LjgzMTU2TDEyLjU3ODUgMTQuMjVIMy4xNjAwN1pNOC43NDQyOSAxMS42MjVWMTMuMzc1SDYuOTk0MjlWMTEuNjI1SDguNzQ0MjlaTTYuOTk0MjkgMTAuNzVWNy4yNUg4Ljc0NDI5VjEwLjc1SDYuOTk0MjlaIiBmaWxsPSIjRUM3MjExIi8+CjxwYXRoIGQ9Ik0xMS4xOTkxIDIuOTUyMzhDMTAuODgwOSAyLjMxNDY3IDEwLjM1MzcgMS44MDUyNiA5LjcwNTUgMS41MDlMMTEuMDQxIDEuMDY5NzhDMTEuNjg4MyAwLjk0OTgxNCAxMi4zMzcgMS4yNzI2MyAxMi42MzE3IDEuODYxNDFMMTcuNjEzNiAxMS44MTYxQzE4LjM1MjcgMTMuMjkyOSAxNy41OTM4IDE1LjA4MDQgMTYuMDE4IDE1LjU3NDVDMTYuNDA0NCAxNC40NTA3IDE2LjMyMzEgMTMuMjE4OCAxNS43OTI0IDEyLjE1NTVMMTEuMTk5MSAyLjk1MjM4WiIgZmlsbD0iI0VDNzIxMSIvPgo8L3N2Zz4=");\n    background-color: darkorange;\n}\n\n.ace_scrollbar {\n    contain: strict;\n    position: absolute;\n    right: 0;\n    bottom: 0;\n    z-index: 6;\n}\n\n.ace_scrollbar-inner {\n    position: absolute;\n    cursor: text;\n    left: 0;\n    top: 0;\n}\n\n.ace_scrollbar-v{\n    overflow-x: hidden;\n    overflow-y: scroll;\n    top: 0;\n}\n\n.ace_scrollbar-h {\n    overflow-x: scroll;\n    overflow-y: hidden;\n    left: 0;\n}\n\n.ace_print-margin {\n    position: absolute;\n    height: 100%;\n}\n\n.ace_text-input {\n    position: absolute;\n    z-index: 0;\n    width: 0.5em;\n    height: 1em;\n    opacity: 0;\n    background: transparent;\n    -moz-appearance: none;\n    appearance: none;\n    border: none;\n    resize: none;\n    outline: none;\n    overflow: hidden;\n    font: inherit;\n    padding: 0 1px;\n    margin: 0 -1px;\n    contain: strict;\n    -ms-user-select: text;\n    -moz-user-select: text;\n    -webkit-user-select: text;\n    user-select: text;\n    /*with `pre-line` chrome inserts &nbsp; instead of space*/\n    white-space: pre!important;\n}\n.ace_text-input.ace_composition {\n    background: transparent;\n    color: inherit;\n    z-index: 1000;\n    opacity: 1;\n}\n.ace_composition_placeholder { color: transparent }\n.ace_composition_marker { \n    border-bottom: 1px solid;\n    position: absolute;\n    border-radius: 0;\n    margin-top: 1px;\n}\n\n[ace_nocontext=true] {\n    transform: none!important;\n    filter: none!important;\n    clip-path: none!important;\n    mask : none!important;\n    contain: none!important;\n    perspective: none!important;\n    mix-blend-mode: initial!important;\n    z-index: auto;\n}\n\n.ace_layer {\n    z-index: 1;\n    position: absolute;\n    overflow: hidden;\n    /* workaround for chrome bug https://github.com/ajaxorg/ace/issues/2312*/\n    word-wrap: normal;\n    white-space: pre;\n    height: 100%;\n    width: 100%;\n    box-sizing: border-box;\n    /* setting pointer-events: auto; on node under the mouse, which changes\n        during scroll, will break mouse wheel scrolling in Safari */\n    pointer-events: none;\n}\n\n.ace_gutter-layer {\n    position: relative;\n    width: auto;\n    text-align: right;\n    pointer-events: auto;\n    height: 1000000px;\n    contain: style size layout;\n}\n\n.ace_text-layer {\n    font: inherit !important;\n    position: absolute;\n    height: 1000000px;\n    width: 1000000px;\n    contain: style size layout;\n}\n\n.ace_text-layer > .ace_line, .ace_text-layer > .ace_line_group {\n    contain: style size layout;\n    position: absolute;\n    top: 0;\n    left: 0;\n    right: 0;\n}\n\n.ace_hidpi .ace_text-layer,\n.ace_hidpi .ace_gutter-layer,\n.ace_hidpi .ace_content,\n.ace_hidpi .ace_gutter {\n    contain: strict;\n}\n.ace_hidpi .ace_text-layer > .ace_line, \n.ace_hidpi .ace_text-layer > .ace_line_group {\n    contain: strict;\n}\n\n.ace_cjk {\n    display: inline-block;\n    text-align: center;\n}\n\n.ace_cursor-layer {\n    z-index: 4;\n}\n\n.ace_cursor {\n    z-index: 4;\n    position: absolute;\n    box-sizing: border-box;\n    border-left: 2px solid;\n    /* workaround for smooth cursor repaintng whole screen in chrome */\n    transform: translatez(0);\n}\n\n.ace_multiselect .ace_cursor {\n    border-left-width: 1px;\n}\n\n.ace_slim-cursors .ace_cursor {\n    border-left-width: 1px;\n}\n\n.ace_overwrite-cursors .ace_cursor {\n    border-left-width: 0;\n    border-bottom: 1px solid;\n}\n\n.ace_hidden-cursors .ace_cursor {\n    opacity: 0.2;\n}\n\n.ace_hasPlaceholder .ace_hidden-cursors .ace_cursor {\n    opacity: 0;\n}\n\n.ace_smooth-blinking .ace_cursor {\n    transition: opacity 0.18s;\n}\n\n.ace_animate-blinking .ace_cursor {\n    animation-duration: 1000ms;\n    animation-timing-function: step-end;\n    animation-name: blink-ace-animate;\n    animation-iteration-count: infinite;\n}\n\n.ace_animate-blinking.ace_smooth-blinking .ace_cursor {\n    animation-duration: 1000ms;\n    animation-timing-function: ease-in-out;\n    animation-name: blink-ace-animate-smooth;\n}\n    \n@keyframes blink-ace-animate {\n    from, to { opacity: 1; }\n    60% { opacity: 0; }\n}\n\n@keyframes blink-ace-animate-smooth {\n    from, to { opacity: 1; }\n    45% { opacity: 1; }\n    60% { opacity: 0; }\n    85% { opacity: 0; }\n}\n\n.ace_marker-layer .ace_step, .ace_marker-layer .ace_stack {\n    position: absolute;\n    z-index: 3;\n}\n\n.ace_marker-layer .ace_selection {\n    position: absolute;\n    z-index: 5;\n}\n\n.ace_marker-layer .ace_bracket {\n    position: absolute;\n    z-index: 6;\n}\n\n.ace_marker-layer .ace_error_bracket {\n    position: absolute;\n    border-bottom: 1px solid #DE5555;\n    border-radius: 0;\n}\n\n.ace_marker-layer .ace_active-line {\n    position: absolute;\n    z-index: 2;\n}\n\n.ace_marker-layer .ace_selected-word {\n    position: absolute;\n    z-index: 4;\n    box-sizing: border-box;\n}\n\n.ace_line .ace_fold {\n    box-sizing: border-box;\n\n    display: inline-block;\n    height: 11px;\n    margin-top: -2px;\n    vertical-align: middle;\n\n    background-image:\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACJJREFUeNpi+P//fxgTAwPDBxDxD078RSX+YeEyDFMCIMAAI3INmXiwf2YAAAAASUVORK5CYII=");\n    background-repeat: no-repeat, repeat-x;\n    background-position: center center, top left;\n    color: transparent;\n\n    border: 1px solid black;\n    border-radius: 2px;\n\n    cursor: pointer;\n    pointer-events: auto;\n}\n\n.ace_dark .ace_fold {\n}\n\n.ace_fold:hover{\n    background-image:\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABEAAAAJCAYAAADU6McMAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAJpJREFUeNpi/P//PwOlgAXGYGRklAVSokD8GmjwY1wasKljQpYACtpCFeADcHVQfQyMQAwzwAZI3wJKvCLkfKBaMSClBlR7BOQikCFGQEErIH0VqkabiGCAqwUadAzZJRxQr/0gwiXIal8zQQPnNVTgJ1TdawL0T5gBIP1MUJNhBv2HKoQHHjqNrA4WO4zY0glyNKLT2KIfIMAAQsdgGiXvgnYAAAAASUVORK5CYII="),\n        url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAA3CAYAAADNNiA5AAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAACBJREFUeNpi+P//fz4TAwPDZxDxD5X4i5fLMEwJgAADAEPVDbjNw87ZAAAAAElFTkSuQmCC");\n}\n\n.ace_tooltip {\n    background-color: #f5f5f5;\n    border: 1px solid gray;\n    border-radius: 1px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n    color: black;\n    padding: 3px 4px;\n    position: fixed;\n    z-index: 999999;\n    box-sizing: border-box;\n    cursor: default;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n    line-height: normal;\n    font-style: normal;\n    font-weight: normal;\n    letter-spacing: normal;\n    pointer-events: none;\n    overflow: auto;\n    max-width: min(33em, 66vw);\n    overscroll-behavior: contain;\n}\n.ace_tooltip pre {\n    white-space: pre-wrap;\n}\n\n.ace_tooltip.ace_dark {\n    background-color: #636363;\n    color: #fff;\n}\n\n.ace_tooltip:focus {\n    outline: 1px solid #5E9ED6;\n}\n\n.ace_icon {\n    display: inline-block;\n    width: 18px;\n    vertical-align: top;\n}\n\n.ace_icon_svg {\n    display: inline-block;\n    width: 12px;\n    vertical-align: top;\n    -webkit-mask-repeat: no-repeat;\n    -webkit-mask-size: 12px;\n    -webkit-mask-position: center;\n}\n\n.ace_folding-enabled > .ace_gutter-cell, .ace_folding-enabled > .ace_gutter-cell_svg-icons {\n    padding-right: 13px;\n}\n\n.ace_fold-widget, .ace_custom-widget {\n    box-sizing: border-box;\n\n    margin: 0 -12px 0 1px;\n    display: none;\n    width: 11px;\n    vertical-align: top;\n\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42mWKsQ0AMAzC8ixLlrzQjzmBiEjp0A6WwBCSPgKAXoLkqSot7nN3yMwR7pZ32NzpKkVoDBUxKAAAAABJRU5ErkJggg==");\n    background-repeat: no-repeat;\n    background-position: center;\n\n    border-radius: 3px;\n    \n    border: 1px solid transparent;\n    cursor: pointer;\n    pointer-events: auto;\n}\n\n.ace_custom-widget {\n    background: none;\n}\n\n.ace_folding-enabled .ace_fold-widget {\n    display: inline-block;   \n}\n\n.ace_fold-widget.ace_end {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAANElEQVR42m3HwQkAMAhD0YzsRchFKI7sAikeWkrxwScEB0nh5e7KTPWimZki4tYfVbX+MNl4pyZXejUO1QAAAABJRU5ErkJggg==");\n}\n\n.ace_fold-widget.ace_closed {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAGCAYAAAAG5SQMAAAAOUlEQVR42jXKwQkAMAgDwKwqKD4EwQ26sSOkVWjgIIHAzPiCgaqiqnJHZnKICBERHN194O5b9vbLuAVRL+l0YWnZAAAAAElFTkSuQmCCXA==");\n}\n\n.ace_fold-widget:hover {\n    border: 1px solid rgba(0, 0, 0, 0.3);\n    background-color: rgba(255, 255, 255, 0.2);\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.7);\n}\n\n.ace_fold-widget:active {\n    border: 1px solid rgba(0, 0, 0, 0.4);\n    background-color: rgba(0, 0, 0, 0.05);\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.8);\n}\n/**\n * Dark version for fold widgets\n */\n.ace_dark .ace_fold-widget {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHklEQVQIW2P4//8/AzoGEQ7oGCaLLAhWiSwB146BAQCSTPYocqT0AAAAAElFTkSuQmCC");\n}\n.ace_dark .ace_fold-widget.ace_end {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAH0lEQVQIW2P4//8/AxQ7wNjIAjDMgC4AxjCVKBirIAAF0kz2rlhxpAAAAABJRU5ErkJggg==");\n}\n.ace_dark .ace_fold-widget.ace_closed {\n    background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAMAAAAFCAYAAACAcVaiAAAAHElEQVQIW2P4//+/AxAzgDADlOOAznHAKgPWAwARji8UIDTfQQAAAABJRU5ErkJggg==");\n}\n.ace_dark .ace_fold-widget:hover {\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);\n    background-color: rgba(255, 255, 255, 0.1);\n}\n.ace_dark .ace_fold-widget:active {\n    box-shadow: 0 1px 1px rgba(255, 255, 255, 0.2);\n}\n\n.ace_inline_button {\n    border: 1px solid lightgray;\n    display: inline-block;\n    margin: -1px 8px;\n    padding: 0 5px;\n    pointer-events: auto;\n    cursor: pointer;\n}\n.ace_inline_button:hover {\n    border-color: gray;\n    background: rgba(200,200,200,0.2);\n    display: inline-block;\n    pointer-events: auto;\n}\n\n.ace_fold-widget.ace_invalid {\n    background-color: #FFB4B4;\n    border-color: #DE5555;\n}\n\n.ace_fade-fold-widgets .ace_fold-widget {\n    transition: opacity 0.4s ease 0.05s;\n    opacity: 0;\n}\n\n.ace_fade-fold-widgets:hover .ace_fold-widget {\n    transition: opacity 0.05s ease 0.05s;\n    opacity:1;\n}\n\n.ace_underline {\n    text-decoration: underline;\n}\n\n.ace_bold {\n    font-weight: bold;\n}\n\n.ace_nobold .ace_bold {\n    font-weight: normal;\n}\n\n.ace_italic {\n    font-style: italic;\n}\n\n\n.ace_error-marker {\n    background-color: rgba(255, 0, 0,0.2);\n    position: absolute;\n    z-index: 9;\n}\n\n.ace_highlight-marker {\n    background-color: rgba(255, 255, 0,0.2);\n    position: absolute;\n    z-index: 8;\n}\n\n.ace_mobile-menu {\n    position: absolute;\n    line-height: 1.5;\n    border-radius: 4px;\n    -ms-user-select: none;\n    -moz-user-select: none;\n    -webkit-user-select: none;\n    user-select: none;\n    background: white;\n    box-shadow: 1px 3px 2px grey;\n    border: 1px solid #dcdcdc;\n    color: black;\n}\n.ace_dark > .ace_mobile-menu {\n    background: #333;\n    color: #ccc;\n    box-shadow: 1px 3px 2px grey;\n    border: 1px solid #444;\n\n}\n.ace_mobile-button {\n    padding: 2px;\n    cursor: pointer;\n    overflow: hidden;\n}\n.ace_mobile-button:hover {\n    background-color: #eee;\n    opacity:1;\n}\n.ace_mobile-button:active {\n    background-color: #ddd;\n}\n\n.ace_placeholder {\n    position: relative;\n    font-family: arial;\n    transform: scale(0.9);\n    transform-origin: left;\n    white-space: pre;\n    opacity: 0.7;\n    margin: 0 10px;\n    z-index: 1;\n}\n\n.ace_ghost_text {\n    opacity: 0.5;\n    font-style: italic;\n}\n\n.ace_ghost_text_container > div {\n    white-space: pre;\n}\n\n.ghost_text_line_wrapped::after {\n    content: "↩";\n    position: absolute;\n}\n\n.ace_lineWidgetContainer.ace_ghost_text {\n    margin: 0px 4px\n}\n\n.ace_screenreader-only {\n    position:absolute;\n    left:-10000px;\n    top:auto;\n    width:1px;\n    height:1px;\n    overflow:hidden;\n}\n\n.ace_hidden_token {\n    display: none;\n}'}),ace.define("ace/layer/decorators",["require","exports","module","ace/lib/dom","ace/lib/oop","ace/lib/event_emitter"],function(e,t,i){"use strict";var n=e("../lib/dom"),o=e("../lib/oop"),r=e("../lib/event_emitter").EventEmitter,s=function(){function e(e,t){this.renderer=t,this.pixelRatio=1,this.maxHeight=t.layerConfig.maxHeight,this.lineHeight=t.layerConfig.lineHeight,this.minDecorationHeight=2*this.pixelRatio|0,this.halfMinDecorationHeight=this.minDecorationHeight/2|0,this.colors={},this.colors.dark={error:"rgba(255, 18, 18, 1)",warning:"rgba(18, 136, 18, 1)",info:"rgba(18, 18, 136, 1)"},this.colors.light={error:"rgb(255,51,51)",warning:"rgb(32,133,72)",info:"rgb(35,68,138)"},this.setScrollBarV(e)}return e.prototype.$createCanvas=function(){this.canvas=n.createElement("canvas"),this.canvas.style.top="0px",this.canvas.style.right="0px",this.canvas.style.zIndex="7",this.canvas.style.position="absolute"},e.prototype.setScrollBarV=function(e){this.$createCanvas(),this.scrollbarV=e,e.element.appendChild(this.canvas),this.setDimensions()},e.prototype.$updateDecorators=function(e){if("function"==typeof this.canvas.getContext){var t=!0===this.renderer.theme.isDark?this.colors.dark:this.colors.light;this.setDimensions(e);var i=this.canvas.getContext("2d"),n=this.renderer.session.$annotations;if(i.clearRect(0,0,this.canvas.width,this.canvas.height),n){var o={info:1,warning:2,error:3};n.forEach(function(e){e.priority=o[e.type]||null}),n=n.sort(function(e,t){return e.priority<t.priority?-1:+(e.priority>t.priority)});for(var r=0;r<n.length;r++){var s=n[r].row,a=this.getVerticalOffsetForRow(s),l=a+this.lineHeight,c=Math.round(this.heightRatio*a),h=Math.round(this.heightRatio*l),u=Math.round((c+h)/2),d=h-u;d<this.halfMinDecorationHeight&&(d=this.halfMinDecorationHeight),u-d<0&&(u=d),u+d>this.canvasHeight&&(u=this.canvasHeight-d);var p=u-d,g=u+d-p;i.fillStyle=t[n[r].type]||null,i.fillRect(0,p,Math.round(this.oneZoneWidth-1),g)}}var f=this.renderer.session.selection.getCursor();if(f){var m=Math.round(this.getVerticalOffsetForRow(f.row)*this.heightRatio);i.fillStyle="rgba(0, 0, 0, 0.5)",i.fillRect(0,m,this.canvasWidth,2)}}},e.prototype.getVerticalOffsetForRow=function(e){return e|=0,this.renderer.session.documentToScreenRow(e,0)*this.lineHeight},e.prototype.setDimensions=function(e){e=e||this.renderer.layerConfig,this.maxHeight=e.maxHeight,this.lineHeight=e.lineHeight,this.canvasHeight=e.height,this.canvasWidth=this.scrollbarV.width||this.canvasWidth,this.setZoneWidth(),this.canvas.width=this.canvasWidth,this.canvas.height=this.canvasHeight,this.maxHeight<this.canvasHeight?this.heightRatio=1:this.heightRatio=this.canvasHeight/this.maxHeight},e.prototype.setZoneWidth=function(){this.oneZoneWidth=this.canvasWidth},e.prototype.destroy=function(){this.canvas.parentNode.removeChild(this.canvas)},e}();o.implement(s.prototype,r),t.Decorator=s}),ace.define("ace/virtual_renderer",["require","exports","module","ace/lib/oop","ace/lib/dom","ace/lib/lang","ace/config","ace/layer/gutter","ace/layer/marker","ace/layer/text","ace/layer/cursor","ace/scrollbar","ace/scrollbar","ace/scrollbar_custom","ace/scrollbar_custom","ace/renderloop","ace/layer/font_metrics","ace/lib/event_emitter","ace/css/editor-css","ace/layer/decorators","ace/lib/useragent","ace/layer/text_util"],function(e,t,i){"use strict";var n=e("./lib/oop"),o=e("./lib/dom"),r=e("./lib/lang"),s=e("./config"),a=e("./layer/gutter").Gutter,l=e("./layer/marker").Marker,c=e("./layer/text").Text,h=e("./layer/cursor").Cursor,u=e("./scrollbar").HScrollBar,d=e("./scrollbar").VScrollBar,p=e("./scrollbar_custom").HScrollBar,g=e("./scrollbar_custom").VScrollBar,f=e("./renderloop").RenderLoop,m=e("./layer/font_metrics").FontMetrics,y=e("./lib/event_emitter").EventEmitter,v=e("./css/editor-css"),w=e("./layer/decorators").Decorator,b=e("./lib/useragent"),$=e("./layer/text_util").isTextToken;o.importCssString(v,"ace_editor.css",!1);var C=function(){function e(e,t){var i=this;this.container=e||o.createElement("div"),o.addCssClass(this.container,"ace_editor"),o.HI_DPI&&o.addCssClass(this.container,"ace_hidpi"),this.setTheme(t),null==s.get("useStrictCSP")&&s.set("useStrictCSP",!1),this.$gutter=o.createElement("div"),this.$gutter.className="ace_gutter",this.container.appendChild(this.$gutter),this.$gutter.setAttribute("aria-hidden","true"),this.scroller=o.createElement("div"),this.scroller.className="ace_scroller",this.container.appendChild(this.scroller),this.content=o.createElement("div"),this.content.className="ace_content",this.scroller.appendChild(this.content),this.$gutterLayer=new a(this.$gutter),this.$gutterLayer.on("changeGutterWidth",this.onGutterResize.bind(this)),this.$markerBack=new l(this.content);var n=this.$textLayer=new c(this.content);this.canvas=n.element,this.$markerFront=new l(this.content),this.$cursorLayer=new h(this.content),this.$horizScroll=!1,this.$vScroll=!1,this.scrollBar=this.scrollBarV=new d(this.container,this),this.scrollBarH=new u(this.container,this),this.scrollBarV.on("scroll",function(e){i.$scrollAnimation||i.session.setScrollTop(e.data-i.scrollMargin.top)}),this.scrollBarH.on("scroll",function(e){i.$scrollAnimation||i.session.setScrollLeft(e.data-i.scrollMargin.left)}),this.scrollTop=0,this.scrollLeft=0,this.cursorPos={row:0,column:0},this.$fontMetrics=new m(this.container),this.$textLayer.$setFontMetrics(this.$fontMetrics),this.$textLayer.on("changeCharacterSize",function(e){i.updateCharacterSize(),i.onResize(!0,i.gutterWidth,i.$size.width,i.$size.height),i._signal("changeCharacterSize",e)}),this.$size={width:0,height:0,scrollerHeight:0,scrollerWidth:0,$dirty:!0},this.layerConfig={width:1,padding:0,firstRow:0,firstRowScreen:0,lastRow:0,lineHeight:0,characterWidth:0,minHeight:1,maxHeight:1,offset:0,height:1,gutterOffset:1},this.scrollMargin={left:0,right:0,top:0,bottom:0,v:0,h:0},this.margin={left:0,right:0,top:0,bottom:0,v:0,h:0},this.$keepTextAreaAtCursor=!b.isIOS,this.$loop=new f(this.$renderChanges.bind(this),this.container.ownerDocument.defaultView),this.$loop.schedule(this.CHANGE_FULL),this.updateCharacterSize(),this.setPadding(4),this.$addResizeObserver(),s.resetOptions(this),s._signal("renderer",this)}return e.prototype.updateCharacterSize=function(){this.$textLayer.allowBoldFonts!=this.$allowBoldFonts&&(this.$allowBoldFonts=this.$textLayer.allowBoldFonts,this.setStyle("ace_nobold",!this.$allowBoldFonts)),this.layerConfig.characterWidth=this.characterWidth=this.$textLayer.getCharacterWidth(),this.layerConfig.lineHeight=this.lineHeight=this.$textLayer.getLineHeight(),this.$updatePrintMargin(),o.setStyle(this.scroller.style,"line-height",this.lineHeight+"px")},e.prototype.setSession=function(e){this.session&&this.session.doc.off("changeNewLineMode",this.onChangeNewLineMode),this.session=e,e&&this.scrollMargin.top&&0>=e.getScrollTop()&&e.setScrollTop(-this.scrollMargin.top),this.$cursorLayer.setSession(e),this.$markerBack.setSession(e),this.$markerFront.setSession(e),this.$gutterLayer.setSession(e),this.$textLayer.setSession(e),e&&(this.$loop.schedule(this.CHANGE_FULL),this.session.$setFontMetrics(this.$fontMetrics),this.scrollBarH.scrollLeft=this.scrollBarV.scrollTop=null,this.onChangeNewLineMode=this.onChangeNewLineMode.bind(this),this.onChangeNewLineMode(),this.session.doc.on("changeNewLineMode",this.onChangeNewLineMode))},e.prototype.updateLines=function(e,t,i){if(void 0===t&&(t=1/0),this.$changedLines?(this.$changedLines.firstRow>e&&(this.$changedLines.firstRow=e),this.$changedLines.lastRow<t&&(this.$changedLines.lastRow=t)):this.$changedLines={firstRow:e,lastRow:t},this.$changedLines.lastRow<this.layerConfig.firstRow)if(!i)return;else this.$changedLines.lastRow=this.layerConfig.lastRow;this.$changedLines.firstRow>this.layerConfig.lastRow||this.$loop.schedule(this.CHANGE_LINES)},e.prototype.onChangeNewLineMode=function(){this.$loop.schedule(this.CHANGE_TEXT),this.$textLayer.$updateEolChar(),this.session.$bidiHandler.setEolChar(this.$textLayer.EOL_CHAR)},e.prototype.onChangeTabSize=function(){this.$loop.schedule(this.CHANGE_TEXT|this.CHANGE_MARKER),this.$textLayer.onChangeTabSize()},e.prototype.updateText=function(){this.$loop.schedule(this.CHANGE_TEXT)},e.prototype.updateFull=function(e){e?this.$renderChanges(this.CHANGE_FULL,!0):this.$loop.schedule(this.CHANGE_FULL)},e.prototype.updateFontSize=function(){this.$textLayer.checkForSizeChanges()},e.prototype.$updateSizeAsync=function(){this.$loop.pending?this.$size.$dirty=!0:this.onResize()},e.prototype.onResize=function(e,t,i,n){if(!(this.resizing>2)){this.resizing>0?this.resizing++:this.resizing=+!!e;var o=this.container;n||(n=o.clientHeight||o.scrollHeight),!n&&this.$maxLines&&this.lineHeight>1&&(!o.style.height||"0px"==o.style.height)&&(o.style.height="1px",n=o.clientHeight||o.scrollHeight),i||(i=o.clientWidth||o.scrollWidth);var r=this.$updateCachedSize(e,t,i,n);if(this.$resizeTimer&&this.$resizeTimer.cancel(),!this.$size.scrollerHeight||!i&&!n)return this.resizing=0;e&&(this.$gutterLayer.$padding=null),e?this.$renderChanges(r|this.$changes,!0):this.$loop.schedule(r|this.$changes),this.resizing&&(this.resizing=0),this.scrollBarH.scrollLeft=this.scrollBarV.scrollTop=null,this.$customScrollbar&&this.$updateCustomScrollbar(!0)}},e.prototype.$updateCachedSize=function(e,t,i,n){n-=this.$extraHeight||0;var r=0,s=this.$size,a={width:s.width,height:s.height,scrollerHeight:s.scrollerHeight,scrollerWidth:s.scrollerWidth};if(n&&(e||s.height!=n)&&(s.height=n,r|=this.CHANGE_SIZE,s.scrollerHeight=s.height,this.$horizScroll&&(s.scrollerHeight-=this.scrollBarH.getHeight()),this.scrollBarV.setHeight(s.scrollerHeight),this.scrollBarV.element.style.bottom=this.scrollBarH.getHeight()+"px",r|=this.CHANGE_SCROLL),i&&(e||s.width!=i)){r|=this.CHANGE_SIZE,s.width=i,null==t&&(t=this.$showGutter?this.$gutter.offsetWidth:0),this.gutterWidth=t,o.setStyle(this.scrollBarH.element.style,"left",t+"px"),o.setStyle(this.scroller.style,"left",t+this.margin.left+"px"),s.scrollerWidth=Math.max(0,i-t-this.scrollBarV.getWidth()-this.margin.h),o.setStyle(this.$gutter.style,"left",this.margin.left+"px");var l=this.scrollBarV.getWidth()+"px";o.setStyle(this.scrollBarH.element.style,"right",l),o.setStyle(this.scroller.style,"right",l),o.setStyle(this.scroller.style,"bottom",this.scrollBarH.getHeight()),this.scrollBarH.setWidth(s.scrollerWidth),(this.session&&this.session.getUseWrapMode()&&this.adjustWrapLimit()||e)&&(r|=this.CHANGE_FULL)}return s.$dirty=!i||!n,r&&this._signal("resize",a),r},e.prototype.onGutterResize=function(e){var t=this.$showGutter?e:0;t!=this.gutterWidth&&(this.$changes|=this.$updateCachedSize(!0,t,this.$size.width,this.$size.height)),this.session.getUseWrapMode()&&this.adjustWrapLimit()||this.$size.$dirty?this.$loop.schedule(this.CHANGE_FULL):this.$computeLayerConfig()},e.prototype.adjustWrapLimit=function(){var e=Math.floor((this.$size.scrollerWidth-2*this.$padding)/this.characterWidth);return this.session.adjustWrapLimit(e,this.$showPrintMargin&&this.$printMarginColumn)},e.prototype.setAnimatedScroll=function(e){this.setOption("animatedScroll",e)},e.prototype.getAnimatedScroll=function(){return this.$animatedScroll},e.prototype.setShowInvisibles=function(e){this.setOption("showInvisibles",e),this.session.$bidiHandler.setShowInvisibles(e)},e.prototype.getShowInvisibles=function(){return this.getOption("showInvisibles")},e.prototype.getDisplayIndentGuides=function(){return this.getOption("displayIndentGuides")},e.prototype.setDisplayIndentGuides=function(e){this.setOption("displayIndentGuides",e)},e.prototype.getHighlightIndentGuides=function(){return this.getOption("highlightIndentGuides")},e.prototype.setHighlightIndentGuides=function(e){this.setOption("highlightIndentGuides",e)},e.prototype.setShowPrintMargin=function(e){this.setOption("showPrintMargin",e)},e.prototype.getShowPrintMargin=function(){return this.getOption("showPrintMargin")},e.prototype.setPrintMarginColumn=function(e){this.setOption("printMarginColumn",e)},e.prototype.getPrintMarginColumn=function(){return this.getOption("printMarginColumn")},e.prototype.getShowGutter=function(){return this.getOption("showGutter")},e.prototype.setShowGutter=function(e){return this.setOption("showGutter",e)},e.prototype.getFadeFoldWidgets=function(){return this.getOption("fadeFoldWidgets")},e.prototype.setFadeFoldWidgets=function(e){this.setOption("fadeFoldWidgets",e)},e.prototype.setHighlightGutterLine=function(e){this.setOption("highlightGutterLine",e)},e.prototype.getHighlightGutterLine=function(){return this.getOption("highlightGutterLine")},e.prototype.$updatePrintMargin=function(){if(this.$showPrintMargin||this.$printMarginEl){if(!this.$printMarginEl){var e=o.createElement("div");e.className="ace_layer ace_print-margin-layer",this.$printMarginEl=o.createElement("div"),this.$printMarginEl.className="ace_print-margin",e.appendChild(this.$printMarginEl),this.content.insertBefore(e,this.content.firstChild)}var t=this.$printMarginEl.style;t.left=Math.round(this.characterWidth*this.$printMarginColumn+this.$padding)+"px",t.visibility=this.$showPrintMargin?"visible":"hidden",this.session&&-1==this.session.$wrap&&this.adjustWrapLimit()}},e.prototype.getContainerElement=function(){return this.container},e.prototype.getMouseEventTarget=function(){return this.scroller},e.prototype.getTextAreaContainer=function(){return this.container},e.prototype.$moveTextAreaToCursor=function(){if(!this.$isMousePressed){var e=this.textarea.style,t=this.$composition;if(!this.$keepTextAreaAtCursor&&!t)return void o.translate(this.textarea,-100,0);var i=this.$cursorLayer.$pixelPos;if(i){t&&t.markerRange&&(i=this.$cursorLayer.getPixelPosition(t.markerRange.start,!0));var n=this.layerConfig,r=i.top,s=i.left;r-=n.offset;var a=t&&t.useTextareaForIME||b.isMobile?this.lineHeight:1;if(r<0||r>n.height-a)return void o.translate(this.textarea,0,0);var l=1,c=this.$size.height-a;if(t)if(t.useTextareaForIME){var h=this.textarea.value;l=this.characterWidth*this.session.$getStringScreenWidth(h)[0]}else r+=this.lineHeight+2;else r+=this.lineHeight;(s-=this.scrollLeft)>this.$size.scrollerWidth-l&&(s=this.$size.scrollerWidth-l),s+=this.gutterWidth+this.margin.left,o.setStyle(e,"height",a+"px"),o.setStyle(e,"width",l+"px"),o.translate(this.textarea,Math.min(s,this.$size.scrollerWidth-l),Math.min(r,c))}}},e.prototype.getFirstVisibleRow=function(){return this.layerConfig.firstRow},e.prototype.getFirstFullyVisibleRow=function(){return this.layerConfig.firstRow+ +(0!==this.layerConfig.offset)},e.prototype.getLastFullyVisibleRow=function(){var e=this.layerConfig,t=e.lastRow;return this.session.documentToScreenRow(t,0)*e.lineHeight-this.session.getScrollTop()>e.height-e.lineHeight?t-1:t},e.prototype.getLastVisibleRow=function(){return this.layerConfig.lastRow},e.prototype.setPadding=function(e){this.$padding=e,this.$textLayer.setPadding(e),this.$cursorLayer.setPadding(e),this.$markerFront.setPadding(e),this.$markerBack.setPadding(e),this.$loop.schedule(this.CHANGE_FULL),this.$updatePrintMargin()},e.prototype.setScrollMargin=function(e,t,i,n){var o=this.scrollMargin;o.top=0|e,o.bottom=0|t,o.right=0|n,o.left=0|i,o.v=o.top+o.bottom,o.h=o.left+o.right,o.top&&this.scrollTop<=0&&this.session&&this.session.setScrollTop(-o.top),this.updateFull()},e.prototype.setMargin=function(e,t,i,n){var o=this.margin;o.top=0|e,o.bottom=0|t,o.right=0|n,o.left=0|i,o.v=o.top+o.bottom,o.h=o.left+o.right,this.$updateCachedSize(!0,this.gutterWidth,this.$size.width,this.$size.height),this.updateFull()},e.prototype.getHScrollBarAlwaysVisible=function(){return this.$hScrollBarAlwaysVisible},e.prototype.setHScrollBarAlwaysVisible=function(e){this.setOption("hScrollBarAlwaysVisible",e)},e.prototype.getVScrollBarAlwaysVisible=function(){return this.$vScrollBarAlwaysVisible},e.prototype.setVScrollBarAlwaysVisible=function(e){this.setOption("vScrollBarAlwaysVisible",e)},e.prototype.$updateScrollBarV=function(){var e=this.layerConfig.maxHeight,t=this.$size.scrollerHeight;!this.$maxLines&&this.$scrollPastEnd&&(e-=(t-this.lineHeight)*this.$scrollPastEnd,this.scrollTop>e-t&&(e=this.scrollTop+t,this.scrollBarV.scrollTop=null)),this.scrollBarV.setScrollHeight(e+this.scrollMargin.v),this.scrollBarV.setScrollTop(this.scrollTop+this.scrollMargin.top)},e.prototype.$updateScrollBarH=function(){this.scrollBarH.setScrollWidth(this.layerConfig.width+2*this.$padding+this.scrollMargin.h),this.scrollBarH.setScrollLeft(this.scrollLeft+this.scrollMargin.left)},e.prototype.freeze=function(){this.$frozen=!0},e.prototype.unfreeze=function(){this.$frozen=!1},e.prototype.$renderChanges=function(e,t){if(this.$changes&&(e|=this.$changes,this.$changes=0),!this.session||!this.container.offsetWidth||this.$frozen||!e&&!t){this.$changes|=e;return}if(this.$size.$dirty)return this.$changes|=e,this.onResize(!0);this.lineHeight||this.$textLayer.checkForSizeChanges(),this._signal("beforeRender",e),this.session&&this.session.$bidiHandler&&this.session.$bidiHandler.updateCharacterWidths(this.$fontMetrics);var i=this.layerConfig;if(e&this.CHANGE_FULL||e&this.CHANGE_SIZE||e&this.CHANGE_TEXT||e&this.CHANGE_LINES||e&this.CHANGE_SCROLL||e&this.CHANGE_H_SCROLL){if(e|=this.$computeLayerConfig()|this.$loop.clear(),i.firstRow!=this.layerConfig.firstRow&&i.firstRowScreen==this.layerConfig.firstRowScreen){var n=this.scrollTop+(i.firstRow-Math.max(this.layerConfig.firstRow,0))*this.lineHeight;n>0&&(this.scrollTop=n,e|=this.CHANGE_SCROLL,e|=this.$computeLayerConfig()|this.$loop.clear())}i=this.layerConfig,this.$updateScrollBarV(),e&this.CHANGE_H_SCROLL&&this.$updateScrollBarH(),o.translate(this.content,-this.scrollLeft,-i.offset);var r=i.width+2*this.$padding+"px",s=i.minHeight+"px";o.setStyle(this.content.style,"width",r),o.setStyle(this.content.style,"height",s)}if(e&this.CHANGE_H_SCROLL&&(o.translate(this.content,-this.scrollLeft,-i.offset),this.scroller.className=this.scrollLeft<=0?"ace_scroller ":"ace_scroller ace_scroll-left ",this.enableKeyboardAccessibility&&(this.scroller.className+=this.keyboardFocusClassName)),e&this.CHANGE_FULL){this.$changedLines=null,this.$textLayer.update(i),this.$showGutter&&this.$gutterLayer.update(i),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(i),this.$markerBack.update(i),this.$markerFront.update(i),this.$cursorLayer.update(i),this.$moveTextAreaToCursor(),this._signal("afterRender",e);return}if(e&this.CHANGE_SCROLL){this.$changedLines=null,e&this.CHANGE_TEXT||e&this.CHANGE_LINES?this.$textLayer.update(i):this.$textLayer.scrollLines(i),this.$showGutter&&(e&this.CHANGE_GUTTER||e&this.CHANGE_LINES?this.$gutterLayer.update(i):this.$gutterLayer.scrollLines(i)),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(i),this.$markerBack.update(i),this.$markerFront.update(i),this.$cursorLayer.update(i),this.$moveTextAreaToCursor(),this._signal("afterRender",e);return}e&this.CHANGE_TEXT?(this.$changedLines=null,this.$textLayer.update(i),this.$showGutter&&this.$gutterLayer.update(i),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(i)):e&this.CHANGE_LINES?((this.$updateLines()||e&this.CHANGE_GUTTER&&this.$showGutter)&&this.$gutterLayer.update(i),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(i)):e&this.CHANGE_TEXT||e&this.CHANGE_GUTTER?(this.$showGutter&&this.$gutterLayer.update(i),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(i)):e&this.CHANGE_CURSOR&&(this.$highlightGutterLine&&this.$gutterLayer.updateLineHighlight(i),this.$customScrollbar&&this.$scrollDecorator.$updateDecorators(i)),e&this.CHANGE_CURSOR&&(this.$cursorLayer.update(i),this.$moveTextAreaToCursor()),e&(this.CHANGE_MARKER|this.CHANGE_MARKER_FRONT)&&this.$markerFront.update(i),e&(this.CHANGE_MARKER|this.CHANGE_MARKER_BACK)&&this.$markerBack.update(i),this._signal("afterRender",e)},e.prototype.$autosize=function(){var e=this.session.getScreenLength()*this.lineHeight,t=this.$maxLines*this.lineHeight,i=Math.min(t,Math.max((this.$minLines||1)*this.lineHeight,e))+this.scrollMargin.v+(this.$extraHeight||0);this.$horizScroll&&(i+=this.scrollBarH.getHeight()),this.$maxPixelHeight&&i>this.$maxPixelHeight&&(i=this.$maxPixelHeight);var n=!(i<=2*this.lineHeight)&&e>t;if(i!=this.desiredHeight||this.$size.height!=this.desiredHeight||n!=this.$vScroll){n!=this.$vScroll&&(this.$vScroll=n,this.scrollBarV.setVisible(n));var o=this.container.clientWidth;this.container.style.height=i+"px",this.$updateCachedSize(!0,this.$gutterWidth,o,i),this.desiredHeight=i,this._signal("autosize")}},e.prototype.$computeLayerConfig=function(){var e,t,i=this.session,n=this.$size,o=n.height<=2*this.lineHeight,r=this.session.getScreenLength()*this.lineHeight,s=this.$getLongestLine(),a=!o&&(this.$hScrollBarAlwaysVisible||n.scrollerWidth-s-2*this.$padding<0),l=this.$horizScroll!==a;l&&(this.$horizScroll=a,this.scrollBarH.setVisible(a));var c=this.$vScroll;this.$maxLines&&this.lineHeight>1&&(this.$autosize(),o=n.height<=2*this.lineHeight);var h=n.scrollerHeight+this.lineHeight,u=!this.$maxLines&&this.$scrollPastEnd?(n.scrollerHeight-this.lineHeight)*this.$scrollPastEnd:0;r+=u;var d=this.scrollMargin;this.session.setScrollTop(Math.max(-d.top,Math.min(this.scrollTop,r-n.scrollerHeight+d.bottom))),this.session.setScrollLeft(Math.max(-d.left,Math.min(this.scrollLeft,s+2*this.$padding-n.scrollerWidth+d.right)));var p=!o&&(this.$vScrollBarAlwaysVisible||n.scrollerHeight-r+u<0||this.scrollTop>d.top),g=c!==p;g&&(this.$vScroll=p,this.scrollBarV.setVisible(p));var f=this.scrollTop%this.lineHeight,m=Math.ceil(h/this.lineHeight)-1,y=Math.max(0,Math.round((this.scrollTop-f)/this.lineHeight)),v=y+m,w=this.lineHeight;y=i.screenToDocumentRow(y,0);var b=i.getFoldLine(y);b&&(y=b.start.row),e=i.documentToScreenRow(y,0),t=i.getRowLength(y)*w,v=Math.min(i.screenToDocumentRow(v,0),i.getLength()-1),h=n.scrollerHeight+i.getRowLength(v)*w+t,(f=this.scrollTop-e*w)<0&&e>0&&(e=Math.max(0,e+Math.floor(f/w)),f=this.scrollTop-e*w);var $=0;return(this.layerConfig.width!=s||l)&&($=this.CHANGE_H_SCROLL),(l||g)&&($|=this.$updateCachedSize(!0,this.gutterWidth,n.width,n.height),this._signal("scrollbarVisibilityChanged"),g&&(s=this.$getLongestLine())),this.layerConfig={width:s,padding:this.$padding,firstRow:y,firstRowScreen:e,lastRow:v,lineHeight:w,characterWidth:this.characterWidth,minHeight:h,maxHeight:r,offset:f,gutterOffset:w?Math.max(0,Math.ceil((f+n.height-n.scrollerHeight)/w)):0,height:this.$size.scrollerHeight},this.session.$bidiHandler&&this.session.$bidiHandler.setContentWidth(s-this.$padding),$},e.prototype.$updateLines=function(){if(this.$changedLines){var e=this.$changedLines.firstRow,t=this.$changedLines.lastRow;this.$changedLines=null;var i=this.layerConfig;if(!(e>i.lastRow+1)&&!(t<i.firstRow)){if(t===1/0){this.$showGutter&&this.$gutterLayer.update(i),this.$textLayer.update(i);return}return this.$textLayer.updateLines(i,e,t),!0}}},e.prototype.$getLongestLine=function(){var e=this.session.getScreenWidth();return this.showInvisibles&&!this.session.$useWrapMode&&(e+=1),this.$textLayer&&e>this.$textLayer.MAX_LINE_LENGTH&&(e=this.$textLayer.MAX_LINE_LENGTH+30),Math.max(this.$size.scrollerWidth-2*this.$padding,Math.round(e*this.characterWidth))},e.prototype.updateFrontMarkers=function(){this.$markerFront.setMarkers(this.session.getMarkers(!0)),this.$loop.schedule(this.CHANGE_MARKER_FRONT)},e.prototype.updateBackMarkers=function(){this.$markerBack.setMarkers(this.session.getMarkers()),this.$loop.schedule(this.CHANGE_MARKER_BACK)},e.prototype.addGutterDecoration=function(e,t){this.$gutterLayer.addGutterDecoration(e,t)},e.prototype.removeGutterDecoration=function(e,t){this.$gutterLayer.removeGutterDecoration(e,t)},e.prototype.updateBreakpoints=function(e){this._rows=e,this.$loop.schedule(this.CHANGE_GUTTER)},e.prototype.setAnnotations=function(e){this.$gutterLayer.setAnnotations(e),this.$loop.schedule(this.CHANGE_GUTTER)},e.prototype.updateCursor=function(){this.$loop.schedule(this.CHANGE_CURSOR)},e.prototype.hideCursor=function(){this.$cursorLayer.hideCursor()},e.prototype.showCursor=function(){this.$cursorLayer.showCursor()},e.prototype.scrollSelectionIntoView=function(e,t,i){this.scrollCursorIntoView(e,i),this.scrollCursorIntoView(t,i)},e.prototype.scrollCursorIntoView=function(e,t,i){if(0!==this.$size.scrollerHeight){var n=this.$cursorLayer.getPixelPosition(e),o=n.left,r=n.top,s=i&&i.top||0,a=i&&i.bottom||0;this.$scrollAnimation&&(this.$stopAnimation=!0);var l=this.$scrollAnimation?this.session.getScrollTop():this.scrollTop;l+s>r?(t&&l+s>r+this.lineHeight&&(r-=t*this.$size.scrollerHeight),0===r&&(r=-this.scrollMargin.top),this.session.setScrollTop(r)):l+this.$size.scrollerHeight-a<r+this.lineHeight&&(t&&l+this.$size.scrollerHeight-a<r-this.lineHeight&&(r+=t*this.$size.scrollerHeight),this.session.setScrollTop(r+this.lineHeight+a-this.$size.scrollerHeight));var c=this.scrollLeft,h=2*this.layerConfig.characterWidth;o-h<c?((o-=h)<this.$padding+h&&(o=-this.scrollMargin.left),this.session.setScrollLeft(o)):(o+=h,c+this.$size.scrollerWidth<o+this.characterWidth?this.session.setScrollLeft(Math.round(o+this.characterWidth-this.$size.scrollerWidth)):c<=this.$padding&&o-c<this.characterWidth&&this.session.setScrollLeft(0))}},e.prototype.getScrollTop=function(){return this.session.getScrollTop()},e.prototype.getScrollLeft=function(){return this.session.getScrollLeft()},e.prototype.getScrollTopRow=function(){return this.scrollTop/this.lineHeight},e.prototype.getScrollBottomRow=function(){return Math.max(0,Math.floor((this.scrollTop+this.$size.scrollerHeight)/this.lineHeight)-1)},e.prototype.scrollToRow=function(e){this.session.setScrollTop(e*this.lineHeight)},e.prototype.alignCursor=function(e,t){"number"==typeof e&&(e={row:e,column:0});var i=this.$cursorLayer.getPixelPosition(e),n=this.$size.scrollerHeight-this.lineHeight,o=i.top-n*(t||0);return this.session.setScrollTop(o),o},e.prototype.$calcSteps=function(e,t){var i,n=0,o=this.STEPS,r=[];for(n=0;n<o;++n)r.push((i=n/this.STEPS,(t-e)*(Math.pow(i-1,3)+1)+e));return r},e.prototype.scrollToLine=function(e,t,i,n){var o=this.$cursorLayer.getPixelPosition({row:e,column:0}).top;t&&(o-=this.$size.scrollerHeight/2);var r=this.scrollTop;this.session.setScrollTop(o),!1!==i&&this.animateScrolling(r,n)},e.prototype.animateScrolling=function(e,t){var i=this.scrollTop;if(this.$animatedScroll){var n=this;if(e!=i){if(this.$scrollAnimation){var o=this.$scrollAnimation.steps;if(o.length&&(e=o[0])==i)return}var r=n.$calcSteps(e,i);this.$scrollAnimation={from:e,to:i,steps:r},clearInterval(this.$timer),n.session.setScrollTop(r.shift()),n.session.$scrollTop=i,this.$timer=setInterval(function(){return n.$stopAnimation?void s():n.session?void(r.length?(n.session.setScrollTop(r.shift()),n.session.$scrollTop=i):null!=i?(n.session.$scrollTop=-1,n.session.setScrollTop(i),i=null):s()):clearInterval(n.$timer)},10)}}function s(){n.$timer=clearInterval(n.$timer),n.$scrollAnimation=null,n.$stopAnimation=!1,t&&t()}},e.prototype.scrollToY=function(e){this.scrollTop!==e&&(this.$loop.schedule(this.CHANGE_SCROLL),this.scrollTop=e)},e.prototype.scrollToX=function(e){this.scrollLeft!==e&&(this.scrollLeft=e),this.$loop.schedule(this.CHANGE_H_SCROLL)},e.prototype.scrollTo=function(e,t){this.session.setScrollTop(t),this.session.setScrollLeft(e)},e.prototype.scrollBy=function(e,t){t&&this.session.setScrollTop(this.session.getScrollTop()+t),e&&this.session.setScrollLeft(this.session.getScrollLeft()+e)},e.prototype.isScrollableBy=function(e,t){if(t<0&&this.session.getScrollTop()>=1-this.scrollMargin.top||t>0&&this.session.getScrollTop()+this.$size.scrollerHeight-this.layerConfig.maxHeight<-1+this.scrollMargin.bottom||e<0&&this.session.getScrollLeft()>=1-this.scrollMargin.left||e>0&&this.session.getScrollLeft()+this.$size.scrollerWidth-this.layerConfig.width<-1+this.scrollMargin.right)return!0},e.prototype.pixelToScreenCoordinates=function(e,t){if(this.$hasCssTransforms){var i={top:0,left:0},n=this.$fontMetrics.transformCoordinates([e,t]);e=n[1]-this.gutterWidth-this.margin.left,t=n[0]}else i=this.scroller.getBoundingClientRect();var o=e+this.scrollLeft-i.left-this.$padding,r=o/this.characterWidth,s=Math.floor((t+this.scrollTop-i.top)/this.lineHeight),a=this.$blockCursor?Math.floor(r):Math.round(r);return{row:s,column:a,side:r-a>0?1:-1,offsetX:o}},e.prototype.screenToTextCoordinates=function(e,t){if(this.$hasCssTransforms){var i={top:0,left:0},n=this.$fontMetrics.transformCoordinates([e,t]);e=n[1]-this.gutterWidth-this.margin.left,t=n[0]}else i=this.scroller.getBoundingClientRect();var o=e+this.scrollLeft-i.left-this.$padding,r=o/this.characterWidth,s=this.$blockCursor?Math.floor(r):Math.round(r),a=Math.floor((t+this.scrollTop-i.top)/this.lineHeight);return this.session.screenToDocumentPosition(a,Math.max(s,0),o)},e.prototype.textToScreenCoordinates=function(e,t){var i=this.scroller.getBoundingClientRect(),n=this.session.documentToScreenPosition(e,t),o=this.$padding+(this.session.$bidiHandler.isBidiRow(n.row,e)?this.session.$bidiHandler.getPosLeft(n.column):Math.round(n.column*this.characterWidth)),r=n.row*this.lineHeight;return{pageX:i.left+o-this.scrollLeft,pageY:i.top+r-this.scrollTop}},e.prototype.visualizeFocus=function(){o.addCssClass(this.container,"ace_focus")},e.prototype.visualizeBlur=function(){o.removeCssClass(this.container,"ace_focus")},e.prototype.showComposition=function(e){this.$composition=e,e.cssText||(e.cssText=this.textarea.style.cssText),void 0==e.useTextareaForIME&&(e.useTextareaForIME=this.$useTextareaForIME),this.$useTextareaForIME?(o.addCssClass(this.textarea,"ace_composition"),this.textarea.style.cssText="",this.$moveTextAreaToCursor(),this.$cursorLayer.element.style.display="none"):e.markerId=this.session.addMarker(e.markerRange,"ace_composition_marker","text")},e.prototype.setCompositionText=function(e){var t=this.session.selection.cursor;this.addToken(e,"composition_placeholder",t.row,t.column),this.$moveTextAreaToCursor()},e.prototype.hideComposition=function(){if(this.$composition){this.$composition.markerId&&this.session.removeMarker(this.$composition.markerId),o.removeCssClass(this.textarea,"ace_composition"),this.textarea.style.cssText=this.$composition.cssText;var e=this.session.selection.cursor;this.removeExtraToken(e.row,e.column),this.$composition=null,this.$cursorLayer.element.style.display=""}},e.prototype.setGhostText=function(e,t){var i=this.session.selection.cursor,n=t||{row:i.row,column:i.column};this.removeGhostText();var r=this.$calculateWrappedTextChunks(e,n);this.addToken(r[0].text,"ghost_text",n.row,n.column),this.$ghostText={text:e,position:{row:n.row,column:n.column}};var s=o.createElement("div");if(r.length>1){var a,l=this.hideTokensAfterPosition(n.row,n.column);r.slice(1).forEach(function(e){var t=o.createElement("div"),i=o.createElement("span");i.className="ace_ghost_text",e.wrapped&&(t.className="ghost_text_line_wrapped"),0===e.text.length&&(e.text=" "),i.appendChild(o.createTextNode(e.text)),t.appendChild(i),s.appendChild(t),a=t}),l.forEach(function(e){var t=o.createElement("span");$(e.type)||(t.className="ace_"+e.type.replace(/\./g," ace_")),t.appendChild(o.createTextNode(e.value)),a.appendChild(t)}),this.$ghostTextWidget={el:s,row:n.row,column:n.column,className:"ace_ghost_text_container"},this.session.widgetManager.addLineWidget(this.$ghostTextWidget);var c=this.$cursorLayer.getPixelPosition(n,!0),h=this.container.getBoundingClientRect().height,u=r.length*this.lineHeight;if(u<h-c.top)return;u<h?this.scrollBy(0,(r.length-1)*this.lineHeight):this.scrollToRow(n.row)}},e.prototype.$calculateWrappedTextChunks=function(e,t){var i=Math.floor((this.$size.scrollerWidth-2*this.$padding)/this.characterWidth)-2;i=i<=0?60:i;for(var n=e.split(/\r?\n/),o=[],r=0;r<n.length;r++){var s=this.session.$getDisplayTokens(n[r],t.column),a=this.session.$computeWrapSplits(s,i,this.session.$tabSize);if(a.length>0){var l=0;a.push(n[r].length);for(var c=0;c<a.length;c++){var h=n[r].slice(l,a[c]);o.push({text:h,wrapped:!0}),l=a[c]}}else o.push({text:n[r],wrapped:!1})}return o},e.prototype.removeGhostText=function(){if(this.$ghostText){var e=this.$ghostText.position;this.removeExtraToken(e.row,e.column),this.$ghostTextWidget&&(this.session.widgetManager.removeLineWidget(this.$ghostTextWidget),this.$ghostTextWidget=null),this.$ghostText=null}},e.prototype.addToken=function(e,t,i,n){var o=this.session;o.bgTokenizer.lines[i]=null;var r={type:t,value:e},s=o.getTokens(i);if(null!=n&&s.length)for(var a=0,l=0;l<s.length;l++){var c=s[l];if(n<=(a+=c.value.length)){var h=c.value.length-(a-n),u=c.value.slice(0,h),d=c.value.slice(h);s.splice(l,1,{type:c.type,value:u},r,{type:c.type,value:d});break}}else s.push(r);this.updateLines(i,i)},e.prototype.hideTokensAfterPosition=function(e,t){for(var i=this.session.getTokens(e),n=0,o=!1,r=[],s=0;s<i.length;s++){var a=i[s];if(n+=a.value.length,"ghost_text"!==a.type){if(o){r.push({type:a.type,value:a.value}),a.type="hidden_token";continue}n===t&&(o=!0)}}return this.updateLines(e,e),r},e.prototype.removeExtraToken=function(e,t){this.session.bgTokenizer.lines[e]=null,this.updateLines(e,e)},e.prototype.setTheme=function(e,t){var i=this;if(this.$themeId=e,i._dispatchEvent("themeChange",{theme:e}),e&&"string"!=typeof e)r(e);else{var n=e||this.$options.theme.initialValue;s.loadModule(["theme",n],r)}function r(n){if(i.$themeId!=e)return t&&t();if(!n||!n.cssClass)throw Error("couldn't load module "+e+" or it didn't call define");n.$id&&(i.$themeId=n.$id),o.importCssString(n.cssText,n.cssClass,i.container),i.theme&&o.removeCssClass(i.container,i.theme.cssClass);var r="padding"in n?n.padding:"padding"in(i.theme||{})?4:i.$padding;i.$padding&&r!=i.$padding&&i.setPadding(r),i.$theme=n.cssClass,i.theme=n,o.addCssClass(i.container,n.cssClass),o.setCssClass(i.container,"ace_dark",n.isDark),i.$size&&(i.$size.width=0,i.$updateSizeAsync()),i._dispatchEvent("themeLoaded",{theme:n}),t&&t(),b.isSafari&&i.scroller&&(i.scroller.style.background="red",i.scroller.style.background="")}},e.prototype.getTheme=function(){return this.$themeId},e.prototype.setStyle=function(e,t){o.setCssClass(this.container,e,!1!==t)},e.prototype.unsetStyle=function(e){o.removeCssClass(this.container,e)},e.prototype.setCursorStyle=function(e){o.setStyle(this.scroller.style,"cursor",e)},e.prototype.setMouseCursor=function(e){o.setStyle(this.scroller.style,"cursor",e)},e.prototype.attachToShadowRoot=function(){o.importCssString(v,"ace_editor.css",this.container)},e.prototype.destroy=function(){this.freeze(),this.$fontMetrics.destroy(),this.$cursorLayer.destroy(),this.removeAllListeners(),this.container.textContent="",this.setOption("useResizeObserver",!1)},e.prototype.$updateCustomScrollbar=function(e){var t=this;this.$horizScroll=this.$vScroll=null,this.scrollBarV.element.remove(),this.scrollBarH.element.remove(),!0===e?(this.scrollBarV=new g(this.container,this),this.scrollBarH=new p(this.container,this),this.scrollBarV.setHeight(this.$size.scrollerHeight),this.scrollBarH.setWidth(this.$size.scrollerWidth),this.scrollBarV.addEventListener("scroll",function(e){t.$scrollAnimation||t.session.setScrollTop(e.data-t.scrollMargin.top)}),this.scrollBarH.addEventListener("scroll",function(e){t.$scrollAnimation||t.session.setScrollLeft(e.data-t.scrollMargin.left)}),this.$scrollDecorator?this.$scrollDecorator.setScrollBarV(this.scrollBarV):this.$scrollDecorator=new w(this.scrollBarV,this),this.$scrollDecorator.$updateDecorators()):(this.scrollBarV=new d(this.container,this),this.scrollBarH=new u(this.container,this),this.scrollBarV.addEventListener("scroll",function(e){t.$scrollAnimation||t.session.setScrollTop(e.data-t.scrollMargin.top)}),this.scrollBarH.addEventListener("scroll",function(e){t.$scrollAnimation||t.session.setScrollLeft(e.data-t.scrollMargin.left)}))},e.prototype.$addResizeObserver=function(){if(window.ResizeObserver&&!this.$resizeObserver){var e=this;this.$resizeTimer=r.delayedCall(function(){e.destroyed||e.onResize()},50),this.$resizeObserver=new window.ResizeObserver(function(t){var i=t[0].contentRect.width,n=t[0].contentRect.height;Math.abs(e.$size.width-i)>1||Math.abs(e.$size.height-n)>1?e.$resizeTimer.delay():e.$resizeTimer.cancel()}),this.$resizeObserver.observe(this.container)}},e}();C.prototype.CHANGE_CURSOR=1,C.prototype.CHANGE_MARKER=2,C.prototype.CHANGE_GUTTER=4,C.prototype.CHANGE_SCROLL=8,C.prototype.CHANGE_LINES=16,C.prototype.CHANGE_TEXT=32,C.prototype.CHANGE_SIZE=64,C.prototype.CHANGE_MARKER_BACK=128,C.prototype.CHANGE_MARKER_FRONT=256,C.prototype.CHANGE_FULL=512,C.prototype.CHANGE_H_SCROLL=1024,C.prototype.$changes=0,C.prototype.$padding=null,C.prototype.$frozen=!1,C.prototype.STEPS=8,n.implement(C.prototype,y),s.defineOptions(C.prototype,"renderer",{useResizeObserver:{set:function(e){!e&&this.$resizeObserver?(this.$resizeObserver.disconnect(),this.$resizeTimer.cancel(),this.$resizeTimer=this.$resizeObserver=null):e&&!this.$resizeObserver&&this.$addResizeObserver()}},animatedScroll:{initialValue:!1},showInvisibles:{set:function(e){this.$textLayer.setShowInvisibles(e)&&this.$loop.schedule(this.CHANGE_TEXT)},initialValue:!1},showPrintMargin:{set:function(){this.$updatePrintMargin()},initialValue:!0},printMarginColumn:{set:function(){this.$updatePrintMargin()},initialValue:80},printMargin:{set:function(e){"number"==typeof e&&(this.$printMarginColumn=e),this.$showPrintMargin=!!e,this.$updatePrintMargin()},get:function(){return this.$showPrintMargin&&this.$printMarginColumn}},showGutter:{set:function(e){this.$gutter.style.display=e?"block":"none",this.$loop.schedule(this.CHANGE_FULL),this.onGutterResize()},initialValue:!0},useSvgGutterIcons:{set:function(e){this.$gutterLayer.$useSvgGutterIcons=e},initialValue:!1},showFoldedAnnotations:{set:function(e){this.$gutterLayer.$showFoldedAnnotations=e},initialValue:!1},fadeFoldWidgets:{set:function(e){o.setCssClass(this.$gutter,"ace_fade-fold-widgets",e)},initialValue:!1},showFoldWidgets:{set:function(e){this.$gutterLayer.setShowFoldWidgets(e),this.$loop.schedule(this.CHANGE_GUTTER)},initialValue:!0},displayIndentGuides:{set:function(e){this.$textLayer.setDisplayIndentGuides(e)&&this.$loop.schedule(this.CHANGE_TEXT)},initialValue:!0},highlightIndentGuides:{set:function(e){!0==this.$textLayer.setHighlightIndentGuides(e)?this.$textLayer.$highlightIndentGuide():this.$textLayer.$clearActiveIndentGuide(this.$textLayer.$lines.cells)},initialValue:!0},highlightGutterLine:{set:function(e){this.$gutterLayer.setHighlightGutterLine(e),this.$loop.schedule(this.CHANGE_GUTTER)},initialValue:!0},hScrollBarAlwaysVisible:{set:function(e){this.$hScrollBarAlwaysVisible&&this.$horizScroll||this.$loop.schedule(this.CHANGE_SCROLL)},initialValue:!1},vScrollBarAlwaysVisible:{set:function(e){this.$vScrollBarAlwaysVisible&&this.$vScroll||this.$loop.schedule(this.CHANGE_SCROLL)},initialValue:!1},fontSize:{set:function(e){"number"==typeof e&&(e+="px"),this.container.style.fontSize=e,this.updateFontSize()},initialValue:12},fontFamily:{set:function(e){this.container.style.fontFamily=e,this.updateFontSize()}},maxLines:{set:function(e){this.updateFull()}},minLines:{set:function(e){this.$minLines<0x1ffffffffffff||(this.$minLines=0),this.updateFull()}},maxPixelHeight:{set:function(e){this.updateFull()},initialValue:0},scrollPastEnd:{set:function(e){e=+e||0,this.$scrollPastEnd!=e&&(this.$scrollPastEnd=e,this.$loop.schedule(this.CHANGE_SCROLL))},initialValue:0,handlesSet:!0},fixedWidthGutter:{set:function(e){this.$gutterLayer.$fixedWidth=!!e,this.$loop.schedule(this.CHANGE_GUTTER)}},customScrollbar:{set:function(e){this.$updateCustomScrollbar(e)},initialValue:!1},theme:{set:function(e){this.setTheme(e)},get:function(){return this.$themeId||this.theme},initialValue:"./theme/textmate",handlesSet:!0},hasCssTransforms:{},useTextareaForIME:{initialValue:!b.isMobile&&!b.isIE}}),t.VirtualRenderer=C}),ace.define("ace/worker/worker_client",["require","exports","module","ace/lib/oop","ace/lib/net","ace/lib/event_emitter","ace/config"],function(e,t,i){"use strict";var n=e("../lib/oop"),o=e("../lib/net"),r=e("../lib/event_emitter").EventEmitter,s=e("../config");function a(e){if("undefined"==typeof Worker)return{postMessage:function(){},terminate:function(){}};if(s.get("loadWorkerFromBlob")){var t=function(e){var t="importScripts('"+o.qualifyURL(e)+"');";try{return new Blob([t],{type:"application/javascript"})}catch(e){var i=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder);return i.append(t),i.getBlob("application/javascript")}}(e);return new Worker((window.URL||window.webkitURL).createObjectURL(t))}return new Worker(e)}var l=function(e){e.postMessage||(e=this.$createWorkerFromOldConfig.apply(this,arguments)),this.$worker=e,this.$sendDeltaQueue=this.$sendDeltaQueue.bind(this),this.changeListener=this.changeListener.bind(this),this.onMessage=this.onMessage.bind(this),this.callbackId=1,this.callbacks={},this.$worker.onmessage=this.onMessage};(function(){n.implement(this,r),this.$createWorkerFromOldConfig=function(t,i,n,o,r){if(e.nameToUrl&&!e.toUrl&&(e.toUrl=e.nameToUrl),s.get("packaged")||!e.toUrl)o=o||s.moduleUrl(i,"worker");else{var l=this.$normalizePath;o=o||l(e.toUrl("ace/worker/worker.js",null,"_"));var c={};t.forEach(function(t){c[t]=l(e.toUrl(t,null,"_").replace(/(\.js)?(\?.*)?$/,""))})}return this.$worker=a(o),r&&this.send("importScripts",r),this.$worker.postMessage({init:!0,tlns:c,module:i,classname:n}),this.$worker},this.onMessage=function(e){var t=e.data;switch(t.type){case"event":this._signal(t.name,{data:t.data});break;case"call":var i=this.callbacks[t.id];i&&(i(t.data),delete this.callbacks[t.id]);break;case"error":this.reportError(t.data);break;case"log":window.console&&console.log&&console.log.apply(console,t.data)}},this.reportError=function(e){window.console&&console.error&&console.error(e)},this.$normalizePath=function(e){return o.qualifyURL(e)},this.terminate=function(){this._signal("terminate",{}),this.deltaQueue=null,this.$worker.terminate(),this.$worker.onerror=function(e){e.preventDefault()},this.$worker=null,this.$doc&&this.$doc.off("change",this.changeListener),this.$doc=null},this.send=function(e,t){this.$worker.postMessage({command:e,args:t})},this.call=function(e,t,i){if(i){var n=this.callbackId++;this.callbacks[n]=i,t.push(n)}this.send(e,t)},this.emit=function(e,t){try{t.data&&t.data.err&&(t.data.err={message:t.data.err.message,stack:t.data.err.stack,code:t.data.err.code}),this.$worker&&this.$worker.postMessage({event:e,data:{data:t.data}})}catch(e){console.error(e.stack)}},this.attachToDocument=function(e){this.$doc&&this.terminate(),this.$doc=e,this.call("setValue",[e.getValue()]),e.on("change",this.changeListener,!0)},this.changeListener=function(e){this.deltaQueue||(this.deltaQueue=[],setTimeout(this.$sendDeltaQueue,0)),"insert"==e.action?this.deltaQueue.push(e.start,e.lines):this.deltaQueue.push(e.start,e.end)},this.$sendDeltaQueue=function(){var e=this.deltaQueue;e&&(this.deltaQueue=null,e.length>50&&e.length>this.$doc.getLength()>>1?this.call("setValue",[this.$doc.getValue()]):this.emit("change",{data:e}))}}).call(l.prototype),t.UIWorkerClient=function(e,t,i){var n=null,o=!1,a=Object.create(r),c=[],h=new l({messageBuffer:c,terminate:function(){},postMessage:function(e){c.push(e),n&&(o?setTimeout(u):u())}});h.setEmitSync=function(e){o=e};var u=function(){var e=c.shift();e.command?n[e.command].apply(n,e.args):e.event&&a._signal(e.event,e.data)};return a.postMessage=function(e){h.onMessage({data:e})},a.callback=function(e,t){this.postMessage({type:"call",id:t,data:e})},a.emit=function(e,t){this.postMessage({type:"event",name:e,data:t})},s.loadModule(["worker",t],function(e){for(n=new e[i](a);c.length;)u()}),h},t.WorkerClient=l,t.createWorker=a}),ace.define("ace/placeholder",["require","exports","module","ace/range","ace/lib/event_emitter","ace/lib/oop"],function(e,t,i){"use strict";var n=e("./range").Range,o=e("./lib/event_emitter").EventEmitter,r=e("./lib/oop"),s=function(){function e(e,t,i,n,o,r){var s=this;this.length=t,this.session=e,this.doc=e.getDocument(),this.mainClass=o,this.othersClass=r,this.$onUpdate=this.onUpdate.bind(this),this.doc.on("change",this.$onUpdate,!0),this.$others=n,this.$onCursorChange=function(){setTimeout(function(){s.onCursorChange()})},this.$pos=i;var a=e.getUndoManager().$undoStack||e.getUndoManager().$undostack||{length:-1};this.$undoStackDepth=a.length,this.setup(),e.selection.on("changeCursor",this.$onCursorChange)}return e.prototype.setup=function(){var e=this,t=this.doc,i=this.session;this.selectionBefore=i.selection.toJSON(),i.selection.inMultiSelectMode&&i.selection.toSingleRange(),this.pos=t.createAnchor(this.$pos.row,this.$pos.column);var o=this.pos;o.$insertRight=!0,o.detach(),o.markerId=i.addMarker(new n(o.row,o.column,o.row,o.column+this.length),this.mainClass,null,!1),this.others=[],this.$others.forEach(function(i){var n=t.createAnchor(i.row,i.column);n.$insertRight=!0,n.detach(),e.others.push(n)}),i.setUndoSelect(!1)},e.prototype.showOtherMarkers=function(){if(!this.othersActive){var e=this.session,t=this;this.othersActive=!0,this.others.forEach(function(i){i.markerId=e.addMarker(new n(i.row,i.column,i.row,i.column+t.length),t.othersClass,null,!1)})}},e.prototype.hideOtherMarkers=function(){if(this.othersActive){this.othersActive=!1;for(var e=0;e<this.others.length;e++)this.session.removeMarker(this.others[e].markerId)}},e.prototype.onUpdate=function(e){if(this.$updating)return this.updateAnchors(e);if(e.start.row===e.end.row&&e.start.row===this.pos.row){this.$updating=!0;var t="insert"===e.action?e.end.column-e.start.column:e.start.column-e.end.column,i=e.start.column>=this.pos.column&&e.start.column<=this.pos.column+this.length+1,o=e.start.column-this.pos.column;if(this.updateAnchors(e),i&&(this.length+=t),i&&!this.session.$fromUndo){if("insert"===e.action)for(var r=this.others.length-1;r>=0;r--){var s=this.others[r],a={row:s.row,column:s.column+o};this.doc.insertMergedLines(a,e.lines)}else if("remove"===e.action)for(var r=this.others.length-1;r>=0;r--){var s=this.others[r],a={row:s.row,column:s.column+o};this.doc.remove(new n(a.row,a.column,a.row,a.column-t))}}this.$updating=!1,this.updateMarkers()}},e.prototype.updateAnchors=function(e){this.pos.onChange(e);for(var t=this.others.length;t--;)this.others[t].onChange(e);this.updateMarkers()},e.prototype.updateMarkers=function(){if(!this.$updating){var e=this,t=this.session,i=function(i,o){t.removeMarker(i.markerId),i.markerId=t.addMarker(new n(i.row,i.column,i.row,i.column+e.length),o,null,!1)};i(this.pos,this.mainClass);for(var o=this.others.length;o--;)i(this.others[o],this.othersClass)}},e.prototype.onCursorChange=function(e){if(!this.$updating&&this.session){var t=this.session.selection.getCursor();t.row===this.pos.row&&t.column>=this.pos.column&&t.column<=this.pos.column+this.length?(this.showOtherMarkers(),this._emit("cursorEnter",e)):(this.hideOtherMarkers(),this._emit("cursorLeave",e))}},e.prototype.detach=function(){this.session.removeMarker(this.pos&&this.pos.markerId),this.hideOtherMarkers(),this.doc.off("change",this.$onUpdate),this.session.selection.off("changeCursor",this.$onCursorChange),this.session.setUndoSelect(!0),this.session=null},e.prototype.cancel=function(){if(-1!==this.$undoStackDepth){for(var e=this.session.getUndoManager(),t=(e.$undoStack||e.$undostack).length-this.$undoStackDepth,i=0;i<t;i++)e.undo(this.session,!0);this.selectionBefore&&this.session.selection.fromJSON(this.selectionBefore)}},e}();r.implement(s.prototype,o),t.PlaceHolder=s}),ace.define("ace/mouse/multi_select_handler",["require","exports","module","ace/lib/event","ace/lib/useragent"],function(e,t,i){var n=e("../lib/event"),o=e("../lib/useragent");function r(e,t){return e.row==t.row&&e.column==t.column}t.onMouseDown=function(e){var t=e.domEvent,i=t.altKey,s=t.shiftKey,a=t.ctrlKey,l=e.getAccelKey(),c=e.getButton();if(a&&o.isMac&&(c=t.button),e.editor.inMultiSelectMode&&2==c)return void e.editor.textInput.onContextMenu(e.domEvent);if(!a&&!i&&!l){0===c&&e.editor.inMultiSelectMode&&e.editor.exitMultiSelectMode();return}if(0===c){var h=e.editor,u=h.selection,d=h.inMultiSelectMode,p=e.getDocumentPosition(),g=u.getCursor(),f=e.inSelection()||u.isEmpty()&&r(p,g),m=e.x,y=e.y,v=h.session,w=h.renderer.pixelToScreenCoordinates(m,y),b=w;if(h.$mouseHandler.$enableJumpToDef)a&&i||l&&i?S=s?"block":"add":i&&h.$blockSelectEnabled&&(S="block");else if(l&&!i){if(S="add",!d&&s)return}else i&&h.$blockSelectEnabled&&(S="block");if(S&&o.isMac&&t.ctrlKey&&h.$mouseHandler.cancelContextMenu(),"add"==S){if(!d&&f)return;if(!d){var $=u.toOrientedRange();h.addSelectionMarker($)}var C=u.rangeList.rangeAtPoint(p);h.inVirtualSelectionMode=!0,s&&(C=null,$=u.ranges[0]||$,h.removeSelectionMarker($)),h.once("mouseup",function(){var e=u.toOrientedRange();C&&e.isEmpty()&&r(C.cursor,e.cursor)?u.substractPoint(e.cursor):(s?u.substractPoint($.cursor):$&&(h.removeSelectionMarker($),u.addRange($)),u.addRange(e)),h.inVirtualSelectionMode=!1})}else if("block"==S){e.stop(),h.inVirtualSelectionMode=!0;var S,x,A=[],M=function(){var e=h.renderer.pixelToScreenCoordinates(m,y),t=v.screenToDocumentPosition(e.row,e.column,e.offsetX);r(b,e)&&r(t,u.lead)||(b=e,h.selection.moveToPosition(t),h.renderer.scrollCursorIntoView(),h.removeSelectionMarkers(A),A=u.rectangularRangeBlock(b,w),h.$mouseHandler.$clickSelection&&1==A.length&&A[0].isEmpty()&&(A[0]=h.$mouseHandler.$clickSelection.clone()),A.forEach(h.addSelectionMarker,h),h.updateSelectionMarkers())};d&&!l?u.toSingleRange():!d&&l&&(x=u.toOrientedRange(),h.addSelectionMarker(x)),s?w=v.documentToScreenPosition(u.lead):u.moveToPosition(p),b={row:-1,column:-1},n.capture(h.container,function(e){m=e.clientX,y=e.clientY},function(e){M(),clearInterval(k),h.removeSelectionMarkers(A),A.length||(A=[u.toOrientedRange()]),x&&(h.removeSelectionMarker(x),u.toSingleRange(x));for(var t=0;t<A.length;t++)u.addRange(A[t]);h.inVirtualSelectionMode=!1,h.$mouseHandler.$clickSelection=null});var k=setInterval(function(){M()},20);return e.preventDefault()}}}}),ace.define("ace/commands/multi_select_commands",["require","exports","module","ace/keyboard/hash_handler"],function(e,t,i){t.defaultCommands=[{name:"addCursorAbove",description:"Add cursor above",exec:function(e){e.selectMoreLines(-1)},bindKey:{win:"Ctrl-Alt-Up",mac:"Ctrl-Alt-Up"},scrollIntoView:"cursor",readOnly:!0},{name:"addCursorBelow",description:"Add cursor below",exec:function(e){e.selectMoreLines(1)},bindKey:{win:"Ctrl-Alt-Down",mac:"Ctrl-Alt-Down"},scrollIntoView:"cursor",readOnly:!0},{name:"addCursorAboveSkipCurrent",description:"Add cursor above (skip current)",exec:function(e){e.selectMoreLines(-1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Up",mac:"Ctrl-Alt-Shift-Up"},scrollIntoView:"cursor",readOnly:!0},{name:"addCursorBelowSkipCurrent",description:"Add cursor below (skip current)",exec:function(e){e.selectMoreLines(1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Down",mac:"Ctrl-Alt-Shift-Down"},scrollIntoView:"cursor",readOnly:!0},{name:"selectMoreBefore",description:"Select more before",exec:function(e){e.selectMore(-1)},bindKey:{win:"Ctrl-Alt-Left",mac:"Ctrl-Alt-Left"},scrollIntoView:"cursor",readOnly:!0},{name:"selectMoreAfter",description:"Select more after",exec:function(e){e.selectMore(1)},bindKey:{win:"Ctrl-Alt-Right",mac:"Ctrl-Alt-Right"},scrollIntoView:"cursor",readOnly:!0},{name:"selectNextBefore",description:"Select next before",exec:function(e){e.selectMore(-1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Left",mac:"Ctrl-Alt-Shift-Left"},scrollIntoView:"cursor",readOnly:!0},{name:"selectNextAfter",description:"Select next after",exec:function(e){e.selectMore(1,!0)},bindKey:{win:"Ctrl-Alt-Shift-Right",mac:"Ctrl-Alt-Shift-Right"},scrollIntoView:"cursor",readOnly:!0},{name:"toggleSplitSelectionIntoLines",description:"Split selection into lines",exec:function(e){e.multiSelect.rangeCount>1?e.multiSelect.joinSelections():e.multiSelect.splitIntoLines()},bindKey:{win:"Ctrl-Alt-L",mac:"Ctrl-Alt-L"},readOnly:!0},{name:"splitSelectionIntoLines",description:"Split into lines",exec:function(e){e.multiSelect.splitIntoLines()},readOnly:!0},{name:"alignCursors",description:"Align cursors",exec:function(e){e.alignCursors()},bindKey:{win:"Ctrl-Alt-A",mac:"Ctrl-Alt-A"},scrollIntoView:"cursor"},{name:"findAll",description:"Find all",exec:function(e){e.findAll()},bindKey:{win:"Ctrl-Alt-K",mac:"Ctrl-Alt-G"},scrollIntoView:"cursor",readOnly:!0}],t.multiSelectCommands=[{name:"singleSelection",description:"Single selection",bindKey:"esc",exec:function(e){e.exitMultiSelectMode()},scrollIntoView:"cursor",readOnly:!0,isAvailable:function(e){return e&&e.inMultiSelectMode}}],t.keyboardHandler=new(e("../keyboard/hash_handler")).HashHandler(t.multiSelectCommands)}),ace.define("ace/multi_select",["require","exports","module","ace/range_list","ace/range","ace/selection","ace/mouse/multi_select_handler","ace/lib/event","ace/lib/lang","ace/commands/multi_select_commands","ace/search","ace/edit_session","ace/editor","ace/config"],function(e,t,i){var n=e("./range_list").RangeList,o=e("./range").Range,r=e("./selection").Selection,s=e("./mouse/multi_select_handler").onMouseDown,a=e("./lib/event"),l=e("./lib/lang"),c=e("./commands/multi_select_commands");t.commands=c.defaultCommands.concat(c.multiSelectCommands);var h=new(e("./search")).Search;(function(){this.getSelectionMarkers=function(){return this.$selectionMarkers}}).call(e("./edit_session").EditSession.prototype),(function(){this.ranges=null,this.rangeList=null,this.addRange=function(e,t){if(e){if(!this.inMultiSelectMode&&0===this.rangeCount){var i=this.toOrientedRange();if(this.rangeList.add(i),this.rangeList.add(e),2!=this.rangeList.ranges.length)return this.rangeList.removeAll(),t||this.fromOrientedRange(e);this.rangeList.removeAll(),this.rangeList.add(i),this.$onAddRange(i)}e.cursor||(e.cursor=e.end);var n=this.rangeList.add(e);return this.$onAddRange(e),n.length&&this.$onRemoveRange(n),this.rangeCount>1&&!this.inMultiSelectMode&&(this._signal("multiSelect"),this.inMultiSelectMode=!0,this.session.$undoSelect=!1,this.rangeList.attach(this.session)),t||this.fromOrientedRange(e)}},this.toSingleRange=function(e){e=e||this.ranges[0];var t=this.rangeList.removeAll();t.length&&this.$onRemoveRange(t),e&&this.fromOrientedRange(e)},this.substractPoint=function(e){var t=this.rangeList.substractPoint(e);if(t)return this.$onRemoveRange(t),t[0]},this.mergeOverlappingRanges=function(){var e=this.rangeList.merge();e.length&&this.$onRemoveRange(e)},this.$onAddRange=function(e){this.rangeCount=this.rangeList.ranges.length,this.ranges.unshift(e),this._signal("addRange",{range:e})},this.$onRemoveRange=function(e){if(this.rangeCount=this.rangeList.ranges.length,1==this.rangeCount&&this.inMultiSelectMode){var t=this.rangeList.ranges.pop();e.push(t),this.rangeCount=0}for(var i=e.length;i--;){var n=this.ranges.indexOf(e[i]);this.ranges.splice(n,1)}this._signal("removeRange",{ranges:e}),0===this.rangeCount&&this.inMultiSelectMode&&(this.inMultiSelectMode=!1,this._signal("singleSelect"),this.session.$undoSelect=!0,this.rangeList.detach(this.session)),(t=t||this.ranges[0])&&!t.isEqual(this.getRange())&&this.fromOrientedRange(t)},this.$initRangeList=function(){this.rangeList||(this.rangeList=new n,this.ranges=[],this.rangeCount=0)},this.getAllRanges=function(){return this.rangeCount?this.rangeList.ranges.concat():[this.getRange()]},this.splitIntoLines=function(){for(var e=this.ranges.length?this.ranges:[this.getRange()],t=[],i=0;i<e.length;i++){var n=e[i],r=n.start.row,s=n.end.row;if(r===s)t.push(n.clone());else{for(t.push(new o(r,n.start.column,r,this.session.getLine(r).length));++r<s;)t.push(this.getLineRange(r,!0));t.push(new o(s,0,s,n.end.column))}0!=i||this.isBackwards()||(t=t.reverse())}this.toSingleRange();for(var i=t.length;i--;)this.addRange(t[i])},this.joinSelections=function(){var e=this.rangeList.ranges,t=e[e.length-1],i=o.fromPoints(e[0].start,t.end);this.toSingleRange(),this.setSelectionRange(i,t.cursor==t.start)},this.toggleBlockSelection=function(){if(this.rangeCount>1){var e=this.rangeList.ranges,t=e[e.length-1],i=o.fromPoints(e[0].start,t.end);this.toSingleRange(),this.setSelectionRange(i,t.cursor==t.start)}else{var n=this.session.documentToScreenPosition(this.cursor),r=this.session.documentToScreenPosition(this.anchor);this.rectangularRangeBlock(n,r).forEach(this.addRange,this)}},this.rectangularRangeBlock=function(e,t,i){var n,r=[],s=e.column<t.column;if(s)var a=e.column,l=t.column,c=e.offsetX,h=t.offsetX;else var a=t.column,l=e.column,c=t.offsetX,h=e.offsetX;var u=e.row<t.row;if(u)var d=e.row,p=t.row;else var d=t.row,p=e.row;a<0&&(a=0),d<0&&(d=0),d==p&&(i=!0);for(var g=d;g<=p;g++){var f,m,y=o.fromPoints(this.session.screenToDocumentPosition(g,a,c),this.session.screenToDocumentPosition(g,l,h));if(y.isEmpty()){if(n&&(f=y.end,m=n,f.row==m.row&&f.column==m.column))break;n=y.end}y.cursor=s?y.start:y.end,r.push(y)}if(u&&r.reverse(),!i){for(var v=r.length-1;r[v].isEmpty()&&v>0;)v--;if(v>0)for(var w=0;r[w].isEmpty();)w++;for(var b=v;b>=w;b--)r[b].isEmpty()&&r.splice(b,1)}return r}}).call(r.prototype);var u=e("./editor").Editor;function d(e){e.$multiselectOnSessionChange||(e.$onAddRange=e.$onAddRange.bind(e),e.$onRemoveRange=e.$onRemoveRange.bind(e),e.$onMultiSelect=e.$onMultiSelect.bind(e),e.$onSingleSelect=e.$onSingleSelect.bind(e),e.$multiselectOnSessionChange=t.onSessionChange.bind(e),e.$checkMultiselectChange=e.$checkMultiselectChange.bind(e),e.$multiselectOnSessionChange(e),e.on("changeSession",e.$multiselectOnSessionChange),e.on("mousedown",s),e.commands.addCommands(c.defaultCommands),function(e){if(e.textInput){var t=e.textInput.getElement(),i=!1;a.addListener(t,"keydown",function(t){var o=18==t.keyCode&&!(t.ctrlKey||t.shiftKey||t.metaKey);e.$blockSelectEnabled&&o?i||(e.renderer.setMouseCursor("crosshair"),i=!0):i&&n()},e),a.addListener(t,"keyup",n,e),a.addListener(t,"blur",n,e)}function n(t){i&&(e.renderer.setMouseCursor(""),i=!1)}}(e))}(function(){this.updateSelectionMarkers=function(){this.renderer.updateCursor(),this.renderer.updateBackMarkers()},this.addSelectionMarker=function(e){e.cursor||(e.cursor=e.end);var t=this.getSelectionStyle();return e.marker=this.session.addMarker(e,"ace_selection",t),this.session.$selectionMarkers.push(e),this.session.selectionMarkerCount=this.session.$selectionMarkers.length,e},this.removeSelectionMarker=function(e){if(e.marker){this.session.removeMarker(e.marker);var t=this.session.$selectionMarkers.indexOf(e);-1!=t&&this.session.$selectionMarkers.splice(t,1),this.session.selectionMarkerCount=this.session.$selectionMarkers.length}},this.removeSelectionMarkers=function(e){for(var t=this.session.$selectionMarkers,i=e.length;i--;){var n=e[i];if(n.marker){this.session.removeMarker(n.marker);var o=t.indexOf(n);-1!=o&&t.splice(o,1)}}this.session.selectionMarkerCount=t.length},this.$onAddRange=function(e){this.addSelectionMarker(e.range),this.renderer.updateCursor(),this.renderer.updateBackMarkers()},this.$onRemoveRange=function(e){this.removeSelectionMarkers(e.ranges),this.renderer.updateCursor(),this.renderer.updateBackMarkers()},this.$onMultiSelect=function(e){this.inMultiSelectMode||(this.inMultiSelectMode=!0,this.setStyle("ace_multiselect"),this.keyBinding.addKeyboardHandler(c.keyboardHandler),this.commands.setDefaultHandler("exec",this.$onMultiSelectExec),this.renderer.updateCursor(),this.renderer.updateBackMarkers())},this.$onSingleSelect=function(e){this.session.multiSelect.inVirtualMode||(this.inMultiSelectMode=!1,this.unsetStyle("ace_multiselect"),this.keyBinding.removeKeyboardHandler(c.keyboardHandler),this.commands.removeDefaultHandler("exec",this.$onMultiSelectExec),this.renderer.updateCursor(),this.renderer.updateBackMarkers(),this._emit("changeSelection"))},this.$onMultiSelectExec=function(e){var t=e.command,i=e.editor;if(i.multiSelect){if(t.multiSelectAction)"forEach"==t.multiSelectAction?n=i.forEachSelection(t,e.args):"forEachLine"==t.multiSelectAction?n=i.forEachSelection(t,e.args,!0):"single"==t.multiSelectAction?(i.exitMultiSelectMode(),n=t.exec(i,e.args||{})):n=t.multiSelectAction(i,e.args||{});else{var n=t.exec(i,e.args||{});i.multiSelect.addRange(i.multiSelect.toOrientedRange()),i.multiSelect.mergeOverlappingRanges()}return n}},this.forEachSelection=function(e,t,i){if(!this.inVirtualSelectionMode){var n,o=i&&i.keepOrder,s=!0==i||i&&i.$byLines,a=this.session,l=this.selection,c=l.rangeList,h=(o?l:c).ranges;if(!h.length)return e.exec?e.exec(this,t||{}):e(this,t||{});var u=l._eventRegistry;l._eventRegistry={};var d=new r(a);this.inVirtualSelectionMode=!0;for(var p=h.length;p--;){if(s)for(;p>0&&h[p].start.row==h[p-1].end.row;)p--;d.fromOrientedRange(h[p]),d.index=p,this.selection=a.selection=d;var g=e.exec?e.exec(this,t||{}):e(this,t||{});n||void 0===g||(n=g),d.toOrientedRange(h[p])}d.detach(),this.selection=a.selection=l,this.inVirtualSelectionMode=!1,l._eventRegistry=u,l.mergeOverlappingRanges(),l.ranges[0]&&l.fromOrientedRange(l.ranges[0]);var f=this.renderer.$scrollAnimation;return this.onCursorChange(),this.onSelectionChange(),f&&f.from==f.to&&this.renderer.animateScrolling(f.from),n}},this.exitMultiSelectMode=function(){this.inMultiSelectMode&&!this.inVirtualSelectionMode&&this.multiSelect.toSingleRange()},this.getSelectedText=function(){var e="";if(this.inMultiSelectMode&&!this.inVirtualSelectionMode){for(var t=this.multiSelect.rangeList.ranges,i=[],n=0;n<t.length;n++)i.push(this.session.getTextRange(t[n]));var o=this.session.getDocument().getNewLineCharacter();(e=i.join(o)).length==(i.length-1)*o.length&&(e="")}else this.selection.isEmpty()||(e=this.session.getTextRange(this.getSelectionRange()));return e},this.$checkMultiselectChange=function(e,t){if(this.inMultiSelectMode&&!this.inVirtualSelectionMode){var i=this.multiSelect.ranges[0];if(!this.multiSelect.isEmpty()||t!=this.multiSelect.anchor){var n=t==this.multiSelect.anchor?i.cursor==i.start?i.end:i.start:i.cursor;n.row!=t.row||this.session.$clipPositionToDocument(n.row,n.column).column!=t.column?this.multiSelect.toSingleRange(this.multiSelect.toOrientedRange()):this.multiSelect.mergeOverlappingRanges()}}},this.findAll=function(e,t,i){if((t=t||{}).needle=e||t.needle,void 0==t.needle){var n=this.selection.isEmpty()?this.selection.getWordRange():this.selection.getRange();t.needle=this.session.getTextRange(n)}this.$search.set(t);var o=this.$search.findAll(this.session);if(!o.length)return 0;var r=this.multiSelect;i||r.toSingleRange(o[0]);for(var s=o.length;s--;)r.addRange(o[s],!0);return n&&r.rangeList.rangeAtPoint(n.start)&&r.addRange(n,!0),o.length},this.selectMoreLines=function(e,t){var i=this.selection.toOrientedRange(),n=i.cursor==i.end,r=this.session.documentToScreenPosition(i.cursor);this.selection.$desiredColumn&&(r.column=this.selection.$desiredColumn);var s=this.session.screenToDocumentPosition(r.row+e,r.column);if(i.isEmpty())var a=s;else var l=this.session.documentToScreenPosition(n?i.end:i.start),a=this.session.screenToDocumentPosition(l.row+e,l.column);if(n){var c=o.fromPoints(s,a);c.cursor=c.start}else{var c=o.fromPoints(a,s);c.cursor=c.end}if(c.desiredColumn=r.column,this.selection.inMultiSelectMode){if(t)var h=i.cursor}else this.selection.addRange(i);this.selection.addRange(c),h&&this.selection.substractPoint(h)},this.transposeSelections=function(e){for(var t=this.session,i=t.multiSelect,n=i.ranges,o=n.length;o--;){var r=n[o];if(r.isEmpty()){var s=t.getWordRange(r.start.row,r.start.column);r.start.row=s.start.row,r.start.column=s.start.column,r.end.row=s.end.row,r.end.column=s.end.column}}i.mergeOverlappingRanges();for(var a=[],o=n.length;o--;){var r=n[o];a.unshift(t.getTextRange(r))}e<0?a.unshift(a.pop()):a.push(a.shift());for(var o=n.length;o--;){var r=n[o],l=r.clone();t.replace(r,a[o]),r.start.row=l.start.row,r.start.column=l.start.column}i.fromOrientedRange(i.ranges[0])},this.selectMore=function(e,t,i){var n=this.session,o=n.multiSelect.toOrientedRange();if(!o.isEmpty()||((o=n.getWordRange(o.start.row,o.start.column)).cursor=-1==e?o.start:o.end,this.multiSelect.addRange(o),!i)){var r=n.getTextRange(o),s=(h.$options.wrap=!0,h.$options.needle=r,h.$options.backwards=-1==e,h.find(n));s&&(s.cursor=-1==e?s.start:s.end,this.session.unfold(s),this.multiSelect.addRange(s),this.renderer.scrollCursorIntoView(null,.5)),t&&this.multiSelect.substractPoint(o.cursor)}},this.alignCursors=function(){var e=this.session,t=e.multiSelect,i=t.ranges,n=-1,r=i.filter(function(e){if(e.cursor.row==n)return!0;n=e.cursor.row});if(i.length&&r.length!=i.length-1){r.forEach(function(e){t.substractPoint(e.cursor)});var s=0,a=1/0,c=i.map(function(t){var i=t.cursor,n=e.getLine(i.row).substr(i.column).search(/\S/g);return -1==n&&(n=0),i.column>s&&(s=i.column),n<a&&(a=n),n});i.forEach(function(t,i){var n=t.cursor,r=s-n.column,h=c[i]-a;r>h?e.insert(n,l.stringRepeat(" ",r-h)):e.remove(new o(n.row,n.column,n.row,n.column-r+h)),t.start.column=t.end.column=s,t.start.row=t.end.row=n.row,t.cursor=t.end}),t.fromOrientedRange(i[0]),this.renderer.updateCursor(),this.renderer.updateBackMarkers()}else{var h=this.selection.getRange(),u=h.start.row,d=h.end.row,p=u==d;if(p){var g,f=this.session.getLength();do g=this.session.getLine(d);while(/[=:]/.test(g)&&++d<f);do g=this.session.getLine(u);while(/[=:]/.test(g)&&--u>0);u<0&&(u=0),d>=f&&(d=f-1)}var m=this.session.removeFullLines(u,d);m=this.$reAlignText(m,p),this.session.insert({row:u,column:0},m.join("\n")+"\n"),p||(h.start.column=0,h.end.column=m[m.length-1].length),this.selection.setRange(h)}},this.$reAlignText=function(e,t){var i,n,o,r=!0,s=!0;return e.map(function(e){var t=e.match(/(\s*)(.*?)(\s*)([=:].*)/);return t?(null==i?(i=t[1].length,n=t[2].length,o=t[3].length):(i+n+o!=t[1].length+t[2].length+t[3].length&&(s=!1),i!=t[1].length&&(r=!1),i>t[1].length&&(i=t[1].length),n<t[2].length&&(n=t[2].length),o>t[3].length&&(o=t[3].length)),t):[e]}).map(t?c:r?s?function(e){return e[2]?a(i+n-e[2].length)+e[2]+a(o)+e[4].replace(/^([=:])\s+/,"$1 "):e[0]}:c:function(e){return e[2]?a(i)+e[2]+a(o)+e[4].replace(/^([=:])\s+/,"$1 "):e[0]});function a(e){return l.stringRepeat(" ",e)}function c(e){return e[2]?a(i)+e[2]+a(n-e[2].length+o)+e[4].replace(/^([=:])\s+/,"$1 "):e[0]}}}).call(u.prototype),t.onSessionChange=function(e){var t=e.session;t&&!t.multiSelect&&(t.$selectionMarkers=[],t.selection.$initRangeList(),t.multiSelect=t.selection),this.multiSelect=t&&t.multiSelect;var i=e.oldSession;i&&(i.multiSelect.off("addRange",this.$onAddRange),i.multiSelect.off("removeRange",this.$onRemoveRange),i.multiSelect.off("multiSelect",this.$onMultiSelect),i.multiSelect.off("singleSelect",this.$onSingleSelect),i.multiSelect.lead.off("change",this.$checkMultiselectChange),i.multiSelect.anchor.off("change",this.$checkMultiselectChange)),t&&(t.multiSelect.on("addRange",this.$onAddRange),t.multiSelect.on("removeRange",this.$onRemoveRange),t.multiSelect.on("multiSelect",this.$onMultiSelect),t.multiSelect.on("singleSelect",this.$onSingleSelect),t.multiSelect.lead.on("change",this.$checkMultiselectChange),t.multiSelect.anchor.on("change",this.$checkMultiselectChange)),t&&this.inMultiSelectMode!=t.selection.inMultiSelectMode&&(t.selection.inMultiSelectMode?this.$onMultiSelect():this.$onSingleSelect())},t.MultiSelect=d,e("./config").defineOptions(u.prototype,"editor",{enableMultiselect:{set:function(e){d(this),e?this.on("mousedown",s):this.off("mousedown",s)},value:!0},enableBlockSelect:{set:function(e){this.$blockSelectEnabled=e},value:!0}})}),ace.define("ace/mode/folding/fold_mode",["require","exports","module","ace/range"],function(e,t,i){"use strict";var n=e("../../range").Range;(function(){this.foldingStartMarker=null,this.foldingStopMarker=null,this.getFoldWidget=function(e,t,i){var n=e.getLine(i);return this.foldingStartMarker.test(n)?"start":"markbeginend"==t&&this.foldingStopMarker&&this.foldingStopMarker.test(n)?"end":""},this.getFoldWidgetRange=function(e,t,i){return null},this.indentationBlock=function(e,t,i){var o=/\S/,r=e.getLine(t),s=r.search(o);if(-1!=s){for(var a=i||r.length,l=e.getLength(),c=t,h=t;++t<l;){var u=e.getLine(t).search(o);if(-1!=u){if(u<=s){var d=e.getTokenAt(t,0);if(!d||"string"!==d.type)break}h=t}}if(h>c){var p=e.getLine(h).length;return new n(c,a,h,p)}}},this.openingBracketBlock=function(e,t,i,o,r){var s={row:i,column:o+1},a=e.$findClosingBracket(t,s,r);if(a){var l=e.foldWidgets[a.row];return null==l&&(l=e.getFoldWidget(a.row)),"start"==l&&a.row>s.row&&(a.row--,a.column=e.getLine(a.row).length),n.fromPoints(s,a)}},this.closingBracketBlock=function(e,t,i,o,r){var s={row:i,column:o},a=e.$findOpeningBracket(t,s);if(a)return a.column++,s.column--,n.fromPoints(a,s)}}).call((t.FoldMode=function(){}).prototype)}),ace.define("ace/ext/error_marker",["require","exports","module","ace/lib/dom","ace/range","ace/config"],function(e,t,i){"use strict";var n=e("../lib/dom"),o=e("../range").Range,r=e("../config").nls;t.showErrorMarker=function(e,t){var i,s=e.session,a=e.getCursorPosition(),l=a.row,c=s.widgetManager.getWidgetsAtRow(l).filter(function(e){return"errorMarker"==e.type})[0];c?c.destroy():l-=t;var h=function(e,t,i){var n=e.getAnnotations().sort(o.comparePoints);if(n.length){var r=function(e,t,i){for(var n=0,o=e.length-1;n<=o;){var r=n+o>>1,s=i(t,e[r]);if(s>0)n=r+1;else{if(!(s<0))return r;o=r-1}}return-(n+1)}(n,{row:t,column:-1},o.comparePoints);r<0&&(r=-r-1),r>=n.length?r=i>0?0:n.length-1:0===r&&i<0&&(r=n.length-1);var s=n[r];if(s&&i){if(s.row===t){do s=n[r+=i];while(s&&s.row===t);if(!s)return n.slice()}var a=[];t=s.row;do a[i<0?"unshift":"push"](s),s=n[r+=i];while(s&&s.row==t);return a.length&&a}}}(s,l,t);if(h){var u=h[0];a.column=(u.pos&&"number"!=typeof u.column?u.pos.sc:u.column)||0,a.row=u.row,i=e.renderer.$gutterLayer.$annotations[a.row]}else{if(c)return;i={displayText:[r("error-marker.good-state","Looks good!")],className:"ace_ok"}}e.session.unfold(a.row),e.selection.moveToPosition(a);var d={row:a.row,fixedWidth:!0,coverGutter:!0,el:n.createElement("div"),type:"errorMarker"},p=d.el.appendChild(n.createElement("div")),g=d.el.appendChild(n.createElement("div"));g.className="error_widget_arrow "+i.className;var f=e.renderer.$cursorLayer.getPixelPosition(a).left;g.style.left=f+e.renderer.gutterWidth-5+"px",d.el.className="error_widget_wrapper",p.className="error_widget "+i.className,i.displayText.forEach(function(e,t){p.appendChild(n.createTextNode(e)),t<i.displayText.length-1&&p.appendChild(n.createElement("br"))}),p.appendChild(n.createElement("div"));var m=function(e,t,i){if(0===t&&("esc"===i||"return"===i))return d.destroy(),{command:"null"}};d.destroy=function(){e.$mouseHandler.isMousePressed||(e.keyBinding.removeKeyboardHandler(m),s.widgetManager.removeLineWidget(d),e.off("changeSelection",d.destroy),e.off("changeSession",d.destroy),e.off("mouseup",d.destroy),e.off("change",d.destroy))},e.keyBinding.addKeyboardHandler(m),e.on("changeSelection",d.destroy),e.on("changeSession",d.destroy),e.on("mouseup",d.destroy),e.on("change",d.destroy),e.session.widgetManager.addLineWidget(d),d.el.onmousedown=e.focus.bind(e),e.renderer.scrollCursorIntoView(null,.5,{bottom:d.el.offsetHeight})},n.importCssString("\n    .error_widget_wrapper {\n        background: inherit;\n        color: inherit;\n        border:none\n    }\n    .error_widget {\n        border-top: solid 2px;\n        border-bottom: solid 2px;\n        margin: 5px 0;\n        padding: 10px 40px;\n        white-space: pre-wrap;\n    }\n    .error_widget.ace_error, .error_widget_arrow.ace_error{\n        border-color: #ff5a5a\n    }\n    .error_widget.ace_warning, .error_widget_arrow.ace_warning{\n        border-color: #F1D817\n    }\n    .error_widget.ace_info, .error_widget_arrow.ace_info{\n        border-color: #5a5a5a\n    }\n    .error_widget.ace_ok, .error_widget_arrow.ace_ok{\n        border-color: #5aaa5a\n    }\n    .error_widget_arrow {\n        position: absolute;\n        border: solid 5px;\n        border-top-color: transparent!important;\n        border-right-color: transparent!important;\n        border-left-color: transparent!important;\n        top: -5px;\n    }\n","error_marker.css",!1)}),ace.define("ace/ace",["require","exports","module","ace/lib/dom","ace/range","ace/editor","ace/edit_session","ace/undomanager","ace/virtual_renderer","ace/worker/worker_client","ace/keyboard/hash_handler","ace/placeholder","ace/multi_select","ace/mode/folding/fold_mode","ace/theme/textmate","ace/ext/error_marker","ace/config","ace/loader_build"],function(e,t,i){"use strict";e("./loader_build")(t);var n=e("./lib/dom"),o=e("./range").Range,r=e("./editor").Editor,s=e("./edit_session").EditSession,a=e("./undomanager").UndoManager,l=e("./virtual_renderer").VirtualRenderer;e("./worker/worker_client"),e("./keyboard/hash_handler"),e("./placeholder"),e("./multi_select"),e("./mode/folding/fold_mode"),e("./theme/textmate"),e("./ext/error_marker"),t.config=e("./config"),t.edit=function(e,i){if("string"==typeof e){var o=e;if(!(e=document.getElementById(o)))throw Error("ace.edit can't find div #"+o)}if(e&&e.env&&e.env.editor instanceof r)return e.env.editor;var s="";if(e&&/input|textarea/i.test(e.tagName)){var a=e;s=a.value,e=n.createElement("pre"),a.parentNode.replaceChild(e,a)}else e&&(s=e.textContent,e.innerHTML="");var c=t.createEditSession(s),h=new r(new l(e),c,i),u={document:c,editor:h,onResize:h.resize.bind(h,null)};return a&&(u.textarea=a),h.on("destroy",function(){u.editor.container.env=null}),h.container.env=h.env=u,h},t.createEditSession=function(e,t){var i=new s(e,t);return i.setUndoManager(new a),i},t.Range=o,t.Editor=r,t.EditSession=s,t.UndoManager=a,t.VirtualRenderer=l;var c=t.config.version;t.version=c}),ace.require(["ace/ace"],function(t){t&&(t.config.init(!0),t.define=ace.define);var i=function(){return this}();for(var n in i||"undefined"==typeof window||(i=window),i||"undefined"==typeof self||(i=self),i.ace||(i.ace=t),t)t.hasOwnProperty(n)&&(i.ace[n]=t[n]);i.ace.default=i.ace,e&&(e.exports=i.ace)})}}]);