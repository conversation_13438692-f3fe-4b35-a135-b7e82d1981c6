import { extendTheme, type ThemeConfig } from '@chakra-ui/react';

// 1. Global theme configuration
const config: ThemeConfig = {
  initialColorMode: 'dark',
  useSystemColorMode: false,
};

// 2. Custom brand color palette (indigo-violet tone)
const colors = {
  brand: {
    50:  '#f5f3ff',
    100: '#ede9fe',
    200: '#ddd6fe',
    300: '#c4b5fd',
    400: '#a78bfa',
    500: '#8b5cf6', // primary accent
    600: '#7c3aed',
    700: '#6d28d9',
    800: '#5b21b6',
    900: '#4c1d95',
  },
  discord: {
    50: '#e8e9fd',
    100: '#d1d3fc',
    200: '#b9bcfa',
    300: '#a2a5f9',
    400: '#8b8ef7',
    500: '#5865F2', // Discord brand color
    600: '#4752c4',
    700: '#363f97',
    800: '#242c69',
    900: '#12193c',
  },
};

// 3. Extend the default theme
const theme = extendTheme({
  config,
  fonts: {
    heading: `'Inter', sans-serif`,
    body: `'Inter', sans-serif`,
  },
  colors,
  styles: {
    global: {
      body: {
        bg: 'gray.900',
        color: 'gray.100',
      },
    },
  },
  components: {
    Button: {
      defaultProps: {
        colorScheme: 'brand',
      },
      variants: {
        solid: (props: any) => ({
          bg: `${props.colorScheme}.500`,
          color: 'white',
          _hover: {
            bg: `${props.colorScheme}.600`,
            transform: 'translateY(-2px)',
            boxShadow: 'lg',
          },
          _active: {
            bg: `${props.colorScheme}.700`,
            transform: 'translateY(0)',
          },
          transition: 'all 0.2s ease',
        }),
      },
    },
    Link: {
      baseStyle: {
        _hover: { textDecoration: 'none' },
      },
    },
    Box: {
      baseStyle: {
        transition: 'all 0.2s ease',
      },
    },
  },
});

export default theme; 