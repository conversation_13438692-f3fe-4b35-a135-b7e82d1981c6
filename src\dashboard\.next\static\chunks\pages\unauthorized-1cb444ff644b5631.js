(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4426],{34105:(e,n,i)=>{(window.__NEXT_P=window.__NEXT_P||[]).push(["/unauthorized",function(){return i(56736)}])},56736:(e,n,i)=>{"use strict";i.r(n),i.d(n,{default:()=>a});var c=i(94513),r=i(51961),s=i(31678),t=i(41611),d=i(62690),l=i(60341),o=i(53424);function a(){return(0,c.jsx)(l.A,{children:(0,c.jsxs)(r.a,{textAlign:"center",mt:20,children:[(0,c.jsx)(s.D,{size:"xl",mb:4,children:"Access Denied"}),(0,c.jsx)(t.E,{mb:6,children:"You do not have permission to view this page."}),(0,c.jsx)(d.$,{colorScheme:"brand",onClick:()=>(0,o.signIn)("discord",{callbackUrl:"/"}),children:"Sign in with a different account"})]})})}}},e=>{var n=n=>e(e.s=n);e.O(0,[4108,4976,217,2965,341,636,6593,8792],()=>n(34105)),_N_E=e.O()}]);