import { SlashCommandBuilder, ChatInputCommandInteraction, PermissionFlagsBits } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('ban')
  .setDescription('Ban a member from the server')
  .addUserOption(option =>
    option.setName('user')
      .setDescription('The user to ban')
      .setRequired(true))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for the ban')
      .setRequired(false))
  .addIntegerOption(option =>
    option.setName('delete_days')
      .setDescription('Delete messages from the past few days (0-7)')
      .setRequired(false)
      .setMinValue(0)
      .setMaxValue(7))
  .setDefaultMemberPermissions(PermissionFlagsBits.BanMembers);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const targetUser = interaction.options.getUser('user', true);
  const reason = interaction.options.getString('reason') ?? 'No reason provided';
  const deleteDays = interaction.options.getInteger('delete_days') ?? 0;

  // Permission check
  if (!interaction.memberPermissions?.has(PermissionFlagsBits.BanMembers)) {
    await interaction.reply({ content: '❌ You do not have permission to ban members.', ephemeral: true });
    return;
  }

  const member = await interaction.guild?.members.fetch(targetUser.id).catch(() => null);
  if (!member) {
    await interaction.reply({ content: '❌ Could not find that member in this guild.', ephemeral: true });
    return;
  }

  if (!member.bannable) {
    await interaction.reply({ content: '❌ I cannot ban this user. They may have higher permissions or roles than me.', ephemeral: true });
    return;
  }

  // Build confirmation buttons
  const { ActionRowBuilder, ButtonBuilder, ButtonStyle, ComponentType } = await import('discord.js');

  const confirmBtn = new ButtonBuilder()
    .setCustomId('ban_confirm')
    .setLabel('Confirm Ban')
    .setStyle(ButtonStyle.Danger);

  const cancelBtn = new ButtonBuilder()
    .setCustomId('ban_cancel')
    .setLabel('Cancel')
    .setStyle(ButtonStyle.Secondary);

  const row = new ActionRowBuilder().addComponents(confirmBtn, cancelBtn);

  await interaction.reply({
    content: `🛑 Are you sure you want to ban **${targetUser.tag}**?`,
    components: [row as any],
    ephemeral: true,
  });

  // Await button interaction from the command invoker only
  try {
    const buttonInteraction = await interaction.fetchReply().then((msg: any) =>
      msg.awaitMessageComponent({
        componentType: ComponentType.Button,
        filter: (i: any) => i.user.id === interaction.user.id,
        time: 15000,
      })
    );

    if (buttonInteraction.customId === 'ban_cancel') {
      await buttonInteraction.update({ content: '❌ Ban cancelled.', components: [] });
      return;
    }

    // Proceed with ban
    await member.ban({ reason, deleteMessageDays: deleteDays });

    await buttonInteraction.update({
      content: `🔨 Banned **${targetUser.tag}** | Reason: ${reason}` + (deleteDays ? ` | Deleted ${deleteDays}d of messages` : ''),
      components: [],
    });
  } catch {
    // Timeout or other error, just disable buttons
    await interaction.editReply({ content: '⌛ Ban timed out or no response.', components: [] });
  }
}

export const cooldown = 5000; 