// Simple Mongo helper for API routes
import { dashboardConfig } from '../core/config';
import { MongoClient, Db } from 'mongodb';

let cachedClient: MongoClient | null = null;
let cachedDb: Db | null = null;

export async function getDb(): Promise<Db> {
  if (cachedDb) return cachedDb;

  const mongoUrl = dashboardConfig.database?.url || 'mongodb://localhost:27017';
  const dbName = dashboardConfig.database?.name || 'discord_bot';

  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl, {
      ...(dashboardConfig.database?.options || {}),
    });
  }
  cachedDb = cachedClient.db(dbName);
  return cachedDb;
} 