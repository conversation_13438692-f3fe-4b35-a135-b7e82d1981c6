"use strict";(()=>{var e={};e.id=4023,e.ids=[4023],e.modules={264:(e,a)=>{Object.defineProperty(a,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,a)=>{Object.defineProperty(a,"M",{enumerable:!0,get:function(){return function e(a,t){return t in a?a[t]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,t)):"function"==typeof a&&"default"===t?a:void 0}}})},2071:(e,a,t)=>{t.r(a),t.d(a,{config:()=>M,default:()=>T,routeModule:()=>g});var o={};t.r(o),t.d(o,{default:()=>f});var n=t(3433),i=t(264),r=t(584),s=t(5806),l=t(8525),d=t(2518);let{url:u,name:c}=t(8580).dashboardConfig.database,p=null;async function b(){if(p)return p;let e=await d.MongoClient.connect(u);return p=e,e}let m={isOpen:!0,type:"applications",questions:[{id:"discord_username",type:"text",label:"What is your Discord username?",required:!0},{id:"age",type:"number",label:"How old are you?",required:!0},{id:"timezone",type:"select",label:"What timezone are you in?",required:!0,options:[{value:"GMT-12:00",label:"(GMT-12:00) International Date Line West"},{value:"GMT-11:00",label:"(GMT-11:00) Midway Island, Samoa"},{value:"GMT-10:00",label:"(GMT-10:00) Hawaii"},{value:"GMT-09:00",label:"(GMT-09:00) Alaska"},{value:"GMT-08:00",label:"(GMT-08:00) Pacific Time (US & Canada)"},{value:"GMT-07:00",label:"(GMT-07:00) Mountain Time (US & Canada)"},{value:"GMT-06:00",label:"(GMT-06:00) Central Time (US & Canada)"},{value:"GMT-05:00",label:"(GMT-05:00) Eastern Time (US & Canada)"},{value:"GMT-04:00",label:"(GMT-04:00) Atlantic Time (Canada)"},{value:"GMT-03:00",label:"(GMT-03:00) Buenos Aires, Georgetown"},{value:"GMT-02:00",label:"(GMT-02:00) Mid-Atlantic"},{value:"GMT-01:00",label:"(GMT-01:00) Azores, Cape Verde Islands"},{value:"GMT+00:00",label:"(GMT+00:00) London, Dublin, Edinburgh"},{value:"GMT+01:00",label:"(GMT+01:00) Paris, Amsterdam, Berlin"},{value:"GMT+02:00",label:"(GMT+02:00) Athens, Istanbul, Helsinki"},{value:"GMT+03:00",label:"(GMT+03:00) Moscow, Baghdad, Kuwait"},{value:"GMT+04:00",label:"(GMT+04:00) Abu Dhabi, Dubai, Baku"},{value:"GMT+05:00",label:"(GMT+05:00) Karachi, Tashkent"},{value:"GMT+06:00",label:"(GMT+06:00) Dhaka, Almaty"},{value:"GMT+07:00",label:"(GMT+07:00) Bangkok, Jakarta"},{value:"GMT+08:00",label:"(GMT+08:00) Beijing, Singapore, Hong Kong"},{value:"GMT+09:00",label:"(GMT+09:00) Tokyo, Seoul, Osaka"},{value:"GMT+10:00",label:"(GMT+10:00) Sydney, Melbourne, Brisbane"},{value:"GMT+11:00",label:"(GMT+11:00) Solomon Islands"},{value:"GMT+12:00",label:"(GMT+12:00) Auckland, Wellington"}].map(e=>e.label)},{id:"hours_per_week",type:"text",label:"How many hours per week can you dedicate to moderation?",required:!0},{id:"why_moderator",type:"textarea",label:"Why do you want to be a moderator?",required:!0},{id:"moderation_experience",type:"textarea",label:"Do you have any previous moderation experience?",required:!1},{id:"heated_argument",type:"textarea",label:"How would you handle a heated argument between two members?",required:!0}],scenarios:[],open:!0};async function f(e,a){if(!await (0,s.getServerSession)(e,a,l.authOptions))return a.status(401).json({error:"Unauthorized"});if("GET"===e.method)try{let e=(await b()).db(c),t=await e.collection("config").findOne({type:"applications"});return t?(t={...m,...t,scenarios:m.scenarios,questions:m.questions},"boolean"==typeof t.isOpen&&t.open!==t.isOpen?(await e.collection("config").updateOne({type:"applications"},{$set:{open:t.isOpen}}),t.open=t.isOpen):"boolean"==typeof t.open&&t.isOpen!==t.open&&(await e.collection("config").updateOne({type:"applications"},{$set:{isOpen:t.open}}),t.isOpen=t.open)):t=m,a.status(200).json(t)}catch(e){return a.status(500).json({error:"Internal server error"})}if("POST"===e.method){let{isOpen:t}=e.body;if("boolean"!=typeof t)return a.status(400).json({error:"Missing or invalid isOpen field"});try{let e=(await b()).db(c),o=await e.collection("config").findOne({type:"applications"})||m;await e.collection("config").updateOne({type:"applications"},{$set:{...o,type:"applications",isOpen:t,open:t,scenarios:[],questions:m.questions}},{upsert:!0});let n=await e.collection("config").findOne({type:"applications"});return a.status(200).json(n)}catch(e){return a.status(500).json({error:"Internal server error"})}}return a.setHeader("Allow",["GET","POST"]),a.status(405).json({error:"Method not allowed"})}let T=(0,r.M)(o,"default"),M=(0,r.M)(o,"config"),g=new n.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/applications/config",pathname:"/api/applications/config",bundlePath:"",filename:""},userland:o})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,a,t)=>{e.exports=t(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,a,t)=>{t.r(a),t.d(a,{authOptions:()=>l,default:()=>d});var o=t(5542),n=t.n(o);let i=require("next-auth/providers/discord");var r=t.n(i),s=t(8580);let l={providers:[r()({clientId:s.dashboardConfig.bot.clientId,clientSecret:s.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:a,profile:t})=>(a&&t&&(e.accessToken=a.access_token||null,e.id=t.id||null),e),async session({session:e,token:a}){if(e?.user){let t=a.id||null,o=a.accessToken||null;e.user.id=t,e.user.accessToken=o;let n=!1;if(t)if((s.dashboardConfig.dashboard.admins||[]).includes(t))n=!0;else{let e=s.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&s.dashboardConfig.bot.token&&s.dashboardConfig.bot.guildId)try{let a=await fetch(`https://discord.com/api/v10/guilds/${s.dashboardConfig.bot.guildId}/members/${t}`,{headers:{Authorization:`Bot ${s.dashboardConfig.bot.token}`}});if(a.ok){let t=await a.json();n=e.some(e=>t.roles?.includes(e))||!1}else await a.text()}catch(e){}}e.user.isAdmin=n,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:a}){let t=new URL(a),o=`${t.protocol}//localhost${t.port?`:${t.port}`:""}`;return e.startsWith(a)||e.startsWith(o)?e:a}},secret:s.dashboardConfig.dashboard.session.secret||s.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,a)=>{},warn:e=>{},debug:(e,a)=>{}}},d=n()(l)},8580:(e,a,t)=>{t.r(a),t.d(a,{dashboardConfig:()=>l,default:()=>d});var o=t(9021),n=t(2115),i=t.n(n),r=t(3873);let s={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>o.existsSync(e));if(!e){let a=r.resolve(__dirname,"../../../config.yml");o.existsSync(a)&&(e=a)}if(!e)throw Error("config.yml not found");let a=o.readFileSync(e,"utf8");s=i().parse(a)}catch(e){process.exit(1)}let l={bot:{token:s.bot.token,clientId:s.bot.clientId,clientSecret:s.bot.clientSecret,guildId:s.bot.guildId,ticketCategoryId:s.bot.ticketCategoryId||null,ticketLogChannelId:s.bot.ticketLogChannelId||null,prefix:s.bot.prefix},dashboard:{admins:s.dashboard?.admins||[],adminRoleIds:s.dashboard?.adminRoleIds||[],session:{secret:s.dashboard?.session?.secret||s.bot.clientSecret}},database:{url:s.database.url,name:s.database.name,options:{maxPoolSize:s.database.options?.maxPoolSize||10,minPoolSize:s.database.options?.minPoolSize||1,maxIdleTimeMS:s.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:s.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:s.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:s.database.options?.connectTimeoutMS||1e4,retryWrites:s.database.options?.retryWrites!==!1,retryReads:s.database.options?.retryReads!==!1}}};l.bot.token||process.exit(1),l.bot.clientId&&l.bot.clientSecret||process.exit(1),l.bot.guildId||process.exit(1),l.database.url&&l.database.name||process.exit(1);let d=l},9021:e=>{e.exports=require("fs")}};var a=require("../../../webpack-api-runtime.js");a.C(e);var t=a(a.s=2071);module.exports=t})();