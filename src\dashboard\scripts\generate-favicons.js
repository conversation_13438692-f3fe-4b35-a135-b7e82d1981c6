import sharp from 'sharp';
import { promises as fs } from 'fs';
import path from 'path';

const sizes = [
  { width: 16, height: 16, name: 'favicon-16x16.png' },
  { width: 32, height: 32, name: 'favicon-32x32.png' },
  { width: 180, height: 180, name: 'apple-touch-icon.png' },
  { width: 192, height: 192, name: 'android-chrome-192x192.png' },
  { width: 512, height: 512, name: 'android-chrome-512x512.png' }
];

async function generateFavicons() {
  const publicDir = path.resolve(process.cwd(), 'public');
  const svgPath = path.join(publicDir, 'favicon.min.svg');
  
  try {
    // Read the SVG file
    const svgBuffer = await fs.readFile(svgPath);
    
    // Generate each size
    await Promise.all(sizes.map(async ({ width, height, name }) => {
      const outputPath = path.join(publicDir, name);
      await sharp(svgBuffer)
        .resize(width, height)
        .png()
        .toFile(outputPath);
      console.log(`Generated ${name}`);
    }));
    
    console.log('All favicons generated successfully!');
  } catch (error) {
    console.error('Error generating favicons:', error);
    process.exit(1);
  }
}

generateFavicons(); 