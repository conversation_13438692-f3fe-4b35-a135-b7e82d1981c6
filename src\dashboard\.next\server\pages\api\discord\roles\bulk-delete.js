"use strict";(()=>{var e={};e.id=6858,e.ids=[6858],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8378:(e,t,r)=>{r.r(t),r.d(t,{config:()=>b,default:()=>m,routeModule:()=>h});var s={};r.r(s),r.d(s,{default:()=>f});var o=r(3433),i=r(264),a=r(584),n=r(5806),d=r(8525),l=r(8580);async function u(e,t,r,s=0){try{let o=await fetch(`https://discord.com/api/v10/guilds/${t}/roles/${r}`,{method:"DELETE",headers:{Authorization:`Bot ${e}`}});if(o.ok)return!0;if(429===o.status){let i=await o.json(),a=1e3*(i.retry_after||1);if(await new Promise(e=>setTimeout(e,a)),s<2)return u(e,t,r,s+1)}return!1}catch(o){if(s<2)return await new Promise(e=>setTimeout(e,1e3)),u(e,t,r,s+1);return!1}}async function c(e,t,r){return(await Promise.all(r.map(async r=>{let s=await u(e,t,r);return{roleId:r,success:s}}))).reduce((e,{roleId:t,success:r})=>(r?e.succeeded.push(t):e.failed.push(t),e),{succeeded:[],failed:[]})}async function f(e,t){try{let r=await (0,n.getServerSession)(e,t,d.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:s,token:o}=l.dashboardConfig.bot;if(!o||!s)return t.status(500).json({error:"Bot configuration missing"});if("POST"===e.method)try{let{roleIds:r}=e.body;if(!Array.isArray(r)||0===r.length)return t.status(400).json({error:"Role IDs array is required"});let i={succeeded:[],failed:[]};for(let e=0;e<r.length;e+=5){let t=r.slice(e,e+5),a=await c(o,s,t);i.succeeded.push(...a.succeeded),i.failed.push(...a.failed),e+5<r.length&&await new Promise(e=>setTimeout(e,1e3))}return t.status(200).json({message:`Successfully deleted ${i.succeeded.length} roles${i.failed.length>0?`, failed to delete ${i.failed.length} roles`:""}`,succeeded:i.succeeded,failed:i.failed})}catch(e){return t.status(500).json({error:"Failed to delete roles"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let m=(0,a.M)(s,"default"),b=(0,a.M)(s,"config"),h=new o.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/roles/bulk-delete",pathname:"/api/discord/roles/bulk-delete",bundlePath:"",filename:""},userland:s})},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var s=r(5542),o=r.n(s);let i=require("next-auth/providers/discord");var a=r.n(i),n=r(8580);let d={providers:[a()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,s=t.accessToken||null;e.user.id=r,e.user.accessToken=s;let o=!1;if(r)if((n.dashboardConfig.dashboard.admins||[]).includes(r))o=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();o=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=o,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),s=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=o()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var s=r(9021),o=r(2115),i=r.n(o),a=r(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");n=i().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=8378);module.exports=r})();