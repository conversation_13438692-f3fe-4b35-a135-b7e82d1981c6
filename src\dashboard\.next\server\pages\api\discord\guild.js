"use strict";(()=>{var e={};e.id=2913,e.ids=[2913],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2235:(e,t,o)=>{o.r(t),o.d(t,{config:()=>m,default:()=>c,routeModule:()=>p});var i={};o.r(i),o.d(i,{default:()=>u});var s=o(3433),a=o(264),r=o(584),n=o(5806),d=o(8525),l=o(8580);async function u(e,t){try{let o=await (0,n.getServerSession)(e,t,d.authOptions);if(!o?.user)return t.status(401).json({error:"Unauthorized"});let{guildId:i,token:s}=l.dashboardConfig.bot;if(!s||!i)return t.status(500).json({error:"Bot configuration missing"});if("GET"===e.method)try{let e=await fetch(`https://discord.com/api/v10/guilds/${i}`,{headers:{Authorization:`Bot ${s}`}});if(!e.ok)throw await e.text(),Error(`Failed to fetch guild data: ${e.status}`);let o=await e.json(),a=await fetch(`https://discord.com/api/v10/guilds/${i}/members/${l.dashboardConfig.bot.clientId}`,{headers:{Authorization:`Bot ${s}`}}),r="";if(a.ok){let e=await a.json();r=e.nick||e.user?.username||""}return t.status(200).json({id:o.id,name:o.name,icon:o.icon,botName:r,features:o.features,description:o.description,preferred_locale:o.preferred_locale})}catch(e){return t.status(500).json({error:"Failed to fetch guild data",details:e.message})}if("PATCH"===e.method){if(!o.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildName:a,botName:r,icon:n}=e.body;try{let e=[],o=null,d=null;if(a||n)try{let t={};a&&(t.name=a),n&&(t.icon=n);let r=await fetch(`https://discord.com/api/v10/guilds/${i}`,{method:"PATCH",headers:{Authorization:`Bot ${s}`,"Content-Type":"application/json"},body:JSON.stringify(t)});r.ok?e.push(n?"guild icon":"guild name"):(await r.text(),o=`Failed to update guild settings: ${r.status}`)}catch(e){o=e.message}if(r)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i}/members/@me`,{method:"PATCH",headers:{Authorization:`Bot ${s}`,"Content-Type":"application/json"},body:JSON.stringify({nick:r})});(404===t.status||405===t.status)&&(t=await fetch(`https://discord.com/api/v10/guilds/${i}/members/@me/nick`,{method:"PATCH",headers:{Authorization:`Bot ${s}`,"Content-Type":"application/json"},body:JSON.stringify({nick:r})})),t.ok||(t=await fetch(`https://discord.com/api/v10/guilds/${i}/members/${l.dashboardConfig.bot.clientId}`,{method:"PATCH",headers:{Authorization:`Bot ${s}`,"Content-Type":"application/json"},body:JSON.stringify({nick:r})})),t.ok?e.push("bot nickname"):(await t.text(),d=`Failed to update bot nickname: ${t.status}`)}catch(e){d=e.message}if(o||d){let i=[];o&&i.push(o),d&&i.push(d);let s=e.length>0?207:500;return t.status(s).json({error:e.length>0?"Partial success":"Failed to update settings",details:i.join("; "),partialSuccess:e.length>0,succeeded:e})}if(0===e.length)return t.status(400).json({error:"No valid updates provided"});return t.status(200).json({message:`Successfully updated ${e.join(" and ")}`,updated:e})}catch(e){return t.status(500).json({error:"Failed to update guild settings",details:e.message})}}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error",details:e.message})}}let c=(0,r.M)(i,"default"),m=(0,r.M)(i,"config"),p=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/discord/guild",pathname:"/api/discord/guild",bundlePath:"",filename:""},userland:i})},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var i=o(5542),s=o.n(i);let a=require("next-auth/providers/discord");var r=o.n(a),n=o(8580);let d={providers:[r()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,i=t.accessToken||null;e.user.id=o,e.user.accessToken=i;let s=!1;if(o)if((n.dashboardConfig.dashboard.admins||[]).includes(o))s=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();s=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),i=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(i)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=s()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var i=o(9021),s=o(2115),a=o.n(s),r=o(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>i.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");i.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=i.readFileSync(e,"utf8");n=a().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=2235);module.exports=o})();