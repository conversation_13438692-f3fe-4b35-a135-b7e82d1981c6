(()=>{var e={};e.id=3332,e.ids=[3220,3332],e.modules={361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},786:(e,t)=>{"use strict";Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2015:e=>{"use strict";e.exports=require("react")},2115:e=>{"use strict";e.exports=require("yaml")},2742:(e,t)=>{"use strict";function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,a=new WeakMap;return(r=function(e){return e?a:t})(e)}t._=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var a=r(t);if(a&&a.has(e))return a.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var i=o?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(n,s,i):n[s]=e[s]}return n.default=e,a&&a.set(e,n),n}},3001:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{DP:()=>u,NP:()=>f,nk:()=>d});var n=r(8732),o=r(2015),s=r(9733),i=r(6390),c=e([s,i]);[s,i]=c.then?(await c)():c;let d=[{id:"default",name:"Default Purple",description:"Classic purple and blue gradient theme",colors:{primary:"#8b5cf6",primaryLight:"#a78bfa",primaryDark:"#7c3aed",secondary:"#5865F2",accent:"#06b6d4",background:"#1a202c",surface:"rgba(255,255,255,0.03)",text:"#f7fafc",textSecondary:"#a0aec0",border:"rgba(255,255,255,0.2)",success:"#68d391",warning:"#fbb6ce",error:"#fc8181",info:"#63b3ed"}},{id:"ocean",name:"Ocean Blue",description:"Deep blue ocean-inspired theme",colors:{primary:"#0ea5e9",primaryLight:"#38bdf8",primaryDark:"#0284c7",secondary:"#06b6d4",accent:"#8b5cf6",background:"#0f172a",surface:"rgba(59, 130, 246, 0.05)",text:"#f1f5f9",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}},{id:"forest",name:"Forest Green",description:"Nature-inspired green theme",colors:{primary:"#059669",primaryLight:"#10b981",primaryDark:"#047857",secondary:"#065f46",accent:"#8b5cf6",background:"#0f1419",surface:"rgba(16, 185, 129, 0.05)",text:"#f0fdf4",textSecondary:"#86efac",border:"rgba(16, 185, 129, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"sunset",name:"Sunset Orange",description:"Warm sunset-inspired theme",colors:{primary:"#ea580c",primaryLight:"#fb923c",primaryDark:"#c2410c",secondary:"#dc2626",accent:"#8b5cf6",background:"#1c1917",surface:"rgba(251, 146, 60, 0.05)",text:"#fef7ed",textSecondary:"#fdba74",border:"rgba(251, 146, 60, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"rose",name:"Rose Pink",description:"Elegant rose and pink theme",colors:{primary:"#e11d48",primaryLight:"#f43f5e",primaryDark:"#be123c",secondary:"#ec4899",accent:"#8b5cf6",background:"#1f1720",surface:"rgba(244, 63, 94, 0.05)",text:"#fdf2f8",textSecondary:"#fda4af",border:"rgba(244, 63, 94, 0.2)",success:"#22c55e",warning:"#f59e0b",error:"#ef4444",info:"#06b6d4"}},{id:"midnight",name:"Midnight Blue",description:"Dark midnight blue theme",colors:{primary:"#1e40af",primaryLight:"#3b82f6",primaryDark:"#1e3a8a",secondary:"#4338ca",accent:"#06b6d4",background:"#0c0a1f",surface:"rgba(59, 130, 246, 0.05)",text:"#f8fafc",textSecondary:"#94a3b8",border:"rgba(59, 130, 246, 0.2)",success:"#10b981",warning:"#f59e0b",error:"#ef4444",info:"#3b82f6"}}],l=(0,o.createContext)(void 0),u=()=>{let e=(0,o.useContext)(l);if(!e)throw Error("useTheme must be used within a ThemeProvider");return e},f=({children:e})=>{let[t,r]=(0,o.useState)(d[0]),[a,c]=(0,o.useState)([]);(0,o.useEffect)(()=>{let e=localStorage.getItem("dashboard-color-scheme"),t=localStorage.getItem("dashboard-custom-schemes");if(t)try{let e=JSON.parse(t);c(e)}catch(e){}if(e){let t=d.find(t=>t.id===e);if(t)r(t);else{let t=localStorage.getItem("dashboard-custom-schemes");if(t)try{let a=JSON.parse(t).find(t=>t.id===e);a&&r(a)}catch(e){}}}},[]),(0,o.useEffect)(()=>{localStorage.setItem("dashboard-color-scheme",t.id)},[t]),(0,o.useEffect)(()=>{localStorage.setItem("dashboard-custom-schemes",JSON.stringify(a))},[a]);let u=[...d,...a],f=(0,s.extendTheme)({...i.A,colors:{...i.A.colors,brand:{50:t.colors.primaryLight+"20",100:t.colors.primaryLight+"40",200:t.colors.primaryLight+"60",300:t.colors.primaryLight+"80",400:t.colors.primaryLight,500:t.colors.primary,600:t.colors.primaryDark,700:t.colors.primaryDark+"CC",800:t.colors.primaryDark+"AA",900:t.colors.primaryDark+"88"},custom:{primary:t.colors.primary,primaryLight:t.colors.primaryLight,primaryDark:t.colors.primaryDark,secondary:t.colors.secondary,accent:t.colors.accent,background:t.colors.background,surface:t.colors.surface,text:t.colors.text,textSecondary:t.colors.textSecondary,border:t.colors.border,success:t.colors.success,warning:t.colors.warning,error:t.colors.error,info:t.colors.info}},styles:{global:{body:{bg:t.colors.background,color:t.colors.text}}}});return(0,n.jsx)(l.Provider,{value:{currentScheme:t,setColorScheme:e=>{let t=d.find(t=>t.id===e);if(t)return void r(t);let n=a.find(t=>t.id===e);n&&r(n)},colorSchemes:u,customSchemes:a,addCustomScheme:e=>{c(t=>[...t.filter(t=>t.id!==e.id),e]),r(e)},deleteCustomScheme:e=>{c(t=>t.filter(t=>t.id!==e)),t.id===e&&r(d[0])},resetToDefault:()=>{r(d[0])}},children:(0,n.jsx)(s.ChakraProvider,{theme:f,children:e})})};a()}catch(e){a(e)}})},3118:(e,t,r)=>{"use strict";e.exports=r(1292).vendored.contexts.AmpContext},3567:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var a=r(8732),n=r(8270);function o(){return(0,a.jsxs)(n.Html,{lang:"en",children:[(0,a.jsx)(n.Head,{}),(0,a.jsxs)("body",{children:[(0,a.jsx)(n.Main,{}),(0,a.jsx)(n.NextScript,{})]})]})}},3762:(e,t,r)=>{"use strict";r.d(t,{N:()=>p});var a=r(5542),n=r.n(a);let o=require("next-auth/providers/discord");var s=r.n(o),i=r(9021),c=r(2115),d=r.n(c),l=r(3873);let u={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>l.resolve(process.cwd(),e)).find(e=>i.existsSync(e));if(!e){let t=l.resolve(__dirname,"../../../config.yml");i.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=i.readFileSync(e,"utf8");u=d().parse(t)}catch(e){process.exit(1)}let f={bot:{token:u.bot.token,clientId:u.bot.clientId,clientSecret:u.bot.clientSecret,guildId:u.bot.guildId,ticketCategoryId:u.bot.ticketCategoryId||null,ticketLogChannelId:u.bot.ticketLogChannelId||null,prefix:u.bot.prefix},dashboard:{admins:u.dashboard?.admins||[],adminRoleIds:u.dashboard?.adminRoleIds||[],session:{secret:u.dashboard?.session?.secret||u.bot.clientSecret}},database:{url:u.database.url,name:u.database.name,options:{maxPoolSize:u.database.options?.maxPoolSize||10,minPoolSize:u.database.options?.minPoolSize||1,maxIdleTimeMS:u.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:u.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:u.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:u.database.options?.connectTimeoutMS||1e4,retryWrites:u.database.options?.retryWrites!==!1,retryReads:u.database.options?.retryReads!==!1}}};f.bot.token||process.exit(1),f.bot.clientId&&f.bot.clientSecret||process.exit(1),f.bot.guildId||process.exit(1),f.database.url&&f.database.name||process.exit(1);let p={providers:[s()({clientId:f.bot.clientId,clientSecret:f.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,a=t.accessToken||null;e.user.id=r,e.user.accessToken=a;let n=!1;if(r)if((f.dashboard.admins||[]).includes(r))n=!0;else{let e=f.dashboard.adminRoleIds||[];if(e.length&&f.bot.token&&f.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${f.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${f.bot.token}`}});if(t.ok){let r=await t.json();n=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=n,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),a=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(a)?e:t}},secret:f.dashboard.session.secret||f.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}};n()(p)},3873:e=>{"use strict";e.exports=require("path")},4722:e=>{"use strict";e.exports=require("next-auth/react")},4959:(e,t,r)=>{e.exports=r(8193)},5100:(e,t,r)=>{"use strict";e.exports=r(1292).vendored.contexts.HeadManagerContext},5416:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return s}});let a=r(2015),n=()=>{},o=()=>{};function s(e){var t;let{headManager:r,reduceComponentsToState:s}=e;function i(){if(r&&r.mountedInstances){let t=a.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(s(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),i(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=i),()=>{r&&(r._pendingUpdate=i)})),o(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},5542:e=>{"use strict";e.exports=require("next-auth")},5806:e=>{"use strict";e.exports=require("next-auth/next")},5817:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},6390:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.d(t,{A:()=>s});var n=r(9733),o=e([n]);n=(o.then?(await o)():o)[0];let s=(0,n.extendTheme)({config:{initialColorMode:"dark",useSystemColorMode:!1},fonts:{heading:"'Inter', sans-serif",body:"'Inter', sans-serif"},colors:{brand:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},discord:{50:"#e8e9fd",100:"#d1d3fc",200:"#b9bcfa",300:"#a2a5f9",400:"#8b8ef7",500:"#5865F2",600:"#4752c4",700:"#363f97",800:"#242c69",900:"#12193c"}},styles:{global:{body:{bg:"gray.900",color:"gray.100"}}},components:{Button:{defaultProps:{colorScheme:"brand"},variants:{solid:e=>({bg:`${e.colorScheme}.500`,color:"white",_hover:{bg:`${e.colorScheme}.600`,transform:"translateY(-2px)",boxShadow:"lg"},_active:{bg:`${e.colorScheme}.700`,transform:"translateY(0)"},transition:"all 0.2s ease"})}},Link:{baseStyle:{_hover:{textDecoration:"none"}}},Box:{baseStyle:{transition:"all 0.2s ease"}}}});a()}catch(e){a(e)}})},6665:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:a=!1}=void 0===e?{}:e;return t||r&&a}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},7840:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>b,default:()=>u,getServerSideProps:()=>m,getStaticPaths:()=>p,getStaticProps:()=>f,reportWebVitals:()=>g,routeModule:()=>P,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>v,unstable_getStaticParams:()=>x,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>h});var n=r(1292),o=r(8834),s=r(786),i=r(3567),c=r(8077),d=r(9931),l=e([c]);c=(l.then?(await l)():l)[0];let u=(0,s.M)(d,"default"),f=(0,s.M)(d,"getStaticProps"),p=(0,s.M)(d,"getStaticPaths"),m=(0,s.M)(d,"getServerSideProps"),b=(0,s.M)(d,"config"),g=(0,s.M)(d,"reportWebVitals"),h=(0,s.M)(d,"unstable_getStaticProps"),y=(0,s.M)(d,"unstable_getStaticPaths"),x=(0,s.M)(d,"unstable_getStaticParams"),S=(0,s.M)(d,"unstable_getServerProps"),v=(0,s.M)(d,"unstable_getServerSideProps"),P=new n.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/index",pathname:"/",bundlePath:"",filename:""},components:{App:c.default,Document:i.default},userland:d});a()}catch(e){a(e)}})},8077:(e,t,r)=>{"use strict";r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u});var n=r(8732),o=r(4722),s=r(4959),i=r.n(s),c=r(3001),d=e([c]);function l({Component:e,pageProps:t}){return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(i(),{children:[(0,n.jsx)("title",{children:"404 Bot Dashboard"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,n.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/site.webmanifest"})]}),(0,n.jsx)(e,{...t})]})}function u({Component:e,pageProps:{session:t,...r}}){return(0,n.jsx)(o.SessionProvider,{session:t,children:(0,n.jsx)(c.NP,{children:(0,n.jsx)(l,{Component:e,pageProps:r})})})}c=(d.then?(await d)():d)[0],a()}catch(e){a(e)}})},8193:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},defaultHead:function(){return u}});let a=r(2403),n=r(2742),o=r(8732),s=n._(r(2015)),i=a._(r(5416)),c=r(3118),d=r(5100),l=r(6665);function u(e){void 0===e&&(e=!1);let t=[(0,o.jsx)("meta",{charSet:"utf-8"},"charset")];return e||t.push((0,o.jsx)("meta",{name:"viewport",content:"width=device-width"},"viewport")),t}function f(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===s.default.Fragment?e.concat(s.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(5817);let p=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(f,[]).reverse().concat(u(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,a={};return n=>{let o=!0,s=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){s=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?o=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?o=!1:t.add(n.type);break;case"meta":for(let e=0,t=p.length;e<t;e++){let t=p[e];if(n.props.hasOwnProperty(t))if("charSet"===t)r.has(t)?o=!1:r.add(t);else{let e=n.props[t],r=a[t]||new Set;("name"!==t||!s)&&r.has(e)?o=!1:(r.add(e),a[t]=r)}}}return o}}()).reverse().map((e,t)=>{let a=e.key||t;if(process.env.__NEXT_OPTIMIZE_FONTS&&!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,s.default.cloneElement(e,t)}return s.default.cloneElement(e,{key:a})})}let b=function(e){let{children:t}=e,r=(0,s.useContext)(c.AmpStateContext),a=(0,s.useContext)(d.HeadManagerContext);return(0,o.jsx)(i.default,{reduceComponentsToState:m,headManager:a,inAmpMode:(0,l.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},8834:(e,t)=>{"use strict";Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},9021:e=>{"use strict";e.exports=require("fs")},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")},9931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s,getServerSideProps:()=>o});var a=r(5806),n=r(3762);let o=async e=>{let t=await (0,a.getServerSession)(e.req,e.res,n.N);return t?(t.user?.isAdmin,{redirect:{destination:"/overview",permanent:!1}}):{redirect:{destination:"/signin",permanent:!1}}};function s(){return null}}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[8270],()=>r(7840));module.exports=a})();