"use strict";(()=>{var e={};e.id=7053,e.ids=[7053],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3248:(e,t,o)=>{o.r(t),o.d(t,{config:()=>h,default:()=>g,routeModule:()=>y});var s={};o.r(s),o.d(s,{default:()=>b});var a=o(3433),n=o(264),r=o(584),i=o(5806),d=o(8525),l=o(8580),c=o(2518),u=o(9021),m=o.n(u),p=o(3873),f=o.n(p);async function b(e,t){let o=await (0,i.getServerSession)(e,t,d.authOptions);if(!o)return t.status(401).json({error:"Unauthorized"});if(!o.user.isAdmin)return t.status(403).json({error:"Forbidden"});let{bot:s,database:a}=l.dashboardConfig;if(!s?.token||!s?.clientId)return t.status(500).json({error:"Bot credentials missing"});let n={Authorization:`Bot ${s.token}`,"Content-Type":"application/json"};try{let r=(await c.MongoClient.connect(a.url)).db(a.name).collection("command_states");if("GET"===e.method){let e=await fetch(`https://discord.com/api/v10/applications/${s.clientId}/commands`,{headers:n}),o=e.ok?await e.json():[],a=[];if(s.guildId){let e=await fetch(`https://discord.com/api/v10/applications/${s.clientId}/guilds/${s.guildId}/commands`,{headers:n});a=e.ok?await e.json():[]}let i=await r.find({}).toArray(),d=new Map(i.map(e=>[e.commandId,e.enabled])),l=new Map;try{for(let e of[f().resolve(process.cwd(),"../addons"),f().resolve(process.cwd(),"src/addons"),f().resolve(process.cwd(),"../../addons"),f().resolve(process.cwd(),"../dist/addons"),f().resolve(process.cwd(),"dist/addons")].filter(e=>m().existsSync(e)))for(let t of m().readdirSync(e,{withFileTypes:!0}).filter(e=>e.isDirectory()).map(e=>e.name)){let o=f().join(e,t,"commands");if(m().existsSync(o))for(let e of m().readdirSync(o).filter(e=>e.endsWith(".ts")||e.endsWith(".js")).map(e=>f().basename(e,f().extname(e))))l.set(e,{addon:t,category:t.charAt(0).toUpperCase()+t.slice(1)})}}catch(e){}let c=[...o.map(e=>({scope:"GLOBAL",...e,enabled:d.get(e.id)??!0,addon:l.get(e.name)?.addon||"Unknown",category:l.get(e.name)?.category||"Unknown"})),...a.map(e=>({scope:"GUILD",...e,enabled:d.get(e.id)??!0,addon:l.get(e.name)?.addon||"Unknown",category:l.get(e.name)?.category||"Unknown"}))];return t.status(200).json(c)}if("PUT"===e.method){let{commandId:s,enabled:a}=e.body;if("string"!=typeof s||"boolean"!=typeof a)return t.status(400).json({error:"Invalid request body"});await r.updateOne({commandId:s},{$set:{commandId:s,enabled:a}},{upsert:!0});try{let e=process.cwd().includes(f().join("src","dashboard"))?f().resolve(process.cwd(),"..",".."):process.cwd(),t=f().join(e,"command-state.signal");m().writeFileSync(t,JSON.stringify({commandId:s,enabled:a,requestedBy:o.user?.email||"unknown",timestamp:Date.now()}))}catch(e){}return t.status(200).json({commandId:s,enabled:a})}if("DELETE"===e.method){let{commandId:o,scope:a}=e.query;if(!o)return t.status(400).json({error:"Command ID required"});let i="GUILD"===a&&s.guildId?`https://discord.com/api/v10/applications/${s.clientId}/guilds/${s.guildId}/commands/${o}`:`https://discord.com/api/v10/applications/${s.clientId}/commands/${o}`,d=await fetch(i,{method:"DELETE",headers:n});if(!d.ok){let e=await d.json().catch(()=>({message:"Unknown error"}));return t.status(d.status).json(e)}return await r.deleteOne({commandId:o}),t.status(204).end()}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Failed to handle command operation",details:e.message})}}let g=(0,r.M)(s,"default"),h=(0,r.M)(s,"config"),y=new a.PagesAPIRouteModule({definition:{kind:n.A.PAGES_API,page:"/api/admin/commands",pathname:"/api/admin/commands",bundlePath:"",filename:""},userland:s})},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>d,default:()=>l});var s=o(5542),a=o.n(s);let n=require("next-auth/providers/discord");var r=o.n(n),i=o(8580);let d={providers:[r()({clientId:i.dashboardConfig.bot.clientId,clientSecret:i.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,s=t.accessToken||null;e.user.id=o,e.user.accessToken=s;let a=!1;if(o)if((i.dashboardConfig.dashboard.admins||[]).includes(o))a=!0;else{let e=i.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&i.dashboardConfig.bot.token&&i.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${i.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${i.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();a=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=a,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),s=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:i.dashboardConfig.dashboard.session.secret||i.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=a()(d)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>d,default:()=>l});var s=o(9021),a=o(2115),n=o.n(a),r=o(3873);let i={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>r.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=r.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");i=n().parse(t)}catch(e){process.exit(1)}let d={bot:{token:i.bot.token,clientId:i.bot.clientId,clientSecret:i.bot.clientSecret,guildId:i.bot.guildId,ticketCategoryId:i.bot.ticketCategoryId||null,ticketLogChannelId:i.bot.ticketLogChannelId||null,prefix:i.bot.prefix},dashboard:{admins:i.dashboard?.admins||[],adminRoleIds:i.dashboard?.adminRoleIds||[],session:{secret:i.dashboard?.session?.secret||i.bot.clientSecret}},database:{url:i.database.url,name:i.database.name,options:{maxPoolSize:i.database.options?.maxPoolSize||10,minPoolSize:i.database.options?.minPoolSize||1,maxIdleTimeMS:i.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:i.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:i.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:i.database.options?.connectTimeoutMS||1e4,retryWrites:i.database.options?.retryWrites!==!1,retryReads:i.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=3248);module.exports=o})();