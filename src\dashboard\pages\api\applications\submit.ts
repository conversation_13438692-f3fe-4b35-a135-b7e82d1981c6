import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';
import { Session } from 'next-auth';
import { DefaultUser } from 'next-auth';

const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase(): Promise<MongoClient> {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    res.setHeader('Allow', ['POST']);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const session: Session | null = await getServerSession(req, res, authOptions);

  if (!session || !session.user) { // Only check if a user is logged in, not necessarily an admin
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { applicationId, answers } = req.body;

  if (!applicationId || !answers) {
    return res.status(400).json({ error: 'Missing applicationId or answers' });
  }

  let client: MongoClient;
  try {
    client = await connectToDatabase();
    const db = client.db(dbName);

    const submissionCollection = db.collection('application_submissions');

    const newSubmission = {
      applicationId,
      answers,
      submittedBy: (session.user as DefaultUser & { id: string; isAdmin: boolean; }).id,
      submittedAt: new Date(),
      status: 'pending', // Initial status
    };

    await submissionCollection.insertOne(newSubmission);

    return res.status(201).json({ message: 'Application submitted successfully!', submission: newSubmission });
  } catch (error) {
    console.error('Error submitting application:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 