# 404 Bot – Hosting & Dashboard Setup

This guide assumes you are using a typical game-panel / container host such as **Pterodactyl**, **Docker**, or any VPS where you can upload files and define environment variables.

---

## 1. Files to Upload

1. C<PERSON> or download the repository, then copy **everything** to your server (or upload the zip via the panel).
2. Rename `config.example.yml` to `config.yml` and fill in **bot token**, **client ID**, **client secret**, etc.

👉 **You do *not* have to edit the dashboard URL inside the YAML any more** – the project now picks it up from an environment variable so you can reuse the same config for local and production.

---

## 2. Required Environment Variables

| Variable          | Example value                            | Purpose                           |
|-------------------|------------------------------------------|-----------------------------------|
| `SERVER_PORT`     | `3000` (or the port allocated by the panel) | Port your host forwards           |
| `NEXTAUTH_URL`    | `http://*************:3000`              | Public URL of the dashboard       |
| *(alias)* `DASHBOARD_URL` | `http://*************:3000`      | Same as above – alternative name  |

> Set these in **Startup / Environment** section of your panel.  
> `NEXTAUTH_URL` is the one required by Next-Auth / Discord OAuth.

---

## 3. Install & Build

Most panels will do this automatically from the `package.json` scripts, but manually:

```bash
# Install dependencies (only once)
pnpm install   # or npm install / yarn install

# Compile the bot and copy static assets
npm run build
# Build the dashboard (Next.js)
npm run build:dashboard
```

You can also skip building locally – upload the source and let the panel run the commands above on first start.

---

## 4. Startup Commands

Add **two separate servers / services** if you want to scale independently, or run both commands inside one container (tmux / pm2).  The dashboard will automatically use `SERVER_PORT` so you don't need to edit anything else:

### Bot
```bash
npm start
```

### Dashboard
```bash
npm run start:dashboard   # binds 0.0.0.0:${SERVER_PORT}
```

Both scripts already listen on all interfaces (`0.0.0.0`).

---

## 5. Discord Developer Portal

1. Go to **OAuth2 ➜ Redirects**.
2. Add the callback that matches your public URL **exactly**:

```
http://*************:3000/api/auth/callback/discord
```

(Replace the IP/domain and port to match *your* setup.)

---

## 6. Test

1. Visit `http://<your-ip>:3000` in a browser.
2. Click **Sign in with Discord**.
3. You should be redirected back to the same host, *not* to localhost.

If something breaks, check:
- Environment variables (`echo $NEXTAUTH_URL` inside the container).
- Port forwarding / firewall.
- Discord redirect URI spelling.

---

## 7. One-line Checklist ✅

```
✔ Upload files
✔ Rename & fill config.yml (token, IDs)
✔ Set env vars: SERVER_PORT & NEXTAUTH_URL
✔ Build: npm run build && npm run build:dashboard
✔ Start bot           → npm start
✔ Start dashboard     → npm run start:dashboard
✔ Add redirect in Discord Dev Portal
```

Happy hosting! 🚀 