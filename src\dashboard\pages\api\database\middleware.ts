import { MongoClient } from 'mongodb';
import { dashboardConfig } from '../../../core/config';

const { url: mongoUrl, name: dbName } = dashboardConfig.database;

export async function logDatabaseOperation(operation: string, collection: string, details: any) {
  let client: MongoClient | null = null;
  
  try {
    client = await MongoClient.connect(mongoUrl);
    const db = client.db(dbName);
    const logsCollection = db.collection('database_logs');
    
    await logsCollection.insertOne({
      timestamp: new Date(),
      operation,
      collection,
      details,
      id: new Date().getTime().toString()
    });
  } catch (error) {
    console.error('Failed to log database operation:', error);
  } finally {
    if (client) {
      await client.close();
    }
  }
} 