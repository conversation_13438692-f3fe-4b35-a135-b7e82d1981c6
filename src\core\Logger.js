class Logger {
  static write(message, level = 'INFO') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${level}: ${message}\n`;
    
    // Write to file
    fs.appendFileSync('logs/app.log', logEntry);
    
    // Write to console
    switch(level.toUpperCase()) {
      case 'ERROR':
        console.error(logEntry);
        break;
      case 'WARNING':
        console.warn(logEntry);
        break;
      default:
        console.log(logEntry);
    }
  }

  static error(message) {
    Logger.write(message, 'ERROR');
  }

  static warning(message) {
    Logger.write(message, 'WARNING');
  }

  static info(message) {
    Logger.write(message, 'INFO');
  }
}

module.exports = Logger;
