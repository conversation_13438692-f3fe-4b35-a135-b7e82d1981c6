// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { MongoClient, ObjectId } from 'mongodb';
import { dashboardConfig } from '../../../../core/config';

const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;
async function connect() {
  if (cachedClient) return cachedClient;
  cachedClient = await MongoClient.connect(mongoUrl);
  return cachedClient;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) return res.status(401).json({ error: 'Unauthorized' });

  const { id } = req.query;
  if (!id || Array.isArray(id)) return res.status(400).json({ error: 'Invalid ticket id' });

  const {
    token: botToken,
    guildId,
    ticketLogChannelId,
  } = dashboardConfig.bot as any;
  if (!botToken || !guildId) return res.status(500).json({ error: 'Bot configuration missing' });

  // db
  let client: MongoClient;
  try {
    client = await connect();
  } catch (err) {
    console.error('DB connect error', err);
    return res.status(500).json({ error: 'Database connection failed' });
  }
  const tickets = client.db(dbName).collection('tickets');

  // fetch ticket document
  let ticket;
  try {
    ticket = await tickets.findOne({ _id: new ObjectId(id) });
    if (!ticket) return res.status(404).json({ error: 'Ticket not found' });
  } catch (err) {
    console.error('Ticket find error', err);
    return res.status(500).json({ error: 'Database error' });
  }

  const isAdmin = (session.user as any).isAdmin;
  const isCreator = ticket.creatorId === (session.user as any).id;
  if (!isAdmin && !isCreator) return res.status(403).json({ error: 'Forbidden' });

  switch (req.method) {
    case 'PATCH': {
      if (ticket.status === 'closed') {
        return res.status(400).json({ error: 'Ticket already closed' });
      }

      // Build overwrites to lock the channel (deny SEND_MESSAGES) but still allow view
      try {
        await fetch(`https://discord.com/api/v10/channels/${ticket.channelId}`, {
          method: 'PATCH',
          headers: {
            Authorization: `Bot ${botToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: `closed-${ticket.channelId}`.slice(0, 100),
            permission_overwrites: [
              {
                id: guildId,
                type: 0,
                deny: '2048', // SEND_MESSAGES
              },
              {
                id: ticket.creatorId,
                type: 1,
                deny: '2048',
              },
            ],
          }),
        });
      } catch (err) {
        console.error('Discord channel lock error', err);
      }

      // Fetch messages from Discord to build transcript
      const fetchApiMessages = async () => {
        const msgs: any[] = [];
        let before;
        while (true) {
          const url = `https://discord.com/api/v10/channels/${ticket.channelId}/messages?limit=100${before ? `&before=${before}` : ''}`;
          const resp = await fetch(url, { headers: { Authorization: `Bot ${botToken}` } });
          if (!resp.ok) break;
          const batch = await resp.json();
          msgs.push(...batch);
          if (batch.length < 100) break;
          before = batch[batch.length-1].id;
        }
        msgs.reverse();
        return msgs;
      };

      const msgs = await fetchApiMessages();

      // Helper functions for base64 conversion
      const fetchToDataUri = async (url: string, mimeGuess: string): Promise<string> => {
        try {
          const res = await fetch(url);
          const buf = await res.arrayBuffer();
          if (buf.byteLength > 1024*1024) return url; // too big
          const base64 = Buffer.from(buf).toString('base64');
          return `data:${mimeGuess};base64,${base64}`;
        } catch { return url; }
      };

      const replaceAsync = async (str: string, regex: RegExp, asyncFn: (...args:any[])=>Promise<string>): Promise<string> => {
        const matches = Array.from(str.matchAll(regex));
        if (matches.length === 0) return str;
        
        const results = await Promise.all(matches.map(match => asyncFn(...match)));
        
        let result = str;
        for (let i = matches.length - 1; i >= 0; i--) {
          const match = matches[i];
          const replacement = results[i];
          result = result.substring(0, match.index!) + replacement + result.substring(match.index! + match[0].length);
        }
        return result;
      };

      const attachmentToImg = async (att: any) => {
        if (!att.content_type?.startsWith('image/') || att.size > 1024 * 1024) {
          return att.content_type?.startsWith('image/') ? '' : `<a href="${att.url}" target="_blank">${att.filename}</a>`;
        }
        try {
          const buf = await (await fetch(att.url)).arrayBuffer();
          const base64 = Buffer.from(buf).toString('base64');
          return `<img class="attach" src="data:${att.content_type};base64,${base64}" />`;
        } catch {
          return `<img class="attach" src="${att.url}" />`;
        }
      };

      const rowsPromises = msgs.map(async m=>{
        const time = new Date(m.timestamp).toLocaleString();
        const avatar = m.author.avatar
          ? `https://cdn.discordapp.com/avatars/${m.author.id}/${m.author.avatar}.png?size=32`
          : `https://cdn.discordapp.com/embed/avatars/${parseInt(m.author.discriminator) % 5}.png`;
        let content = m.content || '';
        
        console.log('Dashboard API - Original content (before HTML encoding):', content);
        
        // Handle custom emojis - simplified approach
        const emojiMatches = content.match(/<a?:[\w]+:\d+>/g);
        console.log('Dashboard API - Found emoji matches:', emojiMatches);
        
        if (emojiMatches) {
          for (const emojiMatch of emojiMatches) {
            console.log('Dashboard API - Processing emoji match:', emojiMatch);
            const isAnimated = emojiMatch.startsWith('<a:');
            const emojiIdMatch = emojiMatch.match(/:(\d+)>/);
            
            if (emojiIdMatch) {
              const emojiId = emojiIdMatch[1];
              const ext = isAnimated ? 'gif' : 'png';
              const emojiUrl = `https://cdn.discordapp.com/emojis/${emojiId}.${ext}`;
              console.log('Dashboard API - Emoji URL:', emojiUrl);
              
              try {
                const dataUri = await fetchToDataUri(emojiUrl, `image/${ext}`);
                console.log('Dashboard API - Emoji dataUri length:', dataUri.length);
                content = content.replace(emojiMatch, `<img class="emoji" src="${dataUri}" />`);
              } catch (e) {
                console.error('Dashboard API - Failed to convert emoji:', e);
              }
            }
          }
        }

        // Handle URLs - simplified approach for Tenor
        const tenorRegex = /(https?:\/\/tenor\.com\/view\/[^\s]+)/g;
        content = await replaceAsync(content, tenorRegex, async (link: string) => {
          const idMatch = link.match(/-(\d+)(?:\?.*)?$/);
          if (!idMatch) return `<a href="${link}" target="_blank">🎬 Tenor GIF</a>`;

          const tenorId = idMatch[1];
          try {
            const apiResp = await fetch(`https://g.tenor.com/v1/gifs?ids=${tenorId}&key=LIVDSRZULELA`);
            const apiJson: any = await apiResp.json();
            if (!apiJson?.results?.length) throw 0;

            const media = apiJson.results[0].media_formats || apiJson.results[0].media?.[0];
            const order = ['mediumgif','gif','tinygif','nanogif'];
            let chosenUrl: string | undefined;
            for (const k of order) {
              if (media?.[k]?.url) { chosenUrl = media[k].url; break; }
            }
            if (!chosenUrl) throw 0;

            const dataUri = await fetchToDataUri(chosenUrl, 'image/gif');
            if (dataUri.startsWith('data:')) {
              return `<img class="attach" src="${dataUri}" />`;
            }
            return `<img class="attach" src="${chosenUrl}" />`;
          } catch {
            return `<a href="${link}" target="_blank">🎬 Tenor GIF</a>`;
          }
        });

        // Handle direct image URLs
        const imageMatches = content.match(/https?:\/\/[^\s]+\.(png|jpe?g|gif|webp)/gi);
        console.log('Dashboard API - Found direct image matches:', imageMatches);
        
        if (imageMatches) {
          for (const imageUrl of imageMatches) {
            console.log('Dashboard API - Processing direct image URL:', imageUrl);
            try {
              const mime = imageUrl.endsWith('.gif') ? 'image/gif' : 
                         imageUrl.endsWith('.webp') ? 'image/webp' : 
                         imageUrl.endsWith('.png') ? 'image/png' : 'image/jpeg';
              const dataUri = await fetchToDataUri(imageUrl, mime);
              console.log('Dashboard API - Direct image dataUri length:', dataUri.length);
              content = content.replace(imageUrl, `<img class="attach" src="${dataUri}" />`);
            } catch (e) {
              console.error('Dashboard API - Failed to convert direct image:', e);
            }
          }
        }

        // HTML encode everything except our img tags
        content = content
          .replace(/</g, '|||LT|||')  // temp placeholder
          .replace(/>/g, '|||GT|||')  // temp placeholder
          .replace(/|||LT|||img/g, '<img')  // restore img tags
          .replace(/src="[^"]*"[^>]*|||GT|||/g, (match: string) => match.replace(/|||GT|||/g, '>'))  // restore img closing
          .replace(/\/|||GT|||/g, '/>')  // restore self-closing tags
          .replace(/|||LT|||\/img|||GT|||/g, '</img>')  // restore closing img tags
          .replace(/|||LT|||a href/g, '<a href')  // restore link tags
          .replace(/|||LT|||\/a|||GT|||/g, '</a>')  // restore closing link tags
          .replace(/target="_blank"[^>]*|||GT|||/g, (match: string) => match.replace(/|||GT|||/g, '>'))  // restore link closing
          .replace(/|||LT|||/g, '&lt;')  // convert remaining < to HTML entities
          .replace(/|||GT|||/g, '&gt;')  // convert remaining > to HTML entities
          .replace(/\n/g, '<br/>');
        
        console.log('Dashboard API - Final content (after selective HTML encoding):', content);
        
        // Handle attachments
        let attachHtml = '';
        if (m.attachments?.length > 0) {
          const imgs = await Promise.all(m.attachments.map(attachmentToImg));
          attachHtml = imgs.join('');
        }

        return `<div class="msg"><img class="avatar" src="${avatar}"/><div class="bubble"><div class="meta"><span class="name">${m.author.username}${m.author.discriminator ? `#${m.author.discriminator}` : ''}</span><span class="time">${time}</span></div><div class="content">${content}${attachHtml}</div></div></div>`;
      });

      const rows = (await Promise.all(rowsPromises)).join('\n');

      const badgeColor = (category: string) => ({support:'#3b82f6', '18plus':'#ef4444', other:'#a855f7'}[category] || '#6b7280');

      const transcriptHtml = `<!DOCTYPE html>
<html lang="en"><head><meta charset="utf-8"/><title>Ticket ${id} Transcript</title>
<style>
*{box-sizing:border-box;margin:0;padding:0}
body{font-family:Segoe UI,Arial,sans-serif;background:#0f172a;color:#e2e8f0;min-height:100vh;display:flex;flex-direction:column;align-items:center;padding:40px 12px;background-image:radial-gradient(circle at 25% 25%,rgba(29,78,216,.4),transparent 60%),radial-gradient(circle at 75% 75%,rgba(60,46,123,.35),transparent 60%)}
h2{color:#60a5fa;margin-bottom:12px;font-weight:600;letter-spacing:.3px;text-shadow:0 1px 2px rgba(0,0,0,.4)}
#wrapper{width:100%;max-width:860px}
.msg{display:flex;gap:12px;margin-bottom:18px;animation:fadeIn .4s ease}
.avatar{width:42px;height:42px;border-radius:50%;flex-shrink:0;box-shadow:0 0 0 2px rgba(255,255,255,.07)}
.bubble{background:rgba(255,255,255,0.06);padding:10px 14px;border-radius:10px;backdrop-filter:blur(14px) saturate(130%);width:fit-content;max-width:90%}
.meta{font-size:12px;color:#94a3b8;margin-bottom:6px;display:flex;flex-wrap:wrap;gap:6px 10px}
.name{color:#82b1ff;font-weight:600}
.content{font-size:14px;line-height:1.5;word-break:break-word}
.emoji{width:20px;height:20px;vertical-align:-4px}
.attach{margin-top:8px;max-width:260px;border-radius:8px;box-shadow:0 2px 6px rgba(0,0,0,.4)}
@keyframes fadeIn{from{opacity:0;transform:translateY(6px)}to{opacity:1;transform:translateY(0)}}
.badge{display:inline-block;padding:4px 10px;border-radius:999px;font-size:12px;font-weight:600;margin-bottom:28px;background:var(--badge-bg);color:#fff;box-shadow:0 1px 3px rgba(0,0,0,.4)}
</style></head><body>
<h2>Ticket ${id} Transcript</h2>
<span class="badge" style="--badge-bg:${badgeColor(ticket.category || 'other')}">${ticket.category || 'Support'}</span>
<div id="wrapper">
${rows}
</div>
</body></html>`;

      // update doc
      await tickets.updateOne({ _id: new ObjectId(id) }, {
        $set: { status: 'closed', closedAt: new Date(), transcriptHtml },
      });

      // log
      if (ticketLogChannelId) {
        try {
          await fetch(`https://discord.com/api/v10/channels/${ticketLogChannelId}/messages`, {
            method: 'POST',
            headers: {
              Authorization: `Bot ${botToken}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              content: `✅ Ticket <#${ticket.channelId}> closed by <@${(session.user as any).id}>`,
            }),
          });
        } catch {}
      }

      return res.status(200).json({ message: 'Ticket closed' });
    }

    case 'DELETE': {
      if (!isAdmin) return res.status(403).json({ error: 'Admin only' });

      // delete channel on Discord first
      try {
        await fetch(`https://discord.com/api/v10/channels/${ticket.channelId}`, {
          method: 'DELETE',
          headers: { Authorization: `Bot ${botToken}` },
        });
      } catch (err) {
        console.error('Discord delete channel error', err);
      }
      await tickets.deleteOne({ _id: new ObjectId(id) });

      if (ticketLogChannelId) {
        try {
          await fetch(`https://discord.com/api/v10/channels/${ticketLogChannelId}/messages`, {
            method: 'POST',
            headers: { Authorization: `Bot ${botToken}`, 'Content-Type': 'application/json' },
            body: JSON.stringify({ content: `🗑️ Ticket ${id} deleted by <@${(session.user as any).id}>` }),
          });
        } catch {}
      }

      return res.status(200).json({ message: 'Ticket deleted' });
    }

    default:
      res.setHeader('Allow', ['PATCH', 'DELETE']);
      return res.status(405).json({ error: `Method ${req.method} not allowed` });
  }
} 