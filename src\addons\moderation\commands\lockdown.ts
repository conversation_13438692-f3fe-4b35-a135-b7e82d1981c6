import { <PERSON>lash<PERSON><PERSON>mand<PERSON>uilder, ChatInputCommandInteraction, PermissionFlagsBits, ChannelType, TextChannel } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('lockdown')
  .setDescription('Lock or unlock a channel (prevent/allow @everyone from sending messages)')
  .addBooleanOption(option =>
    option.setName('lock')
      .setDescription('True to lock, false to unlock')
      .setRequired(true))
  .addChannelOption(option =>
    option.setName('channel')
      .setDescription('Channel to lock/unlock (defaults to current)')
      .addChannelTypes(ChannelType.GuildText)
      .setRequired(false))
  .addStringOption(option =>
    option.setName('reason')
      .setDescription('Reason for lockdown')
      .setRequired(false))
  .setDefaultMemberPermissions(PermissionFlagsBits.ManageChannels);

export async function execute(interaction: ChatInputCommandInteraction, _bot: BotInstance) {
  const lock = interaction.options.getBoolean('lock', true);
  const channelOption = interaction.options.getChannel('channel');
  const channel = (channelOption ?? interaction.channel) as TextChannel | null;
  const reason = interaction.options.getString('reason') ?? 'No reason provided';

  if (!interaction.memberPermissions?.has(PermissionFlagsBits.ManageChannels)) {
    await interaction.reply({ content: '❌ You do not have permission to manage channels.', ephemeral: true });
    return;
  }

  if (!channel || channel.type !== ChannelType.GuildText) {
    await interaction.reply({ content: '❌ Invalid channel specified.', ephemeral: true });
    return;
  }

  if (!interaction.guild?.roles.everyone) {
    await interaction.reply({ content: '❌ Could not find @everyone role.', ephemeral: true });
    return;
  }

  try {
    const everyoneRole = interaction.guild.roles.everyone;
    
    if (lock) {
      // Lock the channel - deny send messages permission
      await channel.permissionOverwrites.edit(everyoneRole, {
        SendMessages: false
      }, { reason });
      
      await interaction.reply({ 
        content: `🔒 **Locked** ${channel} - @everyone can no longer send messages | Reason: ${reason}` 
      });
    } else {
      // Unlock the channel - remove the send messages override (inherit from role)
      await channel.permissionOverwrites.edit(everyoneRole, {
        SendMessages: null
      }, { reason });
      
      await interaction.reply({ 
        content: `🔓 **Unlocked** ${channel} - @everyone can now send messages | Reason: ${reason}` 
      });
    }
  } catch (error) {
    console.error('Error with lockdown:', error);
    await interaction.reply({ content: '❌ Failed to change channel lockdown. Please try again.', ephemeral: true });
  }
}

export const cooldown = 3000; 