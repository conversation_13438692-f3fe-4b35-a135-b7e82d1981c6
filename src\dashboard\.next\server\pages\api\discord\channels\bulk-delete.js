"use strict";(()=>{var e={};e.id=5641,e.ids=[5641],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return r}});var r=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,r){return r in t?t[r]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,r)):"function"==typeof t&&"default"===r?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2886:(e,t,r)=>{r.r(t),r.d(t,{config:()=>m,default:()=>h,routeModule:()=>b});var s={};r.r(s),r.d(s,{default:()=>f});var a=r(3433),i=r(264),o=r(584),n=r(5806),d=r(8525),l=r(8580);async function c(e,t,r=0){try{let s=await fetch(`https://discord.com/api/v10/channels/${t}`,{method:"DELETE",headers:{Authorization:`Bot ${e}`}});if(s.ok)return!0;if(429===s.status){let a=await s.json(),i=1e3*(a.retry_after||1);if(await new Promise(e=>setTimeout(e,i)),r<2)return c(e,t,r+1)}return!1}catch(s){if(r<2)return await new Promise(e=>setTimeout(e,1e3)),c(e,t,r+1);return!1}}async function u(e,t){return(await Promise.all(t.map(async t=>{let r=await c(e,t);return{channelId:t,success:r}}))).reduce((e,{channelId:t,success:r})=>(r?e.succeeded.push(t):e.failed.push(t),e),{succeeded:[],failed:[]})}async function f(e,t){try{let r=await (0,n.getServerSession)(e,t,d.authOptions);if(!r?.user)return t.status(401).json({error:"Unauthorized"});if(!r.user.isAdmin)return t.status(403).json({error:"Forbidden - Admin access required"});let{guildId:s,token:a}=l.dashboardConfig.bot;if(!a||!s)return t.status(500).json({error:"Bot configuration missing"});if("POST"===e.method)try{let{channelIds:r}=e.body;if(!Array.isArray(r)||0===r.length)return t.status(400).json({error:"Channel IDs array is required"});let i=await fetch(`https://discord.com/api/v10/guilds/${s}/channels`,{headers:{Authorization:`Bot ${a}`}});if(!i.ok)throw Error("Failed to fetch channels for validation");let o=await i.json();r.filter(e=>{let t=o.find(t=>t.id===e);return t&&2===t.type}).length;let n={succeeded:[],failed:[]};for(let e=0;e<r.length;e+=5){let t=r.slice(e,e+5),s=await u(a,t);n.succeeded.push(...s.succeeded),n.failed.push(...s.failed),e+5<r.length&&await new Promise(e=>setTimeout(e,1e3))}return t.status(200).json({message:`Successfully deleted ${n.succeeded.length} channels${n.failed.length>0?`, failed to delete ${n.failed.length} channels`:""}`,succeeded:n.succeeded,failed:n.failed})}catch(e){return t.status(500).json({error:"Failed to delete channels"})}return t.status(405).json({error:"Method not allowed"})}catch(e){return t.status(500).json({error:"Internal server error"})}}let h=(0,o.M)(s,"default"),m=(0,o.M)(s,"config"),b=new a.PagesAPIRouteModule({definition:{kind:i.A.PAGES_API,page:"/api/discord/channels/bulk-delete",pathname:"/api/discord/channels/bulk-delete",bundlePath:"",filename:""},userland:s})},3433:(e,t,r)=>{e.exports=r(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},8525:(e,t,r)=>{r.r(t),r.d(t,{authOptions:()=>d,default:()=>l});var s=r(5542),a=r.n(s);let i=require("next-auth/providers/discord");var o=r.n(i),n=r(8580);let d={providers:[o()({clientId:n.dashboardConfig.bot.clientId,clientSecret:n.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:r})=>(t&&r&&(e.accessToken=t.access_token||null,e.id=r.id||null),e),async session({session:e,token:t}){if(e?.user){let r=t.id||null,s=t.accessToken||null;e.user.id=r,e.user.accessToken=s;let a=!1;if(r)if((n.dashboardConfig.dashboard.admins||[]).includes(r))a=!0;else{let e=n.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&n.dashboardConfig.bot.token&&n.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${n.dashboardConfig.bot.guildId}/members/${r}`,{headers:{Authorization:`Bot ${n.dashboardConfig.bot.token}`}});if(t.ok){let r=await t.json();a=e.some(e=>r.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=a,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let r=new URL(t),s=`${r.protocol}//localhost${r.port?`:${r.port}`:""}`;return e.startsWith(t)||e.startsWith(s)?e:t}},secret:n.dashboardConfig.dashboard.session.secret||n.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=a()(d)},8580:(e,t,r)=>{r.r(t),r.d(t,{dashboardConfig:()=>d,default:()=>l});var s=r(9021),a=r(2115),i=r.n(a),o=r(3873);let n={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>o.resolve(process.cwd(),e)).find(e=>s.existsSync(e));if(!e){let t=o.resolve(__dirname,"../../../config.yml");s.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=s.readFileSync(e,"utf8");n=i().parse(t)}catch(e){process.exit(1)}let d={bot:{token:n.bot.token,clientId:n.bot.clientId,clientSecret:n.bot.clientSecret,guildId:n.bot.guildId,ticketCategoryId:n.bot.ticketCategoryId||null,ticketLogChannelId:n.bot.ticketLogChannelId||null,prefix:n.bot.prefix},dashboard:{admins:n.dashboard?.admins||[],adminRoleIds:n.dashboard?.adminRoleIds||[],session:{secret:n.dashboard?.session?.secret||n.bot.clientSecret}},database:{url:n.database.url,name:n.database.name,options:{maxPoolSize:n.database.options?.maxPoolSize||10,minPoolSize:n.database.options?.minPoolSize||1,maxIdleTimeMS:n.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:n.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:n.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:n.database.options?.connectTimeoutMS||1e4,retryWrites:n.database.options?.retryWrites!==!1,retryReads:n.database.options?.retryReads!==!1}}};d.bot.token||process.exit(1),d.bot.clientId&&d.bot.clientSecret||process.exit(1),d.bot.guildId||process.exit(1),d.database.url&&d.database.name||process.exit(1);let l=d},9021:e=>{e.exports=require("fs")}};var t=require("../../../../webpack-api-runtime.js");t.C(e);var r=t(t.s=2886);module.exports=r})();