import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { getSupportedGames, searchGames, getGameTypeId } from '../../../utils/gamedig';

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  const session = await getServerSession(req, res, authOptions);

  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { search, type } = req.query;

    // If type is provided, try to get the proper game type ID
    if (type) {
      const gameType = getGameTypeId(type as string);
      if (!gameType) {
        return res.status(404).json({ error: 'Game type not found' });
      }
      return res.status(200).json({ type: gameType });
    }

    // If search is provided, search for games
    if (search) {
      const results = searchGames(search as string);
      return res.status(200).json(results);
    }

    // Otherwise, return all supported games
    const games = getSupportedGames();
    return res.status(200).json(games);
  } catch (error) {
    console.error('Error handling game type request:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 