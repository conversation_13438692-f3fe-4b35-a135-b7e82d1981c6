"use strict";exports.id=5129,exports.ids=[5129],exports.modules={5129:(e,s,o)=>{o.a(e,async(e,i)=>{try{o.r(s),o.d(s,{default:()=>c});var r=o(8732),n=o(9733),t=o(2015),l=o(8079),a=e([n]);n=(a.then?(await a)():a)[0];let d={General:{icon:l.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:l.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","MANAGE_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:l.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:l.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function c({isOpen:e,onClose:s,onSuccess:o}){let i=(0,n.useToast)(),[l,a]=(0,t.useState)(!1),[c,E]=(0,t.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),h=async()=>{if(!c.name.trim())return void i({title:"Error",description:"Role name is required",status:"error",duration:3e3});a(!0);try{let e=await fetch("/api/discord/roles",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(c)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to create role")}i({title:"Success",description:"Role created successfully",status:"success",duration:3e3}),E({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1}),o(),s()}catch(e){i({title:"Error",description:e.message||"Failed to create role",status:"error",duration:5e3})}finally{a(!1)}},m=e=>{E(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},A=(e,s)=>{E(o=>({...o,[e]:s}))};return(0,r.jsxs)(n.Modal,{isOpen:e,onClose:s,size:"xl",scrollBehavior:"inside",children:[(0,r.jsx)(n.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,r.jsxs)(n.ModalContent,{bg:"gray.800",children:[(0,r.jsx)(n.ModalHeader,{children:"Create New Role"}),(0,r.jsx)(n.ModalCloseButton,{}),(0,r.jsx)(n.ModalBody,{children:(0,r.jsxs)(n.VStack,{spacing:6,children:[(0,r.jsxs)(n.FormControl,{isRequired:!0,children:[(0,r.jsx)(n.FormLabel,{children:"Role Name"}),(0,r.jsx)(n.Input,{placeholder:"Enter role name",value:c.name,onChange:e=>A("name",e.target.value)})]}),(0,r.jsxs)(n.FormControl,{children:[(0,r.jsx)(n.FormLabel,{children:"Role Color"}),(0,r.jsx)(n.Input,{type:"color",value:c.color,onChange:e=>A("color",e.target.value)})]}),(0,r.jsx)(n.FormControl,{children:(0,r.jsxs)(n.HStack,{spacing:4,children:[(0,r.jsx)(n.Checkbox,{isChecked:c.hoist,onChange:e=>A("hoist",e.target.checked),children:"Display role separately"}),(0,r.jsx)(n.Checkbox,{isChecked:c.mentionable,onChange:e=>A("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,r.jsx)(n.Divider,{}),(0,r.jsx)(n.Text,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(d).map(([e,s])=>(0,r.jsxs)(n.Box,{w:"full",children:[(0,r.jsxs)(n.HStack,{mb:2,children:[(0,r.jsx)(n.Icon,{as:s.icon}),(0,r.jsx)(n.Text,{fontWeight:"semibold",children:e})]}),(0,r.jsx)(n.SimpleGrid,{columns:2,spacing:2,children:s.permissions.map(e=>(0,r.jsx)(n.Checkbox,{isChecked:c.permissions.includes(e),onChange:()=>m(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},e))]})}),(0,r.jsxs)(n.ModalFooter,{children:[(0,r.jsx)(n.Button,{variant:"ghost",mr:3,onClick:s,children:"Cancel"}),(0,r.jsx)(n.Button,{colorScheme:"blue",onClick:h,isLoading:l,children:"Create Role"})]})]})]})}i()}catch(e){i(e)}})}};