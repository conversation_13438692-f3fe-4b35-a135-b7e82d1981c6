"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/admin/addons";
exports.ids = ["pages/admin/addons"];
exports.modules = {

/***/ "(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Caddons.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Caddons.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.tsx\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.tsx\");\n/* harmony import */ var _pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages\\admin\\addons.tsx */ \"(pages-dir-node)/./pages/admin/addons.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n([private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__, _pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/admin/addons\",\n        pathname: \"/admin/addons\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_admin_addons_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Caddons.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _Navbar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./Navbar */ \"(pages-dir-node)/./components/Navbar.tsx\");\n/* harmony import */ var _Sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./Sidebar */ \"(pages-dir-node)/./components/Sidebar.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _Navbar__WEBPACK_IMPORTED_MODULE_2__, _Sidebar__WEBPACK_IMPORTED_MODULE_3__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _Navbar__WEBPACK_IMPORTED_MODULE_2__, _Sidebar__WEBPACK_IMPORTED_MODULE_3__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst Layout = ({ children })=>{\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n        minH: \"100vh\",\n        bg: currentScheme.colors.background,\n        position: \"relative\",\n        overflow: \"hidden\",\n        _before: {\n            content: '\"\"',\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bgImage: `\n          radial-gradient(circle at 15% 50%, ${currentScheme.colors.primary}15 0%, transparent 25%),\n          radial-gradient(circle at 85% 30%, ${currentScheme.colors.accent}15 0%, transparent 25%)\n        `,\n            zIndex: 0\n        },\n        _after: {\n            content: '\"\"',\n            position: 'fixed',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backdropFilter: 'blur(100px)',\n            zIndex: 0\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n            position: \"relative\",\n            zIndex: 1,\n            display: \"flex\",\n            flexDirection: \"column\",\n            minH: \"100vh\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    position: \"fixed\",\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    zIndex: 30,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Navbar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    display: \"flex\",\n                    flex: \"1\",\n                    position: \"relative\",\n                    pt: \"4rem\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                            position: \"fixed\",\n                            top: \"4rem\",\n                            bottom: 0,\n                            left: 0,\n                            w: \"64\",\n                            zIndex: 20,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Sidebar__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                            flex: \"1\",\n                            ml: \"64\",\n                            p: {\n                                base: 4,\n                                md: 8\n                            },\n                            maxW: \"100%\",\n                            transition: \"all 0.3s\",\n                            position: \"relative\",\n                            _before: {\n                                content: '\"\"',\n                                position: 'absolute',\n                                top: 0,\n                                left: 0,\n                                right: 0,\n                                bottom: 0,\n                                bg: 'linear-gradient(135deg, rgba(255,255,255,0.03) 0%, transparent 100%)',\n                                pointerEvents: 'none',\n                                zIndex: -1\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Container, {\n                                maxW: \"container.xl\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n            lineNumber: 43,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Layout.tsx\",\n        lineNumber: 14,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Layout.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Navbar.tsx":
/*!*******************************!*\
  !*** ./components/Navbar.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_FiLogOut_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiLogOut!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../hooks/useGuildInfo */ \"(pages-dir-node)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var _NotificationCenter__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./NotificationCenter */ \"(pages-dir-node)/./components/NotificationCenter.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__, _NotificationCenter__WEBPACK_IMPORTED_MODULE_4__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__, _NotificationCenter__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n// @ts-nocheck\n\n\n\n\n\n\nfunction Navbar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_3__[\"default\"])();\n    const headingText = displayName ? `${displayName} Dashboard` : 'Bot Dashboard';\n    // Show experimental announcement if applications are open and user is not a developer\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n        px: 6,\n        py: 2,\n        bg: \"rgba(255,255,255,0.05)\",\n        backdropFilter: \"blur(20px)\",\n        borderBottom: \"1px solid\",\n        borderColor: \"whiteAlpha.200\",\n        position: \"sticky\",\n        top: 0,\n        zIndex: 1000,\n        _before: {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bg: 'linear-gradient(135deg, rgba(66, 153, 225, 0.1), rgba(159, 122, 234, 0.1))',\n            zIndex: -1\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {\n            h: 16,\n            alignItems: \"center\",\n            justifyContent: \"space-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    flex: \"1\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Heading, {\n                        as: \"h1\",\n                        fontSize: \"xl\",\n                        bgGradient: \"linear(to-r, blue.400, purple.400)\",\n                        bgClip: \"text\",\n                        _hover: {\n                            bgGradient: \"linear(to-r, blue.300, purple.300)\",\n                            transform: \"scale(1.02)\"\n                        },\n                        transition: \"all 0.2s\",\n                        children: headingText\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 41,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    flex: \"1\",\n                    display: \"flex\",\n                    justifyContent: \"flex-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Flex, {\n                        alignItems: \"center\",\n                        gap: 4,\n                        children: session?.user ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                            spacing: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_NotificationCenter__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 65,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Menu, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.MenuButton, {\n                                            as: _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button,\n                                            variant: \"ghost\",\n                                            size: \"sm\",\n                                            px: 2,\n                                            py: 1,\n                                            borderRadius: \"full\",\n                                            _hover: {\n                                                bg: \"whiteAlpha.200\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.HStack, {\n                                                spacing: 2,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                                        size: \"sm\",\n                                                        name: session.user.name ?? undefined,\n                                                        src: session.user.image ?? undefined,\n                                                        borderWidth: 2,\n                                                        borderColor: \"blue.400\",\n                                                        _hover: {\n                                                            borderColor: \"purple.400\",\n                                                            transform: \"scale(1.05)\"\n                                                        },\n                                                        transition: \"all 0.2s\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                        color: \"gray.300\",\n                                                        display: {\n                                                            base: \"none\",\n                                                            md: \"block\"\n                                                        },\n                                                        children: session.user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 78,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.MenuList, {\n                                            bg: \"gray.800\",\n                                            borderColor: \"whiteAlpha.200\",\n                                            boxShadow: \"lg\",\n                                            _hover: {\n                                                borderColor: \"blue.400\"\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.MenuItem, {\n                                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiLogOut_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLogOut, {}, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                    lineNumber: 103,\n                                                    columnNumber: 27\n                                                }, void 0),\n                                                onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                                _hover: {\n                                                    bg: \"whiteAlpha.200\",\n                                                    color: \"red.400\"\n                                                },\n                                                children: \"Sign out\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signIn)('discord', {\n                                    callbackUrl: '/overview'\n                                }),\n                            bgGradient: \"linear(to-r, blue.500, purple.500)\",\n                            color: \"white\",\n                            _hover: {\n                                bgGradient: \"linear(to-r, blue.400, purple.400)\",\n                                transform: \"translateY(-1px)\"\n                            },\n                            _active: {\n                                bgGradient: \"linear(to-r, blue.600, purple.600)\",\n                                transform: \"translateY(1px)\"\n                            },\n                            transition: \"all 0.2s\",\n                            children: \"Login with Discord\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n            lineNumber: 39,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Navbar.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Navbar.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/NotificationCenter.tsx":
/*!*******************************************!*\
  !*** ./components/NotificationCenter.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotificationCenter)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _barrel_optimize_names_FiBell_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiBell!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n// Lightweight notification center - minimal functionality to reduce bundle size\nfunction NotificationCenter() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const [notifications] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]); // Empty for now to reduce complexity\n    // Don't render if no session\n    if (!session?.user) {\n        return null;\n    }\n    const unreadCount = 0; // Simplified for now\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Popover, {\n        placement: \"bottom-end\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverTrigger, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                    position: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            label: \"Notifications\",\n                            placement: \"bottom\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.IconButton, {\n                                \"aria-label\": \"Notifications\",\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiBell_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiBell, {}, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 21\n                                }, void 0),\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                color: \"gray.300\",\n                                _hover: {\n                                    bg: \"whiteAlpha.200\",\n                                    color: \"white\",\n                                    transform: \"scale(1.05)\"\n                                },\n                                transition: \"all 0.2s\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this),\n                        unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                            position: \"absolute\",\n                            top: \"-1\",\n                            right: \"-1\",\n                            colorScheme: \"red\",\n                            borderRadius: \"full\",\n                            fontSize: \"xs\",\n                            minW: \"18px\",\n                            h: \"18px\",\n                            display: \"flex\",\n                            alignItems: \"center\",\n                            justifyContent: \"center\",\n                            children: unreadCount > 99 ? '99+' : unreadCount\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverContent, {\n                bg: \"gray.800\",\n                borderColor: \"whiteAlpha.200\",\n                boxShadow: \"2xl\",\n                maxW: \"400px\",\n                _focus: {\n                    boxShadow: \"2xl\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverHeader, {\n                        borderBottomColor: \"whiteAlpha.200\",\n                        fontWeight: \"semibold\",\n                        fontSize: \"lg\",\n                        color: \"white\",\n                        children: \"Notifications\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.PopoverBody, {\n                        maxH: \"400px\",\n                        overflowY: \"auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                            spacing: 0,\n                            align: \"stretch\",\n                            children: !notifications || notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                py: 8,\n                                textAlign: \"center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                    color: \"gray.400\",\n                                    fontSize: \"sm\",\n                                    children: \"No notifications yet\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this) : (notifications || []).map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                    p: 3,\n                                    borderBottom: \"1px\",\n                                    borderColor: \"whiteAlpha.100\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                            fontSize: \"sm\",\n                                            color: \"white\",\n                                            fontWeight: \"medium\",\n                                            children: notification.title\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 94,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                            fontSize: \"xs\",\n                                            color: \"gray.400\",\n                                            mt: 1,\n                                            children: notification.message\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, notification.id, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\NotificationCenter.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/NotificationCenter.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./components/Sidebar.tsx":
/*!********************************!*\
  !*** ./components/Sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../hooks/useGuildInfo */ \"(pages-dir-node)/./hooks/useGuildInfo.ts\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__, _hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__, _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n// Import package.json version\nconst BOT_VERSION = '1.0.0'; // You can update this manually or import from package.json\nconst DEVELOPER_ID = '933023999770918932';\nfunction Sidebar() {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const isAdmin = session?.user?.isAdmin;\n    const userId = session?.user?.id;\n    const [isAdminExpanded, setIsAdminExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [isApplicationsExpanded, setIsApplicationsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [applicationsOpen, setApplicationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [openApplications, setOpenApplications] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)([]);\n    const { displayName } = (0,_hooks_useGuildInfo__WEBPACK_IMPORTED_MODULE_6__[\"default\"])();\n    const { currentScheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_7__.useTheme)();\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"Sidebar.useEffect\": ()=>{\n            const fetchApplicationStatus = {\n                \"Sidebar.useEffect.fetchApplicationStatus\": async ()=>{\n                    try {\n                        const configRes = await fetch('/api/applications/config');\n                        const configData = await configRes.json();\n                        setApplicationsOpen(configData.isOpen || configData.open);\n                        if (configData.isOpen || configData.open) {\n                            const appsRes = await fetch('/api/admin/applications-builder');\n                            const appsData = await appsRes.json();\n                            const now = new Date();\n                            const filteredApps = appsData.applications.filter({\n                                \"Sidebar.useEffect.fetchApplicationStatus.filteredApps\": (app)=>{\n                                    if (!app.enabled) return false;\n                                    if (app.settings?.openingSchedule?.enabled) {\n                                        const startDate = new Date(app.settings.openingSchedule.startDate);\n                                        const endDate = new Date(app.settings.openingSchedule.endDate);\n                                        return now >= startDate && now <= endDate;\n                                    }\n                                    return true; // If no schedule, and enabled, assume always open\n                                }\n                            }[\"Sidebar.useEffect.fetchApplicationStatus.filteredApps\"]);\n                            setOpenApplications(filteredApps);\n                        }\n                    } catch (error) {\n                        console.error('Failed to fetch application status or open applications:', error);\n                        setApplicationsOpen(false);\n                        setOpenApplications([]);\n                    }\n                }\n            }[\"Sidebar.useEffect.fetchApplicationStatus\"];\n            fetchApplicationStatus();\n        }\n    }[\"Sidebar.useEffect\"], []);\n    const menuItems = [\n        {\n            name: 'Overview',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHome,\n            href: '/overview'\n        },\n        {\n            name: 'Applications',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiPackage,\n            href: '/applications'\n        },\n        {\n            name: 'Tickets',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiHelpCircle,\n            href: '/tickets'\n        },\n        {\n            name: 'Game Servers',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiMonitor,\n            href: '/gameservers'\n        }\n    ];\n    // Admin functionality is now handled through the expandable admin section below\n    const adminQuickLinks = [\n        {\n            name: 'Server Management',\n            href: '/admin/guilds',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiSettings\n        },\n        {\n            name: 'Addons',\n            href: '/admin/addons',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiBox\n        },\n        {\n            name: 'Errors',\n            href: '/admin/errors',\n            icon: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiAlertCircle\n        }\n    ];\n    // All experimental features are now consolidated into the main Applications page\n    // Experimental admin links for developers (Removed)\n    const isActive = (href)=>{\n        if (href === '/overview') {\n            return router.pathname === href;\n        }\n        return router.pathname.startsWith(href);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n        as: \"nav\",\n        h: \"100%\",\n        bg: currentScheme.colors.surface,\n        backdropFilter: \"blur(20px)\",\n        borderRight: \"1px solid\",\n        borderColor: currentScheme.colors.border,\n        py: 8,\n        display: \"flex\",\n        flexDirection: \"column\",\n        _before: {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            bg: `linear-gradient(180deg, ${currentScheme.colors.primary}15 0%, ${currentScheme.colors.accent}15 100%)`,\n            zIndex: -1\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                spacing: 2,\n                align: \"stretch\",\n                flex: \"1\",\n                children: [\n                    menuItems.map((item)=>{\n                        const active = isActive(item.href);\n                        if (item.name === 'Applications') {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                spacing: 0,\n                                align: \"stretch\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                        display: \"flex\",\n                                        alignItems: \"center\",\n                                        px: 4,\n                                        py: 3,\n                                        fontSize: \"sm\",\n                                        fontWeight: \"medium\",\n                                        color: isApplicationsExpanded || isActive(item.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                        bg: isActive(item.href) ? `${currentScheme.colors.primary}30` : 'transparent',\n                                        _hover: {\n                                            bg: isActive(item.href) ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,\n                                            color: currentScheme.colors.text,\n                                            transform: 'translateX(4px)'\n                                        },\n                                        _active: {\n                                            bg: `${currentScheme.colors.primary}50`\n                                        },\n                                        borderRight: isActive(item.href) ? '2px solid' : 'none',\n                                        borderColor: isActive(item.href) ? currentScheme.colors.primary : 'transparent',\n                                        transition: \"all 0.2s\",\n                                        cursor: \"pointer\",\n                                        role: \"group\",\n                                        onClick: ()=>setIsApplicationsExpanded(!isApplicationsExpanded),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                as: item.icon,\n                                                w: 5,\n                                                h: 5,\n                                                mr: 3,\n                                                transition: \"all 0.2s\",\n                                                _groupHover: {\n                                                    transform: 'scale(1.1)'\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                display: {\n                                                    base: 'none',\n                                                    lg: 'block'\n                                                },\n                                                bgGradient: isActive(item.href) ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none',\n                                                bgClip: isActive(item.href) ? 'text' : 'none',\n                                                transition: \"all 0.2s\",\n                                                flex: \"1\",\n                                                children: item.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                as: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiChevronDown,\n                                                w: 4,\n                                                h: 4,\n                                                ml: 2,\n                                                transition: \"all 0.2s\",\n                                                transform: isApplicationsExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n                                                opacity: 0.6\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Collapse, {\n                                        in: isApplicationsExpanded,\n                                        animateOpacity: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                            spacing: 1,\n                                            align: \"stretch\",\n                                            pl: 4,\n                                            py: 2,\n                                            children: openApplications.length > 0 ? openApplications.map((app)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                                    as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                                    href: `/applications/${app.id}`,\n                                                    display: \"flex\",\n                                                    alignItems: \"center\",\n                                                    px: 4,\n                                                    py: 2,\n                                                    fontSize: \"xs\",\n                                                    fontWeight: \"medium\",\n                                                    color: isActive(`/applications/${app.id}`) ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                                    bg: isActive(`/applications/${app.id}`) ? `${currentScheme.colors.primary}20` : 'transparent',\n                                                    _hover: {\n                                                        bg: currentScheme.colors.surface,\n                                                        color: currentScheme.colors.text,\n                                                        transform: 'translateX(2px)'\n                                                    },\n                                                    borderRadius: \"md\",\n                                                    transition: \"all 0.2s\",\n                                                    role: \"group\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                        display: {\n                                                            base: 'none',\n                                                            lg: 'block'\n                                                        },\n                                                        children: app.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                }, app.id, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 25\n                                                }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                px: 4,\n                                                py: 2,\n                                                fontSize: \"xs\",\n                                                color: currentScheme.colors.textSecondary,\n                                                display: {\n                                                    base: 'none',\n                                                    lg: 'block'\n                                                },\n                                                children: \"No open applications\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.name, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 15\n                            }, this);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Tooltip, {\n                            label: item.name,\n                            placement: \"right\",\n                            hasArrow: true,\n                            gutter: 20,\n                            openDelay: 500,\n                            display: {\n                                base: 'block',\n                                '2xl': 'none'\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                href: item.href,\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                px: 4,\n                                py: 3,\n                                fontSize: \"sm\",\n                                fontWeight: \"medium\",\n                                color: active ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                bg: active ? `${currentScheme.colors.primary}30` : 'transparent',\n                                _hover: {\n                                    bg: active ? `${currentScheme.colors.primary}40` : currentScheme.colors.surface,\n                                    color: currentScheme.colors.text,\n                                    transform: 'translateX(4px)'\n                                },\n                                _active: {\n                                    bg: `${currentScheme.colors.primary}50`\n                                },\n                                borderRight: active ? '2px solid' : 'none',\n                                borderColor: active ? currentScheme.colors.primary : 'transparent',\n                                transition: \"all 0.2s\",\n                                role: \"group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        as: item.icon,\n                                        w: 5,\n                                        h: 5,\n                                        mr: 3,\n                                        transition: \"all 0.2s\",\n                                        _groupHover: {\n                                            transform: 'scale(1.1)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 263,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        bgGradient: active ? `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})` : 'none',\n                                        bgClip: active ? 'text' : 'none',\n                                        transition: \"all 0.2s\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, this),\n                                    item.name === 'Applications' && !applicationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                        ml: 2,\n                                        colorScheme: \"red\",\n                                        variant: \"solid\",\n                                        fontSize: \"xs\",\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        children: \"Closed\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 19\n                                    }, this),\n                                    item.name === 'Applications' && openApplications.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Badge, {\n                                        ml: 2,\n                                        colorScheme: \"green\",\n                                        variant: \"solid\",\n                                        fontSize: \"xs\",\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        children: [\n                                            openApplications.length,\n                                            \" Open\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 293,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 15\n                            }, this)\n                        }, item.name, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                            lineNumber: 230,\n                            columnNumber: 13\n                        }, this);\n                    }),\n                    isAdmin && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                        spacing: 0,\n                        align: \"stretch\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                                display: \"flex\",\n                                alignItems: \"center\",\n                                px: 4,\n                                py: 3,\n                                fontSize: \"sm\",\n                                fontWeight: \"medium\",\n                                color: currentScheme.colors.textSecondary,\n                                bg: \"transparent\",\n                                _hover: {\n                                    bg: currentScheme.colors.surface,\n                                    color: currentScheme.colors.text,\n                                    transform: 'translateX(4px)'\n                                },\n                                transition: \"all 0.2s\",\n                                cursor: \"pointer\",\n                                role: \"group\",\n                                onClick: ()=>setIsAdminExpanded(!isAdminExpanded),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        as: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiServer,\n                                        w: 5,\n                                        h: 5,\n                                        mr: 3,\n                                        transition: \"all 0.2s\",\n                                        _groupHover: {\n                                            transform: 'scale(1.1)'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                        display: {\n                                            base: 'none',\n                                            lg: 'block'\n                                        },\n                                        transition: \"all 0.2s\",\n                                        flex: \"1\",\n                                        children: \"Admin\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 340,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                        as: _barrel_optimize_names_FiAlertCircle_FiBox_FiChevronDown_FiHelpCircle_FiHome_FiMonitor_FiPackage_FiServer_FiSettings_react_icons_fi__WEBPACK_IMPORTED_MODULE_8__.FiChevronDown,\n                                        w: 4,\n                                        h: 4,\n                                        ml: 2,\n                                        transition: \"all 0.2s\",\n                                        transform: isAdminExpanded ? 'rotate(180deg)' : 'rotate(0deg)',\n                                        opacity: 0.6\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Collapse, {\n                                in: isAdminExpanded,\n                                animateOpacity: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.VStack, {\n                                    spacing: 1,\n                                    align: \"stretch\",\n                                    pl: 4,\n                                    py: 2,\n                                    children: adminQuickLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Link, {\n                                            as: (next_link__WEBPACK_IMPORTED_MODULE_2___default()),\n                                            href: link.href,\n                                            display: \"flex\",\n                                            alignItems: \"center\",\n                                            px: 4,\n                                            py: 2,\n                                            fontSize: \"xs\",\n                                            fontWeight: \"medium\",\n                                            color: isActive(link.href) ? currentScheme.colors.text : currentScheme.colors.textSecondary,\n                                            bg: isActive(link.href) ? `${currentScheme.colors.primary}20` : 'transparent',\n                                            _hover: {\n                                                bg: currentScheme.colors.surface,\n                                                color: currentScheme.colors.text,\n                                                transform: 'translateX(2px)'\n                                            },\n                                            borderRadius: \"md\",\n                                            transition: \"all 0.2s\",\n                                            role: \"group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Icon, {\n                                                    as: link.icon,\n                                                    w: 4,\n                                                    h: 4,\n                                                    mr: 2,\n                                                    transition: \"all 0.2s\",\n                                                    _groupHover: {\n                                                        transform: 'scale(1.1)'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                                                    display: {\n                                                        base: 'none',\n                                                        lg: 'block'\n                                                    },\n                                                    children: link.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, link.href, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Box, {\n                px: 4,\n                pt: 4,\n                mt: \"auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Divider, {\n                        borderColor: currentScheme.colors.border,\n                        mb: 4\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_1__.Text, {\n                        fontSize: \"xs\",\n                        color: currentScheme.colors.textSecondary,\n                        textAlign: \"center\",\n                        bgGradient: `linear(to-r, ${currentScheme.colors.primaryLight}, ${currentScheme.colors.accent})`,\n                        bgClip: \"text\",\n                        opacity: 0.7,\n                        _hover: {\n                            opacity: 1,\n                            transform: \"scale(1.05)\"\n                        },\n                        transition: \"all 0.2s\",\n                        children: displayName ? `${displayName} v${BOT_VERSION}` : `Bot v${BOT_VERSION}`\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\components\\\\Sidebar.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./components/Sidebar.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./contexts/ThemeContext.tsx":
/*!***********************************!*\
  !*** ./contexts/ThemeContext.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COLOR_SCHEMES: () => (/* binding */ COLOR_SCHEMES),\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _styles_theme__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../styles/theme */ \"(pages-dir-node)/./styles/theme.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _styles_theme__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst COLOR_SCHEMES = [\n    {\n        id: 'default',\n        name: 'Default Purple',\n        description: 'Classic purple and blue gradient theme',\n        colors: {\n            primary: '#8b5cf6',\n            primaryLight: '#a78bfa',\n            primaryDark: '#7c3aed',\n            secondary: '#5865F2',\n            accent: '#06b6d4',\n            background: '#1a202c',\n            surface: 'rgba(255,255,255,0.03)',\n            text: '#f7fafc',\n            textSecondary: '#a0aec0',\n            border: 'rgba(255,255,255,0.2)',\n            success: '#68d391',\n            warning: '#fbb6ce',\n            error: '#fc8181',\n            info: '#63b3ed'\n        }\n    },\n    {\n        id: 'ocean',\n        name: 'Ocean Blue',\n        description: 'Deep blue ocean-inspired theme',\n        colors: {\n            primary: '#0ea5e9',\n            primaryLight: '#38bdf8',\n            primaryDark: '#0284c7',\n            secondary: '#06b6d4',\n            accent: '#8b5cf6',\n            background: '#0f172a',\n            surface: 'rgba(59, 130, 246, 0.05)',\n            text: '#f1f5f9',\n            textSecondary: '#94a3b8',\n            border: 'rgba(59, 130, 246, 0.2)',\n            success: '#10b981',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#3b82f6'\n        }\n    },\n    {\n        id: 'forest',\n        name: 'Forest Green',\n        description: 'Nature-inspired green theme',\n        colors: {\n            primary: '#059669',\n            primaryLight: '#10b981',\n            primaryDark: '#047857',\n            secondary: '#065f46',\n            accent: '#8b5cf6',\n            background: '#0f1419',\n            surface: 'rgba(16, 185, 129, 0.05)',\n            text: '#f0fdf4',\n            textSecondary: '#86efac',\n            border: 'rgba(16, 185, 129, 0.2)',\n            success: '#22c55e',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#06b6d4'\n        }\n    },\n    {\n        id: 'sunset',\n        name: 'Sunset Orange',\n        description: 'Warm sunset-inspired theme',\n        colors: {\n            primary: '#ea580c',\n            primaryLight: '#fb923c',\n            primaryDark: '#c2410c',\n            secondary: '#dc2626',\n            accent: '#8b5cf6',\n            background: '#1c1917',\n            surface: 'rgba(251, 146, 60, 0.05)',\n            text: '#fef7ed',\n            textSecondary: '#fdba74',\n            border: 'rgba(251, 146, 60, 0.2)',\n            success: '#22c55e',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#06b6d4'\n        }\n    },\n    {\n        id: 'rose',\n        name: 'Rose Pink',\n        description: 'Elegant rose and pink theme',\n        colors: {\n            primary: '#e11d48',\n            primaryLight: '#f43f5e',\n            primaryDark: '#be123c',\n            secondary: '#ec4899',\n            accent: '#8b5cf6',\n            background: '#1f1720',\n            surface: 'rgba(244, 63, 94, 0.05)',\n            text: '#fdf2f8',\n            textSecondary: '#fda4af',\n            border: 'rgba(244, 63, 94, 0.2)',\n            success: '#22c55e',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#06b6d4'\n        }\n    },\n    {\n        id: 'midnight',\n        name: 'Midnight Blue',\n        description: 'Dark midnight blue theme',\n        colors: {\n            primary: '#1e40af',\n            primaryLight: '#3b82f6',\n            primaryDark: '#1e3a8a',\n            secondary: '#4338ca',\n            accent: '#06b6d4',\n            background: '#0c0a1f',\n            surface: 'rgba(59, 130, 246, 0.05)',\n            text: '#f8fafc',\n            textSecondary: '#94a3b8',\n            border: 'rgba(59, 130, 246, 0.2)',\n            success: '#10b981',\n            warning: '#f59e0b',\n            error: '#ef4444',\n            info: '#3b82f6'\n        }\n    }\n];\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useTheme = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (!context) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n};\nconst ThemeProvider = ({ children })=>{\n    const [currentScheme, setCurrentScheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(COLOR_SCHEMES[0]);\n    const [customSchemes, setCustomSchemes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const savedSchemeId = localStorage.getItem('dashboard-color-scheme');\n            const savedCustomSchemes = localStorage.getItem('dashboard-custom-schemes');\n            if (savedCustomSchemes) {\n                try {\n                    const parsedCustomSchemes = JSON.parse(savedCustomSchemes);\n                    setCustomSchemes(parsedCustomSchemes);\n                } catch (error) {\n                    console.error('Failed to parse custom schemes:', error);\n                }\n            }\n            if (savedSchemeId) {\n                // First check built-in schemes\n                const builtInScheme = COLOR_SCHEMES.find({\n                    \"ThemeProvider.useEffect.builtInScheme\": (s)=>s.id === savedSchemeId\n                }[\"ThemeProvider.useEffect.builtInScheme\"]);\n                if (builtInScheme) {\n                    setCurrentScheme(builtInScheme);\n                } else {\n                    // Check custom schemes\n                    const savedCustomSchemes = localStorage.getItem('dashboard-custom-schemes');\n                    if (savedCustomSchemes) {\n                        try {\n                            const parsedCustomSchemes = JSON.parse(savedCustomSchemes);\n                            const customScheme = parsedCustomSchemes.find({\n                                \"ThemeProvider.useEffect.customScheme\": (s)=>s.id === savedSchemeId\n                            }[\"ThemeProvider.useEffect.customScheme\"]);\n                            if (customScheme) {\n                                setCurrentScheme(customScheme);\n                            }\n                        } catch (error) {\n                            console.error('Failed to find custom scheme:', error);\n                        }\n                    }\n                }\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Save theme to localStorage when it changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            localStorage.setItem('dashboard-color-scheme', currentScheme.id);\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        currentScheme\n    ]);\n    // Save custom schemes to localStorage when they change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            localStorage.setItem('dashboard-custom-schemes', JSON.stringify(customSchemes));\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        customSchemes\n    ]);\n    const setColorScheme = (schemeId)=>{\n        // First check built-in schemes\n        const builtInScheme = COLOR_SCHEMES.find((s)=>s.id === schemeId);\n        if (builtInScheme) {\n            setCurrentScheme(builtInScheme);\n            return;\n        }\n        // Check custom schemes\n        const customScheme = customSchemes.find((s)=>s.id === schemeId);\n        if (customScheme) {\n            setCurrentScheme(customScheme);\n        }\n    };\n    const addCustomScheme = (scheme)=>{\n        setCustomSchemes((prev)=>{\n            const filtered = prev.filter((s)=>s.id !== scheme.id);\n            return [\n                ...filtered,\n                scheme\n            ];\n        });\n        setCurrentScheme(scheme);\n    };\n    const deleteCustomScheme = (schemeId)=>{\n        setCustomSchemes((prev)=>prev.filter((s)=>s.id !== schemeId));\n        // If the deleted scheme is currently active, switch to default\n        if (currentScheme.id === schemeId) {\n            setCurrentScheme(COLOR_SCHEMES[0]);\n        }\n    };\n    const resetToDefault = ()=>{\n        setCurrentScheme(COLOR_SCHEMES[0]);\n    };\n    // Get all schemes (built-in + custom)\n    const allSchemes = [\n        ...COLOR_SCHEMES,\n        ...customSchemes\n    ];\n    // Create dynamic Chakra UI theme based on current colors\n    const dynamicTheme = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.extendTheme)({\n        ..._styles_theme__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        colors: {\n            ..._styles_theme__WEBPACK_IMPORTED_MODULE_3__[\"default\"].colors,\n            brand: {\n                50: currentScheme.colors.primaryLight + '20',\n                100: currentScheme.colors.primaryLight + '40',\n                200: currentScheme.colors.primaryLight + '60',\n                300: currentScheme.colors.primaryLight + '80',\n                400: currentScheme.colors.primaryLight,\n                500: currentScheme.colors.primary,\n                600: currentScheme.colors.primaryDark,\n                700: currentScheme.colors.primaryDark + 'CC',\n                800: currentScheme.colors.primaryDark + 'AA',\n                900: currentScheme.colors.primaryDark + '88'\n            },\n            custom: {\n                primary: currentScheme.colors.primary,\n                primaryLight: currentScheme.colors.primaryLight,\n                primaryDark: currentScheme.colors.primaryDark,\n                secondary: currentScheme.colors.secondary,\n                accent: currentScheme.colors.accent,\n                background: currentScheme.colors.background,\n                surface: currentScheme.colors.surface,\n                text: currentScheme.colors.text,\n                textSecondary: currentScheme.colors.textSecondary,\n                border: currentScheme.colors.border,\n                success: currentScheme.colors.success,\n                warning: currentScheme.colors.warning,\n                error: currentScheme.colors.error,\n                info: currentScheme.colors.info\n            }\n        },\n        styles: {\n            global: {\n                body: {\n                    bg: currentScheme.colors.background,\n                    color: currentScheme.colors.text\n                }\n            }\n        }\n    });\n    const contextValue = {\n        currentScheme,\n        setColorScheme,\n        colorSchemes: allSchemes,\n        customSchemes,\n        addCustomScheme,\n        deleteCustomScheme,\n        resetToDefault\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ChakraProvider, {\n            theme: dynamicTheme,\n            children: children\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\contexts\\\\ThemeContext.tsx\",\n            lineNumber: 327,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\contexts\\\\ThemeContext.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, undefined);\n};\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./hooks/useGuildInfo.ts":
/*!*******************************!*\
  !*** ./hooks/useGuildInfo.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useGuildInfo)\n/* harmony export */ });\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! swr */ \"swr\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([swr__WEBPACK_IMPORTED_MODULE_0__]);\nswr__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst fetcher = async (url)=>{\n    const res = await fetch(url);\n    if (!res.ok) {\n        if (res.status === 401) {\n            // Return default data for unauthorized state\n            return {\n                name: '404 Bot',\n                botName: '404 Bot'\n            };\n        }\n        throw new Error('Failed to fetch guild info');\n    }\n    return res.json();\n};\nfunction useGuildInfo() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    // Only fetch if we're authenticated\n    const shouldFetch = status === 'authenticated';\n    const { data, error } = (0,swr__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(shouldFetch ? '/api/discord/guild' : null, fetcher, {\n        revalidateOnFocus: false,\n        revalidateOnReconnect: false\n    });\n    // Local preference state (guild vs bot)\n    const [pref, setPref] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"useGuildInfo.useState\": ()=>{\n            if (true) return 'guild';\n            return localStorage.getItem('dashboardDisplayNamePref') || 'guild';\n        }\n    }[\"useGuildInfo.useState\"]);\n    // Function to update preference and broadcast change\n    const updatePreference = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"useGuildInfo.useCallback[updatePreference]\": (newPref)=>{\n            setPref(newPref);\n            if (false) {}\n        }\n    }[\"useGuildInfo.useCallback[updatePreference]\"], []);\n    // Listen for preference changes in this tab (custom event) or other tabs (storage event)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"useGuildInfo.useEffect\": ()=>{\n            if (true) return;\n            const handleCustom = {\n                \"useGuildInfo.useEffect.handleCustom\": (e)=>{\n                    if (e?.detail) setPref(e.detail);\n                }\n            }[\"useGuildInfo.useEffect.handleCustom\"];\n            const handleStorage = {\n                \"useGuildInfo.useEffect.handleStorage\": (e)=>{\n                    if (e.key === 'dashboardDisplayNamePref') {\n                        setPref(e.newValue || 'guild');\n                    }\n                }\n            }[\"useGuildInfo.useEffect.handleStorage\"];\n            window.addEventListener('displayNamePrefChanged', handleCustom);\n            window.addEventListener('storage', handleStorage);\n            return ({\n                \"useGuildInfo.useEffect\": ()=>{\n                    window.removeEventListener('displayNamePrefChanged', handleCustom);\n                    window.removeEventListener('storage', handleStorage);\n                }\n            })[\"useGuildInfo.useEffect\"];\n        }\n    }[\"useGuildInfo.useEffect\"], []);\n    // Default display name when not authenticated\n    const defaultName = '404 Bot Dashboard';\n    // Determine displayName\n    let displayName = defaultName;\n    if (data) {\n        if (pref === 'bot' && data.botName) {\n            displayName = data.botName;\n        } else {\n            displayName = data.name || defaultName;\n        }\n    }\n    return {\n        guild: data,\n        displayName,\n        pref,\n        updatePreference,\n        isLoading: shouldFetch && !error && !data,\n        isError: !!error\n    };\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./hooks/useGuildInfo.ts\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"next-auth/react\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(pages-dir-node)/./contexts/ThemeContext.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__]);\n_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n// @ts-nocheck\n\n\n\n\n// Create a wrapper component that uses useGuildInfo\nfunction AppContent({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: \"404 Bot Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/site.webmanifest\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\nfunction App({ Component, pageProps: { session, ...pageProps } }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_3__.ThemeProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppContent, {\n                Component: Component,\n                pageProps: pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_app.tsx\",\n        lineNumber: 27,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUEsY0FBYzs7QUFJb0M7QUFDckI7QUFDNEI7QUFFekQsb0RBQW9EO0FBQ3BELFNBQVNHLFdBQVcsRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDMUMscUJBQ0U7OzBCQUNFLDhEQUFDSixrREFBSUE7O2tDQUNILDhEQUFDSztrQ0FBTTs7Ozs7O2tDQUNQLDhEQUFDQzt3QkFBS0MsS0FBSTt3QkFBT0MsTUFBSzt3QkFBWUMsT0FBTTt3QkFBUUMsTUFBSzs7Ozs7O2tDQUNyRCw4REFBQ0o7d0JBQUtDLEtBQUk7d0JBQU9DLE1BQUs7d0JBQVlDLE9BQU07d0JBQVFDLE1BQUs7Ozs7OztrQ0FDckQsOERBQUNKO3dCQUFLQyxLQUFJO3dCQUFtQkUsT0FBTTt3QkFBVUMsTUFBSzs7Ozs7O2tDQUNsRCw4REFBQ0o7d0JBQUtDLEtBQUk7d0JBQVdHLE1BQUs7Ozs7Ozs7Ozs7OzswQkFFNUIsOERBQUNQO2dCQUFXLEdBQUdDLFNBQVM7Ozs7Ozs7O0FBRzlCO0FBRWUsU0FBU08sSUFBSSxFQUFFUixTQUFTLEVBQUVDLFdBQVcsRUFBRVEsT0FBTyxFQUFFLEdBQUdSLFdBQVcsRUFBWTtJQUN2RixxQkFDRSw4REFBQ0wsNERBQWVBO1FBQUNhLFNBQVNBO2tCQUN4Qiw0RUFBQ1gsaUVBQWFBO3NCQUNaLDRFQUFDQztnQkFBV0MsV0FBV0E7Z0JBQVdDLFdBQVdBOzs7Ozs7Ozs7Ozs7Ozs7O0FBSXJEIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxzcmNcXGRhc2hib2FyZFxccGFnZXNcXF9hcHAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIEB0cy1ub2NoZWNrXHJcblxyXG5pbXBvcnQgeyBDb2xvck1vZGVTY3JpcHQgfSBmcm9tICdAY2hha3JhLXVpL3JlYWN0JztcclxuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJztcclxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcclxuaW1wb3J0IEhlYWQgZnJvbSAnbmV4dC9oZWFkJztcclxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL1RoZW1lQ29udGV4dCc7XHJcblxyXG4vLyBDcmVhdGUgYSB3cmFwcGVyIGNvbXBvbmVudCB0aGF0IHVzZXMgdXNlR3VpbGRJbmZvXHJcbmZ1bmN0aW9uIEFwcENvbnRlbnQoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgIDxIZWFkPlxyXG4gICAgICAgIDx0aXRsZT40MDQgQm90IERhc2hib2FyZDwvdGl0bGU+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9wbmdcIiBzaXplcz1cIjMyeDMyXCIgaHJlZj1cIi9mYXZpY29uLTMyeDMyLnBuZ1wiIC8+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiaWNvblwiIHR5cGU9XCJpbWFnZS9wbmdcIiBzaXplcz1cIjE2eDE2XCIgaHJlZj1cIi9mYXZpY29uLTE2eDE2LnBuZ1wiIC8+XHJcbiAgICAgICAgPGxpbmsgcmVsPVwiYXBwbGUtdG91Y2gtaWNvblwiIHNpemVzPVwiMTgweDE4MFwiIGhyZWY9XCIvYXBwbGUtdG91Y2gtaWNvbi5wbmdcIiAvPlxyXG4gICAgICAgIDxsaW5rIHJlbD1cIm1hbmlmZXN0XCIgaHJlZj1cIi9zaXRlLndlYm1hbmlmZXN0XCIgLz5cclxuICAgICAgPC9IZWFkPlxyXG4gICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XHJcbiAgICA8Lz5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wczogeyBzZXNzaW9uLCAuLi5wYWdlUHJvcHMgfSB9OiBBcHBQcm9wcykge1xyXG4gIHJldHVybiAoXHJcbiAgICA8U2Vzc2lvblByb3ZpZGVyIHNlc3Npb249e3Nlc3Npb259PlxyXG4gICAgICA8VGhlbWVQcm92aWRlcj5cclxuICAgICAgICA8QXBwQ29udGVudCBDb21wb25lbnQ9e0NvbXBvbmVudH0gcGFnZVByb3BzPXtwYWdlUHJvcHN9IC8+XHJcbiAgICAgIDwvVGhlbWVQcm92aWRlcj5cclxuICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gICk7XHJcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIkhlYWQiLCJUaGVtZVByb3ZpZGVyIiwiQXBwQ29udGVudCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsInRpdGxlIiwibGluayIsInJlbCIsInR5cGUiLCJzaXplcyIsImhyZWYiLCJBcHAiLCJzZXNzaW9uIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\_document.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTREO0FBRTdDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcc3JjXFxkYXNoYm9hcmRcXHBhZ2VzXFxfZG9jdW1lbnQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEh0bWwsIEhlYWQsIE1haW4sIE5leHRTY3JpcHQgfSBmcm9tICduZXh0L2RvY3VtZW50J1xyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxyXG4gICAgICA8SGVhZCAvPlxyXG4gICAgICA8Ym9keT5cclxuICAgICAgICA8TWFpbiAvPlxyXG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XHJcbiAgICAgIDwvYm9keT5cclxuICAgIDwvSHRtbD5cclxuICApXHJcbn0gIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/admin/addons.tsx":
/*!********************************!*\
  !*** ./pages/admin/addons.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddonsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/Layout */ \"(pages-dir-node)/./components/Layout.tsx\");\n/* harmony import */ var _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FiCode,FiPackage,FiRefreshCw,FiSave,FiSettings,FiTrash2,FiZap!=!react-icons/fi */ \"(pages-dir-node)/__barrel_optimize__?names=FiCode,FiPackage,FiRefreshCw,FiSave,FiSettings,FiTrash2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dynamic */ \"(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dynamic.js\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dynamic__WEBPACK_IMPORTED_MODULE_5__);\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_Layout__WEBPACK_IMPORTED_MODULE_3__]);\n([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__, _components_Layout__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\nconst YamlEditor = next_dynamic__WEBPACK_IMPORTED_MODULE_5___default()(()=>__webpack_require__.e(/*! import() */ \"_pages-dir-node_components_YamlEditor_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! ../../components/YamlEditor */ \"(pages-dir-node)/./components/YamlEditor.tsx\")), {\n    loadableGenerated: {\n        modules: [\n            \"pages\\\\admin\\\\addons.tsx -> \" + \"../../components/YamlEditor\"\n        ]\n    },\n    ssr: false,\n    loading: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Skeleton, {\n            height: \"400px\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n            lineNumber: 44,\n            columnNumber: 18\n        }, undefined)\n});\n// Color schemes for different addon types\nconst ADDON_COLORS = {\n    'voice-mistress': {\n        color: 'purple',\n        gradient: {\n            from: 'rgba(159, 122, 234, 0.4)',\n            to: 'rgba(159, 122, 234, 0.1)'\n        }\n    },\n    'tickets': {\n        color: 'orange',\n        gradient: {\n            from: 'rgba(237, 137, 54, 0.4)',\n            to: 'rgba(237, 137, 54, 0.1)'\n        }\n    },\n    'welcome-goodbye': {\n        color: 'blue',\n        gradient: {\n            from: 'rgba(66, 153, 225, 0.4)',\n            to: 'rgba(66, 153, 225, 0.1)'\n        }\n    },\n    'example': {\n        color: 'green',\n        gradient: {\n            from: 'rgba(72, 187, 120, 0.4)',\n            to: 'rgba(72, 187, 120, 0.1)'\n        }\n    },\n    // Default color scheme\n    default: {\n        color: 'teal',\n        gradient: {\n            from: 'rgba(49, 151, 149, 0.4)',\n            to: 'rgba(49, 151, 149, 0.1)'\n        }\n    }\n};\nconst AddonCard = ({ addon, onSave, onToggle, onEdit, onDelete })=>{\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedConfig, setEditedConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(addon.config || '');\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showDeleteConfirm, setShowDeleteConfirm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Update editedConfig when addon.config changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddonCard.useEffect\": ()=>{\n            setEditedConfig(addon.config || '');\n        }\n    }[\"AddonCard.useEffect\"], [\n        addon.config\n    ]);\n    const handleSave = async ()=>{\n        try {\n            // Validate YAML format\n            yaml__WEBPACK_IMPORTED_MODULE_4__.parse(editedConfig || '');\n            setError(null);\n            const response = await fetch(`/api/admin/addons/${addon.name}`, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    config: editedConfig\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.message || 'Failed to save configuration');\n            }\n            onSave(editedConfig);\n            setIsEditing(false);\n            toast({\n                title: 'Success',\n                description: 'Configuration saved successfully',\n                status: 'success',\n                duration: 3000\n            });\n        } catch (err) {\n            setError(err.message || 'Invalid YAML format');\n            toast({\n                title: 'Error',\n                description: err.message || 'Invalid YAML format',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        bg: ADDON_COLORS[addon.name]?.color ? `linear-gradient(135deg, ${ADDON_COLORS[addon.name].gradient.from}, ${ADDON_COLORS[addon.name].gradient.to})` : 'gray.900',\n        backdropFilter: \"blur(10px)\",\n        borderWidth: 2,\n        borderColor: ADDON_COLORS[addon.name]?.color ? `${ADDON_COLORS[addon.name].color}.400` : 'gray.600',\n        rounded: \"xl\",\n        overflow: \"hidden\",\n        transition: \"all 0.2s\",\n        _hover: {\n            transform: 'translateY(-2px)',\n            boxShadow: ADDON_COLORS[addon.name]?.color ? `0 4px 20px ${ADDON_COLORS[addon.name].gradient.from}` : 'none'\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {\n                spacing: 4,\n                align: \"stretch\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(YamlEditor, {\n                        value: editedConfig,\n                        onChange: (value)=>{\n                            setEditedConfig(value);\n                            setError(null); // Clear error when user makes changes\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 171,\n                        columnNumber: 13\n                    }, undefined),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        color: \"red.400\",\n                        fontSize: \"sm\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 15\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                        justify: \"flex-end\",\n                        spacing: 2,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                onClick: ()=>{\n                                    setIsEditing(false);\n                                    setEditedConfig(addon.config || '');\n                                    setError(null);\n                                },\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                colorScheme: \"purple\",\n                                onClick: handleSave,\n                                isDisabled: !!error,\n                                children: \"Save Changes\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 183,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                lineNumber: 170,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {\n                align: \"stretch\",\n                spacing: 4,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                        justify: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                        as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiPackage,\n                                        color: `${ADDON_COLORS[addon.name]?.color || 'gray'}.400`,\n                                        boxSize: 5\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {\n                                        size: \"md\",\n                                        color: \"white\",\n                                        children: addon.name\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                colorScheme: addon.enabled ? ADDON_COLORS[addon.name]?.color || 'gray' : 'gray',\n                                children: addon.enabled ? 'Active' : 'Inactive'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                        color: \"gray.300\",\n                        fontSize: \"sm\",\n                        noOfLines: 2,\n                        children: addon.description\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                color: \"gray.400\",\n                                fontSize: \"xs\",\n                                children: [\n                                    \"v\",\n                                    addon.version\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                color: \"gray.400\",\n                                fontSize: \"xs\",\n                                children: [\n                                    \"by \",\n                                    addon.author\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Divider, {\n                        borderColor: \"whiteAlpha.200\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                        justify: \"space-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Switch, {\n                                isChecked: addon.enabled,\n                                onChange: ()=>onToggle(addon),\n                                colorScheme: ADDON_COLORS[addon.name]?.color || 'gray'\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                                spacing: 2,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"sm\",\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                            as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiCode\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        variant: \"ghost\",\n                                        colorScheme: ADDON_COLORS[addon.name]?.color || 'gray',\n                                        onClick: ()=>onEdit(addon),\n                                        children: \"Edit Config\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    addon.isCustomAddon && onDelete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                size: \"sm\",\n                                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                                    as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiTrash2\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                    lineNumber: 256,\n                                                    columnNumber: 33\n                                                }, void 0),\n                                                variant: \"ghost\",\n                                                colorScheme: \"red\",\n                                                onClick: ()=>setShowDeleteConfirm(true),\n                                                children: \"Delete\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                                                isOpen: showDeleteConfirm,\n                                                onClose: ()=>setShowDeleteConfirm(false),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalOverlay, {}, void 0, false, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                        lineNumber: 265,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalHeader, {\n                                                                children: \"Delete Custom Addon\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalCloseButton, {}, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                lineNumber: 268,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalBody, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                                    children: [\n                                                                        \"Are you sure you want to delete \",\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: addon.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                            lineNumber: 271,\n                                                                            columnNumber: 61\n                                                                        }, undefined),\n                                                                        \"? This action cannot be undone and will permanently remove all files for this custom addon.\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        variant: \"ghost\",\n                                                                        mr: 3,\n                                                                        onClick: ()=>setShowDeleteConfirm(false),\n                                                                        children: \"Cancel\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                        lineNumber: 276,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        colorScheme: \"red\",\n                                                                        onClick: ()=>{\n                                                                            onDelete(addon);\n                                                                            setShowDeleteConfirm(false);\n                                                                        },\n                                                                        children: \"Delete\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                        lineNumber: 283,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                lineNumber: 204,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n            lineNumber: 168,\n            columnNumber: 7\n        }, undefined)\n    }, addon.name, false, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n        lineNumber: 154,\n        columnNumber: 5\n    }, undefined);\n};\nfunction AddonsPage() {\n    const [builtInAddons, setBuiltInAddons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [customAddons, setCustomAddons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedAddon, setSelectedAddon] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [configYaml, setConfigYaml] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { isOpen, onOpen, onClose } = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useDisclosure)();\n    const toast = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.useToast)();\n    // Add cleanup effect when modal closes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddonsPage.useEffect\": ()=>{\n            if (!isOpen) {\n                setSelectedAddon(null);\n                setConfigYaml('');\n            }\n        }\n    }[\"AddonsPage.useEffect\"], [\n        isOpen\n    ]);\n    const fetchAddons = async ()=>{\n        try {\n            const response = await fetch('/api/admin/addons', {\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                // If unauthorized, redirect to sign in\n                if (response.status === 401 || response.status === 403) {\n                    window.location.href = '/signin';\n                    return;\n                }\n                throw new Error(errorData.message || 'Failed to fetch addons');\n            }\n            const data = await response.json();\n            // Use the new API structure if available, fallback to legacy\n            if (data.builtInAddons && data.customAddons) {\n                setBuiltInAddons(data.builtInAddons);\n                setCustomAddons(data.customAddons);\n            } else {\n                // Legacy fallback - categorize addons client-side\n                const builtIn = data.addons?.filter((addon)=>!addon.isCustomAddon) || [];\n                const custom = data.addons?.filter((addon)=>addon.isCustomAddon) || [];\n                setBuiltInAddons(builtIn);\n                setCustomAddons(custom);\n            }\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to fetch addons',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AddonsPage.useEffect\": ()=>{\n            fetchAddons();\n        }\n    }[\"AddonsPage.useEffect\"], []);\n    const handleToggleAddon = async (addon)=>{\n        try {\n            const response = await fetch(`/api/admin/addons/${addon.name}`, {\n                method: 'PATCH',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    enabled: !addon.enabled\n                })\n            });\n            if (!response.ok) throw new Error('Failed to toggle addon');\n            // Trigger live reload of addons (bot-side)\n            try {\n                await fetch('/api/admin/addons/reload', {\n                    method: 'POST',\n                    credentials: 'include'\n                });\n            } catch  {}\n            // Update the appropriate list\n            const updateAddon = (prev)=>prev.map((a)=>a.name === addon.name ? {\n                        ...a,\n                        enabled: !a.enabled\n                    } : a);\n            if (addon.isCustomAddon) {\n                setCustomAddons(updateAddon);\n            } else {\n                setBuiltInAddons(updateAddon);\n            }\n            toast({\n                title: 'Success',\n                description: `Addon ${addon.enabled ? 'disabled' : 'enabled'} successfully`,\n                status: 'success',\n                duration: 3000\n            });\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to toggle addon',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    const handleUpdateConfig = (addon, newConfig)=>{\n        const updateAddon = (prev)=>prev.map((a)=>a.name === addon.name ? {\n                    ...a,\n                    config: newConfig\n                } : a);\n        if (addon.isCustomAddon) {\n            setCustomAddons(updateAddon);\n        } else {\n            setBuiltInAddons(updateAddon);\n        }\n    };\n    const handleConfigureAddon = async (addon)=>{\n        try {\n            const response = await fetch(`/api/admin/addons/${addon.name}`, {\n                credentials: 'include'\n            });\n            if (!response.ok) throw new Error('Failed to fetch addon config');\n            const data = await response.json();\n            setSelectedAddon(addon);\n            setConfigYaml(data.configYaml || '');\n            onOpen();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to fetch addon configuration',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    const handleEditConfig = async (addon)=>{\n        try {\n            console.log(`Fetching config for addon: ${addon.name}`);\n            const response = await fetch(`/api/admin/addons/${addon.name}/config`, {\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                const error = await response.json();\n                console.error('Config fetch error:', error);\n                throw new Error(error.error || `Failed to fetch addon config: ${response.status}`);\n            }\n            const data = await response.json();\n            console.log('Config data received:', data);\n            // Set the selected addon first\n            setSelectedAddon(addon);\n            // Then set the config with a small delay to ensure the modal is ready\n            setTimeout(()=>{\n                setConfigYaml(data.config || '');\n                onOpen();\n            }, 100);\n        } catch (error) {\n            console.error('Error fetching config:', error);\n            toast({\n                title: 'Error Fetching Config',\n                description: error.message,\n                status: 'error',\n                duration: 5000,\n                isClosable: true\n            });\n        }\n    };\n    const handleSaveConfig = async ()=>{\n        if (!selectedAddon) return;\n        try {\n            // Validate YAML format\n            const parsedConfig = yaml__WEBPACK_IMPORTED_MODULE_4__.parse(configYaml);\n            const response = await fetch(`/api/admin/addons/${selectedAddon.name}/config`, {\n                method: 'POST',\n                credentials: 'include',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    config: configYaml\n                })\n            });\n            if (!response.ok) throw new Error('Failed to save configuration');\n            toast({\n                title: 'Success',\n                description: 'Configuration saved successfully',\n                status: 'success',\n                duration: 3000\n            });\n            onClose();\n            fetchAddons(); // Refresh the addons list\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error.message || 'Failed to save configuration',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    const handleReloadAddons = async ()=>{\n        try {\n            await fetch('/api/admin/addons/reload', {\n                method: 'POST',\n                credentials: 'include'\n            });\n            toast({\n                title: 'Success',\n                description: 'Addons reloaded successfully',\n                status: 'success',\n                duration: 3000\n            });\n            // Refresh the addons list\n            fetchAddons();\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to reload addons',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    const handleRefreshCommands = async ()=>{\n        try {\n            await fetch('/api/admin/commands/refresh', {\n                method: 'POST',\n                credentials: 'include'\n            });\n            toast({\n                title: 'Success',\n                description: 'Discord commands refreshed successfully',\n                status: 'success',\n                duration: 3000\n            });\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: 'Failed to refresh commands',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    const handleDeleteAddon = async (addon)=>{\n        try {\n            const response = await fetch(`/api/admin/addons/${addon.name}`, {\n                method: 'DELETE',\n                credentials: 'include'\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.details || errorData.error || 'Failed to delete addon');\n            }\n            // Trigger live reload of addons (bot-side)\n            try {\n                await fetch('/api/admin/addons/reload', {\n                    method: 'POST',\n                    credentials: 'include'\n                });\n            } catch  {}\n            setCustomAddons((prev)=>prev.filter((a)=>a.name !== addon.name));\n            toast({\n                title: 'Success',\n                description: 'Custom addon deleted successfully',\n                status: 'success',\n                duration: 3000\n            });\n        } catch (error) {\n            toast({\n                title: 'Error',\n                description: error instanceof Error ? error.message : 'Failed to delete addon',\n                status: 'error',\n                duration: 5000\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Container, {\n                maxW: \"container.xl\",\n                py: 8,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {\n                    spacing: 8,\n                    align: \"stretch\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                            bg: \"rgba(255,255,255,0.08)\",\n                            p: 8,\n                            rounded: \"2xl\",\n                            backdropFilter: \"blur(10px)\",\n                            border: \"2px solid\",\n                            borderColor: \"purple.400\",\n                            boxShadow: \"0 0 15px rgba(159, 122, 234, 0.4)\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                                justify: \"space-between\",\n                                align: \"center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {\n                                        align: \"start\",\n                                        spacing: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {\n                                                size: \"xl\",\n                                                bgGradient: \"linear(to-r, purple.300, pink.400)\",\n                                                bgClip: \"text\",\n                                                children: \"Bot Addons Management\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 620,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                color: \"gray.300\",\n                                                children: \"Manage built-in addons and custom addons created with the addon builder\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 627,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                                color: \"gray.500\",\n                                                fontSize: \"sm\",\n                                                children: 'Use \"Reload Addons\" to refresh the bot\\'s addon system. Use \"Refresh Commands\" to update Discord\\'s command list.'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 630,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 619,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                                        spacing: 2,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                                    as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiRefreshCw\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                    lineNumber: 636,\n                                                    columnNumber: 29\n                                                }, void 0),\n                                                colorScheme: \"purple\",\n                                                variant: \"outline\",\n                                                onClick: handleReloadAddons,\n                                                children: \"Reload Addons\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                                    as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiZap\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                    lineNumber: 644,\n                                                    columnNumber: 29\n                                                }, void 0),\n                                                colorScheme: \"orange\",\n                                                variant: \"outline\",\n                                                onClick: handleRefreshCommands,\n                                                children: \"Refresh Commands\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                                lineNumber: 643,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 618,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {\n                                    size: \"lg\",\n                                    mb: 4,\n                                    color: \"blue.300\",\n                                    children: [\n                                        \"\\uD83D\\uDEE0️ Built-in Addons (\",\n                                        builtInAddons.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 657,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                    color: \"gray.400\",\n                                    mb: 6,\n                                    fontSize: \"sm\",\n                                    children: \"Core bot functionality - these addons cannot be deleted\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.SimpleGrid, {\n                                    columns: {\n                                        base: 1,\n                                        md: 2,\n                                        lg: 3\n                                    },\n                                    spacing: 6,\n                                    children: builtInAddons.map((addon)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddonCard, {\n                                            addon: addon,\n                                            onSave: (newConfig)=>handleUpdateConfig(addon, newConfig),\n                                            onToggle: handleToggleAddon,\n                                            onEdit: handleEditConfig\n                                        }, addon.name, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 13\n                                }, this),\n                                builtInAddons.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                    bg: \"gray.800\",\n                                    p: 6,\n                                    rounded: \"lg\",\n                                    textAlign: \"center\",\n                                    borderColor: \"gray.600\",\n                                    borderWidth: 1,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                        color: \"gray.400\",\n                                        children: \"No built-in addons found\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 684,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Heading, {\n                                    size: \"lg\",\n                                    mb: 4,\n                                    color: \"green.300\",\n                                    children: [\n                                        \"⚗️ Custom Addons (\",\n                                        customAddons.length,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 691,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                    color: \"gray.400\",\n                                    mb: 6,\n                                    fontSize: \"sm\",\n                                    children: \"Addons created with the addon builder - these can be edited or deleted\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 694,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.SimpleGrid, {\n                                    columns: {\n                                        base: 1,\n                                        md: 2,\n                                        lg: 3\n                                    },\n                                    spacing: 6,\n                                    children: customAddons.map((addon)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AddonCard, {\n                                            addon: addon,\n                                            onSave: (newConfig)=>handleUpdateConfig(addon, newConfig),\n                                            onToggle: handleToggleAddon,\n                                            onEdit: handleEditConfig,\n                                            onDelete: handleDeleteAddon\n                                        }, addon.name, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 699,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 13\n                                }, this),\n                                customAddons.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Box, {\n                                    bg: \"gray.800\",\n                                    p: 6,\n                                    rounded: \"lg\",\n                                    textAlign: \"center\",\n                                    borderColor: \"gray.600\",\n                                    borderWidth: 1,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            color: \"gray.400\",\n                                            children: \"No custom addons created yet\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            color: \"gray.500\",\n                                            fontSize: \"sm\",\n                                            mt: 2,\n                                            children: \"Use the Addon Builder to create your first custom addon!\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 710,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                            lineNumber: 690,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                    lineNumber: 607,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                lineNumber: 606,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Modal, {\n                isOpen: isOpen,\n                onClose: onClose,\n                size: \"6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalOverlay, {\n                        backdropFilter: \"blur(10px)\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalContent, {\n                        bg: \"gray.800\",\n                        border: \"1px solid\",\n                        borderColor: `${ADDON_COLORS[selectedAddon?.name]?.color || 'gray'}.500`,\n                        maxW: \"1200px\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.HStack, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                            as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSettings\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 739,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            children: [\n                                                \"Configure \",\n                                                selectedAddon?.name\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 738,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 737,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalCloseButton, {}, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalBody, {\n                                maxH: \"70vh\",\n                                overflowY: \"auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.VStack, {\n                                    spacing: 4,\n                                    align: \"stretch\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Text, {\n                                            color: \"gray.400\",\n                                            fontSize: \"sm\",\n                                            children: \"Edit the configuration in YAML format. Changes will be saved to config.yml\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 746,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(YamlEditor, {\n                                            value: configYaml,\n                                            onChange: setConfigYaml,\n                                            height: \"60vh\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 749,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 744,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.ModalFooter, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        mr: 3,\n                                        onClick: onClose,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        colorScheme: ADDON_COLORS[selectedAddon?.name]?.color || 'gray',\n                                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_chakra_ui_react__WEBPACK_IMPORTED_MODULE_2__.Icon, {\n                                            as: _barrel_optimize_names_FiCode_FiPackage_FiRefreshCw_FiSave_FiSettings_FiTrash2_FiZap_react_icons_fi__WEBPACK_IMPORTED_MODULE_6__.FiSave\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 25\n                                        }, void 0),\n                                        onClick: handleSaveConfig,\n                                        children: \"Save Changes\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                        lineNumber: 731,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n                lineNumber: 729,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Users\\\\Pete Gaming PC\\\\Desktop\\\\404 Bot\\\\src\\\\dashboard\\\\pages\\\\admin\\\\addons.tsx\",\n        lineNumber: 605,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/admin/addons.tsx\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/theme.ts":
/*!*************************!*\
  !*** ./styles/theme.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @chakra-ui/react */ \"@chakra-ui/react\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__]);\n_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n// 1. Global theme configuration\nconst config = {\n    initialColorMode: 'dark',\n    useSystemColorMode: false\n};\n// 2. Custom brand color palette (indigo-violet tone)\nconst colors = {\n    brand: {\n        50: '#f5f3ff',\n        100: '#ede9fe',\n        200: '#ddd6fe',\n        300: '#c4b5fd',\n        400: '#a78bfa',\n        500: '#8b5cf6',\n        600: '#7c3aed',\n        700: '#6d28d9',\n        800: '#5b21b6',\n        900: '#4c1d95'\n    },\n    discord: {\n        50: '#e8e9fd',\n        100: '#d1d3fc',\n        200: '#b9bcfa',\n        300: '#a2a5f9',\n        400: '#8b8ef7',\n        500: '#5865F2',\n        600: '#4752c4',\n        700: '#363f97',\n        800: '#242c69',\n        900: '#12193c'\n    }\n};\n// 3. Extend the default theme\nconst theme = (0,_chakra_ui_react__WEBPACK_IMPORTED_MODULE_0__.extendTheme)({\n    config,\n    fonts: {\n        heading: `'Inter', sans-serif`,\n        body: `'Inter', sans-serif`\n    },\n    colors,\n    styles: {\n        global: {\n            body: {\n                bg: 'gray.900',\n                color: 'gray.100'\n            }\n        }\n    },\n    components: {\n        Button: {\n            defaultProps: {\n                colorScheme: 'brand'\n            },\n            variants: {\n                solid: (props)=>({\n                        bg: `${props.colorScheme}.500`,\n                        color: 'white',\n                        _hover: {\n                            bg: `${props.colorScheme}.600`,\n                            transform: 'translateY(-2px)',\n                            boxShadow: 'lg'\n                        },\n                        _active: {\n                            bg: `${props.colorScheme}.700`,\n                            transform: 'translateY(0)'\n                        },\n                        transition: 'all 0.2s ease'\n                    })\n            }\n        },\n        Link: {\n            baseStyle: {\n                _hover: {\n                    textDecoration: 'none'\n                }\n            }\n        },\n        Box: {\n            baseStyle: {\n                transition: 'all 0.2s ease'\n            }\n        }\n    }\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (theme);\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./styles/theme.ts\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!*******************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \*******************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQWxlcnRDaXJjbGUsRmlCb3gsRmlDaGV2cm9uRG93bixGaUhlbHBDaXJjbGUsRmlIb21lLEZpTW9uaXRvcixGaVBhY2thZ2UsRmlTZXJ2ZXIsRmlTZXR0aW5ncyE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiAlertCircle,FiBox,FiChevronDown,FiHelpCircle,FiHome,FiMonitor,FiPackage,FiServer,FiSettings!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!****************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \****************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQmVsbCE9IS4uLy4uL25vZGVfbW9kdWxlcy8ucG5wbS9yZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL3JlYWN0LWljb25zL2ZpL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUEwSiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcbm9kZV9tb2R1bGVzXFwucG5wbVxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxub2RlX21vZHVsZXNcXHJlYWN0LWljb25zXFxmaVxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCAqIGZyb20gXCJEOlxcXFxVc2Vyc1xcXFxQZXRlIEdhbWluZyBQQ1xcXFxEZXNrdG9wXFxcXDQwNCBCb3RcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcXFxub2RlX21vZHVsZXNcXFxccmVhY3QtaWNvbnNcXFxcZmlcXFxcaW5kZXgubWpzXCIiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiBell!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiCode,FiPackage,FiRefreshCw,FiSave,FiSettings,FiTrash2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!***********************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiCode,FiPackage,FiRefreshCw,FiSave,FiSettings,FiTrash2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \***********************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpQ29kZSxGaVBhY2thZ2UsRmlSZWZyZXNoQ3csRmlTYXZlLEZpU2V0dGluZ3MsRmlUcmFzaDIsRmlaYXAhPSEuLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vcmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9yZWFjdC1pY29ucy9maS9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEoiLCJzb3VyY2VzIjpbIkQ6XFxVc2Vyc1xcUGV0ZSBHYW1pbmcgUENcXERlc2t0b3BcXDQwNCBCb3RcXG5vZGVfbW9kdWxlc1xcLnBucG1cXHJlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxyZWFjdC1pY29uc1xcZmlcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgKiBmcm9tIFwiRDpcXFxcVXNlcnNcXFxcUGV0ZSBHYW1pbmcgUENcXFxcRGVza3RvcFxcXFw0MDQgQm90XFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxyZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjBcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWljb25zXFxcXGZpXFxcXGluZGV4Lm1qc1wiIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiCode,FiPackage,FiRefreshCw,FiSave,FiSettings,FiTrash2,FiZap!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs */ \"(pages-dir-node)/../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\");\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__) if(__WEBPACK_IMPORT_KEY__ !== \"default\") __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => D_Users_Pete_Gaming_PC_Desktop_404_Bot_node_modules_pnpm_react_icons_5_5_0_react_19_1_0_node_modules_react_icons_fi_index_mjs__WEBPACK_IMPORTED_MODULE_0__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUZpTG9nT3V0IT0hLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL3JlYWN0LWljb25zQDUuNS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvcmVhY3QtaWNvbnMvZmkvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTBKIiwic291cmNlcyI6WyJEOlxcVXNlcnNcXFBldGUgR2FtaW5nIFBDXFxEZXNrdG9wXFw0MDQgQm90XFxub2RlX21vZHVsZXNcXC5wbnBtXFxyZWFjdC1pY29uc0A1LjUuMF9yZWFjdEAxOS4xLjBcXG5vZGVfbW9kdWxlc1xccmVhY3QtaWNvbnNcXGZpXFxpbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIkQ6XFxcXFVzZXJzXFxcXFBldGUgR2FtaW5nIFBDXFxcXERlc2t0b3BcXFxcNDA0IEJvdFxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxccmVhY3QtaWNvbnNANS41LjBfcmVhY3RAMTkuMS4wXFxcXG5vZGVfbW9kdWxlc1xcXFxyZWFjdC1pY29uc1xcXFxmaVxcXFxpbmRleC5tanNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=FiLogOut!=!../../node_modules/.pnpm/react-icons@5.5.0_react@19.1.0/node_modules/react-icons/fi/index.mjs\n");

/***/ }),

/***/ "@chakra-ui/react":
/*!***********************************!*\
  !*** external "@chakra-ui/react" ***!
  \***********************************/
/***/ ((module) => {

module.exports = import("@chakra-ui/react");;

/***/ }),

/***/ "ace-builds/src-noconflict/ext-language_tools":
/*!***************************************************************!*\
  !*** external "ace-builds/src-noconflict/ext-language_tools" ***!
  \***************************************************************/
/***/ ((module) => {

module.exports = require("ace-builds/src-noconflict/ext-language_tools");

/***/ }),

/***/ "ace-builds/src-noconflict/mode-yaml":
/*!******************************************************!*\
  !*** external "ace-builds/src-noconflict/mode-yaml" ***!
  \******************************************************/
/***/ ((module) => {

module.exports = require("ace-builds/src-noconflict/mode-yaml");

/***/ }),

/***/ "ace-builds/src-noconflict/theme-twilight":
/*!***********************************************************!*\
  !*** external "ace-builds/src-noconflict/theme-twilight" ***!
  \***********************************************************/
/***/ ((module) => {

module.exports = require("ace-builds/src-noconflict/theme-twilight");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "next-auth/react":
/*!**********************************!*\
  !*** external "next-auth/react" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("next-auth/react");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

module.exports = require("react");

/***/ }),

/***/ "react-ace":
/*!****************************!*\
  !*** external "react-ace" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-ace");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "swr":
/*!**********************!*\
  !*** external "swr" ***!
  \**********************/
/***/ ((module) => {

module.exports = import("swr");;

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/@swc+helpers@0.5.15","vendor-chunks/react-icons@5.5.0_react@19.1.0"], () => (__webpack_exec__("(pages-dir-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadmin%2Faddons&preferredRegion=&absolutePagePath=.%2Fpages%5Cadmin%5Caddons.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();