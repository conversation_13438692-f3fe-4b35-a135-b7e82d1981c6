(()=>{var e={};e.id=2365,e.ids=[2365],e.modules={361:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},1802:(e,t,r)=>{"use strict";r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{default:()=>g});var s=r(8732),a=r(2015),n=r(9733),i=r(1011),d=r(8079),l=r(2115),c=r(8364),u=r.n(c),h=e([n,i]);[n,i]=h.then?(await h)():h;let m=u()(()=>r.e(823).then(r.bind(r,823)),{loadableGenerated:{modules:["pages\\admin\\addons.tsx -> ../../components/YamlEditor"]},ssr:!1,loading:()=>(0,s.jsx)(n.Skeleton,{height:"400px"})}),x={"voice-mistress":{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"}},tickets:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"}},"welcome-goodbye":{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"}},example:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"}},default:{color:"teal",gradient:{from:"rgba(49, 151, 149, 0.4)",to:"rgba(49, 151, 149, 0.1)"}}},p=({addon:e,onSave:t,onToggle:r,onEdit:o,onDelete:i})=>{let[c,u]=(0,a.useState)(!1),[h,g]=(0,a.useState)(e.config||""),[p,f]=(0,a.useState)(null),[b,j]=(0,a.useState)(!1),y=(0,n.useToast)();(0,a.useEffect)(()=>{g(e.config||"")},[e.config]);let S=async()=>{try{l.parse(h||""),f(null);let r=await fetch(`/api/admin/addons/${e.name}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:h})});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to save configuration")}t(h),u(!1),y({title:"Success",description:"Configuration saved successfully",status:"success",duration:3e3})}catch(e){f(e.message||"Invalid YAML format"),y({title:"Error",description:e.message||"Invalid YAML format",status:"error",duration:5e3})}};return(0,s.jsx)(n.Card,{bg:x[e.name]?.color?`linear-gradient(135deg, ${x[e.name].gradient.from}, ${x[e.name].gradient.to})`:"gray.900",backdropFilter:"blur(10px)",borderWidth:2,borderColor:x[e.name]?.color?`${x[e.name].color}.400`:"gray.600",rounded:"xl",overflow:"hidden",transition:"all 0.2s",_hover:{transform:"translateY(-2px)",boxShadow:x[e.name]?.color?`0 4px 20px ${x[e.name].gradient.from}`:"none"},children:(0,s.jsx)(n.CardBody,{children:c?(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(m,{value:h,onChange:e=>{g(e),f(null)}}),p&&(0,s.jsx)(n.Text,{color:"red.400",fontSize:"sm",children:p}),(0,s.jsxs)(n.HStack,{justify:"flex-end",spacing:2,children:[(0,s.jsx)(n.Button,{variant:"ghost",onClick:()=>{u(!1),g(e.config||""),f(null)},children:"Cancel"}),(0,s.jsx)(n.Button,{colorScheme:"purple",onClick:S,isDisabled:!!p,children:"Save Changes"})]})]}):(0,s.jsxs)(n.VStack,{align:"stretch",spacing:4,children:[(0,s.jsxs)(n.HStack,{justify:"space-between",children:[(0,s.jsxs)(n.HStack,{children:[(0,s.jsx)(n.Icon,{as:d.est,color:`${x[e.name]?.color||"gray"}.400`,boxSize:5}),(0,s.jsx)(n.Heading,{size:"md",color:"white",children:e.name})]}),(0,s.jsx)(n.Badge,{colorScheme:e.enabled&&x[e.name]?.color||"gray",children:e.enabled?"Active":"Inactive"})]}),(0,s.jsx)(n.Text,{color:"gray.300",fontSize:"sm",noOfLines:2,children:e.description}),(0,s.jsxs)(n.HStack,{children:[(0,s.jsxs)(n.Text,{color:"gray.400",fontSize:"xs",children:["v",e.version]}),(0,s.jsxs)(n.Text,{color:"gray.400",fontSize:"xs",children:["by ",e.author]})]}),(0,s.jsx)(n.Divider,{borderColor:"whiteAlpha.200"}),(0,s.jsxs)(n.HStack,{justify:"space-between",children:[(0,s.jsx)(n.Switch,{isChecked:e.enabled,onChange:()=>r(e),colorScheme:x[e.name]?.color||"gray"}),(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsx)(n.Button,{size:"sm",leftIcon:(0,s.jsx)(n.Icon,{as:d.PjK}),variant:"ghost",colorScheme:x[e.name]?.color||"gray",onClick:()=>o(e),children:"Edit Config"}),e.isCustomAddon&&i&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.Button,{size:"sm",leftIcon:(0,s.jsx)(n.Icon,{as:d.IXo}),variant:"ghost",colorScheme:"red",onClick:()=>j(!0),children:"Delete"}),(0,s.jsxs)(n.Modal,{isOpen:b,onClose:()=>j(!1),children:[(0,s.jsx)(n.ModalOverlay,{}),(0,s.jsxs)(n.ModalContent,{children:[(0,s.jsx)(n.ModalHeader,{children:"Delete Custom Addon"}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{children:(0,s.jsxs)(n.Text,{children:["Are you sure you want to delete ",(0,s.jsx)("strong",{children:e.name}),"? This action cannot be undone and will permanently remove all files for this custom addon."]})}),(0,s.jsxs)(n.ModalFooter,{children:[(0,s.jsx)(n.Button,{variant:"ghost",mr:3,onClick:()=>j(!1),children:"Cancel"}),(0,s.jsx)(n.Button,{colorScheme:"red",onClick:()=>{i(e),j(!1)},children:"Delete"})]})]})]})]})]})]})]})})},e.name)};function g(){let[e,t]=(0,a.useState)([]),[r,o]=(0,a.useState)([]),[c,u]=(0,a.useState)(null),[h,g]=(0,a.useState)(""),{isOpen:f,onOpen:b,onClose:j}=(0,n.useDisclosure)(),y=(0,n.useToast)(),S=async()=>{try{let e=await fetch("/api/admin/addons",{credentials:"include",headers:{"Content-Type":"application/json"}});if(!e.ok){let t=await e.json();if(401===e.status||403===e.status){window.location.href="/signin";return}throw Error(t.message||"Failed to fetch addons")}let r=await e.json();if(r.builtInAddons&&r.customAddons)t(r.builtInAddons),o(r.customAddons);else{let e=r.addons?.filter(e=>!e.isCustomAddon)||[],s=r.addons?.filter(e=>e.isCustomAddon)||[];t(e),o(s)}}catch(e){y({title:"Error",description:e instanceof Error?e.message:"Failed to fetch addons",status:"error",duration:5e3})}},C=async e=>{try{if(!(await fetch(`/api/admin/addons/${e.name}`,{method:"PATCH",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({enabled:!e.enabled})})).ok)throw Error("Failed to toggle addon");try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"})}catch{}let r=t=>t.map(t=>t.name===e.name?{...t,enabled:!t.enabled}:t);e.isCustomAddon?o(r):t(r),y({title:"Success",description:`Addon ${e.enabled?"disabled":"enabled"} successfully`,status:"success",duration:3e3})}catch(e){y({title:"Error",description:"Failed to toggle addon",status:"error",duration:5e3})}},v=(e,r)=>{let s=t=>t.map(t=>t.name===e.name?{...t,config:r}:t);e.isCustomAddon?o(s):t(s)},w=async e=>{try{let t=await fetch(`/api/admin/addons/${e.name}/config`,{credentials:"include"});if(!t.ok){let e=await t.json();throw Error(e.error||`Failed to fetch addon config: ${t.status}`)}let r=await t.json();u(e),setTimeout(()=>{g(r.config||""),b()},100)}catch(e){y({title:"Error Fetching Config",description:e.message,status:"error",duration:5e3,isClosable:!0})}},k=async()=>{if(c)try{if(l.parse(h),!(await fetch(`/api/admin/addons/${c.name}/config`,{method:"POST",credentials:"include",headers:{"Content-Type":"application/json"},body:JSON.stringify({config:h})})).ok)throw Error("Failed to save configuration");y({title:"Success",description:"Configuration saved successfully",status:"success",duration:3e3}),j(),S()}catch(e){y({title:"Error",description:e.message||"Failed to save configuration",status:"error",duration:5e3})}},M=async()=>{try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"}),y({title:"Success",description:"Addons reloaded successfully",status:"success",duration:3e3}),S()}catch(e){y({title:"Error",description:"Failed to reload addons",status:"error",duration:5e3})}},A=async()=>{try{await fetch("/api/admin/commands/refresh",{method:"POST",credentials:"include"}),y({title:"Success",description:"Discord commands refreshed successfully",status:"success",duration:3e3})}catch(e){y({title:"Error",description:"Failed to refresh commands",status:"error",duration:5e3})}},T=async e=>{try{let t=await fetch(`/api/admin/addons/${e.name}`,{method:"DELETE",credentials:"include"});if(!t.ok){let e=await t.json();throw Error(e.details||e.error||"Failed to delete addon")}try{await fetch("/api/admin/addons/reload",{method:"POST",credentials:"include"})}catch{}o(t=>t.filter(t=>t.name!==e.name)),y({title:"Success",description:"Custom addon deleted successfully",status:"success",duration:3e3})}catch(e){y({title:"Error",description:e instanceof Error?e.message:"Failed to delete addon",status:"error",duration:5e3})}};return(0,s.jsxs)(i.A,{children:[(0,s.jsx)(n.Container,{maxW:"container.xl",py:8,children:(0,s.jsxs)(n.VStack,{spacing:8,align:"stretch",children:[(0,s.jsx)(n.Box,{bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"purple.400",boxShadow:"0 0 15px rgba(159, 122, 234, 0.4)",children:(0,s.jsxs)(n.HStack,{justify:"space-between",align:"center",children:[(0,s.jsxs)(n.VStack,{align:"start",spacing:2,children:[(0,s.jsx)(n.Heading,{size:"xl",bgGradient:"linear(to-r, purple.300, pink.400)",bgClip:"text",children:"Bot Addons Management"}),(0,s.jsx)(n.Text,{color:"gray.300",children:"Manage built-in addons and custom addons created with the addon builder"}),(0,s.jsx)(n.Text,{color:"gray.500",fontSize:"sm",children:'Use "Reload Addons" to refresh the bot\'s addon system. Use "Refresh Commands" to update Discord\'s command list.'})]}),(0,s.jsxs)(n.HStack,{spacing:2,children:[(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(n.Icon,{as:d.jTZ}),colorScheme:"purple",variant:"outline",onClick:M,children:"Reload Addons"}),(0,s.jsx)(n.Button,{leftIcon:(0,s.jsx)(n.Icon,{as:d.FrA}),colorScheme:"orange",variant:"outline",onClick:A,children:"Refresh Commands"})]})]})}),(0,s.jsxs)(n.Box,{children:[(0,s.jsxs)(n.Heading,{size:"lg",mb:4,color:"blue.300",children:["\uD83D\uDEE0️ Built-in Addons (",e.length,")"]}),(0,s.jsx)(n.Text,{color:"gray.400",mb:6,fontSize:"sm",children:"Core bot functionality - these addons cannot be deleted"}),(0,s.jsx)(n.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:e.map(e=>(0,s.jsx)(p,{addon:e,onSave:t=>v(e,t),onToggle:C,onEdit:w},e.name))}),0===e.length&&(0,s.jsx)(n.Box,{bg:"gray.800",p:6,rounded:"lg",textAlign:"center",borderColor:"gray.600",borderWidth:1,children:(0,s.jsx)(n.Text,{color:"gray.400",children:"No built-in addons found"})})]}),(0,s.jsxs)(n.Box,{children:[(0,s.jsxs)(n.Heading,{size:"lg",mb:4,color:"green.300",children:["⚗️ Custom Addons (",r.length,")"]}),(0,s.jsx)(n.Text,{color:"gray.400",mb:6,fontSize:"sm",children:"Addons created with the addon builder - these can be edited or deleted"}),(0,s.jsx)(n.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:r.map(e=>(0,s.jsx)(p,{addon:e,onSave:t=>v(e,t),onToggle:C,onEdit:w,onDelete:T},e.name))}),0===r.length&&(0,s.jsxs)(n.Box,{bg:"gray.800",p:6,rounded:"lg",textAlign:"center",borderColor:"gray.600",borderWidth:1,children:[(0,s.jsx)(n.Text,{color:"gray.400",children:"No custom addons created yet"}),(0,s.jsx)(n.Text,{color:"gray.500",fontSize:"sm",mt:2,children:"Use the Addon Builder to create your first custom addon!"})]})]})]})}),(0,s.jsxs)(n.Modal,{isOpen:f,onClose:j,size:"6xl",children:[(0,s.jsx)(n.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(n.ModalContent,{bg:"gray.800",border:"1px solid",borderColor:`${x[c?.name]?.color||"gray"}.500`,maxW:"1200px",children:[(0,s.jsx)(n.ModalHeader,{children:(0,s.jsxs)(n.HStack,{children:[(0,s.jsx)(n.Icon,{as:d.VSk}),(0,s.jsxs)(n.Text,{children:["Configure ",c?.name]})]})}),(0,s.jsx)(n.ModalCloseButton,{}),(0,s.jsx)(n.ModalBody,{maxH:"70vh",overflowY:"auto",children:(0,s.jsxs)(n.VStack,{spacing:4,align:"stretch",children:[(0,s.jsx)(n.Text,{color:"gray.400",fontSize:"sm",children:"Edit the configuration in YAML format. Changes will be saved to config.yml"}),(0,s.jsx)(m,{value:h,onChange:g,height:"60vh"})]})}),(0,s.jsxs)(n.ModalFooter,{children:[(0,s.jsx)(n.Button,{variant:"ghost",mr:3,onClick:j,children:"Cancel"}),(0,s.jsx)(n.Button,{colorScheme:x[c?.name]?.color||"gray",leftIcon:(0,s.jsx)(n.Icon,{as:d.Bc_}),onClick:k,children:"Save Changes"})]})]})]})]})}o()}catch(e){o(e)}})},2015:e=>{"use strict";e.exports=require("react")},2115:e=>{"use strict";e.exports=require("yaml")},2326:e=>{"use strict";e.exports=require("react-dom")},2813:e=>{"use strict";e.exports=require("react-ace")},3873:e=>{"use strict";e.exports=require("path")},4002:e=>{"use strict";e.exports=require("ace-builds/src-noconflict/mode-yaml")},4038:(e,t,r)=>{"use strict";r.a(e,async(e,o)=>{try{r.r(t),r.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>m,getStaticPaths:()=>g,getStaticProps:()=>h,reportWebVitals:()=>p,routeModule:()=>C,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>b,unstable_getStaticProps:()=>f});var s=r(1292),a=r(8834),n=r(786),i=r(3567),d=r(8077),l=r(1802),c=e([d,l]);[d,l]=c.then?(await c)():c;let u=(0,n.M)(l,"default"),h=(0,n.M)(l,"getStaticProps"),g=(0,n.M)(l,"getStaticPaths"),m=(0,n.M)(l,"getServerSideProps"),x=(0,n.M)(l,"config"),p=(0,n.M)(l,"reportWebVitals"),f=(0,n.M)(l,"unstable_getStaticProps"),b=(0,n.M)(l,"unstable_getStaticPaths"),j=(0,n.M)(l,"unstable_getStaticParams"),y=(0,n.M)(l,"unstable_getServerProps"),S=(0,n.M)(l,"unstable_getServerSideProps"),C=new s.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/admin/addons",pathname:"/admin/addons",bundlePath:"",filename:""},components:{App:d.default,Document:i.default},userland:l});o()}catch(e){o(e)}})},4075:e=>{"use strict";e.exports=require("zlib")},4078:e=>{"use strict";e.exports=import("swr")},4722:e=>{"use strict";e.exports=require("next-auth/react")},6649:e=>{"use strict";e.exports=require("ace-builds/src-noconflict/theme-twilight")},7910:e=>{"use strict";e.exports=require("stream")},8364:(e,t,r)=>{e.exports=r(9554)},8732:e=>{"use strict";e.exports=require("react/jsx-runtime")},8914:(e,t,r)=>{"use strict";e.exports=r(1292).vendored.contexts.Loadable},9021:e=>{"use strict";e.exports=require("fs")},9554:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return d},noSSR:function(){return i}});let o=r(2403),s=r(8732);r(2015);let a=o._(r(8914));function n(e){return{default:(null==e?void 0:e.default)||e}}function i(e,t){delete t.webpack,delete t.modules;let r=t.loading;return()=>(0,s.jsx)(r,{error:null,isLoading:!0,pastDelay:!1,timedOut:!1})}function d(e,t){let r=a.default,o={loading:e=>{let{error:t,isLoading:r,pastDelay:o}=e;return null}};e instanceof Promise?o.loader=()=>e:"function"==typeof e?o.loader=e:"object"==typeof e&&(o={...o,...e});let s=(o={...o,...t}).loader;return(o.loadableGenerated&&(o={...o,...o.loadableGenerated},delete o.loadableGenerated),"boolean"!=typeof o.ssr||o.ssr)?r({...o,loader:()=>null!=s?s().then(n):Promise.resolve(n(()=>null))}):(delete o.webpack,delete o.modules,i(r,o))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9581:e=>{"use strict";e.exports=require("ace-builds/src-noconflict/ext-language_tools")},9733:e=>{"use strict";e.exports=import("@chakra-ui/react")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[8270,4874,752,6281,5333],()=>r(4038));module.exports=o})();