import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Badge,
  Stat,
  StatLabel,
  StatNumber,
  useColorModeValue,
  Collapse,
  Button,
  Code,
  Divider,
  Icon,
  Tooltip,
  useClipboard
} from '@chakra-ui/react';
import { FaUser, FaGamepad, FaClock, FaLock, FaUnlock, FaInfoCircle, FaCopy, FaCheck } from 'react-icons/fa';

interface GameServerStatus {
  name: string;
  host: string;
  port: number;
  type: string;
  online: boolean;
  players?: Array<{ name: string }>;
  maxPlayers?: number;
  map?: string;
  ping?: number;
  error?: string;
  lastUpdated: string;
  description?: string;
  hasPassword?: boolean;
  password?: string;
}

interface GameServerCardProps {
  server: GameServerStatus;
}

// Color schemes for different game types
const GAME_COLORS = {
  minecraft: {
    color: 'green',
    gradient: {
      from: 'rgba(72, 187, 120, 0.4)',
      to: 'rgba(72, 187, 120, 0.1)'
    },
    accent: '#68D391'
  },
  minecraftbe: {
    color: 'green',
    gradient: {
      from: 'rgba(72, 187, 120, 0.4)',
      to: 'rgba(72, 187, 120, 0.1)'
    },
    accent: '#68D391'
  },
  csgo: {
    color: 'orange',
    gradient: {
      from: 'rgba(237, 137, 54, 0.4)',
      to: 'rgba(237, 137, 54, 0.1)'
    },
    accent: '#ED8936'
  },
  valheim: {
    color: 'red',
    gradient: {
      from: 'rgba(245, 101, 101, 0.4)',
      to: 'rgba(245, 101, 101, 0.1)'
    },
    accent: '#F56565'
  },
  rust: {
    color: 'brown',
    gradient: {
      from: 'rgba(193, 105, 79, 0.4)',
      to: 'rgba(193, 105, 79, 0.1)'
    },
    accent: '#C1694F'
  },
  arkse: {
    color: 'purple',
    gradient: {
      from: 'rgba(159, 122, 234, 0.4)',
      to: 'rgba(159, 122, 234, 0.1)'
    },
    accent: '#9F7AEA'
  },
  sdtd: {
    color: 'yellow',
    gradient: {
      from: 'rgba(236, 201, 75, 0.4)',
      to: 'rgba(236, 201, 75, 0.1)'
    },
    accent: '#ECC94B'
  },
  // Default color scheme for unknown game types
  default: {
    color: 'blue',
    gradient: {
      from: 'rgba(66, 153, 225, 0.4)',
      to: 'rgba(66, 153, 225, 0.1)'
    },
    accent: '#4299E1'
  }
};

export const GameServerCard: React.FC<GameServerCardProps> = ({ server }) => {
  const [showDetails, setShowDetails] = useState(false);
  const { hasCopied: hasConnectionCopied, onCopy: onConnectionCopy } = useClipboard(
    server.hasPassword 
      ? `Server: ${server.host}:${server.port}\nPassword: ${server.password}`
      : `${server.host}:${server.port}`
  );

  // Get color scheme based on game type
  const gameColors = GAME_COLORS[server.type.toLowerCase() as keyof typeof GAME_COLORS] || GAME_COLORS.default;

  const bgColor = useColorModeValue('gray.800', 'gray.800');
  const borderColor = server.online 
    ? `${gameColors.color}.400`
    : 'red.400';
  const statusColor = server.online ? gameColors.color : 'red';
  const playerCount = server.players?.length || 0;

  // Get connection instructions based on game type
  const getConnectionInstructions = () => {
    switch (server.type.toLowerCase()) {
      case 'minecraft':
      case 'minecraftbe':
        return `1. Open Minecraft
2. Click "Multiplayer"
3. Click "Add Server"
4. Enter server address: ${server.host}:${server.port}${server.hasPassword ? `\n5. Enter Password: ${server.password}` : ''}`;
      case 'sdtd':
        return `1. Open 7 Days to Die
2. Click "Join Game"
3. Click "Server Browser"
4. Search for "${server.name}"
${server.hasPassword ? `5. Enter Password: ${server.password}` : ''}`;
      default:
        return `Connect using: ${server.host}:${server.port}${server.hasPassword ? `\nPassword: ${server.password}` : ''}`;
    }
  };

  return (
    <Box
      p={5}
      bg={`linear-gradient(135deg, ${gameColors.gradient.from}, ${gameColors.gradient.to})`}
      borderRadius="xl"
      border="2px"
      borderColor={borderColor}
      width="100%"
      position="relative"
      overflow="hidden"
      zIndex={1}
      transition="all 0.2s"
      _before={{
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        bg: 'linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%)',
        zIndex: -1,
      }}
      _hover={{
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 20px ${gameColors.gradient.from}`,
        borderColor: server.online ? `${gameColors.color}.300` : 'red.300',
      }}
    >
      <VStack align="stretch" spacing={4}>
        <HStack justify="space-between">
          <VStack align="start" spacing={1}>
            <HStack>
              <Text fontSize="xl" fontWeight="bold" color="white">
                {server.name || `${server.host}:${server.port}`}
              </Text>
              {server.hasPassword && (
                <Tooltip label="Password Protected">
                  <span>
                    <Icon as={FaLock} color={`${gameColors.color}.200`} />
                  </span>
                </Tooltip>
              )}
            </HStack>
            <HStack spacing={2}>
              <Badge colorScheme={statusColor} fontSize="sm">
                {server.online ? 'Online' : 'Offline'}
              </Badge>
              <Badge colorScheme={gameColors.color} fontSize="sm">
                {server.type.toUpperCase()}
              </Badge>
            </HStack>
          </VStack>
        </HStack>

        {server.description && (
          <Text color="gray.300" fontSize="sm">
            {server.description}
          </Text>
        )}

        {server.online ? (
          <>
            <HStack spacing={8} justify="space-around">
              <Stat>
                <StatLabel color="gray.400">
                  <HStack>
                    <FaUser />
                    <Text>Players</Text>
                  </HStack>
                </StatLabel>
                <StatNumber color="white">
                  {playerCount}/{server.maxPlayers || '?'}
                </StatNumber>
              </Stat>

              {server.map && (
                <Stat>
                  <StatLabel color="gray.400">
                    <HStack>
                      <FaGamepad />
                      <Text>Map</Text>
                    </HStack>
                  </StatLabel>
                  <StatNumber color="white" fontSize="lg">{server.map}</StatNumber>
                </Stat>
              )}

              {server.ping && (
                <Stat>
                  <StatLabel color="gray.400">Ping</StatLabel>
                  <StatNumber color={server.ping < 100 ? `${gameColors.color}.400` : server.ping < 200 ? 'yellow.400' : 'red.400'}>
                    {server.ping}ms
                  </StatNumber>
                </Stat>
              )}
            </HStack>

            <Button
              size="sm"
              variant="ghost"
              colorScheme={gameColors.color}
              onClick={() => setShowDetails(!showDetails)}
              leftIcon={<FaInfoCircle />}
            >
              {showDetails ? 'Hide Details' : 'Show Details'}
            </Button>

            <Collapse in={showDetails}>
              <VStack align="stretch" spacing={3} pt={2}>
                <Divider borderColor="whiteAlpha.200" />
                
                <Box>
                  <Text color="gray.400" mb={2} fontWeight="bold">Connection Information</Text>
                  <VStack align="stretch" spacing={2}>
                    <HStack>
                      <Code p={2} borderRadius="md" bg="gray.700" color={`${gameColors.color}.300`}>
                        {server.host}:{server.port}
                      </Code>
                      {server.hasPassword && (
                        <Code p={2} borderRadius="md" bg="gray.700" color={`${gameColors.color}.300`}>
                          Password: {server.password}
                        </Code>
                      )}
                      <Tooltip label={hasConnectionCopied ? 'Copied!' : 'Copy All'}>
                        <Button
                          size="sm"
                          variant="ghost"
                          colorScheme={hasConnectionCopied ? 'green' : gameColors.color}
                          onClick={onConnectionCopy}
                        >
                          <Icon as={hasConnectionCopied ? FaCheck : FaCopy} />
                        </Button>
                      </Tooltip>
                    </HStack>
                  </VStack>
                </Box>

                <Box>
                  <Text color="gray.400" mb={2} fontWeight="bold">How to Connect</Text>
                  <Code display="block" whiteSpace="pre" p={3} borderRadius="md" bg="gray.700" color={`${gameColors.color}.300`}>
                    {getConnectionInstructions()}
                  </Code>
                </Box>
              </VStack>
            </Collapse>
          </>
        ) : (
          <Text color="red.400">{server.error || 'Server is offline'}</Text>
        )}

        <HStack fontSize="sm" color="gray.500" spacing={2}>
          <FaClock />
          <Text>
            Last updated: {new Date(server.lastUpdated).toLocaleTimeString()}
          </Text>
        </HStack>
      </VStack>
    </Box>
  );
}; 