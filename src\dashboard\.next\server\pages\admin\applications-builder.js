"use strict";(()=>{var e={};e.id=4779,e.ids=[4779],e.modules={361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},2015:e=>{e.exports=require("react")},2326:e=>{e.exports=require("react-dom")},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},6536:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{default:()=>u});var a=i(8732),s=i(2015),l=i(9733),o=i(2695),n=i(1011),c=i(4722),d=i(8358),p=e([l,n]);[l,n]=p.then?(await p)():p;let b=[{value:"text",label:"Short Text",icon:o.uO9},{value:"textarea",label:"Long Text",icon:o.kkc},{value:"select",label:"Dropdown",icon:o.Vr3},{value:"radio",label:"Multiple Choice",icon:o.aQJ},{value:"checkbox",label:"Checkboxes",icon:o.aQJ},{value:"number",label:"Number",icon:o.Ph},{value:"email",label:"Email",icon:o.uoG}],m=["blue","green","red","purple","orange","pink","teal","cyan","yellow"];function u(){let[e,t]=(0,s.useState)([]),[i,r]=(0,s.useState)(null),[p,u]=(0,s.useState)(!0),[b,m]=(0,s.useState)(!1),[g,y]=(0,s.useState)(0),{isOpen:q,onOpen:S,onClose:f}=(0,l.useDisclosure)(),{isOpen:C,onOpen:k,onClose:v}=(0,l.useDisclosure)(),w=(0,l.useToast)(),{data:T}=(0,c.useSession)();(0,d.useRouter)();let A=async()=>{try{u(!0);let e=await fetch("/api/admin/applications-builder");if(e.ok){let i=await e.json();t(i.applications||[])}}catch(e){w({title:"Error",description:"Failed to load applications",status:"error",duration:3e3})}finally{u(!1)}},P=e=>{r(e),S()},M=async e=>{try{(await fetch(`/api/admin/applications-builder/${e}`,{method:"DELETE"})).ok&&(t(t=>t.filter(t=>t.id!==e)),w({title:"Success",description:"Application deleted successfully",status:"success",duration:3e3}))}catch(e){w({title:"Error",description:"Failed to delete application",status:"error",duration:3e3})}},F=async e=>{m(!0);try{let t=await fetch("/api/admin/applications-builder",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(t.ok)await t.json(),await A(),f(),w({title:"Success",description:"Application saved successfully",status:"success",duration:3e3});else{let e=await t.json();throw Error(e.error||"Failed to save application")}}catch(e){w({title:"Error",description:e instanceof Error?e.message:"Failed to save application",status:"error",duration:5e3})}finally{m(!1)}},H=async(t,i)=>{try{let r=e.find(e=>e.id===t);if(!r)return;let a={...r,enabled:i},s=await fetch("/api/admin/applications-builder",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(s.ok)await A(),w({title:"Success",description:`Application ${i?"activated":"deactivated"} successfully`,status:"success",duration:3e3});else{let e=await s.json();throw Error(e.error||"Failed to update application status")}}catch(e){w({title:"Error",description:e instanceof Error?e.message:"Failed to update application status",status:"error",duration:3e3})}};return(0,a.jsxs)(n.A,{children:[(0,a.jsx)(l.Box,{p:8,children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:6,children:[(0,a.jsxs)(l.HStack,{justify:"space-between",children:[(0,a.jsxs)(l.VStack,{align:"start",spacing:1,children:[(0,a.jsx)(l.Heading,{size:"lg",children:"Applications Builder"}),(0,a.jsx)(l.Text,{color:"gray.600",_dark:{color:"gray.300"},children:"Create and manage custom application forms for your server"})]}),(0,a.jsx)(l.Button,{colorScheme:"blue",leftIcon:(0,a.jsx)(o.OiG,{}),onClick:()=>{let e={id:`app-${Date.now()}`,title:"New Application",description:"Description for new application",color:"blue",icon:"FaClipboardList",enabled:!1,questions:[],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!1,notificationChannels:[]}};r(e),S()},children:"Create Application"})]}),(0,a.jsxs)(l.Tabs,{index:g,onChange:y,variant:"enclosed",children:[(0,a.jsxs)(l.TabList,{children:[(0,a.jsx)(l.Tab,{children:"Applications"}),(0,a.jsx)(l.Tab,{children:"Templates"}),(0,a.jsx)(l.Tab,{children:"Settings"})]}),(0,a.jsxs)(l.TabPanels,{children:[(0,a.jsx)(l.TabPanel,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.Alert,{status:"info",children:[(0,a.jsx)(l.AlertIcon,{}),(0,a.jsx)(l.Text,{children:"Build custom application forms with drag-and-drop ease. Create different types of applications for your server members."})]}),(0,a.jsx)(l.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:4,children:e.map(e=>(0,a.jsx)(l.Card,{borderTop:"4px solid",borderTopColor:`${e.color}.500`,children:(0,a.jsx)(l.CardBody,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:3,children:[(0,a.jsxs)(l.HStack,{justify:"space-between",children:[(0,a.jsxs)(l.VStack,{align:"start",spacing:1,children:[(0,a.jsx)(l.Heading,{size:"md",children:e.title}),(0,a.jsx)(l.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,a.jsx)(l.Badge,{colorScheme:e.enabled?"green":"gray",children:e.enabled?"Active":"Inactive"})]}),(0,a.jsxs)(l.Text,{fontSize:"sm",color:"gray.500",children:[e.questions.length," questions configured"]}),(0,a.jsxs)(l.HStack,{spacing:2,children:[(0,a.jsx)(l.Button,{size:"sm",colorScheme:"blue",leftIcon:(0,a.jsx)(o.uO9,{}),onClick:()=>P(e),children:"Edit"}),(0,a.jsx)(l.Button,{size:"sm",variant:"outline",leftIcon:(0,a.jsx)(o.Ny1,{}),onClick:()=>{r(e),k()},children:"Preview"}),(0,a.jsxs)(l.Menu,{children:[(0,a.jsx)(l.MenuButton,{as:l.IconButton,icon:(0,a.jsx)(o.Pcn,{}),size:"sm"}),(0,a.jsxs)(l.MenuList,{children:[(0,a.jsx)(l.MenuItem,{icon:(0,a.jsx)(o.Pcn,{}),onClick:()=>H(e.id,!e.enabled),children:e.enabled?"Deactivate":"Activate"}),(0,a.jsx)(l.MenuItem,{icon:(0,a.jsx)(o.qbC,{}),color:"red.500",onClick:()=>M(e.id),children:"Delete"})]})]})]})]})})},e.id))})]})}),(0,a.jsx)(l.TabPanel,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.Alert,{status:"info",children:[(0,a.jsx)(l.AlertIcon,{}),(0,a.jsx)(l.Text,{children:"Pre-built templates to get you started quickly. Choose from common application types."})]}),(0,a.jsx)(l.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:4,children:(0,a.jsx)(x,{onSelectTemplate:e=>{let t={...e,id:`app-${Date.now()}`,enabled:!1};r(t),S()}})})]})}),(0,a.jsx)(l.TabPanel,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.Alert,{status:"info",children:[(0,a.jsx)(l.AlertIcon,{}),(0,a.jsx)(l.Text,{children:"Global settings for all applications. Configure defaults and system-wide preferences."})]}),(0,a.jsx)(l.Card,{children:(0,a.jsx)(l.CardBody,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsx)(l.Heading,{size:"md",children:"Global Settings"}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Default Auto-Response"}),(0,a.jsx)(l.Switch,{})]}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Require Email Verification"}),(0,a.jsx)(l.Switch,{})]}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Application Cooldown (days)"}),(0,a.jsx)(l.Input,{type:"number",defaultValue:30})]})]})})})]})})]})]})]})}),(0,a.jsxs)(l.Modal,{isOpen:q,onClose:f,size:"6xl",scrollBehavior:"inside",children:[(0,a.jsx)(l.ModalOverlay,{}),(0,a.jsxs)(l.ModalContent,{children:[(0,a.jsx)(l.ModalHeader,{children:(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Icon,{as:o.lV_,color:"blue.500"}),(0,a.jsxs)(l.Text,{children:[i?.id.startsWith("app-")?"Create":"Edit"," Application"]})]})}),(0,a.jsx)(l.ModalCloseButton,{}),(0,a.jsx)(l.ModalBody,{children:i&&(0,a.jsx)(h,{application:i,onSave:F,onCancel:f,isSaving:b})})]})]}),(0,a.jsxs)(l.Modal,{isOpen:C,onClose:v,size:"4xl",children:[(0,a.jsx)(l.ModalOverlay,{}),(0,a.jsxs)(l.ModalContent,{children:[(0,a.jsx)(l.ModalHeader,{children:(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Icon,{as:o.Ny1,color:"green.500"}),(0,a.jsxs)(l.Text,{children:["Preview: ",i?.title]})]})}),(0,a.jsx)(l.ModalCloseButton,{}),(0,a.jsx)(l.ModalBody,{children:i&&(0,a.jsx)(j,{application:i})}),(0,a.jsx)(l.ModalFooter,{children:(0,a.jsx)(l.Button,{onClick:v,children:"Close"})})]})]})]})}function h({application:e,onSave:t,onCancel:i,isSaving:r=!1}){let[n,c]=(0,s.useState)(e),[d,p]=(0,s.useState)(0),u=e=>{c(t=>({...t,...e}))},h=(e,t)=>{c(i=>({...i,questions:i.questions.map(i=>i.id===e?{...i,...t}:i)}))},x=e=>{c(t=>({...t,questions:t.questions.filter(t=>t.id!==e)}))};return(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.Tabs,{index:d,onChange:p,children:[(0,a.jsxs)(l.TabList,{children:[(0,a.jsx)(l.Tab,{children:"Basic Info"}),(0,a.jsx)(l.Tab,{children:"Questions"}),(0,a.jsx)(l.Tab,{children:"Settings"})]}),(0,a.jsxs)(l.TabPanels,{children:[(0,a.jsx)(l.TabPanel,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Application Title"}),(0,a.jsx)(l.Input,{value:n.title,onChange:e=>u({title:e.target.value}),placeholder:"Enter application title"})]}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Description"}),(0,a.jsx)(l.Textarea,{value:n.description,onChange:e=>u({description:e.target.value}),placeholder:"Enter application description"})]}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Color Scheme"}),(0,a.jsx)(l.HStack,{spacing:2,children:m.map(e=>(0,a.jsx)(l.Box,{w:8,h:8,bg:`${e}.500`,rounded:"md",cursor:"pointer",border:n.color===e?"3px solid":"1px solid",borderColor:n.color===e?"white":"gray.300",onClick:()=>u({color:e})},e))})]}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Status"}),(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Switch,{isChecked:n.enabled,onChange:e=>u({enabled:e.target.checked})}),(0,a.jsx)(l.Text,{children:n.enabled?"Active":"Inactive"})]})]})]})}),(0,a.jsx)(l.TabPanel,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.HStack,{justify:"space-between",children:[(0,a.jsxs)(l.Text,{fontWeight:"bold",children:["Questions (",n.questions.length,")"]}),(0,a.jsx)(l.Button,{size:"sm",colorScheme:"blue",leftIcon:(0,a.jsx)(o.OiG,{}),onClick:()=>{let e={id:`q-${Date.now()}`,type:"text",label:"New Question",required:!1};c(t=>({...t,questions:[...t.questions,e]}))},children:"Add Question"})]}),n.questions.map(e=>(0,a.jsx)(l.Card,{borderLeft:"4px solid",borderLeftColor:"blue.500",children:(0,a.jsx)(l.CardBody,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:3,children:[(0,a.jsxs)(l.HStack,{justify:"space-between",children:[(0,a.jsxs)(l.FormControl,{flex:1,children:[(0,a.jsx)(l.FormLabel,{children:"Question Label"}),(0,a.jsx)(l.Input,{value:e.label,onChange:t=>h(e.id,{label:t.target.value}),placeholder:"Enter question label"})]}),(0,a.jsxs)(l.FormControl,{w:"200px",children:[(0,a.jsx)(l.FormLabel,{children:"Type"}),(0,a.jsx)(l.Select,{value:e.type,onChange:t=>h(e.id,{type:t.target.value}),children:b.map(e=>(0,a.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,a.jsx)(l.IconButton,{"aria-label":"Delete question",icon:(0,a.jsx)(o.qbC,{}),colorScheme:"red",size:"sm",onClick:()=>x(e.id)})]}),(0,a.jsxs)(l.FormControl,{children:[(0,a.jsx)(l.FormLabel,{children:"Placeholder"}),(0,a.jsx)(l.Input,{value:e.placeholder||"",onChange:t=>h(e.id,{placeholder:t.target.value}),placeholder:"Enter placeholder text"})]}),(0,a.jsx)(l.HStack,{children:(0,a.jsx)(l.FormControl,{children:(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Switch,{isChecked:e.required,onChange:t=>h(e.id,{required:t.target.checked})}),(0,a.jsx)(l.Text,{children:"Required"})]})})})]})})},e.id))]})}),(0,a.jsx)(l.TabPanel,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsx)(l.FormControl,{children:(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Switch,{isChecked:n.settings.allowMultipleSubmissions,onChange:e=>u({settings:{...n.settings,allowMultipleSubmissions:e.target.checked}})}),(0,a.jsx)(l.Text,{children:"Allow Multiple Submissions"})]})}),(0,a.jsx)(l.FormControl,{children:(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Switch,{isChecked:n.settings.requireApproval,onChange:e=>u({settings:{...n.settings,requireApproval:e.target.checked}})}),(0,a.jsx)(l.Text,{children:"Require Manual Approval"})]})}),(0,a.jsx)(l.FormControl,{children:(0,a.jsxs)(l.HStack,{children:[(0,a.jsx)(l.Switch,{isChecked:n.settings.autoResponse,onChange:e=>u({settings:{...n.settings,autoResponse:e.target.checked}})}),(0,a.jsx)(l.Text,{children:"Send Auto-Response"})]})})]})})]})]}),(0,a.jsxs)(l.HStack,{justify:"flex-end",spacing:4,children:[(0,a.jsx)(l.Button,{onClick:i,isDisabled:r,children:"Cancel"}),(0,a.jsx)(l.Button,{colorScheme:"blue",onClick:()=>t(n),isLoading:r,loadingText:"Saving...",children:"Save Application"})]})]})}function x({onSelectTemplate:e}){return(0,a.jsx)(a.Fragment,{children:[{id:"template-moderator",title:"Moderator Application",description:"Standard moderator application with experience and scenario questions",color:"blue",icon:"FaUserShield",enabled:!1,questions:[{id:"q1",type:"text",label:"What is your Discord username?",required:!0},{id:"q2",type:"number",label:"How old are you?",required:!0},{id:"q3",type:"text",label:"What timezone are you in?",required:!0},{id:"q4",type:"number",label:"How many hours per week can you dedicate to moderation?",required:!0},{id:"q5",type:"textarea",label:"Why do you want to be a moderator?",required:!0},{id:"q6",type:"textarea",label:"Do you have any previous moderation experience?",required:!1},{id:"q7",type:"textarea",label:"How would you handle a heated argument between two members?",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-developer",title:"Developer Application",description:"Technical application for developer positions",color:"green",icon:"FaCode",enabled:!1,questions:[{id:"q1",type:"text",label:"Full Name",required:!0},{id:"q2",type:"email",label:"Email Address",required:!0},{id:"q3",type:"textarea",label:"Tell us about your programming experience",required:!0},{id:"q4",type:"select",label:"Primary Programming Language",required:!0,options:["JavaScript","Python","Java","C#","Go","Other"]},{id:"q5",type:"textarea",label:"Describe a challenging project you worked on",required:!0},{id:"q6",type:"text",label:"GitHub/Portfolio URL",required:!1},{id:"q7",type:"radio",label:"Are you available for full-time work?",required:!0,options:["Yes","No","Part-time only"]}],settings:{allowMultipleSubmissions:!0,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-event-host",title:"Event Host Application",description:"Application for community event organizers",color:"purple",icon:"FaCalendar",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"text",label:"Preferred Name",required:!0},{id:"q3",type:"textarea",label:"What types of events would you like to host?",required:!0},{id:"q4",type:"textarea",label:"Do you have experience organizing events?",required:!1},{id:"q5",type:"select",label:"How often would you like to host events?",required:!0,options:["Weekly","Bi-weekly","Monthly","As needed"]},{id:"q6",type:"textarea",label:"Describe an event idea you have",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-support",title:"Support Team Application",description:"Customer support and help desk application",color:"orange",icon:"FaHeadset",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"text",label:"Age",required:!0},{id:"q3",type:"text",label:"Timezone",required:!0},{id:"q4",type:"textarea",label:"Why do you want to join the support team?",required:!0},{id:"q5",type:"checkbox",label:"Which areas can you help with?",required:!0,options:["Technical Issues","Account Problems","General Questions","Bug Reports","Feature Requests"]},{id:"q6",type:"textarea",label:"How would you help a frustrated user?",required:!0}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-content",title:"Content Creator Application",description:"Application for content creators and influencers",color:"pink",icon:"FaVideo",enabled:!1,questions:[{id:"q1",type:"text",label:"Creator Name/Handle",required:!0},{id:"q2",type:"email",label:"Contact Email",required:!0},{id:"q3",type:"select",label:"Primary Content Platform",required:!0,options:["YouTube","Twitch","TikTok","Instagram","Twitter","Other"]},{id:"q4",type:"text",label:"Channel/Profile URL",required:!0},{id:"q5",type:"textarea",label:"What type of content do you create?",required:!0},{id:"q6",type:"number",label:"How many followers/subscribers do you have?",required:!1},{id:"q7",type:"textarea",label:"How would you promote our community?",required:!0}],settings:{allowMultipleSubmissions:!0,requireApproval:!0,autoResponse:!0,notificationChannels:[]}},{id:"template-beta",title:"Beta Tester Application",description:"Application for beta testing programs",color:"teal",icon:"FaFlask",enabled:!1,questions:[{id:"q1",type:"text",label:"Discord Username",required:!0},{id:"q2",type:"textarea",label:"What interests you about beta testing?",required:!0},{id:"q3",type:"textarea",label:"Do you have experience finding and reporting bugs?",required:!1},{id:"q4",type:"select",label:"How much time can you dedicate to testing?",required:!0,options:["1-2 hours/week","3-5 hours/week","6-10 hours/week","10+ hours/week"]},{id:"q5",type:"checkbox",label:"Which platforms do you have access to?",required:!0,options:["Windows","Mac","Linux","iOS","Android","Web Browser"]}],settings:{allowMultipleSubmissions:!1,requireApproval:!0,autoResponse:!0,notificationChannels:[]}}].map(t=>(0,a.jsx)(l.Card,{borderTop:"4px solid",borderTopColor:`${t.color}.500`,children:(0,a.jsx)(l.CardBody,{children:(0,a.jsxs)(l.VStack,{align:"stretch",spacing:3,children:[(0,a.jsxs)(l.VStack,{align:"start",spacing:1,children:[(0,a.jsx)(l.Heading,{size:"md",children:t.title}),(0,a.jsx)(l.Text,{fontSize:"sm",color:"gray.600",_dark:{color:"gray.400"},children:t.description})]}),(0,a.jsxs)(l.Text,{fontSize:"sm",color:"gray.500",children:[t.questions.length," pre-configured questions"]}),(0,a.jsx)(l.Button,{size:"sm",colorScheme:t.color,onClick:()=>e(t),leftIcon:(0,a.jsx)(o.uoG,{}),children:"Use Template"})]})})},t.id))})}function j({application:e}){return(0,a.jsxs)(l.VStack,{align:"stretch",spacing:4,children:[(0,a.jsxs)(l.VStack,{align:"center",spacing:2,children:[(0,a.jsx)(l.Heading,{size:"lg",children:e.title}),(0,a.jsx)(l.Text,{color:"gray.600",_dark:{color:"gray.400"},children:e.description})]}),(0,a.jsx)(l.Divider,{}),(0,a.jsx)(l.VStack,{align:"stretch",spacing:4,children:e.questions.map(e=>(0,a.jsxs)(l.FormControl,{isRequired:e.required,children:[(0,a.jsx)(l.FormLabel,{children:e.label}),"text"===e.type&&(0,a.jsx)(l.Input,{placeholder:e.placeholder}),"textarea"===e.type&&(0,a.jsx)(l.Textarea,{placeholder:e.placeholder}),"select"===e.type&&(0,a.jsx)(l.Select,{placeholder:e.placeholder,children:e.options?.map(e=>(0,a.jsx)("option",{value:e,children:e},e))}),"number"===e.type&&(0,a.jsx)(l.Input,{type:"number",placeholder:e.placeholder}),"email"===e.type&&(0,a.jsx)(l.Input,{type:"email",placeholder:e.placeholder})]},e.id))}),(0,a.jsx)(l.Button,{colorScheme:e.color,size:"lg",isDisabled:!0,children:"Submit Application (Preview)"})]})}r()}catch(e){r(e)}})},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9018:(e,t,i)=>{i.a(e,async(e,r)=>{try{i.r(t),i.d(t,{config:()=>j,default:()=>p,getServerSideProps:()=>x,getStaticPaths:()=>h,getStaticProps:()=>u,reportWebVitals:()=>b,routeModule:()=>f,unstable_getServerProps:()=>q,unstable_getServerSideProps:()=>S,unstable_getStaticParams:()=>y,unstable_getStaticPaths:()=>g,unstable_getStaticProps:()=>m});var a=i(1292),s=i(8834),l=i(786),o=i(3567),n=i(8077),c=i(6536),d=e([n,c]);[n,c]=d.then?(await d)():d;let p=(0,l.M)(c,"default"),u=(0,l.M)(c,"getStaticProps"),h=(0,l.M)(c,"getStaticPaths"),x=(0,l.M)(c,"getServerSideProps"),j=(0,l.M)(c,"config"),b=(0,l.M)(c,"reportWebVitals"),m=(0,l.M)(c,"unstable_getStaticProps"),g=(0,l.M)(c,"unstable_getStaticPaths"),y=(0,l.M)(c,"unstable_getStaticParams"),q=(0,l.M)(c,"unstable_getServerProps"),S=(0,l.M)(c,"unstable_getServerSideProps"),f=new a.PagesRouteModule({definition:{kind:s.A.PAGES,page:"/admin/applications-builder",pathname:"/admin/applications-builder",bundlePath:"",filename:""},components:{App:n.default,Document:o.default},userland:c});r()}catch(e){r(e)}})},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var t=require("../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[8270,4874,752,6281,2695,5333],()=>i(9018));module.exports=r})();