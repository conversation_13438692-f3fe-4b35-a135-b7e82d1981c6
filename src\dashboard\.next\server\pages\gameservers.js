"use strict";(()=>{var e={};e.id=6934,e.ids=[6934],e.modules={261:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.r(r),o.d(r,{default:()=>b,getServerSideProps:()=>m});var s=o(8732),a=o(2015),i=o.n(a),l=o(9733),n=o(2695),d=o(1011),c=o(2390),h=o(468),p=o(5806),u=o(3762),g=o(4722),x=e([l,d,c,h]);function b(){let{data:e}=(0,g.useSession)(),r=e?.user?.isAdmin===!0,[o,t]=(0,a.useState)([]),[p,u]=(0,a.useState)(!0),[x,b]=(0,a.useState)(!1),[m,S]=(0,a.useState)(),[j,v]=(0,a.useState)(),{isOpen:f,onOpen:y,onClose:w}=(0,l.useDisclosure)(),{isOpen:C,onOpen:k,onClose:_}=(0,l.useDisclosure)(),P=i().useRef(null),A=(0,l.useToast)(),I=async()=>{try{b(!0);let e=await fetch("/api/gameservers/query");if(!e.ok)throw Error("Failed to fetch server status");let r=await e.json(),o=Array.isArray(r)?r:[];t(o)}catch(e){t([]),A({title:"Error",description:e instanceof Error?e.message:"Failed to fetch server status",status:"error",duration:5e3,isClosable:!0})}finally{u(!1),b(!1)}},T=async e=>{try{let r,o=await fetch(`/api/gameservers/games?type=${encodeURIComponent(e.type)}`);if(!o.ok)throw Error("Invalid game type");let{type:t}=await o.json();e.type=t;let s=e._id?"PUT":"POST";if("PUT"===s){let{_id:o,...t}=e;r={id:o,...t}}else{let{_id:o,...t}=e;r=t}let a=await fetch("/api/gameservers/manage",{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),i=await a.json();if(!a.ok)throw Error(i.error||"Failed to save server");I(),A({title:e._id?"Server Updated":"Server Added",description:e._id?"The server has been updated successfully":"The server has been added successfully",status:"success",duration:3e3,isClosable:!0})}catch(e){throw A({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0}),e}},$=e=>{S({_id:e._id,name:e.name,type:e.type,host:e.host,port:e.port,description:e.description,hasPassword:e.hasPassword,password:e.password}),y()},F=e=>{S(e),k()},M=async()=>{if(m)try{let e={id:m._id},r=await fetch("/api/gameservers/manage",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),o=await r.json();if(!r.ok)throw Error(o.error||"Failed to delete server");t(e=>e.filter(e=>e._id!==m._id)),S(void 0),_(),A({title:"Success",description:"Server deleted successfully",status:"success",duration:5e3,isClosable:!0})}catch(e){A({title:"Error",description:e.message||"Failed to delete server",status:"error",duration:5e3,isClosable:!0})}};return(0,s.jsx)(d.A,{children:(0,s.jsxs)(l.Box,{w:"full",p:4,children:[(0,s.jsxs)(l.Box,{maxW:"4xl",mx:"auto",mb:8,mt:8,bg:"rgba(255,255,255,0.08)",p:8,rounded:"2xl",backdropFilter:"blur(10px)",border:"2px solid",borderColor:"green.400",boxShadow:"0 0 15px rgba(72, 187, 120, 0.4)",textAlign:"center",children:[(0,s.jsx)(l.Heading,{size:"2xl",bgGradient:"linear(to-r, green.300, teal.400)",bgClip:"text",mb:4,children:"Game Servers"}),(0,s.jsx)(l.Text,{color:"gray.300",fontSize:"lg",mb:6,children:"Monitor and manage your game servers in real-time"}),(0,s.jsxs)(l.HStack,{spacing:4,justify:"center",children:[r&&(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(n.OiG,{}),colorScheme:"green",onClick:()=>{S(void 0),y()},size:"md",variant:"solid",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Server"}),(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(n.DIg,{}),onClick:I,isLoading:x,loadingText:"Refreshing",size:"md",variant:"outline",colorScheme:"green",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Refresh Status"})]})]}),(0,s.jsx)(l.Box,{maxW:"7xl",mx:"auto",children:p?(0,s.jsxs)(l.VStack,{py:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(l.Spinner,{size:"xl",color:"green.400"}),(0,s.jsx)(l.Text,{color:"gray.400",children:"Loading servers..."})]}):o&&0!==o.length?(0,s.jsx)(l.SimpleGrid,{columns:{base:1,md:2,lg:3},spacing:6,children:(o||[]).map((e,o)=>(0,s.jsxs)(l.Box,{position:"relative",transition:"all 0.2s",_hover:r?{transform:"translateY(-4px)","& > .server-actions":{opacity:1,transform:"translateY(0)"}}:void 0,children:[(0,s.jsx)(c.e,{server:e}),r&&(0,s.jsxs)(l.HStack,{className:"server-actions",position:"absolute",top:2,right:2,spacing:1,bg:"blackAlpha.800",p:1,borderRadius:"md",opacity:0,transform:"translateY(-4px)",transition:"all 0.2s",zIndex:2,backdropFilter:"blur(8px)",children:[(0,s.jsx)(l.IconButton,{"aria-label":"Edit server",icon:(0,s.jsx)(n.uO9,{}),size:"sm",variant:"ghost",colorScheme:"green",onClick:()=>$(e),_hover:{bg:"green.700"}}),(0,s.jsx)(l.IconButton,{"aria-label":"Delete server",icon:(0,s.jsx)(n.qbC,{}),size:"sm",variant:"ghost",colorScheme:"red",onClick:()=>F(e),_hover:{bg:"red.700"}})]})]},`${e._id||`${e.host}:${e.port}-${o}`}`))}):(0,s.jsxs)(l.VStack,{spacing:4,p:8,bg:"rgba(255,255,255,0.08)",rounded:"2xl",backdropFilter:"blur(10px)",border:"1px solid",borderColor:"whiteAlpha.200",textAlign:"center",children:[(0,s.jsx)(l.Icon,{as:n.pBr,boxSize:12,color:"green.400"}),(0,s.jsx)(l.Text,{color:"gray.300",fontSize:"lg",children:"No game servers found"}),(0,s.jsx)(l.Text,{fontSize:"md",color:"gray.500",children:"Add your first game server to start monitoring"}),r&&(0,s.jsx)(l.Button,{leftIcon:(0,s.jsx)(n.OiG,{}),colorScheme:"green",onClick:()=>{S(void 0),y()},size:"md",variant:"outline",_hover:{transform:"translateY(-2px)",shadow:"lg"},children:"Add Your First Server"})]})}),r&&(0,s.jsx)(h._,{isOpen:f,onClose:w,server:m,onSave:T}),(0,s.jsx)(l.AlertDialog,{isOpen:C,leastDestructiveRef:P,onClose:_,isCentered:!0,children:(0,s.jsx)(l.AlertDialogOverlay,{backdropFilter:"blur(10px)",children:(0,s.jsxs)(l.AlertDialogContent,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(l.AlertDialogHeader,{fontSize:"lg",fontWeight:"bold",color:"white",children:"Delete Server"}),(0,s.jsxs)(l.AlertDialogBody,{color:"gray.300",children:["Are you sure you want to delete ",m?.name||`${m?.host}:${m?.port}`,"? This action cannot be undone."]}),(0,s.jsxs)(l.AlertDialogFooter,{gap:3,children:[(0,s.jsx)(l.Button,{ref:P,onClick:_,variant:"ghost",color:"gray.300",children:"Cancel"}),(0,s.jsx)(l.Button,{colorScheme:"red",onClick:M,_hover:{bg:"red.600"},_active:{bg:"red.700"},children:"Delete"})]})]})})})]})})}[l,d,c,h]=x.then?(await x)():x;let m=async e=>await (0,p.getServerSession)(e.req,e.res,u.N)?{props:{}}:{redirect:{destination:"/signin",permanent:!1}};t()}catch(e){t(e)}})},361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},468:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{_:()=>n});var s=o(8732),a=o(2015),i=o(9733),l=e([i]);function n({isOpen:e,onClose:r,onSave:o,server:t}){let[l,n]=(0,a.useState)(t?.name||""),[d,c]=(0,a.useState)(t?.description||""),[h,p]=(0,a.useState)(t?.host||""),[u,g]=(0,a.useState)(t?.port||25565),[x,b]=(0,a.useState)(t?.type||""),[m,S]=(0,a.useState)(t?.hasPassword||!1),[j,v]=(0,a.useState)(t?.password||""),[f,y]=(0,a.useState)(""),[w,C]=(0,a.useState)([]),[k,_]=(0,a.useState)(!1),[P,A]=(0,a.useState)(!1),I=(0,i.useToast)(),T=async()=>{if(!l||!h||!u||!x)return void I({title:"Error",description:"Please fill in all required fields",status:"error",duration:5e3,isClosable:!0});if(m&&!j)return void I({title:"Error",description:"Password is required when password protection is enabled",status:"error",duration:5e3,isClosable:!0});try{let e={_id:t?._id,name:l,description:d,host:h,port:Number(u),type:x,hasPassword:m,password:m?j:void 0};await o(e),r()}catch(e){I({title:"Error",description:e instanceof Error?e.message:"Failed to save server",status:"error",duration:5e3,isClosable:!0})}},$=e=>{b(e.id),y(e.name),A(!1)};return(0,s.jsxs)(i.Modal,{isOpen:e,onClose:r,size:"lg",children:[(0,s.jsx)(i.ModalOverlay,{backdropFilter:"blur(10px)"}),(0,s.jsxs)(i.ModalContent,{bg:"gray.800",border:"1px",borderColor:"whiteAlpha.200",children:[(0,s.jsx)(i.ModalHeader,{color:"white",children:t?"Edit Server":"Add Server"}),(0,s.jsx)(i.ModalCloseButton,{}),(0,s.jsx)(i.ModalBody,{children:(0,s.jsxs)(i.VStack,{spacing:6,children:[(0,s.jsxs)(i.FormControl,{isRequired:!0,children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",children:"Server Name"}),(0,s.jsx)(i.Input,{value:l,onChange:e=>n(e.target.value),placeholder:"My Game Server",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(i.FormControl,{children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",children:"Description"}),(0,s.jsx)(i.Input,{value:d,onChange:e=>c(e.target.value),placeholder:"Optional description",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(i.FormControl,{isRequired:!0,children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",children:"Game Type"}),(0,s.jsx)(i.Input,{value:f,onChange:e=>{y(e.target.value),b("")},placeholder:"Search for a game...",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"},onFocus:()=>A(!0)}),k&&(0,s.jsx)(i.Flex,{justify:"center",mt:2,children:(0,s.jsx)(i.Spinner,{size:"sm",color:"blue.300"})}),P&&w.length>0&&(0,s.jsx)(i.Box,{mt:2,border:"1px",borderColor:"whiteAlpha.200",borderRadius:"md",maxH:"200px",overflowY:"auto",bg:"gray.700",children:(0,s.jsx)(i.List,{spacing:0,children:(w||[]).map(e=>(0,s.jsx)(i.ListItem,{p:2,cursor:"pointer",_hover:{bg:"whiteAlpha.100"},onClick:()=>$(e),color:"gray.200",children:(0,s.jsx)(i.Text,{children:e.name})},e.id))})}),x&&(0,s.jsxs)(i.Text,{mt:1,fontSize:"sm",color:"blue.300",children:["Selected: ",w.find(e=>e.id===x)?.name||x]})]}),(0,s.jsxs)(i.FormControl,{isRequired:!0,children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",children:"Host"}),(0,s.jsx)(i.Input,{value:h,onChange:e=>p(e.target.value),placeholder:"localhost or IP address",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]}),(0,s.jsxs)(i.FormControl,{isRequired:!0,children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",children:"Port"}),(0,s.jsx)(i.NumberInput,{value:u,min:1,max:65535,children:(0,s.jsx)(i.NumberInputField,{onChange:e=>g(parseInt(e.target.value)||25565),bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})})]}),(0,s.jsx)(i.FormControl,{children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",mb:"0",children:"Password Protected"}),(0,s.jsx)(i.Switch,{colorScheme:"blue",isChecked:m,onChange:e=>S(e.target.checked)})]})}),m&&(0,s.jsxs)(i.FormControl,{isRequired:!0,children:[(0,s.jsx)(i.FormLabel,{color:"gray.200",children:"Server Password"}),(0,s.jsx)(i.Input,{value:j,onChange:e=>v(e.target.value),placeholder:"Enter server password",bg:"gray.700",border:"1px",borderColor:"whiteAlpha.300",_hover:{borderColor:"whiteAlpha.400"},_focus:{borderColor:"blue.300",boxShadow:"0 0 0 1px var(--chakra-colors-blue-300)"}})]})]})}),(0,s.jsxs)(i.ModalFooter,{gap:3,children:[(0,s.jsx)(i.Button,{variant:"ghost",onClick:r,color:"gray.300",_hover:{bg:"whiteAlpha.100"},children:"Cancel"}),(0,s.jsx)(i.Button,{colorScheme:"blue",onClick:T,_hover:{bg:"blue.500"},_active:{bg:"blue.600"},children:t?"Save Changes":"Add Server"})]})]})]})}i=(l.then?(await l)():l)[0],t()}catch(e){t(e)}})},2015:e=>{e.exports=require("react")},2115:e=>{e.exports=require("yaml")},2326:e=>{e.exports=require("react-dom")},2390:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.d(r,{e:()=>c});var s=o(8732),a=o(2015),i=o(9733),l=o(2695),n=e([i]);i=(n.then?(await n)():n)[0];let d={minecraft:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},minecraftbe:{color:"green",gradient:{from:"rgba(72, 187, 120, 0.4)",to:"rgba(72, 187, 120, 0.1)"},accent:"#68D391"},csgo:{color:"orange",gradient:{from:"rgba(237, 137, 54, 0.4)",to:"rgba(237, 137, 54, 0.1)"},accent:"#ED8936"},valheim:{color:"red",gradient:{from:"rgba(245, 101, 101, 0.4)",to:"rgba(245, 101, 101, 0.1)"},accent:"#F56565"},rust:{color:"brown",gradient:{from:"rgba(193, 105, 79, 0.4)",to:"rgba(193, 105, 79, 0.1)"},accent:"#C1694F"},arkse:{color:"purple",gradient:{from:"rgba(159, 122, 234, 0.4)",to:"rgba(159, 122, 234, 0.1)"},accent:"#9F7AEA"},sdtd:{color:"yellow",gradient:{from:"rgba(236, 201, 75, 0.4)",to:"rgba(236, 201, 75, 0.1)"},accent:"#ECC94B"},default:{color:"blue",gradient:{from:"rgba(66, 153, 225, 0.4)",to:"rgba(66, 153, 225, 0.1)"},accent:"#4299E1"}},c=({server:e})=>{let[r,o]=(0,a.useState)(!1),{hasCopied:t,onCopy:n}=(0,i.useClipboard)(e.hasPassword?`Server: ${e.host}:${e.port}
Password: ${e.password}`:`${e.host}:${e.port}`),c=d[e.type.toLowerCase()]||d.default;(0,i.useColorModeValue)("gray.800","gray.800");let h=e.online?`${c.color}.400`:"red.400",p=e.online?c.color:"red",u=e.players?.length||0;return(0,s.jsx)(i.Box,{p:5,bg:`linear-gradient(135deg, ${c.gradient.from}, ${c.gradient.to})`,borderRadius:"xl",border:"2px",borderColor:h,width:"100%",position:"relative",overflow:"hidden",zIndex:1,transition:"all 0.2s",_before:{content:'""',position:"absolute",top:0,left:0,right:0,bottom:0,bg:"linear-gradient(45deg, rgba(0,0,0,0.1) 0%, rgba(255,255,255,0.05) 100%)",zIndex:-1},_hover:{transform:"translateY(-2px)",boxShadow:`0 8px 20px ${c.gradient.from}`,borderColor:e.online?`${c.color}.300`:"red.300"},children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:4,children:[(0,s.jsx)(i.HStack,{justify:"space-between",children:(0,s.jsxs)(i.VStack,{align:"start",spacing:1,children:[(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(i.Text,{fontSize:"xl",fontWeight:"bold",color:"white",children:e.name||`${e.host}:${e.port}`}),e.hasPassword&&(0,s.jsx)(i.Tooltip,{label:"Password Protected",children:(0,s.jsx)("span",{children:(0,s.jsx)(i.Icon,{as:l.JhU,color:`${c.color}.200`})})})]}),(0,s.jsxs)(i.HStack,{spacing:2,children:[(0,s.jsx)(i.Badge,{colorScheme:p,fontSize:"sm",children:e.online?"Online":"Offline"}),(0,s.jsx)(i.Badge,{colorScheme:c.color,fontSize:"sm",children:e.type.toUpperCase()})]})]})}),e.description&&(0,s.jsx)(i.Text,{color:"gray.300",fontSize:"sm",children:e.description}),e.online?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i.HStack,{spacing:8,justify:"space-around",children:[(0,s.jsxs)(i.Stat,{children:[(0,s.jsx)(i.StatLabel,{color:"gray.400",children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(l.x$1,{}),(0,s.jsx)(i.Text,{children:"Players"})]})}),(0,s.jsxs)(i.StatNumber,{color:"white",children:[u,"/",e.maxPlayers||"?"]})]}),e.map&&(0,s.jsxs)(i.Stat,{children:[(0,s.jsx)(i.StatLabel,{color:"gray.400",children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsx)(l.pBr,{}),(0,s.jsx)(i.Text,{children:"Map"})]})}),(0,s.jsx)(i.StatNumber,{color:"white",fontSize:"lg",children:e.map})]}),e.ping&&(0,s.jsxs)(i.Stat,{children:[(0,s.jsx)(i.StatLabel,{color:"gray.400",children:"Ping"}),(0,s.jsxs)(i.StatNumber,{color:e.ping<100?`${c.color}.400`:e.ping<200?"yellow.400":"red.400",children:[e.ping,"ms"]})]})]}),(0,s.jsx)(i.Button,{size:"sm",variant:"ghost",colorScheme:c.color,onClick:()=>o(!r),leftIcon:(0,s.jsx)(l.__w,{}),children:r?"Hide Details":"Show Details"}),(0,s.jsx)(i.Collapse,{in:r,children:(0,s.jsxs)(i.VStack,{align:"stretch",spacing:3,pt:2,children:[(0,s.jsx)(i.Divider,{borderColor:"whiteAlpha.200"}),(0,s.jsxs)(i.Box,{children:[(0,s.jsx)(i.Text,{color:"gray.400",mb:2,fontWeight:"bold",children:"Connection Information"}),(0,s.jsx)(i.VStack,{align:"stretch",spacing:2,children:(0,s.jsxs)(i.HStack,{children:[(0,s.jsxs)(i.Code,{p:2,borderRadius:"md",bg:"gray.700",color:`${c.color}.300`,children:[e.host,":",e.port]}),e.hasPassword&&(0,s.jsxs)(i.Code,{p:2,borderRadius:"md",bg:"gray.700",color:`${c.color}.300`,children:["Password: ",e.password]}),(0,s.jsx)(i.Tooltip,{label:t?"Copied!":"Copy All",children:(0,s.jsx)(i.Button,{size:"sm",variant:"ghost",colorScheme:t?"green":c.color,onClick:n,children:(0,s.jsx)(i.Icon,{as:t?l.CMH:l.paH})})})]})})]}),(0,s.jsxs)(i.Box,{children:[(0,s.jsx)(i.Text,{color:"gray.400",mb:2,fontWeight:"bold",children:"How to Connect"}),(0,s.jsx)(i.Code,{display:"block",whiteSpace:"pre",p:3,borderRadius:"md",bg:"gray.700",color:`${c.color}.300`,children:(()=>{switch(e.type.toLowerCase()){case"minecraft":case"minecraftbe":return`1. Open Minecraft
2. Click "Multiplayer"
3. Click "Add Server"
4. Enter server address: ${e.host}:${e.port}${e.hasPassword?`
5. Enter Password: ${e.password}`:""}`;case"sdtd":return`1. Open 7 Days to Die
2. Click "Join Game"
3. Click "Server Browser"
4. Search for "${e.name}"
${e.hasPassword?`5. Enter Password: ${e.password}`:""}`;default:return`Connect using: ${e.host}:${e.port}${e.hasPassword?`
Password: ${e.password}`:""}`}})()})]})]})})]}):(0,s.jsx)(i.Text,{color:"red.400",children:e.error||"Server is offline"}),(0,s.jsxs)(i.HStack,{fontSize:"sm",color:"gray.500",spacing:2,children:[(0,s.jsx)(l.w_X,{}),(0,s.jsxs)(i.Text,{children:["Last updated: ",new Date(e.lastUpdated).toLocaleTimeString()]})]})]})})};t()}catch(e){t(e)}})},3762:(e,r,o)=>{o.d(r,{N:()=>u});var t=o(5542),s=o.n(t);let a=require("next-auth/providers/discord");var i=o.n(a),l=o(9021),n=o(2115),d=o.n(n),c=o(3873);let h={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>c.resolve(process.cwd(),e)).find(e=>l.existsSync(e));if(!e){let r=c.resolve(__dirname,"../../../config.yml");l.existsSync(r)&&(e=r)}if(!e)throw Error("config.yml not found");let r=l.readFileSync(e,"utf8");h=d().parse(r)}catch(e){process.exit(1)}let p={bot:{token:h.bot.token,clientId:h.bot.clientId,clientSecret:h.bot.clientSecret,guildId:h.bot.guildId,ticketCategoryId:h.bot.ticketCategoryId||null,ticketLogChannelId:h.bot.ticketLogChannelId||null,prefix:h.bot.prefix},dashboard:{admins:h.dashboard?.admins||[],adminRoleIds:h.dashboard?.adminRoleIds||[],session:{secret:h.dashboard?.session?.secret||h.bot.clientSecret}},database:{url:h.database.url,name:h.database.name,options:{maxPoolSize:h.database.options?.maxPoolSize||10,minPoolSize:h.database.options?.minPoolSize||1,maxIdleTimeMS:h.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:h.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:h.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:h.database.options?.connectTimeoutMS||1e4,retryWrites:h.database.options?.retryWrites!==!1,retryReads:h.database.options?.retryReads!==!1}}};p.bot.token||process.exit(1),p.bot.clientId&&p.bot.clientSecret||process.exit(1),p.bot.guildId||process.exit(1),p.database.url&&p.database.name||process.exit(1);let u={providers:[i()({clientId:p.bot.clientId,clientSecret:p.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:r,profile:o})=>(r&&o&&(e.accessToken=r.access_token||null,e.id=o.id||null),e),async session({session:e,token:r}){if(e?.user){let o=r.id||null,t=r.accessToken||null;e.user.id=o,e.user.accessToken=t;let s=!1;if(o)if((p.dashboard.admins||[]).includes(o))s=!0;else{let e=p.dashboard.adminRoleIds||[];if(e.length&&p.bot.token&&p.bot.guildId)try{let r=await fetch(`https://discord.com/api/v10/guilds/${p.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${p.bot.token}`}});if(r.ok){let o=await r.json();s=e.some(e=>o.roles?.includes(e))||!1}else await r.text()}catch(e){}}e.user.isAdmin=s,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:r}){let o=new URL(r),t=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(r)||e.startsWith(t)?e:r}},secret:p.dashboard.session.secret||p.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,r)=>{},warn:e=>{},debug:(e,r)=>{}}};s()(u)},3873:e=>{e.exports=require("path")},4075:e=>{e.exports=require("zlib")},4078:e=>{e.exports=import("swr")},4722:e=>{e.exports=require("next-auth/react")},5542:e=>{e.exports=require("next-auth")},5806:e=>{e.exports=require("next-auth/next")},6532:(e,r,o)=>{o.a(e,async(e,t)=>{try{o.r(r),o.d(r,{config:()=>x,default:()=>h,getServerSideProps:()=>g,getStaticPaths:()=>u,getStaticProps:()=>p,reportWebVitals:()=>b,routeModule:()=>y,unstable_getServerProps:()=>v,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>S,unstable_getStaticProps:()=>m});var s=o(1292),a=o(8834),i=o(786),l=o(3567),n=o(8077),d=o(261),c=e([n,d]);[n,d]=c.then?(await c)():c;let h=(0,i.M)(d,"default"),p=(0,i.M)(d,"getStaticProps"),u=(0,i.M)(d,"getStaticPaths"),g=(0,i.M)(d,"getServerSideProps"),x=(0,i.M)(d,"config"),b=(0,i.M)(d,"reportWebVitals"),m=(0,i.M)(d,"unstable_getStaticProps"),S=(0,i.M)(d,"unstable_getStaticPaths"),j=(0,i.M)(d,"unstable_getStaticParams"),v=(0,i.M)(d,"unstable_getServerProps"),f=(0,i.M)(d,"unstable_getServerSideProps"),y=new s.PagesRouteModule({definition:{kind:a.A.PAGES,page:"/gameservers",pathname:"/gameservers",bundlePath:"",filename:""},components:{App:n.default,Document:l.default},userland:d});t()}catch(e){t(e)}})},7910:e=>{e.exports=require("stream")},8732:e=>{e.exports=require("react/jsx-runtime")},9021:e=>{e.exports=require("fs")},9733:e=>{e.exports=import("@chakra-ui/react")}};var r=require("../webpack-runtime.js");r.C(e);var o=e=>r(r.s=e),t=r.X(0,[8270,4874,752,6281,2695,5333],()=>o(6532));module.exports=t})();