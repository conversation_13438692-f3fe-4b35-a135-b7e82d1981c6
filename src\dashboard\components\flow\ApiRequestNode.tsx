import React, { useState, memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import {
  Box,
  Text,
  VStack,
  HStack,
  Select,
  Input,
  Textarea,
  Button,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalCloseButton,
  ModalFooter,
  FormControl,
  FormLabel,
  useDisclosure,
  IconButton,
  Badge,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionPanel,
  AccordionIcon,
  Switch,
  SimpleGrid,
  Alert,
  AlertIcon,
  AlertDescription,
  Code,
  Collapse,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Divider,
  useToast,
  Spinner,
  Tooltip,
  NumberInput,
  NumberInputField,
  NumberInputStepper,
  NumberIncrementStepper,
  NumberDecrementStepper,
  Wrap,
  WrapItem,
} from '@chakra-ui/react';
import { FiSettings, FiGlobe, FiPlus, FiTrash2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Fi<PERSON> } from 'react-icons/fi';
import { useTheme } from '../../contexts/ThemeContext';

interface ApiRequestNodeData {
  label: string;
  url?: string;
  method?: string;
  headers?: Array<{ key: string; value: string }>;
  body?: string;
  bodyType?: 'json' | 'form' | 'text' | 'xml';
  timeout?: number;
  saveToVariable?: string;
  errorHandling?: 'ignore' | 'log' | 'throw' | 'retry';
  description?: string;
  retryCount?: number;
  retryDelay?: number;
  followRedirects?: boolean;
  validateSSL?: boolean;
}

// Available variables for API requests
const apiVariables = {
  user: [
    { name: '{user.id}', description: 'User ID for authentication', icon: '🆔' },
    { name: '{user.username}', description: 'Username for requests', icon: '👤' },
    { name: '{user.token}', description: 'User auth token', icon: '🔑' },
    { name: '{user.email}', description: 'User email address', icon: '📧' },
  ],
  server: [
    { name: '{server.id}', description: 'Server ID', icon: '🏠' },
    { name: '{server.name}', description: 'Server name', icon: '📝' },
    { name: '{server.memberCount}', description: 'Member count', icon: '👥' },
    { name: '{server.region}', description: 'Server region', icon: '🌍' },
  ],
  message: [
    { name: '{message.id}', description: 'Message ID', icon: '💬' },
    { name: '{message.content}', description: 'Message content', icon: '📝' },
    { name: '{message.channelId}', description: 'Channel ID', icon: '📺' },
    { name: '{message.authorId}', description: 'Author ID', icon: '👤' },
  ],
  response: [
    { name: '{response.data}', description: 'Full response data', icon: '📊' },
    { name: '{response.status}', description: 'HTTP status code', icon: '🔢' },
    { name: '{response.headers}', description: 'Response headers', icon: '📋' },
    { name: '{response.error}', description: 'Error message if failed', icon: '❌' },
  ],
  time: [
    { name: '{time.now}', description: 'Current timestamp', icon: '⏰' },
    { name: '{time.iso}', description: 'ISO timestamp', icon: '📅' },
    { name: '{time.unix}', description: 'Unix timestamp', icon: '🕐' },
  ],
  random: [
    { name: '{random.uuid}', description: 'Random UUID', icon: '🎲' },
    { name: '{random.number}', description: 'Random number', icon: '🔢' },
    { name: '{random.string}', description: 'Random string', icon: '🔤' },
  ],
};

const httpMethods = [
  { value: 'GET', label: 'GET', description: 'Retrieve data from server', color: 'green' },
  { value: 'POST', label: 'POST', description: 'Send data to create new resource', color: 'blue' },
  { value: 'PUT', label: 'PUT', description: 'Update existing resource', color: 'orange' },
  { value: 'PATCH', label: 'PATCH', description: 'Partially update resource', color: 'yellow' },
  { value: 'DELETE', label: 'DELETE', description: 'Remove resource from server', color: 'red' },
  { value: 'HEAD', label: 'HEAD', description: 'Get headers only', color: 'purple' },
  { value: 'OPTIONS', label: 'OPTIONS', description: 'Get allowed methods', color: 'gray' },
];

const bodyTypes = [
  { value: 'json', label: 'JSON', description: 'JavaScript Object Notation' },
  { value: 'form', label: 'Form Data', description: 'URL-encoded form data' },
  { value: 'text', label: 'Plain Text', description: 'Raw text content' },
  { value: 'xml', label: 'XML', description: 'Extensible Markup Language' },
];

const errorHandlingOptions = [
  { value: 'ignore', label: 'Ignore Errors', description: 'Continue flow on API errors' },
  { value: 'log', label: 'Log Errors', description: 'Log errors but continue' },
  { value: 'throw', label: 'Throw Errors', description: 'Stop flow on API errors' },
  { value: 'retry', label: 'Retry on Error', description: 'Retry failed requests' },
];

const commonHeaders = [
  { key: 'Authorization', value: 'Bearer {user.token}' },
  { key: 'Content-Type', value: 'application/json' },
  { key: 'User-Agent', value: 'Discord Bot API Client' },
  { key: 'Accept', value: 'application/json' },
  { key: 'X-API-Key', value: '{api.key}' },
];

const ApiRequestNode = memo(({ data, selected, id, updateNodeData: updateParentNodeData }: NodeProps<ApiRequestNodeData> & { updateNodeData?: (nodeId: string, newData: any) => void }) => {
  const { currentScheme } = useTheme();
  const { isOpen, onOpen, onClose } = useDisclosure();
  const toast = useToast();
  const [nodeData, setNodeData] = useState<ApiRequestNodeData>(() => ({
    method: 'GET',
    headers: [],
    bodyType: 'json',
    timeout: 5000,
    errorHandling: 'log',
    retryCount: 0,
    retryDelay: 1000,
    followRedirects: true,
    validateSSL: true,
    ...data
  }));
  
  const [isTesting, setIsTesting] = useState(false);
  const [testResponse, setTestResponse] = useState<any>(null);
  const [testError, setTestError] = useState<string | null>(null);
  const [availableVariables, setAvailableVariables] = useState<string[]>([]);
  const [showVariables, setShowVariables] = useState(false);

  const updateNodeData = (updates: Partial<ApiRequestNodeData>) => {
    setNodeData(prev => ({ ...prev, ...updates }));
  };

  const handleModalClose = () => {
    // Update parent nodes array when modal closes
    if (updateParentNodeData && id) {
      updateParentNodeData(id, nodeData);
    }
    onClose();
  };

  const copyVariable = (variable: string) => {
    navigator.clipboard.writeText(variable);
    toast({
      title: 'Copied!',
      description: `Variable ${variable} copied to clipboard`,
      status: 'success',
      duration: 2000,
      isClosable: true,
    });
  };

  const renderVariablesList = () => (
    <Collapse in={showVariables} animateOpacity>
      <Box
        bg={currentScheme.colors.surface}
        border="1px solid"
        borderColor={currentScheme.colors.border}
        borderRadius="md"
        p={4}
        mt={3}
        maxH="400px"
        overflowY="auto"
      >
        <Accordion allowMultiple>
          {Object.entries(apiVariables).map(([category, variables]) => (
            <AccordionItem key={category} border="none">
              <AccordionButton px={0} py={2}>
                <Box flex="1" textAlign="left">
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} textTransform="capitalize">
                    {category} Variables
                  </Text>
                </Box>
                <AccordionIcon />
              </AccordionButton>
              <AccordionPanel px={0} py={2}>
                <VStack spacing={2} align="stretch">
                  {variables.map((variable) => (
                    <HStack
                      key={variable.name}
                      spacing={2}
                      p={2}
                      bg={currentScheme.colors.background}
                      borderRadius="md"
                      cursor="pointer"
                      _hover={{ bg: currentScheme.colors.surface }}
                      onClick={() => copyVariable(variable.name)}
                    >
                      <Text fontSize="sm">{variable.icon}</Text>
                      <Code fontSize="xs" colorScheme="teal">
                        {variable.name}
                      </Code>
                      <Text fontSize="xs" color={currentScheme.colors.textSecondary} flex="1">
                        {variable.description}
                      </Text>
                      <IconButton
                        icon={<FiCopy />}
                        size="xs"
                        variant="ghost"
                        aria-label="Copy variable"
                        onClick={(e) => {
                          e.stopPropagation();
                          copyVariable(variable.name);
                        }}
                      />
                    </HStack>
                  ))}
                </VStack>
              </AccordionPanel>
            </AccordionItem>
          ))}
        </Accordion>
      </Box>
    </Collapse>
  );

  const testApiRequest = async () => {
    if (!nodeData.url) {
      setTestError('Please enter a URL first');
      toast({
        title: 'Test Failed',
        description: 'Please enter a URL first',
        status: 'error',
        duration: 3000,
        isClosable: true,
      });
      return;
    }

    setIsTesting(true);
    setTestError(null);
    setTestResponse(null);

    try {
      const headers: Record<string, string> = {};
      
      // Add custom headers
      nodeData.headers?.forEach(header => {
        if (header.key && header.value) {
          headers[header.key] = header.value;
        }
      });

      // Set content type based on body type
      if (nodeData.body && (nodeData.method === 'POST' || nodeData.method === 'PUT' || nodeData.method === 'PATCH')) {
        if (nodeData.bodyType === 'json') {
          headers['Content-Type'] = 'application/json';
        } else if (nodeData.bodyType === 'form') {
          headers['Content-Type'] = 'application/x-www-form-urlencoded';
        } else if (nodeData.bodyType === 'xml') {
          headers['Content-Type'] = 'application/xml';
        }
      }

      const requestOptions: RequestInit = {
        method: nodeData.method || 'GET',
        headers,
      };

      // Add body if needed
      if (nodeData.body && (nodeData.method === 'POST' || nodeData.method === 'PUT' || nodeData.method === 'PATCH')) {
        if (nodeData.bodyType === 'json') {
          try {
            // Validate JSON
            JSON.parse(nodeData.body);
            requestOptions.body = nodeData.body;
          } catch (e) {
            throw new Error('Invalid JSON in request body');
          }
        } else {
          requestOptions.body = nodeData.body;
        }
      }

      const response = await fetch(nodeData.url, requestOptions);
      
      const responseData = await response.text();
      let parsedData;
      
      try {
        parsedData = JSON.parse(responseData);
      } catch (e) {
        parsedData = responseData;
      }
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      setTestResponse({
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        data: parsedData
      });
      
      // Extract available variables from response
      const variables = extractVariables(parsedData);
      setAvailableVariables(variables);
      
      toast({
        title: 'API Test Successful!',
        description: `Request completed with status ${response.status}`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Request failed';
      setTestError(errorMessage);
      toast({
        title: 'API Test Failed',
        description: errorMessage,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setIsTesting(false);
    }
  };

  const extractVariables = (obj: any, prefix = ''): string[] => {
    const variables: string[] = [];
    
    if (obj && typeof obj === 'object') {
      if (Array.isArray(obj)) {
        // Handle arrays
        obj.forEach((item, index) => {
          const path = prefix ? `${prefix}.${index}` : `${index}`;
          variables.push(path);
          
          if (typeof item === 'object' && item !== null) {
            variables.push(...extractVariables(item, path));
          }
        });
      } else {
        // Handle objects
        Object.keys(obj).forEach(key => {
          const path = prefix ? `${prefix}.${key}` : key;
          variables.push(path);
          
          if (typeof obj[key] === 'object' && obj[key] !== null) {
            variables.push(...extractVariables(obj[key], path));
          }
        });
      }
    }
    
    return variables;
  };

  const addHeader = () => {
    updateNodeData({
      headers: [...(nodeData.headers || []), { key: '', value: '' }]
    });
  };

  const updateHeader = (index: number, field: 'key' | 'value', value: string) => {
    const newHeaders = [...(nodeData.headers || [])];
    newHeaders[index][field] = value;
    updateNodeData({ headers: newHeaders });
  };

  const removeHeader = (index: number) => {
    const newHeaders = (nodeData.headers || []).filter((_, i) => i !== index);
    updateNodeData({ headers: newHeaders });
  };

  const addCommonHeader = (header: { key: string; value: string }) => {
    const newHeaders = [...(nodeData.headers || []), header];
    updateNodeData({ headers: newHeaders });
  };

  const getMethodColor = (method: string) => {
    const methodConfig = httpMethods.find(m => m.value === method);
    return methodConfig?.color || 'gray';
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'GET': return '📥';
      case 'POST': return '📤';
      case 'PUT': return '🔄';
      case 'PATCH': return '✏️';
      case 'DELETE': return '🗑️';
      default: return '🌐';
    }
  };

  return (
    <>
      <Box
        bg={currentScheme.colors.surface}
        border={`2px solid ${selected ? '#06b6d4' : currentScheme.colors.border}`}
        borderRadius="md"
        p={2}
        minW="140px"
        maxW="180px"
        boxShadow="sm"
        position="relative"
        _hover={{
          boxShadow: 'md',
          transform: 'translateY(-1px)',
        }}
        transition="all 0.2s"
      >
        <Handle
          type="target"
          position={Position.Top}
          style={{
            background: '#06b6d4',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            top: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
        
        <VStack spacing={1} align="stretch">
          <HStack justify="space-between" align="center">
            <HStack spacing={1}>
              <Box
                bg="teal.500"
                color="white"
                borderRadius="full"
                p={0.5}
                fontSize="xs"
              >
                <FiGlobe />
              </Box>
              <Text fontSize="xs" fontWeight="bold" color={currentScheme.colors.text}>
                API Request
              </Text>
            </HStack>
            <IconButton
              icon={<FiSettings />}
              size="xs"
              variant="ghost"
              onClick={onOpen}
              aria-label="Configure API request"
            />
          </HStack>
          
          <Box>
            <HStack spacing={1}>
              {nodeData.method && (
                <Text fontSize="xs">{getMethodIcon(nodeData.method)}</Text>
              )}
              <Text fontSize="xs" color={currentScheme.colors.text} noOfLines={1}>
                {nodeData.method || 'GET'} Request
              </Text>
            </HStack>
          </Box>
          
          {nodeData.url && (
            <Box>
              <Text fontSize="xs" color={currentScheme.colors.textSecondary} noOfLines={1}>
                {nodeData.url.length > 25 ? nodeData.url.substring(0, 25) + '...' : nodeData.url}
              </Text>
            </Box>
          )}
          
          <HStack spacing={1} flexWrap="wrap">
            {nodeData.method && (
              <Badge size="xs" colorScheme={getMethodColor(nodeData.method)}>
                {nodeData.method}
              </Badge>
            )}
            {(nodeData.headers?.length ?? 0) > 0 && (
              <Badge size="xs" colorScheme="blue">
                {nodeData.headers?.length} header{(nodeData.headers?.length ?? 0) !== 1 ? 's' : ''}
              </Badge>
            )}
            {nodeData.saveToVariable && (
              <Badge size="xs" colorScheme="green">
                Saves Data
              </Badge>
            )}
          </HStack>
        </VStack>
        
        <Handle
          type="source"
          position={Position.Bottom}
          style={{
            background: '#06b6d4',
            border: `2px solid ${currentScheme.colors.surface}`,
            width: '12px',
            height: '12px',
            bottom: '-6px',
            left: '50%',
            transform: 'translateX(-50%)',
          }}
        />
      </Box>

      {/* Enhanced Configuration Modal */}
      <Modal isOpen={isOpen} onClose={handleModalClose} size="6xl">
        <ModalOverlay bg="blackAlpha.600" />
        <ModalContent bg={currentScheme.colors.background} border="2px solid" borderColor="teal.400" maxW="1400px">
          <ModalHeader color={currentScheme.colors.text}>
            🌐 Configure API Request
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody pb={6}>
            <VStack spacing={6} align="stretch">
              {/* Variables Helper */}
              <Box>
                <HStack justify="space-between" align="center" mb={2}>
                  <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                    Available Variables
                  </Text>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={showVariables ? <FiEyeOff /> : <FiEye />}
                    onClick={() => setShowVariables(!showVariables)}
                  >
                    {showVariables ? 'Hide' : 'Show'} Variables
                  </Button>
                </HStack>
                <Alert status="info" borderRadius="md" mb={2}>
                <AlertIcon />
                <AlertDescription fontSize="sm">
                    💡 Use variables in your API requests! Click any variable below to copy it. Variables are replaced with actual values when the request executes.
                </AlertDescription>
              </Alert>
                {renderVariablesList()}
              </Box>

              <Divider />

              {/* Tabbed Configuration */}
              <Tabs variant="enclosed" colorScheme="teal">
                <TabList>
                  <Tab>Request</Tab>
                  <Tab>Headers</Tab>
                  <Tab>Body</Tab>
                  <Tab>Settings</Tab>
                  <Tab>Test</Tab>
                </TabList>
                
                <TabPanels>
                  {/* Request Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <FormControl isRequired>
                        <FormLabel color={currentScheme.colors.text}>Request URL</FormLabel>
                        <Input
                          value={nodeData.url || ''}
                          onChange={(e) => updateNodeData({ url: e.target.value })}
                          placeholder="https://api.example.com/data or {server.webhook.url}"
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                        />
                      </FormControl>
                      
                <FormControl>
                        <FormLabel color={currentScheme.colors.text}>HTTP Method</FormLabel>
                  <Select
                    value={nodeData.method || 'GET'}
                    onChange={(e) => updateNodeData({ method: e.target.value })}
                    bg={currentScheme.colors.background}
                    color={currentScheme.colors.text}
                    borderColor={currentScheme.colors.border}
                  >
                    {httpMethods.map((method) => (
                      <option key={method.value} value={method.value}>
                              {method.label} - {method.description}
                      </option>
                    ))}
                  </Select>
                </FormControl>

                <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Save Response To Variable</FormLabel>
                  <Input
                          value={nodeData.saveToVariable || ''}
                          onChange={(e) => updateNodeData({ saveToVariable: e.target.value })}
                          placeholder="response_data (access with {response_data.field})"
                    bg={currentScheme.colors.background}
                    color={currentScheme.colors.text}
                    borderColor={currentScheme.colors.border}
                  />
                        <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                          Variable name to store the API response. Leave empty if you don't need the response.
                        </Text>
                </FormControl>

              <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Description</FormLabel>
                        <Textarea
                          value={nodeData.description || ''}
                          onChange={(e) => updateNodeData({ description: e.target.value })}
                          placeholder="Describe what this API request does"
                  bg={currentScheme.colors.background}
                  color={currentScheme.colors.text}
                  borderColor={currentScheme.colors.border}
                          minH="80px"
                />
              </FormControl>
                    </VStack>
                  </TabPanel>
                  
                  {/* Headers Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                          Request Headers
                        </Text>
                        <Button
                          leftIcon={<FiPlus />}
                          onClick={addHeader}
                          colorScheme="teal"
                          size="sm"
                        >
                          Add Header
                        </Button>
                      </HStack>
                      
                      <Alert status="info" borderRadius="md">
                        <AlertIcon />
                        <AlertDescription fontSize="sm">
                          Headers provide additional information about the request. Common headers are automatically set based on content type.
                        </AlertDescription>
                      </Alert>
                      
                      {/* Common Headers */}
                      <Box>
                        <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mb={2}>
                          Quick Add Common Headers:
                        </Text>
                        <Wrap spacing={2}>
                          {commonHeaders.map((header, index) => (
                            <WrapItem key={index}>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => addCommonHeader(header)}
                                leftIcon={<FiPlus />}
                              >
                                {header.key}
                              </Button>
                            </WrapItem>
                          ))}
                        </Wrap>
                    </Box>
                      
                    <VStack spacing={3} align="stretch">
                      {nodeData.headers?.map((header, index) => (
                          <Box
                            key={index}
                            p={3}
                            bg={currentScheme.colors.surface}
                            borderRadius="md"
                            border="1px solid"
                            borderColor={currentScheme.colors.border}
                          >
                            <HStack justify="space-between" align="center" mb={2}>
                              <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                                Header {index + 1}
                              </Text>
                              <IconButton
                                icon={<FiTrash2 />}
                                size="sm"
                                colorScheme="red"
                                variant="ghost"
                                onClick={() => removeHeader(index)}
                                aria-label="Remove header"
                              />
                            </HStack>
                            
                            <SimpleGrid columns={2} spacing={3}>
                              <FormControl>
                                <FormLabel fontSize="sm" color={currentScheme.colors.text}>Header Name</FormLabel>
                          <Input
                            value={header.key}
                            onChange={(e) => updateHeader(index, 'key', e.target.value)}
                                  placeholder="Authorization, Content-Type, etc."
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                            borderColor={currentScheme.colors.border}
                                  size="sm"
                          />
                              </FormControl>
                              
                              <FormControl>
                                <FormLabel fontSize="sm" color={currentScheme.colors.text}>Header Value</FormLabel>
                          <Input
                            value={header.value}
                            onChange={(e) => updateHeader(index, 'value', e.target.value)}
                                  placeholder="Bearer {user.token}, application/json, etc."
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                            borderColor={currentScheme.colors.border}
                            size="sm"
                                />
                              </FormControl>
                            </SimpleGrid>
                          </Box>
                        ))}
                        
                        {(!nodeData.headers || nodeData.headers.length === 0) && (
                          <Alert status="info" borderRadius="md">
                            <AlertIcon />
                            <AlertDescription>
                              No custom headers configured. Default headers will be set automatically based on request type.
                            </AlertDescription>
                          </Alert>
                        )}
                    </VStack>
                    </VStack>
                  </TabPanel>
                  
                  {/* Body Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                      Request Body
                        </Text>
                        <Text fontSize="sm" color={currentScheme.colors.textSecondary}>
                          Only used for POST, PUT, PATCH requests
                        </Text>
                      </HStack>
                      
                      <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Body Type</FormLabel>
                        <Select
                          value={nodeData.bodyType || 'json'}
                          onChange={(e) => updateNodeData({ bodyType: e.target.value as ApiRequestNodeData['bodyType'] })}
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                        >
                          {bodyTypes.map((type) => (
                            <option key={type.value} value={type.value}>
                              {type.label} - {type.description}
                            </option>
                          ))}
                        </Select>
                      </FormControl>

                      <FormControl>
                        <FormLabel color={currentScheme.colors.text}>Request Body</FormLabel>
                        <Textarea
                          value={nodeData.body || ''}
                          onChange={(e) => updateNodeData({ body: e.target.value })}
                          placeholder={
                            nodeData.bodyType === 'json' 
                              ? '{"key": "value", "user": "{user.id}"}' 
                              : nodeData.bodyType === 'form'
                              ? 'key=value&user={user.id}'
                              : 'Raw text content with {variables}'
                          }
                          bg={currentScheme.colors.background}
                          color={currentScheme.colors.text}
                          borderColor={currentScheme.colors.border}
                          minH="200px"
                          fontFamily="monospace"
                          fontSize="sm"
                        />
                        <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                          {nodeData.bodyType === 'json' && 'Must be valid JSON format'}
                          {nodeData.bodyType === 'form' && 'Use key=value&key2=value2 format'}
                          {nodeData.bodyType === 'text' && 'Plain text content'}
                        </Text>
                      </FormControl>

                      <Alert status="warning" borderRadius="md">
                        <AlertIcon />
                <Box>
                          <Text fontSize="sm" fontWeight="bold" mb={1}>
                            💡 Body Examples:
                    </Text>
                          <VStack align="start" spacing={1} fontSize="sm" fontFamily="monospace">
                            <Text>JSON: {`{"message": "{message.content}", "user_id": "{user.id}"}`}</Text>
                            <Text>Form: user_id={`{user.id}`}&message={`{message.content}`}</Text>
                            <Text>Text: User {`{user.username}`} said: {`{message.content}`}</Text>
                          </VStack>
                        </Box>
                      </Alert>
                    </VStack>
                  </TabPanel>
                  
                  {/* Settings Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                        Advanced Settings
                      </Text>
                      
                      <SimpleGrid columns={2} spacing={4}>
                        <FormControl>
                          <FormLabel color={currentScheme.colors.text}>Timeout (milliseconds)</FormLabel>
                          <NumberInput
                            value={nodeData.timeout || 5000}
                            onChange={(valueString) => updateNodeData({ timeout: parseInt(valueString) || 5000 })}
                            min={1000}
                            max={60000}
                          >
                            <NumberInputField
                              bg={currentScheme.colors.background}
                              color={currentScheme.colors.text}
                              borderColor={currentScheme.colors.border}
                            />
                            <NumberInputStepper>
                              <NumberIncrementStepper />
                              <NumberDecrementStepper />
                            </NumberInputStepper>
                          </NumberInput>
                          <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                            Maximum time to wait for response (5000ms = 5 seconds)
                          </Text>
                        </FormControl>
                        
                        <FormControl>
                          <FormLabel color={currentScheme.colors.text}>Error Handling</FormLabel>
                          <Select
                            value={nodeData.errorHandling || 'log'}
                            onChange={(e) => updateNodeData({ errorHandling: e.target.value as ApiRequestNodeData['errorHandling'] })}
                            bg={currentScheme.colors.background}
                            color={currentScheme.colors.text}
                            borderColor={currentScheme.colors.border}
                          >
                            {errorHandlingOptions.map((option) => (
                              <option key={option.value} value={option.value}>
                                {option.label}
                              </option>
                            ))}
                          </Select>
                          <Text fontSize="xs" color={currentScheme.colors.textSecondary} mt={1}>
                            {errorHandlingOptions.find(o => o.value === nodeData.errorHandling)?.description}
                          </Text>
                        </FormControl>
                      </SimpleGrid>
                      
                      {nodeData.errorHandling === 'retry' && (
                        <SimpleGrid columns={2} spacing={4}>
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Retry Count</FormLabel>
                            <NumberInput
                              value={nodeData.retryCount || 0}
                              onChange={(valueString) => updateNodeData({ retryCount: parseInt(valueString) || 0 })}
                              min={0}
                              max={5}
                            >
                              <NumberInputField
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                              />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                          </FormControl>
                          
                          <FormControl>
                            <FormLabel color={currentScheme.colors.text}>Retry Delay (ms)</FormLabel>
                            <NumberInput
                              value={nodeData.retryDelay || 1000}
                              onChange={(valueString) => updateNodeData({ retryDelay: parseInt(valueString) || 1000 })}
                              min={500}
                              max={10000}
                            >
                              <NumberInputField
                                bg={currentScheme.colors.background}
                                color={currentScheme.colors.text}
                                borderColor={currentScheme.colors.border}
                              />
                              <NumberInputStepper>
                                <NumberIncrementStepper />
                                <NumberDecrementStepper />
                              </NumberInputStepper>
                            </NumberInput>
                          </FormControl>
                        </SimpleGrid>
                      )}
                      
                      <VStack spacing={4} align="stretch">
                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.followRedirects}
                            onChange={(e) => updateNodeData({ followRedirects: e.target.checked })}
                      colorScheme="teal"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Follow Redirects
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Automatically follow HTTP redirects (3xx responses)
                            </Text>
                          </VStack>
                        </HStack>
                        
                        <HStack spacing={4}>
                          <Switch
                            isChecked={nodeData.validateSSL}
                            onChange={(e) => updateNodeData({ validateSSL: e.target.checked })}
                            colorScheme="red"
                          />
                          <VStack align="start" spacing={0}>
                            <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                              Validate SSL Certificates
                            </Text>
                            <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                              Verify SSL certificates (disable only for testing)
                            </Text>
                          </VStack>
                        </HStack>
                      </VStack>
                    </VStack>
                  </TabPanel>
                  
                  {/* Test Tab */}
                  <TabPanel>
                    <VStack spacing={4} align="stretch">
                      <HStack justify="space-between" align="center">
                        <Text fontSize="lg" fontWeight="bold" color={currentScheme.colors.text}>
                          Test API Request
                        </Text>
                        <Button
                          leftIcon={isTesting ? <Spinner size="sm" /> : <FiPlay />}
                      onClick={testApiRequest}
                          colorScheme="teal"
                      isLoading={isTesting}
                      loadingText="Testing..."
                          isDisabled={!nodeData.url}
                    >
                      Test Request
                    </Button>
                  </HStack>

                      <Alert status="warning" borderRadius="md">
                      <AlertIcon />
                      <AlertDescription fontSize="sm">
                          ⚠️ This will make a real API request! Variables will not be replaced during testing. Make sure your URL and credentials are correct.
                      </AlertDescription>
                      </Alert>
                      
                      {testError && (
                        <Alert status="error" borderRadius="md">
                          <AlertIcon />
                          <Box>
                            <Text fontSize="sm" fontWeight="bold" mb={1}>
                              Request Failed
                            </Text>
                            <Text fontSize="sm">
                              {testError}
                            </Text>
                          </Box>
                    </Alert>
                  )}

                  {testResponse && (
                      <Box>
                          <Text fontSize="md" fontWeight="bold" color={currentScheme.colors.text} mb={2}>
                            Response:
                        </Text>
                        <Box
                            p={4}
                            bg={currentScheme.colors.surface}
                            borderRadius="md"
                          border="1px solid"
                          borderColor={currentScheme.colors.border}
                            maxH="400px"
                          overflowY="auto"
                        >
                            <VStack spacing={3} align="stretch">
                              <HStack justify="space-between">
                                <Badge colorScheme="green" size="lg">
                                  {testResponse.status} {testResponse.statusText}
                                </Badge>
                                <HStack spacing={2}>
                                  <FiCheck color="green" />
                                  <Text fontSize="sm" color="green.500">Success</Text>
                                </HStack>
                              </HStack>
                              
                              <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text}>
                                Response Data:
                          </Text>
                              <Box
                                bg={currentScheme.colors.background}
                                p={3}
                                borderRadius="md"
                                fontFamily="monospace"
                                fontSize="xs"
                                overflowX="auto"
                              >
                                <pre>{JSON.stringify(testResponse.data, null, 2)}</pre>
                      </Box>

                      {availableVariables.length > 0 && (
                        <Box>
                          <Text fontSize="sm" fontWeight="bold" color={currentScheme.colors.text} mb={2}>
                                    Available Response Variables:
                          </Text>
                                  <VStack spacing={1} align="stretch" maxH="150px" overflowY="auto">
                                    {availableVariables.slice(0, 20).map((variable) => (
                                      <HStack
                                        key={variable}
                                        spacing={2}
                                        p={2}
                            bg={currentScheme.colors.background}
                            borderRadius="md"
                                        cursor="pointer"
                                        _hover={{ bg: currentScheme.colors.surface }}
                                        onClick={() => copyVariable(`{response.${variable}}`)}
                                      >
                                        <Code fontSize="xs" colorScheme="teal">
                                          {`{response.${variable}}`}
                                        </Code>
                                        <IconButton
                                          icon={<FiCopy />}
                                          size="xs"
                                          variant="ghost"
                                          aria-label="Copy variable"
                                        />
                                  </HStack>
                                    ))}
                                    {availableVariables.length > 20 && (
                                      <Text fontSize="xs" color={currentScheme.colors.textSecondary}>
                                        ...and {availableVariables.length - 20} more variables
                                </Text>
                              )}
                            </VStack>
                        </Box>
                      )}
                    </VStack>
                </Box>
                        </Box>
                      )}
              </VStack>
                  </TabPanel>
                </TabPanels>
              </Tabs>
              
              <Button
                colorScheme="teal"
                                  onClick={() => {
                    // Save the configuration
                    data.url = nodeData.url;
                    data.method = nodeData.method;
                    data.headers = nodeData.headers;
                    data.body = nodeData.body;
                    data.bodyType = nodeData.bodyType;
                    data.timeout = nodeData.timeout;
                    data.saveToVariable = nodeData.saveToVariable;
                    data.errorHandling = nodeData.errorHandling;
                  data.description = nodeData.description;
                  data.retryCount = nodeData.retryCount;
                  data.retryDelay = nodeData.retryDelay;
                  data.followRedirects = nodeData.followRedirects;
                  data.validateSSL = nodeData.validateSSL;
                  data.label = `${nodeData.method || 'GET'} Request`;
                    onClose();
                  }}
                size="lg"
                width="full"
              >
                Save Configuration
              </Button>
            </VStack>
          </ModalBody>
          </ModalContent>
        </Modal>
    </>
  );
});

ApiRequestNode.displayName = 'ApiRequestNode';

export default ApiRequestNode; 