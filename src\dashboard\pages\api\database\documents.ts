import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { MongoClient, ObjectId } from 'mongodb';
import { dashboardConfig } from '../../../core/config';
import { logDatabaseOperation } from './middleware';

// MongoDB connection
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;

async function connectToDatabase() {
  if (cachedClient) {
    return cachedClient;
  }

  const client = await MongoClient.connect(mongoUrl);
  cachedClient = client;
  return client;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  const { collection: collectionName, id } = req.query;
  if (!collectionName) {
    return res.status(400).json({ error: 'Collection name is required' });
  }

  try {
    const client = await connectToDatabase();
    const db = client.db(dbName);
    const collection = db.collection(collectionName as string);

    switch (req.method) {
      case 'GET':
        const documents = await collection.find({}).toArray();
        await logDatabaseOperation('query', collectionName as string, { count: documents.length });
        return res.status(200).json({ documents });

      case 'POST':
        const insertResult = await collection.insertOne(req.body);
        await logDatabaseOperation('insert', collectionName as string, { document: req.body });
        return res.status(201).json({ result: insertResult });

      case 'PUT':
        if (!id) {
          return res.status(400).json({ error: 'Document ID is required' });
        }
        const updateResult = await collection.updateOne(
          { _id: new ObjectId(id as string) },
          { $set: req.body }
        );
        await logDatabaseOperation('update', collectionName as string, { 
          id,
          document: req.body 
        });
        return res.status(200).json({ result: updateResult });

      case 'DELETE':
        if (!id) {
          return res.status(400).json({ error: 'Document ID is required' });
        }
        const deleteResult = await collection.deleteOne({ 
          _id: new ObjectId(id as string) 
        });
        await logDatabaseOperation('delete', collectionName as string, { id });
        return res.status(200).json({ result: deleteResult });

      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('Database error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
} 