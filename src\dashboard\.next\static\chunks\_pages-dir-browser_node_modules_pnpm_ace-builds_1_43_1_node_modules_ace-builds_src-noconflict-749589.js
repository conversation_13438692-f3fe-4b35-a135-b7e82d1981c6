/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_pnpm_ace-builds_1_43_1_node_modules_ace-builds_src-noconflict-749589"],{

/***/ "(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/ext-language_tools.js":
/*!***************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/ext-language_tools.js ***!
  \***************************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("/* module decorator */ module = __webpack_require__.nmd(module);\nace.define(\"ace/snippets\",[\"require\",\"exports\",\"module\",\"ace/lib/dom\",\"ace/lib/oop\",\"ace/lib/event_emitter\",\"ace/lib/lang\",\"ace/range\",\"ace/range_list\",\"ace/keyboard/hash_handler\",\"ace/tokenizer\",\"ace/clipboard\",\"ace/editor\"], function(require, exports, module){\"use strict\";\nvar dom = require(\"./lib/dom\");\nvar oop = require(\"./lib/oop\");\nvar EventEmitter = require(\"./lib/event_emitter\").EventEmitter;\nvar lang = require(\"./lib/lang\");\nvar Range = require(\"./range\").Range;\nvar RangeList = require(\"./range_list\").RangeList;\nvar HashHandler = require(\"./keyboard/hash_handler\").HashHandler;\nvar Tokenizer = require(\"./tokenizer\").Tokenizer;\nvar clipboard = require(\"./clipboard\");\nvar VARIABLES = {\n    CURRENT_WORD: function (editor) {\n        return editor.session.getTextRange(editor.session.getWordRange());\n    },\n    SELECTION: function (editor, name, indentation) {\n        var text = editor.session.getTextRange();\n        if (indentation)\n            return text.replace(/\\n\\r?([ \\t]*\\S)/g, \"\\n\" + indentation + \"$1\");\n        return text;\n    },\n    CURRENT_LINE: function (editor) {\n        return editor.session.getLine(editor.getCursorPosition().row);\n    },\n    PREV_LINE: function (editor) {\n        return editor.session.getLine(editor.getCursorPosition().row - 1);\n    },\n    LINE_INDEX: function (editor) {\n        return editor.getCursorPosition().row;\n    },\n    LINE_NUMBER: function (editor) {\n        return editor.getCursorPosition().row + 1;\n    },\n    SOFT_TABS: function (editor) {\n        return editor.session.getUseSoftTabs() ? \"YES\" : \"NO\";\n    },\n    TAB_SIZE: function (editor) {\n        return editor.session.getTabSize();\n    },\n    CLIPBOARD: function (editor) {\n        return clipboard.getText && clipboard.getText();\n    },\n    FILENAME: function (editor) {\n        return /[^/\\\\]*$/.exec(this.FILEPATH(editor))[0];\n    },\n    FILENAME_BASE: function (editor) {\n        return /[^/\\\\]*$/.exec(this.FILEPATH(editor))[0].replace(/\\.[^.]*$/, \"\");\n    },\n    DIRECTORY: function (editor) {\n        return this.FILEPATH(editor).replace(/[^/\\\\]*$/, \"\");\n    },\n    FILEPATH: function (editor) { return \"/not implemented.txt\"; },\n    WORKSPACE_NAME: function () { return \"Unknown\"; },\n    FULLNAME: function () { return \"Unknown\"; },\n    BLOCK_COMMENT_START: function (editor) {\n        var mode = editor.session.$mode || {};\n        return mode.blockComment && mode.blockComment.start || \"\";\n    },\n    BLOCK_COMMENT_END: function (editor) {\n        var mode = editor.session.$mode || {};\n        return mode.blockComment && mode.blockComment.end || \"\";\n    },\n    LINE_COMMENT: function (editor) {\n        var mode = editor.session.$mode || {};\n        return mode.lineCommentStart || \"\";\n    },\n    CURRENT_YEAR: date.bind(null, { year: \"numeric\" }),\n    CURRENT_YEAR_SHORT: date.bind(null, { year: \"2-digit\" }),\n    CURRENT_MONTH: date.bind(null, { month: \"numeric\" }),\n    CURRENT_MONTH_NAME: date.bind(null, { month: \"long\" }),\n    CURRENT_MONTH_NAME_SHORT: date.bind(null, { month: \"short\" }),\n    CURRENT_DATE: date.bind(null, { day: \"2-digit\" }),\n    CURRENT_DAY_NAME: date.bind(null, { weekday: \"long\" }),\n    CURRENT_DAY_NAME_SHORT: date.bind(null, { weekday: \"short\" }),\n    CURRENT_HOUR: date.bind(null, { hour: \"2-digit\", hour12: false }),\n    CURRENT_MINUTE: date.bind(null, { minute: \"2-digit\" }),\n    CURRENT_SECOND: date.bind(null, { second: \"2-digit\" })\n};\nVARIABLES.SELECTED_TEXT = VARIABLES.SELECTION;\nfunction date(dateFormat) {\n    var str = new Date().toLocaleString(\"en-us\", dateFormat);\n    return str.length == 1 ? \"0\" + str : str;\n}\nvar SnippetManager = /** @class */ (function () {\n    function SnippetManager() {\n        this.snippetMap = {};\n        this.snippetNameMap = {};\n        this.variables = VARIABLES;\n    }\n    SnippetManager.prototype.getTokenizer = function () {\n        return SnippetManager[\"$tokenizer\"] || this.createTokenizer();\n    };\n    SnippetManager.prototype.createTokenizer = function () {\n        function TabstopToken(str) {\n            str = str.substr(1);\n            if (/^\\d+$/.test(str))\n                return [{ tabstopId: parseInt(str, 10) }];\n            return [{ text: str }];\n        }\n        function escape(ch) {\n            return \"(?:[^\\\\\\\\\" + ch + \"]|\\\\\\\\.)\";\n        }\n        var formatMatcher = {\n            regex: \"/(\" + escape(\"/\") + \"+)/\",\n            onMatch: function (val, state, stack) {\n                var ts = stack[0];\n                ts.fmtString = true;\n                ts.guard = val.slice(1, -1);\n                ts.flag = \"\";\n                return \"\";\n            },\n            next: \"formatString\"\n        };\n        SnippetManager[\"$tokenizer\"] = new Tokenizer({\n            start: [\n                { regex: /\\\\./, onMatch: function (val, state, stack) {\n                        var ch = val[1];\n                        if (ch == \"}\" && stack.length) {\n                            val = ch;\n                        }\n                        else if (\"`$\\\\\".indexOf(ch) != -1) {\n                            val = ch;\n                        }\n                        return [val];\n                    } },\n                { regex: /}/, onMatch: function (val, state, stack) {\n                        return [stack.length ? stack.shift() : val];\n                    } },\n                { regex: /\\$(?:\\d+|\\w+)/, onMatch: TabstopToken },\n                { regex: /\\$\\{[\\dA-Z_a-z]+/, onMatch: function (str, state, stack) {\n                        var t = TabstopToken(str.substr(1));\n                        stack.unshift(t[0]);\n                        return t;\n                    }, next: \"snippetVar\" },\n                { regex: /\\n/, token: \"newline\", merge: false }\n            ],\n            snippetVar: [\n                { regex: \"\\\\|\" + escape(\"\\\\|\") + \"*\\\\|\", onMatch: function (val, state, stack) {\n                        var choices = val.slice(1, -1).replace(/\\\\[,|\\\\]|,/g, function (operator) {\n                            return operator.length == 2 ? operator[1] : \"\\x00\";\n                        }).split(\"\\x00\").map(function (value) {\n                            return { value: value };\n                        });\n                        stack[0].choices = choices;\n                        return [choices[0]];\n                    }, next: \"start\" },\n                formatMatcher,\n                { regex: \"([^:}\\\\\\\\]|\\\\\\\\.)*:?\", token: \"\", next: \"start\" }\n            ],\n            formatString: [\n                { regex: /:/, onMatch: function (val, state, stack) {\n                        if (stack.length && stack[0].expectElse) {\n                            stack[0].expectElse = false;\n                            stack[0].ifEnd = { elseEnd: stack[0] };\n                            return [stack[0].ifEnd];\n                        }\n                        return \":\";\n                    } },\n                { regex: /\\\\./, onMatch: function (val, state, stack) {\n                        var ch = val[1];\n                        if (ch == \"}\" && stack.length)\n                            val = ch;\n                        else if (\"`$\\\\\".indexOf(ch) != -1)\n                            val = ch;\n                        else if (ch == \"n\")\n                            val = \"\\n\";\n                        else if (ch == \"t\")\n                            val = \"\\t\";\n                        else if (\"ulULE\".indexOf(ch) != -1)\n                            val = { changeCase: ch, local: ch > \"a\" };\n                        return [val];\n                    } },\n                { regex: \"/\\\\w*}\", onMatch: function (val, state, stack) {\n                        var next = stack.shift();\n                        if (next)\n                            next.flag = val.slice(1, -1);\n                        this.next = next && next.tabstopId ? \"start\" : \"\";\n                        return [next || val];\n                    }, next: \"start\" },\n                { regex: /\\$(?:\\d+|\\w+)/, onMatch: function (val, state, stack) {\n                        return [{ text: val.slice(1) }];\n                    } },\n                { regex: /\\${\\w+/, onMatch: function (val, state, stack) {\n                        var token = { text: val.slice(2) };\n                        stack.unshift(token);\n                        return [token];\n                    }, next: \"formatStringVar\" },\n                { regex: /\\n/, token: \"newline\", merge: false },\n                { regex: /}/, onMatch: function (val, state, stack) {\n                        var next = stack.shift();\n                        this.next = next && next.tabstopId ? \"start\" : \"\";\n                        return [next || val];\n                    }, next: \"start\" }\n            ],\n            formatStringVar: [\n                { regex: /:\\/\\w+}/, onMatch: function (val, state, stack) {\n                        var ts = stack[0];\n                        ts.formatFunction = val.slice(2, -1);\n                        return [stack.shift()];\n                    }, next: \"formatString\" },\n                formatMatcher,\n                { regex: /:[\\?\\-+]?/, onMatch: function (val, state, stack) {\n                        if (val[1] == \"+\")\n                            stack[0].ifEnd = stack[0];\n                        if (val[1] == \"?\")\n                            stack[0].expectElse = true;\n                    }, next: \"formatString\" },\n                { regex: \"([^:}\\\\\\\\]|\\\\\\\\.)*:?\", token: \"\", next: \"formatString\" }\n            ]\n        });\n        return SnippetManager[\"$tokenizer\"];\n    };\n    SnippetManager.prototype.tokenizeTmSnippet = function (str, startState) {\n        return this.getTokenizer().getLineTokens(str, startState).tokens.map(function (x) {\n            return x.value || x;\n        });\n    };\n    SnippetManager.prototype.getVariableValue = function (editor, name, indentation) {\n        if (/^\\d+$/.test(name))\n            return (this.variables.__ || {})[name] || \"\";\n        if (/^[A-Z]\\d+$/.test(name))\n            return (this.variables[name[0] + \"__\"] || {})[name.substr(1)] || \"\";\n        name = name.replace(/^TM_/, \"\");\n        if (!this.variables.hasOwnProperty(name))\n            return \"\";\n        var value = this.variables[name];\n        if (typeof value == \"function\")\n            value = this.variables[name](editor, name, indentation);\n        return value == null ? \"\" : value;\n    };\n    SnippetManager.prototype.tmStrFormat = function (str, ch, editor) {\n        if (!ch.fmt)\n            return str;\n        var flag = ch.flag || \"\";\n        var re = ch.guard;\n        re = new RegExp(re, flag.replace(/[^gim]/g, \"\"));\n        var fmtTokens = typeof ch.fmt == \"string\" ? this.tokenizeTmSnippet(ch.fmt, \"formatString\") : ch.fmt;\n        var _self = this;\n        var formatted = str.replace(re, function () {\n            var oldArgs = _self.variables.__;\n            _self.variables.__ = [].slice.call(arguments);\n            var fmtParts = _self.resolveVariables(fmtTokens, editor);\n            var gChangeCase = \"E\";\n            for (var i = 0; i < fmtParts.length; i++) {\n                var ch = fmtParts[i];\n                if (typeof ch == \"object\") {\n                    fmtParts[i] = \"\";\n                    if (ch.changeCase && ch.local) {\n                        var next = fmtParts[i + 1];\n                        if (next && typeof next == \"string\") {\n                            if (ch.changeCase == \"u\")\n                                fmtParts[i] = next[0].toUpperCase();\n                            else\n                                fmtParts[i] = next[0].toLowerCase();\n                            fmtParts[i + 1] = next.substr(1);\n                        }\n                    }\n                    else if (ch.changeCase) {\n                        gChangeCase = ch.changeCase;\n                    }\n                }\n                else if (gChangeCase == \"U\") {\n                    fmtParts[i] = ch.toUpperCase();\n                }\n                else if (gChangeCase == \"L\") {\n                    fmtParts[i] = ch.toLowerCase();\n                }\n            }\n            _self.variables.__ = oldArgs;\n            return fmtParts.join(\"\");\n        });\n        return formatted;\n    };\n    SnippetManager.prototype.tmFormatFunction = function (str, ch, editor) {\n        if (ch.formatFunction == \"upcase\")\n            return str.toUpperCase();\n        if (ch.formatFunction == \"downcase\")\n            return str.toLowerCase();\n        return str;\n    };\n    SnippetManager.prototype.resolveVariables = function (snippet, editor) {\n        var result = [];\n        var indentation = \"\";\n        var afterNewLine = true;\n        for (var i = 0; i < snippet.length; i++) {\n            var ch = snippet[i];\n            if (typeof ch == \"string\") {\n                result.push(ch);\n                if (ch == \"\\n\") {\n                    afterNewLine = true;\n                    indentation = \"\";\n                }\n                else if (afterNewLine) {\n                    indentation = /^\\t*/.exec(ch)[0];\n                    afterNewLine = /\\S/.test(ch);\n                }\n                continue;\n            }\n            if (!ch)\n                continue;\n            afterNewLine = false;\n            if (ch.fmtString) {\n                var j = snippet.indexOf(ch, i + 1);\n                if (j == -1)\n                    j = snippet.length;\n                ch.fmt = snippet.slice(i + 1, j);\n                i = j;\n            }\n            if (ch.text) {\n                var value = this.getVariableValue(editor, ch.text, indentation) + \"\";\n                if (ch.fmtString)\n                    value = this.tmStrFormat(value, ch, editor);\n                if (ch.formatFunction)\n                    value = this.tmFormatFunction(value, ch, editor);\n                if (value && !ch.ifEnd) {\n                    result.push(value);\n                    gotoNext(ch);\n                }\n                else if (!value && ch.ifEnd) {\n                    gotoNext(ch.ifEnd);\n                }\n            }\n            else if (ch.elseEnd) {\n                gotoNext(ch.elseEnd);\n            }\n            else if (ch.tabstopId != null) {\n                result.push(ch);\n            }\n            else if (ch.changeCase != null) {\n                result.push(ch);\n            }\n        }\n        function gotoNext(ch) {\n            var i1 = snippet.indexOf(ch, i + 1);\n            if (i1 != -1)\n                i = i1;\n        }\n        return result;\n    };\n    SnippetManager.prototype.getDisplayTextForSnippet = function (editor, snippetText) {\n        var processedSnippet = processSnippetText.call(this, editor, snippetText);\n        return processedSnippet.text;\n    };\n    SnippetManager.prototype.insertSnippetForSelection = function (editor, snippetText, options) {\n        if (options === void 0) { options = {}; }\n        var processedSnippet = processSnippetText.call(this, editor, snippetText, options);\n        var range = editor.getSelectionRange();\n        var end = editor.session.replace(range, processedSnippet.text);\n        var tabstopManager = new TabstopManager(editor);\n        var selectionId = editor.inVirtualSelectionMode && editor.selection.index;\n        tabstopManager.addTabstops(processedSnippet.tabstops, range.start, end, selectionId);\n    };\n    SnippetManager.prototype.insertSnippet = function (editor, snippetText, options) {\n        if (options === void 0) { options = {}; }\n        var self = this;\n        if (editor.inVirtualSelectionMode)\n            return self.insertSnippetForSelection(editor, snippetText, options);\n        editor.forEachSelection(function () {\n            self.insertSnippetForSelection(editor, snippetText, options);\n        }, null, { keepOrder: true });\n        if (editor.tabstopManager)\n            editor.tabstopManager.tabNext();\n    };\n    SnippetManager.prototype.$getScope = function (editor) {\n        var scope = editor.session.$mode.$id || \"\";\n        scope = scope.split(\"/\").pop();\n        if (scope === \"html\" || scope === \"php\") {\n            if (scope === \"php\" && !editor.session.$mode.inlinePhp)\n                scope = \"html\";\n            var c = editor.getCursorPosition();\n            var state = editor.session.getState(c.row);\n            if (typeof state === \"object\") {\n                state = state[0];\n            }\n            if (state.substring) {\n                if (state.substring(0, 3) == \"js-\")\n                    scope = \"javascript\";\n                else if (state.substring(0, 4) == \"css-\")\n                    scope = \"css\";\n                else if (state.substring(0, 4) == \"php-\")\n                    scope = \"php\";\n            }\n        }\n        return scope;\n    };\n    SnippetManager.prototype.getActiveScopes = function (editor) {\n        var scope = this.$getScope(editor);\n        var scopes = [scope];\n        var snippetMap = this.snippetMap;\n        if (snippetMap[scope] && snippetMap[scope].includeScopes) {\n            scopes.push.apply(scopes, snippetMap[scope].includeScopes);\n        }\n        scopes.push(\"_\");\n        return scopes;\n    };\n    SnippetManager.prototype.expandWithTab = function (editor, options) {\n        var self = this;\n        var result = editor.forEachSelection(function () {\n            return self.expandSnippetForSelection(editor, options);\n        }, null, { keepOrder: true });\n        if (result && editor.tabstopManager)\n            editor.tabstopManager.tabNext();\n        return result;\n    };\n    SnippetManager.prototype.expandSnippetForSelection = function (editor, options) {\n        var cursor = editor.getCursorPosition();\n        var line = editor.session.getLine(cursor.row);\n        var before = line.substring(0, cursor.column);\n        var after = line.substr(cursor.column);\n        var snippetMap = this.snippetMap;\n        var snippet;\n        this.getActiveScopes(editor).some(function (scope) {\n            var snippets = snippetMap[scope];\n            if (snippets)\n                snippet = this.findMatchingSnippet(snippets, before, after);\n            return !!snippet;\n        }, this);\n        if (!snippet)\n            return false;\n        if (options && options.dryRun)\n            return true;\n        editor.session.doc.removeInLine(cursor.row, cursor.column - snippet.replaceBefore.length, cursor.column + snippet.replaceAfter.length);\n        this.variables.M__ = snippet.matchBefore;\n        this.variables.T__ = snippet.matchAfter;\n        this.insertSnippetForSelection(editor, snippet.content);\n        this.variables.M__ = this.variables.T__ = null;\n        return true;\n    };\n    SnippetManager.prototype.findMatchingSnippet = function (snippetList, before, after) {\n        for (var i = snippetList.length; i--;) {\n            var s = snippetList[i];\n            if (s.startRe && !s.startRe.test(before))\n                continue;\n            if (s.endRe && !s.endRe.test(after))\n                continue;\n            if (!s.startRe && !s.endRe)\n                continue;\n            s.matchBefore = s.startRe ? s.startRe.exec(before) : [\"\"];\n            s.matchAfter = s.endRe ? s.endRe.exec(after) : [\"\"];\n            s.replaceBefore = s.triggerRe ? s.triggerRe.exec(before)[0] : \"\";\n            s.replaceAfter = s.endTriggerRe ? s.endTriggerRe.exec(after)[0] : \"\";\n            return s;\n        }\n    };\n    SnippetManager.prototype.register = function (snippets, scope) {\n        var snippetMap = this.snippetMap;\n        var snippetNameMap = this.snippetNameMap;\n        var self = this;\n        if (!snippets)\n            snippets = [];\n        function wrapRegexp(src) {\n            if (src && !/^\\^?\\(.*\\)\\$?$|^\\\\b$/.test(src))\n                src = \"(?:\" + src + \")\";\n            return src || \"\";\n        }\n        function guardedRegexp(re, guard, opening) {\n            re = wrapRegexp(re);\n            guard = wrapRegexp(guard);\n            if (opening) {\n                re = guard + re;\n                if (re && re[re.length - 1] != \"$\")\n                    re = re + \"$\";\n            }\n            else {\n                re = re + guard;\n                if (re && re[0] != \"^\")\n                    re = \"^\" + re;\n            }\n            return new RegExp(re);\n        }\n        function addSnippet(s) {\n            if (!s.scope)\n                s.scope = scope || \"_\";\n            scope = s.scope;\n            if (!snippetMap[scope]) {\n                snippetMap[scope] = [];\n                snippetNameMap[scope] = {};\n            }\n            var map = snippetNameMap[scope];\n            if (s.name) {\n                var old = map[s.name];\n                if (old)\n                    self.unregister(old);\n                map[s.name] = s;\n            }\n            snippetMap[scope].push(s);\n            if (s.prefix)\n                s.tabTrigger = s.prefix;\n            if (!s.content && s.body)\n                s.content = Array.isArray(s.body) ? s.body.join(\"\\n\") : s.body;\n            if (s.tabTrigger && !s.trigger) {\n                if (!s.guard && /^\\w/.test(s.tabTrigger))\n                    s.guard = \"\\\\b\";\n                s.trigger = lang.escapeRegExp(s.tabTrigger);\n            }\n            if (!s.trigger && !s.guard && !s.endTrigger && !s.endGuard)\n                return;\n            s.startRe = guardedRegexp(s.trigger, s.guard, true);\n            s.triggerRe = new RegExp(s.trigger);\n            s.endRe = guardedRegexp(s.endTrigger, s.endGuard, true);\n            s.endTriggerRe = new RegExp(s.endTrigger);\n        }\n        if (Array.isArray(snippets)) {\n            snippets.forEach(addSnippet);\n        }\n        else {\n            Object.keys(snippets).forEach(function (key) {\n                addSnippet(snippets[key]);\n            });\n        }\n        this._signal(\"registerSnippets\", { scope: scope });\n    };\n    SnippetManager.prototype.unregister = function (snippets, scope) {\n        var snippetMap = this.snippetMap;\n        var snippetNameMap = this.snippetNameMap;\n        function removeSnippet(s) {\n            var nameMap = snippetNameMap[s.scope || scope];\n            if (nameMap && nameMap[s.name]) {\n                delete nameMap[s.name];\n                var map = snippetMap[s.scope || scope];\n                var i = map && map.indexOf(s);\n                if (i >= 0)\n                    map.splice(i, 1);\n            }\n        }\n        if (snippets.content)\n            removeSnippet(snippets);\n        else if (Array.isArray(snippets))\n            snippets.forEach(removeSnippet);\n    };\n    SnippetManager.prototype.parseSnippetFile = function (str) {\n        str = str.replace(/\\r/g, \"\");\n        var list = [], /**@type{Snippet}*/ snippet = {};\n        var re = /^#.*|^({[\\s\\S]*})\\s*$|^(\\S+) (.*)$|^((?:\\n*\\t.*)+)/gm;\n        var m;\n        while (m = re.exec(str)) {\n            if (m[1]) {\n                try {\n                    snippet = JSON.parse(m[1]);\n                    list.push(snippet);\n                }\n                catch (e) { }\n            }\n            if (m[4]) {\n                snippet.content = m[4].replace(/^\\t/gm, \"\");\n                list.push(snippet);\n                snippet = {};\n            }\n            else {\n                var key = m[2], val = m[3];\n                if (key == \"regex\") {\n                    var guardRe = /\\/((?:[^\\/\\\\]|\\\\.)*)|$/g;\n                    snippet.guard = guardRe.exec(val)[1];\n                    snippet.trigger = guardRe.exec(val)[1];\n                    snippet.endTrigger = guardRe.exec(val)[1];\n                    snippet.endGuard = guardRe.exec(val)[1];\n                }\n                else if (key == \"snippet\") {\n                    snippet.tabTrigger = val.match(/^\\S*/)[0];\n                    if (!snippet.name)\n                        snippet.name = val;\n                }\n                else if (key) {\n                    snippet[key] = val;\n                }\n            }\n        }\n        return list;\n    };\n    SnippetManager.prototype.getSnippetByName = function (name, editor) {\n        var snippetMap = this.snippetNameMap;\n        var snippet;\n        this.getActiveScopes(editor).some(function (scope) {\n            var snippets = snippetMap[scope];\n            if (snippets)\n                snippet = snippets[name];\n            return !!snippet;\n        }, this);\n        return snippet;\n    };\n    return SnippetManager;\n}());\noop.implement(SnippetManager.prototype, EventEmitter);\nvar processSnippetText = function (editor, snippetText, options) {\n    if (options === void 0) { options = {}; }\n    var cursor = editor.getCursorPosition();\n    var line = editor.session.getLine(cursor.row);\n    var tabString = editor.session.getTabString();\n    var indentString = line.match(/^\\s*/)[0];\n    if (cursor.column < indentString.length)\n        indentString = indentString.slice(0, cursor.column);\n    snippetText = snippetText.replace(/\\r/g, \"\");\n    var tokens = this.tokenizeTmSnippet(snippetText);\n    tokens = this.resolveVariables(tokens, editor);\n    tokens = tokens.map(function (x) {\n        if (x == \"\\n\" && !options.excludeExtraIndent)\n            return x + indentString;\n        if (typeof x == \"string\")\n            return x.replace(/\\t/g, tabString);\n        return x;\n    });\n    var tabstops = [];\n    tokens.forEach(function (p, i) {\n        if (typeof p != \"object\")\n            return;\n        var id = p.tabstopId;\n        var ts = tabstops[id];\n        if (!ts) {\n            ts = tabstops[id] = [];\n            ts.index = id;\n            ts.value = \"\";\n            ts.parents = {};\n        }\n        if (ts.indexOf(p) !== -1)\n            return;\n        if (p.choices && !ts.choices)\n            ts.choices = p.choices;\n        ts.push(p);\n        var i1 = tokens.indexOf(p, i + 1);\n        if (i1 === -1)\n            return;\n        var value = tokens.slice(i + 1, i1);\n        var isNested = value.some(function (t) { return typeof t === \"object\"; });\n        if (isNested && !ts.value) {\n            ts.value = value;\n        }\n        else if (value.length && (!ts.value || typeof ts.value !== \"string\")) {\n            ts.value = value.join(\"\");\n        }\n    });\n    tabstops.forEach(function (ts) { ts.length = 0; });\n    var expanding = {};\n    function copyValue(val) {\n        var copy = [];\n        for (var i = 0; i < val.length; i++) {\n            var p = val[i];\n            if (typeof p == \"object\") {\n                if (expanding[p.tabstopId])\n                    continue;\n                var j = val.lastIndexOf(p, i - 1);\n                p = copy[j] || { tabstopId: p.tabstopId };\n            }\n            copy[i] = p;\n        }\n        return copy;\n    }\n    for (var i = 0; i < tokens.length; i++) {\n        var p = tokens[i];\n        if (typeof p != \"object\")\n            continue;\n        var id = p.tabstopId;\n        var ts = tabstops[id];\n        var i1 = tokens.indexOf(p, i + 1);\n        if (expanding[id]) {\n            if (expanding[id] === p) {\n                delete expanding[id];\n                Object.keys(expanding).forEach(function (parentId) {\n                    ts.parents[parentId] = true;\n                });\n            }\n            continue;\n        }\n        expanding[id] = p;\n        var value = ts.value;\n        if (typeof value !== \"string\")\n            value = copyValue(value);\n        else if (p.fmt)\n            value = this.tmStrFormat(value, p, editor);\n        tokens.splice.apply(tokens, [i + 1, Math.max(0, i1 - i)].concat(value, p));\n        if (ts.indexOf(p) === -1)\n            ts.push(p);\n    }\n    var row = 0, column = 0;\n    var text = \"\";\n    tokens.forEach(function (t) {\n        if (typeof t === \"string\") {\n            var lines = t.split(\"\\n\");\n            if (lines.length > 1) {\n                column = lines[lines.length - 1].length;\n                row += lines.length - 1;\n            }\n            else\n                column += t.length;\n            text += t;\n        }\n        else if (t) {\n            if (!t.start)\n                t.start = { row: row, column: column };\n            else\n                t.end = { row: row, column: column };\n        }\n    });\n    return {\n        text: text,\n        tabstops: tabstops,\n        tokens: tokens\n    };\n};\nvar TabstopManager = /** @class */ (function () {\n    function TabstopManager(editor) {\n        this.index = 0;\n        this.ranges = [];\n        this.tabstops = [];\n        if (editor.tabstopManager)\n            return editor.tabstopManager;\n        editor.tabstopManager = this;\n        this.$onChange = this.onChange.bind(this);\n        this.$onChangeSelection = lang.delayedCall(this.onChangeSelection.bind(this)).schedule;\n        this.$onChangeSession = this.onChangeSession.bind(this);\n        this.$onAfterExec = this.onAfterExec.bind(this);\n        this.attach(editor);\n    }\n    TabstopManager.prototype.attach = function (editor) {\n        this.$openTabstops = null;\n        this.selectedTabstop = null;\n        this.editor = editor;\n        this.session = editor.session;\n        this.editor.on(\"change\", this.$onChange);\n        this.editor.on(\"changeSelection\", this.$onChangeSelection);\n        this.editor.on(\"changeSession\", this.$onChangeSession);\n        this.editor.commands.on(\"afterExec\", this.$onAfterExec);\n        this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler);\n    };\n    TabstopManager.prototype.detach = function () {\n        this.tabstops.forEach(this.removeTabstopMarkers, this);\n        this.ranges.length = 0;\n        this.tabstops.length = 0;\n        this.selectedTabstop = null;\n        this.editor.off(\"change\", this.$onChange);\n        this.editor.off(\"changeSelection\", this.$onChangeSelection);\n        this.editor.off(\"changeSession\", this.$onChangeSession);\n        this.editor.commands.off(\"afterExec\", this.$onAfterExec);\n        this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler);\n        this.editor.tabstopManager = null;\n        this.session = null;\n        this.editor = null;\n    };\n    TabstopManager.prototype.onChange = function (delta) {\n        var isRemove = delta.action[0] == \"r\";\n        var selectedTabstop = this.selectedTabstop || {};\n        var parents = selectedTabstop.parents || {};\n        var tabstops = this.tabstops.slice();\n        for (var i = 0; i < tabstops.length; i++) {\n            var ts = tabstops[i];\n            var active = ts == selectedTabstop || parents[ts.index];\n            ts.rangeList.$bias = active ? 0 : 1;\n            if (delta.action == \"remove\" && ts !== selectedTabstop) {\n                var parentActive = ts.parents && ts.parents[selectedTabstop.index];\n                var startIndex = ts.rangeList.pointIndex(delta.start, parentActive);\n                startIndex = startIndex < 0 ? -startIndex - 1 : startIndex + 1;\n                var endIndex = ts.rangeList.pointIndex(delta.end, parentActive);\n                endIndex = endIndex < 0 ? -endIndex - 1 : endIndex - 1;\n                var toRemove = ts.rangeList.ranges.slice(startIndex, endIndex);\n                for (var j = 0; j < toRemove.length; j++)\n                    this.removeRange(toRemove[j]);\n            }\n            ts.rangeList.$onChange(delta);\n        }\n        var session = this.session;\n        if (!this.$inChange && isRemove && session.getLength() == 1 && !session.getValue())\n            this.detach();\n    };\n    TabstopManager.prototype.updateLinkedFields = function () {\n        var ts = this.selectedTabstop;\n        if (!ts || !ts.hasLinkedRanges || !ts.firstNonLinked)\n            return;\n        this.$inChange = true;\n        var session = this.session;\n        var text = session.getTextRange(ts.firstNonLinked);\n        for (var i = 0; i < ts.length; i++) {\n            var range = ts[i];\n            if (!range.linked)\n                continue;\n            var original = range.original;\n            var fmt = exports.snippetManager.tmStrFormat(text, original, this.editor);\n            session.replace(range, fmt);\n        }\n        this.$inChange = false;\n    };\n    TabstopManager.prototype.onAfterExec = function (e) {\n        if (e.command && !e.command.readOnly)\n            this.updateLinkedFields();\n    };\n    TabstopManager.prototype.onChangeSelection = function () {\n        if (!this.editor)\n            return;\n        var lead = this.editor.selection.lead;\n        var anchor = this.editor.selection.anchor;\n        var isEmpty = this.editor.selection.isEmpty();\n        for (var i = 0; i < this.ranges.length; i++) {\n            if (this.ranges[i].linked)\n                continue;\n            var containsLead = this.ranges[i].contains(lead.row, lead.column);\n            var containsAnchor = isEmpty || this.ranges[i].contains(anchor.row, anchor.column);\n            if (containsLead && containsAnchor)\n                return;\n        }\n        this.detach();\n    };\n    TabstopManager.prototype.onChangeSession = function () {\n        this.detach();\n    };\n    TabstopManager.prototype.tabNext = function (dir) {\n        var max = this.tabstops.length;\n        var index = this.index + (dir || 1);\n        index = Math.min(Math.max(index, 1), max);\n        if (index == max)\n            index = 0;\n        this.selectTabstop(index);\n        this.updateTabstopMarkers();\n        if (index === 0) {\n            this.detach();\n        }\n    };\n    TabstopManager.prototype.selectTabstop = function (index) {\n        this.$openTabstops = null;\n        var ts = this.tabstops[this.index];\n        if (ts)\n            this.addTabstopMarkers(ts);\n        this.index = index;\n        ts = this.tabstops[this.index];\n        if (!ts || !ts.length)\n            return;\n        this.selectedTabstop = ts;\n        var range = ts.firstNonLinked || ts;\n        if (ts.choices)\n            range.cursor = range.start;\n        if (!this.editor.inVirtualSelectionMode) {\n            var sel = this.editor.multiSelect;\n            sel.toSingleRange(range);\n            for (var i = 0; i < ts.length; i++) {\n                if (ts.hasLinkedRanges && ts[i].linked)\n                    continue;\n                sel.addRange(ts[i].clone(), true);\n            }\n        }\n        else {\n            this.editor.selection.fromOrientedRange(range);\n        }\n        this.editor.keyBinding.addKeyboardHandler(this.keyboardHandler);\n        if (this.selectedTabstop && this.selectedTabstop.choices)\n            this.editor.execCommand(\"startAutocomplete\", { matches: this.selectedTabstop.choices });\n    };\n    TabstopManager.prototype.addTabstops = function (tabstops, start, end) {\n        var useLink = this.useLink || !this.editor.getOption(\"enableMultiselect\");\n        if (!this.$openTabstops)\n            this.$openTabstops = [];\n        if (!tabstops[0]) {\n            var p = Range.fromPoints(end, end);\n            moveRelative(p.start, start);\n            moveRelative(p.end, start);\n            tabstops[0] = [p];\n            tabstops[0].index = 0;\n        }\n        var i = this.index;\n        var arg = [i + 1, 0];\n        var ranges = this.ranges;\n        var snippetId = this.snippetId = (this.snippetId || 0) + 1;\n        tabstops.forEach(function (ts, index) {\n            var dest = this.$openTabstops[index] || ts;\n            dest.snippetId = snippetId;\n            for (var i = 0; i < ts.length; i++) {\n                var p = ts[i];\n                var range = Range.fromPoints(p.start, p.end || p.start);\n                movePoint(range.start, start);\n                movePoint(range.end, start);\n                range.original = p;\n                range.tabstop = dest;\n                ranges.push(range);\n                if (dest != ts)\n                    dest.unshift(range);\n                else\n                    dest[i] = range;\n                if (p.fmtString || (dest.firstNonLinked && useLink)) {\n                    range.linked = true;\n                    dest.hasLinkedRanges = true;\n                }\n                else if (!dest.firstNonLinked)\n                    dest.firstNonLinked = range;\n            }\n            if (!dest.firstNonLinked)\n                dest.hasLinkedRanges = false;\n            if (dest === ts) {\n                arg.push(dest);\n                this.$openTabstops[index] = dest;\n            }\n            this.addTabstopMarkers(dest);\n            dest.rangeList = dest.rangeList || new RangeList();\n            dest.rangeList.$bias = 0;\n            dest.rangeList.addList(dest);\n        }, this);\n        if (arg.length > 2) {\n            if (this.tabstops.length)\n                arg.push(arg.splice(2, 1)[0]);\n            this.tabstops.splice.apply(this.tabstops, arg);\n        }\n    };\n    TabstopManager.prototype.addTabstopMarkers = function (ts) {\n        var session = this.session;\n        ts.forEach(function (range) {\n            if (!range.markerId)\n                range.markerId = session.addMarker(range, \"ace_snippet-marker\", \"text\");\n        });\n    };\n    TabstopManager.prototype.removeTabstopMarkers = function (ts) {\n        var session = this.session;\n        ts.forEach(function (range) {\n            session.removeMarker(range.markerId);\n            range.markerId = null;\n        });\n    };\n    TabstopManager.prototype.updateTabstopMarkers = function () {\n        if (!this.selectedTabstop)\n            return;\n        var currentSnippetId = this.selectedTabstop.snippetId;\n        if (this.selectedTabstop.index === 0) {\n            currentSnippetId--;\n        }\n        this.tabstops.forEach(function (ts) {\n            if (ts.snippetId === currentSnippetId)\n                this.addTabstopMarkers(ts);\n            else\n                this.removeTabstopMarkers(ts);\n        }, this);\n    };\n    TabstopManager.prototype.removeRange = function (range) {\n        var i = range.tabstop.indexOf(range);\n        if (i != -1)\n            range.tabstop.splice(i, 1);\n        i = this.ranges.indexOf(range);\n        if (i != -1)\n            this.ranges.splice(i, 1);\n        i = range.tabstop.rangeList.ranges.indexOf(range);\n        if (i != -1)\n            range.tabstop.splice(i, 1);\n        this.session.removeMarker(range.markerId);\n        if (!range.tabstop.length) {\n            i = this.tabstops.indexOf(range.tabstop);\n            if (i != -1)\n                this.tabstops.splice(i, 1);\n            if (!this.tabstops.length)\n                this.detach();\n        }\n    };\n    return TabstopManager;\n}());\nTabstopManager.prototype.keyboardHandler = new HashHandler();\nTabstopManager.prototype.keyboardHandler.bindKeys({\n    \"Tab\": function (editor) {\n        if (exports.snippetManager && exports.snippetManager.expandWithTab(editor))\n            return;\n        editor.tabstopManager.tabNext(1);\n        editor.renderer.scrollCursorIntoView();\n    },\n    \"Shift-Tab\": function (editor) {\n        editor.tabstopManager.tabNext(-1);\n        editor.renderer.scrollCursorIntoView();\n    },\n    \"Esc\": function (editor) {\n        editor.tabstopManager.detach();\n    }\n});\nvar movePoint = function (point, diff) {\n    if (point.row == 0)\n        point.column += diff.column;\n    point.row += diff.row;\n};\nvar moveRelative = function (point, start) {\n    if (point.row == start.row)\n        point.column -= start.column;\n    point.row -= start.row;\n};\ndom.importCssString(\"\\n.ace_snippet-marker {\\n    -moz-box-sizing: border-box;\\n    box-sizing: border-box;\\n    background: rgba(194, 193, 208, 0.09);\\n    border: 1px dotted rgba(211, 208, 235, 0.62);\\n    position: absolute;\\n}\", \"snippets.css\", false);\nexports.snippetManager = new SnippetManager();\nvar Editor = require(\"./editor\").Editor;\n(function () {\n    this.insertSnippet = function (content, options) {\n        return exports.snippetManager.insertSnippet(this, content, options);\n    };\n    this.expandSnippet = function (options) {\n        return exports.snippetManager.expandWithTab(this, options);\n    };\n}).call(Editor.prototype);\n\n});\n\nace.define(\"ace/autocomplete/popup\",[\"require\",\"exports\",\"module\",\"ace/virtual_renderer\",\"ace/editor\",\"ace/range\",\"ace/lib/event\",\"ace/lib/lang\",\"ace/lib/dom\",\"ace/config\",\"ace/lib/useragent\"], function(require, exports, module){\"use strict\";\nvar Renderer = require(\"../virtual_renderer\").VirtualRenderer;\nvar Editor = require(\"../editor\").Editor;\nvar Range = require(\"../range\").Range;\nvar event = require(\"../lib/event\");\nvar lang = require(\"../lib/lang\");\nvar dom = require(\"../lib/dom\");\nvar nls = require(\"../config\").nls;\nvar userAgent = require(\"./../lib/useragent\");\nvar getAriaId = function (index) {\n    return \"suggest-aria-id:\".concat(index);\n};\nvar popupAriaRole = userAgent.isSafari ? \"menu\" : \"listbox\";\nvar optionAriaRole = userAgent.isSafari ? \"menuitem\" : \"option\";\nvar ariaActiveState = userAgent.isSafari ? \"aria-current\" : \"aria-selected\";\nvar $singleLineEditor = function (el) {\n    var renderer = new Renderer(el);\n    renderer.$maxLines = 4;\n    var editor = new Editor(renderer);\n    editor.setHighlightActiveLine(false);\n    editor.setShowPrintMargin(false);\n    editor.renderer.setShowGutter(false);\n    editor.renderer.setHighlightGutterLine(false);\n    editor.$mouseHandler.$focusTimeout = 0;\n    editor.$highlightTagPending = true;\n    return editor;\n};\nvar AcePopup = /** @class */ (function () {\n    function AcePopup(parentNode) {\n        var el = dom.createElement(\"div\");\n        var popup = $singleLineEditor(el);\n        if (parentNode) {\n            parentNode.appendChild(el);\n        }\n        el.style.display = \"none\";\n        popup.renderer.content.style.cursor = \"default\";\n        popup.renderer.setStyle(\"ace_autocomplete\");\n        popup.renderer.$textLayer.element.setAttribute(\"role\", popupAriaRole);\n        popup.renderer.$textLayer.element.setAttribute(\"aria-roledescription\", nls(\"autocomplete.popup.aria-roledescription\", \"Autocomplete suggestions\"));\n        popup.renderer.$textLayer.element.setAttribute(\"aria-label\", nls(\"autocomplete.popup.aria-label\", \"Autocomplete suggestions\"));\n        popup.renderer.textarea.setAttribute(\"aria-hidden\", \"true\");\n        popup.setOption(\"displayIndentGuides\", false);\n        popup.setOption(\"dragDelay\", 150);\n        var noop = function () { };\n        popup.focus = noop;\n        popup.$isFocused = true;\n        popup.renderer.$cursorLayer.restartTimer = noop;\n        popup.renderer.$cursorLayer.element.style.opacity = \"0\";\n        popup.renderer.$maxLines = 8;\n        popup.renderer.$keepTextAreaAtCursor = false;\n        popup.setHighlightActiveLine(false);\n        popup.session.highlight(\"\");\n        popup.session.$searchHighlight.clazz = \"ace_highlight-marker\";\n        popup.on(\"mousedown\", function (e) {\n            var pos = e.getDocumentPosition();\n            popup.selection.moveToPosition(pos);\n            selectionMarker.start.row = selectionMarker.end.row = pos.row;\n            e.stop();\n        });\n        var lastMouseEvent;\n        var hoverMarker = new Range(-1, 0, -1, Infinity);\n        var selectionMarker = new Range(-1, 0, -1, Infinity);\n        selectionMarker.id = popup.session.addMarker(selectionMarker, \"ace_active-line\", \"fullLine\");\n        popup.setSelectOnHover = function (val) {\n            if (!val) {\n                hoverMarker.id = popup.session.addMarker(hoverMarker, \"ace_line-hover\", \"fullLine\");\n            }\n            else if (hoverMarker.id) {\n                popup.session.removeMarker(hoverMarker.id);\n                hoverMarker.id = null;\n            }\n        };\n        popup.setSelectOnHover(false);\n        popup.on(\"mousemove\", function (e) {\n            if (!lastMouseEvent) {\n                lastMouseEvent = e;\n                return;\n            }\n            if (lastMouseEvent.x == e.x && lastMouseEvent.y == e.y) {\n                return;\n            }\n            lastMouseEvent = e;\n            lastMouseEvent.scrollTop = popup.renderer.scrollTop;\n            popup.isMouseOver = true;\n            var row = lastMouseEvent.getDocumentPosition().row;\n            if (hoverMarker.start.row != row) {\n                if (!hoverMarker.id)\n                    popup.setRow(row);\n                setHoverMarker(row);\n            }\n        });\n        popup.renderer.on(\"beforeRender\", function () {\n            if (lastMouseEvent && hoverMarker.start.row != -1) {\n                lastMouseEvent.$pos = null;\n                var row = lastMouseEvent.getDocumentPosition().row;\n                if (!hoverMarker.id)\n                    popup.setRow(row);\n                setHoverMarker(row, true);\n            }\n        });\n        popup.renderer.on(\"afterRender\", function () {\n            var t = popup.renderer.$textLayer;\n            for (var row = t.config.firstRow, l = t.config.lastRow; row <= l; row++) {\n                var popupRowElement = /** @type {HTMLElement|null} */ (t.element.childNodes[row - t.config.firstRow]);\n                popupRowElement.setAttribute(\"role\", optionAriaRole);\n                popupRowElement.setAttribute(\"aria-roledescription\", nls(\"autocomplete.popup.item.aria-roledescription\", \"item\"));\n                popupRowElement.setAttribute(\"aria-setsize\", popup.data.length);\n                popupRowElement.setAttribute(\"aria-describedby\", \"doc-tooltip\");\n                popupRowElement.setAttribute(\"aria-posinset\", row + 1);\n                var rowData = popup.getData(row);\n                if (rowData) {\n                    var ariaLabel = \"\".concat(rowData.caption || rowData.value).concat(rowData.meta ? \", \".concat(rowData.meta) : '');\n                    popupRowElement.setAttribute(\"aria-label\", ariaLabel);\n                }\n                var highlightedSpans = popupRowElement.querySelectorAll(\".ace_completion-highlight\");\n                highlightedSpans.forEach(function (span) {\n                    span.setAttribute(\"role\", \"mark\");\n                });\n            }\n        });\n        popup.renderer.on(\"afterRender\", function () {\n            var row = popup.getRow();\n            var t = popup.renderer.$textLayer;\n            var selected = /** @type {HTMLElement|null} */ (t.element.childNodes[row - t.config.firstRow]);\n            var el = document.activeElement; // Active element is textarea of main editor\n            if (selected !== popup.selectedNode && popup.selectedNode) {\n                dom.removeCssClass(popup.selectedNode, \"ace_selected\");\n                popup.selectedNode.removeAttribute(ariaActiveState);\n                popup.selectedNode.removeAttribute(\"id\");\n            }\n            el.removeAttribute(\"aria-activedescendant\");\n            popup.selectedNode = selected;\n            if (selected) {\n                var ariaId = getAriaId(row);\n                dom.addCssClass(selected, \"ace_selected\");\n                selected.id = ariaId;\n                t.element.setAttribute(\"aria-activedescendant\", ariaId);\n                el.setAttribute(\"aria-activedescendant\", ariaId);\n                selected.setAttribute(ariaActiveState, \"true\");\n            }\n        });\n        var hideHoverMarker = function () { setHoverMarker(-1); };\n        var setHoverMarker = function (row, suppressRedraw) {\n            if (row !== hoverMarker.start.row) {\n                hoverMarker.start.row = hoverMarker.end.row = row;\n                if (!suppressRedraw)\n                    popup.session._emit(\"changeBackMarker\");\n                popup._emit(\"changeHoverMarker\");\n            }\n        };\n        popup.getHoveredRow = function () {\n            return hoverMarker.start.row;\n        };\n        event.addListener(popup.container, \"mouseout\", function () {\n            popup.isMouseOver = false;\n            hideHoverMarker();\n        });\n        popup.on(\"hide\", hideHoverMarker);\n        popup.on(\"changeSelection\", hideHoverMarker);\n        popup.session.doc.getLength = function () {\n            return popup.data.length;\n        };\n        popup.session.doc.getLine = function (i) {\n            var data = popup.data[i];\n            if (typeof data == \"string\")\n                return data;\n            return (data && data.value) || \"\";\n        };\n        var bgTokenizer = popup.session.bgTokenizer;\n        bgTokenizer.$tokenizeRow = function (row) {\n            var data = popup.data[row];\n            var tokens = [];\n            if (!data)\n                return tokens;\n            if (typeof data == \"string\")\n                data = { value: data };\n            var caption = data.caption || data.value || data.name;\n            function addToken(value, className) {\n                value && tokens.push({\n                    type: (data.className || \"\") + (className || \"\"),\n                    value: value\n                });\n            }\n            var lower = caption.toLowerCase();\n            var filterText = (popup.filterText || \"\").toLowerCase();\n            var lastIndex = 0;\n            var lastI = 0;\n            for (var i = 0; i <= filterText.length; i++) {\n                if (i != lastI && (data.matchMask & (1 << i) || i == filterText.length)) {\n                    var sub = filterText.slice(lastI, i);\n                    lastI = i;\n                    var index = lower.indexOf(sub, lastIndex);\n                    if (index == -1)\n                        continue;\n                    addToken(caption.slice(lastIndex, index), \"\");\n                    lastIndex = index + sub.length;\n                    addToken(caption.slice(index, lastIndex), \"completion-highlight\");\n                }\n            }\n            addToken(caption.slice(lastIndex, caption.length), \"\");\n            tokens.push({ type: \"completion-spacer\", value: \" \" });\n            if (data.meta)\n                tokens.push({ type: \"completion-meta\", value: data.meta });\n            if (data.message)\n                tokens.push({ type: \"completion-message\", value: data.message });\n            return tokens;\n        };\n        bgTokenizer.$updateOnChange = noop;\n        bgTokenizer.start = noop;\n        popup.session.$computeWidth = function () {\n            return this.screenWidth = 0;\n        };\n        popup.isOpen = false;\n        popup.isTopdown = false;\n        popup.autoSelect = true;\n        popup.filterText = \"\";\n        popup.isMouseOver = false;\n        popup.data = [];\n        popup.setData = function (list, filterText) {\n            popup.filterText = filterText || \"\";\n            popup.setValue(lang.stringRepeat(\"\\n\", list.length), -1);\n            popup.data = list || [];\n            popup.setRow(0);\n        };\n        popup.getData = function (row) {\n            return popup.data[row];\n        };\n        popup.getRow = function () {\n            return selectionMarker.start.row;\n        };\n        popup.setRow = function (line) {\n            line = Math.max(this.autoSelect ? 0 : -1, Math.min(this.data.length - 1, line));\n            if (selectionMarker.start.row != line) {\n                popup.selection.clearSelection();\n                selectionMarker.start.row = selectionMarker.end.row = line || 0;\n                popup.session._emit(\"changeBackMarker\");\n                popup.moveCursorTo(line || 0, 0);\n                if (popup.isOpen)\n                    popup._signal(\"select\");\n            }\n        };\n        popup.on(\"changeSelection\", function () {\n            if (popup.isOpen)\n                popup.setRow(popup.selection.lead.row);\n            popup.renderer.scrollCursorIntoView();\n        });\n        popup.hide = function () {\n            this.container.style.display = \"none\";\n            popup.anchorPos = null;\n            popup.anchor = null;\n            if (popup.isOpen) {\n                popup.isOpen = false;\n                this._signal(\"hide\");\n            }\n        };\n        popup.tryShow = function (pos, lineHeight, anchor, forceShow) {\n            if (!forceShow && popup.isOpen && popup.anchorPos && popup.anchor &&\n                popup.anchorPos.top === pos.top && popup.anchorPos.left === pos.left &&\n                popup.anchor === anchor) {\n                return true;\n            }\n            var el = this.container;\n            var scrollBarSize = this.renderer.scrollBar.width || 10;\n            var screenHeight = window.innerHeight - scrollBarSize;\n            var screenWidth = window.innerWidth - scrollBarSize;\n            var renderer = this.renderer;\n            var maxH = renderer.$maxLines * lineHeight * 1.4;\n            var dims = { top: 0, bottom: 0, left: 0 };\n            var spaceBelow = screenHeight - pos.top - 3 * this.$borderSize - lineHeight;\n            var spaceAbove = pos.top - 3 * this.$borderSize;\n            if (!anchor) {\n                if (spaceAbove <= spaceBelow || spaceBelow >= maxH) {\n                    anchor = \"bottom\";\n                }\n                else {\n                    anchor = \"top\";\n                }\n            }\n            if (anchor === \"top\") {\n                dims.bottom = pos.top - this.$borderSize;\n                dims.top = dims.bottom - maxH;\n            }\n            else if (anchor === \"bottom\") {\n                dims.top = pos.top + lineHeight + this.$borderSize;\n                dims.bottom = dims.top + maxH;\n            }\n            var fitsX = dims.top >= 0 && dims.bottom <= screenHeight;\n            if (!forceShow && !fitsX) {\n                return false;\n            }\n            if (!fitsX) {\n                if (anchor === \"top\") {\n                    renderer.$maxPixelHeight = spaceAbove;\n                }\n                else {\n                    renderer.$maxPixelHeight = spaceBelow;\n                }\n            }\n            else {\n                renderer.$maxPixelHeight = null;\n            }\n            if (anchor === \"top\") {\n                el.style.top = \"\";\n                el.style.bottom = (screenHeight + scrollBarSize - dims.bottom) + \"px\";\n                popup.isTopdown = false;\n            }\n            else {\n                el.style.top = dims.top + \"px\";\n                el.style.bottom = \"\";\n                popup.isTopdown = true;\n            }\n            el.style.display = \"\";\n            var left = pos.left;\n            if (left + el.offsetWidth > screenWidth)\n                left = screenWidth - el.offsetWidth;\n            el.style.left = left + \"px\";\n            el.style.right = \"\";\n            if (!popup.isOpen) {\n                popup.isOpen = true;\n                this._signal(\"show\");\n                lastMouseEvent = null;\n            }\n            popup.anchorPos = pos;\n            popup.anchor = anchor;\n            return true;\n        };\n        popup.show = function (pos, lineHeight, topdownOnly) {\n            this.tryShow(pos, lineHeight, topdownOnly ? \"bottom\" : undefined, true);\n        };\n        popup.goTo = function (where) {\n            var row = this.getRow();\n            var max = this.session.getLength() - 1;\n            switch (where) {\n                case \"up\":\n                    row = row <= 0 ? max : row - 1;\n                    break;\n                case \"down\":\n                    row = row >= max ? -1 : row + 1;\n                    break;\n                case \"start\":\n                    row = 0;\n                    break;\n                case \"end\":\n                    row = max;\n                    break;\n            }\n            this.setRow(row);\n        };\n        popup.getTextLeftOffset = function () {\n            return this.$borderSize + this.renderer.$padding + this.$imageSize;\n        };\n        popup.$imageSize = 0;\n        popup.$borderSize = 1;\n        return popup;\n    }\n    return AcePopup;\n}());\ndom.importCssString(\"\\n.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\\n    background-color: #CAD6FA;\\n    z-index: 1;\\n}\\n.ace_dark.ace_editor.ace_autocomplete .ace_marker-layer .ace_active-line {\\n    background-color: #3a674e;\\n}\\n.ace_editor.ace_autocomplete .ace_line-hover {\\n    border: 1px solid #abbffe;\\n    margin-top: -1px;\\n    background: rgba(233,233,253,0.4);\\n    position: absolute;\\n    z-index: 2;\\n}\\n.ace_dark.ace_editor.ace_autocomplete .ace_line-hover {\\n    border: 1px solid rgba(109, 150, 13, 0.8);\\n    background: rgba(58, 103, 78, 0.62);\\n}\\n.ace_completion-meta {\\n    opacity: 0.5;\\n    margin-left: 0.9em;\\n}\\n.ace_completion-message {\\n    margin-left: 0.9em;\\n    color: blue;\\n}\\n.ace_editor.ace_autocomplete .ace_completion-highlight{\\n    color: #2d69c7;\\n}\\n.ace_dark.ace_editor.ace_autocomplete .ace_completion-highlight{\\n    color: #93ca12;\\n}\\n.ace_editor.ace_autocomplete {\\n    width: 300px;\\n    z-index: 200000;\\n    border: 1px lightgray solid;\\n    position: fixed;\\n    box-shadow: 2px 3px 5px rgba(0,0,0,.2);\\n    line-height: 1.4;\\n    background: #fefefe;\\n    color: #111;\\n}\\n.ace_dark.ace_editor.ace_autocomplete {\\n    border: 1px #484747 solid;\\n    box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.51);\\n    line-height: 1.4;\\n    background: #25282c;\\n    color: #c1c1c1;\\n}\\n.ace_autocomplete .ace_text-layer  {\\n    width: calc(100% - 8px);\\n}\\n.ace_autocomplete .ace_line {\\n    display: flex;\\n    align-items: center;\\n}\\n.ace_autocomplete .ace_line > * {\\n    min-width: 0;\\n    flex: 0 0 auto;\\n}\\n.ace_autocomplete .ace_line .ace_ {\\n    flex: 0 1 auto;\\n    overflow: hidden;\\n    text-overflow: ellipsis;\\n}\\n.ace_autocomplete .ace_completion-spacer {\\n    flex: 1;\\n}\\n.ace_autocomplete.ace_loading:after  {\\n    content: \\\"\\\";\\n    position: absolute;\\n    top: 0px;\\n    height: 2px;\\n    width: 8%;\\n    background: blue;\\n    z-index: 100;\\n    animation: ace_progress 3s infinite linear;\\n    animation-delay: 300ms;\\n    transform: translateX(-100%) scaleX(1);\\n}\\n@keyframes ace_progress {\\n    0% { transform: translateX(-100%) scaleX(1) }\\n    50% { transform: translateX(625%) scaleX(2) } \\n    100% { transform: translateX(1500%) scaleX(3) } \\n}\\n@media (prefers-reduced-motion) {\\n    .ace_autocomplete.ace_loading:after {\\n        transform: translateX(625%) scaleX(2);\\n        animation: none;\\n     }\\n}\\n\", \"autocompletion.css\", false);\nexports.AcePopup = AcePopup;\nexports.$singleLineEditor = $singleLineEditor;\nexports.getAriaId = getAriaId;\n\n});\n\nace.define(\"ace/autocomplete/inline_screenreader\",[\"require\",\"exports\",\"module\"], function(require, exports, module){\"use strict\";\nvar AceInlineScreenReader = /** @class */ (function () {\n    function AceInlineScreenReader(editor) {\n        this.editor = editor;\n        this.screenReaderDiv = document.createElement(\"div\");\n        this.screenReaderDiv.classList.add(\"ace_screenreader-only\");\n        this.editor.container.appendChild(this.screenReaderDiv);\n    }\n    AceInlineScreenReader.prototype.setScreenReaderContent = function (content) {\n        if (!this.popup && this.editor.completer && /**@type{import(\"../autocomplete\").Autocomplete}*/ (this.editor.completer).popup) {\n            this.popup = /**@type{import(\"../autocomplete\").Autocomplete}*/ (this.editor.completer).popup;\n            this.popup.renderer.on(\"afterRender\", function () {\n                var row = this.popup.getRow();\n                var t = this.popup.renderer.$textLayer;\n                var selected = t.element.childNodes[row - t.config.firstRow];\n                if (selected) {\n                    var idString = \"doc-tooltip \";\n                    for (var lineIndex = 0; lineIndex < this._lines.length; lineIndex++) {\n                        idString += \"ace-inline-screenreader-line-\".concat(lineIndex, \" \");\n                    }\n                    selected.setAttribute(\"aria-describedby\", idString);\n                }\n            }.bind(this));\n        }\n        while (this.screenReaderDiv.firstChild) {\n            this.screenReaderDiv.removeChild(this.screenReaderDiv.firstChild);\n        }\n        this._lines = content.split(/\\r\\n|\\r|\\n/);\n        var codeElement = this.createCodeBlock();\n        this.screenReaderDiv.appendChild(codeElement);\n    };\n    AceInlineScreenReader.prototype.destroy = function () {\n        this.screenReaderDiv.remove();\n    };\n    AceInlineScreenReader.prototype.createCodeBlock = function () {\n        var container = document.createElement(\"pre\");\n        container.setAttribute(\"id\", \"ace-inline-screenreader\");\n        for (var lineIndex = 0; lineIndex < this._lines.length; lineIndex++) {\n            var codeElement = document.createElement(\"code\");\n            codeElement.setAttribute(\"id\", \"ace-inline-screenreader-line-\".concat(lineIndex));\n            var line = document.createTextNode(this._lines[lineIndex]);\n            codeElement.appendChild(line);\n            container.appendChild(codeElement);\n        }\n        return container;\n    };\n    return AceInlineScreenReader;\n}());\nexports.AceInlineScreenReader = AceInlineScreenReader;\n\n});\n\nace.define(\"ace/autocomplete/inline\",[\"require\",\"exports\",\"module\",\"ace/snippets\",\"ace/autocomplete/inline_screenreader\"], function(require, exports, module){\"use strict\";\nvar snippetManager = require(\"../snippets\").snippetManager;\nvar AceInlineScreenReader = require(\"./inline_screenreader\").AceInlineScreenReader;\nvar AceInline = /** @class */ (function () {\n    function AceInline() {\n        this.editor = null;\n    }\n    AceInline.prototype.show = function (editor, completion, prefix) {\n        prefix = prefix || \"\";\n        if (editor && this.editor && this.editor !== editor) {\n            this.hide();\n            this.editor = null;\n            this.inlineScreenReader = null;\n        }\n        if (!editor || !completion) {\n            return false;\n        }\n        if (!this.inlineScreenReader) {\n            this.inlineScreenReader = new AceInlineScreenReader(editor);\n        }\n        var displayText = completion.snippet ? snippetManager.getDisplayTextForSnippet(editor, completion.snippet) : completion.value;\n        if (completion.hideInlinePreview || !displayText || !displayText.startsWith(prefix)) {\n            return false;\n        }\n        this.editor = editor;\n        this.inlineScreenReader.setScreenReaderContent(displayText);\n        displayText = displayText.slice(prefix.length);\n        if (displayText === \"\") {\n            editor.removeGhostText();\n        }\n        else {\n            editor.setGhostText(displayText);\n        }\n        return true;\n    };\n    AceInline.prototype.isOpen = function () {\n        if (!this.editor) {\n            return false;\n        }\n        return !!this.editor.renderer.$ghostText;\n    };\n    AceInline.prototype.hide = function () {\n        if (!this.editor) {\n            return false;\n        }\n        this.editor.removeGhostText();\n        return true;\n    };\n    AceInline.prototype.destroy = function () {\n        this.hide();\n        this.editor = null;\n        if (this.inlineScreenReader) {\n            this.inlineScreenReader.destroy();\n            this.inlineScreenReader = null;\n        }\n    };\n    return AceInline;\n}());\nexports.AceInline = AceInline;\n\n});\n\nace.define(\"ace/autocomplete/util\",[\"require\",\"exports\",\"module\"], function(require, exports, module){\"use strict\";\nexports.parForEach = function (array, fn, callback) {\n    var completed = 0;\n    var arLength = array.length;\n    if (arLength === 0)\n        callback();\n    for (var i = 0; i < arLength; i++) {\n        fn(array[i], function (result, err) {\n            completed++;\n            if (completed === arLength)\n                callback(result, err);\n        });\n    }\n};\nvar ID_REGEX = /[a-zA-Z_0-9\\$\\-\\u00A2-\\u2000\\u2070-\\uFFFF]/;\nexports.retrievePrecedingIdentifier = function (text, pos, regex) {\n    regex = regex || ID_REGEX;\n    var buf = [];\n    for (var i = pos - 1; i >= 0; i--) {\n        if (regex.test(text[i]))\n            buf.push(text[i]);\n        else\n            break;\n    }\n    return buf.reverse().join(\"\");\n};\nexports.retrieveFollowingIdentifier = function (text, pos, regex) {\n    regex = regex || ID_REGEX;\n    var buf = [];\n    for (var i = pos; i < text.length; i++) {\n        if (regex.test(text[i]))\n            buf.push(text[i]);\n        else\n            break;\n    }\n    return buf;\n};\nexports.getCompletionPrefix = function (editor) {\n    var pos = editor.getCursorPosition();\n    var line = editor.session.getLine(pos.row);\n    var prefix;\n    editor.completers.forEach(function (completer) {\n        if (completer.identifierRegexps) {\n            completer.identifierRegexps.forEach(function (identifierRegex) {\n                if (!prefix && identifierRegex)\n                    prefix = this.retrievePrecedingIdentifier(line, pos.column, identifierRegex);\n            }.bind(this));\n        }\n    }.bind(this));\n    return prefix || this.retrievePrecedingIdentifier(line, pos.column);\n};\nexports.triggerAutocomplete = function (editor, previousChar) {\n    var previousChar = previousChar == null\n        ? editor.session.getPrecedingCharacter()\n        : previousChar;\n    return editor.completers.some(function (completer) {\n        if (completer.triggerCharacters && Array.isArray(completer.triggerCharacters)) {\n            return completer.triggerCharacters.includes(previousChar);\n        }\n    });\n};\n\n});\n\nace.define(\"ace/autocomplete\",[\"require\",\"exports\",\"module\",\"ace/keyboard/hash_handler\",\"ace/autocomplete/popup\",\"ace/autocomplete/inline\",\"ace/autocomplete/popup\",\"ace/autocomplete/util\",\"ace/lib/lang\",\"ace/lib/dom\",\"ace/snippets\",\"ace/config\",\"ace/lib/event\",\"ace/lib/scroll\"], function(require, exports, module){\"use strict\";\nvar HashHandler = require(\"./keyboard/hash_handler\").HashHandler;\nvar AcePopup = require(\"./autocomplete/popup\").AcePopup;\nvar AceInline = require(\"./autocomplete/inline\").AceInline;\nvar getAriaId = require(\"./autocomplete/popup\").getAriaId;\nvar util = require(\"./autocomplete/util\");\nvar lang = require(\"./lib/lang\");\nvar dom = require(\"./lib/dom\");\nvar snippetManager = require(\"./snippets\").snippetManager;\nvar config = require(\"./config\");\nvar event = require(\"./lib/event\");\nvar preventParentScroll = require(\"./lib/scroll\").preventParentScroll;\nvar destroyCompleter = function (e, editor) {\n    editor.completer && editor.completer.destroy();\n};\nvar Autocomplete = /** @class */ (function () {\n    function Autocomplete() {\n        this.autoInsert = false;\n        this.autoSelect = true;\n        this.autoShown = false;\n        this.exactMatch = false;\n        this.inlineEnabled = false;\n        this.keyboardHandler = new HashHandler();\n        this.keyboardHandler.bindKeys(this.commands);\n        this.parentNode = null;\n        this.setSelectOnHover = false;\n        this.hasSeen = new Set();\n        this.showLoadingState = false;\n        this.stickySelectionDelay = 500;\n        this.blurListener = this.blurListener.bind(this);\n        this.changeListener = this.changeListener.bind(this);\n        this.mousedownListener = this.mousedownListener.bind(this);\n        this.mousewheelListener = this.mousewheelListener.bind(this);\n        this.onLayoutChange = this.onLayoutChange.bind(this);\n        this.changeTimer = lang.delayedCall(function () {\n            this.updateCompletions(true);\n        }.bind(this));\n        this.tooltipTimer = lang.delayedCall(this.updateDocTooltip.bind(this), 50);\n        this.popupTimer = lang.delayedCall(this.$updatePopupPosition.bind(this), 50);\n        this.stickySelectionTimer = lang.delayedCall(function () {\n            this.stickySelection = true;\n        }.bind(this), this.stickySelectionDelay);\n        this.$firstOpenTimer = lang.delayedCall(/**@this{Autocomplete}*/ function () {\n            var initialPosition = this.completionProvider && this.completionProvider.initialPosition;\n            if (this.autoShown || (this.popup && this.popup.isOpen) || !initialPosition || this.editor.completers.length === 0)\n                return;\n            this.completions = new FilteredList(Autocomplete.completionsForLoading);\n            this.openPopup(this.editor, initialPosition.prefix, false);\n            this.popup.renderer.setStyle(\"ace_loading\", true);\n        }.bind(this), this.stickySelectionDelay);\n    }\n    Object.defineProperty(Autocomplete, \"completionsForLoading\", {\n        get: function () {\n            return [{\n                    caption: config.nls(\"autocomplete.loading\", \"Loading...\"),\n                    value: \"\"\n                }];\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Autocomplete.prototype.$init = function () {\n        this.popup = new AcePopup(this.parentNode || document.body || document.documentElement);\n        this.popup.on(\"click\", function (e) {\n            this.insertMatch();\n            e.stop();\n        }.bind(this));\n        this.popup.focus = this.editor.focus.bind(this.editor);\n        this.popup.on(\"show\", this.$onPopupShow.bind(this));\n        this.popup.on(\"hide\", this.$onHidePopup.bind(this));\n        this.popup.on(\"select\", this.$onPopupChange.bind(this));\n        event.addListener(this.popup.container, \"mouseout\", this.mouseOutListener.bind(this));\n        this.popup.on(\"changeHoverMarker\", this.tooltipTimer.bind(null, null));\n        this.popup.renderer.on(\"afterRender\", this.$onPopupRender.bind(this));\n        return this.popup;\n    };\n    Autocomplete.prototype.$initInline = function () {\n        if (!this.inlineEnabled || this.inlineRenderer)\n            return;\n        this.inlineRenderer = new AceInline();\n        return this.inlineRenderer;\n    };\n    Autocomplete.prototype.getPopup = function () {\n        return this.popup || this.$init();\n    };\n    Autocomplete.prototype.$onHidePopup = function () {\n        if (this.inlineRenderer) {\n            this.inlineRenderer.hide();\n        }\n        this.hideDocTooltip();\n        this.stickySelectionTimer.cancel();\n        this.popupTimer.cancel();\n        this.stickySelection = false;\n    };\n    Autocomplete.prototype.$seen = function (completion) {\n        if (!this.hasSeen.has(completion) && completion && completion.completer && completion.completer.onSeen && typeof completion.completer.onSeen === \"function\") {\n            completion.completer.onSeen(this.editor, completion);\n            this.hasSeen.add(completion);\n        }\n    };\n    Autocomplete.prototype.$onPopupChange = function (hide) {\n        if (this.inlineRenderer && this.inlineEnabled) {\n            var completion = hide ? null : this.popup.getData(this.popup.getRow());\n            this.$updateGhostText(completion);\n            if (this.popup.isMouseOver && this.setSelectOnHover) {\n                this.tooltipTimer.call(null, null);\n                return;\n            }\n            this.popupTimer.schedule();\n            this.tooltipTimer.schedule();\n        }\n        else {\n            this.popupTimer.call(null, null);\n            this.tooltipTimer.call(null, null);\n        }\n    };\n    Autocomplete.prototype.$updateGhostText = function (completion) {\n        var row = this.base.row;\n        var column = this.base.column;\n        var cursorColumn = this.editor.getCursorPosition().column;\n        var prefix = this.editor.session.getLine(row).slice(column, cursorColumn);\n        if (!this.inlineRenderer.show(this.editor, completion, prefix)) {\n            this.inlineRenderer.hide();\n        }\n        else {\n            this.$seen(completion);\n        }\n    };\n    Autocomplete.prototype.$onPopupRender = function () {\n        var inlineEnabled = this.inlineRenderer && this.inlineEnabled;\n        if (this.completions && this.completions.filtered && this.completions.filtered.length > 0) {\n            for (var i = this.popup.getFirstVisibleRow(); i <= this.popup.getLastVisibleRow(); i++) {\n                var completion = this.popup.getData(i);\n                if (completion && (!inlineEnabled || completion.hideInlinePreview)) {\n                    this.$seen(completion);\n                }\n            }\n        }\n    };\n    Autocomplete.prototype.$onPopupShow = function (hide) {\n        this.$onPopupChange(hide);\n        this.stickySelection = false;\n        if (this.stickySelectionDelay >= 0)\n            this.stickySelectionTimer.schedule(this.stickySelectionDelay);\n    };\n    Autocomplete.prototype.observeLayoutChanges = function () {\n        if (this.$elements || !this.editor)\n            return;\n        window.addEventListener(\"resize\", this.onLayoutChange, { passive: true });\n        window.addEventListener(\"wheel\", this.mousewheelListener);\n        var el = this.editor.container.parentNode;\n        var elements = [];\n        while (el) {\n            elements.push(el);\n            el.addEventListener(\"scroll\", this.onLayoutChange, { passive: true });\n            el = el.parentNode;\n        }\n        this.$elements = elements;\n    };\n    Autocomplete.prototype.unObserveLayoutChanges = function () {\n        var _this = this;\n        window.removeEventListener(\"resize\", this.onLayoutChange, { passive: true });\n        window.removeEventListener(\"wheel\", this.mousewheelListener);\n        this.$elements && this.$elements.forEach(function (el) {\n            el.removeEventListener(\"scroll\", _this.onLayoutChange, { passive: true });\n        });\n        this.$elements = null;\n    };\n    Autocomplete.prototype.onLayoutChange = function () {\n        if (!this.popup.isOpen)\n            return this.unObserveLayoutChanges();\n        this.$updatePopupPosition();\n        this.updateDocTooltip();\n    };\n    Autocomplete.prototype.$updatePopupPosition = function () {\n        var editor = this.editor;\n        var renderer = editor.renderer;\n        var lineHeight = renderer.layerConfig.lineHeight;\n        var pos = renderer.$cursorLayer.getPixelPosition(this.base, true);\n        pos.left -= this.popup.getTextLeftOffset();\n        var rect = editor.container.getBoundingClientRect();\n        pos.top += rect.top - renderer.layerConfig.offset;\n        pos.left += rect.left - editor.renderer.scrollLeft;\n        pos.left += renderer.gutterWidth;\n        var posGhostText = {\n            top: pos.top,\n            left: pos.left\n        };\n        if (renderer.$ghostText && renderer.$ghostTextWidget) {\n            if (this.base.row === renderer.$ghostText.position.row) {\n                posGhostText.top += renderer.$ghostTextWidget.el.offsetHeight;\n            }\n        }\n        var editorContainerBottom = editor.container.getBoundingClientRect().bottom - lineHeight;\n        var lowestPosition = editorContainerBottom < posGhostText.top ?\n            { top: editorContainerBottom, left: posGhostText.left } :\n            posGhostText;\n        if (this.popup.tryShow(lowestPosition, lineHeight, \"bottom\")) {\n            return;\n        }\n        if (this.popup.tryShow(pos, lineHeight, \"top\")) {\n            return;\n        }\n        this.popup.show(pos, lineHeight);\n    };\n    Autocomplete.prototype.openPopup = function (editor, prefix, keepPopupPosition) {\n        this.$firstOpenTimer.cancel();\n        if (!this.popup)\n            this.$init();\n        if (this.inlineEnabled && !this.inlineRenderer)\n            this.$initInline();\n        this.popup.autoSelect = this.autoSelect;\n        this.popup.setSelectOnHover(this.setSelectOnHover);\n        var oldRow = this.popup.getRow();\n        var previousSelectedItem = this.popup.data[oldRow];\n        this.popup.setData(this.completions.filtered, this.completions.filterText);\n        if (this.editor.textInput.setAriaOptions) {\n            this.editor.textInput.setAriaOptions({\n                activeDescendant: getAriaId(this.popup.getRow()),\n                inline: this.inlineEnabled\n            });\n        }\n        editor.keyBinding.addKeyboardHandler(this.keyboardHandler);\n        var newRow;\n        if (this.stickySelection)\n            newRow = this.popup.data.indexOf(previousSelectedItem);\n        if (!newRow || newRow === -1)\n            newRow = 0;\n        this.popup.setRow(this.autoSelect ? newRow : -1);\n        if (newRow === oldRow && previousSelectedItem !== this.completions.filtered[newRow])\n            this.$onPopupChange();\n        var inlineEnabled = this.inlineRenderer && this.inlineEnabled;\n        if (newRow === oldRow && inlineEnabled) {\n            var completion = this.popup.getData(this.popup.getRow());\n            this.$updateGhostText(completion);\n        }\n        if (!keepPopupPosition) {\n            this.popup.setTheme(editor.getTheme());\n            this.popup.setFontSize(editor.getFontSize());\n            this.$updatePopupPosition();\n            if (this.tooltipNode) {\n                this.updateDocTooltip();\n            }\n        }\n        this.changeTimer.cancel();\n        this.observeLayoutChanges();\n    };\n    Autocomplete.prototype.detach = function () {\n        if (this.editor) {\n            this.editor.keyBinding.removeKeyboardHandler(this.keyboardHandler);\n            this.editor.off(\"changeSelection\", this.changeListener);\n            this.editor.off(\"blur\", this.blurListener);\n            this.editor.off(\"mousedown\", this.mousedownListener);\n            this.editor.off(\"mousewheel\", this.mousewheelListener);\n        }\n        this.$firstOpenTimer.cancel();\n        this.changeTimer.cancel();\n        this.hideDocTooltip();\n        if (this.completionProvider) {\n            this.completionProvider.detach();\n        }\n        if (this.popup && this.popup.isOpen)\n            this.popup.hide();\n        if (this.popup && this.popup.renderer) {\n            this.popup.renderer.off(\"afterRender\", this.$onPopupRender);\n        }\n        if (this.base)\n            this.base.detach();\n        this.activated = false;\n        this.completionProvider = this.completions = this.base = null;\n        this.unObserveLayoutChanges();\n    };\n    Autocomplete.prototype.changeListener = function (e) {\n        var cursor = this.editor.selection.lead;\n        if (cursor.row != this.base.row || cursor.column < this.base.column) {\n            this.detach();\n        }\n        if (this.activated)\n            this.changeTimer.schedule();\n        else\n            this.detach();\n    };\n    Autocomplete.prototype.blurListener = function (e) {\n        var el = document.activeElement;\n        var text = this.editor.textInput.getElement();\n        var fromTooltip = e.relatedTarget && this.tooltipNode && this.tooltipNode.contains(e.relatedTarget);\n        var container = this.popup && this.popup.container;\n        if (el != text && el.parentNode != container && !fromTooltip\n            && el != this.tooltipNode && e.relatedTarget != text) {\n            this.detach();\n        }\n    };\n    Autocomplete.prototype.mousedownListener = function (e) {\n        this.detach();\n    };\n    Autocomplete.prototype.mousewheelListener = function (e) {\n        if (this.popup && !this.popup.isMouseOver)\n            this.detach();\n    };\n    Autocomplete.prototype.mouseOutListener = function (e) {\n        if (this.popup.isOpen)\n            this.$updatePopupPosition();\n    };\n    Autocomplete.prototype.goTo = function (where) {\n        this.popup.goTo(where);\n    };\n    Autocomplete.prototype.insertMatch = function (data, options) {\n        if (!data)\n            data = this.popup.getData(this.popup.getRow());\n        if (!data)\n            return false;\n        if (data.value === \"\") // Explicitly given nothing to insert, e.g. \"No suggestion state\"\n            return this.detach();\n        var completions = this.completions;\n        var result = this.getCompletionProvider().insertMatch(this.editor, data, completions.filterText, options);\n        if (this.completions == completions)\n            this.detach();\n        return result;\n    };\n    Autocomplete.prototype.showPopup = function (editor, options) {\n        if (this.editor)\n            this.detach();\n        this.activated = true;\n        this.editor = editor;\n        if (editor.completer != this) {\n            if (editor.completer)\n                editor.completer.detach();\n            editor.completer = this;\n        }\n        editor.on(\"changeSelection\", this.changeListener);\n        editor.on(\"blur\", this.blurListener);\n        editor.on(\"mousedown\", this.mousedownListener);\n        editor.on(\"mousewheel\", this.mousewheelListener);\n        this.updateCompletions(false, options);\n    };\n    Autocomplete.prototype.getCompletionProvider = function (initialPosition) {\n        if (!this.completionProvider)\n            this.completionProvider = new CompletionProvider(initialPosition);\n        return this.completionProvider;\n    };\n    Autocomplete.prototype.gatherCompletions = function (editor, callback) {\n        return this.getCompletionProvider().gatherCompletions(editor, callback);\n    };\n    Autocomplete.prototype.updateCompletions = function (keepPopupPosition, options) {\n        if (keepPopupPosition && this.base && this.completions) {\n            var pos = this.editor.getCursorPosition();\n            var prefix = this.editor.session.getTextRange({ start: this.base, end: pos });\n            if (prefix == this.completions.filterText)\n                return;\n            this.completions.setFilter(prefix);\n            if (!this.completions.filtered.length)\n                return this.detach();\n            if (this.completions.filtered.length == 1\n                && this.completions.filtered[0].value == prefix\n                && !this.completions.filtered[0].snippet)\n                return this.detach();\n            this.openPopup(this.editor, prefix, keepPopupPosition);\n            return;\n        }\n        if (options && options.matches) {\n            var pos = this.editor.getSelectionRange().start;\n            this.base = this.editor.session.doc.createAnchor(pos.row, pos.column);\n            this.base.$insertRight = true;\n            this.completions = new FilteredList(options.matches);\n            this.getCompletionProvider().completions = this.completions;\n            return this.openPopup(this.editor, \"\", keepPopupPosition);\n        }\n        var session = this.editor.getSession();\n        var pos = this.editor.getCursorPosition();\n        var prefix = util.getCompletionPrefix(this.editor);\n        this.base = session.doc.createAnchor(pos.row, pos.column - prefix.length);\n        this.base.$insertRight = true;\n        var completionOptions = {\n            exactMatch: this.exactMatch,\n            ignoreCaption: this.ignoreCaption\n        };\n        this.getCompletionProvider({\n            prefix: prefix,\n            pos: pos\n        }).provideCompletions(this.editor, completionOptions, \n        function (err, completions, finished) {\n            var filtered = completions.filtered;\n            var prefix = util.getCompletionPrefix(this.editor);\n            this.$firstOpenTimer.cancel();\n            if (finished) {\n                if (!filtered.length) {\n                    var emptyMessage = !this.autoShown && this.emptyMessage;\n                    if (typeof emptyMessage == \"function\")\n                        emptyMessage = this.emptyMessage(prefix);\n                    if (emptyMessage) {\n                        var completionsForEmpty = [{\n                                caption: emptyMessage,\n                                value: \"\"\n                            }\n                        ];\n                        this.completions = new FilteredList(completionsForEmpty);\n                        this.openPopup(this.editor, prefix, keepPopupPosition);\n                        this.popup.renderer.setStyle(\"ace_loading\", false);\n                        this.popup.renderer.setStyle(\"ace_empty-message\", true);\n                        return;\n                    }\n                    return this.detach();\n                }\n                if (filtered.length == 1 && filtered[0].value == prefix\n                    && !filtered[0].snippet)\n                    return this.detach();\n                if (this.autoInsert && !this.autoShown && filtered.length == 1)\n                    return this.insertMatch(filtered[0]);\n            }\n            this.completions = !finished && this.showLoadingState ?\n                new FilteredList(Autocomplete.completionsForLoading.concat(filtered), completions.filterText) :\n                completions;\n            this.openPopup(this.editor, prefix, keepPopupPosition);\n            this.popup.renderer.setStyle(\"ace_empty-message\", false);\n            this.popup.renderer.setStyle(\"ace_loading\", !finished);\n        }.bind(this));\n        if (this.showLoadingState && !this.autoShown && !(this.popup && this.popup.isOpen)) {\n            this.$firstOpenTimer.delay(this.stickySelectionDelay / 2);\n        }\n    };\n    Autocomplete.prototype.cancelContextMenu = function () {\n        this.editor.$mouseHandler.cancelContextMenu();\n    };\n    Autocomplete.prototype.updateDocTooltip = function () {\n        var popup = this.popup;\n        var all = this.completions && this.completions.filtered;\n        var selected = all && (all[popup.getHoveredRow()] || all[popup.getRow()]);\n        var doc = null;\n        if (!selected || !this.editor || !this.popup.isOpen)\n            return this.hideDocTooltip();\n        var completersLength = this.editor.completers.length;\n        for (var i = 0; i < completersLength; i++) {\n            var completer = this.editor.completers[i];\n            if (completer.getDocTooltip && selected.completerId === completer.id) {\n                doc = completer.getDocTooltip(selected);\n                break;\n            }\n        }\n        if (!doc && typeof selected != \"string\")\n            doc = selected;\n        if (typeof doc == \"string\")\n            doc = { docText: doc };\n        if (!doc || !(doc.docHTML || doc.docText))\n            return this.hideDocTooltip();\n        this.showDocTooltip(doc);\n    };\n    Autocomplete.prototype.showDocTooltip = function (item) {\n        if (!this.tooltipNode) {\n            this.tooltipNode = dom.createElement(\"div\");\n            this.tooltipNode.style.margin = \"0\";\n            this.tooltipNode.style.pointerEvents = \"auto\";\n            this.tooltipNode.style.overscrollBehavior = \"contain\";\n            this.tooltipNode.tabIndex = -1;\n            this.tooltipNode.onblur = this.blurListener.bind(this);\n            this.tooltipNode.onclick = this.onTooltipClick.bind(this);\n            this.tooltipNode.id = \"doc-tooltip\";\n            this.tooltipNode.setAttribute(\"role\", \"tooltip\");\n            this.tooltipNode.addEventListener(\"wheel\", preventParentScroll);\n        }\n        var theme = this.editor.renderer.theme;\n        this.tooltipNode.className = \"ace_tooltip ace_doc-tooltip \" +\n            (theme.isDark ? \"ace_dark \" : \"\") + (theme.cssClass || \"\");\n        var tooltipNode = this.tooltipNode;\n        if (item.docHTML) {\n            tooltipNode.innerHTML = item.docHTML;\n        }\n        else if (item.docText) {\n            tooltipNode.textContent = item.docText;\n        }\n        if (!tooltipNode.parentNode)\n            this.popup.container.appendChild(this.tooltipNode);\n        var popup = this.popup;\n        var rect = popup.container.getBoundingClientRect();\n        var targetWidth = 400;\n        var targetHeight = 300;\n        var scrollBarSize = popup.renderer.scrollBar.width || 10;\n        var leftSize = rect.left;\n        var rightSize = window.innerWidth - rect.right - scrollBarSize;\n        var topSize = popup.isTopdown ? rect.top : window.innerHeight - scrollBarSize - rect.bottom;\n        var scores = [\n            Math.min(rightSize / targetWidth, 1),\n            Math.min(leftSize / targetWidth, 1),\n            Math.min(topSize / targetHeight * 0.9),\n        ];\n        var max = Math.max.apply(Math, scores);\n        var tooltipStyle = tooltipNode.style;\n        tooltipStyle.display = \"block\";\n        if (max == scores[0]) {\n            tooltipStyle.left = (rect.right + 1) + \"px\";\n            tooltipStyle.right = \"\";\n            tooltipStyle.maxWidth = targetWidth * max + \"px\";\n            tooltipStyle.top = rect.top + \"px\";\n            tooltipStyle.bottom = \"\";\n            tooltipStyle.maxHeight = Math.min(window.innerHeight - scrollBarSize - rect.top, targetHeight) + \"px\";\n        }\n        else if (max == scores[1]) {\n            tooltipStyle.right = window.innerWidth - rect.left + \"px\";\n            tooltipStyle.left = \"\";\n            tooltipStyle.maxWidth = targetWidth * max + \"px\";\n            tooltipStyle.top = rect.top + \"px\";\n            tooltipStyle.bottom = \"\";\n            tooltipStyle.maxHeight = Math.min(window.innerHeight - scrollBarSize - rect.top, targetHeight) + \"px\";\n        }\n        else if (max == scores[2]) {\n            tooltipStyle.left = window.innerWidth - rect.left + \"px\";\n            tooltipStyle.maxWidth = Math.min(targetWidth, window.innerWidth) + \"px\";\n            if (popup.isTopdown) {\n                tooltipStyle.top = rect.bottom + \"px\";\n                tooltipStyle.left = rect.left + \"px\";\n                tooltipStyle.right = \"\";\n                tooltipStyle.bottom = \"\";\n                tooltipStyle.maxHeight = Math.min(window.innerHeight - scrollBarSize - rect.bottom, targetHeight) + \"px\";\n            }\n            else {\n                tooltipStyle.top = popup.container.offsetTop - tooltipNode.offsetHeight + \"px\";\n                tooltipStyle.left = rect.left + \"px\";\n                tooltipStyle.right = \"\";\n                tooltipStyle.bottom = \"\";\n                tooltipStyle.maxHeight = Math.min(popup.container.offsetTop, targetHeight) + \"px\";\n            }\n        }\n    };\n    Autocomplete.prototype.hideDocTooltip = function () {\n        this.tooltipTimer.cancel();\n        if (!this.tooltipNode)\n            return;\n        var el = this.tooltipNode;\n        if (!this.editor.isFocused() && document.activeElement == el)\n            this.editor.focus();\n        this.tooltipNode = null;\n        if (el.parentNode)\n            el.parentNode.removeChild(el);\n    };\n    Autocomplete.prototype.onTooltipClick = function (e) {\n        var a = e.target;\n        while (a && a != this.tooltipNode) {\n            if (a.nodeName == \"A\" && a.href) {\n                a.rel = \"noreferrer\";\n                a.target = \"_blank\";\n                break;\n            }\n            a = a.parentNode;\n        }\n    };\n    Autocomplete.prototype.destroy = function () {\n        this.detach();\n        if (this.popup) {\n            this.popup.destroy();\n            var el = this.popup.container;\n            if (el && el.parentNode)\n                el.parentNode.removeChild(el);\n        }\n        if (this.editor && this.editor.completer == this) {\n            this.editor.off(\"destroy\", destroyCompleter);\n            this.editor.completer = null;\n        }\n        this.inlineRenderer = this.popup = this.editor = null;\n    };\n    Autocomplete.for = function (editor) {\n        if (editor.completer instanceof Autocomplete) {\n            return editor.completer;\n        }\n        if (editor.completer) {\n            editor.completer.destroy();\n            editor.completer = null;\n        }\n        if (config.get(\"sharedPopups\")) {\n            if (!Autocomplete[\"$sharedInstance\"])\n                Autocomplete[\"$sharedInstance\"] = new Autocomplete();\n            editor.completer = Autocomplete[\"$sharedInstance\"];\n        }\n        else {\n            editor.completer = new Autocomplete();\n            editor.once(\"destroy\", destroyCompleter);\n        }\n        return editor.completer;\n    };\n    return Autocomplete;\n}());\nAutocomplete.prototype.commands = {\n    \"Up\": function (editor) { editor.completer.goTo(\"up\"); },\n    \"Down\": function (editor) { editor.completer.goTo(\"down\"); },\n    \"Ctrl-Up|Ctrl-Home\": function (editor) { editor.completer.goTo(\"start\"); },\n    \"Ctrl-Down|Ctrl-End\": function (editor) { editor.completer.goTo(\"end\"); },\n    \"Esc\": function (editor) { editor.completer.detach(); },\n    \"Return\": function (editor) { return editor.completer.insertMatch(); },\n    \"Shift-Return\": function (editor) { editor.completer.insertMatch(null, { deleteSuffix: true }); },\n    \"Tab\": function (editor) {\n        var result = editor.completer.insertMatch();\n        if (!result && !editor.tabstopManager)\n            editor.completer.goTo(\"down\");\n        else\n            return result;\n    },\n    \"Backspace\": function (editor) {\n        editor.execCommand(\"backspace\");\n        var prefix = util.getCompletionPrefix(editor);\n        if (!prefix && editor.completer)\n            editor.completer.detach();\n    },\n    \"PageUp\": function (editor) { editor.completer.popup.gotoPageUp(); },\n    \"PageDown\": function (editor) { editor.completer.popup.gotoPageDown(); }\n};\nAutocomplete.startCommand = {\n    name: \"startAutocomplete\",\n    exec: function (editor, options) {\n        var completer = Autocomplete.for(editor);\n        completer.autoInsert = false;\n        completer.autoSelect = true;\n        completer.autoShown = false;\n        completer.showPopup(editor, options);\n        completer.cancelContextMenu();\n    },\n    bindKey: \"Ctrl-Space|Ctrl-Shift-Space|Alt-Space\"\n};\nvar CompletionProvider = /** @class */ (function () {\n    function CompletionProvider(initialPosition) {\n        this.initialPosition = initialPosition;\n        this.active = true;\n    }\n    CompletionProvider.prototype.insertByIndex = function (editor, index, options) {\n        if (!this.completions || !this.completions.filtered) {\n            return false;\n        }\n        return this.insertMatch(editor, this.completions.filtered[index], options);\n    };\n    CompletionProvider.prototype.insertMatch = function (editor, data, options) {\n        if (!data)\n            return false;\n        editor.startOperation({ command: { name: \"insertMatch\" } });\n        if (data.completer && data.completer.insertMatch) {\n            data.completer.insertMatch(editor, data);\n        }\n        else {\n            if (!this.completions)\n                return false;\n            var replaceBefore = this.completions.filterText.length;\n            var replaceAfter = 0;\n            if (data.range && data.range.start.row === data.range.end.row) {\n                replaceBefore -= this.initialPosition.prefix.length;\n                replaceBefore += this.initialPosition.pos.column - data.range.start.column;\n                replaceAfter += data.range.end.column - this.initialPosition.pos.column;\n            }\n            if (replaceBefore || replaceAfter) {\n                var ranges;\n                if (editor.selection.getAllRanges) {\n                    ranges = editor.selection.getAllRanges();\n                }\n                else {\n                    ranges = [editor.getSelectionRange()];\n                }\n                for (var i = 0, range; range = ranges[i]; i++) {\n                    range.start.column -= replaceBefore;\n                    range.end.column += replaceAfter;\n                    editor.session.remove(range);\n                }\n            }\n            if (data.snippet) {\n                snippetManager.insertSnippet(editor, data.snippet);\n            }\n            else {\n                this.$insertString(editor, data);\n            }\n            if (data.completer && data.completer.onInsert && typeof data.completer.onInsert == \"function\") {\n                data.completer.onInsert(editor, data);\n            }\n            if (data.command && data.command === \"startAutocomplete\") {\n                editor.execCommand(data.command);\n            }\n        }\n        editor.endOperation();\n        return true;\n    };\n    CompletionProvider.prototype.$insertString = function (editor, data) {\n        var text = data.value || data;\n        editor.execCommand(\"insertstring\", text);\n    };\n    CompletionProvider.prototype.gatherCompletions = function (editor, callback) {\n        var session = editor.getSession();\n        var pos = editor.getCursorPosition();\n        var prefix = util.getCompletionPrefix(editor);\n        var matches = [];\n        this.completers = editor.completers;\n        var total = editor.completers.length;\n        editor.completers.forEach(function (completer, i) {\n            completer.getCompletions(editor, session, pos, prefix, function (err, results) {\n                if (completer.hideInlinePreview)\n                    results = results.map(function (result) {\n                        return Object.assign(result, { hideInlinePreview: completer.hideInlinePreview });\n                    });\n                if (!err && results)\n                    matches = matches.concat(results);\n                callback(null, {\n                    prefix: util.getCompletionPrefix(editor),\n                    matches: matches,\n                    finished: (--total === 0)\n                });\n            });\n        });\n        return true;\n    };\n    CompletionProvider.prototype.provideCompletions = function (editor, options, callback) {\n        var processResults = function (results) {\n            var prefix = results.prefix;\n            var matches = results.matches;\n            this.completions = new FilteredList(matches);\n            if (options.exactMatch)\n                this.completions.exactMatch = true;\n            if (options.ignoreCaption)\n                this.completions.ignoreCaption = true;\n            this.completions.setFilter(prefix);\n            if (results.finished || this.completions.filtered.length)\n                callback(null, this.completions, results.finished);\n        }.bind(this);\n        var isImmediate = true;\n        var immediateResults = null;\n        this.gatherCompletions(editor, function (err, results) {\n            if (!this.active) {\n                return;\n            }\n            if (err) {\n                callback(err, [], true);\n                this.detach();\n            }\n            var prefix = results.prefix;\n            if (prefix.indexOf(results.prefix) !== 0)\n                return;\n            if (isImmediate) {\n                immediateResults = results;\n                return;\n            }\n            processResults(results);\n        }.bind(this));\n        isImmediate = false;\n        if (immediateResults) {\n            var results = immediateResults;\n            immediateResults = null;\n            processResults(results);\n        }\n    };\n    CompletionProvider.prototype.detach = function () {\n        this.active = false;\n        this.completers && this.completers.forEach(function (completer) {\n            if (typeof completer.cancel === \"function\") {\n                completer.cancel();\n            }\n        });\n    };\n    return CompletionProvider;\n}());\nvar FilteredList = /** @class */ (function () {\n    function FilteredList(array, filterText) {\n        this.all = array;\n        this.filtered = array;\n        this.filterText = filterText || \"\";\n        this.exactMatch = false;\n        this.ignoreCaption = false;\n    }\n    FilteredList.prototype.setFilter = function (str) {\n        if (str.length > this.filterText && str.lastIndexOf(this.filterText, 0) === 0)\n            var matches = this.filtered;\n        else\n            var matches = this.all;\n        this.filterText = str;\n        matches = this.filterCompletions(matches, this.filterText);\n        matches = matches.sort(function (a, b) {\n            return b.exactMatch - a.exactMatch || b.$score - a.$score\n                || (a.caption || a.value).localeCompare(b.caption || b.value);\n        });\n        var prev = null;\n        matches = matches.filter(function (item) {\n            var caption = item.snippet || item.caption || item.value;\n            if (caption === prev)\n                return false;\n            prev = caption;\n            return true;\n        });\n        this.filtered = matches;\n    };\n    FilteredList.prototype.filterCompletions = function (items, needle) {\n        var results = [];\n        var upper = needle.toUpperCase();\n        var lower = needle.toLowerCase();\n        loop: for (var i = 0, item; item = items[i]; i++) {\n            if (item.skipFilter) {\n                item.$score = item.score;\n                results.push(item);\n                continue;\n            }\n            var caption = (!this.ignoreCaption && item.caption) || item.value || item.snippet;\n            if (!caption)\n                continue;\n            var lastIndex = -1;\n            var matchMask = 0;\n            var penalty = 0;\n            var index, distance;\n            if (this.exactMatch) {\n                if (needle !== caption.substr(0, needle.length))\n                    continue loop;\n            }\n            else {\n                var fullMatchIndex = caption.toLowerCase().indexOf(lower);\n                if (fullMatchIndex > -1) {\n                    penalty = fullMatchIndex;\n                }\n                else {\n                    for (var j = 0; j < needle.length; j++) {\n                        var i1 = caption.indexOf(lower[j], lastIndex + 1);\n                        var i2 = caption.indexOf(upper[j], lastIndex + 1);\n                        index = (i1 >= 0) ? ((i2 < 0 || i1 < i2) ? i1 : i2) : i2;\n                        if (index < 0)\n                            continue loop;\n                        distance = index - lastIndex - 1;\n                        if (distance > 0) {\n                            if (lastIndex === -1)\n                                penalty += 10;\n                            penalty += distance;\n                            matchMask = matchMask | (1 << j);\n                        }\n                        lastIndex = index;\n                    }\n                }\n            }\n            item.matchMask = matchMask;\n            item.exactMatch = penalty ? 0 : 1;\n            item.$score = (item.score || 0) - penalty;\n            results.push(item);\n        }\n        return results;\n    };\n    return FilteredList;\n}());\nexports.Autocomplete = Autocomplete;\nexports.CompletionProvider = CompletionProvider;\nexports.FilteredList = FilteredList;\n\n});\n\nace.define(\"ace/marker_group\",[\"require\",\"exports\",\"module\"], function(require, exports, module){\"use strict\";\nvar MarkerGroup = /** @class */ (function () {\n    function MarkerGroup(session, options) {\n        if (options)\n            this.markerType = options.markerType;\n        this.markers = [];\n        this.session = session;\n        session.addDynamicMarker(this);\n    }\n    MarkerGroup.prototype.getMarkerAtPosition = function (pos) {\n        return this.markers.find(function (marker) {\n            return marker.range.contains(pos.row, pos.column);\n        });\n    };\n    MarkerGroup.prototype.markersComparator = function (a, b) {\n        return a.range.start.row - b.range.start.row;\n    };\n    MarkerGroup.prototype.setMarkers = function (markers) {\n        this.markers = markers.sort(this.markersComparator).slice(0, this.MAX_MARKERS);\n        this.session._signal(\"changeBackMarker\");\n    };\n    MarkerGroup.prototype.update = function (html, markerLayer, session, config) {\n        if (!this.markers || !this.markers.length)\n            return;\n        var visibleRangeStartRow = config.firstRow, visibleRangeEndRow = config.lastRow;\n        var foldLine;\n        var markersOnOneLine = 0;\n        var lastRow = 0;\n        for (var i = 0; i < this.markers.length; i++) {\n            var marker = this.markers[i];\n            if (marker.range.end.row < visibleRangeStartRow)\n                continue;\n            if (marker.range.start.row > visibleRangeEndRow)\n                continue;\n            if (marker.range.start.row === lastRow) {\n                markersOnOneLine++;\n            }\n            else {\n                lastRow = marker.range.start.row;\n                markersOnOneLine = 0;\n            }\n            if (markersOnOneLine > 200) {\n                continue;\n            }\n            var markerVisibleRange = marker.range.clipRows(visibleRangeStartRow, visibleRangeEndRow);\n            if (markerVisibleRange.start.row === markerVisibleRange.end.row\n                && markerVisibleRange.start.column === markerVisibleRange.end.column) {\n                continue; // visible range is empty\n            }\n            var screenRange = markerVisibleRange.toScreenRange(session);\n            if (screenRange.isEmpty()) {\n                foldLine = session.getNextFoldLine(markerVisibleRange.end.row, foldLine);\n                if (foldLine && foldLine.end.row > markerVisibleRange.end.row) {\n                    visibleRangeStartRow = foldLine.end.row;\n                }\n                continue;\n            }\n            if (this.markerType === \"fullLine\") {\n                markerLayer.drawFullLineMarker(html, screenRange, marker.className, config);\n            }\n            else if (screenRange.isMultiLine()) {\n                if (this.markerType === \"line\")\n                    markerLayer.drawMultiLineMarker(html, screenRange, marker.className, config);\n                else\n                    markerLayer.drawTextMarker(html, screenRange, marker.className, config);\n            }\n            else {\n                markerLayer.drawSingleLineMarker(html, screenRange, marker.className + \" ace_br15\", config);\n            }\n        }\n    };\n    return MarkerGroup;\n}());\nMarkerGroup.prototype.MAX_MARKERS = 10000;\nexports.MarkerGroup = MarkerGroup;\n\n});\n\nace.define(\"ace/autocomplete/text_completer\",[\"require\",\"exports\",\"module\",\"ace/range\"], function(require, exports, module){var Range = require(\"../range\").Range;\nvar splitRegex = /[^a-zA-Z_0-9\\$\\-\\u00C0-\\u1FFF\\u2C00-\\uD7FF\\w]+/;\nfunction getWordIndex(doc, pos) {\n    var textBefore = doc.getTextRange(Range.fromPoints({\n        row: 0,\n        column: 0\n    }, pos));\n    return textBefore.split(splitRegex).length - 1;\n}\nfunction wordDistance(doc, pos) {\n    var prefixPos = getWordIndex(doc, pos);\n    var words = doc.getValue().split(splitRegex);\n    var wordScores = Object.create(null);\n    var currentWord = words[prefixPos];\n    words.forEach(function (word, idx) {\n        if (!word || word === currentWord)\n            return;\n        var distance = Math.abs(prefixPos - idx);\n        var score = words.length - distance;\n        if (wordScores[word]) {\n            wordScores[word] = Math.max(score, wordScores[word]);\n        }\n        else {\n            wordScores[word] = score;\n        }\n    });\n    return wordScores;\n}\nexports.getCompletions = function (editor, session, pos, prefix, callback) {\n    var wordScore = wordDistance(session, pos);\n    var wordList = Object.keys(wordScore);\n    callback(null, wordList.map(function (word) {\n        return {\n            caption: word,\n            value: word,\n            score: wordScore[word],\n            meta: \"local\"\n        };\n    }));\n};\n\n});\n\nace.define(\"ace/ext/language_tools\",[\"require\",\"exports\",\"module\",\"ace/snippets\",\"ace/autocomplete\",\"ace/config\",\"ace/lib/lang\",\"ace/autocomplete/util\",\"ace/marker_group\",\"ace/autocomplete/text_completer\",\"ace/editor\",\"ace/config\"], function(require, exports, module){/**\n * ## Language Tools extension for Ace Editor\n *\n * Provides autocompletion, snippets, and language intelligence features for the Ace code editor.\n * This extension integrates multiple completion providers including keyword completion, snippet expansion,\n * and text-based completion to enhance the coding experience with contextual suggestions and automated code generation.\n *\n * **Configuration Options:**\n * - `enableBasicAutocompletion`: Enable/disable basic completion functionality\n * - `enableLiveAutocompletion`: Enable/disable real-time completion suggestions\n * - `enableSnippets`: Enable/disable snippet expansion with Tab key\n * - `liveAutocompletionDelay`: Delay before showing live completion popup\n * - `liveAutocompletionThreshold`: Minimum prefix length to trigger completion\n *\n * **Usage:**\n * ```javascript\n * editor.setOptions({\n *   enableBasicAutocompletion: true,\n *   enableLiveAutocompletion: true,\n *   enableSnippets: true\n * });\n * ```\n *\n * @module\n */\n\"use strict\";\nvar snippetManager = require(\"../snippets\").snippetManager;\nvar Autocomplete = require(\"../autocomplete\").Autocomplete;\nvar config = require(\"../config\");\nvar lang = require(\"../lib/lang\");\nvar util = require(\"../autocomplete/util\");\nvar MarkerGroup = require(\"../marker_group\").MarkerGroup;\nvar textCompleter = require(\"../autocomplete/text_completer\");\nvar keyWordCompleter = {\n    getCompletions: function (editor, session, pos, prefix, callback) {\n        if (session.$mode.completer) {\n            return session.$mode.completer.getCompletions(editor, session, pos, prefix, callback);\n        }\n        var state = editor.session.getState(pos.row);\n        var completions = session.$mode.getCompletions(state, session, pos, prefix);\n        completions = completions.map(function (el) {\n            el.completerId = keyWordCompleter.id;\n            return el;\n        });\n        callback(null, completions);\n    },\n    id: \"keywordCompleter\"\n};\nvar transformSnippetTooltip = function (str) {\n    var record = {};\n    return str.replace(/\\${(\\d+)(:(.*?))?}/g, function (_, p1, p2, p3) {\n        return (record[p1] = p3 || '');\n    }).replace(/\\$(\\d+?)/g, function (_, p1) {\n        return record[p1];\n    });\n};\nvar snippetCompleter = {\n    getCompletions: function (editor, session, pos, prefix, callback) {\n        var scopes = [];\n        var token = session.getTokenAt(pos.row, pos.column);\n        if (token && token.type.match(/(tag-name|tag-open|tag-whitespace|attribute-name|attribute-value)\\.xml$/))\n            scopes.push('html-tag');\n        else\n            scopes = snippetManager.getActiveScopes(editor);\n        var snippetMap = snippetManager.snippetMap;\n        var completions = [];\n        scopes.forEach(function (scope) {\n            var snippets = snippetMap[scope] || [];\n            for (var i = snippets.length; i--;) {\n                var s = snippets[i];\n                var caption = s.name || s.tabTrigger;\n                if (!caption)\n                    continue;\n                completions.push({\n                    caption: caption,\n                    snippet: s.content,\n                    meta: s.tabTrigger && !s.name ? s.tabTrigger + \"\\u21E5 \" : \"snippet\",\n                    completerId: snippetCompleter.id\n                });\n            }\n        }, this);\n        callback(null, completions);\n    },\n    getDocTooltip: function (item) {\n        if (item.snippet && !item.docHTML) {\n            item.docHTML = [\n                \"<b>\", lang.escapeHTML(item.caption), \"</b>\", \"<hr></hr>\",\n                lang.escapeHTML(transformSnippetTooltip(item.snippet))\n            ].join(\"\");\n        }\n    },\n    id: \"snippetCompleter\"\n};\nvar completers = [snippetCompleter, textCompleter, keyWordCompleter];\nexports.setCompleters = function (val) {\n    completers.length = 0;\n    if (val)\n        completers.push.apply(completers, val);\n};\nexports.addCompleter = function (completer) {\n    completers.push(completer);\n};\nexports.textCompleter = textCompleter;\nexports.keyWordCompleter = keyWordCompleter;\nexports.snippetCompleter = snippetCompleter;\nvar expandSnippet = {\n    name: \"expandSnippet\",\n    exec: function (editor) {\n        return snippetManager.expandWithTab(editor);\n    },\n    bindKey: \"Tab\"\n};\nvar onChangeMode = function (e, editor) {\n    loadSnippetsForMode(editor.session.$mode);\n};\nvar loadSnippetsForMode = function (mode) {\n    if (typeof mode == \"string\")\n        mode = config.$modes[mode];\n    if (!mode)\n        return;\n    if (!snippetManager.files)\n        snippetManager.files = {};\n    loadSnippetFile(mode.$id, mode.snippetFileId);\n    if (mode.modes)\n        mode.modes.forEach(loadSnippetsForMode);\n};\nvar loadSnippetFile = function (id, snippetFilePath) {\n    if (!snippetFilePath || !id || snippetManager.files[id])\n        return;\n    snippetManager.files[id] = {};\n    config.loadModule(snippetFilePath, function (m) {\n        if (!m)\n            return;\n        snippetManager.files[id] = m;\n        if (!m.snippets && m.snippetText)\n            m.snippets = snippetManager.parseSnippetFile(m.snippetText);\n        snippetManager.register(m.snippets || [], m.scope);\n        if (m.includeScopes) {\n            snippetManager.snippetMap[m.scope].includeScopes = m.includeScopes;\n            m.includeScopes.forEach(function (x) {\n                loadSnippetsForMode(\"ace/mode/\" + x);\n            });\n        }\n    });\n};\nvar doLiveAutocomplete = function (e) {\n    var editor = e.editor;\n    var hasCompleter = editor.completer && editor.completer.activated;\n    if (e.command.name === \"backspace\") {\n        if (hasCompleter && !util.getCompletionPrefix(editor))\n            editor.completer.detach();\n    }\n    else if (e.command.name === \"insertstring\" && !hasCompleter) {\n        lastExecEvent = e;\n        var delay = e.editor.$liveAutocompletionDelay;\n        if (delay) {\n            liveAutocompleteTimer.delay(delay);\n        }\n        else {\n            showLiveAutocomplete(e);\n        }\n    }\n};\nvar lastExecEvent;\nvar liveAutocompleteTimer = lang.delayedCall(function () {\n    showLiveAutocomplete(lastExecEvent);\n}, 0);\nvar showLiveAutocomplete = function (e) {\n    var editor = e.editor;\n    var prefix = util.getCompletionPrefix(editor);\n    var previousChar = e.args;\n    var triggerAutocomplete = util.triggerAutocomplete(editor, previousChar);\n    if (prefix && prefix.length >= editor.$liveAutocompletionThreshold || triggerAutocomplete) {\n        var completer = Autocomplete.for(editor);\n        completer.autoShown = true;\n        completer.showPopup(editor);\n    }\n};\nvar Editor = require(\"../editor\").Editor;\nrequire(\"../config\").defineOptions(Editor.prototype, \"editor\", {\n    enableBasicAutocompletion: {\n        set: function (val) {\n            if (val) {\n                Autocomplete.for(this);\n                if (!this.completers)\n                    this.completers = Array.isArray(val) ? val : completers;\n                this.commands.addCommand(Autocomplete.startCommand);\n            }\n            else {\n                this.commands.removeCommand(Autocomplete.startCommand);\n            }\n        },\n        value: false\n    },\n    enableLiveAutocompletion: {\n        set: function (val) {\n            if (val) {\n                if (!this.completers)\n                    this.completers = Array.isArray(val) ? val : completers;\n                this.commands.on('afterExec', doLiveAutocomplete);\n            }\n            else {\n                this.commands.off('afterExec', doLiveAutocomplete);\n            }\n        },\n        value: false\n    },\n    liveAutocompletionDelay: {\n        initialValue: 0\n    },\n    liveAutocompletionThreshold: {\n        initialValue: 0\n    },\n    enableSnippets: {\n        set: function (val) {\n            if (val) {\n                this.commands.addCommand(expandSnippet);\n                this.on(\"changeMode\", onChangeMode);\n                onChangeMode(null, this);\n            }\n            else {\n                this.commands.removeCommand(expandSnippet);\n                this.off(\"changeMode\", onChangeMode);\n            }\n        },\n        value: false\n    }\n});\nexports.MarkerGroup = MarkerGroup;\n\n});                (function() {\n                    ace.require([\"ace/ext/language_tools\"], function(m) {\n                        if ( true && module) {\n                            module.exports = m;\n                        }\n                    });\n                })();\n            //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/../../node_modules/.pnpm/ace-builds@1.43.1/node_modules/ace-builds/src-noconflict/ext-language_tools.js\n"));

/***/ })

}]);