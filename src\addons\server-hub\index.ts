import { ButtonInteraction, ChannelType, PermissionFlagsBits, StringSelectMenuInteraction, MessageFlags, PermissionsBitField } from 'discord.js';
import { MongoClient, ObjectId } from 'mongodb';
import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import YAML from 'yaml';
import type { Addon } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';
import { dashboardConfig } from '../../dashboard/core/config.js';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load addon configuration
const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

// Database helpers
const { url: mongoUrl, name: dbName } = dashboardConfig.database;
let cachedClient: MongoClient | null = null;
async function getTicketsDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl);
  }
  return cachedClient.db(dbName).collection('tickets');
}

async function getSuggestionsDb() {
  if (!cachedClient) {
    cachedClient = await MongoClient.connect(mongoUrl);
  }
  return cachedClient.db(dbName).collection('suggestions');
}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  events: [
    {
      name: 'ready',
      once: true,
      execute: async (client) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        try {
          // Initialize server hub
          await setupServerHub(client);
        } catch (error) {
          logger.error('Failed to setup server hub', {
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }
    },
    {
      name: 'interactionCreate',
      once: false,
      execute: async (interaction) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        try {
          if (interaction.isButton() && interaction.customId === 'server_rules') {
            await handleRulesButton(interaction);
          } else if (interaction.isButton() && interaction.customId === 'get_roles') {
            await handleGetRolesButton(interaction);
          } else if (interaction.isButton() && interaction.customId === 'get_support') {
            await handleGetSupportButton(interaction);
          } else if (interaction.isStringSelectMenu() && interaction.customId === 'support_type_select') {
            await handleSupportTypeSelect(interaction);
          } else if (interaction.isStringSelectMenu() && interaction.customId.startsWith('hub_role_select_')) {
            await handleRoleSelectMenu(interaction);
          } else if (interaction.isStringSelectMenu() && interaction.customId === 'support_category_select') {
            await handleSupportCategorySelect(interaction);
          } else if (interaction.isStringSelectMenu() && interaction.customId === 'suggestion_category_select') {
            await handleSuggestionCategorySelect(interaction);
          } else if (interaction.isModalSubmit() && interaction.customId.startsWith('suggestion_form_')) {
            await handleSuggestionFormSubmit(interaction);
          }
        } catch (error) {
          logger.error('Failed to handle interaction', {
            error: error instanceof Error ? error.message : 'Unknown error',
            customId: interaction.customId
          });
        }
      }
    }
  ]
};

// Function to build the server hub container
function buildServerHubContainer(guild: any) {
  const roleCategories = config.settings.roleCategories || [];
  const hubContent = config.settings.hubContent;
  const features = config.settings.features || {};
  
  // Parse accent color from config (hex string to number)
  const accentColor = parseInt(hubContent.accentColor || 'FF6B35', 16);
  
  // Build button components - always show buttons, but functionality depends on enabled state
  const buttonComponents = [
    {
      type: 2, // Button
      custom_id: 'server_rules',
      label: '📜 Server Rules',
      style: features.rules?.enabled === false ? 2 : 4, // Secondary (gray) if disabled, Danger (red) if enabled
      emoji: { name: '🔞' }
    },
    {
      type: 2, // Button
      custom_id: 'get_roles',
      label: '🎭 Get Roles',
      style: features.roles?.enabled === false ? 2 : 1, // Secondary (gray) if disabled, Primary (blue) if enabled
      emoji: { name: '🎯' }
    },
    {
      type: 2, // Button
      custom_id: 'get_support',
      label: '🎫 Support',
      style: (features.tickets?.enabled === false && features.suggestions?.enabled === false) ? 2 : 3, // Secondary (gray) if both disabled, Success (green) if either enabled
      emoji: { name: '💬' }
    }
  ];
  
  return {
    type: 17, // Container
    accent_color: accentColor,
    components: [
      {
        type: 10, // Text - Main Header
        content: `# ${hubContent.title}\n## ${hubContent.subtitle}`
      },
      {
        type: 10, // Text - Welcome message
        content: hubContent.description
      },
      {
        type: 10, // Text - What we offer header
        content: `## ${hubContent.whatWeOffer.title}`
      },
      {
        type: 10, // Text - Features list
        content: hubContent.whatWeOffer.content
      },
      {
        type: 10, // Text - Getting started header
        content: `## ${hubContent.gettingStarted.title}`
      },
      {
        type: 10, // Text - Steps
        content: hubContent.gettingStarted.content
      },
      {
        type: 10, // Text - Ready to join
        content: `## ${hubContent.callToAction.title}\n${hubContent.callToAction.content}`
      },
      ...(buttonComponents.length > 0 ? [{
        type: 1, // ActionRow for primary buttons
        components: buttonComponents
      }] : []),
      {
        type: 10, // Text - Footer
        content: `---\n${hubContent.footer}${hubContent.tip ? `\n\n${hubContent.tip}` : ''}`
      }
    ]
  };
}

async function setupServerHub(client: any) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  // Get the configured channel
  const channel = await client.channels.fetch(config.settings.hubChannelId);
  logger.info('Fetched channel', { channelId: config.settings.hubChannelId, channelType: channel?.type });

  if (!channel || channel.type !== ChannelType.GuildText) {
    logger.error('Invalid hub channel configuration', { channelId: config.settings.hubChannelId });
    return;
  }

  try {
    const container = buildServerHubContainer(channel.guild);

    // Send or update the hub message (no select menus in main message)
    const messages = await channel.messages.fetch({ limit: 1 });
    const existingMessage = messages.first();
    
    const messageData = {
      components: [container],
      flags: MessageFlags.IsComponentsV2
    };
    
    if (existingMessage && existingMessage.author.id === client.user.id) {
      await existingMessage.edit(messageData);
      logger.info('Updated existing hub message');
    } else {
      await channel.send(messageData);
      logger.info('Created new hub message');
    }
  } catch (error) {
    logger.error('Failed to setup hub message', {
      error: error instanceof Error ? error.message : 'Unknown error',
      channelId: config.settings.hubChannelId
    });
    throw error;
  }
}

async function handleGetRolesButton(interaction: ButtonInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  const features = config.settings.features || {};
  
  // Check if roles feature is disabled
  if (features.roles?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: features.roles.maintenanceMessage || "🔧 **Feature Under Maintenance**\nThis feature is currently disabled. Please check back later!"
        }
      ]
    };

    await interaction.reply({
      components: [maintenanceContainer],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });
    return;
  }
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const roleCategories = config.settings.roleCategories || [];
  
  if (roleCategories.length === 0) {
    await interaction.reply({
      content: '❌ No self-assignable role categories are configured.',
      ephemeral: true
    });
    return;
  }

  try {
    // Get member's current roles to show what they have selected
    const memberId = 'user' in interaction.member ? interaction.member.user.id : interaction.user.id;
    const guildMember = await interaction.guild.members.fetch(memberId);
    
    if (!guildMember) {
      await interaction.reply({
        content: '❌ Could not find your member information.',
        ephemeral: true
      });
      return;
    }

    // Create role selection container
    const rolesContainer = {
      type: 17, // Container
      accent_color: 0x9B59B6, // Purple for roles
      components: [
        {
          type: 10, // Text - Header
          content: `# 🎭 Role Selection\n## Choose your roles from the categories below`
        },
        {
          type: 10, // Text - Instructions
          content: `Select roles from each category using the dropdown menus. **Exclusive categories** allow only one role, while **multiple categories** allow several roles.`
        }
      ]
    };

    // Create select menus for each role category
    const selectMenuComponents = [];
    for (let i = 0; i < roleCategories.length; i++) {
      const category = roleCategories[i];
      
      // Get user's current roles in this category
      const categoryRoleIds = category.roles.map((r: any) => r.id);
      const userCategoryRoles = guildMember.roles.cache.filter(role => categoryRoleIds.includes(role.id));
      const currentRoleNames = userCategoryRoles.map(role => {
        const roleConfig = category.roles.find((r: any) => r.id === role.id);
        return `${roleConfig?.emoji} ${roleConfig?.name}`;
      });
      
      const selectMenu = {
        type: 1, // Action Row
        components: [
          {
            type: 3, // String Select Menu
            custom_id: `hub_role_select_${i}`,
            placeholder: `${category.emoji} ${category.name}${currentRoleNames.length > 0 ? ` (Current: ${currentRoleNames.join(', ')})` : ''}`,
            min_values: 0,
            max_values: category.exclusive ? 1 : category.roles.length,
            options: category.roles.map((role: any) => ({
              label: role.name,
              value: role.id,
              description: `${role.description}${category.exclusive ? ' (Exclusive)' : ''}`,
              emoji: { name: role.emoji },
              default: guildMember.roles.cache.has(role.id)
            }))
          }
        ]
      };
      selectMenuComponents.push(selectMenu);
    }

    await interaction.reply({
      components: [rolesContainer, ...selectMenuComponents],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });

  } catch (error) {
    logger.error('Failed to handle get roles button', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    await interaction.reply({
      content: '❌ An error occurred while loading the role selection menu.',
      ephemeral: true
    });
  }
}

async function handleRulesButton(interaction: ButtonInteraction) {
  const features = config.settings.features || {};
  
  // Check if rules feature is disabled
  if (features.rules?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: features.rules.maintenanceMessage || "🔧 **Feature Under Maintenance**\nThis feature is currently disabled. Please check back later!"
        }
      ]
    };

    await interaction.reply({
      components: [maintenanceContainer],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });
    return;
  }

  const rulesContainer = {
    type: 17, // Container
    accent_color: 0x57F287, // Discord Green
    components: [
      {
        type: 10, // Text
        content: config.settings.rules
      },
      {
        type: 10, // Text
        content: `---\n💡 **Remember:** Following these rules helps maintain a positive community for everyone!`
      }
    ]
  };

  await interaction.reply({
    components: [rulesContainer],
    flags: MessageFlags.IsComponentsV2,
    ephemeral: true
  });
}

async function handleRoleSelectMenu(interaction: StringSelectMenuInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const categoryIndex = parseInt(interaction.customId.replace('hub_role_select_', ''));
  const roleCategories = config.settings.roleCategories || [];
  const category = roleCategories[categoryIndex];
  
  if (!category) {
    await interaction.reply({
      content: '❌ Category not found.',
      ephemeral: true
    });
    return;
  }

  try {
    // Get the member as a GuildMember object
    const memberId = 'user' in interaction.member ? interaction.member.user.id : interaction.user.id;
    const guildMember = await interaction.guild.members.fetch(memberId);
    
    if (!guildMember) {
      await interaction.reply({
        content: '❌ Could not find your member information.',
        ephemeral: true
      });
      return;
    }

    const selectedRoleIds = interaction.values;
    const categoryRoleIds = category.roles.map((r: any) => r.id);
    
    // Get current roles in this category
    const currentCategoryRoles = guildMember.roles.cache.filter(role => categoryRoleIds.includes(role.id));
    
    // Remove all current category roles
    if (currentCategoryRoles.size > 0) {
      await guildMember.roles.remove(currentCategoryRoles);
    }
    
    // Add selected roles
    const rolesToAdd = [];
    const roleNames = [];
    for (const roleId of selectedRoleIds) {
      const role = await interaction.guild.roles.fetch(roleId);
      if (role) {
        rolesToAdd.push(role);
        const roleConfig = category.roles.find((r: any) => r.id === roleId);
        roleNames.push(`${roleConfig?.emoji} ${roleConfig?.name}`);
      }
    }
    
    if (rolesToAdd.length > 0) {
      await guildMember.roles.add(rolesToAdd);
    }

    // Create success container
    const successContainer = {
      type: 17, // Container
      accent_color: 0x00FF00, // Green for success
      components: [
        {
          type: 10, // Text
          content: selectedRoleIds.length > 0 ? 
            `✅ **${category.emoji} ${category.name} Roles Updated!**\n${roleNames.join('\n')}` :
            `✅ **${category.emoji} ${category.name} Roles Cleared!**\nAll roles from this category have been removed.`
        }
      ]
    };

    await interaction.reply({
      components: [successContainer],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });

    logger.info('Roles updated via select menu', {
      userId: memberId,
      category: category.name,
      selectedRoles: roleNames,
      removedCount: currentCategoryRoles.size,
      addedCount: rolesToAdd.length
    });

  } catch (error) {
    logger.error('Failed to handle role select menu', {
      error: error instanceof Error ? error.message : 'Unknown error',
      categoryIndex: categoryIndex,
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    const errorContainer = {
      type: 17, // Container
      accent_color: 0xFF0000, // Red for error
      components: [
        {
          type: 10, // Text
          content: `❌ **Failed to Update Roles**\nThere was an error updating your ${category.name} roles. The bot may not have permission to manage these roles.`
        }
      ]
    };
    
    await interaction.reply({
      components: [errorContainer],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });
  }
}

async function handleGetSupportButton(interaction: ButtonInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  const features = config.settings.features || {};
  
  // Check if interaction is still valid
  if (interaction.replied || interaction.deferred) {
    logger.warn('Interaction already handled', { customId: interaction.customId });
    return;
  }
  
  // Check if both support features are disabled
  if (features.tickets?.enabled === false && features.suggestions?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: "🔧 **Support Under Maintenance**\nBoth tickets and suggestions are currently disabled. Please check back later!"
        }
      ]
    };

    try {
      await interaction.reply({
        components: [maintenanceContainer],
        flags: MessageFlags.IsComponentsV2,
        ephemeral: true
      });
    } catch (error) {
      logger.error('Failed to reply to maintenance interaction', { error: error instanceof Error ? error.message : 'Unknown error' });
    }
    return;
  }
  
  if (!interaction.guild || !interaction.member) {
    try {
      await interaction.reply({
        content: '❌ This command can only be used in a server.',
        ephemeral: true
      });
    } catch (error) {
      logger.error('Failed to reply to guild check interaction', { error: error instanceof Error ? error.message : 'Unknown error' });
    }
    return;
  }

  try {
    // Create support type selection container
    const supportContainer = {
      type: 17, // Container
      accent_color: 0x3B82F6, // Blue for support
      components: [
        {
          type: 10, // Text - Header
          content: `# 🎫 Support System\n## Choose your support type`
        },
        {
          type: 10, // Text - Instructions
          content: `Select whether you need help with an issue (tickets) or want to share an idea (suggestions).`
        }
      ]
    };

    // Build support type options
    const supportOptions = [];
    
    if (features.tickets?.enabled !== false) {
      supportOptions.push({
        label: 'Support Tickets',
        value: 'tickets',
        description: 'Get help with issues or report problems',
        emoji: { name: '🎫' }
      });
    }
    
    if (features.suggestions?.enabled !== false) {
      supportOptions.push({
        label: 'Suggestions',
        value: 'suggestions', 
        description: 'Share ideas and feedback with the community',
        emoji: { name: '💡' }
      });
    }

    if (supportOptions.length === 0) {
      await interaction.reply({
        content: '❌ No support options are currently available.',
        ephemeral: true
      });
      return;
    }

    // Create support type select menu
    const typeSelectMenu = {
      type: 1, // Action Row
      components: [
        {
          type: 3, // String Select Menu
          custom_id: 'support_type_select',
          placeholder: '🎫 Choose your support type...',
          min_values: 1,
          max_values: 1,
          options: supportOptions
        }
      ]
    };

    await interaction.reply({
      components: [supportContainer, typeSelectMenu],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });

  } catch (error) {
    logger.error('Failed to handle get support button', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.member && 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    // Only try to reply if we haven't already
    if (!interaction.replied && !interaction.deferred) {
      try {
        await interaction.reply({
          content: '❌ An error occurred while loading the support menu.',
          ephemeral: true
        });
      } catch (replyError) {
        logger.error('Failed to send error reply', { error: replyError instanceof Error ? replyError.message : 'Unknown error' });
      }
    }
  }
}

async function handleSupportTypeSelect(interaction: StringSelectMenuInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  // Check if interaction is still valid
  if (interaction.replied || interaction.deferred) {
    logger.warn('Interaction already handled', { customId: interaction.customId });
    return;
  }
  
  const selectedType = interaction.values[0];
  
  try {
    if (selectedType === 'tickets') {
      // Replace ephemeral with tickets interface
      await handleTicketsInterface(interaction);
    } else if (selectedType === 'suggestions') {
      // Replace ephemeral with suggestions interface
      await handleSuggestionsInterface(interaction);
    } else {
      await interaction.update({
        content: '❌ Invalid support type selected.',
        components: []
      });
    }
  } catch (error) {
    logger.error('Failed to handle support type select', {
      error: error instanceof Error ? error.message : 'Unknown error',
      selectedType,
      userId: interaction.member && 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    // Only try to update if we haven't already
    if (!interaction.replied && !interaction.deferred) {
      try {
        await interaction.update({
          content: '❌ An error occurred while processing your selection.',
          components: []
        });
      } catch (updateError) {
        logger.error('Failed to send error update', { error: updateError instanceof Error ? updateError.message : 'Unknown error' });
      }
    }
  }
}

async function handleTicketsInterface(interaction: StringSelectMenuInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  const features = config.settings.features || {};
  
  // Check if tickets feature is disabled
  if (features.tickets?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: features.tickets.maintenanceMessage || "🔧 **Tickets Under Maintenance**\nThe ticket system is currently disabled. Please check back later!"
        }
      ]
    };

    await interaction.update({
      components: [maintenanceContainer]
    });
    return;
  }

  const supportConfig = config.settings.supportConfig || {};
  const categories = supportConfig.categories || [];
  
  if (categories.length === 0) {
    await interaction.update({
      content: '❌ No support categories are configured.',
      components: []
    });
    return;
  }

  try {
    // Create support selection container
    const supportContainer = {
      type: 17, // Container
      accent_color: 0x3B82F6, // Blue for support
      components: [
        {
          type: 10, // Text - Header
          content: `# 🎫 Support Ticket System\n## Select a category for your support request`
        },
        {
          type: 10, // Text - Instructions
          content: `Choose the category that best describes your issue. A private support channel will be created for you to discuss your concern with our staff team.`
        }
      ]
    };

    // Create category select menu
    const categorySelectMenu = {
      type: 1, // Action Row
      components: [
        {
          type: 3, // String Select Menu
          custom_id: 'support_category_select',
          placeholder: '🎫 Select a support category...',
          min_values: 1,
          max_values: 1,
          options: categories.map((category: any) => ({
            label: category.name,
            value: category.value,
            description: category.description,
            emoji: { name: category.emoji }
          }))
        }
      ]
    };

    await interaction.update({
      components: [supportContainer, categorySelectMenu]
    });

  } catch (error) {
    logger.error('Failed to handle tickets interface', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.member && 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    await interaction.update({
      content: '❌ An error occurred while loading the tickets menu.',
      components: []
    });
  }
}

async function handleSuggestionsInterface(interaction: StringSelectMenuInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  const features = config.settings.features || {};
  
  // Check if suggestions feature is disabled
  if (features.suggestions?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: features.suggestions.maintenanceMessage || "🔧 **Suggestions Under Maintenance**\nThe suggestion system is currently disabled. Please check back later!"
        }
      ]
    };

    await interaction.update({
      components: [maintenanceContainer]
    });
    return;
  }

  const suggestionConfig = config.settings.suggestionConfig || {};
  const categories = suggestionConfig.categories || [];
  
  if (categories.length === 0) {
    await interaction.update({
      content: '❌ No suggestion categories are configured.',
      components: []
    });
    return;
  }

  try {
    // Create suggestion selection container
    const suggestionContainer = {
      type: 17, // Container
      accent_color: 0x10B981, // Green for suggestions
      components: [
        {
          type: 10, // Text - Header
          content: `# 💡 Suggestion System\n## Share your ideas with the community`
        },
        {
          type: 10, // Text - Instructions
          content: `Choose a category for your suggestion. Your idea will be posted in the suggestions channel where the community can vote and discuss it.${suggestionConfig.anonymousMode ? '\n\n🕶️ **Anonymous Mode Available** - You can choose to submit suggestions anonymously.' : ''}`
        }
      ]
    };

    // Create category select menu
    const categorySelectMenu = {
      type: 1, // Action Row
      components: [
        {
          type: 3, // String Select Menu
          custom_id: 'suggestion_category_select',
          placeholder: '💡 Select a suggestion category...',
          min_values: 1,
          max_values: 1,
          options: categories.map((category: any) => ({
            label: category.name,
            value: category.value,
            description: category.description,
            emoji: { name: category.emoji }
          }))
        }
      ]
    };

    await interaction.update({
      components: [suggestionContainer, categorySelectMenu]
    });

  } catch (error) {
    logger.error('Failed to handle suggestions interface', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: interaction.member && 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    await interaction.update({
      content: '❌ An error occurred while loading the suggestions menu.',
      components: []
    });
  }
}

async function handleSupportCategorySelect(interaction: StringSelectMenuInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const selectedCategory = interaction.values[0];
  const supportConfig = config.settings.supportConfig || {};
  const categories = supportConfig.categories || [];
  const category = categories.find((c: any) => c.value === selectedCategory);
  
  if (!category) {
    await interaction.reply({
      content: '❌ Invalid category selected.',
      ephemeral: true
    });
    return;
  }

  try {
    // Defer reply to prevent timeout during channel creation
    await interaction.deferReply({ ephemeral: true });

    const memberId = 'user' in interaction.member ? interaction.member.user.id : interaction.user.id;
    const member = await interaction.guild.members.fetch(memberId);
    
    if (!member) {
      await interaction.editReply({
        content: '❌ Could not find your member information.'
      });
      return;
    }

    // Check if user already has an open ticket
    const ticketsCol = await getTicketsDb();
    const existingTicket = await ticketsCol.findOne({
      creatorId: memberId,
      guildId: interaction.guild.id,
      status: 'open'
    });

    if (existingTicket) {
      await interaction.editReply({
        content: `❌ You already have an open support ticket: <#${existingTicket.channelId}>`
      });
      return;
    }

    // Get support category from config or fallback to bot config
    const categoryId = supportConfig.categoryId || dashboardConfig.bot.ticketCategoryId;
    
    if (!categoryId) {
      await interaction.editReply({
        content: '❌ Support category is not configured. Please contact an administrator.'
      });
      return;
    }

    // Create ticket channel
    const ticketChannel = await interaction.guild.channels.create({
      name: `${category.value}-${member.user.username}`,
      type: ChannelType.GuildText,
      parent: categoryId,
      permissionOverwrites: [
        {
          id: interaction.guild.id, // @everyone
          deny: [PermissionFlagsBits.ViewChannel]
        },
        {
          id: memberId, // Ticket creator
          allow: [
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages,
            PermissionFlagsBits.ReadMessageHistory
          ]
        },
        {
          id: interaction.client.user.id, // Bot
          allow: [
            PermissionFlagsBits.ViewChannel,
            PermissionFlagsBits.SendMessages,
            PermissionFlagsBits.ManageMessages,
            PermissionFlagsBits.ReadMessageHistory
          ]
        }
      ]
    });

    // Add staff role permissions
    const staffRoleIds = supportConfig.staffRoleIds || dashboardConfig.dashboard.adminRoleIds || [];
    for (const roleId of staffRoleIds) {
      if (roleId && roleId !== 'ROLE_ID_STAFF' && roleId !== 'ROLE_ID_MODERATOR') {
        try {
          await ticketChannel.permissionOverwrites.create(roleId, {
            ViewChannel: true,
            SendMessages: true,
            ReadMessageHistory: true,
            ManageMessages: true
          });
        } catch (error) {
          logger.warn('Failed to add staff role permissions', { roleId, error });
        }
      }
    }

    // Create ticket in database
    const ticketDoc = {
      channelId: ticketChannel.id,
      creatorId: memberId,
      guildId: interaction.guild.id,
      category: selectedCategory,
      status: 'open',
      createdAt: new Date(),
      claimedBy: null
    };

    const result = await ticketsCol.insertOne(ticketDoc);
    const ticketId = result.insertedId;

    // Create ticket embed and buttons
    const ticketEmbed = {
      type: 17, // Container
      accent_color: category.value === '18plus' ? 0xEF4444 : 0x3B82F6,
      components: [
        {
          type: 10, // Text - Header
          content: `# ${category.emoji} ${category.name} Ticket\n## Ticket ID: ${ticketId.toString()}`
        },
        {
          type: 10, // Text - Welcome
          content: `Hello ${member.user.username}! Thank you for creating a support ticket.\n\n**Category:** ${category.name}\n**Description:** ${category.description}\n\nPlease describe your issue in detail. A staff member will assist you shortly.`
        }
      ]
    };

    const ticketButtons = {
      type: 1, // Action Row
      components: [
        {
          type: 2, // Button
          custom_id: `ticket_claim_${ticketId.toString()}`,
          label: 'Claim',
          style: 1, // Primary
          emoji: { name: '👋' }
        },
        {
          type: 2, // Button
          custom_id: `ticket_close_${ticketId.toString()}`,
          label: 'Close',
          style: 4, // Danger
          emoji: { name: '🔒' }
        }
      ]
    };

    // Send ticket message
    await ticketChannel.send({
      content: `${member} | Staff will be notified`,
      components: [ticketEmbed, ticketButtons],
      flags: MessageFlags.IsComponentsV2
    });

    // Log ticket creation
    const logChannelId = supportConfig.logChannelId || dashboardConfig.bot.ticketLogChannelId;
    if (logChannelId) {
      try {
        await interaction.client.rest.post(`/channels/${logChannelId}/messages`, {
          body: {
            embeds: [{
              title: 'New Support Ticket',
              color: 0x00ff00,
              fields: [
                { name: 'User', value: `<@${memberId}>`, inline: true },
                { name: 'Category', value: category.name, inline: true },
                { name: 'Channel', value: `<#${ticketChannel.id}>`, inline: true },
                { name: 'Ticket ID', value: ticketId.toString(), inline: true }
              ],
              timestamp: new Date().toISOString()
            }]
          }
        });
      } catch (error) {
        logger.warn('Failed to send ticket log', { error });
      }
    }

    // Success response
    const successContainer = {
      type: 17, // Container
      accent_color: 0x00FF00, // Green for success
      components: [
        {
          type: 10, // Text
          content: `✅ **Support Ticket Created!**\n\n**Category:** ${category.emoji} ${category.name}\n**Channel:** <#${ticketChannel.id}>\n**Ticket ID:** ${ticketId.toString()}\n\nYour support channel has been created. Please head over to discuss your issue with our staff team.`
        }
      ]
    };

    await interaction.editReply({
      components: [successContainer]
    });

    logger.info('Support ticket created', {
      ticketId: ticketId.toString(),
      userId: memberId,
      category: selectedCategory,
      channelId: ticketChannel.id
    });

  } catch (error) {
    logger.error('Failed to create support ticket', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id,
      category: selectedCategory
    });
    
    const errorContainer = {
      type: 17, // Container
      accent_color: 0xFF0000, // Red for error
      components: [
        {
          type: 10, // Text
          content: `❌ **Failed to Create Support Ticket**\nThere was an error creating your support ticket. Please try again or contact an administrator.`
        }
      ]
    };
    
    if (interaction.deferred) {
      await interaction.editReply({
        components: [errorContainer]
      });
    } else {
      await interaction.reply({
        components: [errorContainer],
        flags: MessageFlags.IsComponentsV2,
        ephemeral: true
      });
    }
  }
}

async function handleGetTicketsButton(interaction: ButtonInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  const features = config.settings.features || {};
  
  // Check if tickets feature is disabled
  if (features.tickets?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: features.tickets.maintenanceMessage || "🔧 **Feature Under Maintenance**\nThis feature is currently disabled. Please check back later!"
        }
      ]
    };

    await interaction.reply({
      components: [maintenanceContainer],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });
    return;
  }
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const supportConfig = config.settings.supportConfig || {};
  const categories = supportConfig.categories || [];
  
  if (categories.length === 0) {
    await interaction.reply({
      content: '❌ No support categories are configured.',
      ephemeral: true
    });
    return;
  }

  try {
    // Create support selection container
    const supportContainer = {
      type: 17, // Container
      accent_color: 0x3B82F6, // Blue for support
      components: [
        {
          type: 10, // Text - Header
          content: `# 🎫 Support Ticket System\n## Select a category for your support request`
        },
        {
          type: 10, // Text - Instructions
          content: `Choose the category that best describes your issue. A private support channel will be created for you to discuss your concern with our staff team.`
        }
      ]
    };

    // Create category select menu
    const categorySelectMenu = {
      type: 1, // Action Row
      components: [
        {
          type: 3, // String Select Menu
          custom_id: 'support_category_select',
          placeholder: '🎫 Select a support category...',
          min_values: 1,
          max_values: 1,
          options: categories.map((category: any) => ({
            label: category.name,
            value: category.value,
            description: category.description,
            emoji: { name: category.emoji }
          }))
        }
      ]
    };

    await interaction.reply({
      components: [supportContainer, categorySelectMenu],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });

  } catch (error) {
    logger.error('Failed to handle get tickets button', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    await interaction.reply({
      content: '❌ An error occurred while loading the support menu.',
      ephemeral: true
    });
  }
}

async function handleGetSuggestionsButton(interaction: ButtonInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  const features = config.settings.features || {};
  
  // Check if suggestions feature is disabled
  if (features.suggestions?.enabled === false) {
    const maintenanceContainer = {
      type: 17, // Container
      accent_color: 0xFFA500, // Orange for maintenance
      components: [
        {
          type: 10, // Text
          content: features.suggestions.maintenanceMessage || "🔧 **Feature Under Maintenance**\nThis feature is currently disabled. Please check back later!"
        }
      ]
    };

    await interaction.reply({
      components: [maintenanceContainer],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });
    return;
  }
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const suggestionConfig = config.settings.suggestionConfig || {};
  const categories = suggestionConfig.categories || [];
  
  if (categories.length === 0) {
    await interaction.reply({
      content: '❌ No suggestion categories are configured.',
      ephemeral: true
    });
    return;
  }

  try {
    // Create suggestion selection container
    const suggestionContainer = {
      type: 17, // Container
      accent_color: 0x10B981, // Green for suggestions
      components: [
        {
          type: 10, // Text - Header
          content: `# 💡 Suggestion System\n## Share your ideas with the community`
        },
        {
          type: 10, // Text - Instructions
          content: `Choose a category for your suggestion. Your idea will be posted in the suggestions channel where the community can vote and discuss it.${suggestionConfig.anonymousMode ? '\n\n🕶️ **Anonymous Mode Available** - You can choose to submit suggestions anonymously.' : ''}`
        }
      ]
    };

    // Create category select menu
    const categorySelectMenu = {
      type: 1, // Action Row
      components: [
        {
          type: 3, // String Select Menu
          custom_id: 'suggestion_category_select',
          placeholder: '💡 Select a suggestion category...',
          min_values: 1,
          max_values: 1,
          options: categories.map((category: any) => ({
            label: category.name,
            value: category.value,
            description: category.description,
            emoji: { name: category.emoji }
          }))
        }
      ]
    };

    await interaction.reply({
      components: [suggestionContainer, categorySelectMenu],
      flags: MessageFlags.IsComponentsV2,
      ephemeral: true
    });

  } catch (error) {
    logger.error('Failed to handle get suggestions button', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id
    });
    
    await interaction.reply({
      content: '❌ An error occurred while loading the suggestion menu.',
      ephemeral: true
    });
  }
}

async function handleSuggestionCategorySelect(interaction: StringSelectMenuInteraction) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const selectedCategory = interaction.values[0];
  const suggestionConfig = config.settings.suggestionConfig || {};
  const categories = suggestionConfig.categories || [];
  const category = categories.find((c: any) => c.value === selectedCategory);
  
  if (!category) {
    await interaction.reply({
      content: '❌ Invalid category selected.',
      ephemeral: true
    });
    return;
  }

  try {
    const memberId = 'user' in interaction.member ? interaction.member.user.id : interaction.user.id;
    const member = await interaction.guild.members.fetch(memberId);
    
    if (!member) {
      await interaction.reply({
        content: '❌ Could not find your member information.',
        ephemeral: true
      });
      return;
    }

    // Create modal components (modals can't use containers, only action rows with text inputs)
    const modalComponents = [
      {
        type: 1, // Action Row
        components: [
          {
            type: 4, // Text Input
            custom_id: 'suggestion_title',
            label: 'Suggestion Title',
            style: 1, // Short
            placeholder: 'Brief title for your suggestion...',
            required: true,
            max_length: 100
          }
        ]
      },
      {
        type: 1, // Action Row
        components: [
          {
            type: 4, // Text Input
            custom_id: 'suggestion_description',
            label: 'Detailed Description',
            style: 2, // Paragraph
            placeholder: 'Explain your suggestion in detail. What would you like to see? How would it benefit the community?',
            required: true,
            max_length: 2000
          }
        ]
      }
    ];

    // Add anonymous option if enabled
    if (suggestionConfig.anonymousMode) {
      modalComponents.push({
        type: 1, // Action Row
        components: [
          {
            type: 4, // Text Input
            custom_id: 'suggestion_anonymous',
            label: 'Submit Anonymously? (yes/no)',
            style: 1, // Short
            placeholder: 'Type "yes" to submit anonymously, or "no" to show your name',
            required: false,
            max_length: 3
          }
        ]
      });
    }

    // Store category in custom_id for form submission
    const formData = {
      custom_id: `suggestion_form_${selectedCategory}`,
      title: `${category.emoji} ${category.name} Suggestion`,
      components: modalComponents
    };

    await interaction.showModal(formData);

  } catch (error) {
    logger.error('Failed to handle suggestion category select', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id,
      category: selectedCategory
    });
    
    await interaction.reply({
      content: '❌ An error occurred while loading the suggestion form.',
      ephemeral: true
    });
  }
}

async function handleSuggestionFormSubmit(interaction: any) {
  const logger = Logger.createAddonLogger(config.addon.name);
  
  if (!interaction.guild || !interaction.member) {
    await interaction.reply({
      content: '❌ This command can only be used in a server.',
      ephemeral: true
    });
    return;
  }

  const selectedCategory = interaction.customId.replace('suggestion_form_', '');
  const suggestionConfig = config.settings.suggestionConfig || {};
  const categories = suggestionConfig.categories || [];
  const category = categories.find((c: any) => c.value === selectedCategory);
  
  if (!category) {
    await interaction.reply({
      content: '❌ Invalid category.',
      ephemeral: true
    });
    return;
  }

  try {
    // Defer reply to prevent timeout during processing
    await interaction.deferReply({ ephemeral: true });

    const memberId = 'user' in interaction.member ? interaction.member.user.id : interaction.user.id;
    const member = await interaction.guild.members.fetch(memberId);
    
    if (!member) {
      await interaction.editReply({
        content: '❌ Could not find your member information.'
      });
      return;
    }

    // Get form data
    const title = interaction.fields.getTextInputValue('suggestion_title');
    const description = interaction.fields.getTextInputValue('suggestion_description');
    const anonymousInput = interaction.fields.getTextInputValue('suggestion_anonymous') || 'no';
    const isAnonymous = anonymousInput.toLowerCase().includes('yes');

    // Get suggestions channel
    const channelId = suggestionConfig.channelId;
    if (!channelId) {
      await interaction.editReply({
        content: '❌ Suggestions channel is not configured. Please contact an administrator.'
      });
      return;
    }

    const suggestionsChannel = await interaction.guild.channels.fetch(channelId);
    if (!suggestionsChannel || suggestionsChannel.type !== ChannelType.GuildText) {
      await interaction.editReply({
        content: '❌ Invalid suggestions channel configuration.'
      });
      return;
    }

    // Create suggestion in database
    const suggestionsCol = await getSuggestionsDb();
    const suggestionDoc = {
      title,
      description,
      category: selectedCategory,
      authorId: memberId,
      guildId: interaction.guild.id,
      anonymous: isAnonymous,
      status: suggestionConfig.requireApproval ? 'pending' : 'approved',
      createdAt: new Date(),
      votes: {
        up: 0,
        down: 0,
        neutral: 0
      },
      voters: []
    };

    const result = await suggestionsCol.insertOne(suggestionDoc);
    const suggestionId = result.insertedId;

    // Create suggestion embed using regular Discord embed
    const suggestionEmbed = {
      title: `${category.emoji} ${title}`,
      description: description,
      color: category.color || 0x10B981,
      fields: [
        {
          name: 'Category',
          value: category.name,
          inline: true
        },
        {
          name: 'Suggested by',
          value: isAnonymous ? 'Anonymous' : member.user.username,
          inline: true
        },
        {
          name: 'Status',
          value: suggestionConfig.requireApproval ? 'Pending Approval' : 'Open for Voting',
          inline: true
        }
      ],
      footer: {
        text: `Suggestion ID: ${suggestionId.toString()}`
      },
      timestamp: new Date().toISOString()
    };

    // Create message data
    const messageData: any = {
      embeds: [suggestionEmbed]
    };
    
    // Add voting buttons if voting is enabled and not requiring approval
    if (suggestionConfig.voting?.enabled && !suggestionConfig.requireApproval) {
      const votingButtons = {
        type: 1, // Action Row
        components: [
          {
            type: 2, // Button
            custom_id: `suggestion_vote_up_${suggestionId.toString()}`,
            label: `${suggestionConfig.voting.upvoteEmoji || '👍'} 0`,
            style: 3, // Success (green)
          },
          {
            type: 2, // Button
            custom_id: `suggestion_vote_down_${suggestionId.toString()}`,
            label: `${suggestionConfig.voting.downvoteEmoji || '👎'} 0`,
            style: 4, // Danger (red)
          },
          {
            type: 2, // Button
            custom_id: `suggestion_vote_neutral_${suggestionId.toString()}`,
            label: `${suggestionConfig.voting.neutralEmoji || '🤷'} 0`,
            style: 2, // Secondary (gray)
          }
        ]
      };
      messageData.components = [votingButtons];
    }

    // Post suggestion to channel
    const suggestionMessage = await suggestionsChannel.send(messageData);

    // Update database with message ID
    await suggestionsCol.updateOne(
      { _id: suggestionId },
      { $set: { messageId: suggestionMessage.id } }
    );

    // Log suggestion creation
    const logChannelId = suggestionConfig.logChannelId;
    if (logChannelId) {
      try {
        await interaction.client.rest.post(`/channels/${logChannelId}/messages`, {
          body: {
            embeds: [{
              title: 'New Suggestion',
              color: category.color || 0x10B981,
              fields: [
                { name: 'User', value: isAnonymous ? 'Anonymous' : `<@${memberId}>`, inline: true },
                { name: 'Category', value: category.name, inline: true },
                { name: 'Title', value: title, inline: false },
                { name: 'Status', value: suggestionConfig.requireApproval ? 'Pending Approval' : 'Posted', inline: true },
                { name: 'Suggestion ID', value: suggestionId.toString(), inline: true }
              ],
              timestamp: new Date().toISOString()
            }]
          }
        });
      } catch (error) {
        logger.warn('Failed to send suggestion log', { error });
      }
    }

    // Success response
    const successContainer = {
      type: 17, // Container
      accent_color: 0x00FF00, // Green for success
      components: [
        {
          type: 10, // Text
          content: suggestionConfig.requireApproval ? 
            `✅ **Suggestion Submitted!**\n\n**Title:** ${title}\n**Category:** ${category.emoji} ${category.name}\n**Status:** Pending staff approval\n**Suggestion ID:** ${suggestionId.toString()}\n\nYour suggestion has been submitted and is awaiting staff approval before being posted to the community.` :
            `✅ **Suggestion Posted!**\n\n**Title:** ${title}\n**Category:** ${category.emoji} ${category.name}\n**Channel:** <#${channelId}>\n**Suggestion ID:** ${suggestionId.toString()}\n\nYour suggestion has been posted to the community! ${suggestionConfig.voting?.enabled ? 'Members can now vote on your idea.' : ''}`
        }
      ]
    };

    await interaction.editReply({
      components: [successContainer]
    });

    logger.info('Suggestion created', {
      suggestionId: suggestionId.toString(),
      userId: memberId,
      category: selectedCategory,
      title,
      anonymous: isAnonymous,
      requiresApproval: suggestionConfig.requireApproval
    });

  } catch (error) {
    logger.error('Failed to create suggestion', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: 'user' in interaction.member ? interaction.member.user.id : interaction.user.id,
      category: selectedCategory
    });
    
    const errorContainer = {
      type: 17, // Container
      accent_color: 0xFF0000, // Red for error
      components: [
        {
          type: 10, // Text
          content: `❌ **Failed to Create Suggestion**\nThere was an error creating your suggestion. Please try again or contact an administrator.`
        }
      ]
    };
    
    if (interaction.deferred) {
      await interaction.editReply({
        components: [errorContainer]
      });
    } else {
      await interaction.reply({
        components: [errorContainer],
        flags: MessageFlags.IsComponentsV2,
        ephemeral: true
      });
    }
  }
}

export default addon;
