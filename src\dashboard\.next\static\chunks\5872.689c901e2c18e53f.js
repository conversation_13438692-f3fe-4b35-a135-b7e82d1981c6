"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5872],{28245:(e,s,i)=>{i.d(s,{j:()=>c});var o=i(94513),n=i(55100),l=i(22697),r=i(9557),t=i(2923),a=i(33225);let c=(0,t.R)((e,s)=>{let{className:i,...t}=e,c=(0,l.cx)("chakra-modal__footer",i),E=(0,r.x5)(),d=(0,n.H2)({display:"flex",alignItems:"center",justifyContent:"flex-end",...E.footer});return(0,o.jsx)(a.B.footer,{ref:s,...t,__css:d,className:c})});c.displayName="ModalFooter"},35872:(e,s,i)=>{i.r(s),i.d(s,{default:()=>I});var o=i(94513),n=i(22907),l=i(9557),r=i(7680),t=i(52922),a=i(47847),c=i(59365),E=i(85104),d=i(79156),h=i(40443),m=i(63730),p=i(64057),A=i(78902),S=i(22237),_=i(71185),x=i(41611),N=i(51961),j=i(49217),u=i(25680),M=i(28245),C=i(62690),g=i(94285),f=i(97146);let R={General:{icon:f.pcC,permissions:["ADMINISTRATOR","VIEW_AUDIT_LOG","MANAGE_GUILD","MANAGE_ROLES","MANAGE_CHANNELS","MANAGE_EMOJIS_AND_STICKERS","MANAGE_WEBHOOKS","VIEW_CHANNEL"]},Text:{icon:f.mEP,permissions:["SEND_MESSAGES","EMBED_LINKS","ATTACH_FILES","ADD_REACTIONS","USE_EXTERNAL_EMOJIS","MENTION_EVERYONE","MANAGE_MESSAGES","READ_MESSAGE_HISTORY"]},Voice:{icon:f.o77,permissions:["CONNECT","SPEAK","STREAM","USE_VAD","PRIORITY_SPEAKER","MUTE_MEMBERS","DEAFEN_MEMBERS","MOVE_MEMBERS"]},Members:{icon:f.cfS,permissions:["KICK_MEMBERS","BAN_MEMBERS","CHANGE_NICKNAME","MANAGE_NICKNAMES","CREATE_INSTANT_INVITE"]}};function I(e){let{isOpen:s,onClose:i,onSuccess:f,role:I}=e,T=(0,n.d)(),[b,k]=(0,g.useState)(!1),[O,y]=(0,g.useState)({name:"",color:"#99AAB5",permissions:[],hoist:!1,mentionable:!1});(0,g.useEffect)(()=>{I&&y({name:I.name||"",color:I.color||"#99AAB5",permissions:I.permissions||[],hoist:I.hoist||!1,mentionable:I.mentionable||!1})},[I]);let B=async()=>{k(!0);try{let e=await fetch("/api/discord/roles/".concat(I.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(O)});if(!e.ok){let s=await e.json();throw Error(s.message||"Failed to update role")}T({title:"Success",description:"Role updated successfully",status:"success",duration:3e3}),f(),i()}catch(e){T({title:"Error",description:e.message||"Failed to update role",status:"error",duration:5e3})}finally{k(!1)}},G=e=>{y(s=>({...s,permissions:s.permissions.includes(e)?s.permissions.filter(s=>s!==e):[...s.permissions,e]}))},D=(e,s)=>{y(i=>({...i,[e]:s}))};return(0,o.jsxs)(l.aF,{isOpen:s,onClose:i,size:"xl",scrollBehavior:"inside",children:[(0,o.jsx)(r.m,{backdropFilter:"blur(10px)"}),(0,o.jsxs)(t.$,{bg:"gray.800",children:[(0,o.jsx)(a.r,{children:"Edit Role"}),(0,o.jsx)(c.s,{}),(0,o.jsx)(E.c,{children:(0,o.jsxs)(d.T,{spacing:6,children:[(0,o.jsxs)(h.MJ,{children:[(0,o.jsx)(m.l,{children:"Role Name"}),(0,o.jsx)(p.p,{placeholder:"Enter role name",value:O.name,onChange:e=>D("name",e.target.value)})]}),(0,o.jsxs)(h.MJ,{children:[(0,o.jsx)(m.l,{children:"Role Color"}),(0,o.jsx)(p.p,{type:"color",value:O.color,onChange:e=>D("color",e.target.value)})]}),(0,o.jsx)(h.MJ,{children:(0,o.jsxs)(A.z,{spacing:4,children:[(0,o.jsx)(S.S,{isChecked:O.hoist,onChange:e=>D("hoist",e.target.checked),children:"Display role separately"}),(0,o.jsx)(S.S,{isChecked:O.mentionable,onChange:e=>D("mentionable",e.target.checked),children:"Allow anyone to @mention"})]})}),(0,o.jsx)(_.c,{}),(0,o.jsx)(x.E,{fontSize:"lg",fontWeight:"bold",alignSelf:"flex-start",children:"Permissions"}),Object.entries(R).map(e=>{let[s,i]=e;return(0,o.jsxs)(N.a,{w:"full",children:[(0,o.jsxs)(A.z,{mb:2,children:[(0,o.jsx)(j.I,{as:i.icon}),(0,o.jsx)(x.E,{fontWeight:"semibold",children:s})]}),(0,o.jsx)(u.r,{columns:2,spacing:2,children:i.permissions.map(e=>(0,o.jsx)(S.S,{isChecked:O.permissions.includes(e),onChange:()=>G(e),children:e.split("_").map(e=>e.charAt(0)+e.slice(1).toLowerCase()).join(" ")},e))})]},s)})]})}),(0,o.jsxs)(M.j,{children:[(0,o.jsx)(C.$,{variant:"ghost",mr:3,onClick:i,children:"Cancel"}),(0,o.jsx)(C.$,{colorScheme:"blue",onClick:B,isLoading:b,children:"Save Changes"})]})]})]})}},59365:(e,s,i)=>{i.d(s,{s:()=>a});var o=i(94513),n=i(22697),l=i(50614),r=i(9557),t=i(33021);let a=(0,i(2923).R)((e,s)=>{let{onClick:i,className:a,...c}=e,{onClose:E}=(0,r.k3)(),d=(0,n.cx)("chakra-modal__close-btn",a),h=(0,r.x5)();return(0,o.jsx)(t.J,{ref:s,__css:h.closeButton,className:d,onClick:(0,l.H)(i,e=>{e.stopPropagation(),E()}),...c})});a.displayName="ModalCloseButton"}}]);