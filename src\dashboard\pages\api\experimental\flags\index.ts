import type { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../auth/[...nextauth]';
import { getDb } from '../../../../apiHelpers/db';
import { dashboardConfig } from '../../../../core/config';

const ADMIN_ID = '933023999770918932';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // Global experimental features - available on all instances

  const session = await getServerSession(req, res, authOptions);
  if (!session) return res.status(401).json({ error: 'Unauthorized' });

  const db = await getDb();
  const collection = db.collection('experimental_flags');

  if (req.method === 'GET') {
    const flags = await collection.find({}).toArray();
    return res.status(200).json({ flags });
  }

  if (req.method === 'POST') {
    if (session.user?.id !== ADMIN_ID) {
      return res.status(403).json({ error: 'Forbidden' });
    }

    const { feature, enabled = false } = req.body;
    if (!feature) return res.status(400).json({ error: 'feature required' });

    await collection.updateOne(
      { feature },
      { $set: { feature, enabled, updatedBy: session.user.id, updatedAt: new Date() } },
      { upsert: true }
    );
    const flag = await collection.findOne({ feature });
    return res.status(200).json({ flag });
  }

  return res.status(405).json({ error: 'Method not allowed' });
} 