"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/automation/welcome";
exports.ids = ["pages/api/automation/welcome"];
exports.modules = {

/***/ "(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fwelcome&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cautomation%5Cwelcome.ts&middlewareConfigBase64=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fwelcome&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cautomation%5Cwelcome.ts&middlewareConfigBase64=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages-api/module.compiled */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _pages_api_automation_welcome_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pages\\api\\automation\\welcome.ts */ \"(api-node)/./pages/api/automation/welcome.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_automation_welcome_ts__WEBPACK_IMPORTED_MODULE_3__, 'default'));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_api_automation_welcome_ts__WEBPACK_IMPORTED_MODULE_3__, 'config');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/automation/welcome\",\n        pathname: \"/api/automation/welcome\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    userland: _pages_api_automation_welcome_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fwelcome&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cautomation%5Cwelcome.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api-node)/../core/ConfigManager.ts":
/*!********************************!*\
  !*** ../core/ConfigManager.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigManager: () => (/* binding */ ConfigManager)\n/* harmony export */ });\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nclass ConfigManager {\n    static{\n        this.configPath = ConfigManager.findConfigPath();\n    }\n    static{\n        this.config = null;\n    }\n    static findConfigPath() {\n        // Try multiple possible locations for config.yml\n        const possiblePaths = [\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'config.yml'),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), '..', '..', 'config.yml'),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), '..', 'config.yml'),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(__dirname, '..', '..', 'config.yml'),\n            path__WEBPACK_IMPORTED_MODULE_2___default().join(__dirname, '..', '..', '..', 'config.yml')\n        ];\n        for (const configPath of possiblePaths){\n            if (fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(configPath)) {\n                return configPath;\n            }\n        }\n        // Default fallback\n        return path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), 'config.yml');\n    }\n    static load(configPath) {\n        if (configPath) {\n            ConfigManager.configPath = configPath;\n        }\n        const fullPath = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(ConfigManager.configPath);\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(fullPath)) {\n            throw new Error(`Configuration file not found: ${fullPath}`);\n        }\n        try {\n            const fileContent = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(fullPath, 'utf8');\n            const config = yaml__WEBPACK_IMPORTED_MODULE_0___default().parse(fileContent);\n            ConfigManager.validateConfig(config);\n            ConfigManager.config = config;\n            return config;\n        } catch (error) {\n            if (error instanceof Error) {\n                throw new Error(`Failed to load configuration: ${error.message}`);\n            }\n            throw new Error('Failed to load configuration: Unknown error');\n        }\n    }\n    static get() {\n        if (!ConfigManager.config) {\n            throw new Error('Configuration not loaded. Call ConfigManager.load() first.');\n        }\n        return ConfigManager.config;\n    }\n    static reload() {\n        ConfigManager.config = null;\n        return ConfigManager.load();\n    }\n    static validateConfig(config) {\n        const requiredFields = [\n            'bot.token',\n            'bot.clientId',\n            'bot.prefix',\n            'logging.level',\n            'addons.enabled',\n            'addons.directory'\n        ];\n        for (const field of requiredFields){\n            if (!ConfigManager.getNestedValue(config, field)) {\n                throw new Error(`Missing required configuration field: ${field}`);\n            }\n        }\n        // Validate bot token format (basic check)\n        if (typeof config.bot.token !== 'string' || config.bot.token.length < 50) {\n            process.stdout.write('Warning: Bot token appears to be invalid or placeholder\\n');\n        }\n        // Validate client ID\n        if (typeof config.bot.clientId !== 'string' || !/^\\d+$/.test(config.bot.clientId)) {\n            process.stdout.write('Warning: Client ID appears to be invalid\\n');\n        }\n        // Validate logging level\n        const validLogLevels = [\n            'error',\n            'warn',\n            'info',\n            'debug'\n        ];\n        if (!validLogLevels.includes(config.logging.level)) {\n            throw new Error(`Invalid logging level: ${config.logging.level}. Must be one of: ${validLogLevels.join(', ')}`);\n        }\n        // Ensure required directories exist or can be created\n        const directories = [\n            config.logging.file?.path,\n            config.addons.directory,\n            path__WEBPACK_IMPORTED_MODULE_2___default().dirname(config.database?.path || 'data/bot.db')\n        ].filter(Boolean);\n        for (const dir of directories){\n            try {\n                if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(dir)) {\n                    fs__WEBPACK_IMPORTED_MODULE_1___default().mkdirSync(dir, {\n                        recursive: true\n                    });\n                }\n            } catch (error) {\n                process.stdout.write(`Warning: Could not create directory ${dir}: ${error}\\n`);\n            }\n        }\n    }\n    static getNestedValue(obj, path) {\n        return path.split('.').reduce((current, key)=>current?.[key], obj);\n    }\n    static watch(callback) {\n        const fullPath = path__WEBPACK_IMPORTED_MODULE_2___default().resolve(ConfigManager.configPath);\n        fs__WEBPACK_IMPORTED_MODULE_1___default().watchFile(fullPath, (curr, prev)=>{\n            if (curr.mtime !== prev.mtime) {\n                try {\n                    const newConfig = ConfigManager.reload();\n                    callback(newConfig);\n                } catch (error) {\n                    process.stderr.write(`Failed to reload configuration: ${error}\\n`);\n                }\n            }\n        });\n    }\n    static getConfig() {\n        if (!this.config) {\n            const configFile = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(this.configPath, 'utf8');\n            this.config = yaml__WEBPACK_IMPORTED_MODULE_0___default().parse(configFile);\n        }\n        return this.config;\n    }\n    static reloadConfig() {\n        const configFile = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(this.configPath, 'utf8');\n        this.config = yaml__WEBPACK_IMPORTED_MODULE_0___default().parse(configFile);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../core/ConfigManager.ts\n");

/***/ }),

/***/ "(api-node)/../core/DatabaseManager.ts":
/*!**********************************!*\
  !*** ../core/DatabaseManager.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DatabaseManager: () => (/* binding */ DatabaseManager)\n/* harmony export */ });\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongodb */ \"mongodb\");\n/* harmony import */ var mongodb__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongodb__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _Logger_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./Logger.js */ \"(api-node)/../core/Logger.js\");\n\n\nclass DatabaseManager {\n    constructor(config){\n        this.reconnectAttempts = 0;\n        this.maxReconnectAttempts = 5;\n        this.isConnecting = false;\n        this.connectionPromise = null;\n        this.config = config;\n        try {\n            this.logger = _Logger_js__WEBPACK_IMPORTED_MODULE_1__.Logger.createLogger();\n        } catch (error) {\n            // Fallback logger for dashboard environment\n            this.logger = {\n                info: console.log,\n                error: console.error,\n                warn: console.warn,\n                debug: console.debug\n            };\n        }\n    }\n    async connect() {\n        // Prevent multiple connection attempts\n        if (this.connectionPromise) {\n            return this.connectionPromise;\n        }\n        if (this.client && this.db) {\n            // Already connected\n            return Promise.resolve();\n        }\n        this.connectionPromise = this._connect();\n        return this.connectionPromise;\n    }\n    async _connect() {\n        try {\n            this.isConnecting = true;\n            // Optimized MongoDB connection options\n            const options = {\n                // Connection Pool Settings\n                maxPoolSize: 20,\n                minPoolSize: 5,\n                maxIdleTimeMS: 30000,\n                // Connection Timeout Settings\n                connectTimeoutMS: 10000,\n                socketTimeoutMS: 45000,\n                serverSelectionTimeoutMS: 10000,\n                // Performance Optimizations\n                retryWrites: true,\n                retryReads: true,\n                compressors: [\n                    'zlib'\n                ],\n                // Monitoring and Health\n                heartbeatFrequencyMS: 10000,\n                // Additional optimizations from config\n                ...this.config.database.options\n            };\n            this.client = new mongodb__WEBPACK_IMPORTED_MODULE_0__.MongoClient(this.config.database.url, options);\n            await this.client.connect();\n            this.db = this.client.db(this.config.database.name);\n            // Set up connection event listeners for better monitoring\n            this.client.on('connectionPoolCreated', ()=>{\n                this.logger.debug('MongoDB connection pool created');\n            });\n            this.client.on('connectionPoolClosed', ()=>{\n                this.logger.debug('MongoDB connection pool closed');\n            });\n            this.client.on('connectionCreated', ()=>{\n                this.logger.debug('New MongoDB connection created');\n            });\n            // Test the connection\n            await this.db.admin().ping();\n            this.logger.info(`✅ Connected to MongoDB: ${this.config.database.name}`);\n            this.reconnectAttempts = 0;\n            // Create indexes for better query performance\n            await this.createIndexes();\n        } catch (error) {\n            this.logger.error('Failed to connect to MongoDB:', error);\n            this.reconnectAttempts++;\n            if (this.reconnectAttempts < this.maxReconnectAttempts) {\n                this.logger.info(`Retrying connection (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);\n                await new Promise((resolve)=>setTimeout(resolve, 5000)); // Wait 5 seconds before retry\n                return this._connect();\n            }\n            throw error;\n        } finally{\n            this.isConnecting = false;\n            this.connectionPromise = null;\n        }\n    }\n    async disconnect() {\n        if (this.client) {\n            await this.client.close();\n            this.logger.info('Disconnected from MongoDB');\n        }\n    }\n    // Helper method to ensure connection before operations\n    async ensureConnection() {\n        if (!this.client || !this.db) {\n            await this.connect();\n        }\n    }\n    // Optimized helper methods for common database operations\n    async createIndexes() {\n        try {\n            // Create indexes for performance optimization\n            const indexPromises = [\n                // Command logs - for analytics and response time tracking\n                this.db.collection('command_logs').createIndex({\n                    timestamp: -1\n                }, {\n                    background: true,\n                    name: 'timestamp_desc'\n                }),\n                this.db.collection('command_logs').createIndex({\n                    commandName: 1,\n                    timestamp: -1\n                }, {\n                    background: true,\n                    name: 'command_timestamp'\n                }),\n                this.db.collection('command_logs').createIndex({\n                    userId: 1,\n                    timestamp: -1\n                }, {\n                    background: true,\n                    name: 'user_timestamp'\n                }),\n                // Error logs\n                this.db.collection('error_logs').createIndex({\n                    timestamp: -1\n                }, {\n                    background: true,\n                    name: 'error_timestamp_desc'\n                }),\n                this.db.collection('error_logs').createIndex({\n                    type: 1,\n                    timestamp: -1\n                }, {\n                    background: true,\n                    name: 'error_type_timestamp'\n                }),\n                // Command states - for quick command enable/disable checks\n                this.db.collection('command_states').createIndex({\n                    commandId: 1\n                }, {\n                    unique: true,\n                    background: true,\n                    name: 'command_id_unique'\n                }),\n                // Bot status\n                this.db.collection('bot_status').createIndex({\n                    key: 1\n                }, {\n                    unique: true,\n                    background: true,\n                    name: 'status_key_unique'\n                }),\n                // Guilds collection\n                this.db.collection('guilds').createIndex({\n                    guildId: 1\n                }, {\n                    unique: true,\n                    background: true,\n                    name: 'guild_id_unique'\n                }),\n                // Guild configurations\n                this.db.collection('guild_configs').createIndex({\n                    guildId: 1\n                }, {\n                    unique: true,\n                    background: true,\n                    name: 'guild_id_unique'\n                }),\n                // Users collection\n                this.db.collection('users').createIndex({\n                    userId: 1\n                }, {\n                    background: true,\n                    name: 'user_id'\n                }),\n                this.db.collection('users').createIndex({\n                    userId: 1,\n                    guildId: 1\n                }, {\n                    background: true,\n                    name: 'user_guild'\n                })\n            ];\n            await Promise.allSettled(indexPromises);\n            this.logger.debug('✅ Database indexes created/verified');\n        } catch (error) {\n            this.logger.error('Error creating database indexes:', error);\n        }\n    }\n    // Optimized query methods with connection checking\n    async findOne(collection, query, options) {\n        await this.ensureConnection();\n        return this.db.collection(collection).findOne(query, options);\n    }\n    async find(collection, query, options) {\n        await this.ensureConnection();\n        return this.db.collection(collection).find(query, options).toArray();\n    }\n    async insertOne(collection, document, options) {\n        await this.ensureConnection();\n        return this.db.collection(collection).insertOne(document, options);\n    }\n    async updateOne(collection, filter, update, options) {\n        await this.ensureConnection();\n        return this.db.collection(collection).updateOne(filter, update, options);\n    }\n    async deleteOne(collection, filter, options) {\n        await this.ensureConnection();\n        return this.db.collection(collection).deleteOne(filter, options);\n    }\n    async countDocuments(collection, query, options) {\n        await this.ensureConnection();\n        return this.db.collection(collection).countDocuments(query, options);\n    }\n    // Health check method with connection pool stats\n    getStatus() {\n        return {\n            details: {\n                database: this.config.database.name,\n                connected: this.client !== null && this.db !== null,\n                reconnectAttempts: this.reconnectAttempts,\n                isConnecting: this.isConnecting,\n                hasClient: !!this.client,\n                hasDatabase: !!this.db\n            }\n        };\n    }\n    // Method to get database statistics\n    async getStats() {\n        await this.ensureConnection();\n        try {\n            const stats = await this.db.stats();\n            return {\n                collections: stats['collections'],\n                documents: stats['objects'],\n                dataSize: stats['dataSize'],\n                storageSize: stats['storageSize'],\n                indexes: stats['indexes'],\n                indexSize: stats['indexSize']\n            };\n        } catch (error) {\n            this.logger.error('Failed to get database stats:', error);\n            throw error;\n        }\n    }\n    // Method to warm up the connection pool\n    async warmupConnections() {\n        await this.ensureConnection();\n        try {\n            // Perform a few lightweight operations to establish connections\n            const warmupPromises = Array.from({\n                length: 3\n            }, ()=>this.db.admin().ping());\n            await Promise.all(warmupPromises);\n            this.logger.debug('Database connection pool warmed up');\n        } catch (error) {\n            this.logger.warn('Failed to warm up connection pool:', error);\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../core/DatabaseManager.ts\n");

/***/ }),

/***/ "(api-node)/../core/Logger.js":
/*!*************************!*\
  !*** ../core/Logger.js ***!
  \*************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n\nclass Logger {\n    static write(message, level = 'INFO') {\n        const timestamp = new Date().toISOString();\n        const logEntry = `[${timestamp}] ${level}: ${message}\\n`;\n        // Write to file\n        fs.appendFileSync('logs/app.log', logEntry);\n        // Write to console\n        switch(level.toUpperCase()){\n            case 'ERROR':\n                console.error(logEntry);\n                break;\n            case 'WARNING':\n                console.warn(logEntry);\n                break;\n            default:\n                console.log(logEntry);\n        }\n    }\n    static error(message) {\n        Logger.write(message, 'ERROR');\n    }\n    static warning(message) {\n        Logger.write(message, 'WARNING');\n    }\n    static info(message) {\n        Logger.write(message, 'INFO');\n    }\n}\nmodule.exports = Logger;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/../core/Logger.js\n");

/***/ }),

/***/ "(api-node)/./core/config.ts":
/*!************************!*\
  !*** ./core/config.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dashboardConfig: () => (/* binding */ dashboardConfig),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yaml */ \"yaml\");\n/* harmony import */ var yaml__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(yaml__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n// @ts-nocheck\n\n\n\nlet config = {};\ntry {\n    // Locate config.yml by walking up directories (dashboard may run from nested cwd)\n    const possible = [\n        'config.yml',\n        '../config.yml',\n        '../../config.yml',\n        '../../../config.yml',\n        '../../../../config.yml'\n    ].map((rel)=>path__WEBPACK_IMPORTED_MODULE_2__.resolve(process.cwd(), rel));\n    let configPath = possible.find((p)=>fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(p));\n    if (!configPath) {\n        // fallback relative to file location\n        const dirBased = path__WEBPACK_IMPORTED_MODULE_2__.resolve(__dirname, '../../../config.yml');\n        if (fs__WEBPACK_IMPORTED_MODULE_0__.existsSync(dirBased)) configPath = dirBased;\n    }\n    if (!configPath) {\n        throw new Error('config.yml not found');\n    }\n    const fileContents = fs__WEBPACK_IMPORTED_MODULE_0__.readFileSync(configPath, 'utf8');\n    config = yaml__WEBPACK_IMPORTED_MODULE_1___default().parse(fileContents);\n} catch (error) {\n    console.error('Error: Failed to load config.yml:', error);\n    process.exit(1); // Exit if we can't load the config\n}\n// Export the dashboard-specific config\nconst dashboardConfig = {\n    bot: {\n        token: config.bot.token,\n        clientId: config.bot.clientId,\n        clientSecret: config.bot.clientSecret,\n        guildId: config.bot.guildId,\n        ticketCategoryId: config.bot.ticketCategoryId || null,\n        ticketLogChannelId: config.bot.ticketLogChannelId || null,\n        prefix: config.bot.prefix\n    },\n    dashboard: {\n        admins: config.dashboard?.admins || [],\n        adminRoleIds: config.dashboard?.adminRoleIds || [],\n        session: {\n            secret: config.dashboard?.session?.secret || config.bot.clientSecret\n        }\n    },\n    database: {\n        url: config.database.url,\n        name: config.database.name,\n        options: {\n            maxPoolSize: config.database.options?.maxPoolSize || 10,\n            minPoolSize: config.database.options?.minPoolSize || 1,\n            maxIdleTimeMS: config.database.options?.maxIdleTimeMS || 30000,\n            serverSelectionTimeoutMS: config.database.options?.serverSelectionTimeoutMS || 5000,\n            socketTimeoutMS: config.database.options?.socketTimeoutMS || 45000,\n            connectTimeoutMS: config.database.options?.connectTimeoutMS || 10000,\n            retryWrites: config.database.options?.retryWrites !== false,\n            retryReads: config.database.options?.retryReads !== false\n        }\n    }\n};\n// Validate required configuration\nif (!dashboardConfig.bot.token) {\n    console.error('Error: Discord bot token is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.clientId || !dashboardConfig.bot.clientSecret) {\n    console.error('Error: Discord OAuth2 credentials (clientId and clientSecret) are required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.bot.guildId) {\n    console.error('Error: Guild ID is required in config.yml');\n    process.exit(1);\n}\nif (!dashboardConfig.database.url || !dashboardConfig.database.name) {\n    console.error('Error: Database configuration (url and name) is required in config.yml');\n    process.exit(1);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (dashboardConfig);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./core/config.ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/auth/[...nextauth].ts":
/*!*****************************************!*\
  !*** ./pages/api/auth/[...nextauth].ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authOptions: () => (/* binding */ authOptions),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"next-auth\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/discord */ \"next-auth/providers/discord\");\n/* harmony import */ var next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\nconst authOptions = {\n    providers: [\n        next_auth_providers_discord__WEBPACK_IMPORTED_MODULE_1___default()({\n            clientId: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientId,\n            clientSecret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n            authorization: {\n                params: {\n                    scope: 'identify email guilds'\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, account, profile }) {\n            if (account && profile) {\n                token.accessToken = account.access_token || null;\n                token.id = profile.id || null;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (session?.user) {\n                // Ensure we have valid values for serialization\n                const userId = token.id || null;\n                const accessToken = token.accessToken || null;\n                // Attach Discord user ID to session\n                session.user.id = userId;\n                session.user.accessToken = accessToken;\n                // Default to false for admin status\n                let isAdmin = false;\n                if (userId) {\n                    console.log('Checking admin status for user:', userId);\n                    // Check explicit admin IDs\n                    const adminIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.admins || [];\n                    console.log('Configured admin IDs:', adminIds);\n                    if (adminIds.includes(userId)) {\n                        console.log('User is in admin list');\n                        isAdmin = true;\n                    } else {\n                        console.log('User not in admin list, checking roles...');\n                        // Check roles if configured\n                        const roleIds = _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.adminRoleIds || [];\n                        console.log('Configured admin role IDs:', roleIds);\n                        if (roleIds.length && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token && _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId) {\n                            try {\n                                console.log('Fetching member roles from Discord API...');\n                                console.log('Bot token (first 20 chars):', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token.substring(0, 20) + '...');\n                                console.log('Guild ID:', _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId);\n                                console.log('User ID:', userId);\n                                console.log('Admin role IDs to check:', roleIds);\n                                const res = await fetch(`https://discord.com/api/v10/guilds/${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.guildId}/members/${userId}`, {\n                                    headers: {\n                                        Authorization: `Bot ${_core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.token}`\n                                    }\n                                });\n                                console.log('Discord API response status:', res.status);\n                                if (res.ok) {\n                                    const member = await res.json();\n                                    console.log('Member data:', JSON.stringify(member, null, 2));\n                                    console.log('Member roles:', member.roles);\n                                    isAdmin = roleIds.some((rid)=>member.roles?.includes(rid)) || false;\n                                    console.log('Has admin role:', isAdmin);\n                                } else {\n                                    const errorText = await res.text();\n                                    console.error('Failed to fetch member - Status:', res.status);\n                                    console.error('Error response:', errorText);\n                                }\n                            } catch (error) {\n                                console.error('Failed to fetch guild member:', error);\n                            }\n                        } else {\n                            console.log('No role IDs configured or missing bot token/guild ID');\n                        }\n                    }\n                } else {\n                    console.log('No user ID available');\n                }\n                // Set admin status\n                session.user.isAdmin = isAdmin;\n                console.log('Final admin status:', isAdmin);\n                // Ensure all session values are serializable\n                session.user = {\n                    ...session.user,\n                    id: session.user.id || null,\n                    accessToken: session.user.accessToken || null,\n                    isAdmin: session.user.isAdmin || false,\n                    name: session.user.name || null,\n                    email: session.user.email || null,\n                    image: session.user.image || null\n                };\n            }\n            return session;\n        },\n        async redirect ({ url, baseUrl }) {\n            // Dynamically derive equivalent localhost URL (same protocol & port)\n            const parsed = new URL(baseUrl);\n            const localhostBase = `${parsed.protocol}//localhost${parsed.port ? `:${parsed.port}` : ''}`;\n            if (url.startsWith(baseUrl) || url.startsWith(localhostBase)) {\n                return url;\n            }\n            return baseUrl;\n        }\n    },\n    secret: _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.dashboard.session.secret || _core_config__WEBPACK_IMPORTED_MODULE_2__.dashboardConfig.bot.clientSecret,\n    pages: {\n        signIn: '/signin'\n    },\n    // Always show auth errors, but only debug logs in development\n    debug: \"development\" === 'development',\n    logger: {\n        error: (code, metadata)=>{\n            console.error('[NextAuth Error]', code, metadata);\n        },\n        warn: (code)=>{\n            if (true) {\n                console.warn('[NextAuth Warn]', code);\n            }\n        },\n        debug: (code, metadata)=>{\n            if (true) {\n                console.debug('[NextAuth Debug]', code, metadata);\n            }\n        }\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/auth/[...nextauth].ts\n");

/***/ }),

/***/ "(api-node)/./pages/api/automation/welcome.ts":
/*!*****************************************!*\
  !*** ./pages/api/automation/welcome.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth/next */ \"next-auth/next\");\n/* harmony import */ var next_auth_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../auth/[...nextauth] */ \"(api-node)/./pages/api/auth/[...nextauth].ts\");\n/* harmony import */ var _core_DatabaseManager__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/core/DatabaseManager */ \"(api-node)/../core/DatabaseManager.ts\");\n/* harmony import */ var _core_ConfigManager__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/core/ConfigManager */ \"(api-node)/../core/ConfigManager.ts\");\n/* harmony import */ var _core_config__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../core/config */ \"(api-node)/./core/config.ts\");\n// @ts-nocheck\n\n\n\n\n\nconst config = _core_ConfigManager__WEBPACK_IMPORTED_MODULE_3__.ConfigManager.getConfig();\nconst dbManager = new _core_DatabaseManager__WEBPACK_IMPORTED_MODULE_2__.DatabaseManager(config);\nconst defaultSettings = {\n    welcome: {\n        enabled: false,\n        channelId: null,\n        message: 'Welcome {user} to {guild}! You are the {memberCount}th member.',\n        autoRoles: [],\n        embedColor: '#00FF00'\n    },\n    goodbye: {\n        enabled: false,\n        channelId: null,\n        message: 'Goodbye {user}! We will miss you.',\n        embedColor: '#FF0000'\n    }\n};\nasync function handler(req, res) {\n    const session = await (0,next_auth_next__WEBPACK_IMPORTED_MODULE_0__.getServerSession)(req, res, _auth_nextauth___WEBPACK_IMPORTED_MODULE_1__.authOptions);\n    if (!session) {\n        return res.status(401).json({\n            error: 'Unauthorized'\n        });\n    }\n    // Get guild ID from dashboard config instead of session\n    const guildId = _core_config__WEBPACK_IMPORTED_MODULE_4__.dashboardConfig.bot.guildId;\n    if (!guildId) {\n        return res.status(400).json({\n            error: 'Guild ID not found in configuration.'\n        });\n    }\n    try {\n        await dbManager.connect();\n        if (req.method === 'GET') {\n            const guildConfig = await dbManager.findOne('guild_configs', {\n                guildId\n            });\n            const settings = {\n                welcome: {\n                    ...defaultSettings.welcome,\n                    ...guildConfig?.welcome\n                },\n                goodbye: {\n                    ...defaultSettings.goodbye,\n                    ...guildConfig?.goodbye\n                }\n            };\n            return res.status(200).json(settings);\n        }\n        if (req.method === 'POST') {\n            const { welcome, goodbye } = req.body;\n            // Basic validation\n            if (typeof welcome?.enabled !== 'boolean' || typeof goodbye?.enabled !== 'boolean') {\n                return res.status(400).json({\n                    error: 'Invalid data format.'\n                });\n            }\n            const updateData = {\n                guildId,\n                welcome: {\n                    enabled: welcome.enabled,\n                    channelId: welcome.channelId || null,\n                    message: welcome.message || defaultSettings.welcome.message,\n                    autoRoles: welcome.autoRoles || [],\n                    embedColor: welcome.embedColor || defaultSettings.welcome.embedColor\n                },\n                goodbye: {\n                    enabled: goodbye.enabled,\n                    channelId: goodbye.channelId || null,\n                    message: goodbye.message || defaultSettings.goodbye.message,\n                    embedColor: goodbye.embedColor || defaultSettings.goodbye.embedColor\n                }\n            };\n            await dbManager.updateOne('guild_configs', {\n                guildId\n            }, {\n                $set: updateData\n            }, {\n                upsert: true\n            });\n            return res.status(200).json({\n                message: 'Settings updated successfully.'\n            });\n        }\n        res.setHeader('Allow', [\n            'GET',\n            'POST'\n        ]);\n        return res.status(405).end(`Method ${req.method} Not Allowed`);\n    } catch (error) {\n        console.error(`[API/automation/welcome] Error:`, error);\n        return res.status(500).json({\n            error: 'Internal Server Error'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaS1ub2RlKS8uL3BhZ2VzL2FwaS9hdXRvbWF0aW9uL3dlbGNvbWUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUFBLGNBQWM7QUFFb0M7QUFDRTtBQUNLO0FBQ0o7QUFDRTtBQUV2RCxNQUFNSyxTQUFTRiw4REFBYUEsQ0FBQ0csU0FBUztBQUN0QyxNQUFNQyxZQUFZLElBQUlMLGtFQUFlQSxDQUFDRztBQUV0QyxNQUFNRyxrQkFBa0I7SUFDdEJDLFNBQVM7UUFDUEMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsV0FBVyxFQUFFO1FBQ2JDLFlBQVk7SUFDZDtJQUNBQyxTQUFTO1FBQ1BMLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RFLFlBQVk7SUFDZDtBQUNGO0FBRWUsZUFBZUUsUUFBUUMsR0FBbUIsRUFBRUMsR0FBb0I7SUFDN0UsTUFBTUMsVUFBVSxNQUFNbkIsZ0VBQWdCQSxDQUFDaUIsS0FBS0MsS0FBS2pCLHdEQUFXQTtJQUM1RCxJQUFJLENBQUNrQixTQUFTO1FBQ1osT0FBT0QsSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztZQUFFQyxPQUFPO1FBQWU7SUFDdEQ7SUFFQSx3REFBd0Q7SUFDeEQsTUFBTUMsVUFBVW5CLHlEQUFlQSxDQUFDb0IsR0FBRyxDQUFDRCxPQUFPO0lBQzNDLElBQUksQ0FBQ0EsU0FBUztRQUNaLE9BQU9MLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUM7WUFBRUMsT0FBTztRQUF1QztJQUM5RTtJQUVBLElBQUk7UUFDRixNQUFNZixVQUFVa0IsT0FBTztRQUV2QixJQUFJUixJQUFJUyxNQUFNLEtBQUssT0FBTztZQUN4QixNQUFNQyxjQUFjLE1BQU1wQixVQUFVcUIsT0FBTyxDQUFDLGlCQUFpQjtnQkFBRUw7WUFBUTtZQUN2RSxNQUFNTSxXQUFXO2dCQUNmcEIsU0FBUztvQkFBRSxHQUFHRCxnQkFBZ0JDLE9BQU87b0JBQUUsR0FBR2tCLGFBQWFsQixPQUFPO2dCQUFDO2dCQUMvRE0sU0FBUztvQkFBRSxHQUFHUCxnQkFBZ0JPLE9BQU87b0JBQUUsR0FBR1ksYUFBYVosT0FBTztnQkFBQztZQUNqRTtZQUNBLE9BQU9HLElBQUlFLE1BQU0sQ0FBQyxLQUFLQyxJQUFJLENBQUNRO1FBQzlCO1FBRUEsSUFBSVosSUFBSVMsTUFBTSxLQUFLLFFBQVE7WUFDekIsTUFBTSxFQUFFakIsT0FBTyxFQUFFTSxPQUFPLEVBQUUsR0FBR0UsSUFBSWEsSUFBSTtZQUVyQyxtQkFBbUI7WUFDbkIsSUFBSSxPQUFPckIsU0FBU0MsWUFBWSxhQUFhLE9BQU9LLFNBQVNMLFlBQVksV0FBVztnQkFDbEYsT0FBT1EsSUFBSUUsTUFBTSxDQUFDLEtBQUtDLElBQUksQ0FBQztvQkFBRUMsT0FBTztnQkFBdUI7WUFDOUQ7WUFFQSxNQUFNUyxhQUFhO2dCQUNqQlI7Z0JBQ0FkLFNBQVM7b0JBQ1BDLFNBQVNELFFBQVFDLE9BQU87b0JBQ3hCQyxXQUFXRixRQUFRRSxTQUFTLElBQUk7b0JBQ2hDQyxTQUFTSCxRQUFRRyxPQUFPLElBQUlKLGdCQUFnQkMsT0FBTyxDQUFDRyxPQUFPO29CQUMzREMsV0FBV0osUUFBUUksU0FBUyxJQUFJLEVBQUU7b0JBQ2xDQyxZQUFZTCxRQUFRSyxVQUFVLElBQUlOLGdCQUFnQkMsT0FBTyxDQUFDSyxVQUFVO2dCQUN0RTtnQkFDQUMsU0FBUztvQkFDUEwsU0FBU0ssUUFBUUwsT0FBTztvQkFDeEJDLFdBQVdJLFFBQVFKLFNBQVMsSUFBSTtvQkFDaENDLFNBQVNHLFFBQVFILE9BQU8sSUFBSUosZ0JBQWdCTyxPQUFPLENBQUNILE9BQU87b0JBQzNERSxZQUFZQyxRQUFRRCxVQUFVLElBQUlOLGdCQUFnQk8sT0FBTyxDQUFDRCxVQUFVO2dCQUN0RTtZQUNGO1lBRUEsTUFBTVAsVUFBVXlCLFNBQVMsQ0FDdkIsaUJBQ0E7Z0JBQUVUO1lBQVEsR0FDVjtnQkFBRVUsTUFBTUY7WUFBVyxHQUNuQjtnQkFBRUcsUUFBUTtZQUFLO1lBR2pCLE9BQU9oQixJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO2dCQUFFVCxTQUFTO1lBQWlDO1FBQzFFO1FBRUFNLElBQUlpQixTQUFTLENBQUMsU0FBUztZQUFDO1lBQU87U0FBTztRQUN0QyxPQUFPakIsSUFBSUUsTUFBTSxDQUFDLEtBQUtnQixHQUFHLENBQUMsQ0FBQyxPQUFPLEVBQUVuQixJQUFJUyxNQUFNLENBQUMsWUFBWSxDQUFDO0lBQy9ELEVBQUUsT0FBT0osT0FBTztRQUNkZSxRQUFRZixLQUFLLENBQUMsQ0FBQywrQkFBK0IsQ0FBQyxFQUFFQTtRQUNqRCxPQUFPSixJQUFJRSxNQUFNLENBQUMsS0FBS0MsSUFBSSxDQUFDO1lBQUVDLE9BQU87UUFBd0I7SUFDL0Q7QUFDRiIsInNvdXJjZXMiOlsiRDpcXFVzZXJzXFxQZXRlIEdhbWluZyBQQ1xcRGVza3RvcFxcNDA0IEJvdFxcc3JjXFxkYXNoYm9hcmRcXHBhZ2VzXFxhcGlcXGF1dG9tYXRpb25cXHdlbGNvbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gQHRzLW5vY2hlY2tcclxuaW1wb3J0IHsgTmV4dEFwaVJlcXVlc3QsIE5leHRBcGlSZXNwb25zZSB9IGZyb20gJ25leHQnO1xyXG5pbXBvcnQgeyBnZXRTZXJ2ZXJTZXNzaW9uIH0gZnJvbSAnbmV4dC1hdXRoL25leHQnO1xyXG5pbXBvcnQgeyBhdXRoT3B0aW9ucyB9IGZyb20gJy4uL2F1dGgvWy4uLm5leHRhdXRoXSc7XHJcbmltcG9ydCB7IERhdGFiYXNlTWFuYWdlciB9IGZyb20gJ0AvY29yZS9EYXRhYmFzZU1hbmFnZXInO1xyXG5pbXBvcnQgeyBDb25maWdNYW5hZ2VyIH0gZnJvbSAnQC9jb3JlL0NvbmZpZ01hbmFnZXInO1xyXG5pbXBvcnQgeyBkYXNoYm9hcmRDb25maWcgfSBmcm9tICcuLi8uLi8uLi9jb3JlL2NvbmZpZyc7XHJcblxyXG5jb25zdCBjb25maWcgPSBDb25maWdNYW5hZ2VyLmdldENvbmZpZygpO1xyXG5jb25zdCBkYk1hbmFnZXIgPSBuZXcgRGF0YWJhc2VNYW5hZ2VyKGNvbmZpZyk7XHJcblxyXG5jb25zdCBkZWZhdWx0U2V0dGluZ3MgPSB7XHJcbiAgd2VsY29tZToge1xyXG4gICAgZW5hYmxlZDogZmFsc2UsXHJcbiAgICBjaGFubmVsSWQ6IG51bGwsXHJcbiAgICBtZXNzYWdlOiAnV2VsY29tZSB7dXNlcn0gdG8ge2d1aWxkfSEgWW91IGFyZSB0aGUge21lbWJlckNvdW50fXRoIG1lbWJlci4nLFxyXG4gICAgYXV0b1JvbGVzOiBbXSxcclxuICAgIGVtYmVkQ29sb3I6ICcjMDBGRjAwJyxcclxuICB9LFxyXG4gIGdvb2RieWU6IHtcclxuICAgIGVuYWJsZWQ6IGZhbHNlLFxyXG4gICAgY2hhbm5lbElkOiBudWxsLFxyXG4gICAgbWVzc2FnZTogJ0dvb2RieWUge3VzZXJ9ISBXZSB3aWxsIG1pc3MgeW91LicsXHJcbiAgICBlbWJlZENvbG9yOiAnI0ZGMDAwMCcsXHJcbiAgfSxcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGFzeW5jIGZ1bmN0aW9uIGhhbmRsZXIocmVxOiBOZXh0QXBpUmVxdWVzdCwgcmVzOiBOZXh0QXBpUmVzcG9uc2UpIHtcclxuICBjb25zdCBzZXNzaW9uID0gYXdhaXQgZ2V0U2VydmVyU2Vzc2lvbihyZXEsIHJlcywgYXV0aE9wdGlvbnMpO1xyXG4gIGlmICghc2Vzc2lvbikge1xyXG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAxKS5qc29uKHsgZXJyb3I6ICdVbmF1dGhvcml6ZWQnIH0pO1xyXG4gIH1cclxuXHJcbiAgLy8gR2V0IGd1aWxkIElEIGZyb20gZGFzaGJvYXJkIGNvbmZpZyBpbnN0ZWFkIG9mIHNlc3Npb25cclxuICBjb25zdCBndWlsZElkID0gZGFzaGJvYXJkQ29uZmlnLmJvdC5ndWlsZElkO1xyXG4gIGlmICghZ3VpbGRJZCkge1xyXG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDAwKS5qc29uKHsgZXJyb3I6ICdHdWlsZCBJRCBub3QgZm91bmQgaW4gY29uZmlndXJhdGlvbi4nIH0pO1xyXG4gIH1cclxuXHJcbiAgdHJ5IHtcclxuICAgIGF3YWl0IGRiTWFuYWdlci5jb25uZWN0KCk7XHJcblxyXG4gICAgaWYgKHJlcS5tZXRob2QgPT09ICdHRVQnKSB7XHJcbiAgICAgIGNvbnN0IGd1aWxkQ29uZmlnID0gYXdhaXQgZGJNYW5hZ2VyLmZpbmRPbmUoJ2d1aWxkX2NvbmZpZ3MnLCB7IGd1aWxkSWQgfSk7XHJcbiAgICAgIGNvbnN0IHNldHRpbmdzID0ge1xyXG4gICAgICAgIHdlbGNvbWU6IHsgLi4uZGVmYXVsdFNldHRpbmdzLndlbGNvbWUsIC4uLmd1aWxkQ29uZmlnPy53ZWxjb21lIH0sXHJcbiAgICAgICAgZ29vZGJ5ZTogeyAuLi5kZWZhdWx0U2V0dGluZ3MuZ29vZGJ5ZSwgLi4uZ3VpbGRDb25maWc/Lmdvb2RieWUgfSxcclxuICAgICAgfTtcclxuICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoMjAwKS5qc29uKHNldHRpbmdzKTtcclxuICAgIH1cclxuXHJcbiAgICBpZiAocmVxLm1ldGhvZCA9PT0gJ1BPU1QnKSB7XHJcbiAgICAgIGNvbnN0IHsgd2VsY29tZSwgZ29vZGJ5ZSB9ID0gcmVxLmJvZHk7XHJcblxyXG4gICAgICAvLyBCYXNpYyB2YWxpZGF0aW9uXHJcbiAgICAgIGlmICh0eXBlb2Ygd2VsY29tZT8uZW5hYmxlZCAhPT0gJ2Jvb2xlYW4nIHx8IHR5cGVvZiBnb29kYnllPy5lbmFibGVkICE9PSAnYm9vbGVhbicpIHtcclxuICAgICAgICByZXR1cm4gcmVzLnN0YXR1cyg0MDApLmpzb24oeyBlcnJvcjogJ0ludmFsaWQgZGF0YSBmb3JtYXQuJyB9KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgY29uc3QgdXBkYXRlRGF0YSA9IHtcclxuICAgICAgICBndWlsZElkLFxyXG4gICAgICAgIHdlbGNvbWU6IHtcclxuICAgICAgICAgIGVuYWJsZWQ6IHdlbGNvbWUuZW5hYmxlZCxcclxuICAgICAgICAgIGNoYW5uZWxJZDogd2VsY29tZS5jaGFubmVsSWQgfHwgbnVsbCxcclxuICAgICAgICAgIG1lc3NhZ2U6IHdlbGNvbWUubWVzc2FnZSB8fCBkZWZhdWx0U2V0dGluZ3Mud2VsY29tZS5tZXNzYWdlLFxyXG4gICAgICAgICAgYXV0b1JvbGVzOiB3ZWxjb21lLmF1dG9Sb2xlcyB8fCBbXSxcclxuICAgICAgICAgIGVtYmVkQ29sb3I6IHdlbGNvbWUuZW1iZWRDb2xvciB8fCBkZWZhdWx0U2V0dGluZ3Mud2VsY29tZS5lbWJlZENvbG9yLFxyXG4gICAgICAgIH0sXHJcbiAgICAgICAgZ29vZGJ5ZToge1xyXG4gICAgICAgICAgZW5hYmxlZDogZ29vZGJ5ZS5lbmFibGVkLFxyXG4gICAgICAgICAgY2hhbm5lbElkOiBnb29kYnllLmNoYW5uZWxJZCB8fCBudWxsLFxyXG4gICAgICAgICAgbWVzc2FnZTogZ29vZGJ5ZS5tZXNzYWdlIHx8IGRlZmF1bHRTZXR0aW5ncy5nb29kYnllLm1lc3NhZ2UsXHJcbiAgICAgICAgICBlbWJlZENvbG9yOiBnb29kYnllLmVtYmVkQ29sb3IgfHwgZGVmYXVsdFNldHRpbmdzLmdvb2RieWUuZW1iZWRDb2xvcixcclxuICAgICAgICB9LFxyXG4gICAgICB9O1xyXG5cclxuICAgICAgYXdhaXQgZGJNYW5hZ2VyLnVwZGF0ZU9uZShcclxuICAgICAgICAnZ3VpbGRfY29uZmlncycsXHJcbiAgICAgICAgeyBndWlsZElkIH0sXHJcbiAgICAgICAgeyAkc2V0OiB1cGRhdGVEYXRhIH0sXHJcbiAgICAgICAgeyB1cHNlcnQ6IHRydWUgfVxyXG4gICAgICApO1xyXG5cclxuICAgICAgcmV0dXJuIHJlcy5zdGF0dXMoMjAwKS5qc29uKHsgbWVzc2FnZTogJ1NldHRpbmdzIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5LicgfSk7XHJcbiAgICB9XHJcblxyXG4gICAgcmVzLnNldEhlYWRlcignQWxsb3cnLCBbJ0dFVCcsICdQT1NUJ10pO1xyXG4gICAgcmV0dXJuIHJlcy5zdGF0dXMoNDA1KS5lbmQoYE1ldGhvZCAke3JlcS5tZXRob2R9IE5vdCBBbGxvd2VkYCk7XHJcbiAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgIGNvbnNvbGUuZXJyb3IoYFtBUEkvYXV0b21hdGlvbi93ZWxjb21lXSBFcnJvcjpgLCBlcnJvcik7XHJcbiAgICByZXR1cm4gcmVzLnN0YXR1cyg1MDApLmpzb24oeyBlcnJvcjogJ0ludGVybmFsIFNlcnZlciBFcnJvcicgfSk7XHJcbiAgfVxyXG59XHJcbiJdLCJuYW1lcyI6WyJnZXRTZXJ2ZXJTZXNzaW9uIiwiYXV0aE9wdGlvbnMiLCJEYXRhYmFzZU1hbmFnZXIiLCJDb25maWdNYW5hZ2VyIiwiZGFzaGJvYXJkQ29uZmlnIiwiY29uZmlnIiwiZ2V0Q29uZmlnIiwiZGJNYW5hZ2VyIiwiZGVmYXVsdFNldHRpbmdzIiwid2VsY29tZSIsImVuYWJsZWQiLCJjaGFubmVsSWQiLCJtZXNzYWdlIiwiYXV0b1JvbGVzIiwiZW1iZWRDb2xvciIsImdvb2RieWUiLCJoYW5kbGVyIiwicmVxIiwicmVzIiwic2Vzc2lvbiIsInN0YXR1cyIsImpzb24iLCJlcnJvciIsImd1aWxkSWQiLCJib3QiLCJjb25uZWN0IiwibWV0aG9kIiwiZ3VpbGRDb25maWciLCJmaW5kT25lIiwic2V0dGluZ3MiLCJib2R5IiwidXBkYXRlRGF0YSIsInVwZGF0ZU9uZSIsIiRzZXQiLCJ1cHNlcnQiLCJzZXRIZWFkZXIiLCJlbmQiLCJjb25zb2xlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api-node)/./pages/api/automation/welcome.ts\n");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "mongodb":
/*!**************************!*\
  !*** external "mongodb" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("mongodb");

/***/ }),

/***/ "next-auth":
/*!****************************!*\
  !*** external "next-auth" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("next-auth");

/***/ }),

/***/ "next-auth/next":
/*!*********************************!*\
  !*** external "next-auth/next" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("next-auth/next");

/***/ }),

/***/ "next-auth/providers/discord":
/*!**********************************************!*\
  !*** external "next-auth/providers/discord" ***!
  \**********************************************/
/***/ ((module) => {

module.exports = require("next-auth/providers/discord");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "yaml":
/*!***********************!*\
  !*** external "yaml" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("yaml");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0"], () => (__webpack_exec__("(api-node)/../../node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fautomation%2Fwelcome&preferredRegion=&absolutePagePath=.%2Fpages%5Capi%5Cautomation%5Cwelcome.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();