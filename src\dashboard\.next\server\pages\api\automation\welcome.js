"use strict";(()=>{var e={};e.id=8679,e.ids=[8679],e.modules={264:(e,t)=>{Object.defineProperty(t,"A",{enumerable:!0,get:function(){return o}});var o=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},584:(e,t)=>{Object.defineProperty(t,"M",{enumerable:!0,get:function(){return function e(t,o){return o in t?t[o]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,o)):"function"==typeof t&&"default"===o?t:void 0}}})},2115:e=>{e.exports=require("yaml")},2518:e=>{e.exports=require("mongodb")},3433:(e,t,o)=>{e.exports=o(5600)},3873:e=>{e.exports=require("path")},5542:e=>{e.exports=require("next-auth")},5600:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},5806:e=>{e.exports=require("next-auth/next")},6315:(e,t,o)=>{o.r(t),o.d(t,{config:()=>P,default:()=>x,routeModule:()=>k});var n={};o.r(n);var i={};o.r(i),o.d(i,{default:()=>C});var s=o(3433),a=o(264),r=o(584),c=o(5806),l=o(8525),d=o(2518);class u{static write(e,t="INFO"){let o=new Date().toISOString(),n=`[${o}] ${t}: ${e}
`;fs.appendFileSync("logs/app.log",n),t.toUpperCase()}static error(e){u.write(e,"ERROR")}static warning(e){u.write(e,"WARNING")}static info(e){u.write(e,"INFO")}}module.exports=u;class g{constructor(e){this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.isConnecting=!1,this.connectionPromise=null,this.config=e,this.logger=n.default.createLogger()}async connect(){return this.connectionPromise?this.connectionPromise:this.client&&this.db?Promise.resolve():(this.connectionPromise=this._connect(),this.connectionPromise)}async _connect(){try{this.isConnecting=!0;let e={maxPoolSize:20,minPoolSize:5,maxIdleTimeMS:3e4,connectTimeoutMS:1e4,socketTimeoutMS:45e3,serverSelectionTimeoutMS:1e4,retryWrites:!0,retryReads:!0,compressors:["zlib"],heartbeatFrequencyMS:1e4,...this.config.database.options};this.client=new d.MongoClient(this.config.database.url,e),await this.client.connect(),this.db=this.client.db(this.config.database.name),this.client.on("connectionPoolCreated",()=>{this.logger.debug("MongoDB connection pool created")}),this.client.on("connectionPoolClosed",()=>{this.logger.debug("MongoDB connection pool closed")}),this.client.on("connectionCreated",()=>{this.logger.debug("New MongoDB connection created")}),await this.db.admin().ping(),this.logger.info(`✅ Connected to MongoDB: ${this.config.database.name}`),this.reconnectAttempts=0,await this.createIndexes()}catch(e){if(this.logger.error("Failed to connect to MongoDB:",e),this.reconnectAttempts++,this.reconnectAttempts<this.maxReconnectAttempts)return this.logger.info(`Retrying connection (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`),await new Promise(e=>setTimeout(e,5e3)),this._connect();throw e}finally{this.isConnecting=!1,this.connectionPromise=null}}async disconnect(){this.client&&(await this.client.close(),this.logger.info("Disconnected from MongoDB"))}async ensureConnection(){this.client&&this.db||await this.connect()}async createIndexes(){try{let e=[this.db.collection("command_logs").createIndex({timestamp:-1},{background:!0,name:"timestamp_desc"}),this.db.collection("command_logs").createIndex({commandName:1,timestamp:-1},{background:!0,name:"command_timestamp"}),this.db.collection("command_logs").createIndex({userId:1,timestamp:-1},{background:!0,name:"user_timestamp"}),this.db.collection("error_logs").createIndex({timestamp:-1},{background:!0,name:"error_timestamp_desc"}),this.db.collection("error_logs").createIndex({type:1,timestamp:-1},{background:!0,name:"error_type_timestamp"}),this.db.collection("command_states").createIndex({commandId:1},{unique:!0,background:!0,name:"command_id_unique"}),this.db.collection("bot_status").createIndex({key:1},{unique:!0,background:!0,name:"status_key_unique"}),this.db.collection("guilds").createIndex({guildId:1},{unique:!0,background:!0,name:"guild_id_unique"}),this.db.collection("guild_configs").createIndex({guildId:1},{unique:!0,background:!0,name:"guild_id_unique"}),this.db.collection("users").createIndex({userId:1},{background:!0,name:"user_id"}),this.db.collection("users").createIndex({userId:1,guildId:1},{background:!0,name:"user_guild"})];await Promise.allSettled(e),this.logger.debug("✅ Database indexes created/verified")}catch(e){this.logger.error("Error creating database indexes:",e)}}async findOne(e,t,o){return await this.ensureConnection(),this.db.collection(e).findOne(t,o)}async find(e,t,o){return await this.ensureConnection(),this.db.collection(e).find(t,o).toArray()}async insertOne(e,t,o){return await this.ensureConnection(),this.db.collection(e).insertOne(t,o)}async updateOne(e,t,o,n){return await this.ensureConnection(),this.db.collection(e).updateOne(t,o,n)}async deleteOne(e,t,o){return await this.ensureConnection(),this.db.collection(e).deleteOne(t,o)}async countDocuments(e,t,o){return await this.ensureConnection(),this.db.collection(e).countDocuments(t,o)}getStatus(){return{details:{database:this.config.database.name,connected:null!==this.client&&null!==this.db,reconnectAttempts:this.reconnectAttempts,isConnecting:this.isConnecting,hasClient:!!this.client,hasDatabase:!!this.db}}}async getStats(){await this.ensureConnection();try{let e=await this.db.stats();return{collections:e.collections,documents:e.objects,dataSize:e.dataSize,storageSize:e.storageSize,indexes:e.indexes,indexSize:e.indexSize}}catch(e){throw this.logger.error("Failed to get database stats:",e),e}}async warmupConnections(){await this.ensureConnection();try{let e=Array.from({length:3},()=>this.db.admin().ping());await Promise.all(e),this.logger.debug("Database connection pool warmed up")}catch(e){this.logger.warn("Failed to warm up connection pool:",e)}}}var m=o(2115),h=o.n(m),b=o(9021),f=o.n(b),p=o(3873),y=o.n(p);class w{static{this.configPath=y().join(process.cwd(),"config.yml")}static{this.config=null}static load(e){e&&(w.configPath=e);let t=y().resolve(w.configPath);if(!f().existsSync(t))throw Error(`Configuration file not found: ${t}`);try{let e=f().readFileSync(t,"utf8"),o=h().parse(e);return w.validateConfig(o),w.config=o,o}catch(e){if(e instanceof Error)throw Error(`Failed to load configuration: ${e.message}`);throw Error("Failed to load configuration: Unknown error")}}static get(){if(!w.config)throw Error("Configuration not loaded. Call ConfigManager.load() first.");return w.config}static reload(){return w.config=null,w.load()}static validateConfig(e){for(let t of["bot.token","bot.clientId","bot.prefix","logging.level","addons.enabled","addons.directory"])if(!w.getNestedValue(e,t))throw Error(`Missing required configuration field: ${t}`);("string"!=typeof e.bot.token||e.bot.token.length<50)&&process.stdout.write("Warning: Bot token appears to be invalid or placeholder\n"),"string"==typeof e.bot.clientId&&/^\d+$/.test(e.bot.clientId)||process.stdout.write("Warning: Client ID appears to be invalid\n");let t=["error","warn","info","debug"];if(!t.includes(e.logging.level))throw Error(`Invalid logging level: ${e.logging.level}. Must be one of: ${t.join(", ")}`);for(let t of[e.logging.file?.path,e.addons.directory,y().dirname(e.database?.path||"data/bot.db")].filter(Boolean))try{f().existsSync(t)||f().mkdirSync(t,{recursive:!0})}catch(e){process.stdout.write(`Warning: Could not create directory ${t}: ${e}
`)}}static getNestedValue(e,t){return t.split(".").reduce((e,t)=>e?.[t],e)}static watch(e){let t=y().resolve(w.configPath);f().watchFile(t,(t,o)=>{if(t.mtime!==o.mtime)try{let t=w.reload();e(t)}catch(e){process.stderr.write(`Failed to reload configuration: ${e}
`)}})}static getConfig(){if(!this.config){let e=f().readFileSync(this.configPath,"utf8");this.config=h().parse(e)}return this.config}static reloadConfig(){let e=f().readFileSync(this.configPath,"utf8");this.config=h().parse(e)}}let I=new g(w.getConfig()),S={welcome:{enabled:!1,channelId:null,message:"Welcome {user} to {guild}! You are the {memberCount}th member.",autoRoles:[],embedColor:"#00FF00"},goodbye:{enabled:!1,channelId:null,message:"Goodbye {user}! We will miss you.",embedColor:"#FF0000"}};async function C(e,t){let o=await (0,c.getServerSession)(e,t,l.authOptions);if(!o)return t.status(401).json({error:"Unauthorized"});let{guildId:n}=o;if(!n)return t.status(400).json({error:"Guild ID not found in session."});try{if(await I.connect(),"GET"===e.method){let e=await I.findOne("guild_configs",{guildId:n}),o={welcome:{...S.welcome,...e?.welcome},goodbye:{...S.goodbye,...e?.goodbye}};return t.status(200).json(o)}if("POST"===e.method){let{welcome:o,goodbye:i}=e.body;if("boolean"!=typeof o?.enabled||"boolean"!=typeof i?.enabled)return t.status(400).json({error:"Invalid data format."});let s={guildId:n,welcome:{enabled:o.enabled,channelId:o.channelId||null,message:o.message||S.welcome.message,autoRoles:o.autoRoles||[],embedColor:o.embedColor||S.welcome.embedColor},goodbye:{enabled:i.enabled,channelId:i.channelId||null,message:i.message||S.goodbye.message,embedColor:i.embedColor||S.goodbye.embedColor}};return await I.updateOne("guild_configs",{guildId:n},{$set:s},{upsert:!0}),t.status(200).json({message:"Settings updated successfully."})}return t.setHeader("Allow",["GET","POST"]),t.status(405).end(`Method ${e.method} Not Allowed`)}catch(e){return t.status(500).json({error:"Internal Server Error"})}}let x=(0,r.M)(i,"default"),P=(0,r.M)(i,"config"),k=new s.PagesAPIRouteModule({definition:{kind:a.A.PAGES_API,page:"/api/automation/welcome",pathname:"/api/automation/welcome",bundlePath:"",filename:""},userland:i})},8525:(e,t,o)=>{o.r(t),o.d(t,{authOptions:()=>c,default:()=>l});var n=o(5542),i=o.n(n);let s=require("next-auth/providers/discord");var a=o.n(s),r=o(8580);let c={providers:[a()({clientId:r.dashboardConfig.bot.clientId,clientSecret:r.dashboardConfig.bot.clientSecret,authorization:{params:{scope:"identify email guilds"}}})],callbacks:{jwt:async({token:e,account:t,profile:o})=>(t&&o&&(e.accessToken=t.access_token||null,e.id=o.id||null),e),async session({session:e,token:t}){if(e?.user){let o=t.id||null,n=t.accessToken||null;e.user.id=o,e.user.accessToken=n;let i=!1;if(o)if((r.dashboardConfig.dashboard.admins||[]).includes(o))i=!0;else{let e=r.dashboardConfig.dashboard.adminRoleIds||[];if(e.length&&r.dashboardConfig.bot.token&&r.dashboardConfig.bot.guildId)try{let t=await fetch(`https://discord.com/api/v10/guilds/${r.dashboardConfig.bot.guildId}/members/${o}`,{headers:{Authorization:`Bot ${r.dashboardConfig.bot.token}`}});if(t.ok){let o=await t.json();i=e.some(e=>o.roles?.includes(e))||!1}else await t.text()}catch(e){}}e.user.isAdmin=i,e.user={...e.user,id:e.user.id||null,accessToken:e.user.accessToken||null,isAdmin:e.user.isAdmin||!1,name:e.user.name||null,email:e.user.email||null,image:e.user.image||null}}return e},async redirect({url:e,baseUrl:t}){let o=new URL(t),n=`${o.protocol}//localhost${o.port?`:${o.port}`:""}`;return e.startsWith(t)||e.startsWith(n)?e:t}},secret:r.dashboardConfig.dashboard.session.secret||r.dashboardConfig.bot.clientSecret,pages:{signIn:"/signin"},debug:!1,logger:{error:(e,t)=>{},warn:e=>{},debug:(e,t)=>{}}},l=i()(c)},8580:(e,t,o)=>{o.r(t),o.d(t,{dashboardConfig:()=>c,default:()=>l});var n=o(9021),i=o(2115),s=o.n(i),a=o(3873);let r={};try{let e=["config.yml","../config.yml","../../config.yml","../../../config.yml","../../../../config.yml"].map(e=>a.resolve(process.cwd(),e)).find(e=>n.existsSync(e));if(!e){let t=a.resolve(__dirname,"../../../config.yml");n.existsSync(t)&&(e=t)}if(!e)throw Error("config.yml not found");let t=n.readFileSync(e,"utf8");r=s().parse(t)}catch(e){process.exit(1)}let c={bot:{token:r.bot.token,clientId:r.bot.clientId,clientSecret:r.bot.clientSecret,guildId:r.bot.guildId,ticketCategoryId:r.bot.ticketCategoryId||null,ticketLogChannelId:r.bot.ticketLogChannelId||null,prefix:r.bot.prefix},dashboard:{admins:r.dashboard?.admins||[],adminRoleIds:r.dashboard?.adminRoleIds||[],session:{secret:r.dashboard?.session?.secret||r.bot.clientSecret}},database:{url:r.database.url,name:r.database.name,options:{maxPoolSize:r.database.options?.maxPoolSize||10,minPoolSize:r.database.options?.minPoolSize||1,maxIdleTimeMS:r.database.options?.maxIdleTimeMS||3e4,serverSelectionTimeoutMS:r.database.options?.serverSelectionTimeoutMS||5e3,socketTimeoutMS:r.database.options?.socketTimeoutMS||45e3,connectTimeoutMS:r.database.options?.connectTimeoutMS||1e4,retryWrites:r.database.options?.retryWrites!==!1,retryReads:r.database.options?.retryReads!==!1}}};c.bot.token||process.exit(1),c.bot.clientId&&c.bot.clientSecret||process.exit(1),c.bot.guildId||process.exit(1),c.database.url&&c.database.name||process.exit(1);let l=c},9021:e=>{e.exports=require("fs")}};var t=require("../../../webpack-api-runtime.js");t.C(e);var o=t(t.s=6315);module.exports=o})();