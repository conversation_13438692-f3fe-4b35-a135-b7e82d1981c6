import { NextApiRequest, NextApiResponse } from 'next';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]';
import { MongoClient } from 'mongodb';
import fs from 'fs';
import path from 'path';

// Local type definitions for addon builder
interface AddonConfig {
  name: string;
  version: string;
  description: string;
  author: string;
  commands: CommandConfig[];
  events: EventConfig[];
  settings: {
    embedColor: string;
    [key: string]: any;
  };
  database?: {
    collections: string[];
  };
}

interface CommandConfig {
  name: string;
  description: string;
  type: 'slash' | 'context-menu';
  options?: SlashCommandOption[];
  permissions?: string[];
  cooldown?: number;
  enabled: boolean;
  code: string;
}

interface SlashCommandOption {
  name: string;
  description: string;
  type: 'string' | 'integer' | 'boolean' | 'user' | 'channel' | 'role' | 'mentionable' | 'number' | 'attachment';
  required: boolean;
  choices?: { name: string; value: string | number }[];
}

interface EventConfig {
  name: string;
  once: boolean;
  code: string;
}

// Simple database config - can be expanded
const DATABASE_CONFIG = {
  url: process.env.DATABASE_URL || 'mongodb://localhost:27017',
  name: process.env.DATABASE_NAME || 'discord-bot'
};

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  const session = await getServerSession(req, res, authOptions);
  if (!session) {
    return res.status(401).json({ error: 'Unauthorized' });
  }

  try {
    // Check experimental features access
    const client = await MongoClient.connect(DATABASE_CONFIG.url);
    const db = client.db(DATABASE_CONFIG.name);
    
    const userAccessQuery = { userId: (session.user as any).id };
    const [testerAccess, flagAccess] = await Promise.all([
      db.collection('experimental_testers').findOne(userAccessQuery),
      db.collection('experimental_flags').findOne(userAccessQuery)
    ]);

    const hasAccess = testerAccess || flagAccess || (session.user as any).isAdmin;
    
    if (!hasAccess) {
      await client.close();
      return res.status(403).json({ error: 'Experimental features access required' });
    }

    const addonConfig: AddonConfig = req.body;

    // Validate addon configuration
    const validationResult = validateAddonConfig(addonConfig);
    if (!validationResult.isValid) {
      await client.close();
      return res.status(400).json({ 
        error: 'Invalid addon configuration', 
        details: validationResult.errors 
      });
    }

    // Generate addon files
    const files = generateAddonFiles(addonConfig);

    // Create addon directory structure
    const addonDir = await createAddonDirectory(addonConfig.name);

    // Write files to disk
    await writeAddonFiles(addonDir, files);

    // Log addon creation
    await db.collection('addon_builder_logs').insertOne({
      userId: (session.user as any).id,
      userEmail: (session.user as any).email,
      addonName: addonConfig.name,
      action: 'created',
      timestamp: new Date(),
      fileCount: Object.keys(files).length
    });

    // Create addon reload signal
    const baseDir = process.cwd().includes(path.join('src', 'dashboard'))
      ? path.resolve(process.cwd(), '..', '..')
      : process.cwd();
    const signalPath = path.join(baseDir, 'addon-reload.signal');

    fs.writeFileSync(signalPath, JSON.stringify({
      requestedBy: (session.user as any).email || 'addon-builder',
      timestamp: Date.now(),
      action: 'addon-created',
      addonName: addonConfig.name
    }));

    await client.close();

    res.status(201).json({
      message: 'Addon created successfully',
      addonName: addonConfig.name,
      files: Object.keys(files),
      path: addonDir
    });

  } catch (error) {
    console.error('Error creating addon:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
}

// Simple validation function
function validateAddonConfig(config: AddonConfig): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.name || config.name.length < 2) {
    errors.push('Addon name must be at least 2 characters long');
  }

  if (!/^[a-z0-9-]+$/.test(config.name)) {
    errors.push('Addon name must contain only lowercase letters, numbers, and hyphens');
  }

  if (!config.version || !/^\d+\.\d+\.\d+$/.test(config.version)) {
    errors.push('Version must be in semver format (e.g., 1.0.0)');
  }

  if (!config.description || config.description.length < 10) {
    errors.push('Description must be at least 10 characters long');
  }

  if (!config.author || config.author.length < 2) {
    errors.push('Author name must be at least 2 characters long');
  }

  if (!config.settings?.embedColor || !/^#[0-9a-fA-F]{6}$/.test(config.settings.embedColor)) {
    errors.push('Embed color must be a valid hex color (e.g., #0099FF)');
  }

  return { isValid: errors.length === 0, errors };
}

// Basic file generation functions
function generateAddonFiles(config: AddonConfig): { [filename: string]: string } {
  const files: { [filename: string]: string } = {};

  files['index.ts'] = generateIndexFile(config);
  files['config.yml'] = generateConfigFile(config);
  files['README.md'] = generateReadmeFile(config);

  config.commands.forEach((command: CommandConfig) => {
    files[`commands/${command.name.toLowerCase()}.ts`] = generateCommandFile(command);
  });

  return files;
}

// Simple file generators
function generateIndexFile(config: AddonConfig): string {
  return `import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath, pathToFileURL } from 'node:url';
import YAML from 'yaml';
import type { Addon, BotInstance } from '../../types/index.js';
import { Logger } from '../../core/Logger.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const configPath = path.join(__dirname, 'config.yml');
const configFile = fs.readFileSync(configPath, 'utf8');
const config = YAML.parse(configFile);

async function loadCommands(): Promise<any[]> {
  const commands: any[] = [];
  const commandsPath = path.join(__dirname, 'commands');
  
  if (!fs.existsSync(commandsPath)) {
    return commands;
  }

  const commandFiles = fs.readdirSync(commandsPath).filter(file => 
    file.endsWith('.js') || file.endsWith('.ts')
  );

  for (const file of commandFiles) {
    try {
      const commandPath = path.join(commandsPath, file);
      const commandUrl = pathToFileURL(commandPath).href;
      const commandModule = await import(commandUrl);
      
      if (commandModule.data && commandModule.execute) {
        commands.push({
          data: commandModule.data,
          execute: commandModule.execute,
          cooldown: commandModule.cooldown || 3000
        });
      }
    } catch (error) {
      console.error(\`Failed to load command \${file}:\`, error);
    }
  }

  return commands;
}

const addon: Addon = {
  info: {
    name: config.addon.name,
    version: config.addon.version,
    description: config.addon.description,
    author: config.addon.author
  },

  commands: await loadCommands(),

  events: [
    {
      name: 'ready',
      once: true,
      execute: async (client) => {
        const logger = Logger.createAddonLogger(config.addon.name);
        logger.info(\`\${config.addon.name} addon loaded! Bot ready as \${client.user?.tag}\`);
      }
    }
  ],

  onLoad: async (bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Loading \${config.addon.name} addon...\`);
  },

  onUnload: async (_bot: BotInstance) => {
    const logger = Logger.createAddonLogger(config.addon.name);
    logger.info(\`Unloading \${config.addon.name} addon...\`);
  }
};

export default addon;`;
}

function generateConfigFile(config: AddonConfig): string {
  const configObj = {
    addon: {
      name: config.name,
      version: config.version,
      description: config.description,
      author: config.author,
      enabled: true
    },
    commands: {} as any,
    settings: {
      embedColor: parseInt(config.settings.embedColor.slice(1), 16)
    }
  };

  config.commands.forEach((cmd: CommandConfig) => {
    configObj.commands[cmd.name] = {
      enabled: cmd.enabled,
      cooldown: cmd.cooldown || 3000,
      description: cmd.description
    };
  });

  return `addon:
  name: "${config.name}"
  version: "${config.version}"
  description: "${config.description}"
  author: "${config.author}"
  enabled: true

settings:
  embedColor: 0x${config.settings.embedColor.slice(1)}

logging:
  enabled: true
  logCommands: true
  logLevel: "info"`;
}

function generateCommandFile(command: CommandConfig): string {
  return `import { SlashCommandBuilder, EmbedBuilder, ChatInputCommandInteraction } from 'discord.js';
import type { BotInstance } from '../../../types/index.js';

export const data = new SlashCommandBuilder()
  .setName('${command.name}')
  .setDescription('${command.description}');

export async function execute(interaction: ChatInputCommandInteraction, bot: BotInstance) {
  try {
    ${command.code}
  } catch (error) {
    console.error('Error executing ${command.name} command:', error);
    
    const errorMessage = 'There was an error executing this command!';
    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply({ content: errorMessage, ephemeral: true });
    }
  }
}

export const cooldown = ${command.cooldown || 3000};`;
}

function generateReadmeFile(config: AddonConfig): string {
  return `# ${config.name}

${config.description}

**Author:** ${config.author}  
**Version:** ${config.version}

## Commands

${config.commands.map(cmd => `- **/${cmd.name}** - ${cmd.description}`).join('\n') || 'No commands available.'}

## Settings

- **Embed Color:** ${config.settings.embedColor}

---

*This addon was generated using the 404 Bot Addon Builder.*`;
}

async function createAddonDirectory(addonName: string): Promise<string> {
  const baseDir = process.cwd().includes(path.join('src', 'dashboard'))
    ? path.resolve(process.cwd(), '..', '..')
    : process.cwd();

  const addonsDir = path.join(baseDir, 'src', 'addons');
  const addonDir = path.join(addonsDir, addonName);

  if (fs.existsSync(addonDir)) {
    throw new Error(`Addon '${addonName}' already exists`);
  }

  fs.mkdirSync(addonDir, { recursive: true });
  fs.mkdirSync(path.join(addonDir, 'commands'), { recursive: true });

  return addonDir;
}

async function writeAddonFiles(addonDir: string, files: { [filename: string]: string }) {
  for (const [filename, content] of Object.entries(files)) {
    const filePath = path.join(addonDir, filename);
    const fileDir = path.dirname(filePath);

    if (!fs.existsSync(fileDir)) {
      fs.mkdirSync(fileDir, { recursive: true });
    }

    fs.writeFileSync(filePath, content, 'utf8');
  }
} 